# AI财务系统部署配置文件示例
# 复制此文件为 deploy.config 并修改相应配置

# ==============================================
# 服务器配置
# ==============================================
SERVER_HOST="your-server.com"
SERVER_USER="root"
SERVER_PORT="22"
SERVER_PATH="/www/sites/fin.aiform.com"
ADMIN_SERVER_PATH="/www/sites/admin.aiform.com"
MAIN_DOMAIN="https://fin.aiform.com"
ADMIN_DOMAIN="https://admin.aiform.com"
BACKEND_SERVICE_NAME="aifinancial-backend"

# ==============================================
# 数据库配置
# ==============================================
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="aifinancial"
DB_USER="root"
DB_PASSWORD="your-database-password"  # 必须设置

# ==============================================
# 项目配置
# ==============================================
PROJECT_NAME="aifinancial"
BUILD_DIR="./build"

# ==============================================
# 使用说明
# ==============================================
# 1. 复制此文件为 deploy.config
# 2. 修改上述配置项，特别是数据库密码
# 3. 运行部署命令：
#    ./deploy.sh deploy          # 完整部署（包含数据库迁移）
#    ./deploy.sh migrate-db      # 仅执行数据库迁移
#    ./deploy.sh build           # 仅本地构建
