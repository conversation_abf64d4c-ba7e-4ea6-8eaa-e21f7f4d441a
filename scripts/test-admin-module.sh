#!/bin/bash

# 系统管理员模块测试脚本
# 用于验证管理员模块的基本功能

echo "========================================="
echo "系统管理员模块功能测试"
echo "========================================="

# 配置
BACKEND_URL="http://localhost:9080"
FRONTEND_URL="http://localhost:3001"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="admin123"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_api() {
    local test_name="$1"
    local url="$2"
    local method="$3"
    local data="$4"
    local expected_status="$5"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "测试: $test_name ... "
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "%{http_code}" -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "%{http_code}" "$url")
    fi
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}PASS${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# 检查服务状态
check_service() {
    local service_name="$1"
    local url="$2"
    
    echo -n "检查 $service_name 服务状态 ... "
    
    if curl -s "$url" > /dev/null; then
        echo -e "${GREEN}运行中${NC}"
        return 0
    else
        echo -e "${RED}未运行${NC}"
        return 1
    fi
}

echo "1. 检查服务状态"
echo "-----------------"

# 检查后端服务
if ! check_service "后端" "$BACKEND_URL/actuator/health"; then
    echo -e "${RED}错误: 后端服务未运行，请先启动后端服务${NC}"
    echo "启动命令: ./gradlew bs-server:bootRun"
    exit 1
fi

# 检查前端服务
if ! check_service "前端" "$FRONTEND_URL"; then
    echo -e "${YELLOW}警告: 前端服务未运行${NC}"
    echo "启动命令: cd admin-frontend && yarn dev"
fi

echo ""
echo "2. 数据库连接测试"
echo "-----------------"

# 测试数据库连接（通过API）
test_api "数据库连接" "$BACKEND_URL/api/admin/auth/check-session" "GET" "" "200"

echo ""
echo "3. 管理员认证测试"
echo "-----------------"

# 测试管理员登录
login_data="{\"username\":\"$ADMIN_USERNAME\",\"password\":\"$ADMIN_PASSWORD\"}"
test_api "管理员登录" "$BACKEND_URL/api/admin/auth/login" "POST" "$login_data" "200"

# 测试获取当前用户信息
test_api "获取当前用户" "$BACKEND_URL/api/admin/auth/current" "GET" "" "200"

echo ""
echo "4. 用户管理API测试"
echo "-----------------"

# 测试获取用户列表
test_api "获取用户列表" "$BACKEND_URL/api/admin/users/list?page=1&size=10" "GET" "" "200"

echo ""
echo "5. AI配置管理API测试"
echo "-------------------"

# 测试获取AI配置列表
test_api "获取AI配置列表" "$BACKEND_URL/api/admin/ai-config/list?page=1&size=10" "GET" "" "200"

# 测试获取AI配置统计
test_api "获取AI配置统计" "$BACKEND_URL/api/admin/ai-config/statistics" "GET" "" "200"

echo ""
echo "6. 数据导出测试"
echo "---------------"

# 测试用户数据导出
test_api "用户数据导出" "$BACKEND_URL/api/admin/data/export/users" "GET" "" "200"

echo ""
echo "7. 前端页面测试"
echo "---------------"

if curl -s "$FRONTEND_URL" > /dev/null; then
    # 测试前端主要页面
    test_api "前端首页" "$FRONTEND_URL" "GET" "" "200"
    test_api "登录页面" "$FRONTEND_URL/admin/login" "GET" "" "200"
    
    echo -n "检查前端资源加载 ... "
    if curl -s "$FRONTEND_URL/assets/" | grep -q "css\|js"; then
        echo -e "${GREEN}PASS${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${YELLOW}SKIP${NC} (资源可能未构建)"
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    echo -e "${YELLOW}跳过前端测试 (服务未运行)${NC}"
fi

echo ""
echo "8. 安全性测试"
echo "-------------"

# 测试未授权访问
test_api "未授权访问拦截" "$BACKEND_URL/api/admin/users/list" "GET" "" "401"

echo ""
echo "========================================="
echo "测试结果汇总"
echo "========================================="
echo "总测试数: $TOTAL_TESTS"
echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}所有测试通过！系统管理员模块运行正常。${NC}"
    exit 0
else
    echo -e "${RED}有 $FAILED_TESTS 个测试失败，请检查系统配置。${NC}"
    exit 1
fi
