-- AI智能会计科目匹配功能数据库增强脚本
-- 为现有系统添加AI相关字段和表

-- 1. 为科目表添加AI描述相关字段
-- ALTER TABLE `fxy_financial_subject` 
-- ADD COLUMN `ai_description` TEXT COMMENT 'AI描述信息，用于大模型理解科目用途' AFTER `auxiliary_accounting`,
-- ADD COLUMN `ai_keywords` VARCHAR(500) COMMENT 'AI关键词，逗号分隔，用于匹配' AFTER `ai_description`,
-- ADD COLUMN `matching_rules` JSON COMMENT '匹配规则配置' AFTER `ai_keywords`,
-- ADD COLUMN `usage_frequency` INT DEFAULT 0 COMMENT '使用频率统计' AFTER `matching_rules`,
-- ADD COLUMN `last_matched_date` DATETIME COMMENT '最后匹配时间' AFTER `usage_frequency`;

-- 2. 创建AI匹配历史记录表
CREATE TABLE `fxy_financial_ai_matching_history` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_sets_id` INT NOT NULL COMMENT '账套ID',
  `business_type` VARCHAR(20) NOT NULL COMMENT '业务类型：bank_voucher/bill',
  `business_id` INT NOT NULL COMMENT '业务ID（银证ID或票据ID）',
  `matched_subjects` JSON NOT NULL COMMENT '匹配的科目信息',
  `confidence_score` DECIMAL(3,2) COMMENT '置信度分数',
  `ai_reasoning` TEXT COMMENT 'AI推理过程',
  `user_feedback` TINYINT COMMENT '用户反馈：1正确，0错误，NULL未反馈',
  `processing_time` INT COMMENT '处理时间（毫秒）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` INT COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_account_sets_business` (`account_sets_id`, `business_type`, `business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI匹配历史记录表';

-- 3. 创建AI配置表
CREATE TABLE `fxy_financial_ai_config` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_sets_id` INT NOT NULL COMMENT '账套ID',
  `config_key` VARCHAR(50) NOT NULL COMMENT '配置键',
  `config_value` TEXT NOT NULL COMMENT '配置值',
  `description` VARCHAR(200) COMMENT '配置描述',
  `is_active` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_config` (`account_sets_id`, `config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI配置表';

-- 4. 创建科目匹配模板表
CREATE TABLE `fxy_financial_subject_matching_template` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_sets_id` INT NOT NULL COMMENT '账套ID',
  `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `business_scenario` VARCHAR(50) NOT NULL COMMENT '业务场景',
  `matching_conditions` JSON NOT NULL COMMENT '匹配条件',
  `subject_mapping` JSON NOT NULL COMMENT '科目映射规则',
  `priority` INT DEFAULT 0 COMMENT '优先级',
  `is_active` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
  `success_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '成功率',
  `usage_count` INT DEFAULT 0 COMMENT '使用次数',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` INT COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_account_scenario` (`account_sets_id`, `business_scenario`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='科目匹配模板表';

-- 5. 插入默认AI配置
INSERT INTO `fxy_financial_ai_config` (`account_sets_id`, `config_key`, `config_value`, `description`) VALUES
(0, 'ai_model_endpoint', 'https://api.openai.com/v1/chat/completions', 'AI模型API端点'),
(0, 'ai_model_name', 'gpt-4', 'AI模型名称'),
(0, 'confidence_threshold', '0.7', '置信度阈值'),
(0, 'max_retry_times', '3', '最大重试次数'),
(0, 'enable_learning', 'true', '是否启用学习功能'),
(0, 'batch_size', '10', '批处理大小');

-- 6. 为常用科目添加AI描述示例
UPDATE `fxy_financial_subject` SET 
  `ai_description` = '用于记录企业持有的现金，包括库存的人民币和外币现金',
  `ai_keywords` = '现金,库存现金,备用金,零用金,现金收入,现金支出'
WHERE `code` = '1001' AND `name` = '库存现金';

UPDATE `fxy_financial_subject` SET 
  `ai_description` = '用于记录企业在银行开立的各种账户的资金，包括基本户、一般户等',
  `ai_keywords` = '银行存款,银行转账,银行收入,银行支出,存款,取款,银行利息'
WHERE `code` = '1002' AND `name` = '银行存款';

UPDATE `fxy_financial_subject` SET 
  `ai_description` = '用于记录应向客户收取但尚未收到的款项',
  `ai_keywords` = '应收账款,客户欠款,销售收入,货款,应收,客户'
WHERE `code` = '1122' AND `name` = '应收账款';

UPDATE `fxy_financial_subject` SET 
  `ai_description` = '用于记录应向供应商支付但尚未支付的款项',
  `ai_keywords` = '应付账款,供应商,采购,货款,应付,欠款'
WHERE `code` = '2202' AND `name` = '应付账款';

UPDATE `fxy_financial_subject` SET 
  `ai_description` = '用于记录企业的主营业务收入',
  `ai_keywords` = '主营业务收入,销售收入,营业收入,收入,销售'
WHERE `code` = '6001' AND `name` = '主营业务收入';

UPDATE `fxy_financial_subject` SET 
  `ai_description` = '用于记录企业的主营业务成本',
  `ai_keywords` = '主营业务成本,销售成本,营业成本,成本,采购成本'
WHERE `code` = '6401' AND `name` = '主营业务成本';

-- 7. 创建索引优化查询性能
CREATE INDEX `idx_subject_ai_keywords` ON `fxy_financial_subject` (`ai_keywords`);
CREATE INDEX `idx_subject_usage_freq` ON `fxy_financial_subject` (`usage_frequency` DESC);

-- 8. 插入常用匹配模板示例
INSERT INTO `fxy_financial_subject_matching_template` 
(`account_sets_id`, `template_name`, `business_scenario`, `matching_conditions`, `subject_mapping`, `priority`) VALUES
(0, '银行收款模板', 'bank_income', 
 '{"type": "收入", "keywords": ["收款", "转入", "存款"]}',
 '{"debit": [{"code": "1002", "name": "银行存款"}], "credit": [{"code": "6001", "name": "主营业务收入"}]}',
 10),
(0, '银行付款模板', 'bank_expense', 
 '{"type": "支出", "keywords": ["付款", "转出", "支取"]}',
 '{"debit": [{"code": "6401", "name": "主营业务成本"}], "credit": [{"code": "1002", "name": "银行存款"}]}',
 10),
(0, '现金收款模板', 'cash_income', 
 '{"type": "收入", "keywords": ["现金", "收款"]}',
 '{"debit": [{"code": "1001", "name": "库存现金"}], "credit": [{"code": "6001", "name": "主营业务收入"}]}',
 8);

COMMIT;