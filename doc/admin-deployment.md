# 管理员页面部署指南

## 概述

管理员页面是一个独立的Vue 3 + TypeScript + Element Plus前端应用，用于系统管理和配置。

## 目录结构

```
/www/sites/fin.aiform.com/
├── index/dist/          # 主应用前端文件
├── admin/dist/          # 管理员页面前端文件
├── ssl/                 # SSL证书文件
└── log/                 # 日志文件
```

## 部署步骤

### 1. 构建管理员页面

在项目根目录下执行：

```bash
cd admin-frontend
yarn install
yarn build
```

构建完成后，会在 `admin-frontend/dist/` 目录下生成静态文件。

### 2. 上传文件到服务器

将构建好的文件上传到服务器：

```bash
# 创建管理员页面目录
mkdir -p /www/sites/fin.aiform.com/admin/

# 上传构建文件
scp -r admin-frontend/dist/* user@server:/www/sites/fin.aiform.com/admin/dist/
```

### 3. 配置Nginx

使用项目中的nginx配置文件：

```bash
# 复制nginx配置
cp doc/config/nginx.conf /etc/nginx/sites-available/fin.aiform.com

# 重新加载nginx配置
nginx -t
systemctl reload nginx
```

### 4. 验证部署

访问以下URL验证部署是否成功：

- 主应用：https://fin.aiform.com/
- 管理员页面：https://fin.aiform.com/admin/

### API测试

可以使用curl命令测试API是否正常：

```bash
# 测试管理员API（需要认证）
curl -H "Authorization: Bearer YOUR_TOKEN" https://fin.aiform.com/api/admin/users/list

# 测试主应用API
curl https://fin.aiform.com/api/bills/list
```

## API路径映射（当前最优方案）

### 统一API代理 - 无需路径重写

当前配置采用了最佳实践：**统一API前缀，直接转发**

#### 管理员页面API调用流程

1. **前端请求**：管理员页面发起请求 `/api/admin/users/list`
2. **Nginx处理**：匹配到 `location ^~ /api/`
3. **直接转发**：`/api/admin/users/list` → 后端 `/api/admin/users/list`
4. **后端处理**：`AdminUserManagementController` 处理请求

#### 主应用API调用流程

1. **前端请求**：主应用发起请求 `/api/bank-receipts/list`
2. **Nginx处理**：匹配到 `location ^~ /api/`
3. **直接转发**：`/api/bank-receipts/list` → 后端 `/api/bank-receipts/list`
4. **后端处理**：`BankReceiptsController` 处理请求

#### API路径对照表

| 前端请求路径 | 后端接收路径 | Controller类 | RequestMapping |
|-------------|-------------|-------------|---------------|
| `/api/admin/users/list` | `/api/admin/users/list` | `AdminUserManagementController` | `@RequestMapping("/admin/users")` |
| `/api/admin/account-sets/list` | `/api/admin/account-sets/list` | `AdminAccountSetsController` | `@RequestMapping("/admin/account-sets")` |
| `/api/admin/auth/login` | `/api/admin/auth/login` | `AdminAuthController` | `@RequestMapping("/admin/auth")` |
| `/api/bank-receipts/list` | `/api/bank-receipts/list` | `BankReceiptsController` | `@RequestMapping("/bank-receipts")` |
| `/api/user/list` | `/api/user/list` | `UserController` | `@RequestMapping("/user")` |

### 配置优势

✅ **无顺序依赖**：只有一个location配置，不存在匹配顺序问题
✅ **配置简单**：统一的代理规则，易于维护
✅ **性能最优**：无路径重写开销，直接转发
✅ **扩展性好**：添加新API无需修改nginx配置
✅ **调试友好**：前端请求路径与后端接收路径一致

## Nginx配置说明

### 统一API代理配置（当前最优方案）

当前配置采用统一API代理，无需复杂的路径重写：

```nginx
# 统一API代理配置
location ^~ /api/ {
    # 直接转发，不做路径重写
    proxy_pass http://127.0.0.1:9080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    # 添加超时配置
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
}
```

### 为什么这是最优方案？

1. **后端Controller已正确配置**：
   - 管理员Controller：`@RequestMapping("/admin/xxx")`
   - 主应用Controller：`@RequestMapping("/xxx")`

2. **前端API调用路径正确**：
   - 管理员页面：`baseURL: '/api'` + `/admin/users/list` = `/api/admin/users/list`
   - 主应用：`/api/bank-receipts/list`

3. **nginx直接转发**：
   - 无需路径重写，性能最优
   - 配置简单，维护方便
   - 无顺序依赖问题

### 管理员页面配置

```nginx
# 管理员页面配置
location ^~ /admin/ {
    alias /www/sites/fin.aiform.com/admin/dist/;
    try_files $uri $uri/ /admin/index.html;
    index index.html;

    # 静态资源缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        alias /www/sites/fin.aiform.com/admin/dist/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        gzip_static on;
    }

    # HTML文件不缓存
    location ~* \.html$ {
        alias /www/sites/fin.aiform.com/admin/dist/;
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }
}
```

### 配置特点

1. **统一API代理**：
   - 所有API请求统一通过 `/api/` 前缀
   - 直接转发到后端，无路径重写开销
   - 管理员API和主应用API通过后端Controller路径自然分离

2. **无顺序依赖**：
   - 只有一个API location配置
   - 不存在nginx配置顺序问题
   - 维护简单，不易出错

3. **前端路由支持**：
   - 管理员页面：使用 `try_files` 支持Vue Router的history模式
   - 主应用：同样支持前端路由

4. **缓存策略**：
   - 静态资源（JS、CSS、图片等）缓存1年
   - HTML文件不缓存，确保更新及时生效

5. **性能优化**：
   - 启用Gzip压缩，提高加载速度
   - 合理的超时配置，避免请求超时
   - 无路径重写，减少nginx处理开销

## 访问控制

### 基本认证（可选）

如果需要为管理员页面添加基本认证，可以在nginx配置中添加：

```nginx
location ^~ /admin/ {
    auth_basic "Admin Area";
    auth_basic_user_file /etc/nginx/.htpasswd;
    # ... 其他配置
}
```

创建密码文件：

```bash
# 安装htpasswd工具
apt-get install apache2-utils

# 创建用户
htpasswd -c /etc/nginx/.htpasswd admin
```

### IP白名单（可选）

限制特定IP访问：

```nginx
location ^~ /admin/ {
    allow ***********/24;  # 允许内网访问
    allow 10.0.0.0/8;      # 允许VPN访问
    deny all;              # 拒绝其他所有IP
    # ... 其他配置
}
```

## 更新部署

### 自动化部署脚本

创建部署脚本 `deploy-admin.sh`：

```bash
#!/bin/bash

# 构建管理员页面
cd admin-frontend
yarn install
yarn build

# 备份当前版本
sudo cp -r /www/sites/fin.aiform.com/admin/dist /www/sites/fin.aiform.com/admin/dist.backup.$(date +%Y%m%d_%H%M%S)

# 部署新版本
sudo rm -rf /www/sites/fin.aiform.com/admin/dist/*
sudo cp -r dist/* /www/sites/fin.aiform.com/admin/dist/

# 设置权限
sudo chown -R www-data:www-data /www/sites/fin.aiform.com/admin/dist/
sudo chmod -R 755 /www/sites/fin.aiform.com/admin/dist/

echo "管理员页面部署完成！"
echo "访问地址：https://fin.aiform.com/admin/"
```

### 回滚操作

如果需要回滚到之前的版本：

```bash
# 查看备份版本
ls -la /www/sites/fin.aiform.com/admin/dist.backup.*

# 回滚到指定版本
sudo rm -rf /www/sites/fin.aiform.com/admin/dist/*
sudo cp -r /www/sites/fin.aiform.com/admin/dist.backup.20241217_143000/* /www/sites/fin.aiform.com/admin/dist/
sudo chown -R www-data:www-data /www/sites/fin.aiform.com/admin/dist/
```

## 故障排除

### 常见问题

1. **404错误**：检查文件路径和nginx配置
2. **静态资源加载失败**：检查文件权限和缓存配置
3. **前端路由不工作**：确保 `try_files` 配置正确

### 日志查看

```bash
# 查看nginx访问日志
tail -f /www/sites/fin.aiform.com/log/access.log

# 查看nginx错误日志
tail -f /www/sites/fin.aiform.com/log/error.log

# 查看nginx配置测试
nginx -t
```

## 安全建议

1. **定期更新**：及时更新依赖包和框架版本
2. **访问控制**：根据需要配置IP白名单或基本认证
3. **HTTPS强制**：确保所有访问都通过HTTPS
4. **日志监控**：定期检查访问日志，发现异常访问
5. **备份策略**：定期备份配置文件和静态资源

## 监控和维护

### 健康检查

创建健康检查脚本：

```bash
#!/bin/bash

# 检查管理员页面是否可访问
response=$(curl -s -o /dev/null -w "%{http_code}" https://fin.aiform.com/admin/)

if [ $response -eq 200 ]; then
    echo "管理员页面正常运行"
else
    echo "管理员页面访问异常，状态码：$response"
    # 发送告警通知
fi
```

### 性能优化

1. **启用Gzip压缩**：减少传输大小
2. **设置合理的缓存策略**：提高加载速度
3. **使用CDN**：加速静态资源加载
4. **代码分割**：按需加载减少初始包大小
