# Java Code
- Java files with the '2010-2019 soho team' copyright comment header are from the original system and should be used as reference for coding framework and architecture when checking new Java code for conflicts or mismatches.
- The AiService.java file contains the AI calling method/interface for the financial system.
- User prefers layered architecture (Controller -> Service -> Repository -> Entity) with RESTful APIs and unified JSON response format, without /api prefix in @RequestMapping.

# Financial System Project Setup
- For this financial system project, start the backend service normally and use 'yarn serve' to start the frontend service. The frontend service runs on port 8081 and includes an AI dashboard page at /ai-dashboard.
- User is using JDK 1.8 for this financial system project.
- User's Java 8 installation is located at /Users/<USER>/Library/Java/JavaVirtualMachines/azul-1.8.0_452/Contents/Home (Azul JDK 1.8.0_452).
- Test login credentials for the financial system: phone 13910138483, password 123456.
- Frontend pages access backend services with 'api/' prefix, and nginx is configured to route all 'api/' requests to backend interface calls.
- When working with this financial system, always check the existing codebase API calling conventions and nginx proxy configurations before implementing new endpoints, as the system has established patterns that should be followed.
- For the financial system login form, use phone number field instead of email, do not include registration functionality, and do not include '@稽土师金技术社区' in the footer.
- The financial system should be branded as 'AI模范智能体-财务' instead of 'AI智能财务系统'.
- For the financial system, use the original logo from https://www.aiform.com/logo-Photoroom.png with deep blue as main color and green as accent color.
- Production environment uses kuaiji.beltoo.vip and fin.aiform.com domains, with fin.aiform.com using Nginx proxy to backend at 127.0.0.1:9080 but missing WebSocket /ws/ location block causing 404 errors.
- This project must use JDK 1.8 only, installed at /Users/<USER>/Library/Java/JavaVirtualMachines/azul-1.8.0_452, and use './gradlew bs-server:bootRun' to start backend service.

# AI Features
- The AI voucher generation feature (科目AI增强) is located at http://localhost:8081/ai/subject-enhancement and should be placed in the menu under AI智能助手 -> 科目AI增强.
- AI voucher generation should support two main modes: 1) Bill-based generation (single bill or bill groups), 2) Receipt-based generation (single receipt or receipt groups).
- For AI voucher generation features, use tickets/ticket groups, associated bank receipts, or unassociated receipts/merge groups as data sources to generate accounting vouchers via LLM with manual confirmation required, and maintain UI consistency with AI智能关联 and AI智能归并 pages.
- AI voucher generation should intelligently combine bills (票据), receipts (回单), and chart of accounts (科目) using large language model capabilities to generate accounting vouchers.
- User prefers real LLM calls for AI voucher generation instead of demo/simulation mode, with manual confirmation before saving to database.
- For AI feature pages that aren't loading data properly, reference the implementation pattern used in http://localhost:8081/ai-relation-engine as a working example.
- Ticket groups (票据组) refer to merge groups (票据归并组) and should reference the API pattern at /api/merge-groups/receipts for implementation.
- User prefers to use actual DeepSeek LLM calls instead of simulation for AI analysis features in the financial system.
- AI智能归并 and AI智能关联 features have working DeepSeek API integration that can be used as reference for implementing similar functionality.
- User prefers to integrate OpenAI-compatible API interfaces for actual AI functionality and use LLM for intelligent field mapping from OCR results to database fields to handle different bank receipt formats.

# UI Rendering and Preferences
- For the financial system UI design, user prefers a tech-savvy feel combined with stable and reliable aesthetics that conveys trustworthiness for financial applications.
- When HeyUI components have display issues in modal dialogs, use native HTML input elements as fallback solution when HeyUI components fail to render.
- When HeyUI checkboxes don't work properly, user prefers to use HTML native input elements as fallback solution.
- User prefers checkboxes to be positioned at the beginning of each row rather than at the end, as this follows better UI principles.
- User prefers forms to redirect to list pages after successful save operations and image previews to show in floating modal windows rather than downloading files.

# Accounting Vouchers
- Accounting vouchers in the financial system should include year and period information in the format like '2025年第05期' (Year 2025 Period 05).

# OCR Data Handling
- User prefers parallel processing with 5-10 concurrent processes for image splitting and OCR recognition tasks to improve efficiency, rather than sequential execution.
- OCR recognition needs to integrate LLM capabilities for intelligent field mapping.
- Complete OCR information should be saved to notes field to avoid information loss.
- When implementing OCR data backup, avoid overwriting the remark field as it changes the field's semantic meaning and removes users' ability to add custom notes - need separate field or hybrid approach for OCR data storage.
- OCR recognition results should be formatted in a readable format and saved to the ocr_recognition_info field in both bill and bank receipt tables.
- Single record save functionality for tickets and bank receipts (in addition to batch import) should also implement OCR original information saving when uploading images and calling OCR recognition.
- For both single record and batch operations, bills and bank receipts should use the currently selected year/month/period from the system header instead of calculating from current timestamp.

# Database
- All database tables should be prefixed with "fxy_financial_" and AI configuration is stored in fxy_financial_ai_config table with DeepSeek settings persisted in database.
- All SQL scripts should be placed in the doc/database directory, and the README.md should record the addition time, function, scope of impact, and execution status.
- User prefers to consolidate all incremental SQL changes into a single aiform_update.sql file that should be executed after the base financial.sql and aiform.sql files.
- Unnecessary data import scripts and production synchronization scripts have been deleted; all necessary SQL updates are consolidated into doc/database/aiform_update.sql.
- For database remark/comment fields, prefer TEXT type over VARCHAR for consistency, as bill table already uses TEXT for remark field.
- For new environment deployment, exclude aiform.sql and only include tables in aiform_update.sql that are not already present in financial.sql.
- Use ALTER TABLE statements instead of DROP/CREATE for existing systems to avoid data loss, and remove obsolete bill_bank_receipts_relations table since entity_relations handles all associations.
- MySQL MCP has been configured and is available for database operations.