server {
    listen 80 ; 
    listen 443 ssl ; 
    server_name fin.aiform.com; 
    index index.php index.html index.htm default.php default.htm default.html; 
    proxy_set_header Host $host; 
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; 
    proxy_set_header X-Forwarded-Host $server_name; 
    proxy_set_header X-Real-IP $remote_addr; 
    proxy_http_version 1.1; 
    proxy_set_header Upgrade $http_upgrade; 
    proxy_set_header Connection "upgrade"; 
    access_log /www/sites/fin.aiform.com/log/access.log main; 
    error_log /www/sites/fin.aiform.com/log/error.log; 
    
    location ^~ /.well-known {
        allow all; 
        root /usr/share/nginx/html; 
    }
    
    # API代理配置
    location ^~ /api/ {
        index index.html index.htm; 
        if ( !-e $request_filename ) {
            rewrite ^/api/(.*)$ /$1 break; 
            proxy_pass http://127.0.0.1:9080; 
        }
        # 添加超时配置
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # WebSocket代理配置 - 新增
    location /ws/ {
        proxy_pass http://127.0.0.1:9080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        # WebSocket特殊配置
        proxy_buffering off;
        proxy_cache off;
    }
    
    # 文件上传代理配置 - 新增（如果需要）
    location ^~ /file/ {
        proxy_pass http://127.0.0.1:9080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        # 大文件上传配置
        client_max_body_size 100m;
        client_body_buffer_size 10m;
    }
    
    root /www/sites/fin.aiform.com/index/dist;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    error_page 404 /404.html;
    http2 on; 
    
    if ($scheme = http) {
        return 301 https://$host$request_uri; 
    }
    
    ssl_certificate /www/sites/fin.aiform.com/ssl/fullchain.pem; 
    ssl_certificate_key /www/sites/fin.aiform.com/ssl/privkey.pem; 
    ssl_protocols TLSv1.3 TLSv1.2; 
    ssl_ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:!aNULL:!eNULL:!EXPORT:!DSS:!DES:!RC4:!3DES:!MD5:!PSK:!KRB5:!SRP:!CAMELLIA:!SEED; 
    ssl_prefer_server_ciphers off; 
    ssl_session_cache shared:SSL:10m; 
    ssl_session_timeout 10m; 
    error_page 497 https://$host$request_uri; 
    proxy_set_header X-Forwarded-Proto https; 
    add_header Strict-Transport-Security "max-age=31536000"; 
}