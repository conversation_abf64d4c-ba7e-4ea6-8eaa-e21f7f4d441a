# 管理员页面独立nginx配置
# 域名: admin.aiform.com

server {
    listen 80;
    listen 443 ssl;
    server_name finadmin.aiform.com;
    
    # 基础配置
    index index.html;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Host $server_name;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    
    # 日志配置
    access_log /www/sites/finadmin.aiform.com/log/access.log main;
    error_log /www/sites/finadmin.aiform.com/log/error.log;
    
    # Let's Encrypt证书验证
    location ^~ /.well-known {
        allow all;
        root /usr/share/nginx/html;
    }
    
    # 管理员API代理配置（与主应用保持一致）
    location ^~ /api/ {
        index index.html index.htm;
        if ( !-e $request_filename ) {
            rewrite ^/api/(.*)$ /$1 break;
            proxy_pass http://127.0.0.1:9080;
        }
        # 超时配置
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # 大文件上传支持
        client_max_body_size 100m;
        client_body_buffer_size 10m;
    }
    
    # WebSocket支持（如果需要）
    location /ws/ {
        proxy_pass http://127.0.0.1:9080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        proxy_buffering off;
        proxy_cache off;
    }
    
    # 管理员页面静态文件
    root /www/sites/finadmin.aiform.com/dist;
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
        
        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }
    
    # 静态资源缓存优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        gzip_static on;
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
    }
    
    # HTML文件不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # HTTP2支持
    http2 on;
    
    # 强制HTTPS重定向
    if ($scheme = http) {
        return 301 https://$host$request_uri;
    }
    
    # SSL配置
    ssl_certificate /www/sites/finadmin.aiform.com/ssl/fullchain.pem;
    ssl_certificate_key /www/sites/finadmin.aiform.com/ssl/privkey.pem;
    ssl_protocols TLSv1.3 TLSv1.2;
    ssl_ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:!aNULL:!eNULL:!EXPORT:!DSS:!DES:!RC4:!3DES:!MD5:!PSK:!KRB5:!SRP:!CAMELLIA:!SEED;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    error_page 497 https://$host$request_uri;
    proxy_set_header X-Forwarded-Proto https;
    add_header Strict-Transport-Security "max-age=31536000" always;
    
    # 管理员页面访问控制（可选）
    # 可以根据需要启用以下配置
    
    # IP白名单示例
    # allow ***********/24;    # 内网访问
    # allow 10.0.0.0/8;        # VPN访问
    # deny all;                # 拒绝其他IP
    
    # 基本认证示例（双重保护）
    # auth_basic "Admin Area";
    # auth_basic_user_file /etc/nginx/.htpasswd;
}
