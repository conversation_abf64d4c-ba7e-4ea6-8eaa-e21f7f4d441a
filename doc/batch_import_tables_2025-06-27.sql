-- 批量导入功能数据表创建脚本 - 2025年6月27日
-- 支持PDF和图片的批量上传、识别和导入功能

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 批量导入任务表
-- =====================================================
CREATE TABLE IF NOT EXISTS `fxy_financial_batch_import_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID，使用UUID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `import_type` enum('BANK_RECEIPT','INVOICE') NOT NULL COMMENT '导入类型：银行回单/发票',
  `total_files` int NOT NULL DEFAULT '0' COMMENT '总文件数',
  `total_images` int NOT NULL DEFAULT '0' COMMENT '总图片数（PDF解析后）',
  `processed_files` int NOT NULL DEFAULT '0' COMMENT '已处理文件数',
  `processed_images` int NOT NULL DEFAULT '0' COMMENT '已处理图片数',
  `success_count` int NOT NULL DEFAULT '0' COMMENT '成功识别数量',
  `failed_count` int NOT NULL DEFAULT '0' COMMENT '失败数量',
  `status` enum('UPLOADING','PROCESSING','RECOGNIZING','PREVIEWING','SAVING','COMPLETED','FAILED','CANCELLED') NOT NULL DEFAULT 'UPLOADING' COMMENT '任务状态',
  `progress_percentage` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '进度百分比',
  `error_message` text COMMENT '错误信息',
  `start_time` datetime DEFAULT NULL COMMENT '开始处理时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `create_user` int NOT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_account_sets_user` (`account_sets_id`,`create_user`),
  KEY `idx_status` (`status`),
  KEY `idx_import_type` (`import_type`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='批量导入任务表';

-- =====================================================
-- 2. 批量导入明细表
-- =====================================================
CREATE TABLE IF NOT EXISTS `fxy_financial_batch_import_detail` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `file_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_url` varchar(500) NOT NULL COMMENT '原始文件URL',
  `file_type` enum('PDF','IMAGE') NOT NULL COMMENT '文件类型',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `page_number` int DEFAULT NULL COMMENT 'PDF页码（图片文件为NULL）',
  `image_url` varchar(500) DEFAULT NULL COMMENT '解析后的图片URL',
  `image_width` int DEFAULT NULL COMMENT '图片宽度',
  `image_height` int DEFAULT NULL COMMENT '图片高度',
  `recognition_result` json DEFAULT NULL COMMENT 'OCR识别原始结果',
  `parsed_data` json DEFAULT NULL COMMENT '解析后的结构化数据',
  `final_data` json DEFAULT NULL COMMENT '用户确认后的最终数据',
  `confidence_score` decimal(5,4) DEFAULT NULL COMMENT 'OCR识别置信度',
  `status` enum('PENDING','UPLOADING','PROCESSING','RECOGNIZING','SUCCESS','FAILED','SKIPPED') NOT NULL DEFAULT 'PENDING' COMMENT '处理状态',
  `error_message` text COMMENT '错误信息',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `processing_time` int DEFAULT NULL COMMENT '处理耗时（毫秒）',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_created_time` (`created_time`),
  CONSTRAINT `fk_batch_detail_task` FOREIGN KEY (`task_id`) REFERENCES `fxy_financial_batch_import_task` (`task_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='批量导入明细表';

-- =====================================================
-- 3. 批量导入结果表
-- =====================================================
CREATE TABLE IF NOT EXISTS `fxy_financial_batch_import_result` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `detail_id` int NOT NULL COMMENT '明细ID',
  `business_type` enum('BANK_RECEIPT','BILL') NOT NULL COMMENT '业务类型',
  `business_id` int DEFAULT NULL COMMENT '业务记录ID（保存成功后）',
  `import_status` enum('SUCCESS','FAILED','SKIPPED') NOT NULL COMMENT '导入状态',
  `error_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_detail_id` (`detail_id`),
  KEY `idx_business` (`business_type`,`business_id`),
  KEY `idx_import_status` (`import_status`),
  CONSTRAINT `fk_batch_result_task` FOREIGN KEY (`task_id`) REFERENCES `fxy_financial_batch_import_task` (`task_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_batch_result_detail` FOREIGN KEY (`detail_id`) REFERENCES `fxy_financial_batch_import_detail` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='批量导入结果表';

-- =====================================================
-- 4. 创建索引优化查询性能
-- =====================================================

-- 任务表复合索引
CREATE INDEX IF NOT EXISTS idx_task_status_type ON fxy_financial_batch_import_task(status, import_type);
CREATE INDEX IF NOT EXISTS idx_task_user_time ON fxy_financial_batch_import_task(create_user, created_time DESC);

-- 明细表复合索引
CREATE INDEX IF NOT EXISTS idx_detail_task_status ON fxy_financial_batch_import_detail(task_id, status);
CREATE INDEX IF NOT EXISTS idx_detail_file_status ON fxy_financial_batch_import_detail(file_type, status);

-- 结果表复合索引
CREATE INDEX IF NOT EXISTS idx_result_task_status ON fxy_financial_batch_import_result(task_id, import_status);

-- =====================================================
-- 5. 插入测试数据（可选）
-- =====================================================

-- 插入一个示例任务
INSERT INTO fxy_financial_batch_import_task (
    task_id, task_name, import_type, total_files, status, 
    account_sets_id, create_user
) VALUES (
    'test-task-001', '银行回单批量导入测试', 'BANK_RECEIPT', 0, 'UPLOADING',
    66, 1
) ON DUPLICATE KEY UPDATE task_name = task_name;

-- =====================================================
-- 6. 权限和安全设置
-- =====================================================

-- 确保只有相关用户可以访问自己账套的数据
-- 在应用层通过account_sets_id进行数据隔离

-- =====================================================
-- 7. 数据清理策略（建议）
-- =====================================================

/*
建议的数据清理策略：

1. 定期清理已完成的任务数据（保留30天）
2. 清理失败的临时文件
3. 压缩历史数据

示例清理脚本：
DELETE FROM fxy_financial_batch_import_task 
WHERE status IN ('COMPLETED', 'FAILED', 'CANCELLED') 
AND created_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
*/

-- =====================================================
-- 8. 表结构说明
-- =====================================================

/*
表结构设计说明：

1. fxy_financial_batch_import_task（任务表）
   - 记录每次批量导入的任务信息
   - 跟踪整体进度和状态
   - 支持任务的暂停、恢复、取消

2. fxy_financial_batch_import_detail（明细表）
   - 记录每个文件/图片的处理详情
   - 存储OCR识别结果和用户编辑后的数据
   - 支持单个文件的重试和跳过

3. fxy_financial_batch_import_result（结果表）
   - 记录最终的导入结果
   - 关联到具体的业务记录
   - 便于统计和问题追踪

状态流转：
UPLOADING -> PROCESSING -> RECOGNIZING -> PREVIEWING -> SAVING -> COMPLETED
                                                              -> FAILED
                                                              -> CANCELLED

文件处理流程：
PDF文件 -> 解析为多个图片 -> 逐个OCR识别 -> 用户确认编辑 -> 批量保存
图片文件 -> 直接OCR识别 -> 用户确认编辑 -> 批量保存
*/

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 脚本执行完成
-- =====================================================
-- 创建日期: 2025-06-27
-- 功能: 批量导入基础数据表
-- 说明: 支持PDF和图片的批量上传识别导入功能
