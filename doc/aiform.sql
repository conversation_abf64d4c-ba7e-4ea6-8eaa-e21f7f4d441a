CREATE TABLE `fxy_financial_bill` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bill_no` varchar(50) NOT NULL COMMENT '票据编号',
  `bill_date` date NOT NULL COMMENT '票据日期',
  `type` varchar(20) NOT NULL COMMENT '票据类型',
  `amount` decimal(15,2) NOT NULL COMMENT '金额',
  `issuer` varchar(100) NOT NULL COMMENT '开票方',
  `recipient` varchar(100) DEFAULT NULL COMMENT '收票方',
  `summary` varchar(200) NOT NULL COMMENT '摘要',
  `remark` text COMMENT '备注',
  `status` varchar(20) DEFAULT '未使用' COMMENT '状态',
  `related_voucher_id` int(11) DEFAULT NULL COMMENT '关联凭证ID',
  `account_sets_id` int(11) NOT NULL COMMENT '账套ID',
  `bill_year` int(4) NOT NULL COMMENT '票据年份',
  `bill_month` int(2) NOT NULL COMMENT '票据月份',
  `attachment_path` varchar(500) DEFAULT NULL COMMENT '附件路径',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_account_sets_id` (`account_sets_id`),
  KEY `idx_bill_year_month` (`bill_year`,`bill_month`),
  KEY `idx_bill_date` (`bill_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据表';

CREATE TABLE `fxy_financial_bill_bank_voucher_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bill_no` varchar(50) DEFAULT NULL COMMENT '票据编号',
  `voucher_no` varchar(50) DEFAULT NULL COMMENT '银证编号',
  `relation_amount` decimal(15,2) DEFAULT NULL COMMENT '关联金额（部分关联时使用）',
  `relation_type` varchar(20) DEFAULT 'FULL' COMMENT '关联类型：FULL-完全关联，PARTIAL-部分关联',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user_id` int(11) DEFAULT NULL COMMENT '创建人ID',
  `account_sets_id` int(11) NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_bank_voucher` (`bill_no`, `voucher_no`),
  KEY `idx_bill_no` (`bill_no`),
  KEY `idx_voucher_no` (`voucher_no`),
  KEY `idx_bill_voucher_no` (`bill_no`, `voucher_no`),
  KEY `idx_account_sets_id` (`account_sets_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据银证关联表';


-- 创建AI处理结果表
-- 修正后的AI处理结果表创建语句
CREATE TABLE fxy_financial_bill_ai_result (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    bill_id INT(11) NOT NULL COMMENT '关联的票据ID',  -- 改为INT(11)与bill表匹配
    ai_processed TINYINT(1) DEFAULT 0 COMMENT 'AI处理状态：0-未处理，1-已处理',
    confidence DECIMAL(5,4) DEFAULT NULL COMMENT 'AI置信度(0-1)',
    ai_analysis TEXT DEFAULT NULL COMMENT 'AI分析结果',
    suggested_subjects JSON DEFAULT NULL COMMENT '推荐科目信息(JSON格式)',
    debit_subjects JSON DEFAULT NULL COMMENT '借方科目推荐(JSON格式)',
    credit_subjects JSON DEFAULT NULL COMMENT '贷方科目推荐(JSON格式)',
    matching_reason TEXT DEFAULT NULL COMMENT 'AI匹配原因说明',
    ai_process_time DATETIME DEFAULT NULL COMMENT 'AI处理时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_bill_id (bill_id),
    INDEX idx_ai_processed (ai_processed),
    INDEX idx_confidence (confidence),
    FOREIGN KEY (bill_id) REFERENCES fxy_financial_bill(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据AI处理结果表';