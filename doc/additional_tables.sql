/*
 Additional Tables for Financial System
 
 This file contains tables that are not included in the base financial.sql file.
 These tables were added during development for new features including:
 - AI configuration and processing
 - Bill and bank receipt management
 - Document and receipt grouping
 - Entity relations management
 - Merge rules and tasks
 - Subject AI enhancement and matching templates

 Date: 2025-06-17
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ai_config
-- ----------------------------
DROP TABLE IF EXISTS `ai_config`;
CREATE TABLE `ai_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_key` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置键',
  `config_value` text COLLATE utf8mb4_general_ci COMMENT '配置值',
  `description` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI配置表';

-- ----------------------------
-- Table structure for fxy_financial_ai_config
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_ai_config`;
CREATE TABLE `fxy_financial_ai_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_config` (`account_sets_id`,`config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='AI配置表';

-- ----------------------------
-- Records of fxy_financial_ai_config
-- ----------------------------
INSERT INTO `fxy_financial_ai_config` VALUES (1,0,'ai_model_endpoint','https://api.openai.com/v1/chat/completions','AI模型API端点',1,'2025-06-13 18:25:32','2025-06-13 18:25:32');
INSERT INTO `fxy_financial_ai_config` VALUES (2,0,'ai_model_name','gpt-4','AI模型名称',1,'2025-06-13 18:25:32','2025-06-13 18:25:32');
INSERT INTO `fxy_financial_ai_config` VALUES (3,0,'confidence_threshold','0.7','置信度阈值',1,'2025-06-13 18:25:32','2025-06-13 18:25:32');
INSERT INTO `fxy_financial_ai_config` VALUES (4,0,'max_retry_times','3','最大重试次数',1,'2025-06-13 18:25:32','2025-06-13 18:25:32');
INSERT INTO `fxy_financial_ai_config` VALUES (5,0,'enable_learning','true','是否启用学习功能',1,'2025-06-13 18:25:32','2025-06-13 18:25:32');
INSERT INTO `fxy_financial_ai_config` VALUES (6,0,'batch_size','10','批处理大小',1,'2025-06-13 18:25:32','2025-06-13 18:25:32');
INSERT INTO `fxy_financial_ai_config` VALUES (7,0,'enabled','true','AI功能是否启用',1,'2025-06-17 17:53:22','2025-06-17 18:10:18');
INSERT INTO `fxy_financial_ai_config` VALUES (8,0,'base_url','https://api.deepseek.com/v1','API基础URL',1,'2025-06-17 17:53:22','2025-06-17 18:10:18');
INSERT INTO `fxy_financial_ai_config` VALUES (9,0,'api_key','sk-a3885c72ecf34b929d1738dddaa2cb4d','API密钥',1,'2025-06-17 17:53:22','2025-06-17 18:10:18');
INSERT INTO `fxy_financial_ai_config` VALUES (10,0,'default_model','deepseek-chat','默认模型',1,'2025-06-17 17:53:22','2025-06-17 18:10:18');
INSERT INTO `fxy_financial_ai_config` VALUES (11,0,'timeout','60','请求超时时间(秒)',1,'2025-06-17 17:53:22','2025-06-17 18:10:18');
INSERT INTO `fxy_financial_ai_config` VALUES (12,0,'max_retries','3','最大重试次数',1,'2025-06-17 17:53:22','2025-06-17 18:10:18');
INSERT INTO `fxy_financial_ai_config` VALUES (13,0,'temperature','0.7','温度参数',1,'2025-06-17 17:53:22','2025-06-17 18:10:18');
INSERT INTO `fxy_financial_ai_config` VALUES (14,0,'max_tokens','2000','最大token数',1,'2025-06-17 17:53:22','2025-06-17 18:10:18');

-- ----------------------------
-- Table structure for fxy_financial_ai_matching_history
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_ai_matching_history`;
CREATE TABLE `fxy_financial_ai_matching_history` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型：bank_receipts/bill',
  `business_id` int NOT NULL COMMENT '业务ID（银证ID或票据ID）',
  `matched_subjects` json NOT NULL COMMENT '匹配的科目信息',
  `confidence_score` decimal(3,2) DEFAULT NULL COMMENT '置信度分数',
  `ai_reasoning` text COMMENT 'AI推理过程',
  `user_feedback` tinyint DEFAULT NULL COMMENT '用户反馈：1正确，0错误，NULL未反馈',
  `processing_time` int DEFAULT NULL COMMENT '处理时间（毫秒）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` int DEFAULT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_account_sets_business` (`account_sets_id`,`business_type`,`business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='AI匹配历史记录表';

-- ----------------------------
-- Table structure for fxy_financial_bank_receipts
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_bank_receipts`;
CREATE TABLE `fxy_financial_bank_receipts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `receipts_no` varchar(20) DEFAULT NULL COMMENT '单据编号',
  `receipts_date` date DEFAULT NULL COMMENT '单据日期',
  `type` varchar(20) DEFAULT NULL COMMENT '类型：收入/支出',
  `amount` decimal(15,2) DEFAULT '0.00' COMMENT '金额',
  `counterparty` varchar(100) DEFAULT NULL COMMENT '收付方（交易对手方）',
  `summary` varchar(255) DEFAULT NULL COMMENT '摘要',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `receipt_num` int DEFAULT '0' COMMENT '附单据数量',
  `create_member` int DEFAULT NULL COMMENT '制单人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `account_sets_id` int DEFAULT NULL COMMENT '账套ID',
  `receipts_year` int DEFAULT NULL COMMENT '年',
  `receipts_month` int DEFAULT NULL COMMENT '月',
  `receipt_group_id` varchar(36) DEFAULT NULL COMMENT '银证归并组ID',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `payment_method` varchar(30) DEFAULT NULL COMMENT '支付方式',
  `file_path` varchar(255) DEFAULT NULL COMMENT '文件路径',
  PRIMARY KEY (`id`),
  KEY `idx_account_sets_id` (`account_sets_id`),
  KEY `idx_receipts_year_month` (`receipts_year`,`receipts_month`),
  KEY `idx_receipts_date` (`receipts_date`),
  KEY `idx_receipt_group` (`receipt_group_id`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='银行回单表（银行回单或公司出纳能提供的资金收付证明）';

-- ----------------------------
-- Table structure for fxy_financial_bill
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_bill`;
CREATE TABLE `fxy_financial_bill` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bill_no` varchar(50) NOT NULL COMMENT '票据编号',
  `bill_date` date NOT NULL COMMENT '票据日期',
  `type` varchar(20) NOT NULL COMMENT '票据类型',
  `amount` decimal(15,2) NOT NULL COMMENT '金额',
  `issuer` varchar(100) NOT NULL COMMENT '开票方',
  `recipient` varchar(100) DEFAULT NULL COMMENT '收票方',
  `summary` varchar(200) NOT NULL COMMENT '摘要',
  `remark` text COMMENT '备注',
  `status` varchar(20) DEFAULT '未使用' COMMENT '状态',
  `doc_group_id` varchar(36) DEFAULT NULL COMMENT '票据归并组ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `bill_year` int NOT NULL COMMENT '票据年份',
  `bill_month` int NOT NULL COMMENT '票据月份',
  `invoice_number` varchar(50) DEFAULT NULL COMMENT '发票号码',
  `attachment_path` varchar(500) DEFAULT NULL COMMENT '附件路径',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_account_sets_id` (`account_sets_id`),
  KEY `idx_bill_year_month` (`bill_year`,`bill_month`),
  KEY `idx_bill_date` (`bill_date`),
  KEY `idx_doc_group` (`doc_group_id`),
  UNIQUE KEY `uk_invoice_number_account_sets` (`invoice_number`, `account_sets_id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='票据表';

-- ----------------------------
-- Table structure for fxy_financial_bill_ai_result
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_bill_ai_result`;
CREATE TABLE `fxy_financial_bill_ai_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bill_id` int NOT NULL COMMENT '关联的票据ID',
  `ai_processed` tinyint(1) DEFAULT '0' COMMENT 'AI处理状态：0-未处理，1-已处理',
  `confidence` decimal(5,4) DEFAULT NULL COMMENT 'AI置信度(0-1)',
  `ai_analysis` text COMMENT 'AI分析结果',
  `suggested_subjects` json DEFAULT NULL COMMENT '推荐科目信息(JSON格式)',
  `debit_subjects` json DEFAULT NULL COMMENT '借方科目推荐(JSON格式)',
  `credit_subjects` json DEFAULT NULL COMMENT '贷方科目推荐(JSON格式)',
  `matching_reason` text COMMENT 'AI匹配原因说明',
  `ai_process_time` datetime DEFAULT NULL COMMENT 'AI处理时间',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_ai_processed` (`ai_processed`),
  KEY `idx_confidence` (`confidence`),
  CONSTRAINT `fxy_financial_bill_ai_result_ibfk_1` FOREIGN KEY (`bill_id`) REFERENCES `fxy_financial_bill` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='票据AI处理结果表';

-- ----------------------------
-- Table structure for fxy_financial_document_groups
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_document_groups`;
CREATE TABLE `fxy_financial_document_groups` (
  `group_id` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '组ID，使用UUID',
  `group_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '组名称',
  `merge_rule_id` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '使用的归并规则ID',
  `rule_params` json DEFAULT NULL COMMENT '规则参数',
  `group_summary` text COLLATE utf8mb4_bin COMMENT '组摘要信息',
  `total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '组内总金额',
  `item_count` int NOT NULL DEFAULT '0' COMMENT '组内项目数量',
  `status` enum('ACTIVE','DISSOLVED') COLLATE utf8mb4_bin NOT NULL DEFAULT 'ACTIVE' COMMENT '组状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`group_id`),
  KEY `idx_account_sets_status` (`account_sets_id`,`status`),
  KEY `idx_merge_rule` (`merge_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='票据归并组表';

-- ----------------------------
-- Table structure for fxy_financial_entity_relations
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_entity_relations`;
CREATE TABLE `fxy_financial_entity_relations` (
  `relation_id` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '关联ID，使用UUID',
  `source_type` enum('DOCUMENT','DOCUMENT_GROUP','RECEIPT','RECEIPT_GROUP') COLLATE utf8mb4_bin NOT NULL COMMENT '源类型',
  `source_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '源ID',
  `target_type` enum('DOCUMENT','DOCUMENT_GROUP','RECEIPT','RECEIPT_GROUP') COLLATE utf8mb4_bin NOT NULL COMMENT '目标类型',
  `target_id` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '目标ID',
  `relation_type` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT 'ASSOCIATED' COMMENT '关联类型',
  `relation_amount` decimal(15,2) DEFAULT NULL COMMENT '关联金额（部分关联时使用）',
  `relation_note` text COLLATE utf8mb4_bin COMMENT '关联备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int NOT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`relation_id`),
  UNIQUE KEY `uk_relation` (`source_type`,`source_id`,`target_type`,`target_id`,`account_sets_id`),
  KEY `idx_source` (`source_type`,`source_id`),
  KEY `idx_target` (`target_type`,`target_id`),
  KEY `idx_account_sets` (`account_sets_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='统一关联表';

-- ----------------------------
-- Table structure for fxy_financial_merge_rules
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_merge_rules`;
CREATE TABLE `fxy_financial_merge_rules` (
  `rule_id` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '规则ID，使用UUID',
  `rule_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '规则名称',
  `rule_description` text COLLATE utf8mb4_bin COMMENT '规则描述',
  `applicable_entity` enum('DOCUMENT','RECEIPT','BOTH') COLLATE utf8mb4_bin NOT NULL DEFAULT 'BOTH' COMMENT '适用实体：票据/银证/两者',
  `rule_logic` json NOT NULL COMMENT '规则逻辑配置',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`rule_id`),
  KEY `idx_account_sets_entity` (`account_sets_id`,`applicable_entity`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='归并规则表';

-- ----------------------------
-- Table structure for fxy_financial_merge_tasks
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_merge_tasks`;
CREATE TABLE `fxy_financial_merge_tasks` (
  `task_id` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '任务ID',
  `task_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '任务名称',
  `task_type` enum('DOCUMENT_MERGE','RECEIPT_MERGE','RELATION_CREATE') COLLATE utf8mb4_bin NOT NULL COMMENT '任务类型',
  `rule_id` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '使用的规则ID',
  `status` enum('PENDING','RUNNING','COMPLETED','FAILED') COLLATE utf8mb4_bin NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
  `progress` int NOT NULL DEFAULT '0' COMMENT '进度百分比',
  `total_items` int NOT NULL DEFAULT '0' COMMENT '总项目数',
  `processed_items` int NOT NULL DEFAULT '0' COMMENT '已处理项目数',
  `result_summary` json DEFAULT NULL COMMENT '结果摘要',
  `error_message` text COLLATE utf8mb4_bin COMMENT '错误信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `started_at` datetime DEFAULT NULL COMMENT '开始时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `created_by` int NOT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='归并任务表';

-- ----------------------------
-- Table structure for fxy_financial_receipt_groups
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_receipt_groups`;
CREATE TABLE `fxy_financial_receipt_groups` (
  `group_id` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '组ID，使用UUID',
  `group_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '组名称',
  `merge_rule_id` varchar(36) COLLATE utf8mb4_bin NOT NULL COMMENT '使用的归并规则ID',
  `rule_params` json DEFAULT NULL COMMENT '规则参数',
  `group_summary` text COLLATE utf8mb4_bin COMMENT '组摘要信息',
  `total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '组内总金额',
  `item_count` int NOT NULL DEFAULT '0' COMMENT '组内项目数量',
  `status` enum('ACTIVE','DISSOLVED') COLLATE utf8mb4_bin NOT NULL DEFAULT 'ACTIVE' COMMENT '组状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`group_id`),
  KEY `idx_account_sets_status` (`account_sets_id`,`status`),
  KEY `idx_merge_rule` (`merge_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='银证归并组表';

-- ----------------------------
-- Table structure for fxy_financial_subject_ai_enhancement
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_subject_ai_enhancement`;
CREATE TABLE `fxy_financial_subject_ai_enhancement` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `subject_id` int NOT NULL COMMENT '科目ID，关联fxy_financial_subject.id',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `ai_description` text COMMENT 'AI描述信息，用于大模型理解科目用途',
  `ai_keywords` varchar(500) DEFAULT NULL COMMENT 'AI关键词，逗号分隔，用于匹配',
  `matching_rules` json DEFAULT NULL COMMENT '匹配规则配置，包含条件表达式、权重等',
  `usage_frequency` int DEFAULT '0' COMMENT '使用频率统计',
  `last_matched_date` datetime DEFAULT NULL COMMENT '最后匹配时间',
  `confidence_score` decimal(5,4) DEFAULT '0.0000' COMMENT '匹配置信度评分',
  `learning_data` json DEFAULT NULL COMMENT '学习数据，存储历史匹配结果用于优化',
  `custom_rules` json DEFAULT NULL COMMENT '自定义规则，用户可配置的特殊匹配逻辑',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` int DEFAULT NULL COMMENT '创建用户ID',
  `update_user` int DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_subject_account` (`subject_id`,`account_sets_id`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_usage_frequency` (`usage_frequency` DESC),
  KEY `idx_last_matched` (`last_matched_date`),
  KEY `idx_confidence` (`confidence_score` DESC),
  CONSTRAINT `fk_subject_ai_subject` FOREIGN KEY (`subject_id`) REFERENCES `fxy_financial_subject` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='科目AI增强表';

-- ----------------------------
-- Table structure for fxy_financial_subject_matching_template
-- ----------------------------
DROP TABLE IF EXISTS `fxy_financial_subject_matching_template`;
CREATE TABLE `fxy_financial_subject_matching_template` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `business_scenario` varchar(50) NOT NULL COMMENT '业务场景',
  `matching_conditions` json NOT NULL COMMENT '匹配条件',
  `subject_mapping` json NOT NULL COMMENT '科目映射规则',
  `priority` int DEFAULT '0' COMMENT '优先级',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `success_rate` decimal(5,2) DEFAULT '0.00' COMMENT '成功率',
  `usage_count` int DEFAULT '0' COMMENT '使用次数',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` int DEFAULT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_account_scenario` (`account_sets_id`,`business_scenario`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='科目匹配模板表';

-- ----------------------------
-- Records of fxy_financial_subject_matching_template
-- ----------------------------
INSERT INTO `fxy_financial_subject_matching_template` VALUES (1,0,'银行收款模板','bank_income','{\"type\": \"收入\", \"keywords\": [\"收款\", \"转入\", \"存款\"]}','{\"debit\": [{\"code\": \"1002\", \"name\": \"银行存款\"}], \"credit\": [{\"code\": \"6001\", \"name\": \"主营业务收入\"}]}',10,1,0.00,0,'2025-06-13 18:25:32','2025-06-13 18:25:32',NULL);
INSERT INTO `fxy_financial_subject_matching_template` VALUES (2,0,'银行付款模板','bank_expense','{\"type\": \"支出\", \"keywords\": [\"付款\", \"转出\", \"支取\"]}','{\"debit\": [{\"code\": \"6401\", \"name\": \"主营业务成本\"}], \"credit\": [{\"code\": \"1002\", \"name\": \"银行存款\"}]}',10,1,0.00,0,'2025-06-13 18:25:32','2025-06-13 18:25:32',NULL);
INSERT INTO `fxy_financial_subject_matching_template` VALUES (3,0,'现金收款模板','cash_income','{\"type\": \"收入\", \"keywords\": [\"现金\", \"收款\"]}','{\"debit\": [{\"code\": \"1001\", \"name\": \"库存现金\"}], \"credit\": [{\"code\": \"6001\", \"name\": \"主营业务收入\"}]}',8,1,0.00,0,'2025-06-13 18:25:32','2025-06-13 18:25:32',NULL);

SET FOREIGN_KEY_CHECKS = 1;
