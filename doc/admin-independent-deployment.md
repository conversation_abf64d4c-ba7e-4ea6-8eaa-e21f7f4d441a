# 管理员页面独立部署指南

## 概述

将管理员页面独立部署到子域名 `admin.aiform.com`，与主应用完全分离。

## 部署架构

```
主应用域名: https://fin.aiform.com/
管理员域名: https://admin.aiform.com/
后端服务: http://127.0.0.1:9080 (共享)
```

## 优势分析

### 🔒 安全优势
- **域名隔离**：管理员页面完全独立，降低安全风险
- **访问控制**：可为管理员域名单独配置IP白名单、基本认证
- **SSL证书**：可使用独立证书，增强安全性
- **日志分离**：独立的访问日志，便于安全审计

### 🚀 运维优势
- **独立部署**：管理员页面更新不影响主应用
- **配置隔离**：nginx配置完全分离，降低配置错误风险
- **监控分离**：可独立监控管理员页面的性能和可用性
- **扩展性好**：未来可独立扩展到不同服务器

### 👥 用户体验优势
- **清晰区分**：用户能明确区分管理后台和业务系统
- **专业形象**：独立域名提升管理系统的专业性
- **书签友好**：管理员可单独收藏管理后台地址

## 部署步骤

### 1. DNS配置

添加子域名解析：

```dns
# 添加A记录
admin.aiform.com    A    YOUR_SERVER_IP

# 或者添加CNAME记录（如果使用CDN）
admin.aiform.com    CNAME    fin.aiform.com
```

### 2. 目录结构创建

```bash
# 创建管理员页面目录
sudo mkdir -p /www/sites/admin.aiform.com/{dist,ssl,log}

# 设置权限
sudo chown -R www-data:www-data /www/sites/admin.aiform.com/
sudo chmod -R 755 /www/sites/admin.aiform.com/
```

### 3. SSL证书配置

#### 方案A：使用通配符证书（推荐）

```bash
# 申请通配符证书
certbot certonly --dns-cloudflare \
  --dns-cloudflare-credentials ~/.secrets/cloudflare.ini \
  -d "*.aiform.com" \
  -d "aiform.com"

# 复制证书到管理员目录
sudo cp /etc/letsencrypt/live/aiform.com/fullchain.pem /www/sites/admin.aiform.com/ssl/
sudo cp /etc/letsencrypt/live/aiform.com/privkey.pem /www/sites/admin.aiform.com/ssl/
```

#### 方案B：独立证书

```bash
# 为管理员域名单独申请证书
certbot certonly --webroot \
  -w /www/sites/admin.aiform.com/dist \
  -d admin.aiform.com

# 复制证书
sudo cp /etc/letsencrypt/live/admin.aiform.com/fullchain.pem /www/sites/admin.aiform.com/ssl/
sudo cp /etc/letsencrypt/live/admin.aiform.com/privkey.pem /www/sites/admin.aiform.com/ssl/
```

### 4. 构建和部署前端

```bash
# 进入管理员前端目录
cd admin-frontend

# 更新环境变量（如果需要）
# 编辑 .env.production
echo "VITE_API_BASE_URL=/api" > .env.production

# 构建
yarn build

# 部署到管理员域名目录
sudo rm -rf /www/sites/admin.aiform.com/dist/*
sudo cp -r dist/* /www/sites/admin.aiform.com/dist/
sudo chown -R www-data:www-data /www/sites/admin.aiform.com/dist/
```

### 5. nginx配置

```bash
# 复制管理员nginx配置
sudo cp doc/config/nginx-admin.conf /etc/nginx/sites-available/admin.aiform.com

# 启用配置
sudo ln -s /etc/nginx/sites-available/admin.aiform.com /etc/nginx/sites-enabled/

# 更新主应用配置（移除管理员相关配置）
sudo cp doc/config/nginx.conf /etc/nginx/sites-available/fin.aiform.com

# 测试配置
sudo nginx -t

# 重新加载nginx
sudo systemctl reload nginx
```

### 6. 验证部署

```bash
# 测试主应用
curl -I https://fin.aiform.com/

# 测试管理员页面
curl -I https://admin.aiform.com/

# 测试API转发
curl -I https://admin.aiform.com/api/admin/auth/check-session

# 测试重定向（旧路径）
curl -I https://fin.aiform.com/admin/
```

## 安全配置

### IP白名单配置

编辑 `/etc/nginx/sites-available/admin.aiform.com`：

```nginx
server {
    # ... 其他配置
    
    # 只允许特定IP访问
    allow ***********/24;    # 内网
    allow 10.0.0.0/8;        # VPN
    allow *******;           # 特定公网IP
    deny all;                # 拒绝其他所有IP
    
    # ... 其他配置
}
```

### 基本认证配置

```bash
# 安装htpasswd工具
sudo apt-get install apache2-utils

# 创建密码文件
sudo htpasswd -c /etc/nginx/.htpasswd-admin admin

# 在nginx配置中启用
# auth_basic "Admin Area";
# auth_basic_user_file /etc/nginx/.htpasswd-admin;
```

### 防火墙配置

```bash
# 如果使用ufw
sudo ufw allow from ***********/24 to any port 443 comment 'Admin HTTPS from internal network'
sudo ufw allow from 10.0.0.0/8 to any port 443 comment 'Admin HTTPS from VPN'
```

## 监控和日志

### 日志配置

管理员页面有独立的日志文件：

```bash
# 访问日志
tail -f /www/sites/admin.aiform.com/log/access.log

# 错误日志
tail -f /www/sites/admin.aiform.com/log/error.log

# 日志轮转配置
sudo vim /etc/logrotate.d/nginx-admin
```

### 监控脚本

创建健康检查脚本：

```bash
#!/bin/bash
# /usr/local/bin/check-admin-health.sh

ADMIN_URL="https://admin.aiform.com"
MAIN_URL="https://fin.aiform.com"

# 检查管理员页面
admin_status=$(curl -s -o /dev/null -w "%{http_code}" $ADMIN_URL)
if [ $admin_status -eq 200 ]; then
    echo "✅ 管理员页面正常: $ADMIN_URL"
else
    echo "❌ 管理员页面异常: $ADMIN_URL (状态码: $admin_status)"
fi

# 检查主应用
main_status=$(curl -s -o /dev/null -w "%{http_code}" $MAIN_URL)
if [ $main_status -eq 200 ]; then
    echo "✅ 主应用正常: $MAIN_URL"
else
    echo "❌ 主应用异常: $MAIN_URL (状态码: $main_status)"
fi

# 检查重定向
redirect_status=$(curl -s -o /dev/null -w "%{http_code}" "$MAIN_URL/admin/")
if [ $redirect_status -eq 301 ]; then
    echo "✅ 重定向正常: $MAIN_URL/admin/ → $ADMIN_URL"
else
    echo "❌ 重定向异常: $MAIN_URL/admin/ (状态码: $redirect_status)"
fi
```

## 自动化部署脚本

创建部署脚本：

```bash
#!/bin/bash
# deploy-admin-independent.sh

set -e

echo "🚀 开始部署管理员页面到独立域名..."

# 构建前端
echo "📦 构建前端..."
cd admin-frontend
yarn install
yarn build

# 备份当前版本
echo "💾 备份当前版本..."
sudo cp -r /www/sites/admin.aiform.com/dist /www/sites/admin.aiform.com/dist.backup.$(date +%Y%m%d_%H%M%S)

# 部署新版本
echo "🔄 部署新版本..."
sudo rm -rf /www/sites/admin.aiform.com/dist/*
sudo cp -r dist/* /www/sites/admin.aiform.com/dist/
sudo chown -R www-data:www-data /www/sites/admin.aiform.com/dist/

# 测试nginx配置
echo "🔧 测试nginx配置..."
sudo nginx -t

# 重新加载nginx
echo "🔄 重新加载nginx..."
sudo systemctl reload nginx

# 健康检查
echo "🏥 健康检查..."
sleep 2
/usr/local/bin/check-admin-health.sh

echo "✅ 管理员页面部署完成！"
echo "🌐 访问地址: https://admin.aiform.com/"
```

## 故障排除

### 常见问题

1. **DNS解析失败**
   ```bash
   # 检查DNS解析
   nslookup admin.aiform.com
   dig admin.aiform.com
   ```

2. **SSL证书问题**
   ```bash
   # 检查证书
   openssl x509 -in /www/sites/admin.aiform.com/ssl/fullchain.pem -text -noout
   ```

3. **权限问题**
   ```bash
   # 检查文件权限
   ls -la /www/sites/admin.aiform.com/dist/
   ```

### 回滚方案

```bash
# 快速回滚到备份版本
sudo rm -rf /www/sites/admin.aiform.com/dist/*
sudo cp -r /www/sites/admin.aiform.com/dist.backup.YYYYMMDD_HHMMSS/* /www/sites/admin.aiform.com/dist/
sudo chown -R www-data:www-data /www/sites/admin.aiform.com/dist/
sudo systemctl reload nginx
```

## 总结

独立部署管理员页面到 `admin.aiform.com` 提供了：

✅ **更好的安全性**：域名隔离、独立访问控制
✅ **更清晰的架构**：前后端完全分离
✅ **更好的运维体验**：独立部署、监控、日志
✅ **更专业的用户体验**：清晰的功能区分

这种部署方式特别适合企业级应用，能够提供更好的安全性和可维护性。
