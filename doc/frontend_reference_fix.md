# 前端引用修复总结

## 问题描述
前端编译时出现错误：
```
This relative module was not found:
* ./BankVoucherAiPanel.vue in ./src/components/ai-accounting/AiAccountingPanel.vue
```

这是因为我们将 `BankVoucherAiPanel.vue` 重命名为 `BankReceiptsAiPanel.vue`，但前端代码中还有对旧组件名的引用。

## 修复的文件和内容

### 1. 组件引用修复

#### `front-end/src/components/ai-accounting/AiAccountingPanel.vue`
- **Import语句**: `BankVoucherAiPanel` → `BankReceiptsAiPanel`
- **组件注册**: `BankVoucherAiPanel` → `BankReceiptsAiPanel`
- **Tab名称**: `bankVoucher` → `bankReceipts`
- **Tab标题**: "银证智能处理" → "银行回单智能处理"
- **Ref引用**: `bankVoucherPanel` → `bankReceiptsPanel`

### 2. 数据模型修复

#### `front-end/src/components/ai-accounting/PreviewMatchingResult.vue`
- **Props**: `bankVoucher` → `bankReceipts`
- **模板引用**: 
  - `bankVoucher.voucherNo` → `bankReceipts.receiptsNo`
  - `bankVoucher.voucherDate` → `bankReceipts.receiptsDate`
  - `bankVoucher.amount` → `bankReceipts.amount`
  - `bankVoucher.summary` → `bankReceipts.summary`
  - `bankVoucher.type` → `bankReceipts.type`
- **标签文本**: "银证号" → "银行回单号"
- **事件发射**: `this.bankVoucher` → `this.bankReceipts`

#### `front-end/src/components/BillSelector.vue`
- **Props**: 
  - `bankVoucherAmount` → `bankReceiptsAmount`
  - `bankVoucherCounterparty` → `bankReceiptsCounterparty`
- **Watch方法**: 对应的监听器名称更新
- **日志输出**: "银证金额/收付方" → "银行回单金额/收付方"
- **API调用**: `this.$api.bankVoucher` → `this.$api.bankReceipts`

### 3. API接口修复

#### `front-end/src/api/ai-accounting.js`
- **方法名**: 
  - `matchSubjectsForBankVoucher` → `matchSubjectsForBankReceipts`
  - 参数名: `bankVoucherId` → `bankReceiptsId`
- **URL路径**: 
  - `/bank-voucher/` → `/bank-receipts/`
  - `/generate-voucher/` → `/generate-voucher/` (保持不变，因为生成的是凭证)
  - `/preview-voucher/` → `/preview-voucher/` (保持不变，因为预览的是凭证)
- **参数名**: `bankVoucherIds` → `bankReceiptsIds`
- **注释**: "银证" → "银行回单"

#### `front-end/src/api/upload.js`
- **默认文件夹**: `bank-voucher` → `bank-receipts`

### 4. 页面视图修复

#### `front-end/src/views/aimatch/AiAccounting.vue`
- **统计数据**: `moduleStats.bankVoucher` → `moduleStats.bankReceipts`
- **导航路径**: `/ai/bank-voucher` → `/ai/bank-receipts`
- **数据结构**: 所有 `bankVoucher` 对象重命名为 `bankReceipts`

## 修复后的文件结构

### 组件文件
```
front-end/src/components/ai-accounting/
├── AiAccountingPanel.vue          ✅ 已修复
├── BankReceiptsAiPanel.vue        ✅ 新名称
├── PreviewMatchingResult.vue      ✅ 已修复
└── ...
```

### API文件
```
front-end/src/api/
├── ai-accounting.js               ✅ 已修复
├── upload.js                      ✅ 已修复
└── ...
```

### 页面文件
```
front-end/src/views/aimatch/
├── AiAccounting.vue               ✅ 已修复
└── ...
```

## 验证要点

1. ✅ 所有组件引用已更新
2. ✅ 所有API方法名已更新
3. ✅ 所有URL路径已更新
4. ✅ 所有数据模型字段已更新
5. ✅ 所有用户界面文本已更新

## 注意事项

1. **后端兼容性**: 确保后端API路径也相应更新
2. **路由配置**: 可能需要更新前端路由配置
3. **测试**: 建议全面测试AI会计功能
4. **文档**: 更新相关的API文档和用户手册

## 术语统一

| 旧术语 | 新术语 | 说明 |
|--------|--------|------|
| 银证 | 银行回单 | 更准确的业务术语 |
| BankVoucher | BankReceipts | 代码中的命名 |
| bank-voucher | bank-receipts | URL和文件路径 |
| voucherNo | receiptsNo | 单据编号字段 |
| voucherDate | receiptsDate | 单据日期字段 |

这次修复确保了前端代码与重命名后的组件和业务概念保持一致，提高了代码的可读性和维护性。
