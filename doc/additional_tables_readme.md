# 新增数据库表说明

本文档说明了除 `financial.sql` 基础文件之外新增的数据库表结构和用途。

## 文件说明

- `additional_tables.sql` - 包含所有新增表的结构定义
- `additional_tables_data.sql` - 包含新增表的示例数据
- `additional_tables_readme.md` - 本说明文档

## 新增表列表

### 1. AI 配置相关表

#### `ai_config`
- **用途**: 全局AI配置表（已废弃，建议使用 fxy_financial_ai_config）
- **特点**: 简单的键值对配置存储

#### `fxy_financial_ai_config`
- **用途**: 按账套的AI配置表
- **功能**: 
  - 存储每个账套的AI配置信息
  - 支持API端点、模型名称、置信度阈值等配置
  - 支持启用/禁用状态管理

#### `fxy_financial_ai_matching_history`
- **用途**: AI匹配历史记录表
- **功能**:
  - 记录AI对票据和银证的科目匹配历史
  - 存储置信度分数和推理过程
  - 支持用户反馈收集用于模型优化

### 2. 业务数据表

#### `fxy_financial_bank_receipts`
- **用途**: 银行回单表
- **功能**:
  - 存储银行回单或资金收付证明
  - 支持收入/支出分类
  - 关联银证归并组
  - 支持文件附件存储

#### `fxy_financial_bill`
- **用途**: 票据表
- **功能**:
  - 存储各类票据信息（发票、收据等）
  - 支持票据状态管理
  - 关联票据归并组
  - 支持附件路径存储

#### `fxy_financial_bill_ai_result`
- **用途**: 票据AI处理结果表
- **功能**:
  - 存储AI对票据的分析结果
  - 包含推荐的借贷方科目
  - 记录AI处理状态和置信度

### 3. 归并和分组表

#### `fxy_financial_document_groups`
- **用途**: 票据归并组表
- **功能**:
  - 管理票据的归并分组
  - 支持基于规则的自动归并
  - 记录组内统计信息（总金额、项目数量）

#### `fxy_financial_receipt_groups`
- **用途**: 银证归并组表
- **功能**:
  - 管理银行回单的归并分组
  - 支持基于规则的自动归并
  - 记录组内统计信息

#### `fxy_financial_merge_rules`
- **用途**: 归并规则表
- **功能**:
  - 定义票据和银证的归并规则
  - 支持多种归并策略（同人同日、金额范围、时间窗口等）
  - 规则逻辑以JSON格式存储

#### `fxy_financial_merge_tasks`
- **用途**: 归并任务表
- **功能**:
  - 管理归并任务的执行状态
  - 跟踪任务进度和结果
  - 支持异步任务处理

### 4. 关联关系表

#### `fxy_financial_entity_relations`
- **用途**: 统一关联表
- **功能**:
  - 管理票据、银证及其组之间的关联关系
  - 支持多种关联类型（匹配、关联等）
  - 替代原有的独立关联表设计

### 5. AI增强表

#### `fxy_financial_subject_ai_enhancement`
- **用途**: 科目AI增强表
- **功能**:
  - 为会计科目添加AI描述和关键词
  - 存储匹配规则和学习数据
  - 支持使用频率统计和置信度评分

#### `fxy_financial_subject_matching_template`
- **用途**: 科目匹配模板表
- **功能**:
  - 定义业务场景的科目匹配模板
  - 支持条件匹配和科目映射规则
  - 记录模板使用统计和成功率

## 系统架构说明

### 模块划分
1. **票据模块**: 处理票据的CRUD操作和OCR识别
2. **归并关联模块**: 处理基于规则的分组和关联
3. **AI智能助手模块**: 利用LLM进行智能分组和科目匹配

### 数据流程
1. 票据/银证录入 → 基础数据表
2. 规则引擎处理 → 归并分组
3. AI分析处理 → 科目推荐
4. 用户确认 → 生成凭证

## 部署说明

1. 首先执行 `financial.sql` 创建基础表结构
2. 然后执行 `additional_tables.sql` 创建新增表结构
3. 最后执行 `additional_tables_data.sql` 插入示例数据（可选）

## 注意事项

1. 所有表都使用 `fxy_financial_` 前缀（除了已废弃的 `ai_config`）
2. 使用 UUID 作为组ID和关联ID以确保唯一性
3. JSON字段用于存储复杂的配置和规则信息
4. 外键约束确保数据完整性
5. 索引优化查询性能

## 版本信息

- 创建日期: 2025-06-17
- 数据库版本: MySQL 8.0+
- 字符集: utf8mb4
