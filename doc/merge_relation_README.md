# 会计系统归并关联功能

## 功能概述

本功能实现了会计系统中票据和银证的归并关联管理，支持灵活的归并规则配置和多层次的关联关系管理。

## 核心特性

### 1. 归并功能
- **票据归并**：按照可配置规则将相关票据归并为组
- **银证归并**：按照可配置规则将相关银证归并为组
- **手动归并**：支持用户手动选择记录进行归并
- **增量归并**：新记录自动匹配现有归并组

### 2. 关联功能
- **多层次关联**：支持单条记录间、单条与组间、组与组间的关联
- **跨类型关联**：支持票据与银证之间的关联
- **关联类型**：支持多种关联类型（关联、匹配、部分关联、完全关联）
- **关联验证**：自动验证关联关系的有效性

### 3. 查询功能
- **统一查询**：提供统一的查询接口，支持跨实体类型查询
- **关联查询**：支持查询实体的所有关联关系
- **跨类型查询**：专门的跨类型关联查询功能

## 数据库设计

### 主要表结构

1. **归并规则表** (`fxy_financial_merge_rules`)
   - 存储可配置的归并规则
   - 支持JSON格式的规则逻辑配置
   - 可指定适用于票据、银证或两者

2. **票据归并组表** (`fxy_financial_document_groups`)
   - 管理票据的归并组信息
   - 包含组统计信息（总金额、项目数量等）

3. **银证归并组表** (`fxy_financial_receipt_groups`)
   - 管理银证的归并组信息
   - 包含组统计信息（总金额、项目数量等）

4. **统一关联表** (`fxy_financial_entity_relations`)
   - 统一管理所有类型的关联关系
   - 支持六种关联类型的任意组合

5. **归并任务表** (`fxy_financial_merge_tasks`)
   - 管理异步归并任务
   - 提供任务进度跟踪

### 字段扩展

- 为 `fxy_financial_bill` 表添加 `doc_group_id` 字段
- 为 `fxy_financial_bank_receipts` 表添加 `receipt_group_id` 字段

## 技术架构

### 后端架构

```
Controller层
├── MergeEngineController      # 归并引擎控制器
├── RelationManagerController  # 关联管理控制器
└── UnifiedQueryController     # 统一查询控制器

Service层
├── MergeEngineService        # 归并引擎服务
├── RelationManagerService    # 关联管理服务
└── UnifiedQueryService       # 统一查询服务

Repository层
├── MergeRuleMapper          # 归并规则数据访问
├── DocumentGroupMapper      # 票据组数据访问
├── ReceiptGroupMapper       # 银证组数据访问
├── EntityRelationMapper     # 关联关系数据访问
└── MergeTaskMapper         # 归并任务数据访问

Entity层
├── MergeRule               # 归并规则实体
├── DocumentGroup           # 票据归并组实体
├── ReceiptGroup           # 银证归并组实体
├── EntityRelation         # 关联关系实体
└── MergeTask             # 归并任务实体
```

### 前端架构

```
Views
└── merge/
    └── MergeManagement.vue   # 归并关联管理页面

API
└── merge-relation.js         # 归并关联相关API

Components
├── MergePreview.vue         # 归并预览组件
├── RelationManager.vue      # 关联管理组件
└── UnifiedQuery.vue         # 统一查询组件
```

## API接口

### 归并引擎接口

- `GET /merge-engine/documents/preview` - 预览票据归并结果
- `POST /merge-engine/documents/execute` - 执行票据归并
- `GET /merge-engine/receipts/preview` - 预览银证归并结果
- `POST /merge-engine/receipts/execute` - 执行银证归并
- `POST /merge-engine/documents/manual-merge` - 手动票据归并
- `POST /merge-engine/receipts/manual-merge` - 手动银证归并
- `DELETE /merge-engine/documents/groups/{groupId}` - 解散票据归并组
- `DELETE /merge-engine/receipts/groups/{groupId}` - 解散银证归并组

### 关联管理接口

- `POST /relations` - 创建关联关系
- `POST /relations/batch` - 批量创建关联关系
- `GET /relations/documents/{entityId}` - 查询票据相关关联
- `GET /relations/receipts/{entityId}` - 查询银证相关关联
- `GET /relations/cross-type/{entityId}` - 查询跨类型关联
- `DELETE /relations/{relationId}` - 删除关联关系

### 统一查询接口

- `POST /unified-query/documents` - 票据统一查询
- `POST /unified-query/receipts` - 银证统一查询
- `POST /unified-query/cross-type-relations` - 跨类型关联查询

## 使用指南

### 1. 数据库初始化

```sql
-- 执行数据库架构创建脚本
source doc/merge_relation_schema.sql;
```

### 2. 配置归并规则

系统提供了三种默认归并规则：
- 同人同日同类归并
- 金额范围归并
- 时间窗口归并

可以通过管理界面添加自定义规则。

### 3. 执行归并操作

1. 选择归并规则
2. 预览归并结果
3. 确认后执行归并
4. 查看归并组信息

### 4. 管理关联关系

1. 创建关联关系
2. 查看关联关系
3. 删除不需要的关联
4. 验证关联有效性

### 5. 统一查询

使用统一查询接口可以：
- 跨实体类型查询
- 包含关联信息的查询
- 按多种条件筛选

## 测试

### 运行测试脚本

```sql
-- 执行测试脚本
source doc/test_merge_relation.sql;
```

测试脚本包含：
- 创建测试数据
- 测试归并功能
- 测试关联功能
- 验证查询结果
- 性能测试

### 前端测试

1. 启动前端应用
2. 访问归并关联管理页面
3. 测试各项功能

## 性能优化

### 数据库优化

1. **索引策略**
   - 归并关键字段的复合索引
   - 关联表的source和target字段索引
   - 账套ID相关的索引

2. **查询优化**
   - 使用UNION ALL进行统一查询
   - 合理使用LEFT JOIN
   - 避免N+1查询问题

### 应用优化

1. **缓存策略**
   - 归并组结果缓存
   - 查询结果缓存
   - 规则配置缓存

2. **异步处理**
   - 大批量归并异步执行
   - 任务进度实时更新
   - 失败重试机制

## 扩展性

### 规则扩展

可以通过JSON配置添加新的归并规则：

```json
{
  "type": "CUSTOM_RULE",
  "fields": ["field1", "field2"],
  "conditions": {
    "tolerance": 0.1,
    "weight": 0.8
  }
}
```

### 关联类型扩展

可以在EntityRelation.RelationType枚举中添加新的关联类型。

### 查询扩展

可以在UnifiedQueryService中添加新的查询维度和筛选条件。

## 注意事项

1. **数据一致性**：归并和关联操作都使用事务保证数据一致性
2. **权限控制**：所有操作都基于账套ID进行权限隔离
3. **性能考虑**：大批量操作建议使用异步模式
4. **备份恢复**：重要操作前建议备份相关数据

## 故障排除

### 常见问题

1. **归并失败**：检查归并规则配置和数据完整性
2. **关联创建失败**：验证实体是否存在和关联规则
3. **查询性能慢**：检查索引使用情况和查询条件

### 日志查看

相关日志位于：
- 归并操作：`MergeEngineServiceImpl`
- 关联操作：`RelationManagerServiceImpl`
- 查询操作：`UnifiedQueryServiceImpl`
