-- 为票据表添加发票号码字段
-- 执行日期: 2025-07-07
-- 说明: 添加发票号码字段，支持OCR识别，可为空但不能重复

USE financial;

-- 添加发票号码字段
ALTER TABLE fxy_financial_bill 
ADD COLUMN `invoice_number` varchar(50) DEFAULT NULL COMMENT '发票号码' 
AFTER `bill_month`;

-- 添加唯一索引，确保同一账套内发票号码不重复
ALTER TABLE fxy_financial_bill 
ADD UNIQUE KEY `uk_invoice_number_account_sets` (`invoice_number`, `account_sets_id`);

-- 验证修改
DESCRIBE fxy_financial_bill;
SHOW INDEX FROM fxy_financial_bill;
