-- =====================================================
-- 批量导入图片拆分功能增强
-- 创建时间: 2025-06-29
-- 说明: 为批量导入功能添加图片拆分支持
-- =====================================================

-- 为批量导入明细表添加拆分相关字段
ALTER TABLE `fxy_financial_batch_import_detail` 
ADD COLUMN `sub_image_index` int DEFAULT NULL COMMENT '子图片索引（拆分后的图片序号）' AFTER `image_height`,
ADD COLUMN `original_image_url` varchar(500) DEFAULT NULL COMMENT '原始图片URL（拆分前的图片）' AFTER `sub_image_index`,
ADD COLUMN `is_split` tinyint(1) DEFAULT 0 COMMENT '是否为拆分图片' AFTER `original_image_url`,
ADD COLUMN `account_sets_id` int NOT NULL COMMENT '账套ID' AFTER `is_split`;

-- 添加索引
ALTER TABLE `fxy_financial_batch_import_detail` 
ADD INDEX `idx_task_split` (`task_id`, `is_split`),
ADD INDEX `idx_original_image` (`original_image_url`),
ADD INDEX `idx_account_sets` (`account_sets_id`);

-- 为批量导入任务表添加账套ID字段（如果还没有的话）
ALTER TABLE `fxy_financial_batch_import_task` 
ADD COLUMN `account_sets_id` int NOT NULL COMMENT '账套ID' AFTER `task_name`;

-- 添加任务表账套索引
ALTER TABLE `fxy_financial_batch_import_task` 
ADD INDEX `idx_account_sets` (`account_sets_id`);

-- 更新现有数据的账套ID（需要根据实际情况调整）
-- UPDATE `fxy_financial_batch_import_task` SET `account_sets_id` = 1 WHERE `account_sets_id` IS NULL OR `account_sets_id` = 0;
-- UPDATE `fxy_financial_batch_import_detail` SET `account_sets_id` = 1 WHERE `account_sets_id` IS NULL OR `account_sets_id` = 0;

-- 创建图片拆分日志表（可选，用于记录拆分操作）
CREATE TABLE IF NOT EXISTS `fxy_financial_image_split_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `original_detail_id` int NOT NULL COMMENT '原始明细ID',
  `original_image_url` varchar(500) NOT NULL COMMENT '原始图片URL',
  `split_count` int NOT NULL COMMENT '拆分数量',
  `split_method` varchar(50) DEFAULT 'AUTO' COMMENT '拆分方式：AUTO-自动，MANUAL-手动',
  `split_areas` text COMMENT '拆分区域信息（JSON格式）',
  `created_by` int DEFAULT NULL COMMENT '操作用户ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_original_detail` (`original_detail_id`),
  KEY `idx_account_sets` (`account_sets_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片拆分日志表';

-- 创建拆分配置表（可选，用于存储拆分算法配置）
CREATE TABLE IF NOT EXISTS `fxy_financial_split_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `import_type` enum('BANK_RECEIPT','INVOICE') NOT NULL COMMENT '导入类型',
  `split_method` varchar(50) NOT NULL COMMENT '拆分方法',
  `config_params` text COMMENT '配置参数（JSON格式）',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否为默认配置',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type_method` (`import_type`, `split_method`),
  KEY `idx_account_sets` (`account_sets_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拆分配置表';

-- 插入默认拆分配置
INSERT INTO `fxy_financial_split_config` (`config_name`, `import_type`, `split_method`, `config_params`, `is_default`, `account_sets_id`) VALUES
('银行回单固定网格拆分', 'BANK_RECEIPT', 'FIXED_GRID', '{"rows": 2, "cols": 2, "minWidth": 200, "minHeight": 150}', 1, 1),
('银行回单空白区域检测', 'BANK_RECEIPT', 'WHITESPACE_DETECTION', '{"threshold": 0.8, "minGap": 20}', 0, 1),
('发票固定网格拆分', 'INVOICE', 'FIXED_GRID', '{"rows": 1, "cols": 2, "minWidth": 300, "minHeight": 200}', 1, 1);

-- 为批量导入任务表添加拆分统计字段
ALTER TABLE `fxy_financial_batch_import_task` 
ADD COLUMN `split_count` int DEFAULT 0 COMMENT '拆分图片数量' AFTER `success_count`,
ADD COLUMN `auto_split_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用自动拆分' AFTER `split_count`;

-- 创建视图：拆分统计
CREATE OR REPLACE VIEW `v_batch_import_split_stats` AS
SELECT 
    t.task_id,
    t.task_name,
    t.import_type,
    t.total_images,
    COUNT(d.id) as detail_count,
    SUM(CASE WHEN d.is_split = 1 THEN 1 ELSE 0 END) as split_image_count,
    COUNT(DISTINCT d.original_image_url) as original_image_count,
    t.account_sets_id
FROM `fxy_financial_batch_import_task` t
LEFT JOIN `fxy_financial_batch_import_detail` d ON t.task_id = d.task_id
GROUP BY t.task_id, t.task_name, t.import_type, t.total_images, t.account_sets_id;

-- 添加注释说明
ALTER TABLE `fxy_financial_batch_import_detail` COMMENT = '批量导入明细表（支持图片拆分）';
ALTER TABLE `fxy_financial_batch_import_task` COMMENT = '批量导入任务表（支持拆分统计）';

-- 完成脚本
SELECT '批量导入图片拆分功能增强完成' as message;
