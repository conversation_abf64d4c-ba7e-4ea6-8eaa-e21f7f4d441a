# API路由配置方案对比

## 问题分析

当前系统有两个前端应用：
- **主应用**：财务系统主界面
- **管理员页面**：系统管理后台

两个应用都需要调用后端API，但路径可能冲突。

## 方案对比

### 方案一：路径前缀区分（当前方案）

#### 配置方式
```nginx
# 管理员API - 必须在前面
location ^~ /api/admin/ {
    rewrite ^/api/admin/(.*)$ /admin/$1 break;
    proxy_pass http://127.0.0.1:9080;
}

# 主应用API - 必须在后面
location ^~ /api/ {
    rewrite ^/api/(.*)$ /$1 break;
    proxy_pass http://127.0.0.1:9080;
}
```

#### 路径映射
| 前端请求 | 后端接收 | Controller |
|---------|---------|-----------|
| `/api/admin/users/list` | `/admin/users/list` | `@GetMapping("/admin/users/list")` |
| `/api/bills/list` | `/bills/list` | `@GetMapping("/bills/list")` |

#### 优点
- 前端路径清晰，容易区分
- 后端可以使用不同的Controller包

#### 缺点
- **顺序依赖**：nginx配置顺序不能颠倒
- **维护复杂**：两套不同的重写规则
- **容易出错**：配置顺序错误会导致路由失败

---

### 方案二：统一API前缀（推荐方案）

#### 配置方式
```nginx
# 统一API代理配置
location ^~ /api/ {
    # 直接转发，不做路径重写
    proxy_pass http://127.0.0.1:9080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
}
```

#### 路径映射
| 前端请求 | 后端接收 | Controller |
|---------|---------|-----------|
| `/api/admin/users/list` | `/api/admin/users/list` | `@GetMapping("/api/admin/users/list")` |
| `/api/bills/list` | `/api/bills/list` | `@GetMapping("/api/bills/list")` |

#### 后端Controller调整
```java
// 管理员Controller
@RestController
@RequestMapping("/api/admin")
public class AdminUserController {
    @GetMapping("/users/list")
    public Result getUserList() { ... }
}

// 主应用Controller
@RestController
@RequestMapping("/api")
public class BillController {
    @GetMapping("/bills/list")
    public Result getBillList() { ... }
}
```

#### 优点
- **配置简单**：只需要一个location配置
- **无顺序依赖**：不存在匹配顺序问题
- **易于维护**：统一的代理规则
- **扩展性好**：添加新API不需要修改nginx配置

#### 缺点
- 需要调整后端Controller的RequestMapping

---

### 方案三：子域名区分

#### 配置方式
```nginx
# 主应用
server {
    server_name fin.aiform.com;
    location /api/ {
        proxy_pass http://127.0.0.1:9080;
    }
}

# 管理员应用
server {
    server_name admin.fin.aiform.com;
    location /api/ {
        proxy_pass http://127.0.0.1:9080;
    }
}
```

#### 访问方式
- 主应用：`https://fin.aiform.com/`
- 管理员：`https://admin.fin.aiform.com/`

#### 优点
- **完全隔离**：两个应用完全独立
- **配置清晰**：每个应用有独立的server块
- **安全性好**：可以为管理员域名单独配置安全策略

#### 缺点
- 需要额外的子域名
- SSL证书需要支持子域名

---

### 方案四：端口区分

#### 配置方式
```nginx
# 主应用 - 80/443端口
server {
    listen 80;
    listen 443 ssl;
    server_name fin.aiform.com;
    location /api/ {
        proxy_pass http://127.0.0.1:9080;
    }
}

# 管理员应用 - 8443端口
server {
    listen 8443 ssl;
    server_name fin.aiform.com;
    location /api/ {
        proxy_pass http://127.0.0.1:9080;
    }
}
```

#### 访问方式
- 主应用：`https://fin.aiform.com/`
- 管理员：`https://fin.aiform.com:8443/`

#### 优点
- 完全隔离
- 不需要额外域名

#### 缺点
- 端口号不够友好
- 防火墙可能需要开放额外端口

## 推荐方案

### 最佳选择：方案二（统一API前缀）

**理由：**
1. **简单可靠**：配置最简单，不依赖顺序
2. **易于维护**：只需要维护一套nginx规则
3. **扩展性好**：添加新功能不需要修改nginx
4. **性能最佳**：减少了路径重写的开销

### 实施步骤

#### 1. 修改后端Controller

```java
// 原来的管理员Controller
@RestController
@RequestMapping("/admin")  // 改为 @RequestMapping("/api/admin")
public class AdminUserController { ... }

// 原来的主应用Controller
@RestController
@RequestMapping("")  // 改为 @RequestMapping("/api")
public class BillController { ... }
```

#### 2. 更新nginx配置

使用统一的API代理配置（已更新）

#### 3. 验证测试

```bash
# 测试管理员API
curl https://fin.aiform.com/api/admin/users/list

# 测试主应用API  
curl https://fin.aiform.com/api/bills/list
```

## 迁移指南

### 从方案一迁移到方案二

1. **备份当前配置**
2. **修改后端Controller的@RequestMapping**
3. **更新nginx配置**
4. **重启服务并测试**
5. **如有问题可快速回滚**

### 回滚方案

如果新方案有问题，可以快速回滚：
1. 恢复原nginx配置
2. 恢复原Controller注解
3. 重启服务

## 总结

推荐使用**方案二（统一API前缀）**，因为它：
- ✅ 配置最简单
- ✅ 维护成本最低
- ✅ 不依赖配置顺序
- ✅ 扩展性最好
- ✅ 性能最优

这种方案消除了nginx配置的顺序依赖问题，使系统更加稳定可靠。
