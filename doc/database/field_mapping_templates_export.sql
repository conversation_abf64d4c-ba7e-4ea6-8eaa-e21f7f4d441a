-- =====================================================
-- 字段映射模板数据导出
-- 导出时间: 2025-07-16
-- 用途: 生产环境模板数据修复
-- =====================================================

-- 清空现有模板数据
-- DELETE FROM fxy_financial_field_mapping_template;

-- 重置自增ID
ALTER TABLE fxy_financial_field_mapping_template AUTO_INCREMENT = 17;

-- 插入增值税普通发票标准模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count, 
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate, 
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    17, '增值税普通发票标准模板', NULL, 'INVOICE', '增值税普通发票', 15, 
    'vat_general_invoice_standard', 
    '{"invoice_number": ["发票号码", "号码"], "invoice_code": ["发票代码", "代码"], "bill_date": ["开票日期", "日期"], "issuer": ["销售方名称", "开票方"], "recipient": ["购买方名称", "收票方"], "amount": ["价税合计(小写)", "价税合计（小写）"], "amount_without_tax": ["合计金额", "不含税金额"], "total_tax_amount": ["合计税额", "税额"], "amount_in_words": ["价税合计(大写)", "价税合计（大写）"], "type": ["发票名称", "发票类型"], "remark": ["备注"], "seller_tax_id": ["销售方统一社会信用代码/纳税人识别号", "销售方纳税人识别号"], "buyer_tax_id": ["购买方统一社会信用代码/纳税人识别号", "购买方纳税人识别号"]}',
    '{}', 45, 100.00, '2025-07-13 20:44:51', NULL, 0, '2025-07-13 11:56:49', '2025-07-13 20:44:51', 1
);

-- 插入增值税专用发票标准模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count, 
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate, 
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    18, '增值税专用发票标准模板', NULL, 'INVOICE', '增值税专用发票', 20, 
    'vat_special_invoice_standard', 
    '{"invoice_number": ["发票号码", "号码", "打印发票号码"], "invoice_code": ["发票代码", "代码", "打印发票代码"], "bill_date": ["开票日期", "日期"], "issuer": ["销售方名称", "开票方", "销售方"], "recipient": ["购买方名称", "收票方", "购买方"], "amount": ["价税合计(小写)", "价税合计（小写）", "小写金额"], "amount_without_tax": ["合计金额", "不含税金额", "金额"], "total_tax_amount": ["合计税额", "税额"], "amount_in_words": ["价税合计(大写)", "价税合计（大写）"], "type": ["发票名称", "发票类型"], "summary": ["项目名称", "货物或应税劳务、服务名称"], "tax_rate": ["税率"], "remark": ["备注"], "seller_tax_id": ["销售方纳税人识别号", "销售方识别号", "销售方统一社会信用代码/纳税人识别号"], "buyer_tax_id": ["购买方纳税人识别号", "购买方识别号", "购买方统一社会信用代码/纳税人识别号"]}',
    '{}', 9, 100.00, '2025-07-13 20:15:51', NULL, 0, '2025-07-13 11:56:50', '2025-07-13 20:15:51', 1
);

-- 插入招商银行企业银行收费模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count, 
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate, 
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    19, '招商银行企业银行收费模板', '招商银行', 'BANK_RECEIPT', '企业银行收费', 21, 
    'cmb_enterprise_fee_v2', 
    '{"amount": ["金额", "交易金额(小写)"], "amountInWords": ["交易金额(大写)"], "transferDate": ["交易日期"], "receiptsDate": ["日期"], "serialNumber": ["交易流水", "业务编号"], "payerName": ["付款人"], "payerAccount": ["付款账号"], "payerBank": ["付款开户行"], "payeeName": "招商银行", "summary": ["收费项目", "交易摘要"], "receiptTitle": ["标题"], "transactionInstitution": ["机构"], "type": "支出", "quantity": 1}', 
    '{"amount": 28.50, "amountInWords": "人民币贰拾捌元伍角", "transferDate": "2025-01-04", "serialNumber": "C02471R0004Z69Z", "payerName": "北京征鸟科技有限公司", "payerAccount": "***************", "payerBank": "北京东四环支行", "payeeName": "招商银行", "summary": "网银支付-行内-异地手续费", "receiptTitle": "出账回单", "transactionInstitution": "招商银行", "type": "支出"}', 
    0, 100.00, NULL, 68, 1, '2025-07-13 21:33:26', '2025-07-13 21:33:26', 1
);

-- 插入招商银行支付转账模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count, 
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate, 
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    20, '招商银行支付转账模板', '招商银行', 'BANK_RECEIPT', '支付', 20, 
    'cmb_payment_v2', 
    '{"amount": ["交易金额(小写)"], "amountInWords": ["交易金额(大写)"], "transferDate": ["交易日期"], "receiptsDate": ["日期"], "serialNumber": ["交易流水", "业务编号"], "payerName": ["付款人"], "payerAccount": ["付款账号"], "payerBank": ["付款开户行"], "payeeName": ["收款人"], "payeeAccount": ["收款账号"], "payeeBank": ["收款开户行"], "summary": ["交易摘要"], "receiptTitle": ["标题"], "transactionInstitution": ["机构"], "type": "支出", "quantity": 1}', 
    '{"amount": 130000.0, "amountInWords": "人民币壹拾叁万元整", "transferDate": "2025-01-10", "serialNumber": "C02471X00170KCZ", "payerName": "北京征鸟科技有限公司", "payerAccount": "***************", "payerBank": "招商银行北京分行北京东四环支行", "payeeName": "苏州征鸟信息技术有限公司", "payeeAccount": "***************", "payeeBank": "招商银行", "summary": "付款", "receiptTitle": "出账回单", "transactionInstitution": "招商银行", "type": "支出"}', 
    0, 100.00, NULL, 68, 1, '2025-07-13 21:33:26', '2025-07-13 21:33:26', 1
);

-- 插入招商银行TIPS缴税模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count, 
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate, 
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    21, '招商银行TIPS缴税模板', '招商银行', 'BANK_RECEIPT', 'TIPS缴税出账', 26,
    'cmb_tips_tax_v2',
    '{"amount": ["实缴金额", "交易金额(小写)"], "amountInWords": ["交易金额(大写)"], "transferDate": ["交易日期"], "receiptsDate": ["打印日期"], "serialNumber": ["缴款书交易流水", "交易流水"], "payerName": ["纳税人全称", "付款人"], "payerAccount": ["付款账号"], "payerBank": ["付款开户行"], "payeeName": ["收款国库(银行)名称"], "summary": ["税(费)种名称", "交易摘要"], "receiptTitle": ["标题"], "transactionInstitution": ["机构"], "remark": ["税票号码", "纳税人识别号"], "type": "支出", "quantity": 1}',
    '{"amount": 28878.61, "amountInWords": "人民币贰万捌仟捌佰柒拾捌元陆角壹分", "transferDate": "2025-01-10", "serialNumber": "C02471X001AZDEZ", "payerName": "北京征鸟科技有限公司", "payerAccount": "***************", "payerBank": "北京东四环支行", "payeeName": "国家金库北京市昌平区支库", "summary": "个人所得税", "receiptTitle": "出账回单", "transactionInstitution": "招商银行", "remark": "625011011273480025", "type": "支出"}',
    0, 100.00, NULL, 68, 1, '2025-07-13 21:33:26', '2025-07-13 21:33:26', 1
);

-- 插入招商-企业银行收费模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count,
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate,
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    22, '招商-企业银行收费模板', '招商', 'BANK_RECEIPT', '企业银行收费', 34,
    'c0614ffd9144f9905079fa518c420577',
    '{"rawOcrData.收费项目":"summary","payerAccount":"payer_account","rawOcrData.机构":"transaction_institution","rawOcrData.笔数":"quantity","amountInWords":"amount_in_words","amount":"amount","serialNumber":"serial_number","rawOcrData.付款人":"payer_name","title":"receipt_title","rawOcrData.付款开户行":"payer_bank"}',
    '{}', 3, 100.00, '2025-07-15 23:31:21', 68, 1, '2025-07-15 23:31:25', '2025-07-15 23:31:25', 1
);

-- 插入招商-支付模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count,
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate,
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    23, '招商-支付模板', '招商', 'BANK_RECEIPT', '支付', 35,
    '2abb4a7c795d281c80ad9254e3b68384',
    '{"summary":"summary","payerAccount":"payer_account","payeeName":"payee_name","amountInWords":"amount_in_words","rawOcrData.收款开户行":"transaction_institution","rawOcrData.收款账号":"payee_account","serialNumber":"serial_number","rawOcrData.付款人":"payer_name","title":"receipt_title","rawOcrData.付款开户行":"payer_bank"}',
    '{}', 27, 100.00, '2025-07-15 23:33:17', 68, 1, '2025-07-15 23:31:43', '2025-07-15 23:33:20', 1
);

-- 插入招商-TIPS缴税出账模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count,
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate,
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    24, '招商-TIPS缴税出账模板', '招商', 'BANK_RECEIPT', 'TIPS缴税出账', 39,
    '42dcaae3fb7516d8348a46facba49f25',
    '{"payerAccount":"payer_account","rawOcrData.机构":"transaction_institution","amountInWords":"amount_in_words","rawOcrData.付款人":"payer_name","rawOcrData.收款国库(银行)名称":"payee_name","title":"receipt_title","rawOcrData.付款开户行":"payer_bank","rawOcrData.相关编号":"serial_number"}',
    '{}', 3, 100.00, '2025-07-15 23:32:01', 68, 1, '2025-07-15 23:32:03', '2025-07-15 23:32:05', 1
);

-- 插入招商-汇入汇款模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count,
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate,
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    25, '招商-汇入汇款模板', '招商', 'BANK_RECEIPT', '汇入汇款', 34,
    '924b9ee6fbe20377fe1e7f17c9151491',
    '{"rawOcrData.业务类型":"summary","payerAccount":"payer_account","rawOcrData.机构":"transaction_institution","payeeName":"payee_name","amountInWords":"amount_in_words","rawOcrData.收款开户行":"payee_bank","rawOcrData.收款账号":"payee_account","serialNumber":"serial_number","rawOcrData.付款人":"payer_name","title":"receipt_title","rawOcrData.付款开户行":"payer_bank"}',
    '{}', 2, 100.00, '2025-07-15 23:33:17', 68, 1, '2025-07-15 23:32:23', '2025-07-15 23:33:20', 1
);

-- 插入招商-自助代发付款模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count,
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate,
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    26, '招商-自助代发付款模板', '招商', 'BANK_RECEIPT', '自助代发付款', 31,
    'b716f2a143a46147bd839359b6889d07',
    '{"summary":"summary","payerAccount":"payer_account","rawOcrData.机构":"transaction_institution","amountInWords":"amount_in_words","serialNumber":"serial_number","rawOcrData.付款人":"payer_name","title":"receipt_title","rawOcrData.付款开户行":"payer_bank"}',
    '{}', 2, 100.00, '2025-07-15 23:33:17', 68, 1, '2025-07-15 23:32:40', '2025-07-15 23:33:21', 1
);

-- 插入招商-代发付费模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count,
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate,
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    27, '招商-代发付费模板', '招商', 'BANK_RECEIPT', '代发付费', 35,
    '6da641bc36b3a7bd7e3455b62577d72e',
    '{"summary":"summary","payerAccount":"payer_account","rawOcrData.机构":"transaction_institution","rawOcrData.笔数":"quantity","amountInWords":"amount_in_words","amount":"amount","serialNumber":"serial_number","rawOcrData.付款人":"payer_name","title":"receipt_title","rawOcrData.付款开户行":"payer_bank"}',
    '{}', 1, 100.00, '2025-07-15 23:32:55', 68, 1, '2025-07-15 23:32:59', '2025-07-15 23:32:59', 1
);

-- 插入招商-提回定借-付款模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count,
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate,
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    28, '招商-提回定借-付款模板', '招商', 'BANK_RECEIPT', '提回定借-付款', 38,
    '71cb635028dcfc60d0312f7fbb79ecd8',
    '{"summary":"summary","payerAccount":"payer_account","rawOcrData.机构":"transaction_institution","payeeName":"payee_name","amountInWords":"amount_in_words","rawOcrData.收款开户行":"payee_bank","rawOcrData.收款账号":"payee_account","serialNumber":"serial_number","rawOcrData.付款人":"payer_name","title":"receipt_title","rawOcrData.付款开户行":"payer_bank"}',
    '{}', 1, 100.00, '2025-07-15 23:33:14', 68, 1, '2025-07-15 23:33:17', '2025-07-15 23:33:17', 1
);

-- 插入银行收费通用模板
INSERT INTO fxy_financial_field_mapping_template (
    id, template_name, bank_identifier, document_type, receipt_type, field_count,
    field_signature, mapping_rules, sample_ocr_data, usage_count, success_rate,
    last_used_time, account_sets_id, create_user, created_time, updated_time, is_active
) VALUES (
    29, '银行收费通用模板', NULL, 'BANK_RECEIPT', '银行收费', 16,
    'bank_fee_universal_v1',
    '{"amount": ["金额", "交易金额(小写)", "交易金额", "收费金额"], "amountInWords": ["交易金额(大写)", "金额大写"], "transferDate": ["交易日期", "记账日期", "业务日期"], "receiptsDate": ["日期", "打印日期"], "serialNumber": ["交易流水", "业务编号", "流水号", "回单编号"], "payerName": ["付款人", "付款人名称", "客户名称"], "payerAccount": ["付款账号", "付款人账号", "客户账号"], "payerBank": ["付款开户行", "付款人开户行"], "payeeName": "{{机构}}", "summary": ["收费项目", "交易摘要", "业务类型", "摘要"], "receiptTitle": ["标题", "回单类型"], "transactionInstitution": ["机构", "开户行名称", "银行名称"], "type": "支出", "quantity": 1, "remark": "银行收费业务"}',
    '{"付款人": "北京征鸟科技有限公司", "交易日期": "2025年01月04日", "机构": "招商银行", "付款账号": "***************", "业务编号": "C02471R0004269Z", "交易流水": "C02471R0004Z69Z", "业务类型": "企业银行收费", "交易摘要": "收费", "金额": "28.50", "交易金额(大写)": "人民币贰拾捌元伍角", "标题": "出账回单", "收费项目": "网银支付-行内-异地手续费"}',
    0, 100.00, NULL, 1, 1, '2025-07-16 00:23:54', '2025-07-16 00:40:26', 1
);

-- 提交事务
COMMIT;

-- 验证导入结果
SELECT
    COUNT(*) as total_templates,
    COUNT(CASE WHEN document_type = 'INVOICE' THEN 1 END) as invoice_templates,
    COUNT(CASE WHEN document_type = 'BANK_RECEIPT' THEN 1 END) as bank_receipt_templates
FROM fxy_financial_field_mapping_template
WHERE is_active = 1;
