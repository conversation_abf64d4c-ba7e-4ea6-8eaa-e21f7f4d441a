# 数据库脚本目录

本目录包含所有数据库相关的SQL脚本，包括表结构创建、字段修改、数据迁移等。

## 脚本命名规范

- `migration_*.sql` - 数据迁移脚本
- `create_*.sql` - 表创建脚本  
- `alter_*.sql` - 表结构修改脚本
- `fix_*.sql` - 问题修复脚本
- `update_*.sql` - 数据更新脚本

## 脚本执行记录

### 2025-01-07

#### `migration_add_user_id_to_ai_config.sql`
- **添加时间**: 2025-01-07 (之前已存在)
- **作用**: 为AI配置表添加用户ID字段
- **状态**: 已存在

#### `aiform_update.sql` 扩展表更新
- **添加时间**: 2025-01-07
- **作用**: 添加实际使用的扩展表到主更新脚本
- **影响范围**:
  - 新增 `fxy_financial_bill_ai_result` 表（票据AI处理结果）
  - 新增 `fxy_financial_image_split_log` 表（图片拆分日志）
- **状态**: ✅ 已完成

#### `optional_tables_backup.sql`
- **添加时间**: 2025-01-07
- **作用**: 备份当前未完全实现的可选表
- **影响范围**:
  - 备份 `fxy_financial_split_config` 表（拆分配置，代码实现不完整）
  - 备份 `fxy_financial_subject_matching_template` 表（科目匹配模板，功能可能重复）
- **状态**: ✅ 已完成

#### `alter_add_ocr_info_fields_20250107.sql`
- **添加时间**: 2025-01-07
- **作用**: 为银行回单表和票据表添加专门的OCR识别信息字段
- **目的**: 解决OCR识别信息存储问题，保持备注字段原有用途
- **背景**: 修复批量处理OCR识别结果保存问题，避免覆盖用户备注字段
- **影响表**:
  - `fxy_financial_bank_receipts` - 备注字段改为TEXT类型，添加 `ocr_recognition_info` TEXT 字段
  - `fxy_financial_bill` - 添加 `ocr_recognition_info` TEXT 字段
- **相关代码修改**:
  - `BankReceipts.java` - 添加 `ocrRecognitionInfo` 字段
  - `Bill.java` - 添加 `ocrRecognitionInfo` 字段
  - `BankReceiptsMapper.xml` - 更新字段列表
  - `BatchImportController.java` - 修改保存逻辑
  - `BatchPreviewPage.vue` - 前端表单分离显示
- **状态**: 已整合到 aiform_update.sql

#### `aiform_update.sql`
- **添加时间**: 2025-01-07 (重新生成)
- **作用**: AI财务系统完整部署脚本，基于financial.sql创建所有AI财务系统表
- **执行顺序**: financial.sql -> aiform_update.sql (不再需要aiform.sql)
- **包含内容**:
  - 核心业务表：银行回单表、票据表
  - AI配置和增强功能表
  - 批量导入功能完整表结构
  - 归并和关联功能表
  - OCR识别信息字段
  - 发票号码字段
  - 索引优化
  - 默认数据初始化
- **对比代码实体类生成**: 基于kernel模块中的实体类，排除financial.sql中已有的表
- **新环境部署**: 只需要financial.sql + aiform_update.sql即可完整部署
- **状态**: 待执行

### 2025-07-08

#### OCR识别信息格式化增强
- **添加时间**: 2025-07-08
- **作用**: 实现OCR原始信息的JSON格式化保存功能
- **影响范围**: 票据和银行回单的OCR信息保存
- **修改内容**:
  - **后端**: `OcrService.java` - 新增`formatOcrDataToJson`方法，统一OCR信息格式化
  - **后端**: `BatchImportController.java` - 注入OcrService，使用新的格式化功能
  - **前端**: `BankReceiptsForm.vue` - 修改OCR识别结果处理，保存格式化信息
  - **前端**: `BillForm.vue` - 修改OCR识别结果处理，保存格式化信息
- **功能特点**:
  - 将OCR字段按业务逻辑分类（基本信息、金额信息、账户信息、日期信息、其他信息）
  - 生成结构化的JSON格式数据，便于查询和展示
  - 确保批量导入和单条记录保存使用统一的OCR信息格式
  - 保存所有OCR识别的原始字段，记录识别时间和数据源
- **状态**: ✅ 已完成

#### MySQL索引语法修复
- **添加时间**: 2025-07-08
- **文件**: `aiform_update.sql`
- **作用**: 修复MySQL索引创建语法错误
- **问题**: MySQL不支持`CREATE INDEX IF NOT EXISTS`语法
- **解决方案**: 改为`DROP INDEX IF EXISTS` + `CREATE INDEX`模式
- **状态**: ✅ 已修复

#### 账套当前期间功能优化
- **添加时间**: 2025-07-08
- **作用**: 修改票据和银行回单新增时的年月期号逻辑，使用账套当前期间而不是当前时间
- **影响范围**: 单条记录新增/修改、批量导入功能
- **修改内容**:
  - **BankReceiptsServiceImpl.java**: 修改save/update方法，新增getCurrentAccountPeriod工具方法
  - **BillServiceImpl.java**: 修改save/update方法，新增getCurrentAccountPeriod工具方法
  - 获取期间优先级：currentUser账套信息 > 数据库查询 > 当前系统日期兜底
- **业务逻辑**:
  - 新增的票据和银行回单年月字段使用账套的current_account_date对应的年月
  - 票据编号和银行回单编号基于账套当前期间生成
  - 保持日期字段（receipts_date/bill_date）的用户输入灵活性
- **兼容性**: 如果账套当前期间为空，自动使用当前日期作为兜底
- **状态**: ✅ 已完成

### 2025-07-10

#### 字段映射模板功能（性能优化）
- **添加时间**: 2025-07-10
- **作用**: 实现OCR字段到标准字段的映射模板缓存，减少LLM调用次数和Token消耗
- **影响范围**: 批量导入功能的性能优化
- **新增表结构**:
  - `fxy_financial_field_mapping_template` - 字段映射模板主表
  - `fxy_financial_field_mapping_rule` - 字段映射规则详情表
  - `fxy_financial_template_usage_log` - 模板使用统计表
- **功能特点**:
  - **智能匹配**: 基于银行标识、票据类型、字段特征签名进行多层次模板匹配
  - **自动生成**: LLM映射成功后自动创建可复用模板
  - **性能优化**: 相同银行、相同类型的回单直接使用模板，无需重复调用LLM
  - **统计分析**: 记录模板使用次数、成功率等统计信息
- **匹配策略**:
  1. 精确匹配：字段特征签名完全相同
  2. 银行类型匹配：银行标识+票据类型+回单类型
  3. 相似度匹配：字段数量相近的模板
- **修改内容**:
  - **后端**: 新增 `FieldMappingTemplateService.java` - 模板管理服务
  - **后端**: 新增 `FieldMappingTemplateController.java` - 模板管理API
  - **后端**: 修改 `AsyncBatchProcessService.java` - 集成模板功能
  - **实体**: 新增 `FieldMappingTemplate.java` - 模板实体类
  - **Mapper**: 新增 `FieldMappingTemplateMapper.java` - 模板数据访问层
- **预期效果**:
  - **首次处理**: 使用LLM映射 + 创建模板
  - **后续处理**: 直接使用模板，处理速度提升数十倍
  - **成本降低**: 大幅减少Token消耗
- **状态**: ✅ 已完成并整合到 aiform_update.sql

#### 重复数据检测功能
- **添加时间**: 2025-07-10
- **作用**: 基于关键字段检测并避免重复保存相同的票据和银行回单
- **检测策略**:
  - **银行回单**: 流水号精确匹配 + 金额+日期+付款账号组合匹配
  - **票据**: 发票号码精确匹配 + 金额+日期+开票方组合匹配
- **处理逻辑**: 检测到重复时跳过保存并记录日志，不影响其他数据处理
- **修改内容**:
  - **后端**: 修改 `AsyncBatchProcessService.java` - 添加重复检测逻辑
  - 新增 `isDuplicateBankReceipt()` 方法
  - 新增 `isDuplicateBill()` 方法
- **安全性**: 异常时不阻止保存，只记录日志
- **状态**: ✅ 已完成

#### 进度显示修复
- **添加时间**: 2025-07-10
- **作用**: 修复批量处理进度显示与实际状态不一致的问题
- **问题**: OCR识别完成后进度显示100%，但状态为"待预览"
- **解决方案**: 调整进度逻辑，待预览状态显示90%，只有完全完成才显示100%
- **修改内容**:
  - **后端**: 修改 `AsyncBatchProcessService.java` - 调整进度更新逻辑
- **状态**: ✅ 已完成

## 执行说明

### 完整部署流程
1. **基础数据库**: 执行 `financial.sql` (基础财务系统表，23张表)
2. **AI财务系统**: 执行 `aiform_update.sql` (AI财务系统完整功能，18张表)
3. **可选功能**: 如需要可执行 `optional_tables_backup.sql` (可选扩展表，2张表)

### 单独脚本执行
1. 所有脚本执行前请先备份数据库
2. 建议在测试环境先执行验证
3. 执行后请更新此文档的状态
4. 如有问题请及时回滚

### aiform_update.sql 特别说明
- 此脚本基于代码实体类生成，确保与代码完全一致
- 排除了financial.sql中已有的表，避免冲突
- 使用 `IF NOT EXISTS` 确保幂等性，可以安全地重复执行
- 包含完整的AI财务系统功能：批量导入、OCR识别、归并关联、字段映射模板等
- 新增了实际使用的扩展表：票据AI处理结果、图片拆分日志、字段映射模板
- 执行后需要重启后端服务以使实体类修改生效

### optional_tables_backup.sql 说明
- 备份了当前代码实现不完整或功能重复的表
- 包含详细的使用说明和恢复指导
- 如将来需要这些功能，可参考此文件补充实现

## 执行检查清单

### `alter_add_ocr_info_fields_20250107.sql` 执行清单

**执行前检查**:
- [ ] 数据库已备份
- [ ] 确认当前环境（开发/测试/生产）
- [ ] 检查表是否存在：`fxy_financial_bank_receipts`, `fxy_financial_bill`
- [ ] 检查是否有正在运行的批量处理任务

**执行步骤**:
1. [ ] 执行SQL脚本
2. [ ] 验证字段添加成功
3. [ ] 重启后端服务
4. [ ] 测试批量处理功能
5. [ ] 测试编辑表单显示
6. [ ] 验证OCR信息保存功能

**执行后验证**:
- [ ] 新字段 `ocr_recognition_info` 已添加
- [ ] 原有 `remark` 字段功能正常
- [ ] 批量处理保存功能正常
- [ ] 前端编辑表单显示正常
- [ ] 无数据丢失或异常

## 注意事项

- 执行脚本前请确认当前数据库环境
- 大表结构修改建议在业务低峰期执行
- 执行后请验证功能是否正常
- 记录执行时间和执行人员
- 如有问题请及时回滚并联系开发人员
