# SQL脚本分析总结

## 分析时间
2025-01-07

## 分析范围
doc目录下的所有SQL文件

## 脚本分类

### 1. 基础表结构脚本
- `financial.sql` - 基础财务系统表结构
- `aiform.sql` - AI功能基础表结构

### 2. 功能增强脚本
- `additional_tables.sql` - 额外功能表（AI配置、票据管理、关联关系等）
- `ai_enhancement.sql` - AI智能会计科目匹配功能增强
- `batch_import_tables_2025-06-27.sql` - 批量导入功能基础表
- `production_deployment_batch_import_2025-06-29.sql` - 批量导入生产部署增强

### 3. 字段修改脚本
- `add_invoice_number_field.sql` - 票据表添加发票号码字段
- `database/alter_add_ocr_info_fields_20250107.sql` - 添加OCR识别信息字段
- `database/migration_add_user_id_to_ai_config.sql` - AI配置表添加用户ID字段



## 重新生成结果

### aiform_update.sql 包含内容（基于代码实体类生成）

#### 表结构创建
1. **核心业务表**
   - `fxy_financial_bank_receipts` - 银行回单表（包含OCR识别信息字段）
   - `fxy_financial_bill` - 票据表（包含发票号码和OCR识别信息字段）

2. **AI功能表**
   - `fxy_financial_ai_config` - AI配置表（包含user_id字段）
   - `fxy_financial_ai_matching_history` - AI匹配历史记录表
   - `fxy_financial_subject_ai_enhancement` - 科目AI增强表

3. **批量导入表**
   - `fxy_financial_batch_import_task` - 批量导入任务表
   - `fxy_financial_batch_import_detail` - 批量导入明细表（支持图片拆分）
   - `fxy_financial_batch_import_result` - 批量导入结果表

4. **归并和关联表**
   - `fxy_financial_merge_rules` - 归并规则表
   - `fxy_financial_merge_tasks` - 归并任务表
   - `fxy_financial_document_groups` - 票据归并组表
   - `fxy_financial_receipt_groups` - 银证归并组表
   - `fxy_financial_entity_relations` - 统一关联表

#### 字段特性
1. **OCR识别信息**
   - 银行回单表和票据表都包含 `ocr_recognition_info` TEXT 字段
   - 用于存储格式化的OCR识别原始信息

2. **发票号码唯一性**
   - 票据表的 `invoice_number` 字段有唯一索引
   - 确保同一账套内发票号码不重复

#### 索引优化
- 票据表发票号码唯一索引
- 批量导入相关性能索引
- AI配置表用户ID索引

#### 数据初始化
- 默认AI配置数据（账套66）

## 整合完成

所有必要的SQL更新已整合到 `aiform_update.sql` 中。

## 执行建议

### 新环境部署
```sql
-- 1. 基础财务系统表
source financial.sql;

-- 2. AI财务系统完整功能
source aiform_update.sql;
```

### 现有环境更新
```sql
-- 只需要执行更新脚本
source aiform_update.sql;
```

## 注意事项

1. **代码一致性**: 脚本基于代码实体类生成，确保与代码完全一致
2. **幂等性**: 使用 `IF NOT EXISTS` 确保可重复执行
3. **外键约束**: 批量导入表之间有外键关联，删除时注意顺序
4. **索引优化**: 包含完整的性能优化索引
5. **服务重启**: 执行后需要重启后端服务以使实体类修改生效
6. **数据初始化**: 包含默认AI配置数据

## 验证方法

执行完成后可以通过以下SQL验证：

```sql
-- 检查表是否创建成功
SHOW TABLES LIKE '%batch_import%';
SHOW TABLES LIKE '%ai_%';

-- 检查字段是否添加成功
DESCRIBE fxy_financial_bill;
DESCRIBE fxy_financial_bank_receipts;

-- 检查索引是否创建成功
SHOW INDEX FROM fxy_financial_bill;
SHOW INDEX FROM fxy_financial_batch_import_task;
```
