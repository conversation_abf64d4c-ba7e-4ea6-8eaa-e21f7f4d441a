-- ========================================
-- 系统管理员模块数据库设计
-- 注意：此模块只提供独立的管理界面，操作的是现有系统数据
-- 只需要管理员账号、权限、日志等管理相关的表
-- ========================================

-- 1. 系统管理员账号表
CREATE TABLE IF NOT EXISTS `fxy_financial_admin_users` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '管理员用户名',
  `password` varchar(255) NOT NULL COMMENT '密码(SHA256加密)',
  `real_name` varchar(100) NOT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `is_super_admin` tinyint(1) DEFAULT '0' COMMENT '是否超级管理员',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int DEFAULT '0' COMMENT '登录次数',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` int DEFAULT NULL COMMENT '创建人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统管理员账号表';

-- 2. 管理员角色表
CREATE TABLE IF NOT EXISTS `fxy_financial_admin_roles` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `permissions` json DEFAULT NULL COMMENT '权限列表(JSON格式)',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统内置角色',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` int DEFAULT NULL COMMENT '创建人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='管理员角色表';

-- 3. 管理员角色关联表
CREATE TABLE IF NOT EXISTS `fxy_financial_admin_user_roles` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_user_id` int NOT NULL COMMENT '管理员用户ID',
  `role_id` int NOT NULL COMMENT '角色ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator_id` int DEFAULT NULL COMMENT '创建人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`admin_user_id`,`role_id`),
  KEY `idx_admin_user_id` (`admin_user_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='管理员角色关联表';

-- 4. 操作日志表
CREATE TABLE IF NOT EXISTS `fxy_financial_admin_operation_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_user_id` int NOT NULL COMMENT '操作人ID',
  `admin_username` varchar(50) NOT NULL COMMENT '操作人用户名',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `operation_module` varchar(50) NOT NULL COMMENT '操作模块',
  `operation_desc` varchar(500) NOT NULL COMMENT '操作描述',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `request_params` text DEFAULT NULL COMMENT '请求参数',
  `response_data` text DEFAULT NULL COMMENT '响应数据',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `execution_time` int DEFAULT NULL COMMENT '执行时间(毫秒)',
  `status` tinyint(1) DEFAULT '1' COMMENT '操作状态：1成功，0失败',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_user_id` (`admin_user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_module` (`operation_module`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='管理员操作日志表';

-- 5. 系统配置表
CREATE TABLE IF NOT EXISTS `fxy_financial_admin_system_configs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_group` varchar(50) NOT NULL COMMENT '配置分组',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型：string,number,boolean,json',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `is_encrypted` tinyint(1) DEFAULT '0' COMMENT '是否加密存储',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统配置',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` int DEFAULT NULL COMMENT '创建人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_key` (`config_group`,`config_key`),
  KEY `idx_config_group` (`config_group`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统配置表';

-- 注意：字段映射模板和AI配置使用现有系统的表，不需要新建
-- 现有表：fxy_financial_ai_config (AI配置)
-- 字段映射模板将扩展现有的OCR相关表或新增到现有系统中

-- 6. 数据导入导出记录表
CREATE TABLE IF NOT EXISTS `fxy_financial_admin_import_export_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_user_id` int NOT NULL COMMENT '操作人ID',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型：import,export',
  `data_type` varchar(50) NOT NULL COMMENT '数据类型：users,account_sets,ai_configs,field_mappings',
  `file_name` varchar(200) DEFAULT NULL COMMENT '文件名',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小(字节)',
  `total_records` int DEFAULT '0' COMMENT '总记录数',
  `success_records` int DEFAULT '0' COMMENT '成功记录数',
  `failed_records` int DEFAULT '0' COMMENT '失败记录数',
  `error_details` text DEFAULT NULL COMMENT '错误详情',
  `status` varchar(20) DEFAULT 'processing' COMMENT '状态：processing,completed,failed',
  `start_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execution_time` int DEFAULT NULL COMMENT '执行时间(秒)',
  PRIMARY KEY (`id`),
  KEY `idx_admin_user_id` (`admin_user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_data_type` (`data_type`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数据导入导出记录表';

-- ========================================
-- 初始化数据
-- ========================================

-- 1. 初始化系统管理员角色
INSERT INTO `fxy_financial_admin_roles` (`role_name`, `role_code`, `description`, `permissions`, `is_system`) VALUES
('超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限',
 '["user:*", "account:*", "ai:*", "template:*", "system:*", "log:*", "import:*", "export:*"]', 1),
('用户管理员', 'USER_ADMIN', '用户管理员，负责用户账号管理',
 '["user:view", "user:create", "user:edit", "user:delete", "user:reset_password", "log:view"]', 1),
('账套管理员', 'ACCOUNT_ADMIN', '账套管理员，负责账套管理',
 '["account:view", "account:create", "account:edit", "account:delete", "user:view", "log:view"]', 1),
('AI配置管理员', 'AI_ADMIN', 'AI配置管理员，负责AI相关配置',
 '["ai:view", "ai:edit", "template:view", "template:edit", "log:view"]', 1),
('只读管理员', 'READ_ONLY_ADMIN', '只读管理员，只能查看数据',
 '["user:view", "account:view", "ai:view", "template:view", "log:view"]', 1);

-- 2. 初始化默认超级管理员账号 (密码: admin123)
INSERT INTO `fxy_financial_admin_users` (`username`, `password`, `real_name`, `email`, `is_super_admin`, `status`) VALUES
('admin', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', '系统管理员', '<EMAIL>', 1, 1);

-- 3. 为超级管理员分配角色
INSERT INTO `fxy_financial_admin_user_roles` (`admin_user_id`, `role_id`) VALUES
(1, 1);

-- 4. 初始化系统配置
INSERT INTO `fxy_financial_admin_system_configs` (`config_group`, `config_key`, `config_value`, `config_type`, `description`, `is_system`) VALUES
('system', 'admin_session_timeout', '7200', 'number', '管理员会话超时时间(秒)', 1),
('system', 'max_login_attempts', '5', 'number', '最大登录尝试次数', 1),
('system', 'password_min_length', '8', 'number', '密码最小长度', 1),
('system', 'enable_operation_log', 'true', 'boolean', '是否启用操作日志', 1),
('system', 'log_retention_days', '90', 'number', '日志保留天数', 1),
('ai', 'default_provider', 'openai', 'string', '默认AI服务提供商', 1),
('ai', 'enable_ai_for_new_accounts', 'false', 'boolean', '新账套是否默认启用AI', 1),
('import_export', 'max_file_size', '********', 'number', '最大文件大小(字节)', 1),
('import_export', 'allowed_file_types', '["xlsx", "csv", "json"]', 'json', '允许的文件类型', 1);

-- 注意：AI配置模板和字段映射模板的初始化数据
-- 将通过管理员界面直接操作现有系统的相关表进行管理
