-- =====================================================
-- 可选表备份SQL脚本
-- =====================================================
-- 创建时间: 2025-01-07
-- 作用: 保存当前未完全实现或功能重复的表结构，供将来需要时参考
-- 说明: 这些表在当前系统中实现不完整或功能可能重复，暂时备份保存
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 拆分配置表
-- =====================================================
-- 说明: 用于存储图片拆分算法配置，但当前代码实现不完整
-- 状态: 表结构存在，但缺少对应的实体类和Service实现

CREATE TABLE IF NOT EXISTS `fxy_financial_split_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `import_type` enum('BANK_RECEIPT','INVOICE') NOT NULL COMMENT '导入类型',
  `split_method` varchar(50) NOT NULL COMMENT '拆分方法',
  `config_params` text COMMENT '配置参数（JSON格式）',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否为默认配置',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type_method` (`import_type`, `split_method`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='拆分配置表';

-- 插入默认拆分配置示例数据
INSERT IGNORE INTO `fxy_financial_split_config` (`config_name`, `import_type`, `split_method`, `config_params`, `is_default`, `account_sets_id`) VALUES
('银行回单固定网格拆分', 'BANK_RECEIPT', 'FIXED_GRID', '{"rows": 2, "cols": 2, "minWidth": 200, "minHeight": 150}', 1, 66),
('银行回单空白区域检测', 'BANK_RECEIPT', 'WHITESPACE_DETECTION', '{"threshold": 0.8, "minGap": 20}', 0, 66),
('发票固定网格拆分', 'INVOICE', 'FIXED_GRID', '{"rows": 1, "cols": 2, "minWidth": 300, "minHeight": 200}', 1, 66);

-- =====================================================
-- 2. 科目匹配模板表
-- =====================================================
-- 说明: 用于定义业务场景的科目匹配模板，但功能可能与subject_ai_enhancement重复
-- 状态: 表结构存在，但缺少完整的代码实现，功能可能被其他表替代

CREATE TABLE IF NOT EXISTS `fxy_financial_subject_matching_template` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `business_scenario` varchar(50) NOT NULL COMMENT '业务场景',
  `matching_conditions` json NOT NULL COMMENT '匹配条件',
  `subject_mapping` json NOT NULL COMMENT '科目映射规则',
  `priority` int DEFAULT '0' COMMENT '优先级',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `success_rate` decimal(5,2) DEFAULT '0.00' COMMENT '成功率',
  `usage_count` int DEFAULT '0' COMMENT '使用次数',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` int DEFAULT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_business_scenario` (`business_scenario`),
  KEY `idx_priority` (`priority`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='科目匹配模板表';

-- 插入示例匹配模板数据
INSERT IGNORE INTO `fxy_financial_subject_matching_template` 
(`account_sets_id`, `template_name`, `business_scenario`, `matching_conditions`, `subject_mapping`, `priority`) VALUES
(66, '银行收款模板', 'bank_income', 
 '{"type": "收入", "keywords": ["收款", "转入", "存款"]}',
 '{"debit": [{"code": "1002", "name": "银行存款"}], "credit": [{"code": "6001", "name": "主营业务收入"}]}',
 10),
(66, '银行付款模板', 'bank_expense', 
 '{"type": "支出", "keywords": ["付款", "转出", "支取"]}',
 '{"debit": [{"code": "6401", "name": "主营业务成本"}], "credit": [{"code": "1002", "name": "银行存款"}]}',
 10),
(66, '现金收款模板', 'cash_income', 
 '{"type": "收入", "keywords": ["现金", "收款"]}',
 '{"debit": [{"code": "1001", "name": "库存现金"}], "credit": [{"code": "6001", "name": "主营业务收入"}]}',
 8);

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 使用说明
-- =====================================================
-- 
-- 1. fxy_financial_split_config 表
--    - 设计用途: 存储图片拆分算法的配置参数
--    - 当前状态: 表结构完整，有示例数据，但缺少Java实体类和Service实现
--    - 建议: 如需使用此功能，需要补充完整的代码实现
--
-- 2. fxy_financial_subject_matching_template 表
--    - 设计用途: 定义业务场景的科目匹配模板
--    - 当前状态: 表结构完整，有示例数据，但功能可能与subject_ai_enhancement重复
--    - 建议: 评估是否与现有AI增强功能重复，考虑整合或废弃
--
-- 恢复说明:
-- 如果将来需要使用这些表，可以执行此脚本来创建表结构和初始数据
-- 同时需要补充对应的Java代码实现（实体类、Mapper、Service等）
--
-- =====================================================
