-- AI配置表添加用户ID字段迁移脚本
-- 执行时间: 2024-12-19
-- 目的: 将AI配置从账套关联改为用户关联

-- 1. 添加user_id字段
ALTER TABLE fxy_financial_ai_config 
ADD COLUMN user_id INT(11) COMMENT '用户ID' AFTER account_sets_id;

-- 2. 添加索引
ALTER TABLE fxy_financial_ai_config 
ADD INDEX idx_user_id (user_id);

-- 3. 数据迁移说明
-- 由于现有数据是基于账套的，需要手动决定如何迁移：
-- 选项1: 将现有配置分配给账套管理员
-- 选项2: 清空现有配置，让用户重新配置
-- 选项3: 为每个用户复制一份配置

-- 示例迁移脚本（需要根据实际情况调整）:
-- 将现有配置分配给账套的第一个用户（需要根据实际业务逻辑调整）
/*
UPDATE fxy_financial_ai_config ai
SET ai.user_id = (
    SELECT u.id 
    FROM user u 
    WHERE u.account_sets_id = ai.account_sets_id 
    LIMIT 1
)
WHERE ai.user_id IS NULL;
*/

-- 4. 清理无效数据（可选）
-- DELETE FROM fxy_financial_ai_config WHERE user_id IS NULL;

-- 5. 添加外键约束（可选，根据实际需要）
-- ALTER TABLE fxy_financial_ai_config 
-- ADD CONSTRAINT fk_ai_config_user_id 
-- FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE;

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 根据实际业务需求调整数据迁移策略
-- 3. 测试环境先验证迁移脚本的正确性
-- 4. 生产环境执行时注意业务影响
