-- =====================================================
-- 批量上传和识别功能 - 生产环境部署SQL脚本
-- 创建时间: 2025-06-29
-- 说明: 部署批量上传、OCR识别、LLM智能字段映射功能到生产环境
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 创建批量导入任务表
-- =====================================================
CREATE TABLE IF NOT EXISTS `fxy_financial_batch_import_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID，使用UUID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `import_type` enum('BANK_RECEIPT','INVOICE') NOT NULL COMMENT '导入类型：银行回单/发票',
  `total_files` int NOT NULL DEFAULT '0' COMMENT '总文件数',
  `total_images` int NOT NULL DEFAULT '0' COMMENT '总图片数（PDF解析后）',
  `processed_files` int NOT NULL DEFAULT '0' COMMENT '已处理文件数',
  `processed_images` int NOT NULL DEFAULT '0' COMMENT '已处理图片数',
  `success_count` int NOT NULL DEFAULT '0' COMMENT '成功识别数量',
  `failed_count` int NOT NULL DEFAULT '0' COMMENT '失败数量',
  `status` enum('UPLOADING','PROCESSING','RECOGNIZING','PREVIEWING','SAVING','COMPLETED','FAILED','CANCELLED') NOT NULL DEFAULT 'UPLOADING' COMMENT '任务状态',
  `progress_percentage` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '进度百分比',
  `error_message` text COMMENT '错误信息',
  `start_time` datetime DEFAULT NULL COMMENT '开始处理时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `create_user` int NOT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_account_sets_user` (`account_sets_id`,`create_user`),
  KEY `idx_status` (`status`),
  KEY `idx_import_type` (`import_type`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='批量导入任务表';

-- =====================================================
-- 2. 创建批量导入明细表
-- =====================================================
CREATE TABLE IF NOT EXISTS `fxy_financial_batch_import_detail` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `file_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_url` varchar(500) NOT NULL COMMENT '原始文件URL',
  `file_type` enum('PDF','IMAGE') NOT NULL COMMENT '文件类型',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `page_number` int DEFAULT NULL COMMENT 'PDF页码（图片文件为NULL）',
  `image_url` varchar(500) DEFAULT NULL COMMENT '解析后的图片URL',
  `image_width` int DEFAULT NULL COMMENT '图片宽度',
  `image_height` int DEFAULT NULL COMMENT '图片高度',
  `sub_image_index` int DEFAULT NULL COMMENT '子图片索引（拆分后的图片序号）',
  `original_image_url` varchar(500) DEFAULT NULL COMMENT '原始图片URL（拆分前的图片）',
  `is_split` tinyint(1) DEFAULT 0 COMMENT '是否为拆分图片',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `recognition_result` json DEFAULT NULL COMMENT 'OCR识别原始结果',
  `parsed_data` json DEFAULT NULL COMMENT '解析后的结构化数据',
  `final_data` json DEFAULT NULL COMMENT '用户确认后的最终数据',
  `confidence_score` decimal(5,4) DEFAULT NULL COMMENT 'OCR识别置信度',
  `status` enum('PENDING','UPLOADING','PROCESSING','RECOGNIZING','SUCCESS','FAILED','SKIPPED') NOT NULL DEFAULT 'PENDING' COMMENT '处理状态',
  `error_message` text COMMENT '错误信息',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `processing_time` int DEFAULT NULL COMMENT '处理耗时（毫秒）',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_task_split` (`task_id`, `is_split`),
  KEY `idx_original_image` (`original_image_url`),
  KEY `idx_account_sets` (`account_sets_id`),
  CONSTRAINT `fk_batch_detail_task` FOREIGN KEY (`task_id`) REFERENCES `fxy_financial_batch_import_task` (`task_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='批量导入明细表（支持图片拆分）';

-- =====================================================
-- 3. 创建批量导入结果表
-- =====================================================
CREATE TABLE IF NOT EXISTS `fxy_financial_batch_import_result` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `detail_id` int NOT NULL COMMENT '明细ID',
  `business_type` enum('BANK_RECEIPT','BILL') NOT NULL COMMENT '业务类型',
  `business_id` int DEFAULT NULL COMMENT '业务记录ID（保存成功后）',
  `import_status` enum('SUCCESS','FAILED','SKIPPED') NOT NULL COMMENT '导入状态',
  `error_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_detail_id` (`detail_id`),
  KEY `idx_business` (`business_type`,`business_id`),
  KEY `idx_import_status` (`import_status`),
  CONSTRAINT `fk_batch_result_task` FOREIGN KEY (`task_id`) REFERENCES `fxy_financial_batch_import_task` (`task_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_batch_result_detail` FOREIGN KEY (`detail_id`) REFERENCES `fxy_financial_batch_import_detail` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='批量导入结果表';

-- =====================================================
-- 4. 创建图片拆分日志表（可选）
-- =====================================================
CREATE TABLE IF NOT EXISTS `fxy_financial_image_split_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `original_detail_id` int NOT NULL COMMENT '原始明细ID',
  `original_image_url` varchar(500) NOT NULL COMMENT '原始图片URL',
  `split_count` int NOT NULL COMMENT '拆分数量',
  `split_method` varchar(50) DEFAULT 'AUTO' COMMENT '拆分方式：AUTO-自动，MANUAL-手动',
  `split_areas` text COMMENT '拆分区域信息（JSON格式）',
  `created_by` int DEFAULT NULL COMMENT '操作用户ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_original_detail` (`original_detail_id`),
  KEY `idx_account_sets` (`account_sets_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片拆分日志表';

-- =====================================================
-- 5. 创建拆分配置表（可选）
-- =====================================================
CREATE TABLE IF NOT EXISTS `fxy_financial_split_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `import_type` enum('BANK_RECEIPT','INVOICE') NOT NULL COMMENT '导入类型',
  `split_method` varchar(50) NOT NULL COMMENT '拆分方法',
  `config_params` text COMMENT '配置参数（JSON格式）',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否为默认配置',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type_method` (`import_type`, `split_method`),
  KEY `idx_account_sets` (`account_sets_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拆分配置表';

-- =====================================================
-- 6. 创建性能优化索引
-- =====================================================

-- 任务表复合索引
CREATE INDEX idx_task_status_type ON fxy_financial_batch_import_task(status, import_type);
CREATE INDEX idx_task_user_time ON fxy_financial_batch_import_task(create_user, created_time DESC);

-- 明细表复合索引
CREATE INDEX idx_detail_task_status ON fxy_financial_batch_import_detail(task_id, status);
CREATE INDEX idx_detail_file_status ON fxy_financial_batch_import_detail(file_type, status);

-- 结果表复合索引
CREATE INDEX idx_result_task_status ON fxy_financial_batch_import_result(task_id, import_status);

-- =====================================================
-- 7. 插入默认拆分配置（根据实际账套ID调整）
-- =====================================================

-- 注意：请根据实际的账套ID修改下面的account_sets_id值
INSERT IGNORE INTO `fxy_financial_split_config` (`config_name`, `import_type`, `split_method`, `config_params`, `is_default`, `account_sets_id`) VALUES
('银行回单垂直拆分（3张/页）', 'BANK_RECEIPT', 'VERTICAL_SPLIT', '{"receiptsPerPage": 3, "direction": "vertical"}', 1, 1),
('银行回单固定网格拆分', 'BANK_RECEIPT', 'FIXED_GRID', '{"rows": 2, "cols": 2, "minWidth": 200, "minHeight": 150}', 0, 1),
('发票固定网格拆分', 'INVOICE', 'FIXED_GRID', '{"rows": 1, "cols": 2, "minWidth": 300, "minHeight": 200}', 1, 1);

-- =====================================================
-- 8. 创建统计视图
-- =====================================================

CREATE OR REPLACE VIEW `v_batch_import_split_stats` AS
SELECT 
    t.task_id,
    t.task_name,
    t.import_type,
    t.total_images,
    COUNT(d.id) as detail_count,
    SUM(CASE WHEN d.is_split = 1 THEN 1 ELSE 0 END) as split_image_count,
    COUNT(DISTINCT d.original_image_url) as original_image_count,
    t.account_sets_id
FROM `fxy_financial_batch_import_task` t
LEFT JOIN `fxy_financial_batch_import_detail` d ON t.task_id = d.task_id
GROUP BY t.task_id, t.task_name, t.import_type, t.total_images, t.account_sets_id;

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 9. 验证表创建结果
-- =====================================================

SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE '%batch_import%'
ORDER BY TABLE_NAME;

-- =====================================================
-- 脚本执行完成
-- =====================================================
-- 创建日期: 2025-06-29
-- 功能: 批量上传和识别功能完整数据表
-- 包含功能:
-- 1. 批量文件上传（PDF/图片）
-- 2. PDF自动解析为图片
-- 3. 图片拆分功能（垂直拆分、网格拆分）
-- 4. OCR识别（腾讯云）
-- 5. LLM智能字段映射（OpenAI兼容接口）
-- 6. 批量预览和编辑
-- 7. 批量保存到业务表
-- =====================================================

SELECT '批量上传和识别功能数据表创建完成！' as message;
