-- =====================================================
-- 添加OCR识别信息字段
-- =====================================================
-- 创建时间: 2025-01-07
-- 作用: 为银行回单表和票据表添加专门的OCR识别信息字段
-- 目的: 解决OCR识别信息存储问题，保持备注字段原有用途
-- 执行人: AI Assistant
-- =====================================================

-- 1. 将银行回单表的备注字段改为TEXT类型（与票据表保持一致）
ALTER TABLE `fxy_financial_bank_receipts`
MODIFY COLUMN `remark` TEXT COMMENT '备注';

-- 2. 为银行回单表添加OCR识别信息字段
ALTER TABLE `fxy_financial_bank_receipts`
ADD COLUMN `ocr_recognition_info` TEXT COMMENT 'OCR识别的原始信息（JSON格式）' AFTER `remark`;

-- 3. 为票据表添加OCR识别信息字段
ALTER TABLE `fxy_financial_bill`
ADD COLUMN `ocr_recognition_info` TEXT COMMENT 'OCR识别的原始信息（JSON格式）' AFTER `remark`;

-- 4. 验证修改结果
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('fxy_financial_bank_receipts', 'fxy_financial_bill')
    AND COLUMN_NAME IN ('remark', 'ocr_recognition_info')
ORDER BY TABLE_NAME, ORDINAL_POSITION;

-- =====================================================
-- 执行说明
-- =====================================================
-- 1. 此脚本为银行回单表和票据表添加专门的OCR识别信息字段
-- 2. 保持原有备注字段的用途，用户仍可添加自定义备注
-- 3. OCR识别信息将存储在新的 ocr_recognition_info 字段中
-- 4. 执行前请备份数据库
-- 5. 执行后需要重启后端服务以使实体类修改生效
-- =====================================================
