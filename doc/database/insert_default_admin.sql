-- 插入默认管理员用户
-- 用户名: admin
-- 密码: admin123

INSERT INTO fxy_financial_admin_users (username, password, email, real_name, role, status, create_time, update_time) 
VALUES ('admin', 'admin123', '<EMAIL>', '系统管理员', 'SUPER_ADMIN', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
password = 'admin123',
email = '<EMAIL>',
real_name = '系统管理员',
role = 'SUPER_ADMIN',
status = 1,
update_time = NOW();

-- 插入管理员权限
INSERT INTO fxy_financial_admin_permissions (admin_user_id, permission_code, permission_name, create_time) 
SELECT 
    au.id,
    'ALL',
    '所有权限',
    NOW()
FROM fxy_financial_admin_users au 
WHERE au.username = 'admin'
ON DUPLICATE KEY UPDATE 
permission_name = '所有权限',
create_time = NOW();
