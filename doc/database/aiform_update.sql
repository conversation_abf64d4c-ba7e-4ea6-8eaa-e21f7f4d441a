-- =====================================================
-- AI财务系统完整更新脚本
-- =====================================================
-- 创建时间: 2025-01-07
-- 作用: 在 financial.sql 基础上，创建所有AI财务系统需要的表和字段
-- 执行顺序: financial.sql -> aiform_update.sql
-- 说明: 不再依赖 aiform.sql，直接基于 financial.sql 进行完整部署
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 核心业务表（银行回单和票据）
-- =====================================================

-- 银行回单表
CREATE TABLE IF NOT EXISTS `fxy_financial_bank_receipts` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `receipts_no` varchar(50) NOT NULL COMMENT '单据编号',
  `receipts_date` date NOT NULL COMMENT '单据日期',
  `type` varchar(20) NOT NULL COMMENT '类型：收入/支出',
  `amount` double NOT NULL COMMENT '金额',
  `summary` varchar(200) DEFAULT NULL COMMENT '摘要',
  `remark` text COMMENT '备注',
  `receipt_num` int DEFAULT '0' COMMENT '附单据数量',
  `create_member` int DEFAULT NULL COMMENT '创建人ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `receipts_year` int DEFAULT NULL COMMENT '单据年份',
  `receipts_month` int DEFAULT NULL COMMENT '单据月份',
  `receipt_group_id` varchar(36) DEFAULT NULL COMMENT '银证归并组ID',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `payee_name` varchar(100) DEFAULT NULL COMMENT '收款人姓名',
  `payee_account` varchar(50) DEFAULT NULL COMMENT '收款人账号',
  `payee_bank` varchar(100) DEFAULT NULL COMMENT '收款人开户行',
  `payer_name` varchar(100) DEFAULT NULL COMMENT '付款人姓名',
  `payer_account` varchar(50) DEFAULT NULL COMMENT '付款人账号',
  `payer_bank` varchar(100) DEFAULT NULL COMMENT '付款人开户行',
  `transfer_date` date DEFAULT NULL COMMENT '转账日期',
  `submission_date` date DEFAULT NULL COMMENT '提交日期',
  `serial_number` varchar(50) DEFAULT NULL COMMENT '流水号',
  `payer_bank_code` varchar(20) DEFAULT NULL COMMENT '付款人银行代码',
  `payee_bank_code` varchar(20) DEFAULT NULL COMMENT '收款人银行代码',
  `amount_in_words` varchar(200) DEFAULT NULL COMMENT '金额大写',
  `transaction_institution` varchar(100) DEFAULT NULL COMMENT '交易机构',
  `receipt_title` varchar(200) DEFAULT NULL COMMENT '回单名称',
  `ocr_recognition_info` text COMMENT 'OCR识别的原始信息（JSON格式）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_receipts_no_account` (`receipts_no`, `account_sets_id`),
  KEY `idx_account_sets_date` (`account_sets_id`, `receipts_date`),
  KEY `idx_receipts_year_month` (`receipts_year`, `receipts_month`),
  KEY `idx_receipt_group` (`receipt_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='银行回单表';

-- 票据表
CREATE TABLE IF NOT EXISTS `fxy_financial_bill` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bill_no` varchar(50) NOT NULL COMMENT '票据编号',
  `bill_date` date NOT NULL COMMENT '票据日期',
  `type` varchar(50) NOT NULL COMMENT '票据类型（发票/收据/欠条/机票等）',
  `amount` double NOT NULL COMMENT '金额',
  `issuer` varchar(200) DEFAULT NULL COMMENT '开票方/收款方',
  `recipient` varchar(200) DEFAULT NULL COMMENT '收票方/付款方',
  `summary` varchar(200) DEFAULT NULL COMMENT '摘要/用途',
  `remark` text COMMENT '备注',
  `status` varchar(20) DEFAULT '未使用' COMMENT '状态',
  `doc_group_id` varchar(36) DEFAULT NULL COMMENT '票据归并组ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `bill_year` int DEFAULT NULL COMMENT '票据年份',
  `bill_month` int DEFAULT NULL COMMENT '票据月份',
  `invoice_number` varchar(50) DEFAULT NULL COMMENT '发票号码',
  `attachment_path` varchar(500) DEFAULT NULL COMMENT '附件路径',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tax_rate` decimal(5,4) DEFAULT NULL COMMENT '税率',
  `total_tax_amount` decimal(15,2) DEFAULT NULL COMMENT '合计税额',
  `amount_in_words` varchar(200) DEFAULT NULL COMMENT '小写金额',
  `ocr_recognition_info` text COMMENT 'OCR识别的原始信息（JSON格式）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_no_account` (`bill_no`, `account_sets_id`),
  UNIQUE KEY `uk_invoice_number_account_sets` (`invoice_number`, `account_sets_id`),
  KEY `idx_account_sets_date` (`account_sets_id`, `bill_date`),
  KEY `idx_bill_year_month` (`bill_year`, `bill_month`),
  KEY `idx_doc_group` (`doc_group_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='票据表';

-- =====================================================
-- 2. AI配置和增强功能表
-- =====================================================

-- AI配置表
CREATE TABLE IF NOT EXISTS `fxy_financial_ai_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_config` (`account_sets_id`,`config_key`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='AI配置表';

-- AI匹配历史记录表
CREATE TABLE IF NOT EXISTS `fxy_financial_ai_matching_history` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型：bank_receipts/bill',
  `business_id` int NOT NULL COMMENT '业务ID（银行回单ID或票据ID）',
  `matched_subjects` json NOT NULL COMMENT '匹配的科目信息',
  `confidence_score` decimal(3,2) DEFAULT NULL COMMENT '置信度分数',
  `ai_reasoning` text COMMENT 'AI推理过程',
  `user_feedback` tinyint DEFAULT NULL COMMENT '用户反馈：1正确，0错误，NULL未反馈',
  `processing_time` int DEFAULT NULL COMMENT '处理时间（毫秒）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` int DEFAULT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_account_sets_business` (`account_sets_id`,`business_type`,`business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI匹配历史记录表';

-- 科目AI增强表
CREATE TABLE IF NOT EXISTS `fxy_financial_subject_ai_enhancement` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `subject_id` int NOT NULL COMMENT '科目ID，关联fxy_financial_subject.id',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `ai_description` text COMMENT 'AI描述信息，用于大模型理解科目用途',
  `ai_keywords` varchar(500) DEFAULT NULL COMMENT 'AI关键词，逗号分隔，用于匹配',
  `matching_rules` json DEFAULT NULL COMMENT '匹配规则配置（JSON格式）',
  `confidence_score` decimal(3,2) DEFAULT '0.50' COMMENT '置信度分数，0-1之间',
  `usage_frequency` int DEFAULT '0' COMMENT '使用频率统计',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` int DEFAULT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_subject_account` (`subject_id`, `account_sets_id`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_confidence_score` (`confidence_score`),
  KEY `idx_usage_frequency` (`usage_frequency`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='科目AI增强表';

-- =====================================================
-- 3. 批量导入功能表
-- =====================================================

-- 批量导入任务表
CREATE TABLE IF NOT EXISTS `fxy_financial_batch_import_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID，使用UUID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `import_type` enum('BANK_RECEIPT','INVOICE') NOT NULL COMMENT '导入类型：银行回单/发票',
  `total_files` int NOT NULL DEFAULT '0' COMMENT '总文件数',
  `total_images` int NOT NULL DEFAULT '0' COMMENT '总图片数（PDF解析后）',
  `processed_files` int NOT NULL DEFAULT '0' COMMENT '已处理文件数',
  `processed_images` int NOT NULL DEFAULT '0' COMMENT '已处理图片数',
  `success_count` int NOT NULL DEFAULT '0' COMMENT '成功识别数量',
  `failed_count` int NOT NULL DEFAULT '0' COMMENT '失败数量',
  `split_count` int DEFAULT '0' COMMENT '拆分图片数量',
  `auto_split_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用自动拆分',
  `status` enum('UPLOADING','PROCESSING','RECOGNIZING','PREVIEWING','SAVING','COMPLETED','PARTIAL_SUCCESS','FAILED','CANCELLED') NOT NULL DEFAULT 'UPLOADING' COMMENT '任务状态',
  `progress_percentage` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '进度百分比',
  `error_message` text COMMENT '错误信息',
  `start_time` datetime DEFAULT NULL COMMENT '开始处理时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `create_user` int NOT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_account_sets_user` (`account_sets_id`,`create_user`),
  KEY `idx_status` (`status`),
  KEY `idx_import_type` (`import_type`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_account_sets` (`account_sets_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='批量导入任务表';

-- 批量导入明细表
CREATE TABLE IF NOT EXISTS `fxy_financial_batch_import_detail` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `file_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_url` varchar(500) NOT NULL COMMENT '原始文件URL',
  `file_type` enum('PDF','IMAGE') NOT NULL COMMENT '文件类型',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `page_number` int DEFAULT NULL COMMENT 'PDF页码（图片文件为NULL）',
  `image_url` varchar(500) DEFAULT NULL COMMENT '解析后的图片URL',
  `image_width` int DEFAULT NULL COMMENT '图片宽度',
  `image_height` int DEFAULT NULL COMMENT '图片高度',
  `sub_image_index` int DEFAULT NULL COMMENT '子图片索引（拆分后的图片序号）',
  `original_image_url` varchar(500) DEFAULT NULL COMMENT '原始图片URL（拆分前的图片）',
  `is_split` tinyint(1) DEFAULT '0' COMMENT '是否为拆分图片',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `recognition_result` json DEFAULT NULL COMMENT 'OCR识别原始结果',
  `parsed_data` json DEFAULT NULL COMMENT '解析后的结构化数据',
  `final_data` json DEFAULT NULL COMMENT '用户确认后的最终数据',
  `confidence_score` decimal(5,4) DEFAULT NULL COMMENT 'OCR识别置信度',
  `status` enum('PENDING','UPLOADING','PROCESSING','RECOGNIZING','SUCCESS','FAILED','SKIPPED') NOT NULL DEFAULT 'PENDING' COMMENT '处理状态',
  `error_message` text COMMENT '错误信息',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `processing_time` int DEFAULT NULL COMMENT '处理耗时（毫秒）',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_task_split` (`task_id`,`is_split`),
  KEY `idx_original_image` (`original_image_url`),
  KEY `idx_account_sets` (`account_sets_id`),
  CONSTRAINT `fk_batch_detail_task` FOREIGN KEY (`task_id`) REFERENCES `fxy_financial_batch_import_task` (`task_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='批量导入明细表（支持图片拆分）';

-- 批量导入结果表
CREATE TABLE IF NOT EXISTS `fxy_financial_batch_import_result` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `detail_id` int NOT NULL COMMENT '明细ID',
  `business_type` enum('BANK_RECEIPT','BILL') NOT NULL COMMENT '业务类型',
  `business_id` int DEFAULT NULL COMMENT '业务记录ID（保存成功后）',
  `import_status` enum('SUCCESS','FAILED','SKIPPED') NOT NULL COMMENT '导入状态',
  `error_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_detail_id` (`detail_id`),
  KEY `idx_business` (`business_type`,`business_id`),
  KEY `idx_import_status` (`import_status`),
  CONSTRAINT `fk_batch_result_task` FOREIGN KEY (`task_id`) REFERENCES `fxy_financial_batch_import_task` (`task_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_batch_result_detail` FOREIGN KEY (`detail_id`) REFERENCES `fxy_financial_batch_import_detail` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='批量导入结果表';

-- =====================================================
-- 4. 归并和关联功能表
-- =====================================================

-- 归并规则表
CREATE TABLE IF NOT EXISTS `fxy_financial_merge_rules` (
  `rule_id` varchar(36) NOT NULL COMMENT '规则ID，使用UUID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_description` text COMMENT '规则描述',
  `applicable_entity` enum('DOCUMENT','RECEIPT','BOTH') NOT NULL COMMENT '适用实体：DOCUMENT-票据，RECEIPT-银证，BOTH-两者',
  `rule_logic` json NOT NULL COMMENT '规则逻辑配置（JSON格式）',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int DEFAULT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`rule_id`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_applicable_entity` (`applicable_entity`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='归并规则表';

-- 归并任务表
CREATE TABLE IF NOT EXISTS `fxy_financial_merge_tasks` (
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_type` enum('DOCUMENT_MERGE','RECEIPT_MERGE','RELATION_CREATE') NOT NULL COMMENT '任务类型：DOCUMENT_MERGE-票据归并，RECEIPT_MERGE-银证归并，RELATION_CREATE-关联创建',
  `rule_id` varchar(36) DEFAULT NULL COMMENT '使用的规则ID',
  `status` enum('PENDING','RUNNING','COMPLETED','FAILED') DEFAULT 'PENDING' COMMENT '任务状态：PENDING-待处理，RUNNING-运行中，COMPLETED-已完成，FAILED-失败',
  `progress` int DEFAULT '0' COMMENT '进度百分比',
  `total_items` int DEFAULT '0' COMMENT '总项目数',
  `processed_items` int DEFAULT '0' COMMENT '已处理项目数',
  `success_items` int DEFAULT '0' COMMENT '成功项目数',
  `failed_items` int DEFAULT '0' COMMENT '失败项目数',
  `error_message` text COMMENT '错误信息',
  `result_summary` json DEFAULT NULL COMMENT '结果摘要（JSON格式）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `started_at` datetime DEFAULT NULL COMMENT '开始时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `created_by` int DEFAULT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`task_id`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_status` (`status`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='归并任务表';

-- 票据归并组表
CREATE TABLE IF NOT EXISTS `fxy_financial_document_groups` (
  `group_id` varchar(36) NOT NULL COMMENT '组ID，使用UUID',
  `group_name` varchar(100) NOT NULL COMMENT '组名称',
  `merge_rule_id` varchar(36) DEFAULT NULL COMMENT '使用的归并规则ID',
  `rule_params` json DEFAULT NULL COMMENT '规则参数（JSON格式）',
  `group_summary` text COMMENT '组摘要信息',
  `total_amount` decimal(15,2) DEFAULT '0.00' COMMENT '组内总金额',
  `item_count` int DEFAULT '0' COMMENT '组内项目数量',
  `status` enum('ACTIVE','ARCHIVED','DELETED') DEFAULT 'ACTIVE' COMMENT '组状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int DEFAULT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`group_id`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_merge_rule` (`merge_rule_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='票据归并组表';

-- 银证归并组表
CREATE TABLE IF NOT EXISTS `fxy_financial_receipt_groups` (
  `group_id` varchar(36) NOT NULL COMMENT '组ID，使用UUID',
  `group_name` varchar(100) NOT NULL COMMENT '组名称',
  `merge_rule_id` varchar(36) DEFAULT NULL COMMENT '使用的归并规则ID',
  `rule_params` json DEFAULT NULL COMMENT '规则参数（JSON格式）',
  `group_summary` text COMMENT '组摘要信息',
  `total_amount` decimal(15,2) DEFAULT '0.00' COMMENT '组内总金额',
  `item_count` int DEFAULT '0' COMMENT '组内项目数量',
  `status` enum('ACTIVE','ARCHIVED','DELETED') DEFAULT 'ACTIVE' COMMENT '组状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int DEFAULT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`group_id`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_merge_rule` (`merge_rule_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='银证归并组表';

-- 统一关联表
CREATE TABLE IF NOT EXISTS `fxy_financial_entity_relations` (
  `relation_id` varchar(36) NOT NULL COMMENT '关联ID，使用UUID',
  `source_type` enum('DOCUMENT','DOCUMENT_GROUP','RECEIPT','RECEIPT_GROUP') NOT NULL COMMENT '源类型：DOCUMENT-票据，DOCUMENT_GROUP-票据组，RECEIPT-银证，RECEIPT_GROUP-银证组',
  `source_id` varchar(36) NOT NULL COMMENT '源ID',
  `target_type` enum('DOCUMENT','DOCUMENT_GROUP','RECEIPT','RECEIPT_GROUP') NOT NULL COMMENT '目标类型：DOCUMENT-票据，DOCUMENT_GROUP-票据组，RECEIPT-银证，RECEIPT_GROUP-银证组',
  `target_id` varchar(36) NOT NULL COMMENT '目标ID',
  `relation_type` enum('ONE_TO_ONE','ONE_TO_MANY','MANY_TO_ONE','MANY_TO_MANY') DEFAULT 'ONE_TO_ONE' COMMENT '关联类型',
  `confidence_score` decimal(3,2) DEFAULT NULL COMMENT '关联置信度',
  `relation_reason` text COMMENT '关联原因说明',
  `is_confirmed` tinyint(1) DEFAULT '0' COMMENT '是否已确认',
  `confirmed_by` int DEFAULT NULL COMMENT '确认人ID',
  `confirmed_at` datetime DEFAULT NULL COMMENT '确认时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `created_by` int DEFAULT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`relation_id`),
  UNIQUE KEY `uk_source_target` (`source_type`,`source_id`,`target_type`,`target_id`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_source` (`source_type`,`source_id`),
  KEY `idx_target` (`target_type`,`target_id`),
  KEY `idx_relation_type` (`relation_type`),
  KEY `idx_is_confirmed` (`is_confirmed`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一关联表';

-- =====================================================
-- 5. 索引优化
-- =====================================================

-- 批量导入相关索引（使用DROP IF EXISTS + CREATE 模式避免重复创建错误）
DROP INDEX IF EXISTS idx_task_status_type ON fxy_financial_batch_import_task;
CREATE INDEX idx_task_status_type ON fxy_financial_batch_import_task(status, import_type);

DROP INDEX IF EXISTS idx_task_user_time ON fxy_financial_batch_import_task;
CREATE INDEX idx_task_user_time ON fxy_financial_batch_import_task(create_user, created_time DESC);

DROP INDEX IF EXISTS idx_detail_task_status ON fxy_financial_batch_import_detail;
CREATE INDEX idx_detail_task_status ON fxy_financial_batch_import_detail(task_id, status);

DROP INDEX IF EXISTS idx_detail_file_status ON fxy_financial_batch_import_detail;
CREATE INDEX idx_detail_file_status ON fxy_financial_batch_import_detail(file_type, status);

DROP INDEX IF EXISTS idx_result_task_status ON fxy_financial_batch_import_result;
CREATE INDEX idx_result_task_status ON fxy_financial_batch_import_result(task_id, import_status);

-- =====================================================
-- 6. 扩展功能表（基于实际使用情况添加）
-- =====================================================

-- 票据AI处理结果表
CREATE TABLE IF NOT EXISTS `fxy_financial_bill_ai_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bill_id` int NOT NULL COMMENT '关联的票据ID',
  `ai_processed` tinyint(1) DEFAULT '0' COMMENT 'AI处理状态：0-未处理，1-已处理',
  `confidence` decimal(5,4) DEFAULT NULL COMMENT 'AI置信度(0-1)',
  `ai_analysis` text COMMENT 'AI分析结果',
  `suggested_subjects` json DEFAULT NULL COMMENT '推荐科目信息(JSON格式)',
  `debit_subjects` json DEFAULT NULL COMMENT '借方科目推荐(JSON格式)',
  `credit_subjects` json DEFAULT NULL COMMENT '贷方科目推荐(JSON格式)',
  `matching_reason` text COMMENT 'AI匹配原因说明',
  `ai_process_time` datetime DEFAULT NULL COMMENT 'AI处理时间',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_ai_processed` (`ai_processed`),
  KEY `idx_confidence` (`confidence`),
  CONSTRAINT `fk_bill_ai_result_bill` FOREIGN KEY (`bill_id`) REFERENCES `fxy_financial_bill` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='票据AI处理结果表';

-- 图片拆分日志表
CREATE TABLE IF NOT EXISTS `fxy_financial_image_split_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `original_detail_id` int NOT NULL COMMENT '原始明细ID',
  `original_image_url` varchar(500) NOT NULL COMMENT '原始图片URL',
  `split_count` int NOT NULL COMMENT '拆分数量',
  `split_method` varchar(50) DEFAULT 'AUTO' COMMENT '拆分方式：AUTO-自动，MANUAL-手动',
  `split_areas` text COMMENT '拆分区域信息（JSON格式）',
  `created_by` int DEFAULT NULL COMMENT '操作用户ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_original_detail` (`original_detail_id`),
  KEY `idx_account_sets` (`account_sets_id`),
  CONSTRAINT `fk_image_split_log_task` FOREIGN KEY (`task_id`) REFERENCES `fxy_financial_batch_import_task` (`task_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_image_split_log_detail` FOREIGN KEY (`original_detail_id`) REFERENCES `fxy_financial_batch_import_detail` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='图片拆分日志表';

-- =====================================================
-- 7. 字段映射模板功能表（性能优化）
-- =====================================================

-- 字段映射模板主表
-- 用于缓存OCR字段到标准字段的映射规则，减少LLM调用次数
CREATE TABLE IF NOT EXISTS `fxy_financial_field_mapping_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `bank_identifier` varchar(50) DEFAULT NULL COMMENT '银行标识（如：交通银行、BANK OF COMMUNICATIONS）',
  `document_type` varchar(50) NOT NULL COMMENT '票据类型（BANK_RECEIPT-银行回单, INVOICE-发票）',
  `receipt_type` varchar(100) DEFAULT NULL COMMENT '回单类型（如：支付转账、代发工资、汇总收费扣款）',
  `field_count` int(11) DEFAULT NULL COMMENT 'OCR识别字段数量',
  `field_signature` varchar(500) DEFAULT NULL COMMENT '字段特征签名（字段名称的hash值）',
  `mapping_rules` text NOT NULL COMMENT '字段映射规则（JSON格式）',
  `sample_ocr_data` text DEFAULT NULL COMMENT '样本OCR数据（用于调试和验证）',
  `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `success_rate` decimal(5,2) DEFAULT 100.00 COMMENT '成功率（百分比）',
  `last_used_time` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `account_sets_id` int(11) NOT NULL COMMENT '账套ID',
  `create_user` int(11) NOT NULL COMMENT '创建用户ID',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用（1-启用，0-禁用）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_signature` (`account_sets_id`, `document_type`, `field_signature`),
  KEY `idx_bank_type` (`bank_identifier`, `document_type`, `receipt_type`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_usage` (`usage_count`, `success_rate`),
  KEY `idx_last_used` (`last_used_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字段映射模板表';

-- 字段映射规则详情表
CREATE TABLE IF NOT EXISTS `fxy_financial_field_mapping_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_id` int(11) NOT NULL COMMENT '模板ID',
  `ocr_field_name` varchar(100) NOT NULL COMMENT 'OCR识别的字段名称',
  `standard_field_name` varchar(100) NOT NULL COMMENT '标准字段名称',
  `field_type` varchar(20) NOT NULL COMMENT '字段类型（STRING, NUMBER, DATE, BOOLEAN）',
  `transform_rule` varchar(500) DEFAULT NULL COMMENT '转换规则（如：去除符号、日期格式转换等）',
  `default_value` varchar(200) DEFAULT NULL COMMENT '默认值',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填字段',
  `validation_rule` varchar(200) DEFAULT NULL COMMENT '验证规则',
  `priority` int(11) DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_ocr_field` (`ocr_field_name`),
  KEY `idx_standard_field` (`standard_field_name`),
  CONSTRAINT `fk_mapping_rule_template` FOREIGN KEY (`template_id`) REFERENCES `fxy_financial_field_mapping_template` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字段映射规则详情表';

-- 模板使用统计表
CREATE TABLE IF NOT EXISTS `fxy_financial_template_usage_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_id` int(11) NOT NULL COMMENT '模板ID',
  `batch_task_id` varchar(50) DEFAULT NULL COMMENT '批量任务ID',
  `detail_id` int(11) DEFAULT NULL COMMENT '明细ID',
  `ocr_data_hash` varchar(64) DEFAULT NULL COMMENT 'OCR数据hash值',
  `mapping_success` tinyint(1) NOT NULL COMMENT '映射是否成功',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `processing_time` int(11) DEFAULT NULL COMMENT '处理时间（毫秒）',
  `account_sets_id` int(11) NOT NULL COMMENT '账套ID',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_batch_task` (`batch_task_id`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_created_time` (`created_time`),
  CONSTRAINT `fk_usage_log_template` FOREIGN KEY (`template_id`) REFERENCES `fxy_financial_field_mapping_template` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板使用统计表';

-- =====================================================
-- 8. 字段映射模板表结构修正
-- =====================================================

-- 修改account_sets_id字段允许NULL，以支持系统级模板
ALTER TABLE `fxy_financial_field_mapping_template`
MODIFY COLUMN `account_sets_id` int(11) DEFAULT NULL COMMENT '账套ID（NULL表示系统级模板）';

-- 修改create_user字段允许NULL，以支持系统创建的模板
ALTER TABLE `fxy_financial_field_mapping_template`
MODIFY COLUMN `create_user` int(11) DEFAULT NULL COMMENT '创建用户ID（NULL表示系统创建）';

-- 修改唯一索引，允许account_sets_id为NULL的情况
ALTER TABLE `fxy_financial_field_mapping_template`
DROP INDEX `uk_template_signature`;

-- 重新创建唯一索引，处理NULL值情况
ALTER TABLE `fxy_financial_field_mapping_template`
ADD UNIQUE KEY `uk_template_signature` (`account_sets_id`, `document_type`, `field_signature`);

-- =====================================================
-- 9. 数据初始化
-- =====================================================

-- 插入默认AI配置（如果不存在）
INSERT IGNORE INTO `fxy_financial_ai_config` (`account_sets_id`, `config_key`, `config_value`, `description`, `is_active`) VALUES
(66, 'deepseek_api_key', '', 'DeepSeek API密钥', 1),
(66, 'deepseek_base_url', 'https://api.deepseek.com', 'DeepSeek API基础URL', 1),
(66, 'ai_model_name', 'deepseek-chat', 'AI模型名称', 1),
(66, 'max_tokens', '2000', '最大token数', 1),
(66, 'temperature', '0.1', '温度参数', 1);

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 执行完成提示
-- =====================================================
SELECT 'AI财务系统数据库更新完成！' as message;
SELECT 'Please restart the backend service to apply entity changes.' as reminder;
