# 票据管理和归并功能技术介绍

## 概述

本文档详细介绍了票据管理和手动归并功能的技术实现，包括数据表结构、OCR识别、字段映射和手动归并功能等核心模块。该功能可以独立部署到其他项目中，专注于发票的OCR识别和用户手动归并操作。

## 1. 核心数据表结构

### 1.1 票据表 (fxy_financial_bill)

```sql
CREATE TABLE `fxy_financial_bill` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bill_no` varchar(50) NOT NULL COMMENT '票据编号',
  `bill_date` date NOT NULL COMMENT '票据日期',
  `type` varchar(50) NOT NULL COMMENT '票据类型（发票/收据/欠条/机票等）',
  `amount` double NOT NULL COMMENT '金额',
  `issuer` varchar(200) DEFAULT NULL COMMENT '开票方/收款方',
  `recipient` varchar(200) DEFAULT NULL COMMENT '收票方/付款方',
  `summary` varchar(200) DEFAULT NULL COMMENT '摘要/用途',
  `remark` text COMMENT '备注',
  `status` varchar(20) DEFAULT '未使用' COMMENT '状态',
  `doc_group_id` varchar(36) DEFAULT NULL COMMENT '票据归并组ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `bill_year` int DEFAULT NULL COMMENT '票据年份',
  `bill_month` int DEFAULT NULL COMMENT '票据月份',
  `invoice_number` varchar(50) DEFAULT NULL COMMENT '发票号码',
  `attachment_path` varchar(500) DEFAULT NULL COMMENT '附件路径',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tax_rate` decimal(5,4) DEFAULT NULL COMMENT '税率',
  `total_tax_amount` decimal(15,2) DEFAULT NULL COMMENT '合计税额',
  `amount_in_words` varchar(200) DEFAULT NULL COMMENT '小写金额',
  `ocr_recognition_info` text COMMENT 'OCR识别的原始信息（JSON格式）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_no_account` (`bill_no`, `account_sets_id`),
  UNIQUE KEY `uk_invoice_number_account_sets` (`invoice_number`, `account_sets_id`),
  KEY `idx_account_sets_date` (`account_sets_id`, `bill_date`),
  KEY `idx_doc_group` (`doc_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据表';
```

### 1.2 票据归并组表 (fxy_financial_document_groups)

```sql
CREATE TABLE `fxy_financial_document_groups` (
  `group_id` varchar(36) NOT NULL COMMENT '组ID，使用UUID',
  `group_name` varchar(200) NOT NULL COMMENT '组名称',
  `merge_rule_id` varchar(36) DEFAULT NULL COMMENT '使用的归并规则ID',
  `rule_params` text COMMENT '规则参数（JSON格式）',
  `group_summary` text COMMENT '组摘要信息',
  `total_amount` decimal(15,2) DEFAULT NULL COMMENT '组内总金额',
  `item_count` int DEFAULT NULL COMMENT '组内项目数量',
  `status` varchar(20) DEFAULT 'ACTIVE' COMMENT '组状态：ACTIVE-活跃，DISSOLVED-已解散',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int DEFAULT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`group_id`),
  KEY `idx_account_sets_id` (`account_sets_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据归并组表';
```

## 2. OCR识别和字段映射机制

### 2.1 OCR识别流程

1. **图片上传**: 支持单张图片上传
2. **OCR调用**: 使用腾讯云OCR API进行发票识别
3. **结果解析**: 将OCR结果解析为结构化数据
4. **字段映射**: 通过配置文件映射转换为标准字段

### 2.2 字段映射方式

#### 配置文件映射（推荐方式）
由于发票字段映射主要针对增值税专用发票和增值税普通发票，使用配置文件方式最为简单：

```yaml
# invoice_field_mapping.yml
invoice_mappings:
  vat_special_invoice:  # 增值税专用发票
    name: "增值税专用发票"
    fields:
      invoice_number: ["发票号码", "Invoice Number"]
      invoice_date: ["开票日期", "Invoice Date"]
      amount: ["价税合计", "Total Amount", "合计金额"]
      tax_amount: ["税额", "Tax Amount"]
      issuer: ["销售方名称", "Seller Name"]
      recipient: ["购买方名称", "Buyer Name"]
      tax_rate: ["税率", "Tax Rate"]

  vat_ordinary_invoice:  # 增值税普通发票
    name: "增值税普通发票"
    fields:
      invoice_number: ["发票号码", "Invoice Number"]
      invoice_date: ["开票日期", "Invoice Date"]
      amount: ["价税合计", "Total Amount", "合计金额"]
      issuer: ["销售方名称", "Seller Name"]
      recipient: ["购买方名称", "Buyer Name"]
```

#### LLM智能映射（备用方式）
- 当配置文件映射失败时，使用LLM进行智能字段映射
- 构建专门的提示词，包含OCR数据和目标字段结构
- 解析LLM响应，提取标准字段值

### 2.3 OCR识别核心代码结构

```java
// OCR服务接口
public interface OcrService {
    Map<String, Object> recognizeVatInvoice(String imageUrl);
    String formatOcrDataToReadableText(Map<String, String> rawData, String type);
    String formatOcrDataToJson(Map<String, String> rawData);
}

// 智能字段映射服务
public interface SmartFieldMappingService {
    Map<String, Object> mapInvoiceFields(Map<String, Object> ocrData, Integer accountSetsId, Integer userId);
}
```

## 3. 手动归并功能

### 3.1 归并方式

**手动归并**: 用户在票据列表页面手动勾选多个票据，输入归并组名称，系统创建归并组

### 3.2 归并服务接口

```java
// 归并服务接口
public interface DocumentMergeService {
    // 手动归并票据
    Object executeManualDocumentMerge(Integer accountSetsId, ManualMergeDto mergeDto, Integer currentUserId);

    // 解散归并组
    boolean unmergeDocumentGroup(Integer accountSetsId, String groupId);

    // 获取归并组列表
    List<DocumentGroup> getDocumentGroups(Integer accountSetsId, int page, int size);
}
```

## 4. 部署到新项目的建议

### 4.1 数据库迁移
1. 执行上述SQL脚本创建票据表和归并组表
2. 根据项目需要调整表前缀（如将`fxy_financial_`改为项目特定前缀）
3. 确保数据库支持UUID字段类型

### 4.2 依赖配置
```xml
<!-- OCR相关依赖 -->
<dependency>
    <groupId>com.tencentcloudapi</groupId>
    <artifactId>tencentcloud-sdk-java</artifactId>
    <version>3.1.423</version>
</dependency>

<!-- JSON处理 -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>fastjson</artifactId>
    <version>1.2.83</version>
</dependency>

<!-- 图片处理 -->
<dependency>
    <groupId>net.coobird</groupId>
    <artifactId>thumbnailator</artifactId>
    <version>0.4.14</version>
</dependency>
```

### 4.3 配置文件设置
```yaml
# application.yml
ocr:
  tencent:
    secret-id: ${TENCENT_SECRET_ID}
    secret-key: ${TENCENT_SECRET_KEY}
    region: ap-beijing

file:
  upload:
    path: /data/uploads/
    max-size: 10MB

ai:
  openai:
    api-url: ${AI_API_URL}
    api-key: ${AI_API_KEY}
    model: gpt-3.5-turbo

# 发票字段映射配置
invoice:
  field-mapping:
    enabled: true
    fallback-to-llm: true
```

### 4.4 字段映射配置文件实现

创建配置文件管理器：

```java
@Component
@ConfigurationProperties(prefix = "invoice.mapping")
public class InvoiceFieldMappingConfig {
    private Map<String, InvoiceMappingRule> mappings;

    public Map<String, Object> mapFields(Map<String, Object> ocrData, String invoiceType) {
        InvoiceMappingRule rule = mappings.get(invoiceType);
        if (rule == null) {
            return ocrData;
        }

        Map<String, Object> result = new HashMap<>();
        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");

        for (Map.Entry<String, List<String>> entry : rule.getFields().entrySet()) {
            String standardField = entry.getKey();
            List<String> possibleFields = entry.getValue();

            for (String field : possibleFields) {
                if (rawData.containsKey(field)) {
                    result.put(standardField, rawData.get(field));
                    break;
                }
            }
        }

        return result;
    }
}
```

## 5. 总结

简化后的票据管理和手动归并功能专注于核心需求，包含了OCR识别、字段映射和用户手动归并的完整流程。

关键优势：
- **简化的架构**: 移除复杂的规则引擎和批量导入，专注核心功能
- **配置文件映射**: 针对发票场景优化的字段映射方式
- **用户友好**: 简单的手动勾选归并操作
- **易于维护**: 减少了复杂的异步任务和状态管理

这种简化的实现更适合专注于发票处理的项目需求。

## 6. 核心实现代码示例

### 6.1 票据实体类

```java
@Data
@TableName(value = "fxy_financial_bill")
public class Bill implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "bill_no")
    private String billNo;

    @TableField(value = "bill_date")
    private Date billDate;

    @TableField(value = "type")
    private String type;

    @TableField(value = "amount")
    private Double amount;

    @TableField(value = "issuer")
    private String issuer;

    @TableField(value = "recipient")
    private String recipient;

    @TableField(value = "summary")
    private String summary;

    @TableField(value = "remark")
    private String remark;

    @TableField(value = "status")
    private String status;

    @TableField(value = "doc_group_id")
    private String docGroupId;

    @TableField(value = "account_sets_id")
    private Integer accountSetsId;

    @TableField(value = "bill_year")
    private Integer billYear;

    @TableField(value = "bill_month")
    private Integer billMonth;

    @TableField(value = "invoice_number")
    private String invoiceNumber;

    @TableField(value = "attachment_path")
    private String attachmentPath;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "tax_rate")
    private BigDecimal taxRate;

    @TableField(value = "total_tax_amount")
    private BigDecimal totalTaxAmount;

    @TableField(value = "amount_in_words")
    private String amountInWords;

    @TableField(value = "ocr_recognition_info")
    private String ocrRecognitionInfo;
}
```

### 6.2 OCR服务实现

```java
@Service
@Slf4j
public class OcrServiceImpl implements OcrService {

    @Value("${ocr.tencent.secret-id}")
    private String secretId;

    @Value("${ocr.tencent.secret-key}")
    private String secretKey;

    @Value("${ocr.tencent.region:ap-beijing}")
    private String region;

    private OcrClient ocrClient;

    @PostConstruct
    public void init() {
        Credential cred = new Credential(secretId, secretKey);
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("ocr.tencentcloudapi.com");

        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);

        ocrClient = new OcrClient(cred, region, clientProfile);
    }

    @Override
    public Map<String, Object> recognizeVatInvoice(String imageUrl) {
        try {
            VatInvoiceOCRRequest req = new VatInvoiceOCRRequest();
            req.setImageUrl(imageUrl);

            VatInvoiceOCRResponse resp = ocrClient.VatInvoiceOCR(req);
            return parseVatInvoiceResult(resp);

        } catch (TencentCloudSDKException e) {
            log.error("增值税发票识别失败", e);
            throw new RuntimeException("发票识别失败: " + e.getMessage());
        }
    }

    private Map<String, Object> parseVatInvoiceResult(VatInvoiceOCRResponse response) {
        Map<String, Object> result = new HashMap<>();
        Map<String, String> rawData = new HashMap<>();

        if (response.getVatInvoiceInfos() != null) {
            for (VatInvoiceInfo info : response.getVatInvoiceInfos()) {
                String name = info.getName();
                String value = info.getValue();
                rawData.put(name, value);

                // 映射到标准字段
                switch (name) {
                    case "发票号码":
                        result.put("invoiceNumber", value);
                        break;
                    case "开票日期":
                        result.put("billDate", parseDate(value));
                        break;
                    case "价税合计":
                        result.put("amount", parseAmount(value));
                        break;
                    case "销售方名称":
                        result.put("issuer", value);
                        break;
                    case "购买方名称":
                        result.put("recipient", value);
                        break;
                    case "税额":
                        result.put("totalTaxAmount", parseAmount(value));
                        break;
                }
            }
        }

        result.put("rawOcrData", rawData);
        result.put("ocrRecognitionInfo", formatOcrDataToReadableText(rawData, "INVOICE"));

        return result;
    }

    @Override
    public String formatOcrDataToReadableText(Map<String, String> rawData, String type) {
        StringBuilder sb = new StringBuilder();
        sb.append("OCR识别结果 (").append(type).append("):\n");

        for (Map.Entry<String, String> entry : rawData.entrySet()) {
            sb.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }

        return sb.toString();
    }

    private Date parseDate(String dateStr) {
        // 实现日期解析逻辑
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
            return sdf.parse(dateStr);
        } catch (Exception e) {
            log.warn("日期解析失败: {}", dateStr);
            return null;
        }
    }

    private Double parseAmount(String amountStr) {
        // 实现金额解析逻辑
        try {
            return Double.parseDouble(amountStr.replaceAll("[^0-9.]", ""));
        } catch (Exception e) {
            log.warn("金额解析失败: {}", amountStr);
            return null;
        }
    }
}
```

### 6.3 配置文件字段映射实现

```java
@Component
@ConfigurationProperties(prefix = "invoice.field-mapping")
@Data
public class InvoiceFieldMappingConfig {

    private Map<String, InvoiceMappingRule> rules = new HashMap<>();

    @PostConstruct
    public void init() {
        // 增值税专用发票映射规则
        InvoiceMappingRule specialRule = new InvoiceMappingRule();
        specialRule.setName("增值税专用发票");
        Map<String, List<String>> specialFields = new HashMap<>();
        specialFields.put("invoiceNumber", Arrays.asList("发票号码", "Invoice Number"));
        specialFields.put("billDate", Arrays.asList("开票日期", "Invoice Date"));
        specialFields.put("amount", Arrays.asList("价税合计", "Total Amount", "合计金额"));
        specialFields.put("totalTaxAmount", Arrays.asList("税额", "Tax Amount"));
        specialFields.put("issuer", Arrays.asList("销售方名称", "Seller Name"));
        specialFields.put("recipient", Arrays.asList("购买方名称", "Buyer Name"));
        specialFields.put("taxRate", Arrays.asList("税率", "Tax Rate"));
        specialRule.setFields(specialFields);
        rules.put("VAT_SPECIAL", specialRule);

        // 增值税普通发票映射规则
        InvoiceMappingRule ordinaryRule = new InvoiceMappingRule();
        ordinaryRule.setName("增值税普通发票");
        Map<String, List<String>> ordinaryFields = new HashMap<>();
        ordinaryFields.put("invoiceNumber", Arrays.asList("发票号码", "Invoice Number"));
        ordinaryFields.put("billDate", Arrays.asList("开票日期", "Invoice Date"));
        ordinaryFields.put("amount", Arrays.asList("价税合计", "Total Amount", "合计金额"));
        ordinaryFields.put("issuer", Arrays.asList("销售方名称", "Seller Name"));
        ordinaryFields.put("recipient", Arrays.asList("购买方名称", "Buyer Name"));
        ordinaryRule.setFields(ordinaryFields);
        rules.put("VAT_ORDINARY", ordinaryRule);
    }

    public Map<String, Object> mapFields(Map<String, Object> ocrData, String invoiceType) {
        InvoiceMappingRule rule = rules.get(invoiceType);
        if (rule == null) {
            log.warn("未找到发票类型 {} 的映射规则", invoiceType);
            return ocrData;
        }

        Map<String, Object> result = new HashMap<>();
        @SuppressWarnings("unchecked")
        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");

        if (rawData == null) {
            return ocrData;
        }

        // 应用映射规则
        for (Map.Entry<String, List<String>> entry : rule.getFields().entrySet()) {
            String standardField = entry.getKey();
            List<String> possibleFields = entry.getValue();

            for (String field : possibleFields) {
                if (rawData.containsKey(field) && rawData.get(field) != null) {
                    Object value = convertFieldValue(standardField, rawData.get(field));
                    result.put(standardField, value);
                    break;
                }
            }
        }

        // 保留原始OCR信息
        result.put("rawOcrData", rawData);
        result.put("ocrRecognitionInfo", ocrData.get("ocrRecognitionInfo"));

        log.info("字段映射完成，映射字段数: {}", result.size());
        return result;
    }

    private Object convertFieldValue(String fieldName, String value) {
        switch (fieldName) {
            case "amount":
            case "totalTaxAmount":
                return parseAmount(value);
            case "billDate":
                return parseDate(value);
            case "taxRate":
                return parseTaxRate(value);
            default:
                return value;
        }
    }

    private Double parseAmount(String amountStr) {
        try {
            return Double.parseDouble(amountStr.replaceAll("[^0-9.]", ""));
        } catch (Exception e) {
            log.warn("金额解析失败: {}", amountStr);
            return null;
        }
    }

    private Date parseDate(String dateStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
            return sdf.parse(dateStr);
        } catch (Exception e) {
            log.warn("日期解析失败: {}", dateStr);
            return null;
        }
    }

    private BigDecimal parseTaxRate(String rateStr) {
        try {
            String cleanRate = rateStr.replaceAll("[^0-9.]", "");
            return new BigDecimal(cleanRate).divide(new BigDecimal("100"));
        } catch (Exception e) {
            log.warn("税率解析失败: {}", rateStr);
            return null;
        }
    }

    @Data
    public static class InvoiceMappingRule {
        private String name;
        private Map<String, List<String>> fields;
    }
}
```

### 6.4 智能字段映射服务

```java
@Service
@Slf4j
public class SmartFieldMappingServiceImpl implements SmartFieldMappingService {

    @Resource
    private InvoiceFieldMappingConfig fieldMappingConfig;

    @Resource
    private AiService aiService;

    @Override
    public Map<String, Object> mapInvoiceFields(Map<String, Object> ocrData, Integer accountSetsId, Integer userId) {
        if (ocrData == null || ocrData.isEmpty()) {
            log.warn("OCR数据为空，无法进行发票字段映射");
            return ocrData;
        }

        // 1. 首先尝试配置文件映射
        String invoiceType = detectInvoiceType(ocrData);
        if (invoiceType != null) {
            Map<String, Object> configResult = fieldMappingConfig.mapFields(ocrData, invoiceType);
            if (configResult != null && !configResult.isEmpty()) {
                log.info("配置文件映射成功，发票类型: {}", invoiceType);
                return configResult;
            }
        }

        // 2. 如果配置文件映射失败，使用LLM映射
        if (aiService != null && aiService.isAvailable()) {
            log.info("配置文件映射失败，使用LLM进行发票字段映射");
            return performLlmMapping(ocrData);
        }

        // 3. 都失败则返回原始数据
        log.warn("所有映射方式都失败，返回原始OCR数据");
        return ocrData;
    }

    private String detectInvoiceType(Map<String, Object> ocrData) {
        @SuppressWarnings("unchecked")
        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");

        if (rawData == null) {
            return null;
        }

        // 根据OCR识别的内容判断发票类型
        for (String key : rawData.keySet()) {
            if (key.contains("专用发票") || key.contains("Special Invoice")) {
                return "VAT_SPECIAL";
            } else if (key.contains("普通发票") || key.contains("Ordinary Invoice")) {
                return "VAT_ORDINARY";
            }
        }

        // 默认返回普通发票
        return "VAT_ORDINARY";
    }

    private Map<String, Object> performLlmMapping(Map<String, Object> ocrData) {
        try {
            String prompt = buildInvoiceFieldMappingPrompt(ocrData);

            Map<String, Object> aiParameters = new HashMap<>();
            aiParameters.put("temperature", 0.1);
            aiParameters.put("max_tokens", 1000);

            String aiResponse = aiService.chat(prompt, aiParameters);

            if (aiResponse != null && !aiResponse.trim().isEmpty()) {
                Map<String, Object> mappedData = parseAiMappingResponse(aiResponse);
                if (mappedData != null && !mappedData.isEmpty()) {
                    log.info("LLM发票字段映射成功，映射字段数: {}", mappedData.size());

                    // 添加原始OCR信息
                    mappedData.put("rawOcrData", ocrData.get("rawOcrData"));
                    mappedData.put("ocrRecognitionInfo", ocrData.get("ocrRecognitionInfo"));

                    return mappedData;
                }
            }
        } catch (Exception e) {
            log.error("LLM字段映射失败", e);
        }

        return ocrData;
    }

    private String buildInvoiceFieldMappingPrompt(Map<String, Object> ocrData) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请将以下OCR识别的发票数据映射到标准字段格式。\n\n");
        prompt.append("OCR识别数据：\n");

        @SuppressWarnings("unchecked")
        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
        if (rawData != null) {
            for (Map.Entry<String, String> entry : rawData.entrySet()) {
                prompt.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }
        }

        prompt.append("\n请映射到以下标准字段（JSON格式）：\n");
        prompt.append("{\n");
        prompt.append("  \"invoiceNumber\": \"发票号码\",\n");
        prompt.append("  \"billDate\": \"开票日期(yyyy-MM-dd格式)\",\n");
        prompt.append("  \"amount\": \"价税合计金额(数字)\",\n");
        prompt.append("  \"totalTaxAmount\": \"税额(数字)\",\n");
        prompt.append("  \"issuer\": \"销售方名称\",\n");
        prompt.append("  \"recipient\": \"购买方名称\",\n");
        prompt.append("  \"taxRate\": \"税率(小数格式，如0.13)\",\n");
        prompt.append("  \"summary\": \"商品或服务描述\"\n");
        prompt.append("}\n\n");
        prompt.append("注意：只返回JSON格式的结果，不要包含其他文字说明。");

        return prompt.toString();
    }

    private Map<String, Object> parseAiMappingResponse(String aiResponse) {
        try {
            // 提取JSON部分
            String jsonStr = aiResponse.trim();
            if (jsonStr.startsWith("```json")) {
                jsonStr = jsonStr.substring(7);
            }
            if (jsonStr.endsWith("```")) {
                jsonStr = jsonStr.substring(0, jsonStr.length() - 3);
            }

            // 解析JSON
            return JSON.parseObject(jsonStr, Map.class);

        } catch (Exception e) {
            log.error("解析AI映射响应失败: {}", aiResponse, e);
            return null;
        }
    }
}
```

### 6.5 手动归并服务实现

```java
@Service
@Slf4j
@Transactional
public class DocumentMergeServiceImpl implements DocumentMergeService {

    @Resource
    private BillMapper billMapper;

    @Resource
    private DocumentGroupMapper documentGroupMapper;

    @Override
    public Object executeManualDocumentMerge(Integer accountSetsId, ManualMergeDto mergeDto, Integer currentUserId) {
        log.info("开始手动票据归并，账套ID: {}, 票据数量: {}", accountSetsId, mergeDto.getBillIds().size());

        // 验证票据存在且属于当前账套
        List<Bill> bills = new ArrayList<>();
        for (Integer billId : mergeDto.getBillIds()) {
            Bill bill = billMapper.selectById(billId);
            if (bill == null || !bill.getAccountSetsId().equals(accountSetsId)) {
                throw new RuntimeException("票据不存在或不属于当前账套: " + billId);
            }
            bills.add(bill);
        }

        if (bills.size() < 2) {
            throw new RuntimeException("至少需要选择2个票据进行归并");
        }

        // 创建归并组
        DocumentGroup group = createDocumentGroup(accountSetsId, mergeDto.getGroupName(), bills, currentUserId);

        // 更新票据的归并组ID
        updateBillsGroupId(bills, group.getGroupId());

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", String.format("手动票据归并成功，创建归并组: %s，归并了%d个票据",
            group.getGroupName(), bills.size()));
        result.put("groupId", group.getGroupId());
        result.put("mergedItemCount", bills.size());
        return result;
    }

    @Override
    public boolean unmergeDocumentGroup(Integer accountSetsId, String groupId) {
        log.info("开始解散票据归并组，账套ID: {}, 组ID: {}", accountSetsId, groupId);

        // 验证归并组存在且属于当前账套
        DocumentGroup group = documentGroupMapper.selectById(groupId);
        if (group == null || !group.getAccountSetsId().equals(accountSetsId)) {
            throw new RuntimeException("归并组不存在或不属于当前账套");
        }

        // 清除票据的归并组ID
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("doc_group_id", groupId);
        List<Bill> bills = billMapper.selectList(queryWrapper);

        for (Bill bill : bills) {
            bill.setDocGroupId(null);
            billMapper.updateById(bill);
        }

        // 删除归并组
        documentGroupMapper.deleteById(groupId);

        log.info("票据归并组解散完成，组ID: {}, 影响票据数量: {}", groupId, bills.size());
        return true;
    }

    @Override
    public List<DocumentGroup> getDocumentGroups(Integer accountSetsId, int page, int size) {
        QueryWrapper<DocumentGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .eq("status", "ACTIVE")
                   .orderByDesc("created_at");

        // 分页查询
        Page<DocumentGroup> pageObj = new Page<>(page, size);
        Page<DocumentGroup> result = documentGroupMapper.selectPage(pageObj, queryWrapper);

        return result.getRecords();
    }

    private DocumentGroup createDocumentGroup(Integer accountSetsId, String groupName, List<Bill> bills, Integer currentUserId) {
        DocumentGroup group = new DocumentGroup();
        group.setGroupId(UUID.randomUUID().toString());
        group.setGroupName(groupName);
        group.setAccountSetsId(accountSetsId);
        group.setCreatedBy(currentUserId);
        group.setCreatedAt(new Date());
        group.setUpdatedAt(new Date());
        group.setStatus("ACTIVE");

        // 计算统计信息
        double totalAmount = bills.stream().mapToDouble(Bill::getAmount).sum();
        group.setTotalAmount(BigDecimal.valueOf(totalAmount));
        group.setItemCount(bills.size());

        documentGroupMapper.insert(group);
        return group;
    }

    private void updateBillsGroupId(List<Bill> bills, String groupId) {
        for (Bill bill : bills) {
            bill.setDocGroupId(groupId);
            billMapper.updateById(bill);
        }
    }

}
```

### 6.6 票据管理控制器

```java
@RestController
@RequestMapping("/bill")
@Slf4j
public class BillController extends BaseController {

    @Resource
    private BillService billService;

    @Resource
    private DocumentMergeService documentMergeService;

    @Resource
    private OcrService ocrService;

    @Resource
    private SmartFieldMappingService smartFieldMappingService;

    /**
     * 单张发票OCR识别
     */
    @PostMapping("/ocr-recognize")
    public JsonResult ocrRecognize(@RequestParam("imageUrl") String imageUrl) {
        try {
            log.info("开始发票OCR识别，图片URL: {}", imageUrl);

            // 1. 调用OCR服务
            Map<String, Object> ocrResult = ocrService.recognizeVatInvoice(imageUrl);

            // 2. 智能字段映射
            if (smartFieldMappingService != null) {
                Map<String, Object> mappedResult = smartFieldMappingService.mapInvoiceFields(
                        ocrResult, this.accountSetsId, this.currentUser.getId());

                if (mappedResult != null && !mappedResult.isEmpty()) {
                    log.info("发票智能映射成功，映射字段数: {}", mappedResult.size());
                    return JsonResult.successful(mappedResult);
                }
            }

            // 3. 返回原始OCR结果
            return JsonResult.successful(ocrResult);

        } catch (Exception e) {
            log.error("发票OCR识别失败", e);
            return JsonResult.failure("发票识别失败: " + e.getMessage());
        }
    }

    /**
     * 保存票据
     */
    @PostMapping("/save")
    public JsonResult saveBill(@RequestBody Bill bill) {
        try {
            // 设置账套ID和创建信息
            bill.setAccountSetsId(this.accountSetsId);
            bill.setCreateTime(new Date());
            bill.setUpdateTime(new Date());

            // 设置年月字段
            if (bill.getBillDate() != null) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(bill.getBillDate());
                bill.setBillYear(cal.get(Calendar.YEAR));
                bill.setBillMonth(cal.get(Calendar.MONTH) + 1);
            }

            billService.save(bill);

            return JsonResult.successful("票据保存成功");

        } catch (Exception e) {
            log.error("保存票据失败", e);
            return JsonResult.failure("保存票据失败: " + e.getMessage());
        }
    }

    /**
     * 手动归并票据
     */
    @PostMapping("/manual-merge")
    public JsonResult manualMerge(@RequestBody ManualMergeDto mergeDto) {
        try {
            Object result = documentMergeService.executeManualDocumentMerge(
                this.accountSetsId, mergeDto, this.currentUser.getId());

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("手动归并失败", e);
            return JsonResult.failure("手动归并失败: " + e.getMessage());
        }
    }

    /**
     * 解散归并组
     */
    @PostMapping("/unmerge/{groupId}")
    public JsonResult unmergeGroup(@PathVariable String groupId) {
        try {
            boolean success = documentMergeService.unmergeDocumentGroup(this.accountSetsId, groupId);

            if (success) {
                return JsonResult.successful("归并组解散成功");
            } else {
                return JsonResult.failure("归并组解散失败");
            }

        } catch (Exception e) {
            log.error("解散归并组失败", e);
            return JsonResult.failure("解散归并组失败: " + e.getMessage());
        }
    }

    /**
     * 获取归并组列表
     */
    @GetMapping("/merge-groups")
    public JsonResult getMergeGroups(@RequestParam(defaultValue = "1") int page,
                                   @RequestParam(defaultValue = "20") int size) {
        try {
            List<DocumentGroup> groups = documentMergeService.getDocumentGroups(this.accountSetsId, page, size);
            return JsonResult.successful(groups);

        } catch (Exception e) {
            log.error("获取归并组列表失败", e);
            return JsonResult.failure("获取归并组列表失败: " + e.getMessage());
        }
    }
}

// 手动归并DTO
@Data
public class ManualMergeDto {
    private List<Integer> billIds;  // 要归并的票据ID列表
    private String groupName;       // 归并组名称
}
```

## 7. 部署配置示例

### 7.1 application.yml 配置

```yaml
# 数据库配置
spring:
  datasource:
    url: *******************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

# OCR配置
ocr:
  tencent:
    secret-id: ${TENCENT_SECRET_ID}
    secret-key: ${TENCENT_SECRET_KEY}
    region: ap-beijing
    rate-limit: 10  # 每秒请求限制

# 文件存储配置
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/data/uploads/}
    max-size: 10MB
    allowed-types: pdf,jpg,jpeg,png,bmp

# AI配置
ai:
  openai:
    api-url: ${AI_API_URL:https://api.openai.com/v1/chat/completions}
    api-key: ${AI_API_KEY}
    model: ${AI_MODEL:gpt-3.5-turbo}
    timeout: 30000

# 发票字段映射配置
invoice:
  field-mapping:
    enabled: true
    fallback-to-llm: true

# 线程池配置
async:
  executor:
    core-pool-size: 5
    max-pool-size: 10
    queue-capacity: 100
    thread-name-prefix: async-batch-

# 日志配置
logging:
  level:
    cn.gson.financial: INFO
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

### 7.2 Maven 依赖配置

```xml
<dependencies>
    <!-- Spring Boot Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- MyBatis Plus -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.2</version>
    </dependency>

    <!-- MySQL驱动 -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <scope>runtime</scope>
    </dependency>

    <!-- 腾讯云OCR SDK -->
    <dependency>
        <groupId>com.tencentcloudapi</groupId>
        <artifactId>tencentcloud-sdk-java</artifactId>
        <version>3.1.423</version>
    </dependency>

    <!-- PDF处理 -->
    <dependency>
        <groupId>org.apache.pdfbox</groupId>
        <artifactId>pdfbox</artifactId>
        <version>2.0.24</version>
    </dependency>

    <!-- JSON处理 -->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>1.2.83</version>
    </dependency>

    <!-- 图片处理 -->
    <dependency>
        <groupId>net.coobird</groupId>
        <artifactId>thumbnailator</artifactId>
        <version>0.4.14</version>
    </dependency>

    <!-- HTTP客户端 -->
    <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
    </dependency>

    <!-- 工具类 -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
    </dependency>

    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <optional>true</optional>
    </dependency>
</dependencies>
```

## 8. 总结和最佳实践

### 8.1 核心优势
1. **简化的架构**: 专注于发票OCR识别和手动归并
2. **配置文件映射**: 针对发票场景优化的字段映射方式
3. **用户友好**: 简单的手动勾选归并操作
4. **易于维护**: 减少了复杂的规则引擎和批量任务
5. **模块化设计**: 易于集成到其他项目

### 8.2 部署建议
1. **数据库优化**: 为票据表的查询字段添加索引
2. **文件存储**: 使用对象存储服务存储发票图片
3. **OCR限流**: 合理控制OCR API调用频率
4. **安全考虑**: 文件上传安全检查和访问权限控制
5. **错误处理**: 完善的异常处理和用户提示

### 8.3 使用流程
1. **上传发票图片**: 用户上传发票图片文件
2. **OCR识别**: 系统调用腾讯云OCR进行识别
3. **字段映射**: 通过配置文件将OCR结果映射为标准字段
4. **数据确认**: 用户确认和修改识别结果
5. **保存票据**: 将票据信息保存到数据库
6. **手动归并**: 用户在列表页勾选多个票据进行归并
7. **归并管理**: 查看和管理已创建的归并组

### 8.4 扩展建议
1. **支持更多发票类型**: 扩展配置文件支持更多发票格式
2. **移动端支持**: 提供移动端拍照识别功能
3. **数据统计**: 添加票据数据的统计分析功能
4. **导出功能**: 支持票据数据的Excel导出

通过这种简化的实现方式，可以快速在新项目中部署票据管理和手动归并功能，满足基本的发票处理需求。
```
