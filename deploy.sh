#!/bin/bash

# 自动化部署脚本
# 功能：本地编译打包，上传服务器更新

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 加载配置文件
CONFIG_FILE="deploy.config"
if [ -f "$CONFIG_FILE" ]; then
    # shellcheck source=deploy.config
    source "$CONFIG_FILE"
    log_info "已加载配置文件: $CONFIG_FILE"
else
    log_warning "配置文件 $CONFIG_FILE 不存在，使用默认配置"
fi

# 配置参数（带默认值）
PROJECT_NAME="aifinancial"
VERSION=$(date +%Y%m%d_%H%M%S)
BUILD_DIR="./build"

# 服务器配置（如果配置文件中没有定义，使用默认值）
SERVER_HOST="${SERVER_HOST:-your-server.com}"
SERVER_USER="${SERVER_USER:-root}"
SERVER_PORT="${SERVER_PORT:-22}"
SERVER_PATH="${SERVER_PATH:-/opt/1panel/www/sites/fin.aiform.com/index}"
ADMIN_SERVER_PATH="${ADMIN_SERVER_PATH:-/opt/1panel/www/sites/finadmin.aiform.com/index}"
MAIN_DOMAIN="${MAIN_DOMAIN:-https://fin.aiform.com}"
ADMIN_DOMAIN="${ADMIN_DOMAIN:-https://finadmin.aiform.com}"
BACKEND_SERVICE_NAME="${BACKEND_SERVICE_NAME:-aifinancial-backend}"

# 数据库配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_NAME="${DB_NAME:-aifinancial}"
DB_USER="${DB_USER:-root}"
DB_PASSWORD="${DB_PASSWORD}"

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 Java
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装或未在 PATH 中"
        exit 1
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装或未在 PATH 中"
        exit 1
    fi
    
    # 检查 yarn
    if ! command -v yarn &> /dev/null; then
        log_error "yarn 未安装或未在 PATH 中"
        exit 1
    fi
    
    # 检查 scp
    if ! command -v scp &> /dev/null; then
        log_error "scp 命令不可用，请安装 openssh-client"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 清理构建目录
clean_build() {
    log_info "清理构建目录..."
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    mkdir -p "$BUILD_DIR"
    log_success "构建目录已清理"
}

# 构建前端项目
build_frontend() {
    log_info "开始构建前端项目..."
    
    # 构建主应用前端（对应生产环境 fin.aiform.com/index）
    if [ -d "front-end" ]; then
        log_info "构建主应用前端..."
        cd front-end

        # 安装依赖
        yarn install --frozen-lockfile

        # 构建
        yarn build

        cd ..
        
        # 按生产环境结构复制文件: /opt/1panel/www/sites/fin.aiform.com/index
        mkdir -p "$BUILD_DIR/fin.aiform.com/index/dist"
        cp -r front-end/dist/* "$BUILD_DIR/fin.aiform.com/index/dist/"
        log_success "主应用前端构建完成"
    else
        log_warning "front-end 目录不存在，跳过主应用前端构建"
    fi
    
    # 构建管理员前端（对应生产环境 finadmin.aiform.com/index）
    if [ -d "admin-frontend" ]; then
        log_info "构建管理员前端..."
        cd admin-frontend
        
        # 安装依赖
        yarn install --frozen-lockfile
        
        # 构建
        yarn build
        
        cd ..
        
        # 按生产环境结构复制文件: /opt/1panel/www/sites/finadmin.aiform.com/index
        mkdir -p "$BUILD_DIR/finadmin.aiform.com/index/dist"
        cp -r admin-frontend/dist/* "$BUILD_DIR/finadmin.aiform.com/index/dist/"
        log_success "管理员前端构建完成"
    else
        log_warning "admin-frontend 目录不存在，跳过管理员前端构建"
    fi
}

# 选择性构建
build_selective() {
    local component="$1"
    
    case "$component" in
        "frontend"|"front")
            log_info "仅构建前端项目..."
            build_frontend
            ;;
        "backend"|"back")
            log_info "仅构建后端项目..."
            build_backend
            ;;
        "admin")
            log_info "仅构建管理员前端..."
            build_admin_only
            ;;
        "main")
            log_info "仅构建主应用前端..."
            build_main_frontend_only
            ;;
        "all"|"")
            log_info "构建所有组件..."
            build_frontend
            build_backend
            ;;
        *)
            log_error "未知构建组件: $component"
            log_info "支持的组件: frontend|backend|admin|main|all"
            exit 1
            ;;
    esac
}

# 仅构建主应用前端
build_main_frontend_only() {
    log_info "开始构建主应用前端..."
    
    if [ -d "front-end" ]; then
        log_info "构建主应用前端..."
        cd front-end

        # 安装依赖
        yarn install --frozen-lockfile

        # 构建
        yarn build

        cd ..
        
        # 按生产环境结构复制文件: /opt/1panel/www/sites/fin.aiform.com/index
        mkdir -p "$BUILD_DIR/fin.aiform.com/index/dist"
        cp -r front-end/dist/* "$BUILD_DIR/fin.aiform.com/index/dist/"
        log_success "主应用前端构建完成"
    else
        log_warning "front-end 目录不存在，跳过主应用前端构建"
    fi
}

# 仅构建管理员前端
build_admin_only() {
    log_info "开始构建管理员前端..."
    
    if [ -d "admin-frontend" ]; then
        log_info "构建管理员前端..."
        cd admin-frontend
        
        # 安装依赖
        yarn install --frozen-lockfile
        
        # 构建
        yarn build
        
        cd ..
        
        # 按生产环境结构复制文件: /opt/1panel/www/sites/finadmin.aiform.com/index
        mkdir -p "$BUILD_DIR/finadmin.aiform.com/index/dist"
        cp -r admin-frontend/dist/* "$BUILD_DIR/finadmin.aiform.com/index/dist/"
        log_success "管理员前端构建完成"
    else
        log_warning "admin-frontend 目录不存在，跳过管理员前端构建"
    fi
}

# 构建后端项目
build_backend() {
    log_info "开始构建后端项目..."
    
    # 设置 Java 1.8 环境
    export JAVA_HOME=/Users/<USER>/Library/Java/JavaVirtualMachines/azul-1.8.0_452/Contents/Home
    
    # 编译后端服务
    ./gradlew bs-server:bootJar
    
    # 复制后端 JAR 文件
    mkdir -p "$BUILD_DIR/backend"
    cp bs-server/build/libs/*.jar "$BUILD_DIR/backend/"
    
    # 如果存在配置文件，也复制
    if [ -f "bs-server/src/main/resources/application.yml" ]; then
        cp bs-server/src/main/resources/application.yml "$BUILD_DIR/backend/"
    fi
    
    log_success "后端项目构建完成"
}


# 创建部署包
create_package() {
    log_info "创建部署包..."
    
    cd "$BUILD_DIR"
    
    # 创建压缩包
    tar -czf "${PROJECT_NAME}-${VERSION}.tar.gz" ./*
    
    cd ..
    
    log_success "部署包创建完成: ${BUILD_DIR}/${PROJECT_NAME}-${VERSION}.tar.gz"
}

# 数据库迁移相关函数
check_database_connection() {
    log_info "检查数据库连接..."
    
    if [ -z "$DB_PASSWORD" ]; then
        log_error "数据库密码未设置，请在配置文件中设置 DB_PASSWORD"
        return 1
    fi
    
    # 检查 mysql 客户端
    if ! command -v mysql > /dev/null; then
        log_error "mysql 客户端未安装"
        return 1
    fi
    
    # 测试数据库连接
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1; then
        log_success "数据库连接成功"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

backup_database() {
    log_info "备份数据库..."
    
    local backup_file="database_backup_${VERSION}.sql"
    
    if mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" --single-transaction --routines --triggers "$DB_NAME" > "${BUILD_DIR}/${backup_file}"; then
        log_success "数据库备份完成: ${BUILD_DIR}/${backup_file}"
    else
        log_error "数据库备份失败"
        return 1
    fi
}

execute_migration_script() {
    local script_file="$1"
    local script_name="$2"
    
    if [ ! -f "$script_file" ]; then
        log_warning "迁移脚本不存在: $script_file"
        return 0
    fi
    
    log_info "执行数据库迁移: $script_name"
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$script_file"; then
        log_success "迁移脚本执行成功: $script_name"
    else
        log_error "迁移脚本执行失败: $script_name"
        return 1
    fi
}

migrate_database() {
    log_info "开始数据库迁移..."
    
    # 检查数据库连接
    if ! check_database_connection; then
        return 1
    fi
    
    # 备份数据库
    backup_database
    
    # 执行迁移脚本（按顺序执行）
    local migration_dir="doc/database"
    
    # 1. 基础表结构更新
    if [ -f "${migration_dir}/aiform_update.sql" ]; then
        execute_migration_script "${migration_dir}/aiform_update.sql" "AI财务系统完整更新"
    fi
    
    # 2. 管理员模块
    if [ -f "${migration_dir}/admin_module.sql" ]; then
        execute_migration_script "${migration_dir}/admin_module.sql" "管理员模块"
    fi
    
    # 3. 字段映射模板数据
    if [ -f "${migration_dir}/field_mapping_templates_export.sql" ]; then
        execute_migration_script "${migration_dir}/field_mapping_templates_export.sql" "字段映射模板数据"
    fi
    
    # 4. 默认管理员数据
    if [ -f "${migration_dir}/insert_default_admin.sql" ]; then
        execute_migration_script "${migration_dir}/insert_default_admin.sql" "默认管理员数据"
    fi
    
    # 5. 特定迁移脚本
    if [ -f "${migration_dir}/migration_add_user_id_to_ai_config.sql" ]; then
        execute_migration_script "${migration_dir}/migration_add_user_id_to_ai_config.sql" "AI配置表用户ID字段迁移"
    fi
    
    # 6. OCR信息字段
    if [ -f "${migration_dir}/alter_add_ocr_info_fields_20250107.sql" ]; then
        execute_migration_script "${migration_dir}/alter_add_ocr_info_fields_20250107.sql" "OCR信息字段添加"
    fi
    
    log_success "数据库迁移完成"
}

rollback_database() {
    local backup_version="$1"
    
    if [ -z "$backup_version" ]; then
        log_error "请指定要回滚的备份版本"
        return 1
    fi
    
    local backup_file="database_backup_${backup_version}.sql"
    
    if [ ! -f "${BUILD_DIR}/${backup_file}" ]; then
        log_error "备份文件不存在: ${backup_file}"
        return 1
    fi
    
    log_info "回滚数据库到版本: $backup_version"
    
    # 确认操作
    read -p "⚠️  这将完全替换当前数据库内容，确认继续吗？(yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        log_info "数据库回滚已取消"
        return 0
    fi
    
    # 删除当前数据库并重新创建
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "DROP DATABASE IF EXISTS ${DB_NAME}; CREATE DATABASE ${DB_NAME};"
    
    # 恢复备份
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "${BUILD_DIR}/${backup_file}"; then
        log_success "数据库回滚完成"
    else
        log_error "数据库回滚失败"
        return 1
    fi
}

# 上传文件到服务器
upload_to_server() {
    log_info "上传文件到服务器..."
    
    # 检查服务器连接
    if ! ssh -o ConnectTimeout=10 "$SERVER_USER@$SERVER_HOST" "echo '服务器连接正常'" 2>/dev/null; then
        log_error "无法连接到服务器 $SERVER_HOST"
        log_info "请检查："
        log_info "1. 服务器地址是否正确"
        log_info "2. SSH 密钥是否配置正确"
        log_info "3. 网络连接是否正常"
        exit 1
    fi
    
    # 上传部署包
    scp "${BUILD_DIR}/${PROJECT_NAME}-${VERSION}.tar.gz" "$SERVER_USER@$SERVER_HOST:/tmp/"
    
    # 在服务器上解压并部署
    ssh "$SERVER_USER@$SERVER_HOST" "
        # 创建备份
        if [ -d '$SERVER_PATH' ]; then
            cp -r '$SERVER_PATH' '${SERVER_PATH}.backup.$VERSION'
        fi
        
        if [ -d '$ADMIN_SERVER_PATH' ]; then
            cp -r '$ADMIN_SERVER_PATH' '${ADMIN_SERVER_PATH}.backup.$VERSION'
        fi
        
        # 解压新版本
        cd /tmp
        tar -xzf '${PROJECT_NAME}-${VERSION}.tar.gz'
        
        # 部署主应用前端
        if [ -d 'main-frontend' ]; then
            mkdir -p '$SERVER_PATH/dist'
            rm -rf '$SERVER_PATH/dist/*'
            cp -r main-frontend/* '$SERVER_PATH/dist/'
            chown -R www-data:www-data '$SERVER_PATH/dist'
            chmod -R 755 '$SERVER_PATH/dist'
        fi
        
        # 部署管理员前端
        if [ -d 'admin-frontend' ]; then
            mkdir -p '$ADMIN_SERVER_PATH/dist'
            rm -rf '$ADMIN_SERVER_PATH/dist/*'
            cp -r admin-frontend/* '$ADMIN_SERVER_PATH/dist/'
            chown -R www-data:www-data '$ADMIN_SERVER_PATH/dist'
            chmod -R 755 '$ADMIN_SERVER_PATH/dist'
        fi
        
        # 部署后端
        if [ -d 'backend' ]; then
            mkdir -p '$SERVER_PATH/backend'
            cp backend/*.jar '$SERVER_PATH/backend/'
            if [ -f 'backend/application.yml' ]; then
                cp backend/application.yml '$SERVER_PATH/backend/'
            fi
            chown -R www-data:www-data '$SERVER_PATH/backend'
            chmod +x '$SERVER_PATH/backend'/*.jar
        fi
        
        # 清理临时文件
        rm -rf /tmp/${PROJECT_NAME}-${VERSION}.tar.gz
        rm -rf /tmp/main-frontend /tmp/admin-frontend /tmp/backend
        
        echo '部署完成'
    "
    
    log_success "文件上传并部署完成"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        # 重启 nginx
        if command -v nginx &> /dev/null; then
            nginx -t && systemctl reload nginx
            echo 'Nginx 已重新加载'
        fi
        
        # 重启后端服务（如果使用 systemd）
        if systemctl is-active --quiet aifinancial-backend; then
            systemctl restart aifinancial-backend
            echo '后端服务已重启'
        else
            echo '后端服务未配置为 systemd 服务，请手动重启'
        fi
        
        # 等待服务启动
        sleep 5
    "
    
    log_success "服务重启完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查主应用
    if curl -s -o /dev/null -w "%{http_code}" "$MAIN_DOMAIN/" | grep -q "200"; then
        log_success "主应用健康检查通过 ($MAIN_DOMAIN)"
    else
        log_warning "主应用健康检查失败 ($MAIN_DOMAIN)"
    fi
    
    # 检查管理员页面
    if curl -s -o /dev/null -w "%{http_code}" "$ADMIN_DOMAIN/" | grep -q "200"; then
        log_success "管理员页面健康检查通过 ($ADMIN_DOMAIN)"
    else
        log_warning "管理员页面健康检查失败 ($ADMIN_DOMAIN)"
    fi
    
    # 检查后端 API
    if curl -s -o /dev/null -w "%{http_code}" "$MAIN_DOMAIN/api/actuator/health" | grep -q "200"; then
        log_success "后端 API 健康检查通过"
    else
        log_warning "后端 API 健康检查失败"
    fi
}

# 回滚功能
rollback() {
    local backup_version="$1"
    if [ -z "$backup_version" ]; then
        log_error "请指定要回滚的备份版本"
        exit 1
    fi
    
    log_info "回滚到版本: $backup_version"
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        # 回滚主应用
        if [ -d '${SERVER_PATH}.backup.$backup_version' ]; then
            rm -rf '$SERVER_PATH'
            cp -r '${SERVER_PATH}.backup.$backup_version' '$SERVER_PATH'
        fi
        
        # 回滚管理员页面
        if [ -d '${ADMIN_SERVER_PATH}.backup.$backup_version' ]; then
            rm -rf '$ADMIN_SERVER_PATH'
            cp -r '${ADMIN_SERVER_PATH}.backup.$backup_version' '$ADMIN_SERVER_PATH'
        fi
        
        # 重启服务
        nginx -t && systemctl reload nginx
        
        echo '回滚完成'
    "
    
    log_success "回滚完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  deploy              执行完整部署流程（包含数据库迁移）"
    echo "  build [COMPONENT]   本地构建（可选择组件：frontend|backend|admin|main|all）"
    echo "  upload              仅上传到服务器"
    echo "  migrate-db          仅执行数据库迁移"
    echo "  rollback-db VERSION 回滚数据库到指定版本"
    echo "  restart             仅重启服务"
    echo "  health-check        仅执行健康检查"
    echo "  rollback VERSION    回滚应用到指定版本"
    echo "  help                显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 deploy                      # 执行完整部署（含数据库迁移）"
    echo "  $0 build                       # 构建所有组件"
    echo "  $0 build backend               # 仅构建后端"
    echo "  $0 build frontend              # 仅构建前端（包含主应用和管理员）"
    echo "  $0 build admin                 # 仅构建管理员前端"
    echo "  $0 build main                  # 仅构建主应用前端"
    echo "  $0 migrate-db                  # 仅执行数据库迁移"
    echo "  $0 rollback-db 20250119_122000 # 回滚数据库到指定版本"
    echo "  $0 rollback 20240717_143000    # 回滚应用到指定版本"
    echo ""
    echo "数据库配置（通过环境变量或deploy.config文件设置）:"
    echo "  DB_HOST      数据库主机地址 (默认: localhost)"
    echo "  DB_PORT      数据库端口 (默认: 3306)"
    echo "  DB_NAME      数据库名称 (默认: aifinancial)"
    echo "  DB_USER      数据库用户名 (默认: root)"
    echo "  DB_PASSWORD  数据库密码 (必须设置)"
}

# 主函数
main() {
    local command="${1:-deploy}"
    
    case "$command" in
        "deploy")
            log_info "开始执行完整部署流程..."
            check_dependencies
            clean_build
            build_frontend
            build_backend
            create_package
            migrate_database
            upload_to_server
            restart_services
            health_check
            log_success "部署流程完成！"
            ;;
        "build")
            log_info "开始执行本地构建..."
            check_dependencies
            clean_build
            build_selective "$2"
            create_package
            log_success "本地构建完成！"
            ;;
        "upload")
            log_info "开始上传到服务器..."
            upload_to_server
            restart_services
            log_success "上传完成！"
            ;;
        "restart")
            restart_services
            ;;
        "health-check")
            health_check
            ;;
        "rollback")
            rollback "$2"
            ;;
        "migrate-db")
            log_info "开始执行数据库迁移..."
            migrate_database
            ;;
        "rollback-db")
            log_info "开始执行数据库回滚..."
            rollback_database "$2"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
