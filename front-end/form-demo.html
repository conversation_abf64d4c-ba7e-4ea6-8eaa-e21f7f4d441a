<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的表单布局演示</title>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background: #f3f6f8;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }

        /* 专业表单布局样式 */
        .professional-form {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .professional-form .form-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e2e8f0;
        }

        /* 表单分组样式 */
        .form-section {
            margin-bottom: 32px;
        }

        .form-section .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
            padding: 8px 0;
            border-left: 4px solid #3788ee;
            padding-left: 12px;
            background: #f7fafc;
            margin-left: -12px;
            padding-right: 12px;
        }

        .form-section .section-content {
            display: grid;
            gap: 20px 24px;
        }

        /* 网格布局 */
        .section-content.grid-2 {
            grid-template-columns: 1fr 1fr;
        }

        .section-content.grid-3 {
            grid-template-columns: 1fr 1fr 1fr;
        }

        .section-content.grid-4 {
            grid-template-columns: 1fr 1fr 1fr 1fr;
        }

        .section-content.grid-full {
            grid-template-columns: 1fr;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .section-content.grid-4 {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .section-content.grid-2,
            .section-content.grid-3,
            .section-content.grid-4 {
                grid-template-columns: 1fr;
            }
            .section-content {
                gap: 16px;
            }
        }

        /* 表单项样式 */
        .form-item {
            margin-bottom: 0;
        }

        .form-item .form-label {
            display: block;
            font-weight: 500;
            color: #4a5568;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 6px;
        }

        .form-item .form-label.required::after {
            content: " *";
            color: #e53e3e;
            font-weight: bold;
        }

        .form-item input,
        .form-item textarea,
        .form-item select {
            width: 100%;
            height: 40px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 0 12px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: #ffffff;
            box-sizing: border-box;
        }

        .form-item input:focus,
        .form-item textarea:focus,
        .form-item select:focus {
            outline: none;
            border-color: #3788ee;
            box-shadow: 0 0 0 3px rgba(55, 136, 238, 0.1);
        }

        .form-item input:hover,
        .form-item textarea:hover,
        .form-item select:hover {
            border-color: #9ca3af;
        }

        .form-item input[readonly],
        .form-item textarea[readonly] {
            background: #f9fafb;
            color: #6b7280;
            cursor: not-allowed;
        }

        .form-item textarea {
            height: 80px;
            padding: 12px;
            resize: vertical;
            line-height: 1.5;
        }

        /* 金额输入框特殊样式 */
        .amount-input {
            position: relative;
        }

        .amount-input .currency-symbol {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-weight: 500;
            z-index: 1;
        }

        .amount-input input {
            padding-left: 32px;
        }

        /* 文件上传区域样式 */
        .file-upload-section {
            margin-bottom: 24px;
            padding: 20px;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            background: #f9fafb;
            text-align: center;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .file-upload-section:hover {
            border-color: #3788ee;
            background: #f0f9ff;
        }

        .file-upload-section .upload-icon {
            font-size: 48px;
            color: #9ca3af;
            margin-bottom: 12px;
        }

        .file-upload-section .upload-text {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 8px;
        }

        .file-upload-section .upload-hint {
            font-size: 14px;
            color: #6b7280;
        }

        /* 按钮组样式 */
        .form-actions {
            margin-top: 40px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
        }

        .btn {
            min-width: 100px;
            height: 40px;
            margin: 0 8px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: 1px solid #d1d5db;
            background: #ffffff;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background: #3788ee;
            border-color: #3788ee;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(55, 136, 238, 0.3);
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* 特殊字段样式 */
        .readonly-field input {
            background: #f8fafc !important;
            color: #64748b !important;
            font-weight: 500;
        }

        .important-field .form-label {
            color: #3788ee;
            font-weight: 600;
        }

        /* 对比展示 */
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .comparison h3 {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 6px;
        }

        .old-style h3 {
            background: #fee2e2;
            color: #991b1b;
        }

        .new-style h3 {
            background: #dcfce7;
            color: #166534;
        }

        .old-form {
            background: #ffffff;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }

        .old-form .form-item {
            margin-bottom: 20px;
        }

        .old-form .form-label {
            display: inline-block;
            width: 110px;
            text-align: right;
            margin-right: 10px;
            vertical-align: top;
            padding-top: 8px;
        }

        .old-form input,
        .old-form textarea {
            width: calc(100% - 130px);
            display: inline-block;
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div style="text-align: center; margin-bottom: 40px;">
        <h1 style="color: #1a202c; margin-bottom: 10px;">表单布局优化对比</h1>
        <p style="color: #6b7280; font-size: 16px;">新增票据和新增银行回单页面的表单优化效果展示</p>
    </div>

    <div class="comparison">
        <!-- 旧版样式 -->
        <div class="old-style">
            <h3>优化前 - 传统单列布局</h3>
            <div class="old-form">
                <div class="form-item">
                    <label class="form-label">票据编号：</label>
                    <input type="text" value="PJ202501280001" readonly>
                </div>
                <div class="form-item">
                    <label class="form-label">发票号码：</label>
                    <input type="text" placeholder="请输入发票号码">
                </div>
                <div class="form-item">
                    <label class="form-label">开票日期：</label>
                    <input type="date" value="2025-01-28">
                </div>
                <div class="form-item">
                    <label class="form-label">开票方：</label>
                    <input type="text" placeholder="请输入开票方">
                </div>
                <div class="form-item">
                    <label class="form-label">收票方：</label>
                    <input type="text" placeholder="请输入收票方">
                </div>
                <div class="form-item">
                    <label class="form-label">合计金额：</label>
                    <input type="number" placeholder="0.00">
                </div>
                <div class="form-item">
                    <label class="form-label">税率：</label>
                    <input type="number" placeholder="请输入税率(%)">
                </div>
                <div class="form-item">
                    <label class="form-label">摘要：</label>
                    <textarea placeholder="请输入摘要"></textarea>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary">保存</button>
                    <button class="btn">返回</button>
                </div>
            </div>
        </div>

        <!-- 新版样式 -->
        <div class="new-style">
            <h3>优化后 - 专业分组布局</h3>
            <div class="professional-form">
                <div class="form-title">新增票据</div>
                
                <!-- 文件上传区域 -->
                <div class="file-upload-section">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">点击上传文件</div>
                    <div class="upload-hint">支持PDF、图片格式</div>
                </div>

                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-title">基本信息</div>
                    <div class="section-content grid-3">
                        <div class="form-item readonly-field">
                            <label class="form-label">票据编号</label>
                            <input type="text" value="PJ202501280001" readonly>
                        </div>
                        <div class="form-item">
                            <label class="form-label">发票号码</label>
                            <input type="text" placeholder="请输入发票号码">
                        </div>
                        <div class="form-item important-field">
                            <label class="form-label required">开票日期</label>
                            <input type="date" value="2025-01-28">
                        </div>
                    </div>
                </div>

                <!-- 主体信息 -->
                <div class="form-section">
                    <div class="section-title">主体信息</div>
                    <div class="section-content grid-2">
                        <div class="form-item important-field">
                            <label class="form-label required">开票方</label>
                            <input type="text" placeholder="请输入开票方">
                        </div>
                        <div class="form-item">
                            <label class="form-label">收票方</label>
                            <input type="text" placeholder="请输入收票方">
                        </div>
                    </div>
                </div>

                <!-- 金额信息 -->
                <div class="form-section">
                    <div class="section-title">金额信息</div>
                    <div class="section-content grid-2">
                        <div class="form-item important-field">
                            <label class="form-label required">合计金额</label>
                            <div class="amount-input">
                                <span class="currency-symbol">¥</span>
                                <input type="number" placeholder="0.00" step="0.01">
                            </div>
                        </div>
                        <div class="form-item">
                            <label class="form-label">税率</label>
                            <input type="number" placeholder="请输入税率(%)" step="0.01">
                        </div>
                    </div>
                </div>

                <!-- 详细描述 -->
                <div class="form-section">
                    <div class="section-title">详细描述</div>
                    <div class="section-content grid-full">
                        <div class="form-item important-field">
                            <label class="form-label required">摘要</label>
                            <textarea placeholder="请输入摘要"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button class="btn btn-primary">保存</button>
                    <button class="btn">返回</button>
                </div>
            </div>
        </div>
    </div>

    <div style="background: #ffffff; padding: 24px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); margin-top: 40px;">
        <h2 style="color: #1a202c; margin-bottom: 20px;">优化亮点</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            <div style="padding: 16px; background: #f0f9ff; border-radius: 6px; border-left: 4px solid #3788ee;">
                <h4 style="color: #1e40af; margin: 0 0 8px 0;">📱 响应式布局</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">采用CSS Grid布局，自动适配不同屏幕尺寸，移动端友好</p>
            </div>
            <div style="padding: 16px; background: #f0fdf4; border-radius: 6px; border-left: 4px solid #10b981;">
                <h4 style="color: #166534; margin: 0 0 8px 0;">🎯 逻辑分组</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">相关字段按功能分组，提高填写效率和用户体验</p>
            </div>
            <div style="padding: 16px; background: #fef3c7; border-radius: 6px; border-left: 4px solid #f59e0b;">
                <h4 style="color: #92400e; margin: 0 0 8px 0;">✨ 视觉层次</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">清晰的标题层次和视觉分隔，重要字段突出显示</p>
            </div>
            <div style="padding: 16px; background: #fce7f3; border-radius: 6px; border-left: 4px solid #ec4899;">
                <h4 style="color: #be185d; margin: 0 0 8px 0;">🎨 专业美观</h4>
                <p style="color: #64748b; margin: 0; font-size: 14px;">现代化的设计风格，统一的色彩搭配和交互效果</p>
            </div>
        </div>
    </div>
</body>
</html>
