<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FileUpload组件测试</title>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background: #f3f6f8;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .test-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e2e8f0;
        }

        /* 模拟FileUpload组件的样式 */
        .file-upload-wrapper {
            margin-bottom: 24px;
        }

        .form-item {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 12px;
            padding: 8px 0;
            border-left: 4px solid #3788ee;
            padding-left: 12px;
            background: #f7fafc;
            margin-left: -12px;
            padding-right: 12px;
        }

        .file-upload-container {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 20px;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            background: #f9fafb;
            transition: all 0.2s ease;
        }

        .file-upload-container:hover {
            border-color: #3788ee;
            background: #f0f9ff;
        }

        .upload-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #3788ee;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .upload-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(55, 136, 238, 0.3);
        }

        .upload-tip {
            color: #6b7280;
            font-size: 14px;
            margin-left: 12px;
        }

        .uploaded-file {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 1px solid #10b981;
            border-radius: 8px;
            margin-top: 8px;
            background: #f0fdf4;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #f3f4f6;
        }

        .action-btn.primary {
            background: #3788ee;
            border-color: #3788ee;
            color: white;
        }

        .action-btn.danger {
            background: #ef4444;
            border-color: #ef4444;
            color: white;
        }

        .upload-progress {
            margin-top: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: #3788ee;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 12px;
            color: #666;
            margin-left: 8px;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-top: 40px;
        }

        .comparison-item {
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }

        .comparison-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
            padding: 8px;
            border-radius: 6px;
        }

        .before .comparison-title {
            background: #fee2e2;
            color: #991b1b;
        }

        .after .comparison-title {
            background: #dcfce7;
            color: #166534;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }

        .status-working {
            background: #dcfce7;
            color: #166534;
        }

        .status-issue {
            background: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-title">FileUpload组件修复验证</div>
        
        <div class="comparison">
            <div class="comparison-item before">
                <div class="comparison-title">修复前的问题</div>
                <ul style="color: #6b7280; line-height: 1.8;">
                    <li>文件上传组件被额外的div包装影响显示</li>
                    <li>组件的原有样式被覆盖</li>
                    <li>上传、预览、识别功能可能受影响</li>
                    <li>组件内部的FormItem结构被破坏</li>
                </ul>
            </div>
            
            <div class="comparison-item after">
                <div class="comparison-title">修复后的改进</div>
                <ul style="color: #6b7280; line-height: 1.8;">
                    <li>移除了干扰的包装div</li>
                    <li>保持组件原有的完整功能</li>
                    <li>添加了适配的专业样式</li>
                    <li>确保上传、预览、识别功能正常</li>
                </ul>
            </div>
        </div>

        <div style="margin-top: 40px;">
            <h3 style="color: #1a202c; margin-bottom: 20px;">模拟修复后的FileUpload组件效果</h3>
            
            <!-- 模拟FileUpload组件 -->
            <div class="file-upload-wrapper">
                <div class="form-item">
                    <label class="form-label">附件上传</label>
                    <div class="file-upload-container">
                        <button class="upload-btn" onclick="simulateUpload()">
                            📁 选择文件
                        </button>
                        <span class="upload-tip">支持PDF、图片格式，最大10MB</span>
                    </div>
                    
                    <!-- 模拟已上传文件 -->
                    <div class="uploaded-file" id="uploadedFile" style="display: none;">
                        <div class="file-info">
                            <span>📄</span>
                            <span id="fileName">invoice_sample.pdf</span>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn primary" onclick="simulatePreview()">预览</button>
                            <button class="action-btn primary" onclick="simulateRecognition()">识别</button>
                            <button class="action-btn danger" onclick="simulateRemove()">删除</button>
                        </div>
                    </div>
                    
                    <!-- 模拟上传进度 -->
                    <div class="upload-progress" id="uploadProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text" id="progressText">0%</span>
                    </div>
                </div>
            </div>
        </div>

        <div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border-left: 4px solid #3788ee; margin-top: 32px;">
            <h4 style="color: #1e40af; margin: 0 0 8px 0;">✅ 修复状态</h4>
            <p style="color: #64748b; margin: 0; font-size: 14px;">
                FileUpload组件现在应该能够正常显示和工作，包括文件上传、预览、OCR识别等所有功能。
                组件保持了原有的完整功能，同时融入了新的专业表单设计风格。
            </p>
        </div>
    </div>

    <script>
        let uploadSimulated = false;

        function simulateUpload() {
            if (uploadSimulated) return;
            
            const progressDiv = document.getElementById('uploadProgress');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const uploadedFile = document.getElementById('uploadedFile');
            
            progressDiv.style.display = 'block';
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressFill.style.width = progress + '%';
                progressText.textContent = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        progressDiv.style.display = 'none';
                        uploadedFile.style.display = 'flex';
                        uploadSimulated = true;
                    }, 500);
                }
            }, 100);
        }

        function simulatePreview() {
            alert('模拟文件预览功能 - 在实际应用中会打开文件预览模态框');
        }

        function simulateRecognition() {
            alert('模拟OCR识别功能 - 在实际应用中会调用识别API并填充表单字段');
        }

        function simulateRemove() {
            document.getElementById('uploadedFile').style.display = 'none';
            uploadSimulated = false;
        }
    </script>
</body>
</html>
