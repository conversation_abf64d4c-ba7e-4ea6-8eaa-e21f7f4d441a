plugins {
    id "com.moowork.node" version "1.2.0"
}

node {
    version = '16.6.2'
    yarnVersion = '1.12.3'
    download = true
    distBaseUrl = 'https://npmmirror.com/mirrors/node'
}

task bootRun(dependsOn: 'start') {
    group = 'application'
    description = 'Run the client app (for use with gradle bootRun -parallel）'
}

task start(type: YarnTask, dependsOn: 'yarn') {
    group = 'application'
    description = 'Run the client app'
    args = ['run', 'start']
}

task build(type: YarnTask, dependsOn: 'yarn') {
    group = 'build'
    description = 'Build the client bundle'
    args = ['run', 'build']
}
