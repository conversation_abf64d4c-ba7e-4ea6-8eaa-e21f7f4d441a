# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 5dd9a18baa5fce4741ba729acc3a3272c49c25cb8736c4b18e113099520e7ef7b545a4096a26d600e4416157e63e87d66db46aa3fbf0a5f2286da2705c12da00
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.20.5, @babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/compat-data@npm:7.27.2"
  checksum: 077c9e01af3b90decee384a6a44dcf353898e980cee22ec7941f9074655dbbe97ec317345536cdc7ef7391521e1497930c522a3816af473076dd524be7fccd32
  languageName: node
  linkType: hard

"@babel/core@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/core@npm:7.27.1"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.1"
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helpers": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 0fc31f87f5401ac5d375528cb009f4ea5527fc8c5bb5b64b5b22c033b60fd0ad723388933a5f3f5db14e1edd13c958e9dd7e5c68f9b68c767aeb496199c8a4bb
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/generator@npm:7.27.1"
  dependencies:
    "@babel/parser": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: c4156434b21818f558ebd93ce45f027c53ee570ce55a84fd2d9ba45a79ad204c17e0bff753c886fb6c07df3385445a9e34dc7ccb070d0ac7e80bb91c8b57f423
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: fc4751b59c8f5417e1acb0455d6ffce53fa5e79b3aca690299fbbf73b1b65bfaef3d4a18abceb190024c5836bb6cfbc3711e83888648df93df54e18152a1196c
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.20.7, @babel/helper-compilation-targets@npm:^7.22.6, @babel/helper-compilation-targets@npm:^7.27.1":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: f338fa00dcfea931804a7c55d1a1c81b6f0a09787e528ec580d5c21b3ecb3913f6cb0f361368973ce953b824d910d3ac3e8a8ee15192710d3563826447193ad1
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.18.6, @babel/helper-create-class-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 4ee199671d6b9bdd4988aa2eea4bdced9a73abfc831d81b00c7634f49a8fc271b3ceda01c067af58018eb720c6151322015d463abea7072a368ee13f35adbb4c
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    regexpu-core: "npm:^6.2.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 591fe8bd3bb39679cc49588889b83bd628d8c4b99c55bafa81e80b1e605a348b64da955e3fd891c4ba3f36fd015367ba2eadea22af6a7de1610fbb5bcc2d3df0
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.3, @babel/helper-define-polyfill-provider@npm:^0.6.4":
  version: 0.6.4
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.4"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.22.6"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    debug: "npm:^4.1.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.14.2"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: b74f2b46e233a178618d19432bdae16e0137d0a603497ee901155e083c4a61f26fe01d79fb95d5f4c22131ade9d958d8f587088d412cca1302633587f070919d
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.18.9":
  version: 7.24.7
  resolution: "@babel/helper-environment-visitor@npm:7.24.7"
  dependencies:
    "@babel/types": "npm:^7.24.7"
  checksum: 36ece78882b5960e2d26abf13cf15ff5689bf7c325b10a2895a74a499e712de0d305f8d78bb382dd3c05cfba7e47ec98fe28aab5674243e0625cd38438dd0b2d
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 5762ad009b6a3d8b0e6e79ff6011b3b8fdda0fefad56cfa8bfbe6aa02d5a8a8a9680a45748fe3ac47e735a03d2d88c0a676e3f9f59f20ae9fadcc8d51ccd5a53
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.0.0, @babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: e00aace096e4e29290ff8648455c2bc4ed982f0d61dbf2db1b5e750b9b98f318bf5788d75a4f974c151bd318fd549e81dbcab595f46b14b81c12eda3023f51e8
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-transforms@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 196ab29635fe6eb5ba6ead2972d41b1c0d40f400f99bd8fc109cef21440de24c26c972fabf932585e618694d590379ab8d22def8da65a54459d38ec46112ead7
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: 6b861e7fcf6031b9c9fc2de3cd6c005e94a459d6caf3621d93346b52774925800ca29d4f64595a5ceacf4d161eb0d27649ae385110ed69491d9776686fa488e6
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.20.2, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.27.1, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 94cf22c81a0c11a09b197b41ab488d416ff62254ce13c57e62912c85700dc2e99e555225787a4099ff6bae7a1812d622c80fbaeda824b79baa10a6c5ac4cf69b
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.18.9, @babel/helper-remap-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-remap-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-wrap-function": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 5ba6258f4bb57c7c9fa76b55f416b2d18c867b48c1af4f9f2f7cd7cc933fe6da7514811d08ceb4972f1493be46f4b69c40282b811d1397403febae13c2ec57b5
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 4f2eaaf5fcc196580221a7ccd0f8873447b5d52745ad4096418f6101a1d2e712e9f93722c9a32bc9769a1dc197e001f60d6f5438d4dfde4b9c6a9e4df719354c
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: f625013bcdea422c470223a2614e90d2c1cc9d832e97f32ca1b4f82b34bb4aa67c3904cb4b116375d3b5b753acfb3951ed50835a1e832e7225295c7b0c24dff7
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 8bda3448e07b5583727c103560bcf9c4c24b3c1051a4c516d4050ef69df37bb9a4734a585fe12725b8c2763de0a265aa1e909b485a4e3270b7cfd3e4dbe4b602
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: c558f11c4871d526498e49d07a84752d1800bf72ac0d3dad100309a2eaba24efbf56ea59af5137ff15e3a00280ebe588560534b0e894a4750f8b1411d8f78b84
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 6fec5f006eba40001a20f26b1ef5dbbda377b7b68c8ad518c05baa9af3f396e780bdfded24c4eef95d14bb7b8fd56192a6ed38d5d439b97d10efc5f1a191d148
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-wrap-function@npm:7.27.1"
  dependencies:
    "@babel/template": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: c472f75c0951bc657ab0a117538c7c116566ae7579ed47ac3f572c42dc78bd6f1e18f52ebe80d38300c991c3fcaa06979e2f8864ee919369dabd59072288de30
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helpers@npm:7.27.1"
  dependencies:
    "@babel/template": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: e078257b9342dae2c041ac050276c5a28701434ad09478e6dc6976abd99f721a5a92e4bebddcbca6b1c3a7e8acace56a946340c701aad5e7507d2c87446459ba
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.23.5, @babel/parser@npm:^7.27.1, @babel/parser@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/parser@npm:7.27.2"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 3c06692768885c2f58207fc8c2cbdb4a44df46b7d93135a083f6eaa49310f7ced490ce76043a2a7606cdcc13f27e3d835e141b692f2f6337a2e7f43c1dbb04b4
  languageName: node
  linkType: hard

"@babel/plugin-proposal-async-generator-functions@npm:^7.2.0":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-async-generator-functions@npm:7.20.7"
  dependencies:
    "@babel/helper-environment-visitor": "npm:^7.18.9"
    "@babel/helper-plugin-utils": "npm:^7.20.2"
    "@babel/helper-remap-async-to-generator": "npm:^7.18.9"
    "@babel/plugin-syntax-async-generators": "npm:^7.8.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0f4bc01805704ae4840536acc9888c50a32250e9188d025063bd17fe77ed171a12361c3dc83ce99664dcd73aec612accb8da95b0d8b825c854931b2860c0bfb5
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-properties@npm:^7.0.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-class-properties@npm:7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.18.6"
    "@babel/helper-plugin-utils": "npm:^7.18.6"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d5172ac6c9948cdfc387e94f3493ad86cb04035cf7433f86b5d358270b1b9752dc25e176db0c5d65892a246aca7bdb4636672e15626d7a7de4bc0bd0040168d9
  languageName: node
  linkType: hard

"@babel/plugin-proposal-decorators@npm:^7.1.0":
  version: 7.27.1
  resolution: "@babel/plugin-proposal-decorators@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-syntax-decorators": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3af0db6b2468907bcaf62246b2cfd3616ba9239ea1cd26036ec6baff1bc095fe4964853b1d29a79944d36e6e3d331cd130d05b0c41c835266daf7bb9d8e8f87c
  languageName: node
  linkType: hard

"@babel/plugin-proposal-json-strings@npm:^7.2.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-json-strings@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.18.6"
    "@babel/plugin-syntax-json-strings": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 83f2ce41262a538ee43450044b9b0de320002473e4849421a7318c0500f9b0385c03d228f1be777ad71fd358aef13392e3551f0be52b5c423b0c34f7c9e5a06d
  languageName: node
  linkType: hard

"@babel/plugin-proposal-object-rest-spread@npm:^7.3.4":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-object-rest-spread@npm:7.20.7"
  dependencies:
    "@babel/compat-data": "npm:^7.20.5"
    "@babel/helper-compilation-targets": "npm:^7.20.7"
    "@babel/helper-plugin-utils": "npm:^7.20.2"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.8.3"
    "@babel/plugin-transform-parameters": "npm:^7.20.7"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b9818749bb49d8095df64c45db682448d04743d96722984cbfd375733b2585c26d807f84b4fdb28474f2d614be6a6ffe3d96ffb121840e9e5345b2ccc0438bd8
  languageName: node
  linkType: hard

"@babel/plugin-proposal-optional-catch-binding@npm:^7.2.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-optional-catch-binding@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ab20153d9e95e0b73004fdf86b6a2d219be2a0ace9ca76cd9eccddb680c913fec173bca54d761b1bc6044edde0a53811f3e515908c3b16d2d81cfec1e2e17391
  languageName: node
  linkType: hard

"@babel/plugin-proposal-unicode-property-regex@npm:^7.2.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-unicode-property-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.18.6"
    "@babel/helper-plugin-utils": "npm:^7.18.6"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c68feae57d9b1f4d98ecc2da63bda1993980deb509ccb08f6eace712ece8081032eb6532c304524b544c2dd577e2f9c2fe5c5bfd73d1955c946300def6fc7493
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.2.0, @babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d13efb282838481348c71073b6be6245b35d4f2f964a8f71e4174f235009f929ef7613df25f8d2338e2d3e44bc4265a9f8638c6aaa136d7a61fe95985f9725c8
  languageName: node
  linkType: hard

"@babel/plugin-syntax-decorators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-decorators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 46ef933bae10b02a8f8603b2f424ecbe23e134a133205bee7c0902dae3021c183a683964cab41ea5433820aa05be0f6f36243551f68a1d94e02ac082cec87aa1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.0.0":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9c50927bf71adf63f60c75370e2335879402648f468d0172bc912e303c6a3876927d8eb35807331b57f415392732ed05ab9b42c68ac30a936813ab549e0246c5
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.2.0, @babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e98f31b2ec406c57757d115aac81d0336e8434101c224edd9a5c93cefa53faf63eacc69f3138960c8b25401315af03df37f68d316c151c4b933136716ed6906e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.0.0, @babel/plugin-syntax-jsx@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bc5afe6a458d5f0492c02a54ad98c5756a0c13bd6d20609aae65acd560a9e141b0876da5f358dce34ea136f271c1016df58b461184d7ae9c4321e0f98588bc84
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.2.0, @babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ee1eab52ea6437e3101a0a7018b0da698545230015fc8ab129d292980ec6dff94d265e9e90070e8ae5fed42f08f1622c14c94552c77bcac784b37f503a82ff26
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.2.0, @babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 27e2493ab67a8ea6d693af1287f7e9acec206d1213ff107a928e85e173741e1d594196f99fec50e9dde404b09164f39dec5864c767212154ffe1caa6af0bc5af
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 19abd7a7d11eef58c9340408a4c2594503f6c4eaea1baa7b0e5fbdda89df097e50663edb3448ad2300170b39efca98a75e5767af05cad3b0facb4944326896a3
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.3.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e76b1f6f9c3bbf72e17d7639406d47f09481806de4db99a8de375a0bb40957ea309b20aa705f0c25ab1d7c845e3f365af67eafa368034521151a0e352a03ef2f
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3313130ba3bf0699baad0e60da1c8c3c2f0c2c0a7039cd0063e54e72e739c33f1baadfc9d8c73b3fea8c85dd7250c3964fb09c8e1fa62ba0b24a9fefe0a8dbde
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.3.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-block-scoping@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d3f357beeb92fbdf3045aea2ba286a60dafc9c2d2a9f89065bb3c4bea9cc48934ee6689df3db0439d9ec518eda5e684f3156cab792b7c38c33ece2f8204ddee8
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.3.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-classes@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    globals: "npm:^11.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1071f4cb1ed5deb5e6f8d0442f2293a540cac5caa5ab3c25ad0571aadcbf961f61e26d367a67894976165a543e02f3a19e40b63b909afbed6e710801a590635c
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-computed-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e09a12f8c8ae0e6a6144c102956947b4ec05f6c844169121d0ec4529c2d30ad1dc59fee67736193b87a402f44552c888a519a680a31853bdb4d34788c28af3b0
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-destructuring@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 56afda7a0b205f8d1af727daef4c529fc2e756887408affd39033ae4476e54d586d3d9dc1e72cfb15c74a2a5ca0653ab13dbaa8cbf79fbb2a3a746d0f107cb86
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f9caddfad9a551b4dabe0dcb7c040f458fbaaa7bbb44200c20198b32c8259be8e050e58d2c853fdac901a4cfe490b86aa857036d8d461b192dd010d0e242dedb
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 22a822e5342b7066f83eaedc4fd9bb044ac6bc68725484690b33ba04a7104980e43ea3229de439286cb8db8e7db4a865733a3f05123ab58a10f189f03553746f
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 953d21e01fed76da8e08fb5094cade7bf8927c1bb79301916bec2db0593b41dbcfbca1024ad5db886b72208a93ada8f57a219525aad048cf15814eeb65cf760d
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-for-of@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4635763173a23aae24480681f2b0996b4f54a0cb2368880301a1801638242e263132d1e8adbe112ab272913d1d900ee0d6f7dea79443aef9d3325168cd88b3fb
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-function-name@npm:7.27.1"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5abdc7b5945fbd807269dcc6e76e52b69235056023b0b35d311e8f5dfd6c09d9f225839798998fc3b663f50cf701457ddb76517025a0d7a5474f3fe56e567a4c
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c40dc3eb2f45a92ee476412314a40e471af51a0f51a24e91b85cef5fc59f4fe06758088f541643f07f949d2c67ee7bdce10e11c5ec56791ae09b15c3b451eeca
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-amd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 76e86cd278b6a3c5b8cca8dfb3428e9cd0c81a5df7096e04c783c506696b916a9561386d610a9d846ef64804640e0bd818ea47455fed0ee89b7f66c555b29537
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4def972dcd23375a266ea1189115a4ff61744b2c9366fc1de648b3fab2c650faf1a94092de93a33ff18858d2e6c4dddeeee5384cb42ba0129baeab01a5cdf1e2
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.3.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f16fca62d144d9cbf558e7b5f83e13bb6d0f21fdeff3024b0cecd42ffdec0b4151461da42bd0963512783ece31aafa5ffe03446b4869220ddd095b24d414e2b5
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-umd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e5962a8874889da2ab1aa32eb93ec21d419c7423c766e4befb39b4bb512b9ad44b47837b6cd1c8f1065445cbbcc6dc2be10298ac6e734e5ca1059fc23698daed
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.3.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 8eaa8c9aee00a00f3bd8bd8b561d3f569644d98cb2cfe3026d7398aabf9b29afd62f24f142b4112fa1f572d9b0e1928291b099cde59f56d6b59f4d565e58abf2
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-new-target@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9b0581412fcc5ab1b9a2d86a0c5407bd959391f0a1e77a46953fef9f7a57f3f4020d75f71098c5f9e5dcc680a87f9fd99b3205ab12e25ef8c19eed038c1e4b28
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-object-super@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: efa2d092ef55105deb06d30aff4e460c57779b94861188128489b72378bf1f0ab0f06a4a4d68b9ae2a59a79719fbb2d148b9a3dca19ceff9c73b1f1a95e0527c
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.2.0, @babel/plugin-transform-parameters@npm:^7.20.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-parameters@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 453a9618735eeff5551d4c7f02c250606586fe1dd210ec9f69a4f15629ace180cd944339ebff2b0f11e1a40567d83a229ba1c567620e70b2ebedea576e12196a
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.3.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-regenerator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 42395908899310bb107d9ca31ebd4c302e14c582e3ad3ebfe1498fabafc43155c8f10850265c1e686a2afcf50d1f402cc5c5218fba72e167852607a4d8d6492e
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.4.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-runtime@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    babel-plugin-polyfill-corejs2: "npm:^0.4.10"
    babel-plugin-polyfill-corejs3: "npm:^0.11.0"
    babel-plugin-polyfill-regenerator: "npm:^0.6.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7abbae60a6441ba8546dee3fcbc00b38038304250ba2419adaf0c76267bff43420ff75b7049003a24a829e01d9fde2ac8a422352af6d88aebd31996a83f04c2f
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bd5544b89520a22c41a6df5ddac9039821d3334c0ef364d18b0ba9674c5071c223bcc98be5867dc3865cb10796882b7594e2c40dedaff38e1b1273913fe353e1
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-spread@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b34fc58b33bd35b47d67416655c2cbc8578fbb3948b4592bc15eb6d8b4046986e25c06e3b9929460fa4ab08e9653582415e7ef8b87d265e1239251bdf5a4c162
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5698df2d924f0b1b7bdb7ef370e83f99ed3f0964eb3b9c27d774d021bee7f6d45f9a73e2be369d90b4aff1603ce29827f8743f091789960e7669daf9c3cda850
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-template-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c90f403e42ef062b60654d1c122c70f3ec6f00c2f304b0931ebe6d0b432498ef8a5ef9266ddf00debc535f8390842207e44d3900eff1d2bab0cc1a700f03e083
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a13c68015311fefa06a51830bc69d5badd06c881b13d5cf9ba04bf7c73e3fc6311cc889e18d9645ce2a64a79456dc9c7be88476c0b6802f62a686cb6f662ecd6
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6abda1bcffb79feba6f5c691859cdbe984cc96481ea65d5af5ba97c2e843154005f0886e25006a37a2d213c0243506a06eaeafd93a040dbe1f79539016a0d17a
  languageName: node
  linkType: hard

"@babel/polyfill@npm:^7.4.4":
  version: 7.12.1
  resolution: "@babel/polyfill@npm:7.12.1"
  dependencies:
    core-js: "npm:^2.6.5"
    regenerator-runtime: "npm:^0.13.4"
  checksum: f5d233d2958582e8678838c32c42ba780965119ebb3771d9b9735f85efabc7b8b49161e7d908477486e0aaf8508410e957be764c27a6a828714fb9d1b7f80bc3
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.0.0 < 7.4.0":
  version: 7.3.4
  resolution: "@babel/preset-env@npm:7.3.4"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.0.0"
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@babel/plugin-proposal-async-generator-functions": "npm:^7.2.0"
    "@babel/plugin-proposal-json-strings": "npm:^7.2.0"
    "@babel/plugin-proposal-object-rest-spread": "npm:^7.3.4"
    "@babel/plugin-proposal-optional-catch-binding": "npm:^7.2.0"
    "@babel/plugin-proposal-unicode-property-regex": "npm:^7.2.0"
    "@babel/plugin-syntax-async-generators": "npm:^7.2.0"
    "@babel/plugin-syntax-json-strings": "npm:^7.2.0"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.2.0"
    "@babel/plugin-syntax-optional-catch-binding": "npm:^7.2.0"
    "@babel/plugin-transform-arrow-functions": "npm:^7.2.0"
    "@babel/plugin-transform-async-to-generator": "npm:^7.3.4"
    "@babel/plugin-transform-block-scoped-functions": "npm:^7.2.0"
    "@babel/plugin-transform-block-scoping": "npm:^7.3.4"
    "@babel/plugin-transform-classes": "npm:^7.3.4"
    "@babel/plugin-transform-computed-properties": "npm:^7.2.0"
    "@babel/plugin-transform-destructuring": "npm:^7.2.0"
    "@babel/plugin-transform-dotall-regex": "npm:^7.2.0"
    "@babel/plugin-transform-duplicate-keys": "npm:^7.2.0"
    "@babel/plugin-transform-exponentiation-operator": "npm:^7.2.0"
    "@babel/plugin-transform-for-of": "npm:^7.2.0"
    "@babel/plugin-transform-function-name": "npm:^7.2.0"
    "@babel/plugin-transform-literals": "npm:^7.2.0"
    "@babel/plugin-transform-modules-amd": "npm:^7.2.0"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.2.0"
    "@babel/plugin-transform-modules-systemjs": "npm:^7.3.4"
    "@babel/plugin-transform-modules-umd": "npm:^7.2.0"
    "@babel/plugin-transform-named-capturing-groups-regex": "npm:^7.3.0"
    "@babel/plugin-transform-new-target": "npm:^7.0.0"
    "@babel/plugin-transform-object-super": "npm:^7.2.0"
    "@babel/plugin-transform-parameters": "npm:^7.2.0"
    "@babel/plugin-transform-regenerator": "npm:^7.3.4"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.2.0"
    "@babel/plugin-transform-spread": "npm:^7.2.0"
    "@babel/plugin-transform-sticky-regex": "npm:^7.2.0"
    "@babel/plugin-transform-template-literals": "npm:^7.2.0"
    "@babel/plugin-transform-typeof-symbol": "npm:^7.2.0"
    "@babel/plugin-transform-unicode-regex": "npm:^7.2.0"
    browserslist: "npm:^4.3.4"
    invariant: "npm:^2.2.2"
    js-levenshtein: "npm:^1.1.3"
    semver: "npm:^5.3.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: efd9bf2167892dfc0adb8b422b3434447094f9e3d6009d5a02f6153cea2957b0330cad3890cb7dc55f13e9a137be1fa49b794692334129114915937a235ed80d
  languageName: node
  linkType: hard

"@babel/runtime-corejs2@npm:^7.2.0":
  version: 7.27.1
  resolution: "@babel/runtime-corejs2@npm:7.27.1"
  dependencies:
    core-js: "npm:^2.6.12"
  checksum: 06ba168c010082f6c44c7e27d347c96d3182e90e3548520715669a1616449dd27a59ee8055497681edf14f15ec777f9041f0e32323e9f98ca720031c22ae448b
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.0.0":
  version: 7.27.1
  resolution: "@babel/runtime@npm:7.27.1"
  checksum: 530a7332f86ac5a7442250456823a930906911d895c0b743bf1852efc88a20a016ed4cd26d442d0ca40ae6d5448111e02a08dd638a4f1064b47d080e2875dc05
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.1":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: ed9e9022651e463cc5f2cc21942f0e74544f1754d231add6348ff1b472985a3b3502041c0be62dc99ed2d12cfae0c51394bf827452b98a2f8769c03b87aadc81
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/traverse@npm:7.27.1"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: d912110037b03b1d70a2436cfd51316d930366a5f54252da2bced1ba38642f644f848240a951e5caf12f1ef6c40d3d96baa92ea6e84800f2e891c15e97b25d50
  languageName: node
  linkType: hard

"@babel/types@npm:^7.24.7, @babel/types@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/types@npm:7.27.1"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: ed736f14db2fdf0d36c539c8e06b6bb5e8f9649a12b5c0e1c516fed827f27ef35085abe08bf4d1302a4e20c9a254e762eed453bce659786d4a6e01ba26a91377
  languageName: node
  linkType: hard

"@hapi/address@npm:2.x.x":
  version: 2.1.4
  resolution: "@hapi/address@npm:2.1.4"
  checksum: d43068dd090fa1cb9324232b8cbe3235cd9106d5b8f5280262bddff27e95f383bee887bd30931f7765e10b111169681b92ff6ae46cc608686e65a17315f5c4bb
  languageName: node
  linkType: hard

"@hapi/bourne@npm:1.x.x":
  version: 1.3.2
  resolution: "@hapi/bourne@npm:1.3.2"
  checksum: c0f22d8577e36a86b12de7fd2356cb373a509c859ccdca3e73b017ade85fa7510235c2da46e32a530bee241ee056f7dac5af3f764585148694315d0847a20a8a
  languageName: node
  linkType: hard

"@hapi/hoek@npm:8.x.x, @hapi/hoek@npm:^8.3.0":
  version: 8.5.1
  resolution: "@hapi/hoek@npm:8.5.1"
  checksum: 3279747618d3be88e384af2be9aff8366dd0cbdba61d37f162d896fe4432f90b3986f96f0e5815429a72e7b6b7293429a4aaff42c234e42ee6a66bb1f9fb14dd
  languageName: node
  linkType: hard

"@hapi/joi@npm:^15.0.1":
  version: 15.1.1
  resolution: "@hapi/joi@npm:15.1.1"
  dependencies:
    "@hapi/address": "npm:2.x.x"
    "@hapi/bourne": "npm:1.x.x"
    "@hapi/hoek": "npm:8.x.x"
    "@hapi/topo": "npm:3.x.x"
  checksum: 85170aa4a7d014d66b9db5a403d0790153756edfae52e0c6012309991b1ca10ed43700b28e68aa0b438704e0f7377b20bc212be01ac7cf64028a2fc8debb4da8
  languageName: node
  linkType: hard

"@hapi/topo@npm:3.x.x":
  version: 3.1.6
  resolution: "@hapi/topo@npm:3.1.6"
  dependencies:
    "@hapi/hoek": "npm:^8.3.0"
  checksum: 9948100dc6a3081bf29e88c7c2657a1612e46fe701d6f0cf5a5d3d656344c9cc6199d5489c563f231b65213288293673dec7d32845c59882a0f26c07dc461980
  languageName: node
  linkType: hard

"@intervolga/optimize-cssnano-plugin@npm:^1.0.5":
  version: 1.0.6
  resolution: "@intervolga/optimize-cssnano-plugin@npm:1.0.6"
  dependencies:
    cssnano: "npm:^4.0.0"
    cssnano-preset-default: "npm:^4.0.0"
    postcss: "npm:^7.0.0"
  peerDependencies:
    webpack: ^4.0.0
  checksum: 33fe91486b867a5f53f9c184f1867e51e89ab3251d976c51967bd64537cd2ca32c43fa87763487acc112d5be9e97ee8fc688042c0044bf316481344bd9a98605
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@mrmlnc/readdir-enhanced@npm:^2.2.1":
  version: 2.2.1
  resolution: "@mrmlnc/readdir-enhanced@npm:2.2.1"
  dependencies:
    call-me-maybe: "npm:^1.0.1"
    glob-to-regexp: "npm:^0.3.0"
  checksum: 01840f3c85e9a7cd0ed5e038cc00e7518809b9edda950598e22b1c9804832e39a75707aaa6eb0b023e72182a85e00041c7a01483e425b16257bd3d5e4c788d86
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:^1.1.2":
  version: 1.1.3
  resolution: "@nodelib/fs.stat@npm:1.1.3"
  checksum: dc28ccae626e817a61b1544285b0f86c4e94a4a23db777c2949f78866ec57b1e1ccd5554bc3ed8e965df0646b1019e184315d32e98428c15eef7409974b17598
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@playwright/test@npm:^1.53.0":
  version: 1.53.0
  resolution: "@playwright/test@npm:1.53.0"
  dependencies:
    playwright: "npm:1.53.0"
  bin:
    playwright: cli.js
  checksum: 8335d56545a172b3b0d98c67094ce659899577746d95e991d7c47d94b533f715b2278d13795646cd3c5c2ed4559ce89b520aafd6f4ec4586fb51d047215173db
  languageName: node
  linkType: hard

"@soda/friendly-errors-webpack-plugin@npm:^1.7.1":
  version: 1.8.1
  resolution: "@soda/friendly-errors-webpack-plugin@npm:1.8.1"
  dependencies:
    chalk: "npm:^3.0.0"
    error-stack-parser: "npm:^2.0.6"
    string-width: "npm:^4.2.3"
    strip-ansi: "npm:^6.0.1"
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: 221d1a255f0a7062fc54550484b5b675596fe38adb10d323894aa070de9a245e198e715a4ea56ef8368969158fa275f771d39aaa0cb552df53431d76dc0e5130
  languageName: node
  linkType: hard

"@types/glob@npm:^7.1.1":
  version: 7.2.0
  resolution: "@types/glob@npm:7.2.0"
  dependencies:
    "@types/minimatch": "npm:*"
    "@types/node": "npm:*"
  checksum: a8eb5d5cb5c48fc58c7ca3ff1e1ddf771ee07ca5043da6e4871e6757b4472e2e73b4cfef2644c38983174a4bc728c73f8da02845c28a1212f98cabd293ecae98
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.5":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/minimatch@npm:*":
  version: 5.1.2
  resolution: "@types/minimatch@npm:5.1.2"
  checksum: 83cf1c11748891b714e129de0585af4c55dd4c2cafb1f1d5233d79246e5e1e19d1b5ad9e8db449667b3ffa2b6c80125c429dbee1054e9efb45758dbc4e118562
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 22.15.21
  resolution: "@types/node@npm:22.15.21"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: f092bbccda2131c2b2c8f720338080aa0ef1d928f5f1062c03954a4f7dafa7ee3ed29bc3e51bd4e2584473b3d943c637a2b39ad7174898970818270187cf10c1
  languageName: node
  linkType: hard

"@types/normalize-package-data@npm:^2.4.0":
  version: 2.4.4
  resolution: "@types/normalize-package-data@npm:2.4.4"
  checksum: aef7bb9b015883d6f4119c423dd28c4bdc17b0e8a0ccf112c78b4fe0e91fbc4af7c6204b04bba0e199a57d2f3fbbd5b4a14bf8739bf9d2a39b2a0aad545e0f86
  languageName: node
  linkType: hard

"@types/q@npm:^1.5.1":
  version: 1.5.8
  resolution: "@types/q@npm:1.5.8"
  checksum: 6b2903a03f23ce737503b8a4c409a4133f15009a70e125b5efd5d8c315a5426e64b574ee65288c9dd655c631dcc51c69e4b540b59905ad0b1398952ba367d88b
  languageName: node
  linkType: hard

"@vue/babel-helper-vue-jsx-merge-props@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-helper-vue-jsx-merge-props@npm:1.4.0"
  checksum: 176a75390b5d41599ecc9120a155a3a85b0559afc8b3aa32555c758c6c17e38a7373b068c3e74f9ba314e61529de8a75d262efe4bd441dfa35b531072ce30e4c
  languageName: node
  linkType: hard

"@vue/babel-plugin-transform-vue-jsx@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-plugin-transform-vue-jsx@npm:1.4.0"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.0.0"
    "@babel/plugin-syntax-jsx": "npm:^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props": "npm:^1.4.0"
    html-tags: "npm:^2.0.0"
    lodash.kebabcase: "npm:^4.1.1"
    svg-tags: "npm:^1.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 791d0f252947f634bebe975816e0c17a4bf942460492bcde5dd8c663819949c2c54181712154470adbe7d023016ae989ab886ac6ac7519674e53b714ee690525
  languageName: node
  linkType: hard

"@vue/babel-preset-app@npm:^3.12.1":
  version: 3.12.1
  resolution: "@vue/babel-preset-app@npm:3.12.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.0.0"
    "@babel/plugin-proposal-class-properties": "npm:^7.0.0"
    "@babel/plugin-proposal-decorators": "npm:^7.1.0"
    "@babel/plugin-syntax-dynamic-import": "npm:^7.0.0"
    "@babel/plugin-syntax-jsx": "npm:^7.0.0"
    "@babel/plugin-transform-runtime": "npm:^7.4.0"
    "@babel/preset-env": "npm:^7.0.0 < 7.4.0"
    "@babel/runtime": "npm:^7.0.0"
    "@babel/runtime-corejs2": "npm:^7.2.0"
    "@vue/babel-preset-jsx": "npm:^1.0.0"
    babel-plugin-dynamic-import-node: "npm:^2.2.0"
    babel-plugin-module-resolver: "npm:3.2.0"
    core-js: "npm:^2.6.5"
  checksum: a08c604c12f12e18e390a57ecb0015a69a608c8798e9aef5acb60cccbed28f3156db45503aec9a1b8e3170a4fdb8aad937f07d7da372cb74547ac571f42feec8
  languageName: node
  linkType: hard

"@vue/babel-preset-jsx@npm:^1.0.0":
  version: 1.4.0
  resolution: "@vue/babel-preset-jsx@npm:1.4.0"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props": "npm:^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx": "npm:^1.4.0"
    "@vue/babel-sugar-composition-api-inject-h": "npm:^1.4.0"
    "@vue/babel-sugar-composition-api-render-instance": "npm:^1.4.0"
    "@vue/babel-sugar-functional-vue": "npm:^1.4.0"
    "@vue/babel-sugar-inject-h": "npm:^1.4.0"
    "@vue/babel-sugar-v-model": "npm:^1.4.0"
    "@vue/babel-sugar-v-on": "npm:^1.4.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
    vue: "*"
  peerDependenciesMeta:
    vue:
      optional: true
  checksum: 20ab15ccd012788f81f656f4671bfce2216e39ee6e0d6e9f3beb6ddc26150fe9505efd92fc62238dc67eb98d8adc548fe077fa6a68c39003014156e2a3b6c641
  languageName: node
  linkType: hard

"@vue/babel-sugar-composition-api-inject-h@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-composition-api-inject-h@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": "npm:^7.2.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c8b1fd5693ef99ba5358a954db0208588760022d9591006467f15da21b14b7e1f679a290e9ff0deca54696f22a63d799715d9305a7e48de4762cfc6337e6fe78
  languageName: node
  linkType: hard

"@vue/babel-sugar-composition-api-render-instance@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-composition-api-render-instance@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": "npm:^7.2.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3ef9cd71b721557a56259f20320c457165802cad60d957d21fdabb4e8fabf2e55a7851ea8f4382278bf0e0bbec27a64700f5c668bf16993124f2b58dc3bf2376
  languageName: node
  linkType: hard

"@vue/babel-sugar-functional-vue@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-functional-vue@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": "npm:^7.2.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2e2c470ea31bc0f2a2b82f37406ddf02387b0e6b0e14d6905b989e956d0727283375f8147bd1d71afa138277b8380ff6b5d1d3e9639a420fdd9ffdb520d1d70e
  languageName: node
  linkType: hard

"@vue/babel-sugar-inject-h@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-inject-h@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": "npm:^7.2.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 364823f4d4b122c7740de2e9d6e50dbdd25f5c55b7cd2053e10c388543bf536ada26b038669388727d333277009f3a404aac15658aef0f017890c60a8e0115c1
  languageName: node
  linkType: hard

"@vue/babel-sugar-v-model@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-v-model@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": "npm:^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props": "npm:^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx": "npm:^1.4.0"
    camelcase: "npm:^5.0.0"
    html-tags: "npm:^2.0.0"
    svg-tags: "npm:^1.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10cf44f2e7699487b1aeaa093ffa2721f0f046cdb958261e2df53fe82e39bf3e59bddcbbca527f69f7db97e518e40e55ec0314357052b84d8f948bce76aaa676
  languageName: node
  linkType: hard

"@vue/babel-sugar-v-on@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-v-on@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": "npm:^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx": "npm:^1.4.0"
    camelcase: "npm:^5.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2b60535a7cf27574bbfb36777181ce11d76081b96ac8ac1a71e341146a8a2d7f83a1400ada1e2b54023fa1ec90e9d1c0ff2f353870401c70b74fe988844f6c9b
  languageName: node
  linkType: hard

"@vue/cli-overlay@npm:^3.12.1":
  version: 3.12.1
  resolution: "@vue/cli-overlay@npm:3.12.1"
  checksum: 29233b53bba23f73a799465c06935d3578f5f6b4bc11259e302b25200cc5659704386e7b1797187ac21e0bea4735ce254714a57e0b6c83c5d701cb0d9623ec3a
  languageName: node
  linkType: hard

"@vue/cli-plugin-babel@npm:^3.9.0":
  version: 3.12.1
  resolution: "@vue/cli-plugin-babel@npm:3.12.1"
  dependencies:
    "@babel/core": "npm:^7.0.0"
    "@vue/babel-preset-app": "npm:^3.12.1"
    "@vue/cli-shared-utils": "npm:^3.12.1"
    babel-loader: "npm:^8.0.5"
    webpack: "npm:^4.0.0"
  checksum: 6cc5515a40907ce17e75e0dde065ff4a4accaee4e1f997028c03c094401a2970aef2302a15e8b0dda99489eb13774b20cbbb4b2db6fc8bb9383f6eb0e1c25386
  languageName: node
  linkType: hard

"@vue/cli-service@npm:^3.9.0":
  version: 3.12.1
  resolution: "@vue/cli-service@npm:3.12.1"
  dependencies:
    "@intervolga/optimize-cssnano-plugin": "npm:^1.0.5"
    "@soda/friendly-errors-webpack-plugin": "npm:^1.7.1"
    "@vue/cli-overlay": "npm:^3.12.1"
    "@vue/cli-shared-utils": "npm:^3.12.1"
    "@vue/component-compiler-utils": "npm:^3.0.0"
    "@vue/preload-webpack-plugin": "npm:^1.1.0"
    "@vue/web-component-wrapper": "npm:^1.2.0"
    acorn: "npm:^6.1.1"
    acorn-walk: "npm:^6.1.1"
    address: "npm:^1.0.3"
    autoprefixer: "npm:^9.5.1"
    browserslist: "npm:^4.5.4"
    cache-loader: "npm:^2.0.1"
    case-sensitive-paths-webpack-plugin: "npm:^2.2.0"
    chalk: "npm:^2.4.2"
    cli-highlight: "npm:^2.1.0"
    clipboardy: "npm:^2.0.0"
    cliui: "npm:^5.0.0"
    copy-webpack-plugin: "npm:^4.6.0"
    css-loader: "npm:^1.0.1"
    cssnano: "npm:^4.1.10"
    current-script-polyfill: "npm:^1.0.0"
    debug: "npm:^4.1.1"
    default-gateway: "npm:^5.0.2"
    dotenv: "npm:^7.0.0"
    dotenv-expand: "npm:^5.1.0"
    escape-string-regexp: "npm:^1.0.5"
    file-loader: "npm:^3.0.1"
    fs-extra: "npm:^7.0.1"
    globby: "npm:^9.2.0"
    hash-sum: "npm:^1.0.2"
    html-webpack-plugin: "npm:^3.2.0"
    launch-editor-middleware: "npm:^2.2.1"
    lodash.defaultsdeep: "npm:^4.6.1"
    lodash.mapvalues: "npm:^4.6.0"
    lodash.transform: "npm:^4.6.0"
    mini-css-extract-plugin: "npm:^0.8.0"
    minimist: "npm:^1.2.0"
    ora: "npm:^3.4.0"
    portfinder: "npm:^1.0.20"
    postcss-loader: "npm:^3.0.0"
    read-pkg: "npm:^5.0.0"
    semver: "npm:^6.0.0"
    slash: "npm:^2.0.0"
    source-map-url: "npm:^0.4.0"
    ssri: "npm:^6.0.1"
    string.prototype.padend: "npm:^3.0.0"
    terser-webpack-plugin: "npm:^1.2.3"
    thread-loader: "npm:^2.1.2"
    url-loader: "npm:^1.1.2"
    vue-loader: "npm:^15.7.0"
    webpack: "npm:^4.0.0"
    webpack-bundle-analyzer: "npm:^3.3.0"
    webpack-chain: "npm:^4.11.0"
    webpack-dev-server: "npm:^3.4.1"
    webpack-merge: "npm:^4.2.1"
  peerDependencies:
    vue-template-compiler: ^2.0.0
  bin:
    vue-cli-service: bin/vue-cli-service.js
  checksum: 4ac00540b3d18247a276dec836202f82c531d15c35511099ca4a7d9bafd200d62762957d2c33a97bf10180399cdbf85b3b5e1940bf52455249e7de632b94bb30
  languageName: node
  linkType: hard

"@vue/cli-shared-utils@npm:^3.12.1":
  version: 3.12.1
  resolution: "@vue/cli-shared-utils@npm:3.12.1"
  dependencies:
    "@hapi/joi": "npm:^15.0.1"
    chalk: "npm:^2.4.1"
    execa: "npm:^1.0.0"
    launch-editor: "npm:^2.2.1"
    lru-cache: "npm:^5.1.1"
    node-ipc: "npm:^9.1.1"
    open: "npm:^6.3.0"
    ora: "npm:^3.4.0"
    request: "npm:^2.87.0"
    request-promise-native: "npm:^1.0.7"
    semver: "npm:^6.0.0"
    string.prototype.padstart: "npm:^3.0.0"
  checksum: 566873872f8d23ab0f2aa45dfce0ac1011a0534208a75cc5278101ae6f74a36ccb27cd31807da426e911a97412ddb3a5bc80d33a670f562b0596d8f6ce00a9ba
  languageName: node
  linkType: hard

"@vue/compiler-sfc@npm:2.7.16":
  version: 2.7.16
  resolution: "@vue/compiler-sfc@npm:2.7.16"
  dependencies:
    "@babel/parser": "npm:^7.23.5"
    postcss: "npm:^8.4.14"
    prettier: "npm:^1.18.2 || ^2.0.0"
    source-map: "npm:^0.6.1"
  dependenciesMeta:
    prettier:
      optional: true
  checksum: eaeeef054c939e6cd7591199e2b998ae33d0afd65dc1b5675b54361f0c657c08ae82945791a1a8ca76762e1c1f8e69a00595daf280b854cbc3370ed5c5a34bcd
  languageName: node
  linkType: hard

"@vue/component-compiler-utils@npm:^3.0.0, @vue/component-compiler-utils@npm:^3.1.0":
  version: 3.3.0
  resolution: "@vue/component-compiler-utils@npm:3.3.0"
  dependencies:
    consolidate: "npm:^0.15.1"
    hash-sum: "npm:^1.0.2"
    lru-cache: "npm:^4.1.2"
    merge-source-map: "npm:^1.1.0"
    postcss: "npm:^7.0.36"
    postcss-selector-parser: "npm:^6.0.2"
    prettier: "npm:^1.18.2 || ^2.0.0"
    source-map: "npm:~0.6.1"
    vue-template-es2015-compiler: "npm:^1.9.0"
  dependenciesMeta:
    prettier:
      optional: true
  checksum: ab471a561c29a307b92d019be9f0404157d7bec4ac5040bffea918db4fadc784765a52d9621bef9330a108eb123d1bcb4c276bf1c53fd6f4ac022739b3b80cbe
  languageName: node
  linkType: hard

"@vue/preload-webpack-plugin@npm:^1.1.0":
  version: 1.1.2
  resolution: "@vue/preload-webpack-plugin@npm:1.1.2"
  peerDependencies:
    html-webpack-plugin: ">=2.26.0"
    webpack: ">=4.0.0"
  checksum: 371ab944960413361d7c9610356ba4ae33e0f2706496b23a8d77dc412c2a69bf6490a446c8241d9c1782d2695cc94c0738b29eb62159d1d029ab939ec01be121
  languageName: node
  linkType: hard

"@vue/web-component-wrapper@npm:^1.2.0":
  version: 1.3.0
  resolution: "@vue/web-component-wrapper@npm:1.3.0"
  checksum: 258259f60f4709f1c0372daef8c5a88d9d5e6869304e1390e142c95353c4eadfc1f0422991400cfa1fbcfc5affd4cf69cce7359f98a47d559561dda284421d23
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/ast@npm:1.9.0"
  dependencies:
    "@webassemblyjs/helper-module-context": "npm:1.9.0"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.9.0"
    "@webassemblyjs/wast-parser": "npm:1.9.0"
  checksum: 8246c714346cdcd3ab204a2b09904d9d36c4f7da8f30cc217b0b7272a3ef57a3c21e95d51b26601641133fb66fea5cc46c357cf897808512f13b3d1c2efe88e4
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.9.0"
  checksum: 17acfdfe6650691ae8d0279e6ff4fb8b5efce64e12f3fa18c6a7d279968cc72eb21c0db7ebb5be9d627d05fa7014cef087843d999de96c917079f57d7dac8f77
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-api-error@npm:1.9.0"
  checksum: 892851b25cf4b4b307490328f45858414326dac667ca15244b5e959fa6e22478b29dabeb581d49ef8a2874e291d0417a3a959be70428c39cd40870e73b394dbc
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-buffer@npm:1.9.0"
  checksum: b09a3e27d9127ccaab095bd171336e7675bb5b832e05b701ff174a853b763154a49f5382c4c3f2f1cc746b1cff3f2025452145cf807ddf788133bcccf5920ca8
  languageName: node
  linkType: hard

"@webassemblyjs/helper-code-frame@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-code-frame@npm:1.9.0"
  dependencies:
    "@webassemblyjs/wast-printer": "npm:1.9.0"
  checksum: 010969a6c8b016680a9b1383ff4b8147c363608dd1e29602154e5460954af4fd48daed518a76b232ca43935d4b6bebf54fba38da56f809e2bd12f063d84013ec
  languageName: node
  linkType: hard

"@webassemblyjs/helper-fsm@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-fsm@npm:1.9.0"
  checksum: ef0c99b58716d757a1a41f99fb46578d3f07d97b60cd51deaeffdf0aad09ec47f5093ee8d098d12324d57f8812609704c377fccfe9a32d02c0a658a4a33dce94
  languageName: node
  linkType: hard

"@webassemblyjs/helper-module-context@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-module-context@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": "npm:1.9.0"
  checksum: 130a9ac1141770b9f70ad568ec2dc769e92c756f91b06ece9cda2c2a5e80e21ec9c8c2a945a5839bf379e52fa921ae134245a7492e1b9ae0e8c557bb9b4953c3
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.9.0"
  checksum: 1741993e1c723f56b619a4981ec975f903886aa3f1f50c7bdb2eaa45ca4ad8d023d6ae7413ef643f060567b1f12a9dcfad6c43688879c46ee4f0b53aa71cd5c9
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": "npm:1.9.0"
    "@webassemblyjs/helper-buffer": "npm:1.9.0"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.9.0"
    "@webassemblyjs/wasm-gen": "npm:1.9.0"
  checksum: 2a5baa7749c50a4a428f372ab88b7e52956b48798d44e7291b4aa8558b247337dba791112ce8a4f5b2281e1b9014e6d44d0141476a5fcde6016fac2e009671e8
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/ieee754@npm:1.9.0"
  dependencies:
    "@xtuc/ieee754": "npm:^1.2.0"
  checksum: 0eff34ec7048400b30282ab9af6ad19d2852dab2f5ffaec8bdc697b8380bc2c9dbe6cadf65f49e68242c82ee3caa8aa6e46c89dbfdab37615189b4da2eab3819
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/leb128@npm:1.9.0"
  dependencies:
    "@xtuc/long": "npm:4.2.2"
  checksum: 441be8634733b33b710f44d4394552d6290bb1a0a8311b384b1865b58c3549d0ddeaf1c3985bbee024a8df12c597be3580fc1cde2ae003dcbf26762b493a7a2f
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/utf8@npm:1.9.0"
  checksum: 9566689a1bcf555d6b79d0da79e24ff2be23c0395e5a19ed3c2ceca7831e50b867e0b1c66b3ff1b1d7f297b2d2414314967a884a77634ad0acff8a78489e2b19
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wasm-edit@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": "npm:1.9.0"
    "@webassemblyjs/helper-buffer": "npm:1.9.0"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.9.0"
    "@webassemblyjs/helper-wasm-section": "npm:1.9.0"
    "@webassemblyjs/wasm-gen": "npm:1.9.0"
    "@webassemblyjs/wasm-opt": "npm:1.9.0"
    "@webassemblyjs/wasm-parser": "npm:1.9.0"
    "@webassemblyjs/wast-printer": "npm:1.9.0"
  checksum: 07f4cb4a73989622c524f9264b6afe664d33354f081499f04db675aed2b79498bd43600c3d7bebcb9f93ccce6a094b3c28f3f7b11ea62e9e82074c2ae68dc058
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wasm-gen@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": "npm:1.9.0"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.9.0"
    "@webassemblyjs/ieee754": "npm:1.9.0"
    "@webassemblyjs/leb128": "npm:1.9.0"
    "@webassemblyjs/utf8": "npm:1.9.0"
  checksum: 876826bef91f3af9e48118fb269c348871d5b6f019e071065556da56a3a5818630b00133e07c9dd2cc767e7f2c70934f3ed0060330ce3e37910e9c9df25f1600
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wasm-opt@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": "npm:1.9.0"
    "@webassemblyjs/helper-buffer": "npm:1.9.0"
    "@webassemblyjs/wasm-gen": "npm:1.9.0"
    "@webassemblyjs/wasm-parser": "npm:1.9.0"
  checksum: 3d5558e078b660cd9777950f2df60f005f3cbdbcfa6c8c19dc0cf012f44f5bfa97c991d7ac26b3e78596bad0538e92dd00b5db4b51ebc373da8e329a03639190
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wasm-parser@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": "npm:1.9.0"
    "@webassemblyjs/helper-api-error": "npm:1.9.0"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.9.0"
    "@webassemblyjs/ieee754": "npm:1.9.0"
    "@webassemblyjs/leb128": "npm:1.9.0"
    "@webassemblyjs/utf8": "npm:1.9.0"
  checksum: 1e8615b9f9c3c431c9635c9a9884bca89eff1ab2383ad849341c23e09899454482a8f8813d33bf86ee1b0acc97c7c83926961a9b34d4804fa5d559610ab0a4a2
  languageName: node
  linkType: hard

"@webassemblyjs/wast-parser@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wast-parser@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": "npm:1.9.0"
    "@webassemblyjs/floating-point-hex-parser": "npm:1.9.0"
    "@webassemblyjs/helper-api-error": "npm:1.9.0"
    "@webassemblyjs/helper-code-frame": "npm:1.9.0"
    "@webassemblyjs/helper-fsm": "npm:1.9.0"
    "@xtuc/long": "npm:4.2.2"
  checksum: c79952466fdf7816be527b1db102952b777b12318eabb5c40df074cd8361e3a7b0179a985534fa8b5a7b93668b07ba46875ffeb5da03ca5177c80ba960ebdffc
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wast-printer@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": "npm:1.9.0"
    "@webassemblyjs/wast-parser": "npm:1.9.0"
    "@xtuc/long": "npm:4.2.2"
  checksum: f3d106aa884cbb7687307db7adeb3b98abff9de81b9ba8c1065267340b5e9de64ffc533044ab916b1f4ce8a67fb03efa54b29b61c8e908abe4c07edf82f614cd
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: a8565d29d135039bd99ae4b2220d3e167d22cf53f867e491ed479b3f84f895742d0097f935b19aab90265a23d5d46711e4204f14c479ae3637fbf06c4666882f
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 8582cbc69c79ad2d31568c412129bf23d2b1210a1dfb60c82d5a1df93334da4ee51f3057051658569e2c196d8dc33bc05ae6b974a711d0d16e801e1d0647ccd1
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"accepts@npm:~1.3.4, accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn-walk@npm:^6.1.1":
  version: 6.2.0
  resolution: "acorn-walk@npm:6.2.0"
  checksum: 748c2b5f2c5dedc1455c5d1685d31c744b6850d478824862e7f517688369e4ddb1c169ef4eec533ed328ef5139d786defa30f57a8a71f5d0a164f43619a2e8f9
  languageName: node
  linkType: hard

"acorn-walk@npm:^7.1.1":
  version: 7.2.0
  resolution: "acorn-walk@npm:7.2.0"
  checksum: ff99f3406ed8826f7d6ef6ac76b7608f099d45a1ff53229fa267125da1924188dbacf02e7903dfcfd2ae4af46f7be8847dc7d564c73c4e230dfb69c8ea8e6b4c
  languageName: node
  linkType: hard

"acorn@npm:^6.1.1, acorn@npm:^6.4.1":
  version: 6.4.2
  resolution: "acorn@npm:6.4.2"
  bin:
    acorn: bin/acorn
  checksum: 52a72d5d785fa64a95880f2951021a38954f8f69a4944dfeab6fb1449b0f02293eae109a56d55b58ff31a90a00d16a804658a12db8ef834c20b3d1201fe5ba5b
  languageName: node
  linkType: hard

"acorn@npm:^7.1.1":
  version: 7.4.1
  resolution: "acorn@npm:7.4.1"
  bin:
    acorn: bin/acorn
  checksum: bd0b2c2b0f334bbee48828ff897c12bd2eb5898d03bf556dcc8942022cec795ac5bb5b6b585e2de687db6231faf07e096b59a361231dd8c9344d5df5f7f0e526
  languageName: node
  linkType: hard

"acorn@npm:^8.5.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: dec73ff59b7d6628a01eebaece7f2bdb8bb62b9b5926dcad0f8931f2b8b79c2be21f6c68ac095592adb5adb15831a3635d9343e6a91d028bbe85d564875ec3ec
  languageName: node
  linkType: hard

"address@npm:^1.0.3":
  version: 1.2.2
  resolution: "address@npm:1.2.2"
  checksum: 1c8056b77fb124456997b78ed682ecc19d2fd7ea8bd5850a2aa8c3e3134c913847c57bcae418622efd32ba858fa1e242a40a251ac31da0515664fc0ac03a047d
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.4
  resolution: "agent-base@npm:7.1.4"
  checksum: c2c9ab7599692d594b6a161559ada307b7a624fa4c7b03e3afdb5a5e31cd0e53269115b620fcab024c5ac6a6f37fa5eb2e004f076ad30f5f7e6b8b671f7b35fe
  languageName: node
  linkType: hard

"ajv-errors@npm:^1.0.0":
  version: 1.0.1
  resolution: "ajv-errors@npm:1.0.1"
  peerDependencies:
    ajv: ">=5.0.0"
  checksum: de2d6e8100c8707ea063ee4785d53adf599b457c0d4f72c3592244d67ad16448a6d35f7ce45f12bdd2819939447c876e8ef2f1c0800896d7f2aa25c3838acdf1
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.1.0, ajv-keywords@npm:^3.4.1, ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 0c57a47cbd656e8cdfd99d7c2264de5868918ffa207c8d7a72a7f63379d4333254b2ba03d69e3c035e996a3fd3eb6d5725d7a1597cca10694296e32510546360
  languageName: node
  linkType: hard

"ajv@npm:^6.1.0, ajv@npm:^6.10.2, ajv@npm:^6.12.3, ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"alphanum-sort@npm:^1.0.0":
  version: 1.0.2
  resolution: "alphanum-sort@npm:1.0.2"
  checksum: 2944e7ae4d36328a2df071017c01cd1d06ef6a5d87c8fbbeffd9075d42f4da48051bde79a5e3d863b98f37778c76e8eebd4353c1e7bbba2480382096953ec2b8
  languageName: node
  linkType: hard

"ansi-colors@npm:^3.0.0":
  version: 3.2.4
  resolution: "ansi-colors@npm:3.2.4"
  checksum: 1785466547bac3b1cb8055325a415c8c946a818669da4fd3d1247cab7617b845b221c2ae04756277074d278b52d90efd67f73d2dd927c7a0d1a10395c1b7665b
  languageName: node
  linkType: hard

"ansi-html-community@npm:0.0.8":
  version: 0.0.8
  resolution: "ansi-html-community@npm:0.0.8"
  bin:
    ansi-html: bin/ansi-html
  checksum: 45d3a6f0b4f10b04fdd44bef62972e2470bfd917bf00439471fa7473d92d7cbe31369c73db863cc45dda115cb42527f39e232e9256115534b8ee5806b0caeed4
  languageName: node
  linkType: hard

"ansi-regex@npm:^2.0.0":
  version: 2.1.1
  resolution: "ansi-regex@npm:2.1.1"
  checksum: 78cebaf50bce2cb96341a7230adf28d804611da3ce6bf338efa7b72f06cc6ff648e29f80cd95e582617ba58d5fdbec38abfeed3500a98bce8381a9daec7c548b
  languageName: node
  linkType: hard

"ansi-regex@npm:^4.1.0":
  version: 4.1.1
  resolution: "ansi-regex@npm:4.1.1"
  checksum: d36d34234d077e8770169d980fed7b2f3724bfa2a01da150ccd75ef9707c80e883d27cdf7a0eac2f145ac1d10a785a8a855cffd05b85f778629a0db62e7033da
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^2.2.1":
  version: 2.2.1
  resolution: "ansi-styles@npm:2.2.1"
  checksum: 7c68aed4f1857389e7a12f85537ea5b40d832656babbf511cc7ecd9efc52889b9c3e5653a71a6aade783c3c5e0aa223ad4ff8e83c27ac8a666514e6c79068cab
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.0, ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: ece5a8ef069fcc5298f67e3f4771a663129abd174ea2dfa87923a2be2abf6cd367ef72ac87942da00ce85bd1d651d4cd8595aebdb1b385889b89b205860e977b
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 60f0298ed34c74fef50daab88e8dab786036ed5a7fad02e012ab57e376e0a0b4b29e83b95ea9b5e7d89df762f5f25119b83e00706ecaccb22cfbacee98d74889
  languageName: node
  linkType: hard

"anymatch@npm:^2.0.0":
  version: 2.0.0
  resolution: "anymatch@npm:2.0.0"
  dependencies:
    micromatch: "npm:^3.1.4"
    normalize-path: "npm:^2.1.1"
  checksum: a0d745e52f0233048724b9c9d7b1d8a650f7a50151a0f1d2cce1857b09fd096052d334f8c570cc88596edef8249ae778f767db94025cd00f81e154a37bb7e34e
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"aproba@npm:^1.1.1":
  version: 1.2.0
  resolution: "aproba@npm:1.2.0"
  checksum: 2d34f008c9edfa991f42fe4b667d541d38a474a39ae0e24805350486d76744cd91ee45313283c1d39a055b14026dd0fc4d0cbfc13f210855d59d7e8b5a61dc51
  languageName: node
  linkType: hard

"arch@npm:^2.1.1":
  version: 2.2.0
  resolution: "arch@npm:2.2.0"
  checksum: 4ceaf8d8207817c216ebc4469742052cb0a097bc45d9b7fcd60b7507220da545a28562ab5bdd4dfe87921bb56371a0805da4e10d704e01f93a15f83240f1284c
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"arr-diff@npm:^4.0.0":
  version: 4.0.0
  resolution: "arr-diff@npm:4.0.0"
  checksum: 67b80067137f70c89953b95f5c6279ad379c3ee39f7143578e13bd51580a40066ee2a55da066e22d498dce10f68c2d70056d7823f972fab99dfbf4c78d0bc0f7
  languageName: node
  linkType: hard

"arr-flatten@npm:^1.1.0":
  version: 1.1.0
  resolution: "arr-flatten@npm:1.1.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"arr-union@npm:^3.1.0":
  version: 3.1.0
  resolution: "arr-union@npm:3.1.0"
  checksum: 7d5aa05894e54aa93c77c5726c1dd5d8e8d3afe4f77983c0aa8a14a8a5cbe8b18f0cf4ecaa4ac8c908ef5f744d2cbbdaa83fd6e96724d15fea56cfa7f5efdd51
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 74e1d2d996941c7a1badda9cabb7caab8c449db9086407cad8a1b71d2604cc8abf105db8ca4e02c04579ec58b7be40279ddb09aea4784832984485499f48432d
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-flatten@npm:^2.1.0":
  version: 2.1.2
  resolution: "array-flatten@npm:2.1.2"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-union@npm:^1.0.1, array-union@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-union@npm:1.0.2"
  dependencies:
    array-uniq: "npm:^1.0.1"
  checksum: 18686767c0cfdae8dc4acf5ac119b0f0eacad82b7fcc0aa62cc41f93c5ad406d494b6a6e53d85e52e8f0349b67a4fec815feeb537e95c02510d747bc9a4157c7
  languageName: node
  linkType: hard

"array-uniq@npm:^1.0.1":
  version: 1.0.3
  resolution: "array-uniq@npm:1.0.3"
  checksum: 3acbaf9e6d5faeb1010e2db04ab171b8d265889e46c61762e502979bdc5e55656013726e9a61507de3c82d329a0dc1e8072630a3454b4f2b881cb19ba7fd8aa6
  languageName: node
  linkType: hard

"array-unique@npm:^0.3.2":
  version: 0.3.2
  resolution: "array-unique@npm:0.3.2"
  checksum: dbf4462cdba8a4b85577be07705210b3d35be4b765822a3f52962d907186617638ce15e0603a4fefdcf82f4cbbc9d433f8cbbd6855148a68872fa041b6474121
  languageName: node
  linkType: hard

"array.prototype.reduce@npm:^1.0.6":
  version: 1.0.8
  resolution: "array.prototype.reduce@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-array-method-boxes-properly: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
  checksum: 0a4635f468e9161f51c4a87f80057b8b3c27b0ccc3e40ad7ea77cd1e147f1119f46977b0452f3fa325f543126200f2caf8c1390bd5303edf90d9c1dcd7d5a8a0
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 2f2459caa06ae0f7f615003f9104b01f6435cc803e11bd2a655107d52a1781dc040532dc44d93026b694cc18793993246237423e13a5337e86b43ed604932c06
  languageName: node
  linkType: hard

"asn1.js@npm:^4.10.1":
  version: 4.10.1
  resolution: "asn1.js@npm:4.10.1"
  dependencies:
    bn.js: "npm:^4.0.0"
    inherits: "npm:^2.0.1"
    minimalistic-assert: "npm:^1.0.0"
  checksum: afa7f3ab9e31566c80175a75b182e5dba50589dcc738aa485be42bdd787e2a07246a4b034d481861123cbe646a7656f318f4f1cad2e9e5e808a210d5d6feaa88
  languageName: node
  linkType: hard

"asn1@npm:~0.2.3":
  version: 0.2.6
  resolution: "asn1@npm:0.2.6"
  dependencies:
    safer-buffer: "npm:~2.1.0"
  checksum: 00c8a06c37e548762306bcb1488388d2f76c74c36f70c803f0c081a01d3bdf26090fc088cd812afc5e56a6d49e33765d451a5f8a68ab9c2b087eba65d2e980e0
  languageName: node
  linkType: hard

"assert-plus@npm:1.0.0, assert-plus@npm:^1.0.0":
  version: 1.0.0
  resolution: "assert-plus@npm:1.0.0"
  checksum: b194b9d50c3a8f872ee85ab110784911e696a4d49f7ee6fc5fb63216dedbefd2c55999c70cb2eaeb4cf4a0e0338b44e9ace3627117b5bf0d42460e9132f21b91
  languageName: node
  linkType: hard

"assert@npm:^1.1.1":
  version: 1.5.1
  resolution: "assert@npm:1.5.1"
  dependencies:
    object.assign: "npm:^4.1.4"
    util: "npm:^0.10.4"
  checksum: 836688b928b68b7fc5bbc165443e16a62623d57676a1e8a980a0316f9ae86e5e0a102c63470491bf55a8545e75766303640c0c7ad1cf6bfa5450130396043bbd
  languageName: node
  linkType: hard

"assign-symbols@npm:^1.0.0":
  version: 1.0.0
  resolution: "assign-symbols@npm:1.0.0"
  checksum: 29a654b8a6da6889a190d0d0efef4b1bfb5948fa06cbc245054aef05139f889f2f7c75b989917e3fde853fc4093b88048e4de8578a73a76f113d41bfd66e5775
  languageName: node
  linkType: hard

"async-each@npm:^1.0.1":
  version: 1.0.6
  resolution: "async-each@npm:1.0.6"
  checksum: d4e45e8f077e20e015952c065ceae75f82b30ee2d4a8e56a5c454ae44331aaa009d8c94fe043ba254c177bffae9f6ebeefebb7daf9f7ce4d27fac0274dc328ae
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 669a32c2cb7e45091330c680e92eaeb791bc1d4132d827591e499cd1f776ff5a873e77e5f92d0ce795a8d60f10761dec9ddfe7225a5de680f5d357f67b1aac73
  languageName: node
  linkType: hard

"async-limiter@npm:~1.0.0":
  version: 1.0.1
  resolution: "async-limiter@npm:1.0.1"
  checksum: 0693d378cfe86842a70d4c849595a0bb50dc44c11649640ca982fa90cbfc74e3cc4753b5a0847e51933f2e9c65ce8e05576e75e5e1fd963a086e673735b35969
  languageName: node
  linkType: hard

"async@npm:^3.2.6":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: 36484bb15ceddf07078688d95e27076379cc2f87b10c03b6dd8a83e89475a3c8df5848859dd06a4c95af1e4c16fc973de0171a77f18ea00be899aca2a4f85e70
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"atob@npm:^2.1.2":
  version: 2.1.2
  resolution: "atob@npm:2.1.2"
  bin:
    atob: bin/atob.js
  checksum: ada635b519dc0c576bb0b3ca63a73b50eefacf390abb3f062558342a8d68f2db91d0c8db54ce81b0d89de3b0f000de71f3ae7d761fd7d8cc624278fe443d6c7e
  languageName: node
  linkType: hard

"autoprefixer@npm:^9.5.1":
  version: 9.8.8
  resolution: "autoprefixer@npm:9.8.8"
  dependencies:
    browserslist: "npm:^4.12.0"
    caniuse-lite: "npm:^1.0.30001109"
    normalize-range: "npm:^0.1.2"
    num2fraction: "npm:^1.2.2"
    picocolors: "npm:^0.2.1"
    postcss: "npm:^7.0.32"
    postcss-value-parser: "npm:^4.1.0"
  bin:
    autoprefixer: bin/autoprefixer
  checksum: 9b2688cd0ef7252ae1a565ca935a83ddd5c38b9b4c7bf895f36d88e91dbc36d2e7ccb2d34270e436498d8f372d7320a83af6ceb5d1c3bff8f8cbeb6ff33ac837
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: d07226ef4f87daa01bd0fe80f8f310982e345f372926da2e5296aecc25c41cab440916bbaa4c5e1034b453af3392f67df5961124e4b586df1e99793a1374bdb2
  languageName: node
  linkType: hard

"aws-sign2@npm:~0.7.0":
  version: 0.7.0
  resolution: "aws-sign2@npm:0.7.0"
  checksum: 021d2cc5547d4d9ef1633e0332e746a6f447997758b8b68d6fb33f290986872d2bff5f0c37d5832f41a7229361f093cd81c40898d96ed153493c0fb5cd8575d2
  languageName: node
  linkType: hard

"aws4@npm:^1.8.0":
  version: 1.13.2
  resolution: "aws4@npm:1.13.2"
  checksum: c993d0d186d699f685d73113733695d648ec7d4b301aba2e2a559d0cd9c1c902308cc52f4095e1396b23fddbc35113644e7f0a6a32753636306e41e3ed6f1e79
  languageName: node
  linkType: hard

"axios@npm:^1.7.2":
  version: 1.9.0
  resolution: "axios@npm:1.9.0"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 9371a56886c2e43e4ff5647b5c2c3c046ed0a3d13482ef1d0135b994a628c41fbad459796f101c655e62f0c161d03883454474d2e435b2e021b1924d9f24994c
  languageName: node
  linkType: hard

"babel-code-frame@npm:^6.26.0":
  version: 6.26.0
  resolution: "babel-code-frame@npm:6.26.0"
  dependencies:
    chalk: "npm:^1.1.3"
    esutils: "npm:^2.0.2"
    js-tokens: "npm:^3.0.2"
  checksum: 7fecc128e87578cf1b96e78d2b25e0b260e202bdbbfcefa2eac23b7f8b7b2f7bc9276a14599cde14403cc798cc2a38e428e2cab50b77658ab49228b09ae92473
  languageName: node
  linkType: hard

"babel-loader@npm:^8.0.5":
  version: 8.4.1
  resolution: "babel-loader@npm:8.4.1"
  dependencies:
    find-cache-dir: "npm:^3.3.1"
    loader-utils: "npm:^2.0.4"
    make-dir: "npm:^3.1.0"
    schema-utils: "npm:^2.6.5"
  peerDependencies:
    "@babel/core": ^7.0.0
    webpack: ">=2"
  checksum: efdca9c3ef502af58b923a32123d660c54fd0be125b7b64562c8a43bda0a3a55dac0db32331674104e7e5184061b75c3a0e395b2c5ccdc7cb2125dd9ec7108d2
  languageName: node
  linkType: hard

"babel-plugin-dynamic-import-node@npm:^2.2.0":
  version: 2.3.3
  resolution: "babel-plugin-dynamic-import-node@npm:2.3.3"
  dependencies:
    object.assign: "npm:^4.1.0"
  checksum: 1bd80df981e1fc1aff0cd4e390cf27aaa34f95f7620cd14dff07ba3bad56d168c098233a7d2deb2c9b1dc13643e596a6b94fc608a3412ee3c56e74a25cd2167e
  languageName: node
  linkType: hard

"babel-plugin-module-resolver@npm:3.2.0":
  version: 3.2.0
  resolution: "babel-plugin-module-resolver@npm:3.2.0"
  dependencies:
    find-babel-config: "npm:^1.1.0"
    glob: "npm:^7.1.2"
    pkg-up: "npm:^2.0.0"
    reselect: "npm:^3.0.1"
    resolve: "npm:^1.4.0"
  checksum: d0011e5aa28ed2d36d720e43d9704bcbf7faac143318c7556dea1bd31bf029c2620137aa8c643b4aab6a5d10fba59886d831d1451806e432fb0b3b63b71703dd
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.10":
  version: 0.4.13
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.13"
  dependencies:
    "@babel/compat-data": "npm:^7.22.6"
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: b4a54561606d388e6f9499f39f03171af4be7f9ce2355e737135e40afa7086cf6790fdd706c2e59f488c8fa1f76123d28783708e07ddc84647dca8ed8fb98e06
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.11.0":
  version: 0.11.1
  resolution: "babel-plugin-polyfill-corejs3@npm:0.11.1"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.3"
    core-js-compat: "npm:^3.40.0"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 025f754b6296d84b20200aff63a3c1acdd85e8c621781f2bd27fe2512d0060526192d02329326947c6b29c27cf475fbcfaaff8c51eab1d2bfc7b79086bb64229
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.1":
  version: 0.6.4
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.4"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: ebaaf9e4e53201c02f496d3f686d815e94177b3e55b35f11223b99c60d197a29f907a2e87bbcccced8b7aff22a807fccc1adaf04722864a8e1862c8845ab830a
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base64-js@npm:^1.0.2":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"base@npm:^0.11.1":
  version: 0.11.2
  resolution: "base@npm:0.11.2"
  dependencies:
    cache-base: "npm:^1.0.1"
    class-utils: "npm:^0.3.5"
    component-emitter: "npm:^1.2.1"
    define-property: "npm:^1.0.0"
    isobject: "npm:^3.0.1"
    mixin-deep: "npm:^1.2.0"
    pascalcase: "npm:^0.1.1"
  checksum: 30a2c0675eb52136b05ef496feb41574d9f0bb2d6d677761da579c00a841523fccf07f1dbabec2337b5f5750f428683b8ca60d89e56a1052c4ae1c0cd05de64d
  languageName: node
  linkType: hard

"batch@npm:0.6.1":
  version: 0.6.1
  resolution: "batch@npm:0.6.1"
  checksum: 925a13897b4db80d4211082fe287bcf96d297af38e26448c857cee3e095c9792e3b8f26b37d268812e7f38a589f694609de8534a018b1937d7dc9f84e6b387c5
  languageName: node
  linkType: hard

"bcrypt-pbkdf@npm:^1.0.0":
  version: 1.0.2
  resolution: "bcrypt-pbkdf@npm:1.0.2"
  dependencies:
    tweetnacl: "npm:^0.14.3"
  checksum: ddfe85230b32df25aeebfdccfbc61d3bc493ace49c884c9c68575de1f5dcf733a5d7de9def3b0f318b786616b8d85bad50a28b1da1750c43e0012c93badcc148
  languageName: node
  linkType: hard

"bfj@npm:^6.1.1":
  version: 6.1.2
  resolution: "bfj@npm:6.1.2"
  dependencies:
    bluebird: "npm:^3.5.5"
    check-types: "npm:^8.0.3"
    hoopy: "npm:^0.1.4"
    tryer: "npm:^1.0.1"
  checksum: b3aa02a4ec4d2dea3f64e6c4d7af67bda580574fafef8f819acc3e54a2f5e8b553ad8250f83d5e02d837d96be19cce855560b742635f2875f1a0ceceb304dfad
  languageName: node
  linkType: hard

"big.js@npm:^3.1.3":
  version: 3.2.0
  resolution: "big.js@npm:3.2.0"
  checksum: de0b8e275171060a37846b521e8ebfe077c650532306c2470474da6720feb04351cc8588ef26088756b224923782946ae67e817b90122cc85692bbda7ccd2d0d
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: 230520f1ff920b2d2ce3e372d77a33faa4fa60d802fe01ca4ffbc321ee06023fe9a741ac02793ee778040a16b7e497f7d60c504d1c402b8fdab6f03bb785a25f
  languageName: node
  linkType: hard

"binary-extensions@npm:^1.0.0":
  version: 1.13.1
  resolution: "binary-extensions@npm:1.13.1"
  checksum: 2d616938ac23d828ec3fbe0dea429b566fd2c137ddc38f166f16561ccd58029deac3fa9fddb489ab13d679c8fb5f1bd0e82824041299e5e39d8dd3cc68fbb9f9
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"bindings@npm:^1.5.0":
  version: 1.5.0
  resolution: "bindings@npm:1.5.0"
  dependencies:
    file-uri-to-path: "npm:1.0.0"
  checksum: 3dab2491b4bb24124252a91e656803eac24292473e56554e35bbfe3cc1875332cfa77600c3bac7564049dc95075bf6fcc63a4609920ff2d64d0fe405fcf0d4ba
  languageName: node
  linkType: hard

"bluebird@npm:^3.1.1, bluebird@npm:^3.5.1, bluebird@npm:^3.5.5":
  version: 3.7.2
  resolution: "bluebird@npm:3.7.2"
  checksum: 680de03adc54ff925eaa6c7bb9a47a0690e8b5de60f4792604aae8ed618c65e6b63a7893b57ca924beaf53eee69c5af4f8314148c08124c550fe1df1add897d2
  languageName: node
  linkType: hard

"bn.js@npm:^4.0.0, bn.js@npm:^4.1.0, bn.js@npm:^4.11.9":
  version: 4.12.2
  resolution: "bn.js@npm:4.12.2"
  checksum: 09a249faa416a9a1ce68b5f5ec8bbca87fe54e5dd4ef8b1cc8a4969147b80035592bddcb1e9cc814c3ba79e573503d5c5178664b722b509fb36d93620dba9b57
  languageName: node
  linkType: hard

"bn.js@npm:^5.2.1":
  version: 5.2.2
  resolution: "bn.js@npm:5.2.2"
  checksum: cb97827d476aab1a0194df33cd84624952480d92da46e6b4a19c32964aa01553a4a613502396712704da2ec8f831cf98d02e74ca03398404bd78a037ba93f2ab
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.13.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 0a9a93b7518f222885498dcecaad528cf010dd109b071bf471c93def4bfe30958b83e03496eb9c1ad4896db543d999bb62be1a3087294162a88cfa1b42c16310
  languageName: node
  linkType: hard

"bonjour@npm:^3.5.0":
  version: 3.5.0
  resolution: "bonjour@npm:3.5.0"
  dependencies:
    array-flatten: "npm:^2.1.0"
    deep-equal: "npm:^1.0.1"
    dns-equal: "npm:^1.0.0"
    dns-txt: "npm:^2.0.2"
    multicast-dns: "npm:^6.0.1"
    multicast-dns-service-types: "npm:^1.1.0"
  checksum: 0be7c4cd96df563571973706226e750f6feeacd81d01c1ade11247eb3a7e14846af49cffe397ab970059b828dd89f694f456e22bca4ca315a7f0326e9303e241
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0, boolbase@npm:~1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: e4b53deb4f2b85c52be0e21a273f2045c7b6a6ea002b0e139c744cb6f95e9ec044439a52883b0d74dedd1ff3da55ed140cfdddfed7fb0cccbed373de5dce1bcf
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 6d117a4c793488af86b83172deb6af143e94c17bc53b0b3cec259733923b4ca84679d506ac261f4ba3c7ed37c46018e2ff442f9ce453af8643ecd64f4a54e6cf
  languageName: node
  linkType: hard

"braces@npm:^2.3.1, braces@npm:^2.3.2":
  version: 2.3.2
  resolution: "braces@npm:2.3.2"
  dependencies:
    arr-flatten: "npm:^1.1.0"
    array-unique: "npm:^0.3.2"
    extend-shallow: "npm:^2.0.1"
    fill-range: "npm:^4.0.0"
    isobject: "npm:^3.0.1"
    repeat-element: "npm:^1.1.2"
    snapdragon: "npm:^0.8.1"
    snapdragon-node: "npm:^2.0.1"
    split-string: "npm:^3.0.2"
    to-regex: "npm:^3.0.1"
  checksum: 72b27ea3ea2718f061c29e70fd6e17606e37c65f5801abddcf0b0052db1de7d60f3bf92cfc220ab57b44bd0083a5f69f9d03b3461d2816cfe9f9398207acc728
  languageName: node
  linkType: hard

"braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"brorand@npm:^1.0.1, brorand@npm:^1.1.0":
  version: 1.1.0
  resolution: "brorand@npm:1.1.0"
  checksum: 6f366d7c4990f82c366e3878492ba9a372a73163c09871e80d82fb4ae0d23f9f8924cb8a662330308206e6b3b76ba1d528b4601c9ef73c2166b440b2ea3b7571
  languageName: node
  linkType: hard

"browserify-aes@npm:^1.0.4, browserify-aes@npm:^1.2.0":
  version: 1.2.0
  resolution: "browserify-aes@npm:1.2.0"
  dependencies:
    buffer-xor: "npm:^1.0.3"
    cipher-base: "npm:^1.0.0"
    create-hash: "npm:^1.1.0"
    evp_bytestokey: "npm:^1.0.3"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  checksum: 967f2ae60d610b7b252a4cbb55a7a3331c78293c94b4dd9c264d384ca93354c089b3af9c0dd023534efdc74ffbc82510f7ad4399cf82bc37bc07052eea485f18
  languageName: node
  linkType: hard

"browserify-cipher@npm:^1.0.1":
  version: 1.0.1
  resolution: "browserify-cipher@npm:1.0.1"
  dependencies:
    browserify-aes: "npm:^1.0.4"
    browserify-des: "npm:^1.0.0"
    evp_bytestokey: "npm:^1.0.0"
  checksum: aa256dcb42bc53a67168bbc94ab85d243b0a3b56109dee3b51230b7d010d9b78985ffc1fb36e145c6e4db151f888076c1cfc207baf1525d3e375cbe8187fe27d
  languageName: node
  linkType: hard

"browserify-des@npm:^1.0.0":
  version: 1.0.2
  resolution: "browserify-des@npm:1.0.2"
  dependencies:
    cipher-base: "npm:^1.0.1"
    des.js: "npm:^1.0.0"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.1.2"
  checksum: 943eb5d4045eff80a6cde5be4e5fbb1f2d5002126b5a4789c3c1aae3cdddb1eb92b00fb92277f512288e5c6af330730b1dbabcf7ce0923e749e151fcee5a074d
  languageName: node
  linkType: hard

"browserify-rsa@npm:^4.0.0, browserify-rsa@npm:^4.1.0":
  version: 4.1.1
  resolution: "browserify-rsa@npm:4.1.1"
  dependencies:
    bn.js: "npm:^5.2.1"
    randombytes: "npm:^2.1.0"
    safe-buffer: "npm:^5.2.1"
  checksum: b650ee1192e3d7f3d779edc06dd96ed8720362e72ac310c367b9d7fe35f7e8dbb983c1829142b2b3215458be8bf17c38adc7224920843024ed8cf39e19c513c0
  languageName: node
  linkType: hard

"browserify-sign@npm:^4.2.3":
  version: 4.2.3
  resolution: "browserify-sign@npm:4.2.3"
  dependencies:
    bn.js: "npm:^5.2.1"
    browserify-rsa: "npm:^4.1.0"
    create-hash: "npm:^1.2.0"
    create-hmac: "npm:^1.1.7"
    elliptic: "npm:^6.5.5"
    hash-base: "npm:~3.0"
    inherits: "npm:^2.0.4"
    parse-asn1: "npm:^5.1.7"
    readable-stream: "npm:^2.3.8"
    safe-buffer: "npm:^5.2.1"
  checksum: 30c0eba3f5970a20866a4d3fbba2c5bd1928cd24f47faf995f913f1499214c6f3be14bb4d6ec1ab5c6cafb1eca9cb76ba1c2e1c04ed018370634d4e659c77216
  languageName: node
  linkType: hard

"browserify-zlib@npm:^0.2.0":
  version: 0.2.0
  resolution: "browserify-zlib@npm:0.2.0"
  dependencies:
    pako: "npm:~1.0.5"
  checksum: 9ab10b6dc732c6c5ec8ebcbe5cb7fe1467f97402c9b2140113f47b5f187b9438f93a8e065d8baf8b929323c18324fbf1105af479ee86d9d36cab7d7ef3424ad9
  languageName: node
  linkType: hard

"browserslist@npm:^4.0.0, browserslist@npm:^4.12.0, browserslist@npm:^4.24.0, browserslist@npm:^4.24.4, browserslist@npm:^4.3.4, browserslist@npm:^4.5.4":
  version: 4.24.5
  resolution: "browserslist@npm:4.24.5"
  dependencies:
    caniuse-lite: "npm:^1.0.30001716"
    electron-to-chromium: "npm:^1.5.149"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: f4c1ce1a7d8fdfab5e5b88bb6e93d09e8a883c393f86801537a252da0362dbdcde4dbd97b318246c5d84c6607b2f6b47af732c1b000d6a8a881ee024bad29204
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer-indexof@npm:^1.0.0":
  version: 1.1.1
  resolution: "buffer-indexof@npm:1.1.1"
  checksum: 67906b0a9892854e24ac717ef823c3b19790c653a8b1902835bbf3c3c46ea8d99f0680a92f7394fc5acbbecb3385775ccd504ea00587d2d67d8dfaadd460eeae
  languageName: node
  linkType: hard

"buffer-xor@npm:^1.0.3":
  version: 1.0.3
  resolution: "buffer-xor@npm:1.0.3"
  checksum: fd269d0e0bf71ecac3146187cfc79edc9dbb054e2ee69b4d97dfb857c6d997c33de391696d04bdd669272751fa48e7872a22f3a6c7b07d6c0bc31dbe02a4075c
  languageName: node
  linkType: hard

"buffer@npm:^4.3.0":
  version: 4.9.2
  resolution: "buffer@npm:4.9.2"
  dependencies:
    base64-js: "npm:^1.0.2"
    ieee754: "npm:^1.1.4"
    isarray: "npm:^1.0.0"
  checksum: dc443d7e7caab23816b58aacdde710b72f525ad6eecd7d738fcaa29f6d6c12e8d9c13fed7219fd502be51ecf0615f5c077d4bdc6f9308dde2e53f8e5393c5b21
  languageName: node
  linkType: hard

"bufferutil@npm:^4.0.1":
  version: 4.0.9
  resolution: "bufferutil@npm:4.0.9"
  dependencies:
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.3.0"
  checksum: f8a93279fc9bdcf32b42eba97edc672b39ca0fe5c55a8596099886cffc76ea9dd78e0f6f51ecee3b5ee06d2d564aa587036b5d4ea39b8b5ac797262a363cdf7d
  languageName: node
  linkType: hard

"builtin-status-codes@npm:^3.0.0":
  version: 3.0.0
  resolution: "builtin-status-codes@npm:3.0.0"
  checksum: c37bbba11a34c4431e56bd681b175512e99147defbe2358318d8152b3a01df7bf25e0305873947e5b350073d5ef41a364a22b37e48f1fb6d2fe6d5286a0f348c
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^10.0.4":
  version: 10.0.4
  resolution: "cacache@npm:10.0.4"
  dependencies:
    bluebird: "npm:^3.5.1"
    chownr: "npm:^1.0.1"
    glob: "npm:^7.1.2"
    graceful-fs: "npm:^4.1.11"
    lru-cache: "npm:^4.1.1"
    mississippi: "npm:^2.0.0"
    mkdirp: "npm:^0.5.1"
    move-concurrently: "npm:^1.0.1"
    promise-inflight: "npm:^1.0.1"
    rimraf: "npm:^2.6.2"
    ssri: "npm:^5.2.4"
    unique-filename: "npm:^1.1.0"
    y18n: "npm:^4.0.0"
  checksum: 4c82d037ecc0ef87f58f96ecd3662bdb24aaedd18fa96d749363bc20dee3ac9f623e2c41e09bf894a3f62de1612a8cea8ddae22563a91ce2ef5cb69fe4cf54dd
  languageName: node
  linkType: hard

"cacache@npm:^12.0.2":
  version: 12.0.4
  resolution: "cacache@npm:12.0.4"
  dependencies:
    bluebird: "npm:^3.5.5"
    chownr: "npm:^1.1.1"
    figgy-pudding: "npm:^3.5.1"
    glob: "npm:^7.1.4"
    graceful-fs: "npm:^4.1.15"
    infer-owner: "npm:^1.0.3"
    lru-cache: "npm:^5.1.1"
    mississippi: "npm:^3.0.0"
    mkdirp: "npm:^0.5.1"
    move-concurrently: "npm:^1.0.1"
    promise-inflight: "npm:^1.0.1"
    rimraf: "npm:^2.6.3"
    ssri: "npm:^6.0.1"
    unique-filename: "npm:^1.1.1"
    y18n: "npm:^4.0.0"
  checksum: b4b0aa49e3fbd3ca92f71bc62923e4afce31fd687b31d5ba524b2a54b36e96a8b027165599307dda5e4a6f7268cc951b77ca170efa00c1b72761f9daae51fdfb
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"cache-base@npm:^1.0.1":
  version: 1.0.1
  resolution: "cache-base@npm:1.0.1"
  dependencies:
    collection-visit: "npm:^1.0.0"
    component-emitter: "npm:^1.2.1"
    get-value: "npm:^2.0.6"
    has-value: "npm:^1.0.0"
    isobject: "npm:^3.0.1"
    set-value: "npm:^2.0.0"
    to-object-path: "npm:^0.3.0"
    union-value: "npm:^1.0.0"
    unset-value: "npm:^1.0.0"
  checksum: a7142e25c73f767fa520957dcd179b900b86eac63b8cfeaa3b2a35e18c9ca5968aa4e2d2bed7a3e7efd10f13be404344cfab3a4156217e71f9bdb95940bb9c8c
  languageName: node
  linkType: hard

"cache-loader@npm:^2.0.1":
  version: 2.0.1
  resolution: "cache-loader@npm:2.0.1"
  dependencies:
    loader-utils: "npm:^1.1.0"
    mkdirp: "npm:^0.5.1"
    neo-async: "npm:^2.6.0"
    normalize-path: "npm:^3.0.0"
    schema-utils: "npm:^1.0.0"
  peerDependencies:
    webpack: ^4.0.0
  checksum: 6e00b9e678cf4fd56f6e2b10ffe09355848b83d740eb1512e12bfffc2d0568670cd6addc5c62cd536c505dce5dab58238302530c31a53146cffa979bf0c6793a
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: a13819be0681d915144467741b69875ae5f4eba8961eb0bf322aab63ec87f8250eb6d6b0dcbb2e1349876412a56129ca338592b3829ef4343527f5f18a0752d4
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"call-me-maybe@npm:^1.0.1":
  version: 1.0.2
  resolution: "call-me-maybe@npm:1.0.2"
  checksum: 8eff5dbb61141ebb236ed71b4e9549e488bcb5451c48c11e5667d5c75b0532303788a1101e6978cafa2d0c8c1a727805599c2741e3e0982855c9f1d78cd06c9f
  languageName: node
  linkType: hard

"caller-callsite@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-callsite@npm:2.0.0"
  dependencies:
    callsites: "npm:^2.0.0"
  checksum: a00ca91280e10ee2321de21dda6c168e427df7a63aeaca027ea45e3e466ac5e1a5054199f6547ba1d5a513d3b6b5933457266daaa47f8857fb532a343ee6b5e1
  languageName: node
  linkType: hard

"caller-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-path@npm:2.0.0"
  dependencies:
    caller-callsite: "npm:^2.0.0"
  checksum: 029b5b2c557d831216305c3218e9ff30fa668be31d58dd08088f74c8eabc8362c303e0908b3a93abb25ba10e3a5bfc9cff5eb7fab6ab9cf820e3b160ccb67581
  languageName: node
  linkType: hard

"callsites@npm:^2.0.0":
  version: 2.0.0
  resolution: "callsites@npm:2.0.0"
  checksum: 13bff4fee946e6020b37e76284e95e24aa239c9e34ac4f3451e4c5330fca6f2f962e1d1ab69e4da7940e1fce135107a2b2b98c01d62ea33144350fc89dc5494e
  languageName: node
  linkType: hard

"camel-case@npm:3.0.x":
  version: 3.0.0
  resolution: "camel-case@npm:3.0.0"
  dependencies:
    no-case: "npm:^2.2.0"
    upper-case: "npm:^1.1.1"
  checksum: 491c6bbf986b9d8355e12cca6beb719b44c2fe96e8526c09958a1b4e0dbb081a82ea59c13b5a6ccf9158ce5979cbe56a8a10d7322bfeed2d84725c6b89d8f934
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 92ff9b443bfe8abb15f2b1513ca182d16126359ad4f955ebc83dc4ddcc4ef3fdd2c078bc223f2673dc223488e75c99b16cc4d056624374b799e6a1555cf61b23
  languageName: node
  linkType: hard

"caniuse-api@npm:^3.0.0":
  version: 3.0.0
  resolution: "caniuse-api@npm:3.0.0"
  dependencies:
    browserslist: "npm:^4.0.0"
    caniuse-lite: "npm:^1.0.0"
    lodash.memoize: "npm:^4.1.2"
    lodash.uniq: "npm:^4.5.0"
  checksum: 60f9e85a3331e6d761b1b03eec71ca38ef7d74146bece34694853033292156b815696573ed734b65583acf493e88163618eda915c6c826d46a024c71a9572b4c
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.0, caniuse-lite@npm:^1.0.30001109, caniuse-lite@npm:^1.0.30001716":
  version: 1.0.30001718
  resolution: "caniuse-lite@npm:1.0.30001718"
  checksum: 67f9ad09bc16443e28d14f265d6e468480cd8dc1900d0d8b982222de80c699c4f2306599c3da8a3fa7139f110d4b30d49dbac78f215470f479abb6ffe141d5d3
  languageName: node
  linkType: hard

"case-sensitive-paths-webpack-plugin@npm:^2.2.0":
  version: 2.4.0
  resolution: "case-sensitive-paths-webpack-plugin@npm:2.4.0"
  checksum: 310dab619b661a7fa44ed773870be6d6d7373faff6953ad92720f9553e2579e46dda5b9a79eae6d25ff3733cc15aa466b96e5811af16213f23c115aa220b4ab4
  languageName: node
  linkType: hard

"caseless@npm:~0.12.0":
  version: 0.12.0
  resolution: "caseless@npm:0.12.0"
  checksum: ccf64bcb6c0232cdc5b7bd91ddd06e23a4b541f138336d4725233ac538041fb2f29c2e86c3c4a7a61ef990b665348db23a047060b9414c3a6603e9fa61ad4626
  languageName: node
  linkType: hard

"chalk@npm:^1.1.3":
  version: 1.1.3
  resolution: "chalk@npm:1.1.3"
  dependencies:
    ansi-styles: "npm:^2.2.1"
    escape-string-regexp: "npm:^1.0.2"
    has-ansi: "npm:^2.0.0"
    strip-ansi: "npm:^3.0.0"
    supports-color: "npm:^2.0.0"
  checksum: 28c3e399ec286bb3a7111fd4225ebedb0d7b813aef38a37bca7c498d032459c265ef43404201d5fbb8d888d29090899c95335b4c0cda13e8b126ff15c541cef8
  languageName: node
  linkType: hard

"chalk@npm:^2.0.1, chalk@npm:^2.4.1, chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: e6543f02ec877732e3a2d1c3c3323ddb4d39fbab687c23f526e25bd4c6a9bf3b83a696e8c769d078e04e5754921648f7821b2a2acfd16c550435fd630026e073
  languageName: node
  linkType: hard

"chalk@npm:^3.0.0":
  version: 3.0.0
  resolution: "chalk@npm:3.0.0"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: ee650b0a065b3d7a6fda258e75d3a86fc8e4effa55871da730a9e42ccb035bf5fd203525e5a1ef45ec2582ecc4f65b47eb11357c526b84dd29a14fb162c414d2
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"check-types@npm:^8.0.3":
  version: 8.0.3
  resolution: "check-types@npm:8.0.3"
  checksum: f55dd9a1cdff7f1a663feb034f62209b7697e94c6192c7cfa15d5e84e8dd860b863ab9ac435acc161e29d5f1b7136f6f94d99e02d87f60457199d478822b3903
  languageName: node
  linkType: hard

"chinese-to-pinyin@npm:^1.0.2":
  version: 1.3.1
  resolution: "chinese-to-pinyin@npm:1.3.1"
  checksum: ac865a22ae4c32a85ccb38a603fd9bfdcdefc3360f18cd1e55c2c5cd2f3babf9304efbd27bc2b5cd4dd0ef34ff2d613a3aa2257c5ae03a8dd70d1f01e0dd2922
  languageName: node
  linkType: hard

"chokidar@npm:^2.1.8":
  version: 2.1.8
  resolution: "chokidar@npm:2.1.8"
  dependencies:
    anymatch: "npm:^2.0.0"
    async-each: "npm:^1.0.1"
    braces: "npm:^2.3.2"
    fsevents: "npm:^1.2.7"
    glob-parent: "npm:^3.1.0"
    inherits: "npm:^2.0.3"
    is-binary-path: "npm:^1.0.0"
    is-glob: "npm:^4.0.0"
    normalize-path: "npm:^3.0.0"
    path-is-absolute: "npm:^1.0.0"
    readdirp: "npm:^2.2.1"
    upath: "npm:^1.1.1"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 5631cc00080224f9482cf5418dcbea111aec02fa8d81a8cfe37e47b9cf36089e071de52d503647e3a821a01426a40adc926ba899f657af86a51b8f8d4eef12a7
  languageName: node
  linkType: hard

"chokidar@npm:^3.4.1":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chownr@npm:^1.0.1, chownr@npm:^1.1.1":
  version: 1.1.4
  resolution: "chownr@npm:1.1.4"
  checksum: ed57952a84cc0c802af900cf7136de643d3aba2eecb59d29344bc2f3f9bf703a301b9d84cdc71f82c3ffc9ccde831b0d92f5b45f91727d6c9da62f23aef9d9db
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.4
  resolution: "chrome-trace-event@npm:1.0.4"
  checksum: 3058da7a5f4934b87cf6a90ef5fb68ebc5f7d06f143ed5a4650208e5d7acae47bc03ec844b29fbf5ba7e46e8daa6acecc878f7983a4f4bb7271593da91e61ff5
  languageName: node
  linkType: hard

"cipher-base@npm:^1.0.0, cipher-base@npm:^1.0.1, cipher-base@npm:^1.0.3":
  version: 1.0.6
  resolution: "cipher-base@npm:1.0.6"
  dependencies:
    inherits: "npm:^2.0.4"
    safe-buffer: "npm:^5.2.1"
  checksum: f73268e0ee6585800875d9748f2a2377ae7c2c3375cba346f75598ac6f6bc3a25dec56e984a168ced1a862529ffffe615363f750c40349039d96bd30fba0fca8
  languageName: node
  linkType: hard

"class-utils@npm:^0.3.5":
  version: 0.3.6
  resolution: "class-utils@npm:0.3.6"
  dependencies:
    arr-union: "npm:^3.1.0"
    define-property: "npm:^0.2.5"
    isobject: "npm:^3.0.0"
    static-extend: "npm:^0.1.1"
  checksum: d44f4afc7a3e48dba4c2d3fada5f781a1adeeff371b875c3b578bc33815c6c29d5d06483c2abfd43a32d35b104b27b67bfa39c2e8a422fa858068bd756cfbd42
  languageName: node
  linkType: hard

"clean-css@npm:4.2.x":
  version: 4.2.4
  resolution: "clean-css@npm:4.2.4"
  dependencies:
    source-map: "npm:~0.6.0"
  checksum: 0e41795fdc9d65e5e17a3b0016d90bf2a653e3a680829b5bcebdbab48604cfe36d96d8af6346338d2c2aca8aa9af024ac4fb752ac3eb5b71bef68a34a129b58a
  languageName: node
  linkType: hard

"cli-cursor@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-cursor@npm:2.1.0"
  dependencies:
    restore-cursor: "npm:^2.0.0"
  checksum: 09ee6d8b5b818d840bf80ec9561eaf696672197d3a02a7daee2def96d5f52ce6e0bbe7afca754ccf14f04830b5a1b4556273e983507d5029f95bba3016618eda
  languageName: node
  linkType: hard

"cli-highlight@npm:^2.1.0":
  version: 2.1.11
  resolution: "cli-highlight@npm:2.1.11"
  dependencies:
    chalk: "npm:^4.0.0"
    highlight.js: "npm:^10.7.1"
    mz: "npm:^2.4.0"
    parse5: "npm:^5.1.1"
    parse5-htmlparser2-tree-adapter: "npm:^6.0.0"
    yargs: "npm:^16.0.0"
  bin:
    highlight: bin/highlight
  checksum: b5b4af3b968aa9df77eee449a400fbb659cf47c4b03a395370bd98d5554a00afaa5819b41a9a8a1ca0d37b0b896a94e57c65289b37359a25b700b1f56eb04852
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.0.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 907a1c227ddf0d7a101e7ab8b300affc742ead4b4ebe920a5bf1bc6d45dce2958fcd195eb28fa25275062fe6fa9b109b93b63bc8033396ed3bcb50297008b3a3
  languageName: node
  linkType: hard

"clipboardy@npm:^2.0.0":
  version: 2.3.0
  resolution: "clipboardy@npm:2.3.0"
  dependencies:
    arch: "npm:^2.1.1"
    execa: "npm:^1.0.0"
    is-wsl: "npm:^2.1.1"
  checksum: 171c7d216dbec50213e35796740eaf79e39fb3442b7a8caf7414c2aed1da14e4d040696126c467325641612267a3dd43740d2ec29719fdcfb62065c6a2f91860
  languageName: node
  linkType: hard

"cliui@npm:^5.0.0":
  version: 5.0.0
  resolution: "cliui@npm:5.0.0"
  dependencies:
    string-width: "npm:^3.1.0"
    strip-ansi: "npm:^5.2.0"
    wrap-ansi: "npm:^5.1.0"
  checksum: 76142bf306965850a71efd10c9755bd7f447c7c20dd652e1c1ce27d987f862a3facb3cceb2909cef6f0cb363646ee7a1735e3dfdd49f29ed16d733d33e15e2f8
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 6035f5daf7383470cef82b3d3db00bec70afb3423538c50394386ffbbab135e26c3689c41791f911fa71b62d13d3863c712fdd70f0fbdffd938a1e6fd09aac00
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: 2176952b3649293473999a95d7bebfc9dc96410f6cbd3d2595cf12fd401f63a4bf41a7adbfd3ab2ff09ed60cb9870c58c6acdd18b87767366fabfc163700f13b
  languageName: node
  linkType: hard

"clone@npm:^2.1.1":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: ed0601cd0b1606bc7d82ee7175b97e68d1dd9b91fd1250a3617b38d34a095f8ee0431d40a1a611122dcccb4f93295b4fdb94942aa763392b5fe44effa50c2d5e
  languageName: node
  linkType: hard

"coa@npm:^2.0.2":
  version: 2.0.2
  resolution: "coa@npm:2.0.2"
  dependencies:
    "@types/q": "npm:^1.5.1"
    chalk: "npm:^2.4.1"
    q: "npm:^1.1.2"
  checksum: 0264392e3b691a8551e619889f3e67558b4f755eeb09d67625032a25c37634731e778fabbd9d14df6477d6ae770e30ea9405d18e515b2ec492b0eb90bb8d7f43
  languageName: node
  linkType: hard

"collection-visit@npm:^1.0.0":
  version: 1.0.0
  resolution: "collection-visit@npm:1.0.0"
  dependencies:
    map-visit: "npm:^1.0.0"
    object-visit: "npm:^1.0.0"
  checksum: add72a8d1c37cb90e53b1aaa2c31bf1989bfb733f0b02ce82c9fa6828c7a14358dba2e4f8e698c02f69e424aeccae1ffb39acdeaf872ade2f41369e84a2fcf8a
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0, color-convert@npm:^1.9.3":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: 5ad3c534949a8c68fca8fbc6f09068f435f0ad290ab8b2f76841b9e6af7e0bb57b98cb05b0e19fe33f5d91e5a8611ad457e5f69e0a484caad1f7487fd0e8253c
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 566a3d42cca25b9b3cd5528cd7754b8e89c0eb646b7f214e8e2eaddb69994ac5f0557d9c175eb5d8f0ad73531140d9c47525085ee752a91a2ab15ab459caf6d6
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-string@npm:^1.6.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: b0bfd74c03b1f837f543898b512f5ea353f71630ccdd0d66f83028d1f0924a7d4272deb278b9aef376cacf1289b522ac3fb175e99895283645a2dc3a33af2404
  languageName: node
  linkType: hard

"color@npm:^3.0.0":
  version: 3.2.1
  resolution: "color@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.3"
    color-string: "npm:^1.6.0"
  checksum: 39345d55825884c32a88b95127d417a2c24681d8b57069413596d9fcbb721459ef9d9ec24ce3e65527b5373ce171b73e38dbcd9c830a52a6487e7f37bf00e83c
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.6, combined-stream@npm:^1.0.8, combined-stream@npm:~1.0.6":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:2.17.x":
  version: 2.17.1
  resolution: "commander@npm:2.17.1"
  checksum: b10453ca205d5a794c5e639dbc54bf4eaec61a204d56693b8082e9000744df13a47840f7d10ae2fc6fc046e2d71bb7c67987dff5b77f862064e187cf88beac3f
  languageName: node
  linkType: hard

"commander@npm:^2.18.0, commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"commander@npm:~2.19.0":
  version: 2.19.0
  resolution: "commander@npm:2.19.0"
  checksum: 4cec19989ea492a8b7ebd26e854111b5921641ad737abab64b740d094d7f332f5e210423ca8a31ec6ef988f1e8e6ae1379640160f2a68e9b4ee89c912ac8bd3f
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 33a124960e471c25ee19280c9ce31ccc19574b566dc514fe4f4ca4c34fa8b0b57cf437671f5de380e11353ea9426213fca17687dd2ef03134fea2dbc53809fd6
  languageName: node
  linkType: hard

"component-emitter@npm:^1.2.1":
  version: 1.3.1
  resolution: "component-emitter@npm:1.3.1"
  checksum: e4900b1b790b5e76b8d71b328da41482118c0f3523a516a41be598dc2785a07fd721098d9bf6e22d89b19f4fa4e1025160dc00317ea111633a3e4f75c2b86032
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: "npm:>= 1.43.0 < 2"
  checksum: 8a03712bc9f5b9fe530cc5a79e164e665550d5171a64575d7dcf3e0395d7b4afa2d79ab176c61b5b596e28228b350dd07c1a2a6ead12fd81d1b6cd632af2fef7
  languageName: node
  linkType: hard

"compression@npm:^1.7.4":
  version: 1.8.0
  resolution: "compression@npm:1.8.0"
  dependencies:
    bytes: "npm:3.1.2"
    compressible: "npm:~2.0.18"
    debug: "npm:2.6.9"
    negotiator: "npm:~0.6.4"
    on-headers: "npm:~1.0.2"
    safe-buffer: "npm:5.2.1"
    vary: "npm:~1.1.2"
  checksum: 804d3c8430939f4fd88e5128333f311b4035f6425a7f2959d74cfb5c98ef3a3e3e18143208f3f9d0fcae4cd3bcf3d2fbe525e0fcb955e6e146e070936f025a24
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"concat-stream@npm:^1.5.0":
  version: 1.6.2
  resolution: "concat-stream@npm:1.6.2"
  dependencies:
    buffer-from: "npm:^1.0.0"
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^2.2.2"
    typedarray: "npm:^0.0.6"
  checksum: 2e9864e18282946dabbccb212c5c7cec0702745e3671679eb8291812ca7fd12023f7d8cb36493942a62f770ac96a7f90009dc5c82ad69893438371720fa92617
  languageName: node
  linkType: hard

"connect-history-api-fallback@npm:^1.6.0":
  version: 1.6.0
  resolution: "connect-history-api-fallback@npm:1.6.0"
  checksum: 6d59c68070fcb2f6d981992f88d050d7544e8e1af6600c23ad680d955e316216794a742a1669d1f14ed5171fc628b916f8a4e15c5a1e55bffc8ccc60bfeb0b2c
  languageName: node
  linkType: hard

"console-browserify@npm:^1.1.0":
  version: 1.2.0
  resolution: "console-browserify@npm:1.2.0"
  checksum: 89b99a53b7d6cee54e1e64fa6b1f7ac24b844b4019c5d39db298637e55c1f4ffa5c165457ad984864de1379df2c8e1886cbbdac85d9dbb6876a9f26c3106f226
  languageName: node
  linkType: hard

"consolidate@npm:^0.15.1":
  version: 0.15.1
  resolution: "consolidate@npm:0.15.1"
  dependencies:
    bluebird: "npm:^3.1.1"
  checksum: 02dfbab0a8d5452b74c42dee81526b26a42350ed333575c4f8f099957d02a2dbc92a1f89103b85e83b61371e08a16113ebcddbb38eded53402302e0748f608e1
  languageName: node
  linkType: hard

"constants-browserify@npm:^1.0.0":
  version: 1.0.0
  resolution: "constants-browserify@npm:1.0.0"
  checksum: ab49b1d59a433ed77c964d90d19e08b2f77213fb823da4729c0baead55e3c597f8f97ebccfdfc47bd896d43854a117d114c849a6f659d9986420e97da0f83ac5
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: bac0316ebfeacb8f381b38285dc691c9939bf0a78b0b7c2d5758acadad242d04783cee5337ba7d12a565a19075af1b3c11c728e1e4946de73c6ff7ce45f3f1bb
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: b76ebed15c000aee4678c3707e0860cb6abd4e680a598c0a26e17f0bfae723ec9cc2802f0ff1bc6e4d80603719010431d2231018373d4dde10f9ccff9dadf5af
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: b36fd0d4e3fef8456915fcf7742e58fbfcc12a17a018e0eb9501c9d5ef6893b596466f03b0564b81af29ff2538fd0aa4b9d54fe5ccbfb4c90ea50ad29fe2d221
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: 5de60c67a410e7c8dc8a46a4b72eb0fe925871d057c9a5d2c0e8145c4270a4f81076de83410c4d397179744b478e33cd80ccbcc457abf40a9409ad27dcd21dde
  languageName: node
  linkType: hard

"copy-anything@npm:^2.0.1":
  version: 2.0.6
  resolution: "copy-anything@npm:2.0.6"
  dependencies:
    is-what: "npm:^3.14.1"
  checksum: 2702998a8cc015f9917385b7f16b0d85f1f6e5e2fd34d99f14df584838f492f49aa0c390d973684c687e895c5c58d08b308a0400ac3e1e3d6fa1e5884a5402ad
  languageName: node
  linkType: hard

"copy-concurrently@npm:^1.0.0":
  version: 1.0.5
  resolution: "copy-concurrently@npm:1.0.5"
  dependencies:
    aproba: "npm:^1.1.1"
    fs-write-stream-atomic: "npm:^1.0.8"
    iferr: "npm:^0.1.5"
    mkdirp: "npm:^0.5.1"
    rimraf: "npm:^2.5.4"
    run-queue: "npm:^1.0.0"
  checksum: c2ce213cb27ee3df584d16eb6c9bfe99cfb531585007533c3e4c752521b4fbf0b2f7f90807d79c496683330808ecd9fdbd9ab9ddfa0913150b7f5097423348ce
  languageName: node
  linkType: hard

"copy-descriptor@npm:^0.1.0":
  version: 0.1.1
  resolution: "copy-descriptor@npm:0.1.1"
  checksum: 161f6760b7348c941007a83df180588fe2f1283e0867cc027182734e0f26134e6cc02de09aa24a95dc267b2e2025b55659eef76c8019df27bc2d883033690181
  languageName: node
  linkType: hard

"copy-webpack-plugin@npm:^4.6.0":
  version: 4.6.0
  resolution: "copy-webpack-plugin@npm:4.6.0"
  dependencies:
    cacache: "npm:^10.0.4"
    find-cache-dir: "npm:^1.0.0"
    globby: "npm:^7.1.1"
    is-glob: "npm:^4.0.0"
    loader-utils: "npm:^1.1.0"
    minimatch: "npm:^3.0.4"
    p-limit: "npm:^1.0.0"
    serialize-javascript: "npm:^1.4.0"
  checksum: 4a5b0def5da5ea2b57ee9d367e2bc74e8727bb8014dd448920561f4dcb3fa8b4b2f2078fb65f23b102253b4d3186c95025549203a2b0bce3802706a4a42766d8
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.40.0":
  version: 3.42.0
  resolution: "core-js-compat@npm:3.42.0"
  dependencies:
    browserslist: "npm:^4.24.4"
  checksum: 0138ce005c13ce642fc38e18e54a52a1c78ca8315ee6e4faad748d2a1b0ad2462ea615285ad4e6cf77afe48e47a868d898e64c70606c1eb1c9e6a9f19ee2b186
  languageName: node
  linkType: hard

"core-js@npm:^2.6.12, core-js@npm:^2.6.5":
  version: 2.6.12
  resolution: "core-js@npm:2.6.12"
  checksum: 00128efe427789120a06b819adc94cc72b96955acb331cb71d09287baf9bd37bebd191d91f1ee4939c893a050307ead4faea08876f09115112612b6a05684b63
  languageName: node
  linkType: hard

"core-util-is@npm:1.0.2":
  version: 1.0.2
  resolution: "core-util-is@npm:1.0.2"
  checksum: 980a37a93956d0de8a828ce508f9b9e3317039d68922ca79995421944146700e4aaf490a6dbfebcb1c5292a7184600c7710b957d724be1e37b8254c6bc0fe246
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cosmiconfig@npm:^5.0.0":
  version: 5.2.1
  resolution: "cosmiconfig@npm:5.2.1"
  dependencies:
    import-fresh: "npm:^2.0.0"
    is-directory: "npm:^0.3.1"
    js-yaml: "npm:^3.13.1"
    parse-json: "npm:^4.0.0"
  checksum: ae9ba309cdbb42d0c9d63dad5c1dfa1c56bb8f818cb8633eea14fd2dbdc9f33393b77658ba96fdabda497bc943afed8c3371d1222afe613c518ba676fa624645
  languageName: node
  linkType: hard

"create-ecdh@npm:^4.0.4":
  version: 4.0.4
  resolution: "create-ecdh@npm:4.0.4"
  dependencies:
    bn.js: "npm:^4.1.0"
    elliptic: "npm:^6.5.3"
  checksum: 77b11a51360fec9c3bce7a76288fc0deba4b9c838d5fb354b3e40c59194d23d66efe6355fd4b81df7580da0661e1334a235a2a5c040b7569ba97db428d466e7f
  languageName: node
  linkType: hard

"create-hash@npm:^1.1.0, create-hash@npm:^1.1.2, create-hash@npm:^1.2.0":
  version: 1.2.0
  resolution: "create-hash@npm:1.2.0"
  dependencies:
    cipher-base: "npm:^1.0.1"
    inherits: "npm:^2.0.1"
    md5.js: "npm:^1.3.4"
    ripemd160: "npm:^2.0.1"
    sha.js: "npm:^2.4.0"
  checksum: d402e60e65e70e5083cb57af96d89567954d0669e90550d7cec58b56d49c4b193d35c43cec8338bc72358198b8cbf2f0cac14775b651e99238e1cf411490f915
  languageName: node
  linkType: hard

"create-hmac@npm:^1.1.4, create-hmac@npm:^1.1.7":
  version: 1.1.7
  resolution: "create-hmac@npm:1.1.7"
  dependencies:
    cipher-base: "npm:^1.0.3"
    create-hash: "npm:^1.1.0"
    inherits: "npm:^2.0.1"
    ripemd160: "npm:^2.0.0"
    safe-buffer: "npm:^5.0.1"
    sha.js: "npm:^2.4.8"
  checksum: 24332bab51011652a9a0a6d160eed1e8caa091b802335324ae056b0dcb5acbc9fcf173cf10d128eba8548c3ce98dfa4eadaa01bd02f44a34414baee26b651835
  languageName: node
  linkType: hard

"cross-spawn@npm:^6.0.0":
  version: 6.0.6
  resolution: "cross-spawn@npm:6.0.6"
  dependencies:
    nice-try: "npm:^1.0.4"
    path-key: "npm:^2.0.1"
    semver: "npm:^5.5.0"
    shebang-command: "npm:^1.2.0"
    which: "npm:^1.2.9"
  checksum: bf61fb890e8635102ea9bce050515cf915ff6a50ccaa0b37a17dc82fded0fb3ed7af5478b9367b86baee19127ad86af4be51d209f64fd6638c0862dca185fe1d
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"crypto-browserify@npm:^3.11.0":
  version: 3.12.1
  resolution: "crypto-browserify@npm:3.12.1"
  dependencies:
    browserify-cipher: "npm:^1.0.1"
    browserify-sign: "npm:^4.2.3"
    create-ecdh: "npm:^4.0.4"
    create-hash: "npm:^1.2.0"
    create-hmac: "npm:^1.1.7"
    diffie-hellman: "npm:^5.0.3"
    hash-base: "npm:~3.0.4"
    inherits: "npm:^2.0.4"
    pbkdf2: "npm:^3.1.2"
    public-encrypt: "npm:^4.0.3"
    randombytes: "npm:^2.1.0"
    randomfill: "npm:^1.0.4"
  checksum: 184a2def7b16628e79841243232ab5497f18d8e158ac21b7ce90ab172427d0a892a561280adc08f9d4d517bce8db2a5b335dc21abb970f787f8e874bd7b9db7d
  languageName: node
  linkType: hard

"css-color-names@npm:0.0.4, css-color-names@npm:^0.0.4":
  version: 0.0.4
  resolution: "css-color-names@npm:0.0.4"
  checksum: 88ef97c25bcfb217469cdff428049022438cc546eb208210f0edec03b75050723db3ba02cd0f4959c84cd0659be0b43af3323eff80d1ba5fb15c2accf09ccbe2
  languageName: node
  linkType: hard

"css-declaration-sorter@npm:^4.0.1":
  version: 4.0.1
  resolution: "css-declaration-sorter@npm:4.0.1"
  dependencies:
    postcss: "npm:^7.0.1"
    timsort: "npm:^0.3.0"
  checksum: 5d99058fb4bfec937731724f917d982790f6f08afdf147835053de1f84ecd42f06b139fe68e005adda83c190fac1a84e663eb8003b6bb733a9c72a4420fe22d2
  languageName: node
  linkType: hard

"css-loader@npm:^1.0.1":
  version: 1.0.1
  resolution: "css-loader@npm:1.0.1"
  dependencies:
    babel-code-frame: "npm:^6.26.0"
    css-selector-tokenizer: "npm:^0.7.0"
    icss-utils: "npm:^2.1.0"
    loader-utils: "npm:^1.0.2"
    lodash: "npm:^4.17.11"
    postcss: "npm:^6.0.23"
    postcss-modules-extract-imports: "npm:^1.2.0"
    postcss-modules-local-by-default: "npm:^1.2.0"
    postcss-modules-scope: "npm:^1.1.0"
    postcss-modules-values: "npm:^1.3.0"
    postcss-value-parser: "npm:^3.3.0"
    source-list-map: "npm:^2.0.0"
  peerDependencies:
    webpack: ^4.0.0
  checksum: 6815acbd8e35ccb80b83e0ca74a1634fc95f8400780a497827943782ce06ec0270741d4957ec3d56e8d518840caac2f5092696b7c36b2aafd410cfed84270b00
  languageName: node
  linkType: hard

"css-select-base-adapter@npm:^0.1.1":
  version: 0.1.1
  resolution: "css-select-base-adapter@npm:0.1.1"
  checksum: 17f28a0d9e8596c541de250e48958e72a65399c9e15ba5689915d6631a451068187c19d674f08187843a61cb949951cb33c7db82bd7341536769523baed867dc
  languageName: node
  linkType: hard

"css-select@npm:^2.0.0":
  version: 2.1.0
  resolution: "css-select@npm:2.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^3.2.1"
    domutils: "npm:^1.7.0"
    nth-check: "npm:^1.0.2"
  checksum: 47832492c8218ffd92ed18eaa325397bd0bd8e4bcf3bc71767c5e1ed8b4f39b672ba157b0b5e693ef50006017d78c19e46791a75b43bb192c4db3680a331afc7
  languageName: node
  linkType: hard

"css-select@npm:^4.1.3":
  version: 4.3.0
  resolution: "css-select@npm:4.3.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.0.1"
    domhandler: "npm:^4.3.1"
    domutils: "npm:^2.8.0"
    nth-check: "npm:^2.0.1"
  checksum: a489d8e5628e61063d5a8fe0fa1cc7ae2478cb334a388a354e91cf2908154be97eac9fa7ed4dffe87a3e06cf6fcaa6016553115335c4fd3377e13dac7bd5a8e1
  languageName: node
  linkType: hard

"css-selector-tokenizer@npm:^0.7.0":
  version: 0.7.3
  resolution: "css-selector-tokenizer@npm:0.7.3"
  dependencies:
    cssesc: "npm:^3.0.0"
    fastparse: "npm:^1.1.2"
  checksum: bc4d14204bf5716f70085526bf370cbdd1c643dcb2e7366ba96212649fa5a07885e3f5dc2bde53646bac557ded79a60c7118cf7cf5d4dda898353a9e08277649
  languageName: node
  linkType: hard

"css-tree@npm:1.0.0-alpha.37":
  version: 1.0.0-alpha.37
  resolution: "css-tree@npm:1.0.0-alpha.37"
  dependencies:
    mdn-data: "npm:2.0.4"
    source-map: "npm:^0.6.1"
  checksum: 8f3c197baea919f4f55d0e84b1665d5e7d5fd74cb192fd0bf951828929b9cd5fd71de074afb685705bf5b40d7b04d4c5a206bfab26954378f04f2f5ce426d2f8
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.2":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: "npm:2.0.14"
    source-map: "npm:^0.6.1"
  checksum: 499a507bfa39b8b2128f49736882c0dd636b0cd3370f2c69f4558ec86d269113286b7df469afc955de6a68b0dba00bc533e40022a73698081d600072d5d83c1c
  languageName: node
  linkType: hard

"css-what@npm:^3.2.1":
  version: 3.4.2
  resolution: "css-what@npm:3.4.2"
  checksum: 454dca1b9dff8cf740d666d24a6c517562f374fe3a160891ebf8c82a9dd76864757913573c4db30537a959f5f595750420be00552ea6d5a9456ee68acc2349bf
  languageName: node
  linkType: hard

"css-what@npm:^6.0.1":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: a09f5a6b14ba8dcf57ae9a59474722e80f20406c53a61e9aedb0eedc693b135113ffe2983f4efc4b5065ae639442e9ae88df24941ef159c218b231011d733746
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 6bcfd898662671be15ae7827120472c5667afb3d7429f1f917737f3bf84c4176003228131b643ae74543f17a394446247df090c597bb9a728cce298606ed0aa7
  languageName: node
  linkType: hard

"cssnano-preset-default@npm:^4.0.0, cssnano-preset-default@npm:^4.0.8":
  version: 4.0.8
  resolution: "cssnano-preset-default@npm:4.0.8"
  dependencies:
    css-declaration-sorter: "npm:^4.0.1"
    cssnano-util-raw-cache: "npm:^4.0.1"
    postcss: "npm:^7.0.0"
    postcss-calc: "npm:^7.0.1"
    postcss-colormin: "npm:^4.0.3"
    postcss-convert-values: "npm:^4.0.1"
    postcss-discard-comments: "npm:^4.0.2"
    postcss-discard-duplicates: "npm:^4.0.2"
    postcss-discard-empty: "npm:^4.0.1"
    postcss-discard-overridden: "npm:^4.0.1"
    postcss-merge-longhand: "npm:^4.0.11"
    postcss-merge-rules: "npm:^4.0.3"
    postcss-minify-font-values: "npm:^4.0.2"
    postcss-minify-gradients: "npm:^4.0.2"
    postcss-minify-params: "npm:^4.0.2"
    postcss-minify-selectors: "npm:^4.0.2"
    postcss-normalize-charset: "npm:^4.0.1"
    postcss-normalize-display-values: "npm:^4.0.2"
    postcss-normalize-positions: "npm:^4.0.2"
    postcss-normalize-repeat-style: "npm:^4.0.2"
    postcss-normalize-string: "npm:^4.0.2"
    postcss-normalize-timing-functions: "npm:^4.0.2"
    postcss-normalize-unicode: "npm:^4.0.1"
    postcss-normalize-url: "npm:^4.0.1"
    postcss-normalize-whitespace: "npm:^4.0.2"
    postcss-ordered-values: "npm:^4.1.2"
    postcss-reduce-initial: "npm:^4.0.3"
    postcss-reduce-transforms: "npm:^4.0.2"
    postcss-svgo: "npm:^4.0.3"
    postcss-unique-selectors: "npm:^4.0.1"
  checksum: 6017657e0733da6ca73098452a7aed1a9b408b8b745e58ac86a4e3a5aa44f8d4476c2d6f16284a00aa649396624a03fb9bacce4746cfbb0edb593871ff20c7a1
  languageName: node
  linkType: hard

"cssnano-util-get-arguments@npm:^4.0.0":
  version: 4.0.0
  resolution: "cssnano-util-get-arguments@npm:4.0.0"
  checksum: d14ff2f16852ee0883844f960f035d15ecc04c516f3dd98979e5515b02e9b7f883a5b5a54fd209cc1d117bb906a28382a5b2dd15a165e8e8f3e66ae14fddbe03
  languageName: node
  linkType: hard

"cssnano-util-get-match@npm:^4.0.0":
  version: 4.0.0
  resolution: "cssnano-util-get-match@npm:4.0.0"
  checksum: 62462d6fe07334f7bbe2530fa427c3fdd240af0cc949bb9feb2bf178432743ec1e6e5914f50c44cc3eb65488b1d6e6f9dcd5fbf948bc31e6672dea623e1923bb
  languageName: node
  linkType: hard

"cssnano-util-raw-cache@npm:^4.0.1":
  version: 4.0.1
  resolution: "cssnano-util-raw-cache@npm:4.0.1"
  dependencies:
    postcss: "npm:^7.0.0"
  checksum: d0bc709570d540b5b122baa47f61072f268488ef78eefac3388bdcfc880f533c4cf8361afafcad78222453d405509fd211a832382d61c5ad6f3fef142ce5afb5
  languageName: node
  linkType: hard

"cssnano-util-same-parent@npm:^4.0.0":
  version: 4.0.1
  resolution: "cssnano-util-same-parent@npm:4.0.1"
  checksum: bec24c8609def0a73734a2d5e0df130655c9c7ec9098da0b66166e81f6614eb90fa43cb0afe81dabea51e253c11a240546c5e1a67b30d22aa297349394522642
  languageName: node
  linkType: hard

"cssnano@npm:^4.0.0, cssnano@npm:^4.1.10":
  version: 4.1.11
  resolution: "cssnano@npm:4.1.11"
  dependencies:
    cosmiconfig: "npm:^5.0.0"
    cssnano-preset-default: "npm:^4.0.8"
    is-resolvable: "npm:^1.0.0"
    postcss: "npm:^7.0.0"
  checksum: ca0898a512b203437fd8320b3618aa9806db8fed3406389c83f071b9e52e49b3b9e11c628a56690e61451bdc33dc63cd01567d94d599afd4087f548769ffa19e
  languageName: node
  linkType: hard

"csso@npm:^4.0.2":
  version: 4.2.0
  resolution: "csso@npm:4.2.0"
  dependencies:
    css-tree: "npm:^1.1.2"
  checksum: f8c6b1300efaa0f8855a7905ae3794a29c6496e7f16a71dec31eb6ca7cfb1f058a4b03fd39b66c4deac6cb06bf6b4ba86da7b67d7320389cb9994d52b924b903
  languageName: node
  linkType: hard

"csstype@npm:^3.1.0":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"current-script-polyfill@npm:^1.0.0":
  version: 1.0.0
  resolution: "current-script-polyfill@npm:1.0.0"
  checksum: a06daa43cbce46e0c9bf37411b2a56830053fa21892d3a18cdd5f32c0916b51b2cafcad4032585ea40ce5d36c0e27b5818f91310b968c95967a0cca787f5cebf
  languageName: node
  linkType: hard

"cyclist@npm:^1.0.1":
  version: 1.0.2
  resolution: "cyclist@npm:1.0.2"
  checksum: 163e2f7207180ccf2bb5a6ca8a7360469c13fad631509ef96de02397266b3a42089e2b2b51b97d3d8fdc4709d2fbe651c309670e5cc28b0ae445b1e5a34a98e2
  languageName: node
  linkType: hard

"d@npm:1, d@npm:^1.0.1, d@npm:^1.0.2":
  version: 1.0.2
  resolution: "d@npm:1.0.2"
  dependencies:
    es5-ext: "npm:^0.10.64"
    type: "npm:^2.7.2"
  checksum: 3e6ede10cd3b77586c47da48423b62bed161bf1a48bdbcc94d87263522e22f5dfb0e678a6dba5323fdc14c5d8612b7f7eb9e7d9e37b2e2d67a7bf9f116dabe5a
  languageName: node
  linkType: hard

"dashdash@npm:^1.12.0":
  version: 1.14.1
  resolution: "dashdash@npm:1.14.1"
  dependencies:
    assert-plus: "npm:^1.0.0"
  checksum: 64589a15c5bd01fa41ff7007e0f2c6552c5ef2028075daa16b188a3721f4ba001841bf306dfc2eee6e2e6e7f76b38f5f17fb21fa847504192290ffa9e150118a
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 7986d40fc7979e9e6241f85db8d17060dd9a71bd53c894fa29d126061715e322a4cd47a00b0b8c710394854183d4120462b980b8554012acc1c0fa49df7ad38c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: f8a4534b5c69384d95ac18137d381f18a5cfae1f0fc1df0ef6feef51ef0d568606d970b69e02ea186c6c0f0eac77fe4e6ad96fec2569cc86c3afcc7475068c55
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: fa7aa40078025b7810dcffc16df02c480573b7b53ef1205aa6a61533011005c1890e5ba17018c692ce7c900212b547262d33279fde801ad9843edc0863bf78c4
  languageName: node
  linkType: hard

"de-indent@npm:^1.0.2":
  version: 1.0.2
  resolution: "de-indent@npm:1.0.2"
  checksum: 7058ce58abd6dfc123dd204e36be3797abd419b59482a634605420f47ae97639d0c183ec5d1b904f308a01033f473673897afc2bd59bc620ebf1658763ef4291
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0, debug@npm:^2.3.3":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.4, debug@npm:^4.3.6":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 37d96ae42cbc71c14844d2ae3ba55adf462ec89fd3a999459dec3833944cd999af6007ff29c780f1c61153bcaaf2c842d1e4ce1ec621e4fc4923244942e4a02a
  languageName: node
  linkType: hard

"decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 85c39fe8fbf0482d4a1e224ef0119db5c1897f8503bcef8b826adff7a1b11414972f6fef2d7dec2ee0b4be3863cf64ac1439137ae9e6af23a3d8dcbe26a5b4b2
  languageName: node
  linkType: hard

"decimal.js@npm:^10.2.0":
  version: 10.5.0
  resolution: "decimal.js@npm:10.5.0"
  checksum: 785c35279df32762143914668df35948920b6c1c259b933e0519a69b7003fc0a5ed2a766b1e1dda02574450c566b21738a45f15e274b47c2ac02072c0d1f3ac3
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.0":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 1f4fa54eb740414a816b3f6c24818fbfcabd74ac478391e9f4e2282c994127db02010ce804f3d08e38255493cfe68608b3f5c8e09fd6efc4ae46c807691f7a31
  languageName: node
  linkType: hard

"deep-equal@npm:^1.0.1":
  version: 1.1.2
  resolution: "deep-equal@npm:1.1.2"
  dependencies:
    is-arguments: "npm:^1.1.1"
    is-date-object: "npm:^1.0.5"
    is-regex: "npm:^1.1.4"
    object-is: "npm:^1.1.5"
    object-keys: "npm:^1.1.1"
    regexp.prototype.flags: "npm:^1.5.1"
  checksum: cd85d822d18e9b3e1532d0f6ba412d229aa9d22881d70da161674428ae96e47925191296f7cda29306bac252889007da40ed8449363bd1c96c708acb82068a00
  languageName: node
  linkType: hard

"deepmerge@npm:^1.5.2":
  version: 1.5.2
  resolution: "deepmerge@npm:1.5.2"
  checksum: 5e676957f523c73a69633d236227513310fea934af02839bd6908cf569503f8988e76512fab6d9dde700e72642f22f331455d6b12e2826e4854a8e8233d0789d
  languageName: node
  linkType: hard

"default-gateway@npm:^4.2.0":
  version: 4.2.0
  resolution: "default-gateway@npm:4.2.0"
  dependencies:
    execa: "npm:^1.0.0"
    ip-regex: "npm:^2.1.0"
  checksum: 2f499b3a9a6c995fd2b4c0d2411256b1899c94e7eacdb895be64e25c301fa8bce8fd3f8152e540669bb178c6a355154c2f86ec23d4ff40ff3b8413d2a59cd86d
  languageName: node
  linkType: hard

"default-gateway@npm:^5.0.2":
  version: 5.0.5
  resolution: "default-gateway@npm:5.0.5"
  dependencies:
    execa: "npm:^3.3.0"
  checksum: 178751715344f22ec6999878f9e49d4d7414405e52ae1250a30aea46135bbac489c25cd35ce4794991afd08a6f27b749920156c5c2d6333ef48cecb6eca7469a
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: "npm:^1.0.2"
  checksum: 9cfbe498f5c8ed733775db62dfd585780387d93c17477949e1670bfcfb9346e0281ce8c4bf9f4ac1fc0f9b851113bd6dc9e41182ea1644ccd97de639fa13c35a
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.2, define-properties@npm:^1.1.3, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 88a152319ffe1396ccc6ded510a3896e77efac7a1bfbaa174a7b00414a1747377e0bb525d303794a47cf30e805c2ec84e575758512c6e44a993076d29fd4e6c3
  languageName: node
  linkType: hard

"define-property@npm:^0.2.5":
  version: 0.2.5
  resolution: "define-property@npm:0.2.5"
  dependencies:
    is-descriptor: "npm:^0.1.0"
  checksum: 9986915c0893818dedc9ca23eaf41370667762fd83ad8aa4bf026a28563120dbaacebdfbfbf2b18d3b929026b9c6ee972df1dbf22de8fafb5fe6ef18361e4750
  languageName: node
  linkType: hard

"define-property@npm:^1.0.0":
  version: 1.0.0
  resolution: "define-property@npm:1.0.0"
  dependencies:
    is-descriptor: "npm:^1.0.0"
  checksum: d7cf09db10d55df305f541694ed51dafc776ad9bb8a24428899c9f2d36b11ab38dce5527a81458d1b5e7c389f8cbe803b4abad6e91a0037a329d153b84fc975e
  languageName: node
  linkType: hard

"define-property@npm:^2.0.2":
  version: 2.0.2
  resolution: "define-property@npm:2.0.2"
  dependencies:
    is-descriptor: "npm:^1.0.2"
    isobject: "npm:^3.0.1"
  checksum: f91a08ad008fa764172a2c072adc7312f10217ade89ddaea23018321c6d71b2b68b8c229141ed2064179404e345c537f1a2457c379824813695b51a6ad3e4969
  languageName: node
  linkType: hard

"del@npm:^4.1.1":
  version: 4.1.1
  resolution: "del@npm:4.1.1"
  dependencies:
    "@types/glob": "npm:^7.1.1"
    globby: "npm:^6.1.0"
    is-path-cwd: "npm:^2.0.0"
    is-path-in-cwd: "npm:^2.0.0"
    p-map: "npm:^2.0.0"
    pify: "npm:^4.0.1"
    rimraf: "npm:^2.6.3"
  checksum: ed3233e86e39c0a6a7ea85d8ad0ebc00603078ad408b9c34b4742f707c20028c5731dce2e8aa9a6eb5ae6bee30ccc5405cf7b5d457306520e37c92d0410b6061
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"depd@npm:~1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: acb24aaf936ef9a227b6be6d495f0d2eb20108a9a6ad40585c5bda1a897031512fef6484e4fdbb80bd249fdaa82841fa1039f416ece03188e677ba11bcfda249
  languageName: node
  linkType: hard

"des.js@npm:^1.0.0":
  version: 1.1.0
  resolution: "des.js@npm:1.1.0"
  dependencies:
    inherits: "npm:^2.0.1"
    minimalistic-assert: "npm:^1.0.0"
  checksum: 671354943ad67493e49eb4c555480ab153edd7cee3a51c658082fcde539d2690ed2a4a0b5d1f401f9cde822edf3939a6afb2585f32c091f2d3a1b1665cd45236
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: f039f601790f2e9d4654e499913259a798b1f5246ae24f86ab5e8bd4aaf3bce50484234c494f11fb00aecb0c6e2733aa7b1cf3f530865640b65fbbd65b2c4e09
  languageName: node
  linkType: hard

"diffie-hellman@npm:^5.0.3":
  version: 5.0.3
  resolution: "diffie-hellman@npm:5.0.3"
  dependencies:
    bn.js: "npm:^4.1.0"
    miller-rabin: "npm:^4.0.0"
    randombytes: "npm:^2.0.0"
  checksum: ce53ccafa9ca544b7fc29b08a626e23a9b6562efc2a98559a0c97b4718937cebaa9b5d7d0a05032cc9c1435e9b3c1532b9e9bf2e0ede868525922807ad6e1ecf
  languageName: node
  linkType: hard

"dir-glob@npm:^2.0.0, dir-glob@npm:^2.2.2":
  version: 2.2.2
  resolution: "dir-glob@npm:2.2.2"
  dependencies:
    path-type: "npm:^3.0.0"
  checksum: 67575fd496df80ec90969f1a9f881f03b4ef614ca2c07139df81a12f9816250780dff906f482def0f897dd748d22fa13c076b52ac635e0024f7d434846077a3a
  languageName: node
  linkType: hard

"dns-equal@npm:^1.0.0":
  version: 1.0.0
  resolution: "dns-equal@npm:1.0.0"
  checksum: da966e5275ac50546e108af6bc29aaae2164d2ae96d60601b333c4a3aff91f50b6ca14929cf91f20a9cad1587b356323e300cea3ff6588a6a816988485f445f1
  languageName: node
  linkType: hard

"dns-packet@npm:^1.3.1":
  version: 1.3.4
  resolution: "dns-packet@npm:1.3.4"
  dependencies:
    ip: "npm:^1.1.0"
    safe-buffer: "npm:^5.0.1"
  checksum: ee06478da192f9014ab43c7e9118c77b9e353a8d5c06b0d2cba367b3501dd7453bcfed89354a8890cf740491379dcf4b28153d064d051e55c30cfbdf92b88608
  languageName: node
  linkType: hard

"dns-txt@npm:^2.0.2":
  version: 2.0.2
  resolution: "dns-txt@npm:2.0.2"
  dependencies:
    buffer-indexof: "npm:^1.0.0"
  checksum: 71703e65156a2d626216157e6c4fddd844e7e790b6cd3cec830ef8eed80e7ea2697e5f4f2f3eb3aae809be3c91e370cad7a5d91b05ce6b6fcd5e191e7e3d31ca
  languageName: node
  linkType: hard

"dom-converter@npm:^0.2.0":
  version: 0.2.0
  resolution: "dom-converter@npm:0.2.0"
  dependencies:
    utila: "npm:~0.4"
  checksum: e96aa63bd8c6ee3cd9ce19c3aecfc2c42e50a460e8087114794d4f5ecf3a4f052b34ea3bf2d73b5d80b4da619073b49905e6d7d788ceb7814ca4c29be5354a11
  languageName: node
  linkType: hard

"dom-serializer@npm:0":
  version: 0.2.2
  resolution: "dom-serializer@npm:0.2.2"
  dependencies:
    domelementtype: "npm:^2.0.1"
    entities: "npm:^2.0.0"
  checksum: 5cb595fb77e1a23eca56742f47631e6f4af66ce1982c7ed28b3d0ef21f1f50304c067adc29d3eaf824c572be022cee88627d0ac9b929408f24e923f3c7bed37b
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.4.1
  resolution: "dom-serializer@npm:1.4.1"
  dependencies:
    domelementtype: "npm:^2.0.1"
    domhandler: "npm:^4.2.0"
    entities: "npm:^2.0.0"
  checksum: 67d775fa1ea3de52035c98168ddcd59418356943b5eccb80e3c8b3da53adb8e37edb2cc2f885802b7b1765bf5022aec21dfc32910d7f9e6de4c3148f095ab5e0
  languageName: node
  linkType: hard

"domain-browser@npm:^1.1.1":
  version: 1.2.0
  resolution: "domain-browser@npm:1.2.0"
  checksum: a955f482f4b4710fbd77c12a33e77548d63603c30c80f61a80519f27e3db1ba8530b914584cc9e9365d2038753d6b5bd1f4e6c81e432b007b0ec95b8b5e69b1b
  languageName: node
  linkType: hard

"domelementtype@npm:1":
  version: 1.3.1
  resolution: "domelementtype@npm:1.3.1"
  checksum: 6d4f5761060a21eaf3c96545501e9d188745c7e1c31b8d141bf15d8748feeadba868f4ea32877751b8678b286fb1afbe6ae905ca3fb8f0214d8322e482cdbec0
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 686f5a9ef0fff078c1412c05db73a0dce096190036f33e400a07e2a4518e9f56b1e324f5c576a0a747ef0e75b5d985c040b0d51945ce780c0dd3c625a18cd8c9
  languageName: node
  linkType: hard

"domhandler@npm:^4.0.0, domhandler@npm:^4.2.0, domhandler@npm:^4.3.1":
  version: 4.3.1
  resolution: "domhandler@npm:4.3.1"
  dependencies:
    domelementtype: "npm:^2.2.0"
  checksum: 5c199c7468cb052a8b5ab80b13528f0db3d794c64fc050ba793b574e158e67c93f8336e87fd81e9d5ee43b0e04aea4d8b93ed7be4899cb726a1601b3ba18538b
  languageName: node
  linkType: hard

"domutils@npm:^1.7.0":
  version: 1.7.0
  resolution: "domutils@npm:1.7.0"
  dependencies:
    dom-serializer: "npm:0"
    domelementtype: "npm:1"
  checksum: 437fcd2d6d6be03f488152e73c6f953e289c58496baa22be9626b2b46f9cfd40486ae77d144487ff6b102929a3231cdb9a8bf8ef485fb7b7c30c985daedc77eb
  languageName: node
  linkType: hard

"domutils@npm:^2.5.2, domutils@npm:^2.8.0":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: "npm:^1.0.1"
    domelementtype: "npm:^2.2.0"
    domhandler: "npm:^4.2.0"
  checksum: d58e2ae01922f0dd55894e61d18119924d88091837887bf1438f2327f32c65eb76426bd9384f81e7d6dcfb048e0f83c19b222ad7101176ad68cdc9c695b563db
  languageName: node
  linkType: hard

"dot-prop@npm:^5.2.0":
  version: 5.3.0
  resolution: "dot-prop@npm:5.3.0"
  dependencies:
    is-obj: "npm:^2.0.0"
  checksum: 93f0d343ef87fe8869320e62f2459f7e70f49c6098d948cc47e060f4a3f827d0ad61e83cb82f2bd90cd5b9571b8d334289978a43c0f98fea4f0e99ee8faa0599
  languageName: node
  linkType: hard

"dotenv-expand@npm:^5.1.0":
  version: 5.1.0
  resolution: "dotenv-expand@npm:5.1.0"
  checksum: 24ac633de853ef474d0421cc639328b7134109c8dc2baaa5e3afb7495af5e9237136d7e6971e55668e4dce915487eb140967cdd2b3e99aa439e0f6bf8b56faeb
  languageName: node
  linkType: hard

"dotenv@npm:^7.0.0":
  version: 7.0.0
  resolution: "dotenv@npm:7.0.0"
  checksum: 4d834d09d23ebd284e701c4204172659a7dcd51116f11c29c575ae6d918ccd4760a3383bdfd83cfbed42f061266b787f8e56452b952638867ea5476be875eb27
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"duplexer@npm:^0.1.1":
  version: 0.1.2
  resolution: "duplexer@npm:0.1.2"
  checksum: c57bcd4bdf7e623abab2df43a7b5b23d18152154529d166c1e0da6bee341d84c432d157d7e97b32fecb1bf3a8b8857dd85ed81a915789f550637ed25b8e64fc2
  languageName: node
  linkType: hard

"duplexify@npm:^3.4.2, duplexify@npm:^3.6.0":
  version: 3.7.1
  resolution: "duplexify@npm:3.7.1"
  dependencies:
    end-of-stream: "npm:^1.0.0"
    inherits: "npm:^2.0.1"
    readable-stream: "npm:^2.0.0"
    stream-shift: "npm:^1.0.0"
  checksum: 59d1440c1b4e3a4db35ae96933392703ce83518db1828d06b9b6322920d6cbbf0b7159e88be120385fe459e77f1eb0c7622f26e9ec1f47c9ff05c2b35747dbd3
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"easy-stack@npm:^1.0.1":
  version: 1.0.1
  resolution: "easy-stack@npm:1.0.1"
  checksum: 1eaf066169a20f6cc3cafd2bb36b00baacd60b6414c8d8bf51bfd50bc6f1c487140c8af86bbb8e1ff9ded2faea5e138c55a37867fc79cbbc985bf5a5ebe4b109
  languageName: node
  linkType: hard

"ecc-jsbn@npm:~0.1.1":
  version: 0.1.2
  resolution: "ecc-jsbn@npm:0.1.2"
  dependencies:
    jsbn: "npm:~0.1.0"
    safer-buffer: "npm:^2.1.0"
  checksum: 6cf168bae1e2dad2e46561d9af9cbabfbf5ff592176ad4e9f0f41eaaf5fe5e10bb58147fe0a804de62b1ee9dad42c28810c88d652b21b6013c47ba8efa274ca1
  languageName: node
  linkType: hard

"echarts@npm:^4.2.1":
  version: 4.9.0
  resolution: "echarts@npm:4.9.0"
  dependencies:
    zrender: "npm:4.3.2"
  checksum: f81715c7e9420d82085bdf5e356c78579608858c2abd957086c613e6bd77e4c01fd13624f82076ba55dc07934dcf5a8bba43cc468ff69a50afa8e071727e8022
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"ejs@npm:^2.6.1":
  version: 2.7.4
  resolution: "ejs@npm:2.7.4"
  checksum: d5700120ce5dd10e0853085003862f5d78c198326ed634838aa7f8b0bf1efd65ad9fa0f2bac0053af05afeb21fb015595166b2c4fc83c7ef8f481e469a01ad52
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.149":
  version: 1.5.157
  resolution: "electron-to-chromium@npm:1.5.157"
  checksum: d22dc2603bdfb0d89c8e199bcc29a34995cc6e37261b5029b4e635ea536b843ed00dfc3b1dd2f69e1852031daee0c7cf3fb63cc70abf5312908328075b35e9af
  languageName: node
  linkType: hard

"elliptic@npm:^6.5.3, elliptic@npm:^6.5.5":
  version: 6.6.1
  resolution: "elliptic@npm:6.6.1"
  dependencies:
    bn.js: "npm:^4.11.9"
    brorand: "npm:^1.1.0"
    hash.js: "npm:^1.0.0"
    hmac-drbg: "npm:^1.0.1"
    inherits: "npm:^2.0.4"
    minimalistic-assert: "npm:^1.0.1"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 8b24ef782eec8b472053793ea1e91ae6bee41afffdfcb78a81c0a53b191e715cbe1292aa07165958a9bbe675bd0955142560b1a007ffce7d6c765bcaf951a867
  languageName: node
  linkType: hard

"emoji-regex@npm:^7.0.1":
  version: 7.0.3
  resolution: "emoji-regex@npm:7.0.3"
  checksum: a8917d695c3a3384e4b7230a6a06fd2de6b3db3709116792e8b7b36ddbb3db4deb28ad3e983e70d4f2a1f9063b5dab9025e4e26e9ca08278da4fbb73e213743f
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"emojis-list@npm:^2.0.0":
  version: 2.1.0
  resolution: "emojis-list@npm:2.1.0"
  checksum: bbb941223bfb3e38054cb52ed1b3098a8dac0a90fdd2699eb8a3af3b2172cdc4af0932e05c3edd52e814997c8f45cf1d7f5e86e9ecdcd4e2390a0f27e6914db5
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: 7dc4394b7b910444910ad64b812392159a21e1a7ecc637c775a440227dcb4f80eff7fe61f4453a7d7603fa23d23d30cc93fe9e4b5ed985b88d6441cd4a35117b
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 5d317306acb13e6590e28e27924c754163946a2480de11865c991a3a7eed4315cd3fba378b543ca145829569eefe9b899f3d84bb09870f675ae60bc924b01ceb
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.0.0, end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 870b423afb2d54bb8d243c63e07c170409d41e20b47eeef0727547aea5740bd6717aca45597a9f2745525667a6b804c1e7bede41f856818faee5806dd9ff3975
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^4.5.0":
  version: 4.5.0
  resolution: "enhanced-resolve@npm:4.5.0"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    memory-fs: "npm:^0.5.0"
    tapable: "npm:^1.0.0"
  checksum: d95fc630606ea35bed21c4a029bbb1681919571a2d1d2011c7fc42a26a9e48ed3d74a89949ce331e1fd3229850a303e3218b887b92951330f16bdfbb93a10e64
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 7fba6af1f116300d2ba1c5673fc218af1961b20908638391b4e1e6d5850314ee2ac3ec22d741b3a8060479911c99305164aed19b6254bde75e7e6b1b2c3f3aa3
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"errno@npm:^0.1.1, errno@npm:^0.1.3, errno@npm:~0.1.7":
  version: 0.1.8
  resolution: "errno@npm:0.1.8"
  dependencies:
    prr: "npm:~1.0.1"
  bin:
    errno: cli.js
  checksum: 83758951967ec57bf00b5f5b7dc797e6d65a6171e57ea57adcf1bd1a0b477fd9b5b35fae5be1ff18f4090ed156bce1db749fe7e317aac19d485a5d150f6a4936
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.0.6":
  version: 2.1.4
  resolution: "error-stack-parser@npm:2.1.4"
  dependencies:
    stackframe: "npm:^1.3.4"
  checksum: 7679b780043c98b01fc546725484e0cfd3071bf5c906bbe358722972f04abf4fc3f0a77988017665bab367f6ef3fc2d0185f7528f45966b83e7c99c02d5509b9
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.2, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.9":
  version: 1.23.10
  resolution: "es-abstract@npm:1.23.10"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-regex: "npm:^1.2.1"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.4"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.4"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.19"
  checksum: e65c8fb973d6ba489fc1bc88730c56a592e249f49a9811c77bf88568f23696b682fe3f3485c03aaf6561042a3c7a675ae57d512861dffd8b0abde0035231c6a3
  languageName: node
  linkType: hard

"es-array-method-boxes-properly@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-array-method-boxes-properly@npm:1.0.0"
  checksum: 4b7617d3fbd460d6f051f684ceca6cf7e88e6724671d9480388d3ecdd72119ddaa46ca31f2c69c5426a82e4b3091c1e81867c71dcdc453565cd90005ff2c382d
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: c7e87467abb0b438639baa8139f701a06537d2b9bc758f23e8622c3b42fd0fdb5bde0f535686119e446dd9d5e4c0f238af4e14960f4771877cf818d023f6730b
  languageName: node
  linkType: hard

"es5-ext@npm:^0.10.35, es5-ext@npm:^0.10.62, es5-ext@npm:^0.10.63, es5-ext@npm:^0.10.64, es5-ext@npm:~0.10.14":
  version: 0.10.64
  resolution: "es5-ext@npm:0.10.64"
  dependencies:
    es6-iterator: "npm:^2.0.3"
    es6-symbol: "npm:^3.1.3"
    esniff: "npm:^2.0.1"
    next-tick: "npm:^1.1.0"
  checksum: 4459b6ae216f3c615db086e02437bdfde851515a101577fd61b19f9b3c1ad924bab4d197981eb7f0ccb915f643f2fc10ff76b97a680e96cbb572d15a27acd9a3
  languageName: node
  linkType: hard

"es6-iterator@npm:^2.0.3":
  version: 2.0.3
  resolution: "es6-iterator@npm:2.0.3"
  dependencies:
    d: "npm:1"
    es5-ext: "npm:^0.10.35"
    es6-symbol: "npm:^3.1.1"
  checksum: 91f20b799dba28fb05bf623c31857fc1524a0f1c444903beccaf8929ad196c8c9ded233e5ac7214fc63a92b3f25b64b7f2737fcca8b1f92d2d96cf3ac902f5d8
  languageName: node
  linkType: hard

"es6-symbol@npm:^3.1.1, es6-symbol@npm:^3.1.3":
  version: 3.1.4
  resolution: "es6-symbol@npm:3.1.4"
  dependencies:
    d: "npm:^1.0.2"
    ext: "npm:^1.7.0"
  checksum: 777bf3388db5d7919e09a0fd175aa5b8a62385b17cb2227b7a137680cba62b4d9f6193319a102642aa23d5840d38a62e4784f19cfa5be4a2210a3f0e9b23d15d
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.2, escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"eslint-scope@npm:^4.0.3":
  version: 4.0.3
  resolution: "eslint-scope@npm:4.0.3"
  dependencies:
    esrecurse: "npm:^4.1.0"
    estraverse: "npm:^4.1.1"
  checksum: a2a3fe5845938ce7cfd2e658c309a9bb27a7f9ce94f0cc447ed5f9fa95b16451556d7e1db4c8e5d2aaa02d02850f5346d23091bbe94f7097412ce846504b4dcc
  languageName: node
  linkType: hard

"esniff@npm:^2.0.1":
  version: 2.0.1
  resolution: "esniff@npm:2.0.1"
  dependencies:
    d: "npm:^1.0.1"
    es5-ext: "npm:^0.10.62"
    event-emitter: "npm:^0.3.5"
    type: "npm:^2.7.2"
  checksum: 7efd8d44ac20e5db8cb0ca77eb65eca60628b2d0f3a1030bcb05e71cc40e6e2935c47b87dba3c733db12925aa5b897f8e0e7a567a2c274206f184da676ea2e65
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"esrecurse@npm:^4.1.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: 9cb46463ef8a8a4905d3708a652d60122a0c20bb58dec7e0e12ab0e7235123d74214fc0141d743c381813e1b992767e2708194f6f6e0f9fd00c1b4e0887b8b6d
  languageName: node
  linkType: hard

"estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"event-emitter@npm:^0.3.5":
  version: 0.3.5
  resolution: "event-emitter@npm:0.3.5"
  dependencies:
    d: "npm:1"
    es5-ext: "npm:~0.10.14"
  checksum: 75082fa8ffb3929766d0f0a063bfd6046bd2a80bea2666ebaa0cfd6f4a9116be6647c15667bea77222afc12f5b4071b68d393cf39fdaa0e8e81eda006160aff0
  languageName: node
  linkType: hard

"event-pubsub@npm:4.3.0":
  version: 4.3.0
  resolution: "event-pubsub@npm:4.3.0"
  checksum: 47fa4fb5b55b3ed08b912862cc913e03603fa063cd3ec5cf3dfeb39a19314d3ca327e938a2cf70685254ab3a71af8178969963c705a030c6081d625bec835114
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.0":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 5f6d97cbcbac47be798e6355e3a7639a84ee1f7d9b199a07017f1d2f1e2fe236004d14fa5dfaeba661f94ea57805385e326236a6debbc7145c8877fbc0297c6b
  languageName: node
  linkType: hard

"events@npm:^3.0.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: d6b6f2adbccbcda74ddbab52ed07db727ef52e31a61ed26db9feb7dc62af7fc8e060defa65e5f8af9449b86b52cc1a1f6a79f2eafcf4e62add2b7a1fa4a432f6
  languageName: node
  linkType: hard

"eventsource@npm:^2.0.2":
  version: 2.0.2
  resolution: "eventsource@npm:2.0.2"
  checksum: 0b8c70b35e45dd20f22ff64b001be9d530e33b92ca8bdbac9e004d0be00d957ab02ef33c917315f59bf2f20b178c56af85c52029bc8e6cc2d61c31d87d943573
  languageName: node
  linkType: hard

"evp_bytestokey@npm:^1.0.0, evp_bytestokey@npm:^1.0.3":
  version: 1.0.3
  resolution: "evp_bytestokey@npm:1.0.3"
  dependencies:
    md5.js: "npm:^1.3.4"
    node-gyp: "npm:latest"
    safe-buffer: "npm:^5.1.1"
  checksum: 77fbe2d94a902a80e9b8f5a73dcd695d9c14899c5e82967a61b1fc6cbbb28c46552d9b127cff47c45fcf684748bdbcfa0a50410349109de87ceb4b199ef6ee99
  languageName: node
  linkType: hard

"execa@npm:^1.0.0":
  version: 1.0.0
  resolution: "execa@npm:1.0.0"
  dependencies:
    cross-spawn: "npm:^6.0.0"
    get-stream: "npm:^4.0.0"
    is-stream: "npm:^1.1.0"
    npm-run-path: "npm:^2.0.0"
    p-finally: "npm:^1.0.0"
    signal-exit: "npm:^3.0.0"
    strip-eof: "npm:^1.0.0"
  checksum: cc71707c9aa4a2552346893ee63198bf70a04b5a1bc4f8a0ef40f1d03c319eae80932c59191f037990d7d102193e83a38ec72115fff814ec2fb3099f3661a590
  languageName: node
  linkType: hard

"execa@npm:^3.3.0":
  version: 3.4.0
  resolution: "execa@npm:3.4.0"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    get-stream: "npm:^5.0.0"
    human-signals: "npm:^1.1.1"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.0"
    onetime: "npm:^5.1.0"
    p-finally: "npm:^2.0.0"
    signal-exit: "npm:^3.0.2"
    strip-final-newline: "npm:^2.0.0"
  checksum: fc823d6a32987c3105b38b1cbe26763cb98c5ad58012eeb781e8c25e2fb6c7baad7dd47488456537b273d3ddbf12bd257d772059befeee0acb06af246d2d5be1
  languageName: node
  linkType: hard

"expand-brackets@npm:^2.1.4":
  version: 2.1.4
  resolution: "expand-brackets@npm:2.1.4"
  dependencies:
    debug: "npm:^2.3.3"
    define-property: "npm:^0.2.5"
    extend-shallow: "npm:^2.0.1"
    posix-character-classes: "npm:^0.1.0"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.1"
  checksum: 3e2fb95d2d7d7231486493fd65db913927b656b6fcdfcce41e139c0991a72204af619ad4acb1be75ed994ca49edb7995ef241dbf8cf44dc3c03d211328428a87
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"express@npm:^4.16.3, express@npm:^4.17.1":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.3"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.7.1"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.3.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.3"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.12"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.13.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.19.0"
    serve-static: "npm:1.16.2"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 38168fd0a32756600b56e6214afecf4fc79ec28eca7f7a91c2ab8d50df4f47562ca3f9dee412da7f5cea6b1a1544b33b40f9f8586dbacfbdada0fe90dbb10a1f
  languageName: node
  linkType: hard

"ext@npm:^1.7.0":
  version: 1.7.0
  resolution: "ext@npm:1.7.0"
  dependencies:
    type: "npm:^2.7.2"
  checksum: a8e5f34e12214e9eee3a4af3b5c9d05ba048f28996450975b369fc86e5d0ef13b6df0615f892f5396a9c65d616213c25ec5b0ad17ef42eac4a500512a19da6c7
  languageName: node
  linkType: hard

"extend-shallow@npm:^2.0.1":
  version: 2.0.1
  resolution: "extend-shallow@npm:2.0.1"
  dependencies:
    is-extendable: "npm:^0.1.0"
  checksum: ee1cb0a18c9faddb42d791b2d64867bd6cfd0f3affb711782eb6e894dd193e2934a7f529426aac7c8ddb31ac5d38000a00aa2caf08aa3dfc3e1c8ff6ba340bd9
  languageName: node
  linkType: hard

"extend-shallow@npm:^3.0.0, extend-shallow@npm:^3.0.2":
  version: 3.0.2
  resolution: "extend-shallow@npm:3.0.2"
  dependencies:
    assign-symbols: "npm:^1.0.0"
    is-extendable: "npm:^1.0.1"
  checksum: f39581b8f98e3ad94995e33214fff725b0297cf09f2725b6f624551cfb71e0764accfd0af80becc0182af5014d2a57b31b85ec999f9eb8a6c45af81752feac9a
  languageName: node
  linkType: hard

"extend@npm:~3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 73bf6e27406e80aa3e85b0d1c4fd987261e628064e170ca781125c0b635a3dabad5e05adbf07595ea0cf1e6c5396cacb214af933da7cbaf24fe75ff14818e8f9
  languageName: node
  linkType: hard

"extglob@npm:^2.0.4":
  version: 2.0.4
  resolution: "extglob@npm:2.0.4"
  dependencies:
    array-unique: "npm:^0.3.2"
    define-property: "npm:^1.0.0"
    expand-brackets: "npm:^2.1.4"
    extend-shallow: "npm:^2.0.1"
    fragment-cache: "npm:^0.2.1"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.1"
  checksum: e1a891342e2010d046143016c6c03d58455c2c96c30bf5570ea07929984ee7d48fad86b363aee08f7a8a638f5c3a66906429b21ecb19bc8e90df56a001cd282c
  languageName: node
  linkType: hard

"extsprintf@npm:1.3.0":
  version: 1.3.0
  resolution: "extsprintf@npm:1.3.0"
  checksum: f75114a8388f0cbce68e277b6495dc3930db4dde1611072e4a140c24e204affd77320d004b947a132e9a3b97b8253017b2b62dce661975fb0adced707abf1ab5
  languageName: node
  linkType: hard

"extsprintf@npm:^1.2.0":
  version: 1.4.1
  resolution: "extsprintf@npm:1.4.1"
  checksum: e10e2769985d0e9b6c7199b053a9957589d02e84de42832c295798cb422a025e6d4a92e0259c1fb4d07090f5bfde6b55fd9f880ac5855bd61d775f8ab75a7ab0
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-glob@npm:^2.2.6":
  version: 2.2.7
  resolution: "fast-glob@npm:2.2.7"
  dependencies:
    "@mrmlnc/readdir-enhanced": "npm:^2.2.1"
    "@nodelib/fs.stat": "npm:^1.1.2"
    glob-parent: "npm:^3.1.0"
    is-glob: "npm:^4.0.0"
    merge2: "npm:^1.2.3"
    micromatch: "npm:^3.1.10"
  checksum: 85bc858e298423d5a1b6eed6eee8556005a19d245c4ae9aceac04d56699ea9885ca0a2afc4f76b562416e94fe2048df6b2f306f3d4b7e51ed37b7a52fc1e4fc7
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fastparse@npm:^1.1.2":
  version: 1.1.2
  resolution: "fastparse@npm:1.1.2"
  checksum: c08d6e7ef10c0928426c1963dd4593e2baaf44d223ab1e5ba5d7b30470144b3a4ecb3605958b73754cea3f857ecef00b67c885f07ca2c312b38b67d9d88b84b5
  languageName: node
  linkType: hard

"faye-websocket@npm:^0.11.3, faye-websocket@npm:^0.11.4":
  version: 0.11.4
  resolution: "faye-websocket@npm:0.11.4"
  dependencies:
    websocket-driver: "npm:>=0.5.1"
  checksum: c6052a0bb322778ce9f89af92890f6f4ce00d5ec92418a35e5f4c6864a4fe736fec0bcebd47eac7c0f0e979b01530746b1c85c83cb04bae789271abf19737420
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 45b559cff889934ebb8bc498351e5acba40750ada7e7d6bde197768d2fa67c149be8ae7f8ff34d03f4e1eb20f2764116e56440aaa2f6689e9a4aa7ef06acafe9
  languageName: node
  linkType: hard

"figgy-pudding@npm:^3.5.1":
  version: 3.5.2
  resolution: "figgy-pudding@npm:3.5.2"
  checksum: b21c7adaeb8485ef3c50e056b5dc8c3a6461818343aba141e0d7927aad47a0cb9f1d207ffdf494c380cd60d7c848c46a5ce5cb06987d10e9226fcec419c8af90
  languageName: node
  linkType: hard

"file-loader@npm:^3.0.1":
  version: 3.0.1
  resolution: "file-loader@npm:3.0.1"
  dependencies:
    loader-utils: "npm:^1.0.2"
    schema-utils: "npm:^1.0.0"
  peerDependencies:
    webpack: ^4.0.0
  checksum: 2f9c76dba5ccfb71b74b06c2d0bcedbde6c14b1047817d254371a9961679dae4a78aa924298ce6a84fb0b06d73975066c9642238878910ec362d04975cdd5dbe
  languageName: node
  linkType: hard

"file-uri-to-path@npm:1.0.0":
  version: 1.0.0
  resolution: "file-uri-to-path@npm:1.0.0"
  checksum: 3b545e3a341d322d368e880e1c204ef55f1d45cdea65f7efc6c6ce9e0c4d22d802d5629320eb779d006fe59624ac17b0e848d83cc5af7cd101f206cb704f5519
  languageName: node
  linkType: hard

"filesize@npm:^3.6.1":
  version: 3.6.1
  resolution: "filesize@npm:3.6.1"
  checksum: 7b900b488c914d4b9146ddaf2865c410687977cf62c627760ff3c47dce4a00a53523658f40c9023bba8894d2e4841bc913af280472c2bb5aec29bc342eb33b6f
  languageName: node
  linkType: hard

"fill-range@npm:^4.0.0":
  version: 4.0.0
  resolution: "fill-range@npm:4.0.0"
  dependencies:
    extend-shallow: "npm:^2.0.1"
    is-number: "npm:^3.0.0"
    repeat-string: "npm:^1.6.1"
    to-regex-range: "npm:^2.1.0"
  checksum: ccd57b7c43d7e28a1f8a60adfa3c401629c08e2f121565eece95e2386ebc64dedc7128d8c3448342aabf19db0c55a34f425f148400c7a7be9a606ba48749e089
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: d38035831865a49b5610206a3a9a9aae4e8523cbbcd01175d0480ffbf1278c47f11d89be3ca7f617ae6d94f29cf797546a4619cd84dd109009ef33f12f69019f
  languageName: node
  linkType: hard

"find-babel-config@npm:^1.1.0":
  version: 1.2.2
  resolution: "find-babel-config@npm:1.2.2"
  dependencies:
    json5: "npm:^1.0.2"
    path-exists: "npm:^3.0.0"
  checksum: c82631323b055a3ea8d2dbc42593d243dddf39ec20e83bb6aad847d77676829f4a2bdf507c5177bc9d2d4509a5e239a6023631f1e8b8011ab16d44d227c65639
  languageName: node
  linkType: hard

"find-cache-dir@npm:^1.0.0":
  version: 1.0.0
  resolution: "find-cache-dir@npm:1.0.0"
  dependencies:
    commondir: "npm:^1.0.1"
    make-dir: "npm:^1.0.0"
    pkg-dir: "npm:^2.0.0"
  checksum: 264b706771f30b39385cf921a5576c80ef95cb4c8ef2df8761b8be62326a1537ea63feecf0deac52cd84d6937b312a8d9554b4012a092937ade43a165cd5b6f9
  languageName: node
  linkType: hard

"find-cache-dir@npm:^2.1.0":
  version: 2.1.0
  resolution: "find-cache-dir@npm:2.1.0"
  dependencies:
    commondir: "npm:^1.0.1"
    make-dir: "npm:^2.0.0"
    pkg-dir: "npm:^3.0.0"
  checksum: 556117fd0af14eb88fb69250f4bba9e905e7c355c6136dff0e161b9cbd1f5285f761b778565a278da73a130f42eccc723d7ad4c002ae547ed1d698d39779dabb
  languageName: node
  linkType: hard

"find-cache-dir@npm:^3.3.1":
  version: 3.3.2
  resolution: "find-cache-dir@npm:3.3.2"
  dependencies:
    commondir: "npm:^1.0.1"
    make-dir: "npm:^3.0.2"
    pkg-dir: "npm:^4.1.0"
  checksum: 92747cda42bff47a0266b06014610981cfbb71f55d60f2c8216bc3108c83d9745507fb0b14ecf6ab71112bed29cd6fb1a137ee7436179ea36e11287e3159e587
  languageName: node
  linkType: hard

"find-up@npm:^2.1.0":
  version: 2.1.0
  resolution: "find-up@npm:2.1.0"
  dependencies:
    locate-path: "npm:^2.0.0"
  checksum: c080875c9fe28eb1962f35cbe83c683796a0321899f1eed31a37577800055539815de13d53495049697d3ba313013344f843bb9401dd337a1b832be5edfc6840
  languageName: node
  linkType: hard

"find-up@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-up@npm:3.0.0"
  dependencies:
    locate-path: "npm:^3.0.0"
  checksum: 2c2e7d0a26db858e2f624f39038c74739e38306dee42b45f404f770db357947be9d0d587f1cac72d20c114deb38aa57316e879eb0a78b17b46da7dab0a3bd6e3
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 0406ee89ebeefa2d507feb07ec366bebd8a6167ae74aa4e34fb4c4abd06cf782a3ce26ae4194d70706f72182841733f00551c209fe575cb00bd92104056e78c1
  languageName: node
  linkType: hard

"flush-write-stream@npm:^1.0.0":
  version: 1.1.1
  resolution: "flush-write-stream@npm:1.1.1"
  dependencies:
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^2.3.6"
  checksum: 2cd4f65b728d5f388197a03dafabc6a5e4f0c2ed1a2d912e288f7aa1c2996dd90875e55b50cf32c78dca55ad2e2dfae5d3db09b223838388033d87cf5920dd87
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.0.0, follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 5829165bd112c3c0e82be6c15b1a58fa9dcfaede3b3c54697a82fe4a62dd5ae5e8222956b448d2f98e331525f05d00404aba7d696de9e761ef6e42fdc780244f
  languageName: node
  linkType: hard

"font-awesome@npm:^4.7.0":
  version: 4.7.0
  resolution: "font-awesome@npm:4.7.0"
  checksum: 1c456e2939c55192eed67db9c0efb8db3e92fd357ca189ca00030eb44acffa1e9f835288d2204c14b9a9c490a7b14b7090dfaff80ded6b2473f50a923dfb41e7
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 0e0b50f6a843a282637d43674d1fb278dda1dd85f4f99b640024cfb10b85058aac0cc781bf689d5fe50b4b7f638e91e548560723a4e76e04fe96ae35ef039cee
  languageName: node
  linkType: hard

"for-in@npm:^1.0.2":
  version: 1.0.2
  resolution: "for-in@npm:1.0.2"
  checksum: 42bb609d564b1dc340e1996868b67961257fd03a48d7fdafd4f5119530b87f962be6b4d5b7e3a3fc84c9854d149494b1d358e0b0ce9837e64c4c6603a49451d6
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"forever-agent@npm:~0.6.1":
  version: 0.6.1
  resolution: "forever-agent@npm:0.6.1"
  checksum: 364f7f5f7d93ab661455351ce116a67877b66f59aca199559a999bd39e3cfadbfbfacc10415a915255e2210b30c23febe9aec3ca16bf2d1ff11c935a1000e24c
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.2
  resolution: "form-data@npm:4.0.2"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    mime-types: "npm:^2.1.12"
  checksum: e534b0cf025c831a0929bf4b9bbe1a9a6b03e273a8161f9947286b9b13bf8fb279c6944aae0070c4c311100c6d6dbb815cd955dc217728caf73fad8dc5b8ee9c
  languageName: node
  linkType: hard

"form-data@npm:~2.3.2":
  version: 2.3.3
  resolution: "form-data@npm:2.3.3"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.6"
    mime-types: "npm:^2.1.12"
  checksum: 706ef1e5649286b6a61e5bb87993a9842807fd8f149cd2548ee807ea4fb882247bdf7f6e64ac4720029c0cd5c80343de0e22eee1dc9e9882e12db9cc7bc016a4
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 9b67c3fac86acdbc9ae47ba1ddd5f2f81526fa4c8226863ede5600a3f7c7416ef451f6f1e240a3cc32d0fd79fcfe6beb08fd0da454f360032bde70bf80afbb33
  languageName: node
  linkType: hard

"fragment-cache@npm:^0.2.1":
  version: 0.2.1
  resolution: "fragment-cache@npm:0.2.1"
  dependencies:
    map-cache: "npm:^0.2.2"
  checksum: 5891d1c1d1d5e1a7fb3ccf28515c06731476fa88f7a50f4ede8a0d8d239a338448e7f7cc8b73db48da19c229fa30066104fe6489862065a4f1ed591c42fbeabf
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"from2@npm:^2.1.0":
  version: 2.3.0
  resolution: "from2@npm:2.3.0"
  dependencies:
    inherits: "npm:^2.0.1"
    readable-stream: "npm:^2.0.0"
  checksum: f87f7a2e4513244d551454a7f8324ef1f7837864a8701c536417286ec19ff4915606b1dfa8909a21b7591ebd8440ffde3642f7c303690b9a4d7c832d62248aa1
  languageName: node
  linkType: hard

"front-end@workspace:.":
  version: 0.0.0-use.local
  resolution: "front-end@workspace:."
  dependencies:
    "@babel/polyfill": "npm:^7.4.4"
    "@playwright/test": "npm:^1.53.0"
    "@vue/cli-plugin-babel": "npm:^3.9.0"
    "@vue/cli-service": "npm:^3.9.0"
    axios: "npm:^1.7.2"
    chinese-to-pinyin: "npm:^1.0.2"
    core-js: "npm:^2.6.5"
    decimal.js: "npm:^10.2.0"
    echarts: "npm:^4.2.1"
    font-awesome: "npm:^4.7.0"
    hey-global: "npm:^1.0.0"
    hey-utils: "npm:^1.0.2"
    heyui: "npm:^1.25.0"
    jquery: "npm:^3.4.1"
    less: "npm:^3.9.0"
    less-loader: "npm:^4.1.0"
    lodash.debounce: "npm:^4.0.8"
    moment: "npm:^2.24.0"
    qs: "npm:^6.7.0"
    sockjs-client: "npm:^1.6.1"
    stompjs: "npm:^2.3.3"
    string-utilz: "npm:^1.3.0"
    style-resources-loader: "npm:^1.2.1"
    vue: "npm:^2.6.10"
    vue-router: "npm:^3.0.3"
    vue-template-compiler: "npm:^2.7.16"
    vuex: "npm:^3.0.1"
    wpk-reporter: "npm:^0.6.2"
  languageName: unknown
  linkType: soft

"fs-extra@npm:^7.0.1":
  version: 7.0.1
  resolution: "fs-extra@npm:7.0.1"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    jsonfile: "npm:^4.0.0"
    universalify: "npm:^0.1.0"
  checksum: 1943bb2150007e3739921b8d13d4109abdc3cc481e53b97b7ea7f77eda1c3c642e27ae49eac3af074e3496ea02fde30f411ef410c760c70a38b92e656e5da784
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs-write-stream-atomic@npm:^1.0.8":
  version: 1.0.10
  resolution: "fs-write-stream-atomic@npm:1.0.10"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    iferr: "npm:^0.1.5"
    imurmurhash: "npm:^0.1.4"
    readable-stream: "npm:1 || 2"
  checksum: 293b2b4ed346d35a28f8637a20cb2aef31be86503da501c42c2eda8fefed328bac16ce0e5daa7019f9329d73930c58031eaea2ce0c70f1680943fbfb7cff808b
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:2.3.2":
  version: 2.3.2
  resolution: "fsevents@npm:2.3.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: be78a3efa3e181cda3cf7a4637cb527bcebb0bd0ea0440105a3bb45b86f9245b307dc10a2507e8f4498a7d4ec349d1910f4d73e4d4495b16103106e07eee735b
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@npm:^1.2.7":
  version: 1.2.13
  resolution: "fsevents@npm:1.2.13"
  dependencies:
    bindings: "npm:^1.5.0"
    nan: "npm:^2.12.1"
  checksum: 4427ff08db9ee7327f2c3ad58ec56f9096a917eed861bfffaa2e2be419479cdf37d00750869ab9ecbf5f59f32ad999bd59577d73fc639193e6c0ce52bb253e02
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.2
  resolution: "fsevents@patch:fsevents@npm%3A2.3.2#optional!builtin<compat/fsevents>::version=2.3.2&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A^1.2.7#optional!builtin<compat/fsevents>":
  version: 1.2.13
  resolution: "fsevents@patch:fsevents@npm%3A1.2.13#optional!builtin<compat/fsevents>::version=1.2.13&hash=d11327"
  dependencies:
    bindings: "npm:^1.5.0"
    nan: "npm:^2.12.1"
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: e920a2ab52663005f3cbe7ee3373e3c71c1fb5558b0b0548648cdf3e51961085032458e26c71ff1a8c8c20e7ee7caeb03d43a5d1fa8610c459333323a2e71253
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 33e77fd29bddc2d9bb78ab3eb854c165909201f88c75faa8272e35899e2d35a8a642a15e7420ef945e1f64a9670d6aa3ec744106b2aa42be68ca5114025954ca
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.1, get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-stream@npm:^4.0.0":
  version: 4.1.0
  resolution: "get-stream@npm:4.1.0"
  dependencies:
    pump: "npm:^3.0.0"
  checksum: 294d876f667694a5ca23f0ca2156de67da950433b6fb53024833733975d32582896dbc7f257842d331809979efccf04d5e0b6b75ad4d45744c45f193fd497539
  languageName: node
  linkType: hard

"get-stream@npm:^5.0.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: "npm:^3.0.0"
  checksum: 43797ffd815fbb26685bf188c8cfebecb8af87b3925091dd7b9a9c915993293d78e3c9e1bce125928ff92f2d0796f3889b92b5ec6d58d1041b574682132e0a80
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: d6a7d6afca375779a4b307738c9e80dbf7afc0bdbe5948768d54ab9653c865523d8920e670991a925936eb524b7cb6a6361d199a760b21d0ca7620194455aa4b
  languageName: node
  linkType: hard

"get-value@npm:^2.0.3, get-value@npm:^2.0.6":
  version: 2.0.6
  resolution: "get-value@npm:2.0.6"
  checksum: f069c132791b357c8fc4adfe9e2929b0a2c6e95f98ca7bc6fcbc27f8a302e552f86b4ae61ec56d9e9ac2544b93b6a39743d479866a37b43fcc104088ba74f0d9
  languageName: node
  linkType: hard

"getpass@npm:^0.1.1":
  version: 0.1.7
  resolution: "getpass@npm:0.1.7"
  dependencies:
    assert-plus: "npm:^1.0.0"
  checksum: c13f8530ecf16fc509f3fa5cd8dd2129ffa5d0c7ccdf5728b6022d52954c2d24be3706b4cdf15333eec52f1fbb43feb70a01dabc639d1d10071e371da8aaa52f
  languageName: node
  linkType: hard

"glob-parent@npm:^3.1.0":
  version: 3.1.0
  resolution: "glob-parent@npm:3.1.0"
  dependencies:
    is-glob: "npm:^3.1.0"
    path-dirname: "npm:^1.0.0"
  checksum: bfa89ce5ae1dfea4c2ece7b61d2ea230d87fcbec7472915cfdb3f4caf688a91ecb0dc86ae39b1e17505adce7e64cae3b971d64dc66091f3a0131169fd631b00d
  languageName: node
  linkType: hard

"glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.3.0":
  version: 0.3.0
  resolution: "glob-to-regexp@npm:0.3.0"
  checksum: f7e8091288d88b397b715281560d86ba4998246c300cb0d51db483db0a4c68cb48b489af8da9c03262745e8aa5337ba596d82dee61ff9467c5d7c27d70b676aa
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^7.0.3, glob@npm:^7.1.2, glob@npm:^7.1.3, glob@npm:^7.1.4, glob@npm:^7.2.0":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 9d156f313af79d80b1566b93e19285f481c591ad6d0d319b4be5e03750d004dde40a39a0f26f7e635f9007a3600802f53ecd85a759b86f109e80a5f705e01846
  languageName: node
  linkType: hard

"globby@npm:^6.1.0":
  version: 6.1.0
  resolution: "globby@npm:6.1.0"
  dependencies:
    array-union: "npm:^1.0.1"
    glob: "npm:^7.0.3"
    object-assign: "npm:^4.0.1"
    pify: "npm:^2.0.0"
    pinkie-promise: "npm:^2.0.0"
  checksum: 656ad1f0d02c6ef378c07589519ed3ec27fe988ea177195c05b8aff280320f3d67b91fa0baa6f7e49288f9bf1f92fc84f783a79ac3ed66278f3fa082e627ed84
  languageName: node
  linkType: hard

"globby@npm:^7.1.1":
  version: 7.1.1
  resolution: "globby@npm:7.1.1"
  dependencies:
    array-union: "npm:^1.0.1"
    dir-glob: "npm:^2.0.0"
    glob: "npm:^7.1.2"
    ignore: "npm:^3.3.5"
    pify: "npm:^3.0.0"
    slash: "npm:^1.0.0"
  checksum: 016d4dfac6069221b2db18ad6afb0011639899920dbec87492ddc048fcd433361e6c094b12451ab14cf062013a776f47ef21bb8289d5e09a2f23e81d5aec0f8e
  languageName: node
  linkType: hard

"globby@npm:^9.2.0":
  version: 9.2.0
  resolution: "globby@npm:9.2.0"
  dependencies:
    "@types/glob": "npm:^7.1.1"
    array-union: "npm:^1.0.2"
    dir-glob: "npm:^2.2.2"
    fast-glob: "npm:^2.2.6"
    glob: "npm:^7.1.3"
    ignore: "npm:^4.0.3"
    pify: "npm:^4.0.1"
    slash: "npm:^2.0.0"
  checksum: 2bd47ec43797b81000f3619feff96803b22591961788c06d746f6c8ba2deb14676b591ee625eb74b197c0047b2236e4a7a2ad662417661231b317c1de67aee94
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.11, graceful-fs@npm:^4.1.15, graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"gzip-size@npm:^5.0.0":
  version: 5.1.1
  resolution: "gzip-size@npm:5.1.1"
  dependencies:
    duplexer: "npm:^0.1.1"
    pify: "npm:^4.0.1"
  checksum: 1c346d3ab83b85687a9aa644928250b31e0fc42cf16ca152225a09465f7465b220849b3854b39a457681fabb37ab318b2c0f73be8991ecedf2073e082d1f6dd9
  languageName: node
  linkType: hard

"handle-thing@npm:^2.0.0":
  version: 2.0.1
  resolution: "handle-thing@npm:2.0.1"
  checksum: 7ae34ba286a3434f1993ebd1cc9c9e6b6d8ea672182db28b1afc0a7119229552fa7031e3e5f3cd32a76430ece4e94b7da6f12af2eb39d6239a7693e4bd63a998
  languageName: node
  linkType: hard

"har-schema@npm:^2.0.0":
  version: 2.0.0
  resolution: "har-schema@npm:2.0.0"
  checksum: 3856cb76152658e0002b9c2b45b4360bb26b3e832c823caed8fcf39a01096030bf09fa5685c0f7b0f2cb3ecba6e9dce17edaf28b64a423d6201092e6be56e592
  languageName: node
  linkType: hard

"har-validator@npm:~5.1.3":
  version: 5.1.5
  resolution: "har-validator@npm:5.1.5"
  dependencies:
    ajv: "npm:^6.12.3"
    har-schema: "npm:^2.0.0"
  checksum: f1d606eb1021839e3a905be5ef7cca81c2256a6be0748efb8fefc14312214f9e6c15d7f2eaf37514104071207d84f627b68bb9f6178703da4e06fbd1a0649a5e
  languageName: node
  linkType: hard

"has-ansi@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-ansi@npm:2.0.0"
  dependencies:
    ansi-regex: "npm:^2.0.0"
  checksum: f54e4887b9f8f3c4bfefd649c48825b3c093987c92c27880ee9898539e6f01aed261e82e73153c3f920fde0db5bf6ebd58deb498ed1debabcb4bc40113ccdf05
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 2de0cdc4a1ccf7a1e75ffede1876994525ac03cc6f5ae7392d3415dd475cd9eee5bceec63669ab61aa997ff6cceebb50ef75561c7002bed8988de2b9d1b40788
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 1c6c83b14b8b1b3c25b0727b8ba3e3b647f99e9e6e13eb7322107261de07a4c1be56fc0d45678fc376e09772a3a1642ccdaf8fc69bdf123b6c086598397ce473
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 46538dddab297ec2f43923c3d35237df45d8c55a6fc1067031e04c13ed8a9a8f94954460632fd4da84c31a1721eefee16d901cbb1ae9602bab93bb6e08f93b95
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.1, has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"has-value@npm:^0.3.1":
  version: 0.3.1
  resolution: "has-value@npm:0.3.1"
  dependencies:
    get-value: "npm:^2.0.3"
    has-values: "npm:^0.1.4"
    isobject: "npm:^2.0.0"
  checksum: 7a7c2e9d07bc9742c81806150adb154d149bc6155267248c459cd1ce2a64b0759980d26213260e4b7599c8a3754551179f155ded88d0533a0d2bc7bc29028432
  languageName: node
  linkType: hard

"has-value@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-value@npm:1.0.0"
  dependencies:
    get-value: "npm:^2.0.6"
    has-values: "npm:^1.0.0"
    isobject: "npm:^3.0.0"
  checksum: 17cdccaf50f8aac80a109dba2e2ee5e800aec9a9d382ef9deab66c56b34269e4c9ac720276d5ffa722764304a1180ae436df077da0dd05548cfae0209708ba4d
  languageName: node
  linkType: hard

"has-values@npm:^0.1.4":
  version: 0.1.4
  resolution: "has-values@npm:0.1.4"
  checksum: a8f00ad862c20289798c35243d5bd0b0a97dd44b668c2204afe082e0265f2d0bf3b89fc8cc0ef01a52b49f10aa35cf85c336ee3a5f1cac96ed490f5e901cdbf2
  languageName: node
  linkType: hard

"has-values@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-values@npm:1.0.0"
  dependencies:
    is-number: "npm:^3.0.0"
    kind-of: "npm:^4.0.0"
  checksum: a6f2a1cc6b2e43eacc68e62e71ad6890def7f4b13d2ef06b4ad3ee156c23e470e6df144b9b467701908e17633411f1075fdff0cab45fb66c5e0584d89b25f35e
  languageName: node
  linkType: hard

"has@npm:^1.0.0":
  version: 1.0.4
  resolution: "has@npm:1.0.4"
  checksum: 82c1220573dc1f0a014a5d6189ae52a1f820f99dfdc00323c3a725b5002dcb7f04e44f460fea7af068474b2dd7c88cbe1846925c84017be9e31e1708936d305b
  languageName: node
  linkType: hard

"hash-base@npm:^3.0.0":
  version: 3.1.0
  resolution: "hash-base@npm:3.1.0"
  dependencies:
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.6.0"
    safe-buffer: "npm:^5.2.0"
  checksum: 663eabcf4173326fbb65a1918a509045590a26cc7e0964b754eef248d281305c6ec9f6b31cb508d02ffca383ab50028180ce5aefe013e942b44a903ac8dc80d0
  languageName: node
  linkType: hard

"hash-base@npm:~3.0, hash-base@npm:~3.0.4":
  version: 3.0.5
  resolution: "hash-base@npm:3.0.5"
  dependencies:
    inherits: "npm:^2.0.4"
    safe-buffer: "npm:^5.2.1"
  checksum: 6dc185b79bad9b6d525cd132a588e4215380fdc36fec6f7a8a58c5db8e3b642557d02ad9c367f5e476c7c3ad3ccffa3607f308b124e1ed80e3b80a1b254db61e
  languageName: node
  linkType: hard

"hash-sum@npm:^1.0.2":
  version: 1.0.2
  resolution: "hash-sum@npm:1.0.2"
  checksum: 311b2d7ea317b128860a88c7fd3ae46aef010b7fd7418a44afd2787cd889f24d635fa1e22a51bd5a5d8e338597c1da917d81f572e0de2f375e52e96c9fb63a66
  languageName: node
  linkType: hard

"hash.js@npm:^1.0.0, hash.js@npm:^1.0.3":
  version: 1.1.7
  resolution: "hash.js@npm:1.1.7"
  dependencies:
    inherits: "npm:^2.0.3"
    minimalistic-assert: "npm:^1.0.1"
  checksum: 41ada59494eac5332cfc1ce6b7ebdd7b88a3864a6d6b08a3ea8ef261332ed60f37f10877e0c825aaa4bddebf164fbffa618286aeeec5296675e2671cbfa746c4
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0, hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"he@npm:1.2.x, he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: a27d478befe3c8192f006cdd0639a66798979dfa6e2125c6ac582a19a5ebfec62ad83e8382e6036170d873f46e4536a7e795bf8b95bf7c247f4cc0825ccc8c17
  languageName: node
  linkType: hard

"hex-color-regex@npm:^1.1.0":
  version: 1.1.0
  resolution: "hex-color-regex@npm:1.1.0"
  checksum: 43f0b8bbeb3906b6b2ddfb5ce6cbc28fb4f1e7b5129d0485e006ea20f92dc65f97b3b12591dd6a993de2ca10cb64a16a6a380a37481fc09432e7be0037be8f9c
  languageName: node
  linkType: hard

"hey-global@npm:^1.0.0":
  version: 1.0.0
  resolution: "hey-global@npm:1.0.0"
  checksum: a3bc24979cdbd3d75c5670176e349c31aee5765fe8650d80807e7c23b911dbfa98e6f4656ae9f3024a2906c8606e2a338bb4a9a2881effb52b79882b2d944687
  languageName: node
  linkType: hard

"hey-utils@npm:^1.0.2":
  version: 1.0.2
  resolution: "hey-utils@npm:1.0.2"
  checksum: bfa93cae5776459ec09ca8382f411614283d6f1bd528cd430d3426f7bf89436bcaa08c969113d8e78fa67430b0fffb4baa04cf9da4d4d4082b20ab12b5f40c13
  languageName: node
  linkType: hard

"heyui@npm:^1.25.0":
  version: 1.28.0
  resolution: "heyui@npm:1.28.0"
  dependencies:
    manba: "npm:^1.3.2"
    vue: "npm:^2.6.10"
  checksum: a99407ed067249382bfbc97403ea7e0d2aef792c4eedb76ae15aed41324389955d4e678b75545dc5b940eaba29e653018678a5b5d5468042f63ea8eba34c77f8
  languageName: node
  linkType: hard

"highlight.js@npm:^10.7.1":
  version: 10.7.3
  resolution: "highlight.js@npm:10.7.3"
  checksum: 073837eaf816922427a9005c56c42ad8786473dc042332dfe7901aa065e92bc3d94ebf704975257526482066abb2c8677cc0326559bb8621e046c21c5991c434
  languageName: node
  linkType: hard

"hmac-drbg@npm:^1.0.1":
  version: 1.0.1
  resolution: "hmac-drbg@npm:1.0.1"
  dependencies:
    hash.js: "npm:^1.0.3"
    minimalistic-assert: "npm:^1.0.0"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: f3d9ba31b40257a573f162176ac5930109816036c59a09f901eb2ffd7e5e705c6832bedfff507957125f2086a0ab8f853c0df225642a88bf1fcaea945f20600d
  languageName: node
  linkType: hard

"hoopy@npm:^0.1.4":
  version: 0.1.4
  resolution: "hoopy@npm:0.1.4"
  checksum: 4ef749e1a13d46cae52014b9de452635637086c333fc67245369a1262dee806386354a4ed845d507e59e5a0d3aef55246c0ec66f5bf2908d40eb77e7dff2a254
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.1.4":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: 317cbc6b1bbbe23c2a40ae23f3dafe9fa349ce42a89a36f930e3f9c0530c179a3882d2ef1e4141a4c3674d6faaea862138ec55b43ad6f75e387fda2483a13c70
  languageName: node
  linkType: hard

"hpack.js@npm:^2.1.6":
  version: 2.1.6
  resolution: "hpack.js@npm:2.1.6"
  dependencies:
    inherits: "npm:^2.0.1"
    obuf: "npm:^1.0.0"
    readable-stream: "npm:^2.0.1"
    wbuf: "npm:^1.1.0"
  checksum: 55b9e824430bab82a19d079cb6e33042d7d0640325678c9917fcc020c61d8a08ca671b6c942c7f0aae9bb6e4b67ffb50734a72f9e21d66407c3138c1983b70f0
  languageName: node
  linkType: hard

"hsl-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "hsl-regex@npm:1.0.0"
  checksum: ddc29f4943c4a1768e611fadaebf6913d039640267f1fad2ac06f4dbdbe5cbe535d77dd19804898834671dd82751a4401c1b4d2ecade25d158ea23665d263e84
  languageName: node
  linkType: hard

"hsla-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "hsla-regex@npm:1.0.0"
  checksum: 307874844a055af7a8f225e3908e1f7a2d96aa28e86abcb7ce1837cd86acf32d1f3c71ae105416d4c8623c8f9220b1bc799598851b0826274faa047710985f76
  languageName: node
  linkType: hard

"html-entities@npm:^1.3.1":
  version: 1.4.0
  resolution: "html-entities@npm:1.4.0"
  checksum: eb2de616fb5948e681157805687672ea90e67c8a4f21a3215888ab422a984cab61fec96860708dca3bde0ae52577515683c8e28157ac8637220bb6a57a031b85
  languageName: node
  linkType: hard

"html-minifier@npm:^3.2.3":
  version: 3.5.21
  resolution: "html-minifier@npm:3.5.21"
  dependencies:
    camel-case: "npm:3.0.x"
    clean-css: "npm:4.2.x"
    commander: "npm:2.17.x"
    he: "npm:1.2.x"
    param-case: "npm:2.1.x"
    relateurl: "npm:0.2.x"
    uglify-js: "npm:3.4.x"
  bin:
    html-minifier: ./cli.js
  checksum: 010bd1a1304bca784e36b5a09db442a47c531cef80d9215149f7c08273bd9ba98dc9e3851e76c39d6c94ac4b182d08cfea3c4379604bd52072876a16389b2fbf
  languageName: node
  linkType: hard

"html-tags@npm:^2.0.0":
  version: 2.0.0
  resolution: "html-tags@npm:2.0.0"
  checksum: d438cc99feb04e7af2b51d114c40e4b789290edf544532134d5c92e6f64d1e4408bbb96ef31036260824fc0916dc4da454a4973f447dab83ed704ceafbaf8f19
  languageName: node
  linkType: hard

"html-webpack-plugin@npm:^3.2.0":
  version: 3.2.0
  resolution: "html-webpack-plugin@npm:3.2.0"
  dependencies:
    html-minifier: "npm:^3.2.3"
    loader-utils: "npm:^0.2.16"
    lodash: "npm:^4.17.3"
    pretty-error: "npm:^2.0.2"
    tapable: "npm:^1.0.0"
    toposort: "npm:^1.0.0"
    util.promisify: "npm:1.0.0"
  peerDependencies:
    webpack: ^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0
  checksum: f57294e713641259f8ef99b8221683f0c44812ef7cc0afa165c5c2621071423bb072a836f2e5604a2b96f0eb03d2f78eaaeadc56eb0d398f7b915fb42f54683c
  languageName: node
  linkType: hard

"htmlparser2@npm:^6.1.0":
  version: 6.1.0
  resolution: "htmlparser2@npm:6.1.0"
  dependencies:
    domelementtype: "npm:^2.0.1"
    domhandler: "npm:^4.0.0"
    domutils: "npm:^2.5.2"
    entities: "npm:^2.0.0"
  checksum: 3058499c95634f04dc66be8c2e0927cd86799413b2d6989d8ae542ca4dbf5fa948695d02c27d573acf44843af977aec6d9a7bdd0f6faa6b2d99e2a729b2a31b6
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 45b66a945cf13ec2d1f29432277201313babf4a01d9e52f44b31ca923434083afeca03f18417f599c9ab3d0e7b618ceb21257542338b57c54b710463b4a53e37
  languageName: node
  linkType: hard

"http-deceiver@npm:^1.2.7":
  version: 1.2.7
  resolution: "http-deceiver@npm:1.2.7"
  checksum: 8bb9b716f5fc55f54a451da7f49b9c695c3e45498a789634daec26b61e4add7c85613a4a9e53726c39d09de7a163891ecd6eb5809adb64500a840fd86fe81d03
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-errors@npm:~1.6.2":
  version: 1.6.3
  resolution: "http-errors@npm:1.6.3"
  dependencies:
    depd: "npm:~1.1.2"
    inherits: "npm:2.0.3"
    setprototypeof: "npm:1.1.0"
    statuses: "npm:>= 1.4.0 < 2"
  checksum: 17ec4046ee974477778bfdd525936c254b872054703ec2caa4d6f099566b8adade636ae6aeeacb39302c5cd6e28fb407ebd937f500f5010d0b6850750414ff78
  languageName: node
  linkType: hard

"http-parser-js@npm:>=0.5.1":
  version: 0.5.10
  resolution: "http-parser-js@npm:0.5.10"
  checksum: 8bbcf1832a8d70b2bd515270112116333add88738a2cc05bfb94ba6bde3be4b33efee5611584113818d2bcf654fdc335b652503be5a6b4c0b95e46f214187d93
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"http-proxy-middleware@npm:0.19.1":
  version: 0.19.1
  resolution: "http-proxy-middleware@npm:0.19.1"
  dependencies:
    http-proxy: "npm:^1.17.0"
    is-glob: "npm:^4.0.0"
    lodash: "npm:^4.17.11"
    micromatch: "npm:^3.1.10"
  checksum: b0c466dd54fac365e93f43138cba256063040557ae24fc92944b06fac35e879b882085e36fe276e48f5a27848b1600cd3ae59a845cb18714f5c0f205523783b1
  languageName: node
  linkType: hard

"http-proxy@npm:^1.17.0":
  version: 1.18.1
  resolution: "http-proxy@npm:1.18.1"
  dependencies:
    eventemitter3: "npm:^4.0.0"
    follow-redirects: "npm:^1.0.0"
    requires-port: "npm:^1.0.0"
  checksum: 148dfa700a03fb421e383aaaf88ac1d94521dfc34072f6c59770528c65250983c2e4ec996f2f03aa9f3fe46cd1270a593126068319311e3e8d9e610a37533e94
  languageName: node
  linkType: hard

"http-signature@npm:~1.2.0":
  version: 1.2.0
  resolution: "http-signature@npm:1.2.0"
  dependencies:
    assert-plus: "npm:^1.0.0"
    jsprim: "npm:^1.2.2"
    sshpk: "npm:^1.7.0"
  checksum: 582f7af7f354429e1fb19b3bbb9d35520843c69bb30a25b88ca3c5c2c10715f20ae7924e20cffbed220b1d3a726ef4fe8ccc48568d5744db87be9a79887d6733
  languageName: node
  linkType: hard

"https-browserify@npm:^1.0.0":
  version: 1.0.0
  resolution: "https-browserify@npm:1.0.0"
  checksum: e17b6943bc24ea9b9a7da5714645d808670af75a425f29baffc3284962626efdc1eb3aa9bbffaa6e64028a6ad98af5b09fabcb454a8f918fb686abfdc9e9b8ae
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"human-signals@npm:^1.1.1":
  version: 1.1.1
  resolution: "human-signals@npm:1.1.1"
  checksum: 18810ed239a7a5e23fb6c32d0fd4be75d7cd337a07ad59b8dbf0794cb0761e6e628349ee04c409e605fe55344716eab5d0a47a62ba2a2d0d367c89a2b4247b1e
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"icss-replace-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "icss-replace-symbols@npm:1.1.0"
  checksum: aaa5b67f82781fccc77bf6df14eaa9177ce3944462ef82b2b9e3b9f17d8fcd90f8851ffd5e6e249ebc5c464bfda07c2eccce2d122274c51c9d5b359b087f7049
  languageName: node
  linkType: hard

"icss-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "icss-utils@npm:2.1.0"
  dependencies:
    postcss: "npm:^6.0.1"
  checksum: 69d3b8fdea58fbf5c26d8dc2d1b7d8ded34fd8fb0800ed60b390e0ae5f596e77b155c587fb62e7642d1064d2d72fc672676df58ef935eba7303d79467e43edbf
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.4":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"iferr@npm:^0.1.5":
  version: 0.1.5
  resolution: "iferr@npm:0.1.5"
  checksum: e0669b1757d0501b43a158321945d1cc1fe56f28a972df2f88a5818f05c8853c7669ba5d6cfbbf9a1a312850699de6e528626df108d559005df7e15d16ee334c
  languageName: node
  linkType: hard

"ignore@npm:^3.3.5":
  version: 3.3.10
  resolution: "ignore@npm:3.3.10"
  checksum: 973e0ef3b3eaab8fc19014d80014ed11bcf3585de8088d9c7a5b5c4edefc55f4ecdc498144bdd0440b8e2ff22deb03f89c90300bfef2d1750d5920f997d0a600
  languageName: node
  linkType: hard

"ignore@npm:^4.0.3":
  version: 4.0.6
  resolution: "ignore@npm:4.0.6"
  checksum: 836ee7dc7fd9436096e2dba429359dbb9fa0e33d309e2b2d81692f375f6ca82024fc00567f798613d50c6b989e9cd2ad2b065acf116325cde177f02c86b7d4e0
  languageName: node
  linkType: hard

"image-size@npm:~0.5.0":
  version: 0.5.5
  resolution: "image-size@npm:0.5.5"
  bin:
    image-size: bin/image-size.js
  checksum: 655204163af06732f483a9fe7cce9dff4a29b7b2e88f5c957a5852e8143fa750f5e54b1955a2ca83de99c5220dbd680002d0d4e09140b01433520f4d5a0b1f4c
  languageName: node
  linkType: hard

"import-cwd@npm:^2.0.0":
  version: 2.1.0
  resolution: "import-cwd@npm:2.1.0"
  dependencies:
    import-from: "npm:^2.1.0"
  checksum: d530fe3e70b42ad1db6d6b01db888dc6f0275a3106122e8bde60240694e0833bf7f070f56130da9bf5026fb65d5fcaca5821ae07956437187ed9aeb74bf6621b
  languageName: node
  linkType: hard

"import-fresh@npm:^2.0.0":
  version: 2.0.0
  resolution: "import-fresh@npm:2.0.0"
  dependencies:
    caller-path: "npm:^2.0.0"
    resolve-from: "npm:^3.0.0"
  checksum: 116c55ee5215a7839062285b60df85dbedde084c02111dc58c1b9d03ff7876627059f4beb16cdc090a3db21fea9022003402aa782139dc8d6302589038030504
  languageName: node
  linkType: hard

"import-from@npm:^2.1.0":
  version: 2.1.0
  resolution: "import-from@npm:2.1.0"
  dependencies:
    resolve-from: "npm:^3.0.0"
  checksum: b7cb61873ba0d7df0cfba4904180e705dec1817766c9b39931b792be8e561188765dc49fba3d883e20873529619e9cff52a514c351a7d36db7c744c4ea2a2000
  languageName: node
  linkType: hard

"import-local@npm:^2.0.0":
  version: 2.0.0
  resolution: "import-local@npm:2.0.0"
  dependencies:
    pkg-dir: "npm:^3.0.0"
    resolve-cwd: "npm:^2.0.0"
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: 68f2d9203d3760a836db97e917ea1793e865e0c5dd3749380ccaf52be907553febb0828f14c3169e66ba1a458d931b3cc5597cc9b623c7f79b395b0c3892601e
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"indexes-of@npm:^1.0.1":
  version: 1.0.1
  resolution: "indexes-of@npm:1.0.1"
  checksum: 1ea1d2d00173fa38f728acfa00303657e1115361481e52f6cbae47c5d603219006c9357abf6bc323f1fb0fbe937e363bbb19e5c66c12578eea6ec6b7e892bdba
  languageName: node
  linkType: hard

"infer-owner@npm:^1.0.3":
  version: 1.0.4
  resolution: "infer-owner@npm:1.0.4"
  checksum: a7b241e3149c26e37474e3435779487f42f36883711f198c45794703c7556bc38af224088bd4d1a221a45b8208ae2c2bcf86200383621434d0c099304481c5b9
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.1, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"inherits@npm:2.0.3":
  version: 2.0.3
  resolution: "inherits@npm:2.0.3"
  checksum: 6e56402373149ea076a434072671f9982f5fad030c7662be0332122fe6c0fa490acb3cc1010d90b6eff8d640b1167d77674add52dfd1bb85d545cf29e80e73e7
  languageName: node
  linkType: hard

"internal-ip@npm:^4.3.0":
  version: 4.3.0
  resolution: "internal-ip@npm:4.3.0"
  dependencies:
    default-gateway: "npm:^4.2.0"
    ipaddr.js: "npm:^1.9.0"
  checksum: c0ad0b95981c8f21a2d4f115212af38c894a6a6d0a2a3cac4d73d1b5beb214fdfce7b5e66f087e8d575977d4df630886914412d1bc9c2678e5870210154ad65b
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 03966f5e259b009a9bf1a78d60da920df198af4318ec004f57b8aef1dd3fe377fbc8cce63a96e8c810010302654de89f9e19de1cd8ad0061d15be28a695465c7
  languageName: node
  linkType: hard

"invariant@npm:^2.2.2":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: "npm:^1.0.0"
  checksum: 5af133a917c0bcf65e84e7f23e779e7abc1cd49cb7fdc62d00d1de74b0d8c1b5ee74ac7766099fb3be1b05b26dfc67bab76a17030d2fe7ea2eef867434362dfc
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"ip-regex@npm:^2.1.0":
  version: 2.1.0
  resolution: "ip-regex@npm:2.1.0"
  checksum: 3ce2d8307fa0373ca357eba7504e66e73b8121805fd9eba6a343aeb077c64c30659fa876b11ac7a75635b7529d2ce87723f208a5b9d51571513b5c68c0cc1541
  languageName: node
  linkType: hard

"ip@npm:^1.1.0, ip@npm:^1.1.5":
  version: 1.1.9
  resolution: "ip@npm:1.1.9"
  checksum: 5af58bfe2110c9978acfd77a2ffcdf9d33a6ce1c72f49edbaf16958f7a8eb979b5163e43bb18938caf3aaa55cdacde4e470874c58ca3b4b112ea7a30461a0c27
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1, ipaddr.js@npm:^1.9.0":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"is-absolute-url@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-absolute-url@npm:2.1.0"
  checksum: 2c324c3118752d82c18afadffbea14d35224e76f37a2ee80e05b209386c46d19bef77b58ede41f7b3be552cde16c6cdb0d884807175a6eb40e8c61281dc67c2f
  languageName: node
  linkType: hard

"is-absolute-url@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-absolute-url@npm:3.0.3"
  checksum: 04c415974c32e73a83d3a21a9bea18fc4e2c14fbe6bbd64832cf1e67a75ade2af0e900f552f0b8a447f1305f5ffc9d143ccd8d005dbe715d198c359d342b86f0
  languageName: node
  linkType: hard

"is-accessor-descriptor@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-accessor-descriptor@npm:1.0.1"
  dependencies:
    hasown: "npm:^2.0.0"
  checksum: d034034074c5ffeb6c868e091083182279db1a956f49f8d1494cecaa0f8b99d706556ded2a9b20d9aa290549106eef8204d67d8572902e06dcb1add6db6b524d
  languageName: node
  linkType: hard

"is-arguments@npm:^1.1.1":
  version: 1.2.0
  resolution: "is-arguments@npm:1.2.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 6377344b31e9fcb707c6751ee89b11f132f32338e6a782ec2eac9393b0cbd32235dad93052998cda778ee058754860738341d8114910d50ada5615912bb929fc
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: c5c9f25606e86dbb12e756694afbbff64bc8b348d1bc989324c037e1068695131930199d6ad381952715dad3a9569333817f0b1a72ce5af7f883ce802e49c83d
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: f59b43dc1d129edb6f0e282595e56477f98c40278a2acdc8b0a5c57097c9eff8fe55470493df5775478cf32a4dc8eaf6d3a749f07ceee5bc263a78b2434f6a54
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: d70c236a5e82de6fc4d44368ffd0c2fee2b088b893511ce21e679da275a5ecc6015ff59a7d7e1bdd7ca39f71a8dbdd253cf8cce5c6b3c91cdd5b42b5ce677298
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: f4f4b905ceb195be90a6ea7f34323bf1c18e3793f18922e3e9a73c684c29eeeeff5175605c3a3a74cc38185fe27758f07efba3dbae812e5c5afbc0d2316b40e4
  languageName: node
  linkType: hard

"is-binary-path@npm:^1.0.0":
  version: 1.0.1
  resolution: "is-binary-path@npm:1.0.1"
  dependencies:
    binary-extensions: "npm:^1.0.0"
  checksum: 16e456fa3782eaf3d8e28d382b750507e3d54ff6694df8a1b2c6498da321e2ead311de9c42e653d8fb3213de72bac204b5f97e4a110cda8a72f17b1c1b4eb643
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 36ff6baf6bd18b3130186990026f5a95c709345c39cd368468e6c1b6ab52201e9fd26d8e1f4c066357b4938b0f0401e1a5000e08257787c1a02f3a719457001e
  languageName: node
  linkType: hard

"is-buffer@npm:^1.1.5":
  version: 1.1.6
  resolution: "is-buffer@npm:1.1.6"
  checksum: ae18aa0b6e113d6c490ad1db5e8df9bdb57758382b313f5a22c9c61084875c6396d50bbf49315f5b1926d142d74dfb8d31b40d993a383e0a158b15fea7a82234
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: ceebaeb9d92e8adee604076971dd6000d38d6afc40bb843ea8e45c5579b57671c3f3b50d7f04869618242c6cee08d1b67806a8cb8edaaaf7c0748b3720d6066f
  languageName: node
  linkType: hard

"is-color-stop@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-color-stop@npm:1.1.0"
  dependencies:
    css-color-names: "npm:^0.0.4"
    hex-color-regex: "npm:^1.1.0"
    hsl-regex: "npm:^1.0.0"
    hsla-regex: "npm:^1.0.0"
    rgb-regex: "npm:^1.0.1"
    rgba-regex: "npm:^1.0.0"
  checksum: 12d9a9d6b3da07bd69f678d6dbe897fccbd573ead12057f60c4773129e34ea2b658f29c1fb8ef84c548e8ac62061c04c16e23ffcb00d01900fa1f72b7ab3b569
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-data-descriptor@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-data-descriptor@npm:1.0.1"
  dependencies:
    hasown: "npm:^2.0.0"
  checksum: ad3acc372e3227f87eb8cdba112c343ca2a67f1885aecf64f02f901cb0858a1fc9488ad42135ab102e9d9e71a62b3594740790bb103a9ba5da830a131a89e3e8
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: ef3548a99d7e7f1370ce21006baca6d40c73e9f15c941f89f0049c79714c873d03b02dae1c64b3f861f55163ecc16da06506c5b8a1d4f16650b3d9351c380153
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 1a4d199c8e9e9cac5128d32e6626fa7805175af9df015620ac0d5d45854ccf348ba494679d872d37301032e35a54fc7978fba1687e8721b2139aea7870cafa2f
  languageName: node
  linkType: hard

"is-descriptor@npm:^0.1.0":
  version: 0.1.7
  resolution: "is-descriptor@npm:0.1.7"
  dependencies:
    is-accessor-descriptor: "npm:^1.0.1"
    is-data-descriptor: "npm:^1.0.1"
  checksum: f5960b9783f508aec570465288cb673d4b3cc4aae4e6de970c3afd9a8fc1351edcb85d78b2cce2ec5251893a423f73263cab3bb94cf365a8d71b5d510a116392
  languageName: node
  linkType: hard

"is-descriptor@npm:^1.0.0, is-descriptor@npm:^1.0.2":
  version: 1.0.3
  resolution: "is-descriptor@npm:1.0.3"
  dependencies:
    is-accessor-descriptor: "npm:^1.0.1"
    is-data-descriptor: "npm:^1.0.1"
  checksum: b4ee667ea787d3a0be4e58536087fd0587de2b0b6672fbfe288f5b8d831ac4b79fd987f31d6c2d4e5543a42c97a87428bc5215ce292a1a47070147793878226f
  languageName: node
  linkType: hard

"is-directory@npm:^0.3.1":
  version: 0.3.1
  resolution: "is-directory@npm:0.3.1"
  checksum: 1c39c7d1753b04e9483b89fb88908b8137ab4743b6f481947e97ccf93ecb384a814c8d3f0b95b082b149c5aa19c3e9e4464e2791d95174bce95998c26bb1974b
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: e828365958d155f90c409cdbe958f64051d99e8aedc2c8c4cd7c89dcf35329daed42f7b99346f7828df013e27deb8f721cf9408ba878c76eb9e8290235fbcdcc
  languageName: node
  linkType: hard

"is-extendable@npm:^0.1.0, is-extendable@npm:^0.1.1":
  version: 0.1.1
  resolution: "is-extendable@npm:0.1.1"
  checksum: dd5ca3994a28e1740d1e25192e66eed128e0b2ff161a7ea348e87ae4f616554b486854de423877a2a2c171d5f7cd6e8093b91f54533bc88a59ee1c9838c43879
  languageName: node
  linkType: hard

"is-extendable@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-extendable@npm:1.0.1"
  dependencies:
    is-plain-object: "npm:^2.0.4"
  checksum: 1d6678a5be1563db6ecb121331c819c38059703f0179f52aa80c242c223ee9c6b66470286636c0e63d7163e4d905c0a7d82a096e0b5eaeabb51b9f8d0af0d73f
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.0, is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 818dff679b64f19e228a8205a1e2d09989a98e98def3a817f889208cfcbf918d321b251aadf2c05918194803ebd2eb01b14fc9d0b2bea53d984f4137bfca5e97
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-fullwidth-code-point@npm:2.0.0"
  checksum: e58f3e4a601fc0500d8b2677e26e9fe0cd450980e66adb29d85b6addf7969731e38f8e43ed2ec868a09c101a55ac3d8b78902209269f38c5286bc98f5bc1b4d9
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: fdfa96c8087bf36fc4cd514b474ba2ff404219a4dd4cfa6cf5426404a1eed259bdcdb98f082a71029a48d01f27733e3436ecc6690129a7ec09cb0434bee03a2a
  languageName: node
  linkType: hard

"is-glob@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-glob@npm:3.1.0"
  dependencies:
    is-extglob: "npm:^2.1.0"
  checksum: ba816a35dcf5285de924a8a4654df7b183a86381d73ea3bbf3df3cc61b3ba61fdddf90ee205709a2235b210ee600ee86e5e8600093cf291a662607fd032e2ff4
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 2c4d431b74e00fdda7162cd8e4b763d6f6f217edf97d4f8538b94b8702b150610e2c64961340015fe8df5b1fcee33ccd2e9b62619c4a8a3a155f8de6d6d355fc
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 97b451b41f25135ff021d85c436ff0100d84a039bb87ffd799cbcdbea81ef30c464ced38258cdd34f080be08fc3b076ca1f472086286d2aa43521d6ec6a79f53
  languageName: node
  linkType: hard

"is-number@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-number@npm:3.0.0"
  dependencies:
    kind-of: "npm:^3.0.2"
  checksum: e639c54640b7f029623df24d3d103901e322c0c25ea5bde97cd723c2d0d4c05857a8364ab5c58d963089dbed6bf1d0ffe975cb6aef917e2ad0ccbca653d31b4f
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-obj@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-obj@npm:2.0.0"
  checksum: 85044ed7ba8bd169e2c2af3a178cacb92a97aa75de9569d02efef7f443a824b5e153eba72b9ae3aca6f8ce81955271aa2dc7da67a8b720575d3e38104208cb4e
  languageName: node
  linkType: hard

"is-path-cwd@npm:^2.0.0":
  version: 2.2.0
  resolution: "is-path-cwd@npm:2.2.0"
  checksum: afce71533a427a759cd0329301c18950333d7589533c2c90205bd3fdcf7b91eb92d1940493190567a433134d2128ec9325de2fd281e05be1920fbee9edd22e0a
  languageName: node
  linkType: hard

"is-path-in-cwd@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-path-in-cwd@npm:2.1.0"
  dependencies:
    is-path-inside: "npm:^2.1.0"
  checksum: 674a4282fb3732cf4b4e9ea31e06380d8b074fb8106c4c1742a9f0f3d5650bf059b2c45e5c4cfa7abe847ca88474de63abec323a7fe1eb14f8ec4de2fa951d3a
  languageName: node
  linkType: hard

"is-path-inside@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-path-inside@npm:2.1.0"
  dependencies:
    path-is-inside: "npm:^1.0.2"
  checksum: 50272b9aa301964c0bc4032d5c968e63c516d15bd7800cd06845df97bee637451fcd92a8001b37e309563eff2dffae5fa6d635a0c1d162dc257489c86b1fda51
  languageName: node
  linkType: hard

"is-plain-obj@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-plain-obj@npm:1.1.0"
  checksum: daaee1805add26f781b413fdf192fc91d52409583be30ace35c82607d440da63cc4cac0ac55136716688d6c0a2c6ef3edb2254fecbd1fe06056d6bd15975ee8c
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.3, is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: f050fdd5203d9c81e8c4df1b3ff461c4bc64e8b5ca383bcdde46131361d0a678e80bcf00b5257646f6c636197629644d53bd8e2375aea633de09a82d57e942f4
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4, is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 1d3715d2b7889932349241680032e85d0b492cfcb045acb75ffc2c3085e8d561184f1f7e84b6f8321935b4aea39bc9c6ba74ed595b57ce4881a51dfdbc214e04
  languageName: node
  linkType: hard

"is-resolvable@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-resolvable@npm:1.1.0"
  checksum: 17d5bf39d9268173adf834c23effb6b4e926d809b528a851d87e6fb944e9606ed2c94dfaf1b1b675f922c2990fbc402d754136d8557c90a931ac7fd2f1e4cf07
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: f73732e13f099b2dc879c2a12341cfc22ccaca8dd504e6edae26484bd5707a35d503fba5b4daad530a9b088ced1ae6c9d8200fd92e09b428fe14ea79ce8080b7
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 65158c2feb41ff1edd6bbd6fd8403a69861cf273ff36077982b5d4d68e1d59278c71691216a4a64632bd76d4792d4d1d2553901b6666d84ade13bba5ea7bc7db
  languageName: node
  linkType: hard

"is-stream@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-stream@npm:1.1.0"
  checksum: b8ae7971e78d2e8488d15f804229c6eed7ed36a28f8807a1815938771f4adff0e705218b7dab968270433f67103e4fef98062a0beea55d64835f705ee72c7002
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 2f518b4e47886bb81567faba6ffd0d8a8333cf84336e2e78bf160693972e32ad00fe84b0926491cc598dee576fdc55642c92e62d0cbe96bf36f643b6f956f94d
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: f08f3e255c12442e833f75a9e2b84b2d4882fdfd920513cf2a4a2324f0a5b076c8fd913778e3ea5d258d5183e9d92c0cd20e04b03ab3df05316b049b2670af1e
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 415511da3669e36e002820584e264997ffe277ff136643a3126cc949197e6ca3334d0f12d084e83b1994af2e9c8141275c741cf2b7da5a2ff62dd0cac26f76c4
  languageName: node
  linkType: hard

"is-typedarray@npm:^1.0.0, is-typedarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "is-typedarray@npm:1.0.0"
  checksum: 4c096275ba041a17a13cca33ac21c16bc4fd2d7d7eb94525e7cd2c2f2c1a3ab956e37622290642501ff4310601e413b675cf399ad6db49855527d2163b3eeeec
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 443c35bb86d5e6cc5929cd9c75a4024bb0fff9586ed50b092f94e700b89c43a33b186b76dbc6d54f3d3d09ece689ab38dcdc1af6a482cbe79c0f2da0a17f1299
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 8e0a9c07b0c780949a100e2cab2b5560a48ecd4c61726923c1a9b77b6ab0aa0046c9e7fb2206042296817045376dee2c8ab1dabe08c7c3dfbf195b01275a085b
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 6491eba08acb8dc9532da23cb226b7d0192ede0b88f16199e592e4769db0a077119c1f5d2283d1e0d16d739115f70046e887e477eb0e66cd90e1bb29f28ba647
  languageName: node
  linkType: hard

"is-what@npm:^3.14.1":
  version: 3.14.1
  resolution: "is-what@npm:3.14.1"
  checksum: 4b770b85454c877b6929a84fd47c318e1f8c2ff70fd72fd625bc3fde8e0c18a6e57345b6e7aa1ee9fbd1c608d27cfe885df473036c5c2e40cd2187250804a2c7
  languageName: node
  linkType: hard

"is-windows@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-windows@npm:1.0.2"
  checksum: b32f418ab3385604a66f1b7a3ce39d25e8881dee0bd30816dc8344ef6ff9df473a732bcc1ec4e84fe99b2f229ae474f7133e8e93f9241686cfcf7eebe53ba7a5
  languageName: node
  linkType: hard

"is-wsl@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-wsl@npm:1.1.0"
  checksum: 7ad0012f21092d6f586c7faad84755a8ef0da9b9ec295e4dc82313cce4e1a93a3da3c217265016461f9b141503fe55fa6eb1fd5457d3f05e8d1bdbb48e50c13a
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: a6fa2d370d21be487c0165c7a440d567274fbba1a817f2f0bfa41cc5e3af25041d84267baa22df66696956038a43973e72fca117918c91431920bdef490fa25e
  languageName: node
  linkType: hard

"isarray@npm:1.0.0, isarray@npm:^1.0.0, isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 4199f14a7a13da2177c66c31080008b7124331956f47bca57dd0b6ea9f11687aa25e565a2c7a2b519bc86988d10398e3049a1f5df13c9f6b7664154690ae79fd
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"isobject@npm:^2.0.0":
  version: 2.1.0
  resolution: "isobject@npm:2.1.0"
  dependencies:
    isarray: "npm:1.0.0"
  checksum: c4cafec73b3b2ee11be75dff8dafd283b5728235ac099b07d7873d5182553a707768e208327bbc12931b9422d8822280bf88d894a0024ff5857b3efefb480e7b
  languageName: node
  linkType: hard

"isobject@npm:^3.0.0, isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 03344f5064a82f099a0cd1a8a407f4c0d20b7b8485e8e816c39f249e9416b06c322e8dec5b842b6bb8a06de0af9cb48e7bc1b5352f0fadc2f0abac033db3d4db
  languageName: node
  linkType: hard

"isstream@npm:~0.1.2":
  version: 0.1.2
  resolution: "isstream@npm:0.1.2"
  checksum: a6686a878735ca0a48e0d674dd6d8ad31aedfaf70f07920da16ceadc7577b46d67179a60b313f2e6860cb097a2c2eb3cbd0b89e921ae89199a59a17c3273d66f
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"javascript-stringify@npm:^1.6.0":
  version: 1.6.0
  resolution: "javascript-stringify@npm:1.6.0"
  checksum: 18b2c0f4626c0934c529513d1f0b8770712b8032b552109f7e5fb23adce48b238cc6da276d945101a6a825e4443135d6af6443b0d3be91d739b970a0568a4a3b
  languageName: node
  linkType: hard

"jquery@npm:^3.4.1":
  version: 3.7.1
  resolution: "jquery@npm:3.7.1"
  checksum: 808cfbfb758438560224bf26e17fcd5afc7419170230c810dd11f5c1792e2263e2970cca8d659eb84fcd9acc301edb6d310096e450277d54be4f57071b0c82d9
  languageName: node
  linkType: hard

"js-levenshtein@npm:^1.1.3":
  version: 1.1.6
  resolution: "js-levenshtein@npm:1.1.6"
  checksum: 14045735325ea1fd87f434a74b11d8a14380f090f154747e613529c7cff68b5ee607f5230fa40665d5fb6125a3791f4c223f73b9feca754f989b059f5c05864f
  languageName: node
  linkType: hard

"js-message@npm:1.0.7":
  version: 1.0.7
  resolution: "js-message@npm:1.0.7"
  checksum: 2dc2ff5a594613a9690c157a921999237164a3c213523f406dee23985ed81c92be9afdc7b34e67a2838d447d7d79cbf1662048effe8367bedcd71a950e4e292e
  languageName: node
  linkType: hard

"js-queue@npm:2.0.2":
  version: 2.0.2
  resolution: "js-queue@npm:2.0.2"
  dependencies:
    easy-stack: "npm:^1.0.1"
  checksum: cdadc65576c6d46817f47c27d7a57a1f3acbe3a4da2a4bc7303218f06d6ee7971358db28ec0d6b6835b4cd93332c52792b535064fee706453ef9d2bfe03b4a1a
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.2":
  version: 3.0.2
  resolution: "js-tokens@npm:3.0.2"
  checksum: e3c3ee4d12643d90197628eb022a2884a15f08ea7dcac1ce97fdeee43031fbfc7ede674f2cdbbb582dcd4c94388b22e52d56c6cbeb2ac7d1b57c2f33c405e2ba
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsbn@npm:~0.1.0":
  version: 0.1.1
  resolution: "jsbn@npm:0.1.1"
  checksum: e046e05c59ff880ee4ef68902dbdcb6d2f3c5d60c357d4d68647dc23add556c31c0e5f41bdb7e69e793dd63468bd9e085da3636341048ef577b18f5b713877c0
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: ef22148f9e793180b14d8a145ee6f9f60f301abf443288117b4b6c53d0ecd58354898dc506ccbb553a5f7827965cd38bc5fb726575aae93c5e8915e2de8290e1
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.1, json-parse-better-errors@npm:^1.0.2":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: 2f1287a7c833e397c9ddd361a78638e828fc523038bb3441fd4fc144cfd2c6cd4963ffb9e207e648cf7b692600f1e1e524e965c32df5152120910e4903a47dcb
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 140932564c8f0b88455432e0f33c4cb4086b8868e37524e07e723f4eaedb9425bdc2bafd71bd1d9765bd15fd1e2d126972bc83990f55c467168c228c24d665f3
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-schema@npm:0.4.0":
  version: 0.4.0
  resolution: "json-schema@npm:0.4.0"
  checksum: d4a637ec1d83544857c1c163232f3da46912e971d5bf054ba44fdb88f07d8d359a462b4aec46f2745efbc57053365608d88bc1d7b1729f7b4fc3369765639ed3
  languageName: node
  linkType: hard

"json-stringify-safe@npm:~5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 7dbf35cd0411d1d648dceb6d59ce5857ec939e52e4afc37601aa3da611f0987d5cee5b38d58329ceddf3ed48bd7215229c8d52059ab01f2444a338bf24ed0f37
  languageName: node
  linkType: hard

"json5@npm:^0.5.0":
  version: 0.5.1
  resolution: "json5@npm:0.5.1"
  bin:
    json5: lib/cli.js
  checksum: aca0ab7ccf1883d3fc2ecc16219bc389716a773f774552817deaadb549acc0bb502e317a81946fc0a48f9eb6e0822cf1dc5a097009203f2c94de84c8db02a1f3
  languageName: node
  linkType: hard

"json5@npm:^1.0.1, json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: "npm:^1.2.0"
  bin:
    json5: lib/cli.js
  checksum: 9ee316bf21f000b00752e6c2a3b79ecf5324515a5c60ee88983a1910a45426b643a4f3461657586e8aeca87aaf96f0a519b0516d2ae527a6c3e7eed80f68717f
  languageName: node
  linkType: hard

"json5@npm:^2.1.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7dc94b628d57a66b71fb1b79510d460d662eb975b5f876d723f81549c2e9cd316d58a2ddf742b2b93a4fa6b17b2accaf1a738a0e2ea114bdfb13a32e5377e480
  languageName: node
  linkType: hard

"jsprim@npm:^1.2.2":
  version: 1.4.2
  resolution: "jsprim@npm:1.4.2"
  dependencies:
    assert-plus: "npm:1.0.0"
    extsprintf: "npm:1.3.0"
    json-schema: "npm:0.4.0"
    verror: "npm:1.10.0"
  checksum: 5e4bca99e90727c2040eb4c2190d0ef1fe51798ed5714e87b841d304526190d960f9772acc7108fa1416b61e1122bcd60e4460c91793dce0835df5852aab55af
  languageName: node
  linkType: hard

"killable@npm:^1.0.1":
  version: 1.0.1
  resolution: "killable@npm:1.0.1"
  checksum: 1de0ffe2dd603920685d1b2027136051f095ab42be03e354a43713664e99617cf32cbdb61fc03742c329386d7cf9450edbf4593e50daeaae381e20627b477cd6
  languageName: node
  linkType: hard

"kind-of@npm:^3.0.2, kind-of@npm:^3.0.3, kind-of@npm:^3.2.0":
  version: 3.2.2
  resolution: "kind-of@npm:3.2.2"
  dependencies:
    is-buffer: "npm:^1.1.5"
  checksum: 7e34bc29d4b02c997f92f080de34ebb92033a96736bbb0bb2410e033a7e5ae6571f1fa37b2d7710018f95361473b816c604234197f4f203f9cf149d8ef1574d9
  languageName: node
  linkType: hard

"kind-of@npm:^4.0.0":
  version: 4.0.0
  resolution: "kind-of@npm:4.0.0"
  dependencies:
    is-buffer: "npm:^1.1.5"
  checksum: d6c44c75ee36898142dfc7106afbd50593216c37f96acb81a7ab33ca1a6938ce97d5692b8fc8fccd035f83811a9d97749d68771116441a48eedd0b68e2973165
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 61cdff9623dabf3568b6445e93e31376bee1cdb93f8ba7033d86022c2a9b1791a1d9510e026e6465ebd701a6dd2f7b0808483ad8838341ac52f003f512e0b4c4
  languageName: node
  linkType: hard

"launch-editor-middleware@npm:^2.2.1":
  version: 2.10.0
  resolution: "launch-editor-middleware@npm:2.10.0"
  dependencies:
    launch-editor: "npm:^2.10.0"
  checksum: c6e12b274333940422957cf154f0aeb93d69fc5d49a0e6a8d98e9079a6e408948d02a608011c2928815ec9eaecf5143adce1ae266d728f68d69c286573584286
  languageName: node
  linkType: hard

"launch-editor@npm:^2.10.0, launch-editor@npm:^2.2.1":
  version: 2.10.0
  resolution: "launch-editor@npm:2.10.0"
  dependencies:
    picocolors: "npm:^1.0.0"
    shell-quote: "npm:^1.8.1"
  checksum: 8b5a26be6b0da1da039ed2254b837dea0651a6406ea4dc4c9a5b28ea72862f1b12880135c495baf9d8a08997473b44034172506781744cf82e155451a40b7d51
  languageName: node
  linkType: hard

"less-loader@npm:^4.1.0":
  version: 4.1.0
  resolution: "less-loader@npm:4.1.0"
  dependencies:
    clone: "npm:^2.1.1"
    loader-utils: "npm:^1.1.0"
    pify: "npm:^3.0.0"
  peerDependencies:
    less: ^2.3.1 || ^3.0.0
    webpack: ^2.0.0 || ^3.0.0 || ^4.0.0
  checksum: 2ccd7fb681decc69cbe210d38284697740083b304d702ce082efd7998050bffccdd2f868b65b21a01c53b457241dc7751596fa3dc6c71de688f88fc7054e538d
  languageName: node
  linkType: hard

"less@npm:^3.9.0":
  version: 3.13.1
  resolution: "less@npm:3.13.1"
  dependencies:
    copy-anything: "npm:^2.0.1"
    errno: "npm:^0.1.1"
    graceful-fs: "npm:^4.1.2"
    image-size: "npm:~0.5.0"
    make-dir: "npm:^2.1.0"
    mime: "npm:^1.4.1"
    native-request: "npm:^1.0.5"
    source-map: "npm:~0.6.0"
    tslib: "npm:^1.10.0"
  dependenciesMeta:
    errno:
      optional: true
    graceful-fs:
      optional: true
    image-size:
      optional: true
    make-dir:
      optional: true
    mime:
      optional: true
    native-request:
      optional: true
    source-map:
      optional: true
  bin:
    lessc: ./bin/lessc
  checksum: c529589034670ab2630b4839a06826a6da4138cadd329473332e2ca96a8773658b992e5eef95c05382b0a27a390a94b550e9d7da8099398f5a77d18377830879
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"loader-runner@npm:^2.3.1, loader-runner@npm:^2.4.0":
  version: 2.4.0
  resolution: "loader-runner@npm:2.4.0"
  checksum: 1f723bd8318453c2d073d7befbf891ba6d2a02f22622688bf7d22e7ba527a0f9476c7fdfedc6bfa2b55c0389d9f406f3a5239ed1b33c9088d77cfed085086a1e
  languageName: node
  linkType: hard

"loader-utils@npm:^0.2.16":
  version: 0.2.17
  resolution: "loader-utils@npm:0.2.17"
  dependencies:
    big.js: "npm:^3.1.3"
    emojis-list: "npm:^2.0.0"
    json5: "npm:^0.5.0"
    object-assign: "npm:^4.0.1"
  checksum: d6b65a0d460d2c8621f72e0471127895f4a25ea3a5d2caabf0710c8e58a904af5876834c6ad89d2fbab35e74c6e7f2f4f8137559e6e4e84b74957f4592bcab0b
  languageName: node
  linkType: hard

"loader-utils@npm:^1.0.2, loader-utils@npm:^1.1.0, loader-utils@npm:^1.2.3":
  version: 1.4.2
  resolution: "loader-utils@npm:1.4.2"
  dependencies:
    big.js: "npm:^5.2.2"
    emojis-list: "npm:^3.0.0"
    json5: "npm:^1.0.1"
  checksum: 2b726088b5526f7605615e3e28043ae9bbd2453f4a85898e1151f3c39dbf7a2b65d09f3996bc588d92ac7e717ded529d3e1ea3ea42c433393be84a58234a2f53
  languageName: node
  linkType: hard

"loader-utils@npm:^2.0.0, loader-utils@npm:^2.0.4":
  version: 2.0.4
  resolution: "loader-utils@npm:2.0.4"
  dependencies:
    big.js: "npm:^5.2.2"
    emojis-list: "npm:^3.0.0"
    json5: "npm:^2.1.2"
  checksum: d5654a77f9d339ec2a03d88221a5a695f337bf71eb8dea031b3223420bb818964ba8ed0069145c19b095f6c8b8fd386e602a3fc7ca987042bd8bb1dcc90d7100
  languageName: node
  linkType: hard

"locate-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "locate-path@npm:2.0.0"
  dependencies:
    p-locate: "npm:^2.0.0"
    path-exists: "npm:^3.0.0"
  checksum: 24efa0e589be6aa3c469b502f795126b26ab97afa378846cb508174211515633b770aa0ba610cab113caedab8d2a4902b061a08aaed5297c12ab6f5be4df0133
  languageName: node
  linkType: hard

"locate-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "locate-path@npm:3.0.0"
  dependencies:
    p-locate: "npm:^3.0.0"
    path-exists: "npm:^3.0.0"
  checksum: 3db394b7829a7fe2f4fbdd25d3c4689b85f003c318c5da4052c7e56eed697da8f1bce5294f685c69ff76e32cba7a33629d94396976f6d05fb7f4c755c5e2ae8b
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 33a1c5247e87e022f9713e6213a744557a3e9ec32c5d0b5efb10aa3a38177615bf90221a5592674857039c1a0fd2063b82f285702d37b792d973e9e72ace6c59
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: 762998a63e095412b6099b8290903e0a8ddcb353ac6e2e0f2d7e7d03abd4275fe3c689d88960eb90b0dde4f177554d51a690f22a343932ecbc50a5d111849987
  languageName: node
  linkType: hard

"lodash.defaultsdeep@npm:^4.6.1":
  version: 4.6.1
  resolution: "lodash.defaultsdeep@npm:4.6.1"
  checksum: 0031ca3055d5482fc2e9b55d0ee174a3e956039996fac52dbe94ca67eb50bb72ffc75d0eb86bcc782fefc597ebbc1798df941f27382bec23138e53180427700b
  languageName: node
  linkType: hard

"lodash.kebabcase@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.kebabcase@npm:4.1.1"
  checksum: da5d8f41dbb5bc723d4bf9203d5096ca8da804d6aec3d2b56457156ba6c8d999ff448d347ebd97490da853cb36696ea4da09a431499f1ee8deb17b094ecf4e33
  languageName: node
  linkType: hard

"lodash.mapvalues@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.mapvalues@npm:4.6.0"
  checksum: a976bfc3923d4d8d2034e049ec4700e3aaf141a6143c973d06be3b2c87697923cd0158ee770484ad1af52dfed93ae90d2b76268413db95a42a2f46d7e1754828
  languageName: node
  linkType: hard

"lodash.memoize@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.memoize@npm:4.1.2"
  checksum: c8713e51eccc650422716a14cece1809cfe34bc5ab5e242b7f8b4e2241c2483697b971a604252807689b9dd69bfe3a98852e19a5b89d506b000b4187a1285df8
  languageName: node
  linkType: hard

"lodash.transform@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.transform@npm:4.6.0"
  checksum: ad7f376b00dccff09f8597f19a171f2e074756178ae74887346876e10f6f0d83009460cc0793183cc5ee4e24a72ff86e68031c45b5aa2731c2f681a4dc93fe77
  languageName: node
  linkType: hard

"lodash.uniq@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.uniq@npm:4.5.0"
  checksum: 262d400bb0952f112162a320cc4a75dea4f66078b9e7e3075ffbc9c6aa30b3e9df3cf20e7da7d566105e1ccf7804e4fbd7d804eee0b53de05d83f16ffbf41c5e
  languageName: node
  linkType: hard

"lodash@npm:^4.17.11, lodash@npm:^4.17.15, lodash@npm:^4.17.19, lodash@npm:^4.17.20, lodash@npm:^4.17.21, lodash@npm:^4.17.3":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"log-symbols@npm:^2.2.0":
  version: 2.2.0
  resolution: "log-symbols@npm:2.2.0"
  dependencies:
    chalk: "npm:^2.0.1"
  checksum: 574eb4205f54f0605021aa67ebb372c30ca64e8ddd439efeb8507af83c776dce789e83614e80059014d9e48dcc94c4b60cef2e85f0dc944eea27c799cec62353
  languageName: node
  linkType: hard

"loglevel@npm:^1.6.8":
  version: 1.9.2
  resolution: "loglevel@npm:1.9.2"
  checksum: 1e317fa4648fe0b4a4cffef6de037340592cee8547b07d4ce97a487abe9153e704b98451100c799b032c72bb89c9366d71c9fb8192ada8703269263ae77acdc7
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 655d110220983c1a4b9c0c679a2e8016d4b67f6e9c7b5435ff5979ecdb20d0813f4dec0a08674fcbdd4846a3f07edbb50a36811fd37930b94aaa0d9daceb017e
  languageName: node
  linkType: hard

"lower-case@npm:^1.1.1":
  version: 1.1.4
  resolution: "lower-case@npm:1.1.4"
  checksum: 2153ae5490d655a63addc8e7d2f848c6c94803b342ed2d177f75e8073e9fbb50a733d1432c82e1cb8425fa6eae14b2877bf5bbdcb93ab93bb982fb5c3962c57b
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^4.1.1, lru-cache@npm:^4.1.2":
  version: 4.1.5
  resolution: "lru-cache@npm:4.1.5"
  dependencies:
    pseudomap: "npm:^1.0.2"
    yallist: "npm:^2.1.2"
  checksum: 1ca5306814e5add9ec63556d6fd9b24a4ecdeaef8e9cea52cbf30301e6b88c8d8ddc7cab45b59b56eb763e6c45af911585dc89925a074ab65e1502e3fe8103cf
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"make-dir@npm:^1.0.0":
  version: 1.3.0
  resolution: "make-dir@npm:1.3.0"
  dependencies:
    pify: "npm:^3.0.0"
  checksum: 5eb94f47d7ef41d89d1b8eef6539b8950d5bd99eeba093a942bfd327faa37d2d62227526b88b73633243a2ec7972d21eb0f4e5d62ae4e02a79e389f4a7bb3022
  languageName: node
  linkType: hard

"make-dir@npm:^2.0.0, make-dir@npm:^2.1.0":
  version: 2.1.0
  resolution: "make-dir@npm:2.1.0"
  dependencies:
    pify: "npm:^4.0.1"
    semver: "npm:^5.6.0"
  checksum: ada869944d866229819735bee5548944caef560d7a8536ecbc6536edca28c72add47cc4f6fc39c54fb25d06b58da1f8994cf7d9df7dadea047064749efc085d8
  languageName: node
  linkType: hard

"make-dir@npm:^3.0.2, make-dir@npm:^3.1.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: "npm:^6.0.0"
  checksum: 56aaafefc49c2dfef02c5c95f9b196c4eb6988040cf2c712185c7fe5c99b4091591a7fc4d4eafaaefa70ff763a26f6ab8c3ff60b9e75ea19876f49b18667ecaa
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"manba@npm:^1.3.2":
  version: 1.3.5
  resolution: "manba@npm:1.3.5"
  checksum: ff8e540832fd058e330e90045131383d93d5f20b5d656be8d97986ce4ede820bed45d7b877ea68551cee6cc7acbcafe773558bc8456219a2b7221de26308fd68
  languageName: node
  linkType: hard

"map-cache@npm:^0.2.2":
  version: 0.2.2
  resolution: "map-cache@npm:0.2.2"
  checksum: 05e3eb005c1b80b9f949ca007687640e8c5d0fc88dc45c3c3ab4902a3bec79d66a58f3e3b04d6985d90cd267c629c7b46c977e9c34433e8c11ecfcbb9f0fa290
  languageName: node
  linkType: hard

"map-visit@npm:^1.0.0":
  version: 1.0.0
  resolution: "map-visit@npm:1.0.0"
  dependencies:
    object-visit: "npm:^1.0.0"
  checksum: fb3475e5311939a6147e339999113db607adc11c7c3cd3103e5e9dbf502898416ecba6b1c7c649c6d4d12941de00cee58b939756bdf20a9efe7d4fa5a5738b73
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"md5.js@npm:^1.3.4":
  version: 1.3.5
  resolution: "md5.js@npm:1.3.5"
  dependencies:
    hash-base: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.1.2"
  checksum: b7bd75077f419c8e013fc4d4dada48be71882e37d69a44af65a2f2804b91e253441eb43a0614423a1c91bb830b8140b0dc906bc797245e2e275759584f4efcc5
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 67241f8708c1e665a061d2b042d2d243366e93e5bf1f917693007f6d55111588b952dcbfd3ea9c2d0969fb754aad81b30fdcfdcc24546495fc3b24336b28d4bd
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.4":
  version: 2.0.4
  resolution: "mdn-data@npm:2.0.4"
  checksum: a935c4530b938407481f7d0ccb82119ae618d9c673d2ee78bb10dcba8bd0ccbe2e2c7fe850ddc60b67e08f4c9d97f50b900993f6c2f2926e64a52ed6baa00b3a
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: d160f31246907e79fed398470285f21bafb45a62869dc469b1c8877f3f064f5eabc4bcc122f9479b8b605bc5c76187d7871cf84c4ee3ecd3e487da1993279928
  languageName: node
  linkType: hard

"memory-fs@npm:^0.4.1":
  version: 0.4.1
  resolution: "memory-fs@npm:0.4.1"
  dependencies:
    errno: "npm:^0.1.3"
    readable-stream: "npm:^2.0.1"
  checksum: f114c44ad8285103cb0e71420cf5bb628d3eb6cbd918197f5951590ff56ba2072f4a97924949c170320cdf180d2da4e8d16a0edd92ba0ca2d2de51dc932841e2
  languageName: node
  linkType: hard

"memory-fs@npm:^0.5.0":
  version: 0.5.0
  resolution: "memory-fs@npm:0.5.0"
  dependencies:
    errno: "npm:^0.1.3"
    readable-stream: "npm:^2.0.1"
  checksum: 2737a27b14a9e8b8cd757be2ad99e8cc504b78a78aba9d6aa18ff1ef528e2223a433413d2df6ab5332997a5a8ccf075e6c6e90e31ab732a55455ca620e4a720b
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 866b7094afd9293b5ea5dcd82d71f80e51514bed33b4c4e9f516795dc366612a4cbb4dc94356e943a8a6914889a914530badff27f397191b9b75cda20b6bae93
  languageName: node
  linkType: hard

"merge-source-map@npm:^1.1.0":
  version: 1.1.0
  resolution: "merge-source-map@npm:1.1.0"
  dependencies:
    source-map: "npm:^0.6.1"
  checksum: ac0e0192c9c7e30056c5baa939434c0d1015faa5c7ce7936ad77600f1752c03099134cb33c50f1bb32ec25350e191ca2392c6b76b1eaca89c7c989e42403655f
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.2.3":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: bdf7cc72ff0a33e3eede03708c08983c4d7a173f91348b4b1e4f47d4cdbf734433ad971e7d1e8c77247d9e5cd8adb81ea4c67b0a2db526b758b2233d7814b8b2
  languageName: node
  linkType: hard

"micromatch@npm:^3.1.10, micromatch@npm:^3.1.4":
  version: 3.1.10
  resolution: "micromatch@npm:3.1.10"
  dependencies:
    arr-diff: "npm:^4.0.0"
    array-unique: "npm:^0.3.2"
    braces: "npm:^2.3.1"
    define-property: "npm:^2.0.2"
    extend-shallow: "npm:^3.0.2"
    extglob: "npm:^2.0.4"
    fragment-cache: "npm:^0.2.1"
    kind-of: "npm:^6.0.2"
    nanomatch: "npm:^1.2.9"
    object.pick: "npm:^1.3.0"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.2"
  checksum: 531a32e7ac92bef60657820202be71b63d0f945c08a69cc4c239c0b19372b751483d464a850a2e3a5ff6cc9060641e43d44c303af104c1a27493d137d8af017f
  languageName: node
  linkType: hard

"miller-rabin@npm:^4.0.0":
  version: 4.0.1
  resolution: "miller-rabin@npm:4.0.1"
  dependencies:
    bn.js: "npm:^4.0.0"
    brorand: "npm:^1.0.1"
  bin:
    miller-rabin: bin/miller-rabin
  checksum: 26b2b96f6e49dbcff7faebb78708ed2f5f9ae27ac8cbbf1d7c08f83cf39bed3d418c0c11034dce997da70d135cc0ff6f3a4c15dc452f8e114c11986388a64346
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-db@npm:>= 1.43.0 < 2":
  version: 1.54.0
  resolution: "mime-db@npm:1.54.0"
  checksum: 8d907917bc2a90fa2df842cdf5dfeaf509adc15fe0531e07bb2f6ab15992416479015828d6a74200041c492e42cce3ebf78e5ce714388a0a538ea9c53eece284
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:~2.1.17, mime-types@npm:~2.1.19, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0, mime@npm:^1.4.1":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mime@npm:^2.0.3, mime@npm:^2.4.4":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: a7f2589900d9c16e3bdf7672d16a6274df903da958c1643c9c45771f0478f3846dcb1097f31eb9178452570271361e2149310931ec705c037210fc69639c8e6c
  languageName: node
  linkType: hard

"mimic-fn@npm:^1.0.0":
  version: 1.2.0
  resolution: "mimic-fn@npm:1.2.0"
  checksum: ad55214aec6094c0af4c0beec1a13787556f8116ed88807cf3f05828500f21f93a9482326bcd5a077ae91e3e8795b4e76b5b4c8bb12237ff0e4043a365516cba
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"mini-css-extract-plugin@npm:^0.8.0":
  version: 0.8.2
  resolution: "mini-css-extract-plugin@npm:0.8.2"
  dependencies:
    loader-utils: "npm:^1.1.0"
    normalize-url: "npm:1.9.1"
    schema-utils: "npm:^1.0.0"
    webpack-sources: "npm:^1.1.0"
  peerDependencies:
    webpack: ^4.4.0
  checksum: 08c9b49da26448d726edbd1469528f21f9792c9c14b6d6cb50f9a511f8686b016226e91113c2533f60ebb73a3b3cc9909d5425e99f29cf0cd4bebebffea04519
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0, minimalistic-assert@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: 96730e5601cd31457f81a296f521eb56036e6f69133c0b18c13fe941109d53ad23a4204d946a0d638d7f3099482a0cec8c9bb6d642604612ce43ee536be3dddd
  languageName: node
  linkType: hard

"minimalistic-crypto-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-crypto-utils@npm:1.0.1"
  checksum: 790ecec8c5c73973a4fbf2c663d911033e8494d5fb0960a4500634766ab05d6107d20af896ca2132e7031741f19888154d44b2408ada0852446705441383e9f8
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mississippi@npm:^2.0.0":
  version: 2.0.0
  resolution: "mississippi@npm:2.0.0"
  dependencies:
    concat-stream: "npm:^1.5.0"
    duplexify: "npm:^3.4.2"
    end-of-stream: "npm:^1.1.0"
    flush-write-stream: "npm:^1.0.0"
    from2: "npm:^2.1.0"
    parallel-transform: "npm:^1.1.0"
    pump: "npm:^2.0.1"
    pumpify: "npm:^1.3.3"
    stream-each: "npm:^1.1.0"
    through2: "npm:^2.0.0"
  checksum: f84beaa40491c2466c4a3d15025ceb1ecc822a8e5ef4f6e5622aeb832b32e4eda056c3982337ae2c1e4b3b77fb431af4d3874923c26dee0c026601547589b2db
  languageName: node
  linkType: hard

"mississippi@npm:^3.0.0":
  version: 3.0.0
  resolution: "mississippi@npm:3.0.0"
  dependencies:
    concat-stream: "npm:^1.5.0"
    duplexify: "npm:^3.4.2"
    end-of-stream: "npm:^1.1.0"
    flush-write-stream: "npm:^1.0.0"
    from2: "npm:^2.1.0"
    parallel-transform: "npm:^1.1.0"
    pump: "npm:^3.0.0"
    pumpify: "npm:^1.3.3"
    stream-each: "npm:^1.1.0"
    through2: "npm:^2.0.0"
  checksum: 97424a331ce1b9f789a0d3fa47d725dad9adfe5e0ead8bc458ba9fb51c4d2630df6b0966ca9dcbb4c90db48737d58126cbf0e3c170697bf41c265606efa91103
  languageName: node
  linkType: hard

"mixin-deep@npm:^1.2.0":
  version: 1.3.2
  resolution: "mixin-deep@npm:1.3.2"
  dependencies:
    for-in: "npm:^1.0.2"
    is-extendable: "npm:^1.0.1"
  checksum: cb39ffb73c377222391af788b4c83d1a6cecb2d9fceb7015384f8deb46e151a9b030c21ef59a79cb524d4557e3f74c7248ab948a62a6e7e296b42644863d183b
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.1, mkdirp@npm:^0.5.3, mkdirp@npm:~0.5.1":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: e2e2be789218807b58abced04e7b49851d9e46e88a2f9539242cc8a92c9b5c3a0b9bab360bd3014e02a140fc4fbc58e31176c408b493f8a2a6f4986bd7527b01
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"moment@npm:^2.24.0":
  version: 2.30.1
  resolution: "moment@npm:2.30.1"
  checksum: 865e4279418c6de666fca7786607705fd0189d8a7b7624e2e56be99290ac846f90878a6f602e34b4e0455c549b85385b1baf9966845962b313699e7cb847543a
  languageName: node
  linkType: hard

"move-concurrently@npm:^1.0.1":
  version: 1.0.1
  resolution: "move-concurrently@npm:1.0.1"
  dependencies:
    aproba: "npm:^1.1.1"
    copy-concurrently: "npm:^1.0.0"
    fs-write-stream-atomic: "npm:^1.0.8"
    mkdirp: "npm:^0.5.1"
    rimraf: "npm:^2.5.4"
    run-queue: "npm:^1.0.3"
  checksum: 0fe81acf3bbbc322013c2f4ee4a48cf8d180a7d925fb9284c0f1f444e862d7eb0421ee074b68d35357a12f0d5e94a322049dc9da480672331b5b8895743eb66a
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"multicast-dns-service-types@npm:^1.1.0":
  version: 1.1.0
  resolution: "multicast-dns-service-types@npm:1.1.0"
  checksum: 25abc0e9ee509f38d874e22b03d563b16009d3976760d29bed25bf70ea992cfe30b0403743f49342279c67178a03311d31ecc1ec54bf79af2e6fe55f11af2660
  languageName: node
  linkType: hard

"multicast-dns@npm:^6.0.1":
  version: 6.2.3
  resolution: "multicast-dns@npm:6.2.3"
  dependencies:
    dns-packet: "npm:^1.3.1"
    thunky: "npm:^1.0.2"
  bin:
    multicast-dns: cli.js
  checksum: 972fc50869e922d80d66eeb91ad39fd2e107241e0c791fc914e76578e4f7f3dfe3bf007020dd4d7ed4d0ffd69d9aa2238a9f8bbb4d160bd6eb3f35dde0c2c513
  languageName: node
  linkType: hard

"mz@npm:^2.4.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: "npm:^1.0.0"
    object-assign: "npm:^4.0.1"
    thenify-all: "npm:^1.0.0"
  checksum: 103114e93f87362f0b56ab5b2e7245051ad0276b646e3902c98397d18bb8f4a77f2ea4a2c9d3ad516034ea3a56553b60d3f5f78220001ca4c404bd711bd0af39
  languageName: node
  linkType: hard

"nan@npm:^2.12.1":
  version: 2.22.2
  resolution: "nan@npm:2.22.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 971f963b8120631880fa47a389c71b00cadc1c1b00ef8f147782a3f4387d4fc8195d0695911272d57438c11562fb27b24c4ae5f8c05d5e4eeb4478ba51bb73c5
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.8":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"nanomatch@npm:^1.2.9":
  version: 1.2.13
  resolution: "nanomatch@npm:1.2.13"
  dependencies:
    arr-diff: "npm:^4.0.0"
    array-unique: "npm:^0.3.2"
    define-property: "npm:^2.0.2"
    extend-shallow: "npm:^3.0.2"
    fragment-cache: "npm:^0.2.1"
    is-windows: "npm:^1.0.2"
    kind-of: "npm:^6.0.2"
    object.pick: "npm:^1.3.0"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.1"
  checksum: 0f5cefa755ca2e20c86332821995effb24acb79551ddaf51c1b9112628cad234a0d8fd9ac6aa56ad1f8bfad6ff6ae86e851acb960943249d9fa44b091479953a
  languageName: node
  linkType: hard

"native-request@npm:^1.0.5":
  version: 1.1.2
  resolution: "native-request@npm:1.1.2"
  checksum: 6126aef460472217e8fdc317e0461505500ca4812bb8c20a2a1ad3bfb6cd16e49a7a3b89f272701f0cb756e045deb428985051069c43140b9355f351c8cba344
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 3e677139c7fb7628a6f36335bf11a885a62c21d5390204590a1a214a5631fcbe5ea74ef6a610b60afe84b4d975cbe0566a23f20ee17c77c73e74b80032108dea
  languageName: node
  linkType: hard

"neo-async@npm:^2.5.0, neo-async@npm:^2.6.0, neo-async@npm:^2.6.1":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: c2f5a604a54a8ec5438a342e1f356dff4bc33ccccdb6dc668d94fe8e5eccfc9d2c2eea6064b0967a767ba63b33763f51ccf2cd2441b461a7322656c1f06b3f5d
  languageName: node
  linkType: hard

"next-tick@npm:^1.1.0":
  version: 1.1.0
  resolution: "next-tick@npm:1.1.0"
  checksum: 3ba80dd805fcb336b4f52e010992f3e6175869c8d88bf4ff0a81d5d66e6049f89993463b28211613e58a6b7fe93ff5ccbba0da18d4fa574b96289e8f0b577f28
  languageName: node
  linkType: hard

"nice-try@npm:^1.0.4":
  version: 1.0.5
  resolution: "nice-try@npm:1.0.5"
  checksum: 95568c1b73e1d0d4069a3e3061a2102d854513d37bcfda73300015b7ba4868d3b27c198d1dbbd8ebdef4112fc2ed9e895d4a0f2e1cce0bd334f2a1346dc9205f
  languageName: node
  linkType: hard

"no-case@npm:^2.2.0":
  version: 2.3.2
  resolution: "no-case@npm:2.3.2"
  dependencies:
    lower-case: "npm:^1.1.1"
  checksum: 63f306e83c18efa0bb37f1c23a25baf4ccf5ebaec70b482fa04d4c5bf8bbb8bcc9a8fbcd818af828ab69f2b602153daf81ec26e448b2bda2d704b8d0c7eec8fa
  languageName: node
  linkType: hard

"node-forge@npm:^0.10.0":
  version: 0.10.0
  resolution: "node-forge@npm:0.10.0"
  checksum: 9cbf9ac8fc3889a5a46b0248f7238ee4014770bf31d22e04c0c7f04ed91c8be4584c5f534cdf6037e99f236c636c925cba960501ed2b850e077512e152760663
  languageName: node
  linkType: hard

"node-gyp-build@npm:^4.3.0":
  version: 4.8.4
  resolution: "node-gyp-build@npm:4.8.4"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 444e189907ece2081fe60e75368784f7782cfddb554b60123743dfb89509df89f1f29c03bbfa16b3a3e0be3f48799a4783f487da6203245fa5bed239ba7407e1
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: bd8d8c76b06be761239b0c8680f655f6a6e90b48e44d43415b11c16f7e8c15be346fba0cbf71588c7cdfb52c419d928a7d3db353afc1d952d19756237d8f10b9
  languageName: node
  linkType: hard

"node-ipc@npm:^9.1.1":
  version: 9.2.1
  resolution: "node-ipc@npm:9.2.1"
  dependencies:
    event-pubsub: "npm:4.3.0"
    js-message: "npm:1.0.7"
    js-queue: "npm:2.0.2"
  checksum: ed6448e2c643a831b9795a52de727658417a2cbedd3a59988b9be7654383982577dc507e0ec13211d10ed90377124366e3869a137b05093a424caf647eba0523
  languageName: node
  linkType: hard

"node-libs-browser@npm:^2.2.1":
  version: 2.2.1
  resolution: "node-libs-browser@npm:2.2.1"
  dependencies:
    assert: "npm:^1.1.1"
    browserify-zlib: "npm:^0.2.0"
    buffer: "npm:^4.3.0"
    console-browserify: "npm:^1.1.0"
    constants-browserify: "npm:^1.0.0"
    crypto-browserify: "npm:^3.11.0"
    domain-browser: "npm:^1.1.1"
    events: "npm:^3.0.0"
    https-browserify: "npm:^1.0.0"
    os-browserify: "npm:^0.3.0"
    path-browserify: "npm:0.0.1"
    process: "npm:^0.11.10"
    punycode: "npm:^1.2.4"
    querystring-es3: "npm:^0.2.0"
    readable-stream: "npm:^2.3.3"
    stream-browserify: "npm:^2.0.1"
    stream-http: "npm:^2.7.2"
    string_decoder: "npm:^1.0.0"
    timers-browserify: "npm:^2.0.4"
    tty-browserify: "npm:0.0.0"
    url: "npm:^0.11.0"
    util: "npm:^0.11.0"
    vm-browserify: "npm:^1.0.1"
  checksum: 0e05321a6396408903ed642231d2bca7dd96492d074c7af161ba06a63c95378bd3de50b4105eccbbc02d93ba3da69f0ff5e624bc2a8c92ca462ceb6a403e7986
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-package-data@npm:^2.5.0":
  version: 2.5.0
  resolution: "normalize-package-data@npm:2.5.0"
  dependencies:
    hosted-git-info: "npm:^2.1.4"
    resolve: "npm:^1.10.0"
    semver: "npm:2 || 3 || 4 || 5"
    validate-npm-package-license: "npm:^3.0.1"
  checksum: 357cb1646deb42f8eb4c7d42c4edf0eec312f3628c2ef98501963cc4bbe7277021b2b1d977f982b2edce78f5a1014613ce9cf38085c3df2d76730481357ca504
  languageName: node
  linkType: hard

"normalize-path@npm:^2.1.1":
  version: 2.1.1
  resolution: "normalize-path@npm:2.1.1"
  dependencies:
    remove-trailing-separator: "npm:^1.0.1"
  checksum: db814326ff88057437233361b4c7e9cac7b54815b051b57f2d341ce89b1d8ec8cbd43e7fa95d7652b3b69ea8fcc294b89b8530d556a84d1bdace94229e1e9a8b
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: bf39b73a63e0a42ad1a48c2bd1bda5a07ede64a7e2567307a407674e595bcff0fa0d57e8e5f1e7fa5e91000797c7615e13613227aaaa4d6d6e87f5bd5cc95de6
  languageName: node
  linkType: hard

"normalize-url@npm:1.9.1":
  version: 1.9.1
  resolution: "normalize-url@npm:1.9.1"
  dependencies:
    object-assign: "npm:^4.0.1"
    prepend-http: "npm:^1.0.0"
    query-string: "npm:^4.1.0"
    sort-keys: "npm:^1.0.0"
  checksum: 5ecd525f743c3fb5370d2bab8e78446f3e3bd7c0c97a5fd3f0bc0c5f396fbd117d13c9118766128d25ed575755cb539dc33a38419f18ca9d8577c8d1cd7a8daf
  languageName: node
  linkType: hard

"normalize-url@npm:^3.0.0":
  version: 3.3.0
  resolution: "normalize-url@npm:3.3.0"
  checksum: 07c2fdcfac898d97eee256d7f62108034224588263fadc45caba0cc402b2bd59b9bd9e66e0c54ac9ee902fcb27af80cacd36375f641409f46749b4eb10f47352
  languageName: node
  linkType: hard

"npm-run-path@npm:^2.0.0":
  version: 2.0.2
  resolution: "npm-run-path@npm:2.0.2"
  dependencies:
    path-key: "npm:^2.0.0"
  checksum: 95549a477886f48346568c97b08c4fda9cdbf7ce8a4fbc2213f36896d0d19249e32d68d7451bdcbca8041b5fba04a6b2c4a618beaf19849505c05b700740f1de
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.0":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"nth-check@npm:^1.0.2":
  version: 1.0.2
  resolution: "nth-check@npm:1.0.2"
  dependencies:
    boolbase: "npm:~1.0.0"
  checksum: 1a67ce53a99e276eea672f892d712b29f3e6802bbbef7285ffab72ecea4f972e8244defac1ebded0daffabf459def31355bb9c64e5657ac2ab032c13f185d0fd
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 5fee7ff309727763689cfad844d979aedd2204a817fbaaf0e1603794a7c20db28548d7b024692f953557df6ce4a0ee4ae46cd8ebd9b36cfb300b9226b567c479
  languageName: node
  linkType: hard

"num2fraction@npm:^1.2.2":
  version: 1.2.2
  resolution: "num2fraction@npm:1.2.2"
  checksum: 3bf17b44af00508a2b0370146629710645c3e3ff3c052893680efe3f4a6ff5c953ce9e54734013b02b35744a49352d54fbc5d8b455fac979047ef17dd8ec74bd
  languageName: node
  linkType: hard

"oauth-sign@npm:~0.9.0":
  version: 0.9.0
  resolution: "oauth-sign@npm:0.9.0"
  checksum: fc92a516f6ddbb2699089a2748b04f55c47b6ead55a77cd3a2cbbce5f7af86164cb9425f9ae19acfd066f1ad7d3a96a67b8928c6ea946426f6d6c29e448497c2
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.0":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-copy@npm:^0.1.0":
  version: 0.1.0
  resolution: "object-copy@npm:0.1.0"
  dependencies:
    copy-descriptor: "npm:^0.1.0"
    define-property: "npm:^0.2.5"
    kind-of: "npm:^3.0.3"
  checksum: 79314b05e9d626159a04f1d913f4c4aba9eae8848511cf5f4c8e3b04bb3cc313b65f60357f86462c959a14c2d58380fedf89b6b32ecec237c452a5ef3900a293
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"object-is@npm:^1.1.5":
  version: 1.1.6
  resolution: "object-is@npm:1.1.6"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
  checksum: 506af444c4dce7f8e31f34fc549e2fb8152d6b9c4a30c6e62852badd7f520b579c679af433e7a072f9d78eb7808d230dc12e1cf58da9154dfbf8813099ea0fe0
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b11f7ccdbc6d406d1f186cdadb9d54738e347b2692a14439ca5ac70c225fa6db46db809711b78589866d47b25fc3e8dee0b4c722ac751e11180f9380e3d8601d
  languageName: node
  linkType: hard

"object-visit@npm:^1.0.0":
  version: 1.0.1
  resolution: "object-visit@npm:1.0.1"
  dependencies:
    isobject: "npm:^3.0.0"
  checksum: 086b475bda24abd2318d2b187c3e928959b89f5cb5883d6fe5a42d03719b61fc18e765f658de9ac8730e67ba9ff26d61e73d991215948ff9ecefe771e0071029
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.0, object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 3b2732bd860567ea2579d1567525168de925a8d852638612846bd8082b3a1602b7b89b67b09913cbb5b9bd6e95923b2ae73580baa9d99cb4e990564e8cbf5ddc
  languageName: node
  linkType: hard

"object.getownpropertydescriptors@npm:^2.0.3, object.getownpropertydescriptors@npm:^2.1.0":
  version: 2.1.8
  resolution: "object.getownpropertydescriptors@npm:2.1.8"
  dependencies:
    array.prototype.reduce: "npm:^1.0.6"
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
    gopd: "npm:^1.0.1"
    safe-array-concat: "npm:^1.1.2"
  checksum: 553e9562fd86637c9c169df23a56f1d810d8c9b580a6d4be11552c009f32469310c9347f3d10325abf0cd9cfe4afc521a1e903fbd24148ae7ec860e1e7c75cf3
  languageName: node
  linkType: hard

"object.pick@npm:^1.3.0":
  version: 1.3.0
  resolution: "object.pick@npm:1.3.0"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: cd316ec986e49895a28f2df9182de9cdeee57cd2a952c122aacc86344c28624fe002d9affc4f48b5014ec7c033da9942b08821ddb44db8c5bac5b3ec54bdc31e
  languageName: node
  linkType: hard

"object.values@npm:^1.1.0":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 3c47814fdc64842ae3d5a74bc9d06bdd8d21563c04d9939bf6716a9c00596a4ebc342552f8934013d1ec991c74e3671b26710a0c51815f0b603795605ab6b2c9
  languageName: node
  linkType: hard

"obuf@npm:^1.0.0, obuf@npm:^1.1.2":
  version: 1.1.2
  resolution: "obuf@npm:1.1.2"
  checksum: 520aaac7ea701618eacf000fc96ae458e20e13b0569845800fc582f81b386731ab22d55354b4915d58171db00e79cfcd09c1638c02f89577ef092b38c65b7d81
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: f649e65c197bf31505a4c0444875db0258e198292f34b884d73c2f751e91792ef96bb5cf89aa0f4fecc2e4dc662461dda606b1274b0e564f539cae5d2f5fc32f
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^2.0.0":
  version: 2.0.1
  resolution: "onetime@npm:2.0.1"
  dependencies:
    mimic-fn: "npm:^1.0.0"
  checksum: b4e44a8c34e70e02251bfb578a6e26d6de6eedbed106cd78211d2fd64d28b6281d54924696554e4e966559644243753ac5df73c87f283b0927533d3315696215
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"open@npm:^6.3.0":
  version: 6.4.0
  resolution: "open@npm:6.4.0"
  dependencies:
    is-wsl: "npm:^1.1.0"
  checksum: 447115632b4f3939fa0d973c33e17f28538fd268fd8257fc49763f7de6e76d29d65585b15998bbd2137337cfb70a92084a0e1b183a466e53a4829f704f295823
  languageName: node
  linkType: hard

"opener@npm:^1.5.1":
  version: 1.5.2
  resolution: "opener@npm:1.5.2"
  bin:
    opener: bin/opener-bin.js
  checksum: dd56256ab0cf796585617bc28e06e058adf09211781e70b264c76a1dbe16e90f868c974e5bf5309c93469157c7d14b89c35dc53fe7293b0e40b4d2f92073bc79
  languageName: node
  linkType: hard

"opn@npm:^5.5.0":
  version: 5.5.0
  resolution: "opn@npm:5.5.0"
  dependencies:
    is-wsl: "npm:^1.1.0"
  checksum: 03f78b1ab464fd0d97543e2a90e47ca872e2324696bc13f741467693060fe058e87e38e9cfc9f3b568e60dfb31579fbe664d8e806b2f219262c423da953bba4c
  languageName: node
  linkType: hard

"ora@npm:^3.4.0":
  version: 3.4.0
  resolution: "ora@npm:3.4.0"
  dependencies:
    chalk: "npm:^2.4.2"
    cli-cursor: "npm:^2.1.0"
    cli-spinners: "npm:^2.0.0"
    log-symbols: "npm:^2.2.0"
    strip-ansi: "npm:^5.2.0"
    wcwidth: "npm:^1.0.1"
  checksum: 04cb375f222c36a16a95e6c39c473644a99a42fc34d35c37507cb836ea0a71f4d831fcd53198a460869114b2730891d63cc1047304afe5ddb078974d468edfb1
  languageName: node
  linkType: hard

"os-browserify@npm:^0.3.0":
  version: 0.3.0
  resolution: "os-browserify@npm:0.3.0"
  checksum: 6ff32cb1efe2bc6930ad0fd4c50e30c38010aee909eba8d65be60af55efd6cbb48f0287e3649b4e3f3a63dce5a667b23c187c4293a75e557f0d5489d735bcf52
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 6dfeb3455bff92ec3f16a982d4e3e65676345f6902d9f5ded1d8265a6318d0200ce461956d6d1c70053c7fe9f9fe65e552faac03f8140d37ef0fdd108e67013a
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 6b8552339a71fe7bd424d01d8451eea92d379a711fc62f6b2fe64cad8a472c7259a236c9a22b4733abca0b5666ad503cb497792a0478c5af31ded793d00937e7
  languageName: node
  linkType: hard

"p-finally@npm:^2.0.0":
  version: 2.0.1
  resolution: "p-finally@npm:2.0.1"
  checksum: a4ee34179f5e0eb5417462ca5afbca4b6b537b051ea87c8ec7649ffb2b60a8e82a06441792fe496ab0d0156c4060a3dfd707973915a1b8369b00f2531e3eab94
  languageName: node
  linkType: hard

"p-limit@npm:^1.0.0, p-limit@npm:^1.1.0":
  version: 1.3.0
  resolution: "p-limit@npm:1.3.0"
  dependencies:
    p-try: "npm:^1.0.0"
  checksum: 5c1b1d53d180b2c7501efb04b7c817448e10efe1ba46f4783f8951994d5027e4cd88f36ad79af50546682594c4ebd11702ac4b9364c47f8074890e2acad0edee
  languageName: node
  linkType: hard

"p-limit@npm:^2.0.0, p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 8da01ac53efe6a627080fafc127c873da40c18d87b3f5d5492d465bb85ec7207e153948df6b9cbaeb130be70152f874229b8242ee2be84c0794082510af97f12
  languageName: node
  linkType: hard

"p-locate@npm:^2.0.0":
  version: 2.0.0
  resolution: "p-locate@npm:2.0.0"
  dependencies:
    p-limit: "npm:^1.1.0"
  checksum: 82da4be88fb02fd29175e66021610c881938d3cc97c813c71c1a605fac05617d57fd5d3b337494a6106c0edb2a37c860241430851411f1b265108cead34aee67
  languageName: node
  linkType: hard

"p-locate@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-locate@npm:3.0.0"
  dependencies:
    p-limit: "npm:^2.0.0"
  checksum: 7b7f06f718f19e989ce6280ed4396fb3c34dabdee0df948376483032f9d5ec22fdf7077ec942143a75827bb85b11da72016497fc10dac1106c837ed593969ee8
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 1b476ad69ad7f6059744f343b26d51ce091508935c1dbb80c4e0a2f397ffce0ca3a1f9f5cd3c7ce19d7929a09719d5c65fe70d8ee289c3f267cd36f2881813e9
  languageName: node
  linkType: hard

"p-map@npm:^2.0.0":
  version: 2.1.0
  resolution: "p-map@npm:2.1.0"
  checksum: 735dae87badd4737a2dd582b6d8f93e49a1b79eabbc9815a4d63a528d5e3523e978e127a21d784cccb637010e32103a40d2aaa3ab23ae60250b1a820ca752043
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-retry@npm:^3.0.1":
  version: 3.0.1
  resolution: "p-retry@npm:3.0.1"
  dependencies:
    retry: "npm:^0.12.0"
  checksum: 4fbec30cb0d8d10c5d9d1787a2d2c2b5ee60ddfa1897e86ec4e556ca1dff0901859872d7a7ecc33dd94af6e1c3a92ed79cc828161bbd221a2a1e464971c51b1b
  languageName: node
  linkType: hard

"p-try@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-try@npm:1.0.0"
  checksum: 757ba31de5819502b80c447826fac8be5f16d3cb4fbf9bc8bc4971dba0682e84ac33e4b24176ca7058c69e29f64f34d8d9e9b08e873b7b7bb0aa89d620fa224a
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: c36c19907734c904b16994e6535b02c36c2224d433e01a2f1ab777237f4d86e6289fd5fd464850491e940379d4606ed850c03e0f9ab600b0ebddb511312e177f
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"pako@npm:~1.0.5":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 86dd99d8b34c3930345b8bbeb5e1cd8a05f608eeb40967b293f72fe469d0e9c88b783a8777e4cc7dc7c91ce54c5e93d88ff4b4f060e6ff18408fd21030d9ffbe
  languageName: node
  linkType: hard

"parallel-transform@npm:^1.1.0":
  version: 1.2.0
  resolution: "parallel-transform@npm:1.2.0"
  dependencies:
    cyclist: "npm:^1.0.1"
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^2.1.5"
  checksum: ab0e58569e73681ca4b9c9228189bdb6cbea535295fae344cf0d8342fd33a950961914f3c414f81894c1498fb9ad1c079b4625d2b7ceae9e6ab812f22e3bea3f
  languageName: node
  linkType: hard

"param-case@npm:2.1.x":
  version: 2.1.1
  resolution: "param-case@npm:2.1.1"
  dependencies:
    no-case: "npm:^2.2.0"
  checksum: 8ea1b8472fd51d5f50b28d1d754899713805d05f2241e9b8c4acafa2c500b3f47457a3b4932ab75220f14d2c69180bb7338b78a45576e2b4d90da1e6f0285833
  languageName: node
  linkType: hard

"parse-asn1@npm:^5.0.0, parse-asn1@npm:^5.1.7":
  version: 5.1.7
  resolution: "parse-asn1@npm:5.1.7"
  dependencies:
    asn1.js: "npm:^4.10.1"
    browserify-aes: "npm:^1.2.0"
    evp_bytestokey: "npm:^1.0.3"
    hash-base: "npm:~3.0"
    pbkdf2: "npm:^3.1.2"
    safe-buffer: "npm:^5.2.1"
  checksum: 05eb5937405c904eb5a7f3633bab1acc11f4ae3478a07ef5c6d81ce88c3c0e505ff51f9c7b935ebc1265c868343793698fc91025755a895d0276f620f95e8a82
  languageName: node
  linkType: hard

"parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-json@npm:4.0.0"
  dependencies:
    error-ex: "npm:^1.3.1"
    json-parse-better-errors: "npm:^1.0.1"
  checksum: 8d80790b772ccb1bcea4e09e2697555e519d83d04a77c2b4237389b813f82898943a93ffff7d0d2406203bdd0c30dcf95b1661e3a53f83d0e417f053957bef32
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 77947f2253005be7a12d858aedbafa09c9ae39eb4863adf330f7b416ca4f4a08132e453e08de2db46459256fb66afaac5ee758b44fe6541b7cdaf9d252e59585
  languageName: node
  linkType: hard

"parse5-htmlparser2-tree-adapter@npm:^6.0.0":
  version: 6.0.1
  resolution: "parse5-htmlparser2-tree-adapter@npm:6.0.1"
  dependencies:
    parse5: "npm:^6.0.1"
  checksum: dfa5960e2aaf125707e19a4b1bc333de49232eba5a6ffffb95d313a7d6087c3b7a274b58bee8d3bd41bdf150638815d1d601a42bbf2a0345208c3c35b1279556
  languageName: node
  linkType: hard

"parse5@npm:^5.1.1":
  version: 5.1.1
  resolution: "parse5@npm:5.1.1"
  checksum: b0f87a77a7fea5f242e3d76917c983bbea47703b9371801d51536b78942db6441cbda174bf84eb30e47315ddc6f8a0b57d68e562c790154430270acd76c1fa03
  languageName: node
  linkType: hard

"parse5@npm:^6.0.1":
  version: 6.0.1
  resolution: "parse5@npm:6.0.1"
  checksum: 595821edc094ecbcfb9ddcb46a3e1fe3a718540f8320eff08b8cf6742a5114cce2d46d45f95c26191c11b184dcaf4e2960abcd9c5ed9eb9393ac9a37efcfdecb
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.2, parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"pascalcase@npm:^0.1.1":
  version: 0.1.1
  resolution: "pascalcase@npm:0.1.1"
  checksum: 48dfe90618e33810bf58211d8f39ad2c0262f19ad6354da1ba563935b5f429f36409a1fb9187c220328f7a4dc5969917f8e3e01ee089b5f1627b02aefe39567b
  languageName: node
  linkType: hard

"path-browserify@npm:0.0.1":
  version: 0.0.1
  resolution: "path-browserify@npm:0.0.1"
  checksum: 3d59710cddeea06509d91935196185900f3d9d29376dff68ff0e146fbd41d0fb304e983d0158f30cabe4dd2ffcc6a7d3d977631994ee984c88e66aed50a1ccd3
  languageName: node
  linkType: hard

"path-dirname@npm:^1.0.0":
  version: 1.0.2
  resolution: "path-dirname@npm:1.0.2"
  checksum: 71e59be2bada7c91f62b976245fd421b7cb01fde3207fe53a82d8880621ad04fd8b434e628c9cf4e796259fc168a107d77cd56837725267c5b2c58cefe2c4e1b
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 17d6a5664bc0a11d48e2b2127d28a0e58822c6740bde30403f08013da599182289c56518bec89407e3f31d3c2b6b296a4220bc3f867f0911fee6952208b04167
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-is-inside@npm:^1.0.2":
  version: 1.0.2
  resolution: "path-is-inside@npm:1.0.2"
  checksum: 7fdd4b41672c70461cce734fc222b33e7b447fa489c7c4377c95e7e6852d83d69741f307d88ec0cc3b385b41cb4accc6efac3c7c511cd18512e95424f5fa980c
  languageName: node
  linkType: hard

"path-key@npm:^2.0.0, path-key@npm:^2.0.1":
  version: 2.0.1
  resolution: "path-key@npm:2.0.1"
  checksum: dd2044f029a8e58ac31d2bf34c34b93c3095c1481942960e84dd2faa95bbb71b9b762a106aead0646695330936414b31ca0bd862bf488a937ad17c8c5d73b32b
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: 1c6ff10ca169b773f3bba943bbc6a07182e332464704572962d277b900aeee81ac6aa5d060ff9e01149636c30b1f63af6e69dd7786ba6e0ddb39d4dee1f0645b
  languageName: node
  linkType: hard

"path-type@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-type@npm:3.0.0"
  dependencies:
    pify: "npm:^3.0.0"
  checksum: 1332c632f1cac15790ebab8dd729b67ba04fc96f81647496feb1c2975d862d046f41e4b975dbd893048999b2cc90721f72924ad820acc58c78507ba7141a8e56
  languageName: node
  linkType: hard

"pbkdf2@npm:^3.1.2":
  version: 3.1.2
  resolution: "pbkdf2@npm:3.1.2"
  dependencies:
    create-hash: "npm:^1.1.2"
    create-hmac: "npm:^1.1.4"
    ripemd160: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
    sha.js: "npm:^2.4.8"
  checksum: 5a30374e87d33fa080a92734d778cf172542cc7e41b96198c4c88763997b62d7850de3fbda5c3111ddf79805ee7c1da7046881c90ac4920b5e324204518b05fd
  languageName: node
  linkType: hard

"performance-now@npm:^2.1.0":
  version: 2.1.0
  resolution: "performance-now@npm:2.1.0"
  checksum: 22c54de06f269e29f640e0e075207af57de5052a3d15e360c09b9a8663f393f6f45902006c1e71aa8a5a1cdfb1a47fe268826f8496d6425c362f00f5bc3e85d9
  languageName: node
  linkType: hard

"picocolors@npm:^0.2.1":
  version: 0.2.1
  resolution: "picocolors@npm:0.2.1"
  checksum: 98a83c77912c80aea0fc518aec184768501bfceafa490714b0f43eda9c52e372b844ce0a591e822bbfe5df16dcf366be7cbdb9534d39cf54a80796340371ee17
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.3
  resolution: "picomatch@npm:4.0.3"
  checksum: 9582c951e95eebee5434f59e426cddd228a7b97a0161a375aed4be244bd3fe8e3a31b846808ea14ef2c8a2527a6eeab7b3946a67d5979e81694654f939473ae2
  languageName: node
  linkType: hard

"pify@npm:^2.0.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 551ff8ab830b1052633f59cb8adc9ae8407a436e06b4a9718bcb27dc5844b83d535c3a8512b388b6062af65a98c49bdc0dd523d8b2617b188f7c8fee457158dc
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: fead19ed9d801f1b1fcd0638a1ac53eabbb0945bf615f2f8806a8b646565a04a1b0e7ef115c951d225f042cca388fdc1cd3add46d10d1ed6951c20bd2998af10
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 6f9d404b0d47a965437403c9b90eca8bb2536407f03de165940e62e72c8c8b75adda5516c6b9b23675a5877cc0bcac6bdfb0ef0e39414cd2476d5495da40e7cf
  languageName: node
  linkType: hard

"pinkie-promise@npm:^2.0.0":
  version: 2.0.1
  resolution: "pinkie-promise@npm:2.0.1"
  dependencies:
    pinkie: "npm:^2.0.0"
  checksum: 11b5e5ce2b090c573f8fad7b517cbca1bb9a247587306f05ae71aef6f9b2cd2b923c304aa9663c2409cfde27b367286179f1379bc4ec18a3fbf2bb0d473b160a
  languageName: node
  linkType: hard

"pinkie@npm:^2.0.0":
  version: 2.0.4
  resolution: "pinkie@npm:2.0.4"
  checksum: 25228b08b5597da42dc384221aa0ce56ee0fbf32965db12ba838e2a9ca0193c2f0609c45551ee077ccd2060bf109137fdb185b00c6d7e0ed7e35006d20fdcbc6
  languageName: node
  linkType: hard

"pkg-dir@npm:^2.0.0":
  version: 2.0.0
  resolution: "pkg-dir@npm:2.0.0"
  dependencies:
    find-up: "npm:^2.1.0"
  checksum: 7cdc46c4921bf2c5f9a438851d16243ddde9906928116647ec7784982dd9038ea61c964fbca6f489201845742188180ecd1001b4f69781de1d1dc7d100b14089
  languageName: node
  linkType: hard

"pkg-dir@npm:^3.0.0":
  version: 3.0.0
  resolution: "pkg-dir@npm:3.0.0"
  dependencies:
    find-up: "npm:^3.0.0"
  checksum: 902a3d0c1f8ac43b1795fa1ba6ffeb37dfd53c91469e969790f6ed5e29ff2bdc50b63ba6115dc056d2efb4a040aa2446d512b3804bdafdf302f734fb3ec21847
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.1.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: "npm:^4.0.0"
  checksum: c56bda7769e04907a88423feb320babaed0711af8c436ce3e56763ab1021ba107c7b0cafb11cde7529f669cfc22bffcaebffb573645cbd63842ea9fb17cd7728
  languageName: node
  linkType: hard

"pkg-up@npm:^2.0.0":
  version: 2.0.0
  resolution: "pkg-up@npm:2.0.0"
  dependencies:
    find-up: "npm:^2.1.0"
  checksum: 9ce9eefba264430b7bd3e21eb90d3d215d588688a510e5f29c66e72df3067de9c6249664120dcc86141b68f9b1448039034e1abf401d98ba077d31a9ed87db83
  languageName: node
  linkType: hard

"playwright-core@npm:1.53.0":
  version: 1.53.0
  resolution: "playwright-core@npm:1.53.0"
  bin:
    playwright-core: cli.js
  checksum: fda0cf76115b15b1ca5cbc69e14185904e5c85e9e7cddb0a48121e69d681c638ac497e8a103985976cae260aa02e9c03ea27d6cd0b5f3d3ca914d4c7fd96f930
  languageName: node
  linkType: hard

"playwright@npm:1.53.0":
  version: 1.53.0
  resolution: "playwright@npm:1.53.0"
  dependencies:
    fsevents: "npm:2.3.2"
    playwright-core: "npm:1.53.0"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    playwright: cli.js
  checksum: 8d995114808b92f2005bd12ff5e494cdc3fa2d484f4d85a3e54be1fb99e88ae3e34b24792d83bb987462c73e553a0fa37a2a70264afbf67894b51c1498cf5a11
  languageName: node
  linkType: hard

"portfinder@npm:^1.0.20, portfinder@npm:^1.0.26":
  version: 1.0.37
  resolution: "portfinder@npm:1.0.37"
  dependencies:
    async: "npm:^3.2.6"
    debug: "npm:^4.3.6"
  checksum: eabd2764ced7bb0e6da7a1382bb77f9531309f7782fb6169021d05eecff0c0a17958bcf87573047a164dd0bb23f294d5d74b08ffe58c47005c28ed92eea9a6a7
  languageName: node
  linkType: hard

"posix-character-classes@npm:^0.1.0":
  version: 0.1.1
  resolution: "posix-character-classes@npm:0.1.1"
  checksum: cce88011548a973b4af58361cd8f5f7b5a6faff8eef0901565802f067bcabf82597e920d4c97c22068464be3cbc6447af589f6cc8a7d813ea7165be60a0395bc
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: c810983414142071da1d644662ce4caebce890203eb2bc7bf119f37f3fe5796226e117e6cca146b521921fa6531072674174a3325066ac66fce089a53e1e5196
  languageName: node
  linkType: hard

"postcss-calc@npm:^7.0.1":
  version: 7.0.5
  resolution: "postcss-calc@npm:7.0.5"
  dependencies:
    postcss: "npm:^7.0.27"
    postcss-selector-parser: "npm:^6.0.2"
    postcss-value-parser: "npm:^4.0.2"
  checksum: 37f10245a1280d96165a8f56a0cd90fcbc3f9277fc50b54ba79a45f24da4662b8ce57dcffe81331e8997bcc56f7b89fae4e8d9dc27b8c5d72010976d622388e3
  languageName: node
  linkType: hard

"postcss-colormin@npm:^4.0.3":
  version: 4.0.3
  resolution: "postcss-colormin@npm:4.0.3"
  dependencies:
    browserslist: "npm:^4.0.0"
    color: "npm:^3.0.0"
    has: "npm:^1.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 7f722c3287e51c6103554e536d0ee8497f137f6292874180cb50de98c8e86b0701898aafc81fa133fef29f3f857dc2a36b101d9b629556dcff02487e91e668af
  languageName: node
  linkType: hard

"postcss-convert-values@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-convert-values@npm:4.0.1"
  dependencies:
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 19d81085b7e74610f35a857eb0daba90fc3f0195e211175051ff7cd77f1a6c2b16e82059495651c37e83a3c47b4ad8eae4eb6e7792ad01a2694ba541f518f50e
  languageName: node
  linkType: hard

"postcss-discard-comments@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-discard-comments@npm:4.0.2"
  dependencies:
    postcss: "npm:^7.0.0"
  checksum: 12b491ae1a730137bc77443d2dda274ec385e8a8994a87eda67ca811c9b733f21d362fac6762f9b850a9093ef645201bedcfda91983909cd1b14092524dbbe26
  languageName: node
  linkType: hard

"postcss-discard-duplicates@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-discard-duplicates@npm:4.0.2"
  dependencies:
    postcss: "npm:^7.0.0"
  checksum: ca7a9a1564e4a8aedccb0fb9a8b4b3b42328998d30e54a6cbff8cee85cf9d1751acb802549f2cf8d4a846731f9a22946e83d2ab430ea66332c58e13d9c6e9d08
  languageName: node
  linkType: hard

"postcss-discard-empty@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-discard-empty@npm:4.0.1"
  dependencies:
    postcss: "npm:^7.0.0"
  checksum: be6c575a92dfd8b43e3e31af27b4585273e51b8576a8f8b68128fd66a050aeed2395e8f68000ab4047a5a51388fd1c1d6abdcb21469bccf99394faef5638ed94
  languageName: node
  linkType: hard

"postcss-discard-overridden@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-discard-overridden@npm:4.0.1"
  dependencies:
    postcss: "npm:^7.0.0"
  checksum: 187408dbc7668d8b9c8326b8c8a615c191d1cd159095dd77a35bdd7d53688a650e68f043cdacccb78429a0d0339b834313060ae45c63d022ec3c13f581d37be8
  languageName: node
  linkType: hard

"postcss-load-config@npm:^2.0.0":
  version: 2.1.2
  resolution: "postcss-load-config@npm:2.1.2"
  dependencies:
    cosmiconfig: "npm:^5.0.0"
    import-cwd: "npm:^2.0.0"
  checksum: a91df467d07b9ac3c5373721ac5294e2c21c9acdc9f2e2f43f7dc9d02529bee7d9e65b5ab20165d3f99d2421e38db893109c6bfd5f7432d607bfba5eea0f61df
  languageName: node
  linkType: hard

"postcss-loader@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-loader@npm:3.0.0"
  dependencies:
    loader-utils: "npm:^1.1.0"
    postcss: "npm:^7.0.0"
    postcss-load-config: "npm:^2.0.0"
    schema-utils: "npm:^1.0.0"
  checksum: 1b11a6a3f52c9ef5d86c7be87a16a420fc5396865fcfeaf2d9f21cb5c7b4fd3381ecf9b9fbe748f5efb6de9a17f270fcf3b76f76a45f7d91ffff507b674f0171
  languageName: node
  linkType: hard

"postcss-merge-longhand@npm:^4.0.11":
  version: 4.0.11
  resolution: "postcss-merge-longhand@npm:4.0.11"
  dependencies:
    css-color-names: "npm:0.0.4"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
    stylehacks: "npm:^4.0.0"
  checksum: 6611e41d7fd103befdf429277214da56bcba37e4f2e7a5a0d4b463f418e7b66f71bfa6b5bb21be6e92e9d9e30884d9cc13c7f8025abef69e0941a0202afbcf15
  languageName: node
  linkType: hard

"postcss-merge-rules@npm:^4.0.3":
  version: 4.0.3
  resolution: "postcss-merge-rules@npm:4.0.3"
  dependencies:
    browserslist: "npm:^4.0.0"
    caniuse-api: "npm:^3.0.0"
    cssnano-util-same-parent: "npm:^4.0.0"
    postcss: "npm:^7.0.0"
    postcss-selector-parser: "npm:^3.0.0"
    vendors: "npm:^1.0.0"
  checksum: 999462a396a3987355bce10318db03b2cff08b8162285e98f247713620eb4ac352e325f4dec6362dda91747303d3c83386e48aad3cc8e671f5e51a094a7d9c68
  languageName: node
  linkType: hard

"postcss-minify-font-values@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-minify-font-values@npm:4.0.2"
  dependencies:
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: e22eec80302a193f18956ea6f32f3c010603b607e7c35753c9af14d7ac2c53f1f4cc45bd150c478a04805ade8914ec612f67e486f07fd82bc0af1fe779f3670f
  languageName: node
  linkType: hard

"postcss-minify-gradients@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-minify-gradients@npm:4.0.2"
  dependencies:
    cssnano-util-get-arguments: "npm:^4.0.0"
    is-color-stop: "npm:^1.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 946546279c2fd2d84c9ce42085c524940a5feb3bc73ca8d45bffc978196194565017531d9b405578f02a5d1c337fd7c982d690aaa56471d1c13475c3cefefbdc
  languageName: node
  linkType: hard

"postcss-minify-params@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-minify-params@npm:4.0.2"
  dependencies:
    alphanum-sort: "npm:^1.0.0"
    browserslist: "npm:^4.0.0"
    cssnano-util-get-arguments: "npm:^4.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
    uniqs: "npm:^2.0.0"
  checksum: c43f5a04f62b864303e14a8584c662d1b3f6f9ed1df5585a43f778197729e3ee2b8fc099b2977b47f813ea11f32e3c8fcd59937ecd8a92b39c8207ce6c745936
  languageName: node
  linkType: hard

"postcss-minify-selectors@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-minify-selectors@npm:4.0.2"
  dependencies:
    alphanum-sort: "npm:^1.0.0"
    has: "npm:^1.0.0"
    postcss: "npm:^7.0.0"
    postcss-selector-parser: "npm:^3.0.0"
  checksum: e7f5ffcfb3d9a763201952327c5bf4cd3d4fcb0ffdbab156d935600cbb7bf0d15b7b039449fe3971f54edd33043d7a41580715ee6d57f3cb0336ba1ca915fa12
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^1.2.0":
  version: 1.2.1
  resolution: "postcss-modules-extract-imports@npm:1.2.1"
  dependencies:
    postcss: "npm:^6.0.1"
  checksum: 5746702936c1e1b78a7b31c4e20092197aa89c4e59c156249aea861c84cd0781c11c8c4648b95bcfdce010c48c8edd83b94eb9103f7c72ef8eb96c4b15716ed3
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^1.2.0":
  version: 1.2.0
  resolution: "postcss-modules-local-by-default@npm:1.2.0"
  dependencies:
    css-selector-tokenizer: "npm:^0.7.0"
    postcss: "npm:^6.0.1"
  checksum: 9139846ccefbb089cff17abda76e199c4d464d899816265f0b3939f5cf588b8ba59f9119b87eccaf3bfde3049bddd6ac5989f46e6d8fa9ba936cc6ccb27b89bd
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^1.1.0":
  version: 1.1.0
  resolution: "postcss-modules-scope@npm:1.1.0"
  dependencies:
    css-selector-tokenizer: "npm:^0.7.0"
    postcss: "npm:^6.0.1"
  checksum: ef0328204912f2a3b98322ac5f3d377c234c391a2afd29f51ec83961ad0e0c4dc01192968649ce47c75c85a6c922dd8c608b137c1cce548fe5d92d6fb4454bb2
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^1.3.0":
  version: 1.3.0
  resolution: "postcss-modules-values@npm:1.3.0"
  dependencies:
    icss-replace-symbols: "npm:^1.1.0"
    postcss: "npm:^6.0.1"
  checksum: 11bb1af2478291b72f6cc106ba48dc566079021976b2da8c7989db6dca643db6fe81653aba43d4cfb0467754853d77d1eb0e3593a621affe62a1cc2d72541c70
  languageName: node
  linkType: hard

"postcss-normalize-charset@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-normalize-charset@npm:4.0.1"
  dependencies:
    postcss: "npm:^7.0.0"
  checksum: 4eea4cb61e9153c064354919d3a0eb4666d5f2ea36c5cc1d24e9901601a03fb9bb40e5161f7203088afb306c7bdf21dfaf19489378d642a612741deb5cb17723
  languageName: node
  linkType: hard

"postcss-normalize-display-values@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-display-values@npm:4.0.2"
  dependencies:
    cssnano-util-get-match: "npm:^4.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 01083456e37d466c093defdb7b7fc8779a1ec6fbe0a18469127d243c49fb7c61cc70d378c04461314d7b38327c2833ca928317413bf2a41402b3214a950d7e00
  languageName: node
  linkType: hard

"postcss-normalize-positions@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-positions@npm:4.0.2"
  dependencies:
    cssnano-util-get-arguments: "npm:^4.0.0"
    has: "npm:^1.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 1b774b7277420869bfeb5921d66584b1669a2a0d47b34e11b839a34049895e6146721d26c20e6aab7928240a8565ac6dec13b6101837ee50b7edc3016f6288f6
  languageName: node
  linkType: hard

"postcss-normalize-repeat-style@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-repeat-style@npm:4.0.2"
  dependencies:
    cssnano-util-get-arguments: "npm:^4.0.0"
    cssnano-util-get-match: "npm:^4.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: a13e6d0aac3a7a0627a180bcca04467dcab23f14a676ded735dee1ca742293ea7baf283f2cf3c8e28b7429b2ab158b6646f4375d0bbbeffec7870fea1818af0b
  languageName: node
  linkType: hard

"postcss-normalize-string@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-string@npm:4.0.2"
  dependencies:
    has: "npm:^1.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 2827a5a16bbfd87c4f41aa30e818428eb3a86755c43938590e7436c6f7bf7cc17099b37f6b89425f0b7f46d482e230dc3d887426b26566a818eeaa64131e8c8d
  languageName: node
  linkType: hard

"postcss-normalize-timing-functions@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-timing-functions@npm:4.0.2"
  dependencies:
    cssnano-util-get-match: "npm:^4.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 999c3166332b83c7cad2d5cf21dfda52349068eeeec79117304e754d3607dc7ed2133f78c1c0fec870c34e700687a4ed0b4f8780ca3c0a4428b422fbc72ec14e
  languageName: node
  linkType: hard

"postcss-normalize-unicode@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-normalize-unicode@npm:4.0.1"
  dependencies:
    browserslist: "npm:^4.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 9841bcfb11c4371cea853c2b88756428215c3dd64b3535b136189ca67f721b309d32d59067ca9ba8a6862a69c1ada4a5ca65c8d40757fd2ba87f313bccc97634
  languageName: node
  linkType: hard

"postcss-normalize-url@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-normalize-url@npm:4.0.1"
  dependencies:
    is-absolute-url: "npm:^2.0.0"
    normalize-url: "npm:^3.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 713d0b87d5dd0aa126f3f4850be581b05c3668887c591f84e89ca90fb892a6a78fb7fdcf4750c968bf804dcd0bd9d7768e0339c7259e891a69c5d6b414333a3f
  languageName: node
  linkType: hard

"postcss-normalize-whitespace@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-whitespace@npm:4.0.2"
  dependencies:
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 497ed988df6c4e6f2dcfcc9f6cfff967585c941549d885b02ef55b2d1ab7bceabb633595dd5b6ad1a2998af0c8c6f2d59cdffad4828c86ca79a690dd727a6dc2
  languageName: node
  linkType: hard

"postcss-ordered-values@npm:^4.1.2":
  version: 4.1.2
  resolution: "postcss-ordered-values@npm:4.1.2"
  dependencies:
    cssnano-util-get-arguments: "npm:^4.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 6866d7b0b8b7f1986614216af124b36bc10b41995ee690d6a025fb9135438578041be30e779117f20fca94df97189f460d7013d6839d7e2a225802652074f12f
  languageName: node
  linkType: hard

"postcss-reduce-initial@npm:^4.0.3":
  version: 4.0.3
  resolution: "postcss-reduce-initial@npm:4.0.3"
  dependencies:
    browserslist: "npm:^4.0.0"
    caniuse-api: "npm:^3.0.0"
    has: "npm:^1.0.0"
    postcss: "npm:^7.0.0"
  checksum: 5358b64afbfc3eb0b51031f4568850a5c9142ccf45a6416736802778972d7157811cefebcd2b3afbbf7267db3d39bb09575a8c976ca6552b99ffbf7fdd5083ea
  languageName: node
  linkType: hard

"postcss-reduce-transforms@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-reduce-transforms@npm:4.0.2"
  dependencies:
    cssnano-util-get-match: "npm:^4.0.0"
    has: "npm:^1.0.0"
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
  checksum: 72b9c83fb8af3ec756a8dedcc13d025068896484da00c6859c4e0980eb37b85766354cdbf83283f1f047417b00fc3f7472f3acd7411e301fcfe067e02fae03f3
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^3.0.0":
  version: 3.1.2
  resolution: "postcss-selector-parser@npm:3.1.2"
  dependencies:
    dot-prop: "npm:^5.2.0"
    indexes-of: "npm:^1.0.1"
    uniq: "npm:^1.0.1"
  checksum: 65f8fb1dcd64e9a3de03a6bd5e0a2e67475a01057d8470b46723cd569d1ddba4d18107e45aee26b46d8cdaab6ef8f5aad7c2e934fc4c46386418cc578dcc181b
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.2":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 523196a6bd8cf660bdf537ad95abd79e546d54180f9afb165a4ab3e651ac705d0f8b8ce6b3164fb9e3279ce482c5f751a69eb2d3a1e8eb0fd5e82294fb3ef13e
  languageName: node
  linkType: hard

"postcss-svgo@npm:^4.0.3":
  version: 4.0.3
  resolution: "postcss-svgo@npm:4.0.3"
  dependencies:
    postcss: "npm:^7.0.0"
    postcss-value-parser: "npm:^3.0.0"
    svgo: "npm:^1.0.0"
  checksum: 555eccf33c533e5f28f8441c5b14a2b1f606d56b8a357efad190f8d6d2646c11e088b740ba21798d41ba7c6819b6bd237a805dd1f8ddd44dd5c1c50ab5532f91
  languageName: node
  linkType: hard

"postcss-unique-selectors@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-unique-selectors@npm:4.0.1"
  dependencies:
    alphanum-sort: "npm:^1.0.0"
    postcss: "npm:^7.0.0"
    uniqs: "npm:^2.0.0"
  checksum: a7c1ef42914b89b05db624667edfcba9cd6dddc90ab9dc0049c42dd03cad02c9a6a128bded04188d32a62de263ac1893b5ff6ce4e8bb1ad628cfd944068d6edf
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^3.0.0, postcss-value-parser@npm:^3.3.0":
  version: 3.3.1
  resolution: "postcss-value-parser@npm:3.3.1"
  checksum: 23eed98d8eeadb1f9ef1db4a2757da0f1d8e7c1dac2a38d6b35d971aab9eb3c6d8a967d0e9f435558834ffcd966afbbe875a56bcc5bcdd09e663008c106b3e47
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.2, postcss-value-parser@npm:^4.1.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: f4142a4f56565f77c1831168e04e3effd9ffcc5aebaf0f538eee4b2d465adfd4b85a44257bb48418202a63806a7da7fe9f56c330aebb3cac898e46b4cbf49161
  languageName: node
  linkType: hard

"postcss@npm:^6.0.1, postcss@npm:^6.0.23":
  version: 6.0.23
  resolution: "postcss@npm:6.0.23"
  dependencies:
    chalk: "npm:^2.4.1"
    source-map: "npm:^0.6.1"
    supports-color: "npm:^5.4.0"
  checksum: 45d45184ffbb9d510e7585d9441af9a1a771a56b7553b1d598544e54acdfd31df439a95d5f00a6dc57b85b76d0c8925fec18614b1cc795887c845c3965e32e63
  languageName: node
  linkType: hard

"postcss@npm:^7.0.0, postcss@npm:^7.0.1, postcss@npm:^7.0.27, postcss@npm:^7.0.32, postcss@npm:^7.0.36":
  version: 7.0.39
  resolution: "postcss@npm:7.0.39"
  dependencies:
    picocolors: "npm:^0.2.1"
    source-map: "npm:^0.6.1"
  checksum: fd27ee808c0d02407582cccfad4729033e2b439d56cd45534fb39aaad308bb35a290f3b7db5f2394980e8756f9381b458a625618550808c5ff01a125f51efc53
  languageName: node
  linkType: hard

"postcss@npm:^8.4.14":
  version: 8.5.3
  resolution: "postcss@npm:8.5.3"
  dependencies:
    nanoid: "npm:^3.3.8"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: b75510d7b28c3ab728c8733dd01538314a18c52af426f199a3c9177e63eb08602a3938bfb66b62dc01350b9aed62087eabbf229af97a1659eb8d3513cec823b3
  languageName: node
  linkType: hard

"prepend-http@npm:^1.0.0":
  version: 1.0.4
  resolution: "prepend-http@npm:1.0.4"
  checksum: c6c173ca439e58163ba7bea7cbba52a1ed11e3e3da1c048da296f37d4b7654f78f7304e03f76d5923f4b83af7e2d55533e0f79064209c75b743ccddee13904f8
  languageName: node
  linkType: hard

"prettier@npm:^1.18.2 || ^2.0.0":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: 463ea8f9a0946cd5b828d8cf27bd8b567345cf02f56562d5ecde198b91f47a76b7ac9eae0facd247ace70e927143af6135e8cf411986b8cb8478784a4d6d724a
  languageName: node
  linkType: hard

"pretty-error@npm:^2.0.2":
  version: 2.1.2
  resolution: "pretty-error@npm:2.1.2"
  dependencies:
    lodash: "npm:^4.17.20"
    renderkid: "npm:^2.0.4"
  checksum: 779743faf707308e5d07c53c3ec94596c0cb631c92104a2721dd5d021ade39505a9151c5a5f838dfd26b02a06752c410eb6de1769c4fe327c90bd083f61a1fa1
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: 40c3ce4b7e6d4b8c3355479df77aeed46f81b279818ccdc500124e6a5ab882c0cc81ff7ea16384873a95a74c4570b01b120f287abbdd4c877931460eca6084b3
  languageName: node
  linkType: hard

"promise-inflight@npm:^1.0.1":
  version: 1.0.1
  resolution: "promise-inflight@npm:1.0.1"
  checksum: d179d148d98fbff3d815752fa9a08a87d3190551d1420f17c4467f628214db12235ae068d98cd001f024453676d8985af8f28f002345646c4ece4600a79620bc
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: c3eed999781a35f7fd935f398b6d8920b6fb00bbc14287bc6de78128ccc1a02c89b95b56742bf7cf0362cc333c61d138532049c7dedc7a328ef13343eff81210
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"prr@npm:~1.0.1":
  version: 1.0.1
  resolution: "prr@npm:1.0.1"
  checksum: 5b9272c602e4f4472a215e58daff88f802923b84bc39c8860376bb1c0e42aaf18c25d69ad974bd06ec6db6f544b783edecd5502cd3d184748d99080d68e4be5f
  languageName: node
  linkType: hard

"pseudomap@npm:^1.0.2":
  version: 1.0.2
  resolution: "pseudomap@npm:1.0.2"
  checksum: 5a91ce114c64ed3a6a553aa7d2943868811377388bb31447f9d8028271bae9b05b340fe0b6961a64e45b9c72946aeb0a4ab635e8f7cb3715ffd0ff2beeb6a679
  languageName: node
  linkType: hard

"psl@npm:^1.1.28":
  version: 1.15.0
  resolution: "psl@npm:1.15.0"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: d8d45a99e4ca62ca12ac3c373e63d80d2368d38892daa40cfddaa1eb908be98cd549ac059783ef3a56cfd96d57ae8e2fd9ae53d1378d90d42bc661ff924e102a
  languageName: node
  linkType: hard

"public-encrypt@npm:^4.0.3":
  version: 4.0.3
  resolution: "public-encrypt@npm:4.0.3"
  dependencies:
    bn.js: "npm:^4.1.0"
    browserify-rsa: "npm:^4.0.0"
    create-hash: "npm:^1.1.0"
    parse-asn1: "npm:^5.0.0"
    randombytes: "npm:^2.0.1"
    safe-buffer: "npm:^5.1.2"
  checksum: 6c2cc19fbb554449e47f2175065d6b32f828f9b3badbee4c76585ac28ae8641aafb9bb107afc430c33c5edd6b05dbe318df4f7d6d7712b1093407b11c4280700
  languageName: node
  linkType: hard

"pump@npm:^2.0.0, pump@npm:^2.0.1":
  version: 2.0.1
  resolution: "pump@npm:2.0.1"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: f1fe8960f44d145f8617ea4c67de05392da4557052980314c8f85081aee26953bdcab64afad58a2b1df0e8ff7203e3710e848cbe81a01027978edc6e264db355
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.2
  resolution: "pump@npm:3.0.2"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 5ad655cb2a7738b4bcf6406b24ad0970d680649d996b55ad20d1be8e0c02394034e4c45ff7cd105d87f1e9b96a0e3d06fd28e11fae8875da26e7f7a8e2c9726f
  languageName: node
  linkType: hard

"pumpify@npm:^1.3.3":
  version: 1.5.1
  resolution: "pumpify@npm:1.5.1"
  dependencies:
    duplexify: "npm:^3.6.0"
    inherits: "npm:^2.0.3"
    pump: "npm:^2.0.0"
  checksum: 0bcabf9e3dbf2d0cc1f9b84ac80d3c75386111caf8963bfd98817a1e2192000ac0ccc804ca6ccd5b2b8430fdb71347b20fb2f014fe3d41adbacb1b502a841c45
  languageName: node
  linkType: hard

"punycode@npm:^1.2.4, punycode@npm:^1.4.1":
  version: 1.4.1
  resolution: "punycode@npm:1.4.1"
  checksum: 354b743320518aef36f77013be6e15da4db24c2b4f62c5f1eb0529a6ed02fbaf1cb52925785f6ab85a962f2b590d9cd5ad730b70da72b5f180e2556b8bd3ca08
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"q@npm:^1.1.2":
  version: 1.5.1
  resolution: "q@npm:1.5.1"
  checksum: 7855fbdba126cb7e92ef3a16b47ba998c0786ec7fface236e3eb0135b65df36429d91a86b1fff3ab0927b4ac4ee88a2c44527c7c3b8e2a37efbec9fe34803df4
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 62372cdeec24dc83a9fb240b7533c0fdcf0c5f7e0b83343edd7310f0ab4c8205a5e7c56406531f2e47e1b4878a3821d652be4192c841de5b032ca83619d8f860
  languageName: node
  linkType: hard

"qs@npm:^6.12.3, qs@npm:^6.7.0":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: "npm:^1.1.0"
  checksum: 8ea5d91bf34f440598ee389d4a7d95820e3b837d3fd9f433871f7924801becaa0cd3b3b4628d49a7784d06a8aea9bc4554d2b6d8d584e2d221dc06238a42909c
  languageName: node
  linkType: hard

"qs@npm:~6.5.2":
  version: 6.5.3
  resolution: "qs@npm:6.5.3"
  checksum: 6631d4f2fa9d315e480662646745a4aa3a708817fbffe2cbdacec8ab9be130f92740c66191770fe9b704bc5fa9c1cc1f6596f55ad132fef7bd3ad1582f199eb0
  languageName: node
  linkType: hard

"query-string@npm:^4.1.0":
  version: 4.3.4
  resolution: "query-string@npm:4.3.4"
  dependencies:
    object-assign: "npm:^4.1.0"
    strict-uri-encode: "npm:^1.0.0"
  checksum: 6181c343074c2049fbbcde63f87c1da5d3a49c6e34c8d94a61d692e886e0b8cd1ae4a4be00b598112bb9c4cb819e423ed503a5d246e4d24ecb0990d8bb21570b
  languageName: node
  linkType: hard

"querystring-es3@npm:^0.2.0":
  version: 0.2.1
  resolution: "querystring-es3@npm:0.2.1"
  checksum: 476938c1adb45c141f024fccd2ffd919a3746e79ed444d00e670aad68532977b793889648980e7ca7ff5ffc7bfece623118d0fbadcaf217495eeb7059ae51580
  languageName: node
  linkType: hard

"querystringify@npm:^2.1.1":
  version: 2.2.0
  resolution: "querystringify@npm:2.2.0"
  checksum: 3258bc3dbdf322ff2663619afe5947c7926a6ef5fb78ad7d384602974c467fadfc8272af44f5eb8cddd0d011aae8fabf3a929a8eee4b86edcc0a21e6bd10f9aa
  languageName: node
  linkType: hard

"randombytes@npm:^2.0.0, randombytes@npm:^2.0.1, randombytes@npm:^2.0.5, randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: "npm:^5.1.0"
  checksum: 50395efda7a8c94f5dffab564f9ff89736064d32addf0cc7e8bf5e4166f09f8ded7a0849ca6c2d2a59478f7d90f78f20d8048bca3cdf8be09d8e8a10790388f3
  languageName: node
  linkType: hard

"randomfill@npm:^1.0.4":
  version: 1.0.4
  resolution: "randomfill@npm:1.0.4"
  dependencies:
    randombytes: "npm:^2.0.5"
    safe-buffer: "npm:^5.1.0"
  checksum: 11aeed35515872e8f8a2edec306734e6b74c39c46653607f03c68385ab8030e2adcc4215f76b5e4598e028c4750d820afd5c65202527d831d2a5f207fe2bc87c
  languageName: node
  linkType: hard

"range-parser@npm:^1.2.1, range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: b201c4b66049369a60e766318caff5cb3cc5a900efd89bdac431463822d976ad0670912c931fdbdcf5543207daf6f6833bca57aa116e1661d2ea91e12ca692c4
  languageName: node
  linkType: hard

"read-pkg@npm:^5.0.0":
  version: 5.2.0
  resolution: "read-pkg@npm:5.2.0"
  dependencies:
    "@types/normalize-package-data": "npm:^2.4.0"
    normalize-package-data: "npm:^2.5.0"
    parse-json: "npm:^5.0.0"
    type-fest: "npm:^0.6.0"
  checksum: b51a17d4b51418e777029e3a7694c9bd6c578a5ab99db544764a0b0f2c7c0f58f8a6bc101f86a6fceb8ba6d237d67c89acf6170f6b98695d0420ddc86cf109fb
  languageName: node
  linkType: hard

"readable-stream@npm:1 || 2, readable-stream@npm:^2.0.0, readable-stream@npm:^2.0.1, readable-stream@npm:^2.0.2, readable-stream@npm:^2.1.5, readable-stream@npm:^2.2.2, readable-stream@npm:^2.3.3, readable-stream@npm:^2.3.6, readable-stream@npm:^2.3.8, readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"readable-stream@npm:^3.0.6, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readdirp@npm:^2.2.1":
  version: 2.2.1
  resolution: "readdirp@npm:2.2.1"
  dependencies:
    graceful-fs: "npm:^4.1.11"
    micromatch: "npm:^3.1.10"
    readable-stream: "npm:^2.0.2"
  checksum: 770d177372ff2212d382d425d55ca48301fcbf3231ab3827257bbcca7ff44fb51fe4af6acc2dda8512dc7f29da390e9fbea5b2b3fc724b86e85cc828395b7797
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 7facec28c8008876f8ab98e80b7b9cb4b1e9224353fd4756dda5f2a4ab0d30fa0a5074777c6df24e1e0af463a2697513b0a11e548d99cf52f21f7bc6ba48d3ac
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
  checksum: 5510785eeaf56bbfdf4e663d6753f125c08d2a372d4107bc1b756b7bf142e2ed80c2733a8b54e68fb309ba37690e66a0362699b0e21d5c1f0255dea1b00e6460
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: f73c9eba5d398c818edc71d1c6979eaa05af7a808682749dd079f8df2a6d91a9b913db216c2c9b03e0a8ba2bba8701244a93f45211afbff691c32c7b275db1b8
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.4":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 12b069dc774001fbb0014f6a28f11c09ebfe3c0d984d88c9bced77fdb6fedbacbca434d24da9ae9371bfbf23f754869307fb51a4c98a8b8b18e5ef748677ca24
  languageName: node
  linkType: hard

"regex-not@npm:^1.0.0, regex-not@npm:^1.0.2":
  version: 1.0.2
  resolution: "regex-not@npm:1.0.2"
  dependencies:
    extend-shallow: "npm:^3.0.2"
    safe-regex: "npm:^1.1.0"
  checksum: a0f8d6045f63b22e9759db10e248369c443b41cedd7dba0922d002b66c2734bc2aef0d98c4d45772d1f756245f4c5203856b88b9624bba2a58708858a8d485d6
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.1, regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 83b88e6115b4af1c537f8dabf5c3744032cb875d63bc05c288b1b8c0ef37cbe55353f95d8ca817e8843806e3e150b118bc624e4279b24b4776b4198232735a77
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
    regenerate-unicode-properties: "npm:^10.2.0"
    regjsgen: "npm:^0.8.0"
    regjsparser: "npm:^0.12.0"
    unicode-match-property-ecmascript: "npm:^2.0.0"
    unicode-match-property-value-ecmascript: "npm:^2.1.0"
  checksum: bbcb83a854bf96ce4005ee4e4618b71c889cda72674ce6092432f0039b47890c2d0dfeb9057d08d440999d9ea03879ebbb7f26ca005ccf94390e55c348859b98
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: 44f526c4fdbf0b29286101a282189e4dbb303f4013cf3fea058668d96d113b9180d3d03d1e13f6d4cbde38b7728bf951aecd9dc199938c080093a9a6f0d7a6bd
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: "npm:~3.0.2"
  bin:
    regjsparser: bin/parser
  checksum: 99d3e4e10c8c7732eb7aa843b8da2fd8b647fe144d3711b480e4647dc3bff4b1e96691ccf17f3ace24aa866a50b064236177cb25e6e4fbbb18285d99edaed83b
  languageName: node
  linkType: hard

"relateurl@npm:0.2.x":
  version: 0.2.7
  resolution: "relateurl@npm:0.2.7"
  checksum: c248b4e3b32474f116a804b537fa6343d731b80056fb506dffd91e737eef4cac6be47a65aae39b522b0db9d0b1011d1a12e288d82a109ecd94a5299d82f6573a
  languageName: node
  linkType: hard

"remove-trailing-separator@npm:^1.0.1":
  version: 1.1.0
  resolution: "remove-trailing-separator@npm:1.1.0"
  checksum: 3568f9f8f5af3737b4aee9e6e1e8ec4be65a92da9cb27f989e0893714d50aa95ed2ff02d40d1fa35e1b1a234dc9c2437050ef356704a3999feaca6667d9e9bfc
  languageName: node
  linkType: hard

"renderkid@npm:^2.0.4":
  version: 2.0.7
  resolution: "renderkid@npm:2.0.7"
  dependencies:
    css-select: "npm:^4.1.3"
    dom-converter: "npm:^0.2.0"
    htmlparser2: "npm:^6.1.0"
    lodash: "npm:^4.17.21"
    strip-ansi: "npm:^3.0.1"
  checksum: 05e19c8861e0f9f3d379a175fbb52e3be3c957022acf52d19d36b23f99bb401b6bc3c493d43213f4d76efb08cb2f13e66df38c9a487249cb8dad1f6170da6a14
  languageName: node
  linkType: hard

"repeat-element@npm:^1.1.2":
  version: 1.1.4
  resolution: "repeat-element@npm:1.1.4"
  checksum: 81aa8d82bc845780803ef52df3533fa399974b99df571d0bb86e91f0ffca9ee4b9c4e8e5e72af087938cc28d2aef93d106a6d01da685d72ce96455b90a9f9f69
  languageName: node
  linkType: hard

"repeat-string@npm:^1.6.1":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 87fa21bfdb2fbdedc44b9a5b118b7c1239bdd2c2c1e42742ef9119b7d412a5137a1d23f1a83dc6bb686f4f27429ac6f542e3d923090b44181bafa41e8ac0174d
  languageName: node
  linkType: hard

"request-promise-core@npm:1.1.4":
  version: 1.1.4
  resolution: "request-promise-core@npm:1.1.4"
  dependencies:
    lodash: "npm:^4.17.19"
  peerDependencies:
    request: ^2.34
  checksum: 103eb9043450b9312c005ed859c2150825a555b72e4c0a83841f6793d368eddeacde425f8688effa215eb3eb14ff8c486a3c3e80f6246e9c195628db2bf9020e
  languageName: node
  linkType: hard

"request-promise-native@npm:^1.0.7":
  version: 1.0.9
  resolution: "request-promise-native@npm:1.0.9"
  dependencies:
    request-promise-core: "npm:1.1.4"
    stealthy-require: "npm:^1.1.1"
    tough-cookie: "npm:^2.3.3"
  peerDependencies:
    request: ^2.34
  checksum: e4edae38675c3492a370fd7a44718df3cc8357993373156a66cb329fcde7480a2652591279cd48ba52326ea529ee99805da37119ad91563a901d3fba0ab5be92
  languageName: node
  linkType: hard

"request@npm:^2.87.0":
  version: 2.88.2
  resolution: "request@npm:2.88.2"
  dependencies:
    aws-sign2: "npm:~0.7.0"
    aws4: "npm:^1.8.0"
    caseless: "npm:~0.12.0"
    combined-stream: "npm:~1.0.6"
    extend: "npm:~3.0.2"
    forever-agent: "npm:~0.6.1"
    form-data: "npm:~2.3.2"
    har-validator: "npm:~5.1.3"
    http-signature: "npm:~1.2.0"
    is-typedarray: "npm:~1.0.0"
    isstream: "npm:~0.1.2"
    json-stringify-safe: "npm:~5.0.1"
    mime-types: "npm:~2.1.19"
    oauth-sign: "npm:~0.9.0"
    performance-now: "npm:^2.1.0"
    qs: "npm:~6.5.2"
    safe-buffer: "npm:^5.1.2"
    tough-cookie: "npm:~2.5.0"
    tunnel-agent: "npm:^0.6.0"
    uuid: "npm:^3.3.2"
  checksum: 0ec66e7af1391e51ad231de3b1c6c6aef3ebd0a238aa50d4191c7a792dcdb14920eea8d570c702dc5682f276fe569d176f9b8ebc6031a3cf4a630a691a431a63
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"require-main-filename@npm:^2.0.0":
  version: 2.0.0
  resolution: "require-main-filename@npm:2.0.0"
  checksum: db91467d9ead311b4111cbd73a4e67fa7820daed2989a32f7023785a2659008c6d119752d9c4ac011ae07e537eb86523adff99804c5fdb39cd3a017f9b401bb6
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: b2bfdd09db16c082c4326e573a82c0771daaf7b53b9ce8ad60ea46aa6e30aaf475fe9b164800b89f93b748d2c234d8abff945d2551ba47bf5698e04cd7713267
  languageName: node
  linkType: hard

"reselect@npm:^3.0.1":
  version: 3.0.1
  resolution: "reselect@npm:3.0.1"
  checksum: b4bced93369e33832befc245253e2ce2f64ac007bbdf013a260c7b29e23dff8cf82e0415b0d05b85279bc8fc8af4a5f92bd261d3e0e9cbe4e5c1869f8b0ec5a6
  languageName: node
  linkType: hard

"resolve-cwd@npm:^2.0.0":
  version: 2.0.0
  resolution: "resolve-cwd@npm:2.0.0"
  dependencies:
    resolve-from: "npm:^3.0.0"
  checksum: 10c3a7ffeb55af51206f5ca8696ed833376179399336ce8e9df8f76c044c13bccd0e9a3148708daf272193179a581ddb076e203eaa71efa0ad341b243174ca12
  languageName: node
  linkType: hard

"resolve-from@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-from@npm:3.0.0"
  checksum: 24affcf8e81f4c62f0dcabc774afe0e19c1f38e34e43daac0ddb409d79435fc3037f612b0cc129178b8c220442c3babd673e88e870d27215c99454566e770ebc
  languageName: node
  linkType: hard

"resolve-url@npm:^0.2.1":
  version: 0.2.1
  resolution: "resolve-url@npm:0.2.1"
  checksum: c285182cfcddea13a12af92129ce0569be27fb0074ffaefbd3ba3da2eac2acecdfc996d435c4982a9fa2b4708640e52837c9153a5ab9255886a00b0b9e8d2a54
  languageName: node
  linkType: hard

"resolve@npm:^1.10.0, resolve@npm:^1.14.2, resolve@npm:^1.4.0":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.10.0#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.14.2#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.4.0#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"restore-cursor@npm:^2.0.0":
  version: 2.0.0
  resolution: "restore-cursor@npm:2.0.0"
  dependencies:
    onetime: "npm:^2.0.0"
    signal-exit: "npm:^3.0.2"
  checksum: f5b335bee06f440445e976a7031a3ef53691f9b7c4a9d42a469a0edaf8a5508158a0d561ff2b26a1f4f38783bcca2c0e5c3a44f927326f6694d5b44d7a4993e6
  languageName: node
  linkType: hard

"ret@npm:~0.1.10":
  version: 0.1.15
  resolution: "ret@npm:0.1.15"
  checksum: 01f77cad0f7ea4f955852c03d66982609893edc1240c0c964b4c9251d0f9fb6705150634060d169939b096d3b77f4c84d6b6098a5b5d340160898c8581f1f63f
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"rgb-regex@npm:^1.0.1":
  version: 1.0.1
  resolution: "rgb-regex@npm:1.0.1"
  checksum: ab43ea8b92c1e0c6d6bc811d7fff05927ae87473f9576363ae57213b1fd10605549a5cf89c79ccb7a02dd32e1f093c79891868ef31fd92cdb5378d7b180d73f5
  languageName: node
  linkType: hard

"rgba-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "rgba-regex@npm:1.0.0"
  checksum: 4296e7dd41c0edf66c18c13824b746099af642e1b52de97addb6b22051398567090cf1df20bacd1d400cfc79aee6f3a428e0def623d358844495e3630189892c
  languageName: node
  linkType: hard

"rimraf@npm:^2.5.4, rimraf@npm:^2.6.2, rimraf@npm:^2.6.3":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: ./bin.js
  checksum: 4eef73d406c6940927479a3a9dee551e14a54faf54b31ef861250ac815172bade86cc6f7d64a4dc5e98b65e4b18a2e1c9ff3b68d296be0c748413f092bb0dd40
  languageName: node
  linkType: hard

"ripemd160@npm:^2.0.0, ripemd160@npm:^2.0.1":
  version: 2.0.2
  resolution: "ripemd160@npm:2.0.2"
  dependencies:
    hash-base: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
  checksum: f6f0df78817e78287c766687aed4d5accbebc308a8e7e673fb085b9977473c1f139f0c5335d353f172a915bb288098430755d2ad3c4f30612f4dd0c901cd2c3a
  languageName: node
  linkType: hard

"run-queue@npm:^1.0.0, run-queue@npm:^1.0.3":
  version: 1.0.3
  resolution: "run-queue@npm:1.0.3"
  dependencies:
    aproba: "npm:^1.1.1"
  checksum: 4e8964279d8f160f9ffaabe82eaad11a1d4c0db596a0f2b5257ae9d2b900c7e1ffcece3e5719199436f50718e1e7f45bb4bf7a82e331a4e734d67c2588a90cbb
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.2, safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 43c86ffdddc461fb17ff8a17c5324f392f4868f3c7dd2c6a5d9f5971713bc5fd755667212c80eab9567595f9a7509cc2f83e590ddaebd1bd19b780f9c79f9a8d
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:>=5.1.0, safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:^5.1.1, safe-buffer@npm:^5.1.2, safe-buffer@npm:^5.2.0, safe-buffer@npm:^5.2.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 831f1c9aae7436429e7862c7e46f847dfe490afac20d0ee61bae06108dbf5c745a0de3568ada30ccdd3eeb0864ca8331b2eef703abd69bfea0745b21fd320750
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: f2c25281bbe5d39cddbbce7f86fca5ea9b3ce3354ea6cd7c81c31b006a5a9fff4286acc5450a3b9122c56c33eba69c56b9131ad751457b2b4a585825e6a10665
  languageName: node
  linkType: hard

"safe-regex@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex@npm:1.1.0"
  dependencies:
    ret: "npm:~0.1.10"
  checksum: 547d58aa5184cbef368fd5ed5f28d20f911614748c5da6b35f53fd6626396707587251e6e3d1e3010fd3ff1212e413841b8825eaa5f317017ca62a30899af31a
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0, safer-buffer@npm:^2.0.2, safer-buffer@npm:^2.1.0, safer-buffer@npm:~2.1.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sax@npm:~1.2.4":
  version: 1.2.4
  resolution: "sax@npm:1.2.4"
  checksum: 6e9b05ff443ee5e5096ce92d31c0740a20d33002fad714ebcb8fc7a664d9ee159103ebe8f7aef0a1f7c5ecacdd01f177f510dff95611c589399baf76437d3fe3
  languageName: node
  linkType: hard

"schema-utils@npm:^1.0.0":
  version: 1.0.0
  resolution: "schema-utils@npm:1.0.0"
  dependencies:
    ajv: "npm:^6.1.0"
    ajv-errors: "npm:^1.0.0"
    ajv-keywords: "npm:^3.1.0"
  checksum: 670e22d7f0ff0b6f4514a4d6fb27c359101b44b7dbfd9563af201af72eb4a9ff06144020cab5f85b16e88821fd09b97cbdae6c893721c6528c8cb704124e6a2f
  languageName: node
  linkType: hard

"schema-utils@npm:^2.6.5, schema-utils@npm:^2.7.0":
  version: 2.7.1
  resolution: "schema-utils@npm:2.7.1"
  dependencies:
    "@types/json-schema": "npm:^7.0.5"
    ajv: "npm:^6.12.4"
    ajv-keywords: "npm:^3.5.2"
  checksum: f484f34464edd8758712d5d3ba25a306e367dac988aecaf4ce112e99baae73f33a807b5cf869240bb6648c80720b36af2d7d72be3a27faa49a2d4fc63fa3f85f
  languageName: node
  linkType: hard

"select-hose@npm:^2.0.0":
  version: 2.0.0
  resolution: "select-hose@npm:2.0.0"
  checksum: 01cc52edd29feddaf379efb4328aededa633f0ac43c64b11a8abd075ff34f05b0d280882c4fbcbdf1a0658202c9cd2ea8d5985174dcf9a2dac7e3a4996fa9b67
  languageName: node
  linkType: hard

"selfsigned@npm:^1.10.8":
  version: 1.10.14
  resolution: "selfsigned@npm:1.10.14"
  dependencies:
    node-forge: "npm:^0.10.0"
  checksum: cb7c92e28a3e8a34e91a3b20b9dd7d237a1bffccd9890c434f74eef65ae8bfffe7c1ab3379faac017b957ea44522923e06e2b41e0858c643edb01137a507cd16
  languageName: node
  linkType: hard

"semver@npm:2 || 3 || 4 || 5, semver@npm:^5.3.0, semver@npm:^5.5.0, semver@npm:^5.6.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: e4cf10f86f168db772ae95d86ba65b3fd6c5967c94d97c708ccb463b778c2ee53b914cd7167620950fc07faf5a564e6efe903836639e512a1aa15fbc9667fa25
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.3.5":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: ea3f8a67a8f0be3d6bf9080f0baed6d2c51d11d4f7b4470de96a5029c598a7011c497511ccc28968b70ef05508675cebff27da9151dd2ceadd60be4e6cf845e3
  languageName: node
  linkType: hard

"serialize-javascript@npm:^1.4.0":
  version: 1.9.1
  resolution: "serialize-javascript@npm:1.9.1"
  checksum: 01c1aabb28a50133ac7c86038ec14be26b13e0d8d43ac6ad4bad57789bc8dc33b33fef2424330ab9d138da40d9a3c2520f0d66e8c3474eeeab01669d3a38cae1
  languageName: node
  linkType: hard

"serialize-javascript@npm:^4.0.0":
  version: 4.0.0
  resolution: "serialize-javascript@npm:4.0.0"
  dependencies:
    randombytes: "npm:^2.1.0"
  checksum: 510dfe7f0311c0b2f7ab06311afa1668ba2969ab2f1faaac0a4924ede76b7f22ba85cfdeaa0052ec5a047bca42c8cd8ac8df8f0efe52f9bd290b3a39ae69fe9d
  languageName: node
  linkType: hard

"serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "serve-index@npm:1.9.1"
  dependencies:
    accepts: "npm:~1.3.4"
    batch: "npm:0.6.1"
    debug: "npm:2.6.9"
    escape-html: "npm:~1.0.3"
    http-errors: "npm:~1.6.2"
    mime-types: "npm:~2.1.17"
    parseurl: "npm:~1.3.2"
  checksum: a666471a24196f74371edf2c3c7bcdd82adbac52f600804508754b5296c3567588bf694258b19e0cb23a567acfa20d9721bfdaed3286007b81f9741ada8a3a9c
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 528fff6f5e12d0c5a391229ad893910709bc51b5705962b09404a1d813857578149b8815f35d3ee5752f44cd378d0f31669d4b1d7e2d11f41e08283d5134bd1f
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 9f8c1b2d800800d0b589de1477c753492de5c1548d4ade52f57f1d1f5e04af5481554d75ce5e5c43d4004b80a3eb714398d6907027dc0534177b7539119f4454
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 82850e62f412a258b71e123d4ed3873fa9377c216809551192bb6769329340176f109c2eeae8c22a8d386c76739855f78e8716515c818bcaef384b51110f0f3c
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: fce59f90696c450a8523e754abb305e2b8c73586452619c2bad5f7bf38c7b6b4651895c9db895679c5bef9554339cf3ef1c329b66ece3eda7255785fbe299316
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: ca5c3ccbba479d07c30460e367e66337cec825560b11e8ba9c5ebe13a2a0d6021ae34eddf94ff3dfe17a3104dc1f191519cb6c48378b503e5c3f36393938776a
  languageName: node
  linkType: hard

"set-value@npm:^2.0.0, set-value@npm:^2.0.1":
  version: 2.0.1
  resolution: "set-value@npm:2.0.1"
  dependencies:
    extend-shallow: "npm:^2.0.1"
    is-extendable: "npm:^0.1.1"
    is-plain-object: "npm:^2.0.3"
    split-string: "npm:^3.0.1"
  checksum: 4c40573c4f6540456e4b38b95f570272c4cfbe1d12890ad4057886da8535047cd772dfadf5b58e2e87aa244dfb4c57e3586f6716b976fc47c5144b6b09e1811b
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.4":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 5bae81bfdbfbd0ce992893286d49c9693c82b1bcc00dcaaf3a09c8f428fdeacf4190c013598b81875dfac2b08a572422db7df779a99332d0fce186d15a3e4d49
  languageName: node
  linkType: hard

"setprototypeof@npm:1.1.0":
  version: 1.1.0
  resolution: "setprototypeof@npm:1.1.0"
  checksum: a77b20876689c6a89c3b42f0c3596a9cae02f90fc902570cbd97198e9e8240382086c9303ad043e88cee10f61eae19f1004e51d885395a1e9bf49f9ebed12872
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.0, sha.js@npm:^2.4.8":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  bin:
    sha.js: ./bin.js
  checksum: b7a371bca8821c9cc98a0aeff67444a03d48d745cb103f17228b96793f455f0eb0a691941b89ea1e60f6359207e36081d9be193252b0f128e0daf9cfea2815a5
  languageName: node
  linkType: hard

"shebang-command@npm:^1.2.0":
  version: 1.2.0
  resolution: "shebang-command@npm:1.2.0"
  dependencies:
    shebang-regex: "npm:^1.0.0"
  checksum: 7b20dbf04112c456b7fc258622dafd566553184ac9b6938dd30b943b065b21dabd3776460df534cc02480db5e1b6aec44700d985153a3da46e7db7f9bd21326d
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "shebang-regex@npm:1.0.0"
  checksum: 9abc45dee35f554ae9453098a13fdc2f1730e525a5eb33c51f096cc31f6f10a4b38074c1ebf354ae7bffa7229506083844008dfc3bb7818228568c0b2dc1fff2
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"shell-quote@npm:^1.8.1":
  version: 1.8.2
  resolution: "shell-quote@npm:1.8.2"
  checksum: 85fdd44f2ad76e723d34eb72c753f04d847ab64e9f1f10677e3f518d0e5b0752a176fd805297b30bb8c3a1556ebe6e77d2288dbd7b7b0110c7e941e9e9c20ce1
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6, side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.0, signal-exit@npm:^3.0.2":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: df5e4662a8c750bdba69af4e8263c5d96fe4cd0f9fe4bdfa3cbdeb45d2e869dff640beaaeb1ef0e99db4d8d2ec92f85508c269f50c972174851bc1ae5bd64308
  languageName: node
  linkType: hard

"slash@npm:^1.0.0":
  version: 1.0.0
  resolution: "slash@npm:1.0.0"
  checksum: 3944659885d905480f98810542fd314f3e1006eaad25ec78227a7835a469d9ed66fc3dd90abc7377dd2e71f4b5473e8f766bd08198fdd25152a80792e9ed464c
  languageName: node
  linkType: hard

"slash@npm:^2.0.0":
  version: 2.0.0
  resolution: "slash@npm:2.0.0"
  checksum: f83dbd3cb62c41bb8fcbbc6bf5473f3234b97fa1d008f571710a9d3757a28c7169e1811cad1554ccb1cc531460b3d221c9a7b37f549398d9a30707f0a5af9193
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"snapdragon-node@npm:^2.0.1":
  version: 2.1.1
  resolution: "snapdragon-node@npm:2.1.1"
  dependencies:
    define-property: "npm:^1.0.0"
    isobject: "npm:^3.0.0"
    snapdragon-util: "npm:^3.0.1"
  checksum: 7616e6a1ca054afe3ad8defda17ebe4c73b0800d2e0efd635c44ee1b286f8ac7900517314b5330862ce99b28cd2782348ee78bae573ff0f55832ad81d9657f3f
  languageName: node
  linkType: hard

"snapdragon-util@npm:^3.0.1":
  version: 3.0.1
  resolution: "snapdragon-util@npm:3.0.1"
  dependencies:
    kind-of: "npm:^3.2.0"
  checksum: 4441856d343399ba7f37f79681949d51b922e290fcc07e7bc94655a50f584befa4fb08f40c3471cd160e004660161964d8ff140cba49baa59aa6caba774240e3
  languageName: node
  linkType: hard

"snapdragon@npm:^0.8.1":
  version: 0.8.2
  resolution: "snapdragon@npm:0.8.2"
  dependencies:
    base: "npm:^0.11.1"
    debug: "npm:^2.2.0"
    define-property: "npm:^0.2.5"
    extend-shallow: "npm:^2.0.1"
    map-cache: "npm:^0.2.2"
    source-map: "npm:^0.5.6"
    source-map-resolve: "npm:^0.5.0"
    use: "npm:^3.1.0"
  checksum: dfdac1f73d47152d72fc07f4322da09bbddfa31c1c9c3ae7346f252f778c45afa5b03e90813332f02f04f6de8003b34a168c456f8bb719024d092f932520ffca
  languageName: node
  linkType: hard

"sockjs-client@npm:^1.5.0, sockjs-client@npm:^1.6.1":
  version: 1.6.1
  resolution: "sockjs-client@npm:1.6.1"
  dependencies:
    debug: "npm:^3.2.7"
    eventsource: "npm:^2.0.2"
    faye-websocket: "npm:^0.11.4"
    inherits: "npm:^2.0.4"
    url-parse: "npm:^1.5.10"
  checksum: c1b55470aac0a31b0fc87806535b0e5cf5d6289584bcd03ffa9f50328a74a40098be63292d6862bd6f483ac9ef487ad60a8a5082feb1f9d0caee5bad6e50f3a9
  languageName: node
  linkType: hard

"sockjs@npm:^0.3.21":
  version: 0.3.24
  resolution: "sockjs@npm:0.3.24"
  dependencies:
    faye-websocket: "npm:^0.11.3"
    uuid: "npm:^8.3.2"
    websocket-driver: "npm:^0.7.4"
  checksum: aa102c7d921bf430215754511c81ea7248f2dcdf268fbdb18e4d8183493a86b8793b164c636c52f474a886f747447c962741df2373888823271efdb9d2594f33
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.6
  resolution: "socks@npm:2.8.6"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 15b95db4caa359c80bfa880ff3e58f3191b9ffa4313570e501a60ee7575f51e4be664a296f4ee5c2c40544da179db6140be53433ce41ec745f9d51f342557514
  languageName: node
  linkType: hard

"sort-keys@npm:^1.0.0":
  version: 1.1.2
  resolution: "sort-keys@npm:1.1.2"
  dependencies:
    is-plain-obj: "npm:^1.0.0"
  checksum: 5dd383b0299a40277051f7498c3999520138e2eb50d422962f658738341c9e82349fad4a3024d5ba1a3122688fbaf958f2a472d4c53bade55515097c2ce15420
  languageName: node
  linkType: hard

"source-list-map@npm:^2.0.0":
  version: 2.0.1
  resolution: "source-list-map@npm:2.0.1"
  checksum: 2e5e421b185dcd857f46c3c70e2e711a65d717b78c5f795e2e248c9d67757882ea989b80ebc08cf164eeeda5f4be8aa95d3b990225070b2daaaf3257c5958149
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"source-map-resolve@npm:^0.5.0":
  version: 0.5.3
  resolution: "source-map-resolve@npm:0.5.3"
  dependencies:
    atob: "npm:^2.1.2"
    decode-uri-component: "npm:^0.2.0"
    resolve-url: "npm:^0.2.1"
    source-map-url: "npm:^0.4.0"
    urix: "npm:^0.1.0"
  checksum: 410acbe93882e058858d4c1297be61da3e1533f95f25b95903edddc1fb719654e705663644677542d1fb78a66390238fad1a57115fc958a0724cf9bb509caf57
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.12":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map-url@npm:^0.4.0":
  version: 0.4.1
  resolution: "source-map-url@npm:0.4.1"
  checksum: f8af0678500d536c7f643e32094d6718a4070ab4ca2d2326532512cfbe2d5d25a45849b4b385879326f2d7523bb3b686d0360dd347a3cda09fd89a5c28d4bc58
  languageName: node
  linkType: hard

"source-map@npm:^0.5.6":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 904e767bb9c494929be013017380cbba013637da1b28e5943b566031e29df04fba57edf3f093e0914be094648b577372bd8ad247fa98cfba9c600794cd16b599
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.0, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.2.0
  resolution: "spdx-correct@npm:3.2.0"
  dependencies:
    spdx-expression-parse: "npm:^3.0.0"
    spdx-license-ids: "npm:^3.0.0"
  checksum: 49208f008618b9119208b0dadc9208a3a55053f4fd6a0ae8116861bd22696fc50f4142a35ebfdb389e05ccf2de8ad142573fefc9e26f670522d899f7b2fe7386
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.5.0
  resolution: "spdx-exceptions@npm:2.5.0"
  checksum: 37217b7762ee0ea0d8b7d0c29fd48b7e4dfb94096b109d6255b589c561f57da93bf4e328c0290046115961b9209a8051ad9f525e48d433082fc79f496a4ea940
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: "npm:^2.1.0"
    spdx-license-ids: "npm:^3.0.0"
  checksum: 6f8a41c87759fa184a58713b86c6a8b028250f158159f1d03ed9d1b6ee4d9eefdc74181c8ddc581a341aa971c3e7b79e30b59c23b05d2436d5de1c30bdef7171
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.21
  resolution: "spdx-license-ids@npm:3.0.21"
  checksum: ecb24c698d8496aa9efe23e0b1f751f8a7a89faedcdfcbfabae772b546c2db46ccde8f3bc447a238eb86bbcd4f73fea88720ef3b8394f7896381bec3d7736411
  languageName: node
  linkType: hard

"spdy-transport@npm:^3.0.0":
  version: 3.0.0
  resolution: "spdy-transport@npm:3.0.0"
  dependencies:
    debug: "npm:^4.1.0"
    detect-node: "npm:^2.0.4"
    hpack.js: "npm:^2.1.6"
    obuf: "npm:^1.1.2"
    readable-stream: "npm:^3.0.6"
    wbuf: "npm:^1.7.3"
  checksum: eaf7440fa90724fffc813c386d4a8a7427d967d6e46d7c51d8f8a533d1a6911b9823ea9218703debbae755337e85f110185d7a00ae22ec5c847077b908ce71bb
  languageName: node
  linkType: hard

"spdy@npm:^4.0.2":
  version: 4.0.2
  resolution: "spdy@npm:4.0.2"
  dependencies:
    debug: "npm:^4.1.0"
    handle-thing: "npm:^2.0.0"
    http-deceiver: "npm:^1.2.7"
    select-hose: "npm:^2.0.0"
    spdy-transport: "npm:^3.0.0"
  checksum: 983509c0be9d06fd00bb9dff713c5b5d35d3ffd720db869acdd5ad7aa6fc0e02c2318b58f75328957d8ff772acdf1f7d19382b6047df342044ff3e2d6805ccdf
  languageName: node
  linkType: hard

"split-string@npm:^3.0.1, split-string@npm:^3.0.2":
  version: 3.1.0
  resolution: "split-string@npm:3.1.0"
  dependencies:
    extend-shallow: "npm:^3.0.0"
  checksum: 72d7cd625445c7af215130e1e2bc183013bb9dd48a074eda1d35741e2b0dcb355e6df5b5558a62543a24dcec37dd1d6eb7a6228ff510d3c9de0f3dc1d1da8a70
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"sshpk@npm:^1.7.0":
  version: 1.18.0
  resolution: "sshpk@npm:1.18.0"
  dependencies:
    asn1: "npm:~0.2.3"
    assert-plus: "npm:^1.0.0"
    bcrypt-pbkdf: "npm:^1.0.0"
    dashdash: "npm:^1.12.0"
    ecc-jsbn: "npm:~0.1.1"
    getpass: "npm:^0.1.1"
    jsbn: "npm:~0.1.0"
    safer-buffer: "npm:^2.0.2"
    tweetnacl: "npm:~0.14.0"
  bin:
    sshpk-conv: bin/sshpk-conv
    sshpk-sign: bin/sshpk-sign
    sshpk-verify: bin/sshpk-verify
  checksum: e516e34fa981cfceef45fd2e947772cc70dbd57523e5c608e2cd73752ba7f8a99a04df7c3ed751588e8d91956b6f16531590b35d3489980d1c54c38bebcd41b1
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"ssri@npm:^5.2.4":
  version: 5.3.0
  resolution: "ssri@npm:5.3.0"
  dependencies:
    safe-buffer: "npm:^5.1.1"
  checksum: f80372ec982cacb3d8e4b53c2a82a7f7502d915a57aef99a29e6cd293302d5944185a569df5fce694a857abb1e602585fcee90182cd2e853bae3d1c20a84d8cb
  languageName: node
  linkType: hard

"ssri@npm:^6.0.1":
  version: 6.0.2
  resolution: "ssri@npm:6.0.2"
  dependencies:
    figgy-pudding: "npm:^3.5.1"
  checksum: e6f18c57dc9fed69343db5c59f95ef334e9664bfbdbad686c190ef2c6ad6b35e9b56cb203f3e4eb7eee6cb7bb602daa26dab6685e3847f0b5c464cdf7d9c2cee
  languageName: node
  linkType: hard

"stable@npm:^0.1.8":
  version: 0.1.8
  resolution: "stable@npm:0.1.8"
  checksum: df74b5883075076e78f8e365e4068ecd977af6c09da510cfc3148a303d4b87bc9aa8f7c48feb67ed4ef970b6140bd9eabba2129e28024aa88df5ea0114cba39d
  languageName: node
  linkType: hard

"stackframe@npm:^1.3.4":
  version: 1.3.4
  resolution: "stackframe@npm:1.3.4"
  checksum: 18410f7a1e0c5d211a4effa83bdbf24adbe8faa8c34db52e1cd3e89837518c592be60b60d8b7270ac53eeeb8b807cd11b399a41667f6c9abb41059c3ccc8a989
  languageName: node
  linkType: hard

"static-extend@npm:^0.1.1":
  version: 0.1.2
  resolution: "static-extend@npm:0.1.2"
  dependencies:
    define-property: "npm:^0.2.5"
    object-copy: "npm:^0.1.0"
  checksum: 284f5865a9e19d079f1badbcd70d5f9f82e7a08393f818a220839cd5f71729e89105e1c95322bd28e833161d484cee671380ca443869ae89578eef2bf55c0653
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"statuses@npm:>= 1.4.0 < 2":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: e433900956357b3efd79b1c547da4d291799ac836960c016d10a98f6a810b1b5c0dcc13b5a7aa609a58239b5190e1ea176ad9221c2157d2fd1c747393e6b2940
  languageName: node
  linkType: hard

"stealthy-require@npm:^1.1.1":
  version: 1.1.1
  resolution: "stealthy-require@npm:1.1.1"
  checksum: 714b61e152ba03a5e098b5364cc3076d8036edabc2892143fe3c64291194a401b74f071fadebba94551fb013a02f3bcad56a8be29a67b3c644ac78ffda921f80
  languageName: node
  linkType: hard

"stompjs@npm:^2.3.3":
  version: 2.3.3
  resolution: "stompjs@npm:2.3.3"
  dependencies:
    websocket: "npm:latest"
  dependenciesMeta:
    websocket:
      optional: true
  checksum: 53b8371f96673abaf3db698c50782fc99d5a793f8a7a9feab983a5924b902b51b5f41f98b83e38f8149c3694b09f3a0d37d844e1e4a1cac97eeb00ccf1b4704b
  languageName: node
  linkType: hard

"stream-browserify@npm:^2.0.1":
  version: 2.0.2
  resolution: "stream-browserify@npm:2.0.2"
  dependencies:
    inherits: "npm:~2.0.1"
    readable-stream: "npm:^2.0.2"
  checksum: 485562bd5d962d633ae178449029c6fa2611052e356bdb5668f768544aa4daa94c4f9a97de718f3f30ad98f3cb98a5f396252bb3855aff153c138f79c0e8f6ac
  languageName: node
  linkType: hard

"stream-each@npm:^1.1.0":
  version: 1.2.3
  resolution: "stream-each@npm:1.2.3"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    stream-shift: "npm:^1.0.0"
  checksum: 7ed229d3b7c24373058b5742b00066da8d3122d1487c8219a025ed53a8978545c77654a529a8e9c62ba83ae80c424cbb0204776b49abf72270d2e8154831dd5f
  languageName: node
  linkType: hard

"stream-http@npm:^2.7.2":
  version: 2.8.3
  resolution: "stream-http@npm:2.8.3"
  dependencies:
    builtin-status-codes: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
    readable-stream: "npm:^2.3.6"
    to-arraybuffer: "npm:^1.0.0"
    xtend: "npm:^4.0.0"
  checksum: fbe7d327a29216bbabe88d3819bb8f7a502f11eeacf3212579e5af1f76fa7283f6ffa66134ab7d80928070051f571d1029e85f65ce3369fffd4c4df3669446c4
  languageName: node
  linkType: hard

"stream-shift@npm:^1.0.0":
  version: 1.0.3
  resolution: "stream-shift@npm:1.0.3"
  checksum: 939cd1051ca750d240a0625b106a2b988c45fb5a3be0cebe9a9858cb01bc1955e8c7b9fac17a9462976bea4a7b704e317c5c2200c70f0ca715a3363b9aa4fd3b
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^1.0.0":
  version: 1.1.0
  resolution: "strict-uri-encode@npm:1.1.0"
  checksum: eb8a4109ba2588239787389313ba58ec49e043d4c64a1d44716defe5821a68ae49abe0cdefed9946ca9fc2a4af7ecf321da92422b0a67258ec0a3638b053ae62
  languageName: node
  linkType: hard

"string-utilz@npm:^1.3.0":
  version: 1.4.0
  resolution: "string-utilz@npm:1.4.0"
  checksum: 72f1f1d2489c7b9a84d9648ef5a2b6490eb0ece7c1b808750cd4749074910cccc91b3ba55694f3a61055d195afb28fbc5e6f614b480c1aeb34909bb0c4b271d6
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^3.0.0, string-width@npm:^3.1.0":
  version: 3.1.0
  resolution: "string-width@npm:3.1.0"
  dependencies:
    emoji-regex: "npm:^7.0.1"
    is-fullwidth-code-point: "npm:^2.0.0"
    strip-ansi: "npm:^5.1.0"
  checksum: 85fa0d4f106e7999bb68c1c640c76fa69fb8c069dab75b009e29c123914e2d3b532e6cfa4b9d1bd913176fc83dedd7a2d7bf40d21a81a8a1978432cedfb65b91
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string.prototype.padend@npm:^3.0.0":
  version: 3.1.6
  resolution: "string.prototype.padend@npm:3.1.6"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
  checksum: 8f2c8c1f3db1efcdc210668c80c87f2cea1253d6029ff296a172b5e13edc9adebeed4942d023de8d31f9b13b69f3f5d73de7141959b1f09817fba5f527e83be1
  languageName: node
  linkType: hard

"string.prototype.padstart@npm:^3.0.0":
  version: 3.1.7
  resolution: "string.prototype.padstart@npm:3.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-object-atoms: "npm:^1.1.1"
  checksum: b554810e1b65c68c3a1b5bf38d0b17830116491b7ddeeaa4504588cb79e6c614beebe71f4047c5f3ca8fdf5502ef6a0c3770f4374ce5144b1660b81462014e4e
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 8a8854241c4b54a948e992eb7dd6b8b3a97185112deb0037a134f5ba57541d8248dd610c966311887b6c2fd1181a3877bffb14d873ce937a344535dabcc648f8
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 59e1a70bf9414cb4c536a6e31bef5553c8ceb0cf44d8b4d0ed65c9653358d1c64dd0ec203b100df83d0413bbcde38b8c5d49e14bc4b86737d74adc593a0d35b6
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: d53af1899959e53c83b64a5fd120be93e067da740e7e75acb433849aa640782fb6c7d4cd5b84c954c84413745a3764df135a8afeb22908b86a835290788d8366
  languageName: node
  linkType: hard

"string_decoder@npm:^1.0.0, string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^3.0.0, strip-ansi@npm:^3.0.1":
  version: 3.0.1
  resolution: "strip-ansi@npm:3.0.1"
  dependencies:
    ansi-regex: "npm:^2.0.0"
  checksum: f6e7fbe8e700105dccf7102eae20e4f03477537c74b286fd22cfc970f139002ed6f0d9c10d0e21aa9ed9245e0fa3c9275930e8795c5b947da136e4ecb644a70f
  languageName: node
  linkType: hard

"strip-ansi@npm:^5.0.0, strip-ansi@npm:^5.1.0, strip-ansi@npm:^5.2.0":
  version: 5.2.0
  resolution: "strip-ansi@npm:5.2.0"
  dependencies:
    ansi-regex: "npm:^4.1.0"
  checksum: de4658c8a097ce3b15955bc6008f67c0790f85748bdc025b7bc8c52c7aee94bc4f9e50624516150ed173c3db72d851826cd57e7a85fe4e4bb6dbbebd5d297fdf
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-eof@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-eof@npm:1.0.0"
  checksum: f336beed8622f7c1dd02f2cbd8422da9208fae81daf184f73656332899978919d5c0ca84dc6cfc49ad1fc4dd7badcde5412a063cf4e0d7f8ed95a13a63f68f45
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"style-resources-loader@npm:^1.2.1":
  version: 1.5.0
  resolution: "style-resources-loader@npm:1.5.0"
  dependencies:
    glob: "npm:^7.2.0"
    loader-utils: "npm:^2.0.0"
    schema-utils: "npm:^2.7.0"
    tslib: "npm:^2.3.1"
  peerDependencies:
    webpack: ^3.0.0 || ^4.0.0 || ^5.0.0
  checksum: b09c6443bcd3cb41e3f0785bc8bd766621af911cb1c0acd6271cd799429b184749555d4b5252a3d89b79a166fc64e290ab90050b7bb527fc674e10f39e549d7a
  languageName: node
  linkType: hard

"stylehacks@npm:^4.0.0":
  version: 4.0.3
  resolution: "stylehacks@npm:4.0.3"
  dependencies:
    browserslist: "npm:^4.0.0"
    postcss: "npm:^7.0.0"
    postcss-selector-parser: "npm:^3.0.0"
  checksum: d063adba9018dbf765f80bd4fa5136dfabd5d9bb7b3654a4990c633c539e0f38e758fc9d16c12876afbcec1dd6534d9e9178aa0a3e183345ff034eae9605d35b
  languageName: node
  linkType: hard

"supports-color@npm:^2.0.0":
  version: 2.0.0
  resolution: "supports-color@npm:2.0.0"
  checksum: 570e0b63be36cccdd25186350a6cb2eaad332a95ff162fa06d9499982315f2fe4217e69dd98e862fbcd9c81eaff300a825a1fe7bf5cc752e5b84dfed042b0dda
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0, supports-color@npm:^5.4.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 6ae5ff319bfbb021f8a86da8ea1f8db52fac8bd4d499492e30ec17095b58af11f0c55f8577390a749b1c4dde691b6a0315dab78f5f54c9b3d83f8fb5905c1c05
  languageName: node
  linkType: hard

"supports-color@npm:^6.1.0":
  version: 6.1.0
  resolution: "supports-color@npm:6.1.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: ebf2befe41b55932c6d77192b91775f1403c389440ce2dab6f72663cf32ee87a1d9dea3512131a18e45ccac91424a8873b266142828489d0206d65ee93d224b6
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"svg-tags@npm:^1.0.0":
  version: 1.0.0
  resolution: "svg-tags@npm:1.0.0"
  checksum: 5867e29e8f431bf7aecf5a244d1af5725f80a1086187dbc78f26d8433b5e96b8fe9361aeb10d1699ff483b9afec785a10916b9312fe9d734d1a7afd48226c954
  languageName: node
  linkType: hard

"svgo@npm:^1.0.0":
  version: 1.3.2
  resolution: "svgo@npm:1.3.2"
  dependencies:
    chalk: "npm:^2.4.1"
    coa: "npm:^2.0.2"
    css-select: "npm:^2.0.0"
    css-select-base-adapter: "npm:^0.1.1"
    css-tree: "npm:1.0.0-alpha.37"
    csso: "npm:^4.0.2"
    js-yaml: "npm:^3.13.1"
    mkdirp: "npm:~0.5.1"
    object.values: "npm:^1.1.0"
    sax: "npm:~1.2.4"
    stable: "npm:^0.1.8"
    unquote: "npm:~1.1.1"
    util.promisify: "npm:~1.0.0"
  bin:
    svgo: ./bin/svgo
  checksum: 261a82b08acf63accd7a54b47b4ffcd2fc7e7d7f8efef3cbc61184583b24b4c5434656004c30190302821af0f6d7b047eac730b0dcdab5d179e6a74383ccc776
  languageName: node
  linkType: hard

"tapable@npm:^1.0.0, tapable@npm:^1.1.3":
  version: 1.1.3
  resolution: "tapable@npm:1.1.3"
  checksum: c9f0265e55e45821ec672b9b9ee8a35d95bf3ea6b352199f8606a2799018e89cfe4433c554d424b31fc67c4be26b05d4f36dc3c607def416fdb2514cd63dba50
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^1.2.3, terser-webpack-plugin@npm:^1.4.3":
  version: 1.4.6
  resolution: "terser-webpack-plugin@npm:1.4.6"
  dependencies:
    cacache: "npm:^12.0.2"
    find-cache-dir: "npm:^2.1.0"
    is-wsl: "npm:^1.1.0"
    schema-utils: "npm:^1.0.0"
    serialize-javascript: "npm:^4.0.0"
    source-map: "npm:^0.6.1"
    terser: "npm:^4.1.2"
    webpack-sources: "npm:^1.4.0"
    worker-farm: "npm:^1.7.0"
  peerDependencies:
    webpack: ^4.0.0
  checksum: 417607cce0f2fdbd0935ffad8a1fb34a25042c714ddfd06af3b6a738f956b8db0e4659f64c57e18c02ad816ce35a163d502b4e2832798be8d652c9a2e2a36f69
  languageName: node
  linkType: hard

"terser@npm:^4.1.2":
  version: 4.8.1
  resolution: "terser@npm:4.8.1"
  dependencies:
    commander: "npm:^2.20.0"
    source-map: "npm:~0.6.1"
    source-map-support: "npm:~0.5.12"
  bin:
    terser: bin/terser
  checksum: 1ec2620e58df0ea787ac579daf097df0fee2dd402f37acb4de0df1135f0598a29212e5f03042a9c2dc7e1bf1248b1dd9d9ea0724d34331a2017f32da8783b3d7
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: "npm:>= 3.1.0 < 4"
  checksum: 9b896a22735e8122754fe70f1d65f7ee691c1d70b1f116fda04fea103d0f9b356e3676cb789506e3909ae0486a79a476e4914b0f92472c2e093d206aed4b7d6b
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: "npm:^1.0.0"
  checksum: f375aeb2b05c100a456a30bc3ed07ef03a39cbdefe02e0403fb714b8c7e57eeaad1a2f5c4ecfb9ce554ce3db9c2b024eba144843cd9e344566d9fcee73b04767
  languageName: node
  linkType: hard

"thread-loader@npm:^2.1.2":
  version: 2.1.3
  resolution: "thread-loader@npm:2.1.3"
  dependencies:
    loader-runner: "npm:^2.3.1"
    loader-utils: "npm:^1.1.0"
    neo-async: "npm:^2.6.0"
  peerDependencies:
    webpack: ^2.0.0 || ^3.0.0 || ^4.0.0
  checksum: 41c8a67e04d2036212fbc5b61d58e5b18865322fa343a23cbf20fe208a09b27f9c3f3f935b4762ddb19e9cc31dccaa81cffa23518e7221b9c89b0a4d831fc5b6
  languageName: node
  linkType: hard

"through2@npm:^2.0.0":
  version: 2.0.5
  resolution: "through2@npm:2.0.5"
  dependencies:
    readable-stream: "npm:~2.3.6"
    xtend: "npm:~4.0.1"
  checksum: cbfe5b57943fa12b4f8c043658c2a00476216d79c014895cef1ac7a1d9a8b31f6b438d0e53eecbb81054b93128324a82ecd59ec1a4f91f01f7ac113dcb14eade
  languageName: node
  linkType: hard

"thunky@npm:^1.0.2":
  version: 1.1.0
  resolution: "thunky@npm:1.1.0"
  checksum: 369764f39de1ce1de2ba2fa922db4a3f92e9c7f33bcc9a713241bc1f4a5238b484c17e0d36d1d533c625efb00e9e82c3e45f80b47586945557b45abb890156d2
  languageName: node
  linkType: hard

"timers-browserify@npm:^2.0.4":
  version: 2.0.12
  resolution: "timers-browserify@npm:2.0.12"
  dependencies:
    setimmediate: "npm:^1.0.4"
  checksum: 98e84db1a685bc8827c117a8bc62aac811ad56a995d07938fc7ed8cdc5bf3777bfe2d4e5da868847194e771aac3749a20f6cdd22091300fe889a76fe214a4641
  languageName: node
  linkType: hard

"timsort@npm:^0.3.0":
  version: 0.3.0
  resolution: "timsort@npm:0.3.0"
  checksum: 571b2054a0db3cf80eb255f8609a1f798cae9176f9ec6e3fbd03d64186c015cc9e1e75b88ba38e1d71aebcc03a931352522c7387dcb90caeb148375c7bc106f4
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: f789ed6c924287a9b7d3612056ed0cda67306cd2c80c249fd280cf1504742b12583a2089b61f4abbd24605f390809017240e250241f09938054c9b363e51c0a6
  languageName: node
  linkType: hard

"to-arraybuffer@npm:^1.0.0":
  version: 1.0.1
  resolution: "to-arraybuffer@npm:1.0.1"
  checksum: 2460bd95524f4845a751e4f8bf9937f9f3dcd1651f104e1512868782f858f8302c1cf25bbc30794bc1b3ff65c4e135158377302f2abaff43a2d8e3c38dfe098c
  languageName: node
  linkType: hard

"to-object-path@npm:^0.3.0":
  version: 0.3.0
  resolution: "to-object-path@npm:0.3.0"
  dependencies:
    kind-of: "npm:^3.0.2"
  checksum: 731832a977614c03a770363ad2bd9e9c82f233261861724a8e612bb90c705b94b1a290a19f52958e8e179180bb9b71121ed65e245691a421467726f06d1d7fc3
  languageName: node
  linkType: hard

"to-regex-range@npm:^2.1.0":
  version: 2.1.1
  resolution: "to-regex-range@npm:2.1.1"
  dependencies:
    is-number: "npm:^3.0.0"
    repeat-string: "npm:^1.6.1"
  checksum: 440d82dbfe0b2e24f36dd8a9467240406ad1499fc8b2b0f547372c22ed1d092ace2a3eb522bb09bfd9c2f39bf1ca42eb78035cf6d2b8c9f5c78da3abc96cd949
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"to-regex@npm:^3.0.1, to-regex@npm:^3.0.2":
  version: 3.0.2
  resolution: "to-regex@npm:3.0.2"
  dependencies:
    define-property: "npm:^2.0.2"
    extend-shallow: "npm:^3.0.2"
    regex-not: "npm:^1.0.2"
    safe-regex: "npm:^1.1.0"
  checksum: 99d0b8ef397b3f7abed4bac757b0f0bb9f52bfd39167eb7105b144becfaa9a03756892352d01ac6a911f0c1ceef9f81db68c46899521a3eed054082042796120
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"toposort@npm:^1.0.0":
  version: 1.0.7
  resolution: "toposort@npm:1.0.7"
  checksum: a9ddfacf64edae723520bb866c98f00ebf7d9aa45185a3f5b517c30678d22029a444348ca260fbf199ac99bd0bdc3abc21e220775406b90e78bb8a1bb30de875
  languageName: node
  linkType: hard

"tough-cookie@npm:^2.3.3, tough-cookie@npm:~2.5.0":
  version: 2.5.0
  resolution: "tough-cookie@npm:2.5.0"
  dependencies:
    psl: "npm:^1.1.28"
    punycode: "npm:^2.1.1"
  checksum: e1cadfb24d40d64ca16de05fa8192bc097b66aeeb2704199b055ff12f450e4f30c927ce250f53d01f39baad18e1c11d66f65e545c5c6269de4c366fafa4c0543
  languageName: node
  linkType: hard

"tryer@npm:^1.0.1":
  version: 1.0.1
  resolution: "tryer@npm:1.0.1"
  checksum: 19070409a0009dc26127636cc14d2415e9cf8b1dc07b29694e57ea8bb5ea1bded012c0e792f6235b46e31189a7b866841668b3850867ff7eac1a6b55332c960d
  languageName: node
  linkType: hard

"tslib@npm:^1.10.0":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 69ae09c49eea644bc5ebe1bca4fa4cc2c82b7b3e02f43b84bd891504edf66dbc6b2ec0eef31a957042de2269139e4acff911e6d186a258fb14069cd7f6febce2
  languageName: node
  linkType: hard

"tslib@npm:^2.3.1":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"tty-browserify@npm:0.0.0":
  version: 0.0.0
  resolution: "tty-browserify@npm:0.0.0"
  checksum: c0c68206565f1372e924d5cdeeff1a0d9cc729833f1da98c03d78be8f939e5f61a107bd0ab77d1ef6a47d62bb0e48b1081fbea273acf404959e22fd3891439c5
  languageName: node
  linkType: hard

"tunnel-agent@npm:^0.6.0":
  version: 0.6.0
  resolution: "tunnel-agent@npm:0.6.0"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 4c7a1b813e7beae66fdbf567a65ec6d46313643753d0beefb3c7973d66fcec3a1e7f39759f0a0b4465883499c6dc8b0750ab8b287399af2e583823e40410a17a
  languageName: node
  linkType: hard

"tweetnacl@npm:^0.14.3, tweetnacl@npm:~0.14.0":
  version: 0.14.5
  resolution: "tweetnacl@npm:0.14.5"
  checksum: 4612772653512c7bc19e61923fbf42903f5e0389ec76a4a1f17195859d114671ea4aa3b734c2029ce7e1fa7e5cc8b80580f67b071ecf0b46b5636d030a0102a2
  languageName: node
  linkType: hard

"type-fest@npm:^0.6.0":
  version: 0.6.0
  resolution: "type-fest@npm:0.6.0"
  checksum: 0c585c26416fce9ecb5691873a1301b5aff54673c7999b6f925691ed01f5b9232db408cdbb0bd003d19f5ae284322523f44092d1f81ca0a48f11f7cf0be8cd38
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: a23daeb538591b7efbd61ecf06b6feb2501b683ffdc9a19c74ef5baba362b4347e42f1b4ed81f5882a8c96a3bfff7f93ce3ffaf0cbbc879b532b04c97a55db9d
  languageName: node
  linkType: hard

"type@npm:^2.7.2":
  version: 2.7.3
  resolution: "type@npm:2.7.3"
  checksum: dec6902c2c42fcb86e3adf8cdabdf80e5ef9de280872b5fd547351e9cca2fe58dd2aa6d2547626ddff174145db272f62d95c7aa7038e27c11315657d781a688d
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 1105071756eb248774bc71646bfe45b682efcad93b55532c6ffa4518969fb6241354e4aa62af679ae83899ec296d69ef88f1f3763657cdb3a4d29321f7b83079
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 6ae083c6f0354f1fce18b90b243343b9982affd8d839c57bbd2c174a5d5dc71be9eb7019ffd12628a96a4815e7afa85d718d6f1e758615151d5f35df841ffb3e
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 3d805b050c0c33b51719ee52de17c1cd8e6a571abdf0fffb110e45e8dd87a657e8b56eee94b776b13006d3d347a0c18a730b903cf05293ab6d92e99ff8f77e53
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: e38f2ae3779584c138a2d8adfa8ecf749f494af3cd3cdafe4e688ce51418c7d2c5c88df1bd6be2bbea099c3f7cea58c02ca02ed438119e91f162a9de23f61295
  languageName: node
  linkType: hard

"typedarray-to-buffer@npm:^3.1.5":
  version: 3.1.5
  resolution: "typedarray-to-buffer@npm:3.1.5"
  dependencies:
    is-typedarray: "npm:^1.0.0"
  checksum: 4ac5b7a93d604edabf3ac58d3a2f7e07487e9f6e98195a080e81dbffdc4127817f470f219d794a843b87052cedef102b53ac9b539855380b8c2172054b7d5027
  languageName: node
  linkType: hard

"typedarray@npm:^0.0.6":
  version: 0.0.6
  resolution: "typedarray@npm:0.0.6"
  checksum: 6005cb31df50eef8b1f3c780eb71a17925f3038a100d82f9406ac2ad1de5eb59f8e6decbdc145b3a1f8e5836e17b0c0002fb698b9fe2516b8f9f9ff602d36412
  languageName: node
  linkType: hard

"uglify-js@npm:3.4.x":
  version: 3.4.10
  resolution: "uglify-js@npm:3.4.10"
  dependencies:
    commander: "npm:~2.19.0"
    source-map: "npm:~0.6.1"
  bin:
    uglifyjs: bin/uglifyjs
  checksum: 54021f980d6a4a9ad808be3d4d8a7bf52c009c16d4c54c60ed9142490b337b627e5d6616edea93b682f5eb5dced057b185345cc0110425e81e54bc92e535acdd
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 7dbd35ab02b0e05fe07136c72cb9355091242455473ec15057c11430129bab38b7b3624019b8778d02a881c13de44d63cd02d122ee782fb519e1de7775b5b982
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: c01ed51829b10aa72fc3ce64b747f8e74ae9b60eafa19a7b46ef624403508a54c526ffab06a14a26b3120d055e1104d7abe7c9017e83ced038ea5cf52f8d5e04
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: f83bc492fdbe662860795ef37a85910944df7310cac91bd778f1c19ebc911e8b9cde84e703de631e5a2fcca3905e39896f8fc5fc6a44ddaf7f4aff1cda24f381
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: "npm:^2.0.0"
    unicode-property-aliases-ecmascript: "npm:^2.0.0"
  checksum: 4d05252cecaf5c8e36d78dc5332e03b334c6242faf7cf16b3658525441386c0a03b5f603d42cbec0f09bb63b9fd25c9b3b09667aee75463cac3efadae2cd17ec
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 1d0a2deefd97974ddff5b7cb84f9884177f4489928dfcebb4b2b091d6124f2739df51fc6ea15958e1b5637ac2a24cff9bf21ea81e45335086ac52c0b4c717d6d
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 50ded3f8c963c7785e48c510a3b7c6bc4e08a579551489aa0349680a35b1ceceec122e33b2b6c1b579d0be2250f34bb163ac35f5f8695fe10bbc67fb757f0af8
  languageName: node
  linkType: hard

"union-value@npm:^1.0.0":
  version: 1.0.1
  resolution: "union-value@npm:1.0.1"
  dependencies:
    arr-union: "npm:^3.1.0"
    get-value: "npm:^2.0.6"
    is-extendable: "npm:^0.1.1"
    set-value: "npm:^2.0.1"
  checksum: 8758d880cb9545f62ce9cfb9b791b2b7a206e0ff5cc4b9d7cd6581da2c6839837fbb45e639cf1fd8eef3cae08c0201b614b7c06dd9f5f70d9dbe7c5fe2fbf592
  languageName: node
  linkType: hard

"uniq@npm:^1.0.1":
  version: 1.0.1
  resolution: "uniq@npm:1.0.1"
  checksum: 369dca4a07fdd8de9e48378b9d4b6861722ca71d5f496e91687916bd4b48b8cf3d6db1677be1b40eea63bc6d4728efb4b4e0bd7a89c5fd2d23e7a2cff8009c7a
  languageName: node
  linkType: hard

"uniqs@npm:^2.0.0":
  version: 2.0.0
  resolution: "uniqs@npm:2.0.0"
  checksum: f244b158f6b3d30ddea2092c581fe9eb746d6db889a859fd0001e4b7566767bfcc53cdf11e6286f097bb130ebad71025709f41f2e4dd38e5c2f03ec28052baab
  languageName: node
  linkType: hard

"unique-filename@npm:^1.1.0, unique-filename@npm:^1.1.1":
  version: 1.1.1
  resolution: "unique-filename@npm:1.1.1"
  dependencies:
    unique-slug: "npm:^2.0.0"
  checksum: d005bdfaae6894da8407c4de2b52f38b3c58ec86e79fc2ee19939da3085374413b073478ec54e721dc8e32b102cf9e50d0481b8331abdc62202e774b789ea874
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^2.0.0":
  version: 2.0.2
  resolution: "unique-slug@npm:2.0.2"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 9eabc51680cf0b8b197811a48857e41f1364b25362300c1ff636c0eca5ec543a92a38786f59cf0697e62c6f814b11ecbe64e8093db71246468a1f03b80c83970
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: e70e0339f6b36f34c9816f6bf9662372bd241714dc77508d231d08386d94f2c4aa1ba1318614f92015f40d45aae1b9075cd30bd490efbe39387b60a76ca3f045
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"unquote@npm:~1.1.1":
  version: 1.1.1
  resolution: "unquote@npm:1.1.1"
  checksum: de59fb48cbaadc636002c6563dcb6b1bce95c91ebecb92addbc9bb47982cb03e7d8a8371c9617267b9e5746bbcb4403394139bc1310106b9ac4c26790ed57859
  languageName: node
  linkType: hard

"unset-value@npm:^1.0.0":
  version: 1.0.0
  resolution: "unset-value@npm:1.0.0"
  dependencies:
    has-value: "npm:^0.3.1"
    isobject: "npm:^3.0.0"
  checksum: 68a796dde4a373afdbf017de64f08490a3573ebee549136da0b3a2245299e7f65f647ef70dc13c4ac7f47b12fba4de1646fa0967a365638578fedce02b9c0b1f
  languageName: node
  linkType: hard

"upath@npm:^1.1.1":
  version: 1.2.0
  resolution: "upath@npm:1.2.0"
  checksum: 3746f24099bf69dbf8234cecb671e1016e1f6b26bd306de4ff8966fb0bc463fa1014ffc48646b375de1ab573660e3a0256f6f2a87218b2dfa1779a84ef6992fa
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"upper-case@npm:^1.1.1":
  version: 1.1.3
  resolution: "upper-case@npm:1.1.3"
  checksum: 3e4d3a90519915bb591db84d72610392518806d8287b8f7541d87642d30388f42b2def1ed2f687e5792ee025e8f7e17d3a0dcbd5b3b59e306ceb1f3b8121ef54
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"urix@npm:^0.1.0":
  version: 0.1.0
  resolution: "urix@npm:0.1.0"
  checksum: 264f1b29360c33c0aec5fb9819d7e28f15d1a3b83175d2bcc9131efe8583f459f07364957ae3527f1478659ec5b2d0f1ad401dfb625f73e4d424b3ae35fc5fc0
  languageName: node
  linkType: hard

"url-loader@npm:^1.1.2":
  version: 1.1.2
  resolution: "url-loader@npm:1.1.2"
  dependencies:
    loader-utils: "npm:^1.1.0"
    mime: "npm:^2.0.3"
    schema-utils: "npm:^1.0.0"
  peerDependencies:
    webpack: ^3.0.0 || ^4.0.0
  checksum: 7c5137cddd18c972784df4e4aeb65f7d26d38fc56830c8119fceb6a2e69761cb3a8ef61272d8c1a5674dda820553852b862f2011b06066e967ce99e2458e998a
  languageName: node
  linkType: hard

"url-parse@npm:^1.5.10":
  version: 1.5.10
  resolution: "url-parse@npm:1.5.10"
  dependencies:
    querystringify: "npm:^2.1.1"
    requires-port: "npm:^1.0.0"
  checksum: bd5aa9389f896974beb851c112f63b466505a04b4807cea2e5a3b7092f6fbb75316f0491ea84e44f66fed55f1b440df5195d7e3a8203f64fcefa19d182f5be87
  languageName: node
  linkType: hard

"url@npm:^0.11.0":
  version: 0.11.4
  resolution: "url@npm:0.11.4"
  dependencies:
    punycode: "npm:^1.4.1"
    qs: "npm:^6.12.3"
  checksum: cc93405ae4a9b97a2aa60ca67f1cb1481c0221cb4725a7341d149be5e2f9cfda26fd432d64dbbec693d16593b68b8a46aad8e5eab21f814932134c9d8620c662
  languageName: node
  linkType: hard

"use@npm:^3.1.0":
  version: 3.1.1
  resolution: "use@npm:3.1.1"
  checksum: 75b48673ab80d5139c76922630d5a8a44e72ed58dbaf54dee1b88352d10e1c1c1fc332066c782d8ae9a56503b85d3dc67ff6d2ffbd9821120466d1280ebb6d6e
  languageName: node
  linkType: hard

"utf-8-validate@npm:^5.0.2":
  version: 5.0.10
  resolution: "utf-8-validate@npm:5.0.10"
  dependencies:
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.3.0"
  checksum: 23cd6adc29e6901aa37ff97ce4b81be9238d0023c5e217515b34792f3c3edb01470c3bd6b264096dd73d0b01a1690b57468de3a24167dd83004ff71c51cc025f
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"util.promisify@npm:1.0.0":
  version: 1.0.0
  resolution: "util.promisify@npm:1.0.0"
  dependencies:
    define-properties: "npm:^1.1.2"
    object.getownpropertydescriptors: "npm:^2.0.3"
  checksum: af9df9d111b1464586e4fa414ccf6de61c3a14c0664a66a497438a0507d47f65389f5e025c048ef7e2bf6dba73e95adc3d0c56111a0952ae0282817fc4dd83b2
  languageName: node
  linkType: hard

"util.promisify@npm:~1.0.0":
  version: 1.0.1
  resolution: "util.promisify@npm:1.0.1"
  dependencies:
    define-properties: "npm:^1.1.3"
    es-abstract: "npm:^1.17.2"
    has-symbols: "npm:^1.0.1"
    object.getownpropertydescriptors: "npm:^2.1.0"
  checksum: d72b7c1344816bc9c8713efbf5cb23b536730a8fb7df9ae50654d9efa4d24241fc5ecc69a7dc63b9a2f98cabc9635c303923671933f8c6f41fa7d64fe2188e27
  languageName: node
  linkType: hard

"util@npm:^0.10.4":
  version: 0.10.4
  resolution: "util@npm:0.10.4"
  dependencies:
    inherits: "npm:2.0.3"
  checksum: d29f6893e406b63b088ce9924da03201df89b31490d4d011f1c07a386ea4b3dbe907464c274023c237da470258e1805d806c7e4009a5974cd6b1d474b675852a
  languageName: node
  linkType: hard

"util@npm:^0.11.0":
  version: 0.11.1
  resolution: "util@npm:0.11.1"
  dependencies:
    inherits: "npm:2.0.3"
  checksum: 8e9d1a85e661c8a8d9883d821aedbff3f8d9c3accd85357020905386ada5653b20389fc3591901e2a0bde64f8dc86b28c3f990114aa5a38eaaf30b455fa3cdf6
  languageName: node
  linkType: hard

"utila@npm:~0.4":
  version: 0.4.0
  resolution: "utila@npm:0.4.0"
  checksum: 2791604e09ca4f77ae314df83e80d1805f867eb5c7e13e7413caee01273c278cf2c9a3670d8d25c889a877f7b149d892fe61b0181a81654b425e9622ab23d42e
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"uuid@npm:^3.3.2":
  version: 3.4.0
  resolution: "uuid@npm:3.4.0"
  bin:
    uuid: ./bin/uuid
  checksum: 1c13950df865c4f506ebfe0a24023571fa80edf2e62364297a537c80af09c618299797bbf2dbac6b1f8ae5ad182ba474b89db61e0e85839683991f7e08795347
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: bcbb807a917d374a49f475fae2e87fdca7da5e5530820ef53f65ba1d12131bd81a92ecf259cc7ce317cbe0f289e7d79fdfebcef9bfa3087c8c8a2fa304c9be54
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: "npm:^3.0.0"
    spdx-expression-parse: "npm:^3.0.0"
  checksum: 7b91e455a8de9a0beaa9fe961e536b677da7f48c9a493edf4d4d4a87fd80a7a10267d438723364e432c2fcd00b5650b5378275cded362383ef570276e6312f4f
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"vendors@npm:^1.0.0":
  version: 1.0.4
  resolution: "vendors@npm:1.0.4"
  checksum: a9b097f3607013a23bf447cbaff85b79b694cc23b20e81a6aea1ea9e1c59854c93f7c87abcc71b57999e050606e499d9ce18df67968823644b20f6e03d56022a
  languageName: node
  linkType: hard

"verror@npm:1.10.0":
  version: 1.10.0
  resolution: "verror@npm:1.10.0"
  dependencies:
    assert-plus: "npm:^1.0.0"
    core-util-is: "npm:1.0.2"
    extsprintf: "npm:^1.2.0"
  checksum: 37ccdf8542b5863c525128908ac80f2b476eed36a32cb944de930ca1e2e78584cc435c4b9b4c68d0fc13a47b45ff364b4be43aa74f8804f9050140f660fb660d
  languageName: node
  linkType: hard

"vm-browserify@npm:^1.0.1":
  version: 1.1.2
  resolution: "vm-browserify@npm:1.1.2"
  checksum: 0cc1af6e0d880deb58bc974921320c187f9e0a94f25570fca6b1bd64e798ce454ab87dfd797551b1b0cc1849307421aae0193cedf5f06bdb5680476780ee344b
  languageName: node
  linkType: hard

"vue-hot-reload-api@npm:^2.3.0":
  version: 2.3.4
  resolution: "vue-hot-reload-api@npm:2.3.4"
  checksum: 6501a93582c2bba0f17564d1c61b4301e844e14fbac1cb7c3d726c40961375aefa89f2cc4ee8289c3663e12d108c28a5872ba35cfa7f091d1bcaa39feff9ac60
  languageName: node
  linkType: hard

"vue-loader@npm:^15.7.0":
  version: 15.11.1
  resolution: "vue-loader@npm:15.11.1"
  dependencies:
    "@vue/component-compiler-utils": "npm:^3.1.0"
    hash-sum: "npm:^1.0.2"
    loader-utils: "npm:^1.1.0"
    vue-hot-reload-api: "npm:^2.3.0"
    vue-style-loader: "npm:^4.1.0"
  peerDependencies:
    css-loader: "*"
    webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
  peerDependenciesMeta:
    cache-loader:
      optional: true
    prettier:
      optional: true
    vue-template-compiler:
      optional: true
  checksum: 22491414f3743d485cf8d966837314706abf35d330bf055e356d55f16df8d4ab21fb712c7168509f7492d62cdf799aedf8d31df36d89bd5a4479b9f90fa094c1
  languageName: node
  linkType: hard

"vue-router@npm:^3.0.3":
  version: 3.6.5
  resolution: "vue-router@npm:3.6.5"
  checksum: f1c81854a7bb943fe78ad1516acc948fffbf71646ee4d568c52c0c680dd4763152d1c317ac240a5066bc24682700b4698bd135d54c2fc7d8cb21d068a99e50ac
  languageName: node
  linkType: hard

"vue-style-loader@npm:^4.1.0":
  version: 4.1.3
  resolution: "vue-style-loader@npm:4.1.3"
  dependencies:
    hash-sum: "npm:^1.0.2"
    loader-utils: "npm:^1.0.2"
  checksum: 871362711561c817c6b96650cf4bcf422c51d46808650da7e6ec39499d76445d08a1f9f1d1aa0f6cffb191cd128fbd77b6e233d9689a87c21d7e546689bed04c
  languageName: node
  linkType: hard

"vue-template-compiler@npm:^2.7.16":
  version: 2.7.16
  resolution: "vue-template-compiler@npm:2.7.16"
  dependencies:
    de-indent: "npm:^1.0.2"
    he: "npm:^1.2.0"
  checksum: 66667ffd5095b707f169c902c4f1a011e9d5ab99fc228e4dac14eb5ca7f107ed99bff261b21578a4b391d2f3d320a8050e754404443472acad13ddaa4bd7bae2
  languageName: node
  linkType: hard

"vue-template-es2015-compiler@npm:^1.9.0":
  version: 1.9.1
  resolution: "vue-template-es2015-compiler@npm:1.9.1"
  checksum: 21d27d1c6afe10a47f17793e18afb7f321888d3ca728bfdb2a79ff49789ed9b40e98abcb68b5499f3da1bbb76a0f188b94aeb5ab0e879f46d6399ac5d4ae38c8
  languageName: node
  linkType: hard

"vue@npm:^2.6.10":
  version: 2.7.16
  resolution: "vue@npm:2.7.16"
  dependencies:
    "@vue/compiler-sfc": "npm:2.7.16"
    csstype: "npm:^3.1.0"
  checksum: 15bf536c131a863d03c42386a4bbc82316262129421ef70e88d1758bcf951446ef51edeff42e3b27d026015330fe73d90155fca270eb5eadd30b0290735f2c3e
  languageName: node
  linkType: hard

"vuex@npm:^3.0.1":
  version: 3.6.2
  resolution: "vuex@npm:3.6.2"
  peerDependencies:
    vue: ^2.0.0
  checksum: 726f2ff4ecf070bd4c65e98bf31910c3b57a83218be5623a35a57849075ce16ec117fc9f37cc905f9515200ad5e5a6976bdb075a5a701f9fe5c4364d5a72fc71
  languageName: node
  linkType: hard

"watchpack-chokidar2@npm:^2.0.1":
  version: 2.0.1
  resolution: "watchpack-chokidar2@npm:2.0.1"
  dependencies:
    chokidar: "npm:^2.1.8"
  checksum: 9b8d880ae2543dd4f26a69f6b7f881119494f6b772b7431027a06a5cf963e0ebc1cac91a3ef479365c358b693c65fa80a1f8297427fa11fd4c080c3d6408c372
  languageName: node
  linkType: hard

"watchpack@npm:^1.7.4":
  version: 1.7.5
  resolution: "watchpack@npm:1.7.5"
  dependencies:
    chokidar: "npm:^3.4.1"
    graceful-fs: "npm:^4.1.2"
    neo-async: "npm:^2.5.0"
    watchpack-chokidar2: "npm:^2.0.1"
  dependenciesMeta:
    chokidar:
      optional: true
    watchpack-chokidar2:
      optional: true
  checksum: 53e3b112064f5de9edbb2a14973fb3901d9697b24cc70f8531a143eaace2353a273ca25c0ba21def8d3803cfedb8f6861ca1e49e9782257e40d5b5f8f5365c86
  languageName: node
  linkType: hard

"wbuf@npm:^1.1.0, wbuf@npm:^1.7.3":
  version: 1.7.3
  resolution: "wbuf@npm:1.7.3"
  dependencies:
    minimalistic-assert: "npm:^1.0.0"
  checksum: 56edcc5ef2b3d30913ba8f1f5cccc364d180670b24d5f3f8849c1e6fb514e5c7e3a87548ae61227a82859eba6269c11393ae24ce12a2ea1ecb9b465718ddced7
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: "npm:^1.0.3"
  checksum: 5b61ca583a95e2dd85d7078400190efd452e05751a64accb8c06ce4db65d7e0b0cde9917d705e826a2e05cc2548f61efde115ffa374c3e436d04be45c889e5b4
  languageName: node
  linkType: hard

"webpack-bundle-analyzer@npm:^3.3.0":
  version: 3.9.0
  resolution: "webpack-bundle-analyzer@npm:3.9.0"
  dependencies:
    acorn: "npm:^7.1.1"
    acorn-walk: "npm:^7.1.1"
    bfj: "npm:^6.1.1"
    chalk: "npm:^2.4.1"
    commander: "npm:^2.18.0"
    ejs: "npm:^2.6.1"
    express: "npm:^4.16.3"
    filesize: "npm:^3.6.1"
    gzip-size: "npm:^5.0.0"
    lodash: "npm:^4.17.19"
    mkdirp: "npm:^0.5.1"
    opener: "npm:^1.5.1"
    ws: "npm:^6.0.0"
  bin:
    webpack-bundle-analyzer: lib/bin/analyzer.js
  checksum: 442b2e0740ad12c0a1887dcd294cc89c776bfc01301194ea5dd73ab26a7045dd3efee4eca27e4ebdf887729e20f600fe56430e42104dce6abcc4d6ca8f5c3e00
  languageName: node
  linkType: hard

"webpack-chain@npm:^4.11.0":
  version: 4.12.1
  resolution: "webpack-chain@npm:4.12.1"
  dependencies:
    deepmerge: "npm:^1.5.2"
    javascript-stringify: "npm:^1.6.0"
  checksum: 4554eb46ddf77dbdf95ca29242978dce7ea33f53dcfb8a4018c9b2c0d34c6c2cdde0e43accf38a7a5bd2287d1fffee575de968c9d69da4f8bde0c568643daff8
  languageName: node
  linkType: hard

"webpack-dev-middleware@npm:^3.7.2":
  version: 3.7.3
  resolution: "webpack-dev-middleware@npm:3.7.3"
  dependencies:
    memory-fs: "npm:^0.4.1"
    mime: "npm:^2.4.4"
    mkdirp: "npm:^0.5.1"
    range-parser: "npm:^1.2.1"
    webpack-log: "npm:^2.0.0"
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: f9bd8318c6f356d006dc99e3e46ef8870d67640e43f26cfcd2bb36c9e7eaf64015513f43498e92b532896f7fbd8f32c0710d4489fc81d7a45ea328d7e4cf3085
  languageName: node
  linkType: hard

"webpack-dev-server@npm:^3.4.1":
  version: 3.11.3
  resolution: "webpack-dev-server@npm:3.11.3"
  dependencies:
    ansi-html-community: "npm:0.0.8"
    bonjour: "npm:^3.5.0"
    chokidar: "npm:^2.1.8"
    compression: "npm:^1.7.4"
    connect-history-api-fallback: "npm:^1.6.0"
    debug: "npm:^4.1.1"
    del: "npm:^4.1.1"
    express: "npm:^4.17.1"
    html-entities: "npm:^1.3.1"
    http-proxy-middleware: "npm:0.19.1"
    import-local: "npm:^2.0.0"
    internal-ip: "npm:^4.3.0"
    ip: "npm:^1.1.5"
    is-absolute-url: "npm:^3.0.3"
    killable: "npm:^1.0.1"
    loglevel: "npm:^1.6.8"
    opn: "npm:^5.5.0"
    p-retry: "npm:^3.0.1"
    portfinder: "npm:^1.0.26"
    schema-utils: "npm:^1.0.0"
    selfsigned: "npm:^1.10.8"
    semver: "npm:^6.3.0"
    serve-index: "npm:^1.9.1"
    sockjs: "npm:^0.3.21"
    sockjs-client: "npm:^1.5.0"
    spdy: "npm:^4.0.2"
    strip-ansi: "npm:^3.0.1"
    supports-color: "npm:^6.1.0"
    url: "npm:^0.11.0"
    webpack-dev-middleware: "npm:^3.7.2"
    webpack-log: "npm:^2.0.0"
    ws: "npm:^6.2.1"
    yargs: "npm:^13.3.2"
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack-dev-server: bin/webpack-dev-server.js
  checksum: 90fe960dc28cc75b501b1fa4ad3eba358a98dbb929658725e74db12326afaf165b6bd54f0cad0381b9771f6c47c92dba573d615b491ceeec4875ffe49143a38a
  languageName: node
  linkType: hard

"webpack-log@npm:^2.0.0":
  version: 2.0.0
  resolution: "webpack-log@npm:2.0.0"
  dependencies:
    ansi-colors: "npm:^3.0.0"
    uuid: "npm:^3.3.2"
  checksum: 515b800433da1c0b5722317baaeb05fc185da5a1fde5e39d25bed0b05c13ee3a544aa13844db8590696274a3c5dc04fd5abdd39f38f8c46a4084b74ff0dc9c60
  languageName: node
  linkType: hard

"webpack-merge@npm:^4.2.1":
  version: 4.2.2
  resolution: "webpack-merge@npm:4.2.2"
  dependencies:
    lodash: "npm:^4.17.15"
  checksum: 283cb4ffe4d4ae6de23d595154868780126835ded241748da0b070c6cca6974c229493ac0b6b7160c2c92950c950c8e5edf036a192da78e32e22a9c81593ad16
  languageName: node
  linkType: hard

"webpack-sources@npm:^1.1.0, webpack-sources@npm:^1.4.0, webpack-sources@npm:^1.4.1":
  version: 1.4.3
  resolution: "webpack-sources@npm:1.4.3"
  dependencies:
    source-list-map: "npm:^2.0.0"
    source-map: "npm:~0.6.1"
  checksum: 78dafb3e1e297d3f4eb6204311e8c64d28cd028f82887ba33aaf03fffc82482d8e1fdf6de25a60f4dde621d3565f4c3b1bfb350f09add8f4e54e00279ff3db5e
  languageName: node
  linkType: hard

"webpack@npm:^4.0.0":
  version: 4.47.0
  resolution: "webpack@npm:4.47.0"
  dependencies:
    "@webassemblyjs/ast": "npm:1.9.0"
    "@webassemblyjs/helper-module-context": "npm:1.9.0"
    "@webassemblyjs/wasm-edit": "npm:1.9.0"
    "@webassemblyjs/wasm-parser": "npm:1.9.0"
    acorn: "npm:^6.4.1"
    ajv: "npm:^6.10.2"
    ajv-keywords: "npm:^3.4.1"
    chrome-trace-event: "npm:^1.0.2"
    enhanced-resolve: "npm:^4.5.0"
    eslint-scope: "npm:^4.0.3"
    json-parse-better-errors: "npm:^1.0.2"
    loader-runner: "npm:^2.4.0"
    loader-utils: "npm:^1.2.3"
    memory-fs: "npm:^0.4.1"
    micromatch: "npm:^3.1.10"
    mkdirp: "npm:^0.5.3"
    neo-async: "npm:^2.6.1"
    node-libs-browser: "npm:^2.2.1"
    schema-utils: "npm:^1.0.0"
    tapable: "npm:^1.1.3"
    terser-webpack-plugin: "npm:^1.4.3"
    watchpack: "npm:^1.7.4"
    webpack-sources: "npm:^1.4.1"
  peerDependenciesMeta:
    webpack-cli:
      optional: true
    webpack-command:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: bc90202110a341359c11ead60ea09bd5cfa51e2c93004d7e40b7c2f76208cc6717e39c9d9544825cc44958046ada762c78a8cf9848619ea450315bce98228701
  languageName: node
  linkType: hard

"websocket-driver@npm:>=0.5.1, websocket-driver@npm:^0.7.4":
  version: 0.7.4
  resolution: "websocket-driver@npm:0.7.4"
  dependencies:
    http-parser-js: "npm:>=0.5.1"
    safe-buffer: "npm:>=5.1.0"
    websocket-extensions: "npm:>=0.1.1"
  checksum: 5f09547912b27bdc57bac17b7b6527d8993aa4ac8a2d10588bb74aebaf785fdcf64fea034aae0c359b7adff2044dd66f3d03866e4685571f81b13e548f9021f1
  languageName: node
  linkType: hard

"websocket-extensions@npm:>=0.1.1":
  version: 0.1.4
  resolution: "websocket-extensions@npm:0.1.4"
  checksum: bbc8c233388a0eb8a40786ee2e30d35935cacbfe26ab188b3e020987e85d519c2009fe07cfc37b7f718b85afdba7e54654c9153e6697301f72561bfe429177e0
  languageName: node
  linkType: hard

"websocket@npm:latest":
  version: 1.0.35
  resolution: "websocket@npm:1.0.35"
  dependencies:
    bufferutil: "npm:^4.0.1"
    debug: "npm:^2.2.0"
    es5-ext: "npm:^0.10.63"
    typedarray-to-buffer: "npm:^3.1.5"
    utf-8-validate: "npm:^5.0.2"
    yaeti: "npm:^0.0.6"
  checksum: 8be9a68dc0228f18058c9010d1308479f05050af8f6d68b9dbc6baebd9ab484c15a24b2521a5d742a9d78e62ee19194c532992f1047a9b9adf8c3eedb0b1fcdc
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: aceea8ede3b08dede7dce168f3883323f7c62272b49801716e8332ff750e7ae59a511ae088840bc6874f16c1b7fd296c05c949b0e5b357bfe3c431b98c417abe
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 8dcf323c45e5c27887800df42fbe0431d0b66b1163849bb7d46b5a730ad6a96ee8bfe827d078303f825537844ebf20c02459de41239a0a9805e2fcb3cae0d471
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 3345fde20964525a04cdf7c4a96821f85f0cc198f1b2ecb4576e08096746d129eb133571998fe121c77782ac8f21cbd67745a3d35ce100d26d4e684c142ea1f2
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.1
  resolution: "which-module@npm:2.0.1"
  checksum: 087038e7992649eaffa6c7a4f3158d5b53b14cf5b6c1f0e043dccfacb1ba179d12f17545d5b85ebd94a42ce280a6fe65d0cbcab70f4fc6daad1dfae85e0e6a3e
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.19":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 702b5dc878addafe6c6300c3d0af5983b175c75fcb4f2a72dfc3dd38d93cf9e89581e4b29c854b16ea37e50a7d7fca5ae42ece5c273d8060dcd603b2404bbb3f
  languageName: node
  linkType: hard

"which@npm:^1.2.9":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    which: ./bin/which
  checksum: e945a8b6bbf6821aaaef7f6e0c309d4b615ef35699576d5489b4261da9539f70393c6b2ce700ee4321c18f914ebe5644bc4631b15466ffbaad37d83151f6af59
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"worker-farm@npm:^1.7.0":
  version: 1.7.0
  resolution: "worker-farm@npm:1.7.0"
  dependencies:
    errno: "npm:~0.1.7"
  checksum: 069a032f9198a07273a7608dc0c23d7288c1c25256b66008e1ae95838cda6fa2c7aefb3b7ba760f975c8d18120ca54eb193afb66d7237b2a05e5da12c1c961f7
  languageName: node
  linkType: hard

"wpk-reporter@npm:^0.6.2":
  version: 0.6.5
  resolution: "wpk-reporter@npm:0.6.5"
  checksum: 24edfe4d89e2d7916084d7ec6d984cde72d80cde3dc197eeca28d7fd4b827d789160885a52dd54d7c2c218f20c90743b03424eba6be71fe0558dbcbdcfb17f09
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^5.1.0":
  version: 5.1.0
  resolution: "wrap-ansi@npm:5.1.0"
  dependencies:
    ansi-styles: "npm:^3.2.0"
    string-width: "npm:^3.0.0"
    strip-ansi: "npm:^5.0.0"
  checksum: fcd0b39b7453df512f2fe8c714a1c1b147fe3e6a4b5a2e4de6cadc3af47212f335eceaffe588e98322d6345e72672137e2c0b834d8a662e73a32296c1c8216bb
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"ws@npm:^6.0.0, ws@npm:^6.2.1":
  version: 6.2.3
  resolution: "ws@npm:6.2.3"
  dependencies:
    async-limiter: "npm:~1.0.0"
  checksum: 56a35b9799993cea7ce2260197e7879f21bbbb194a967f31acbbda6f7f46ecda4365951966fb062044c95197e19fb2f053be6f65c172435455186835f494de41
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0, xtend@npm:~4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"y18n@npm:^4.0.0":
  version: 4.0.3
  resolution: "y18n@npm:4.0.3"
  checksum: 308a2efd7cc296ab2c0f3b9284fd4827be01cfeb647b3ba18230e3a416eb1bc887ac050de9f8c4fd9e7856b2e8246e05d190b53c96c5ad8d8cb56dffb6f81024
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yaeti@npm:^0.0.6":
  version: 0.0.6
  resolution: "yaeti@npm:0.0.6"
  checksum: 4e88702d8b34d7b61c1c4ec674422b835d453b8f8a6232be41e59fc98bc4d9ab6d5abd2da55bab75dfc07ae897fdc0c541f856ce3ab3b17de1630db6161aa3f6
  languageName: node
  linkType: hard

"yallist@npm:^2.1.2":
  version: 2.1.2
  resolution: "yallist@npm:2.1.2"
  checksum: 0b9e25aa00adf19e01d2bcd4b208aee2b0db643d9927131797b7af5ff69480fc80f1c3db738cbf3946f0bddf39d8f2d0a5709c644fd42d4aa3a4e6e786c087b5
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yargs-parser@npm:^13.1.2":
  version: 13.1.2
  resolution: "yargs-parser@npm:13.1.2"
  dependencies:
    camelcase: "npm:^5.0.0"
    decamelize: "npm:^1.2.0"
  checksum: aeded49d2285c5e284e48b7c69eab4a6cf1c94decfdba073125cc4054ff49da7128a3c7c840edb6b497a075e455be304e89ba4b9228be35f1ed22f4a7bba62cc
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 0685a8e58bbfb57fab6aefe03c6da904a59769bd803a722bb098bd5b0f29d274a1357762c7258fb487512811b8063fb5d2824a3415a0a4540598335b3b086c72
  languageName: node
  linkType: hard

"yargs@npm:^13.3.2":
  version: 13.3.2
  resolution: "yargs@npm:13.3.2"
  dependencies:
    cliui: "npm:^5.0.0"
    find-up: "npm:^3.0.0"
    get-caller-file: "npm:^2.0.1"
    require-directory: "npm:^2.1.1"
    require-main-filename: "npm:^2.0.0"
    set-blocking: "npm:^2.0.0"
    string-width: "npm:^3.0.0"
    which-module: "npm:^2.0.0"
    y18n: "npm:^4.0.0"
    yargs-parser: "npm:^13.1.2"
  checksum: 6612f9f0ffeee07fff4c85f153d10eba4072bf5c11e1acba96153169f9d771409dfb63253dbb0841ace719264b663cd7b18c75c0eba91af7740e76094239d386
  languageName: node
  linkType: hard

"yargs@npm:^16.0.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: "npm:^7.0.2"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.0"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^20.2.2"
  checksum: b1dbfefa679848442454b60053a6c95d62f2d2e21dd28def92b647587f415969173c6e99a0f3bab4f1b67ee8283bf735ebe3544013f09491186ba9e8a9a2b651
  languageName: node
  linkType: hard

"zrender@npm:4.3.2":
  version: 4.3.2
  resolution: "zrender@npm:4.3.2"
  checksum: 53e5bbb84e9dc1b8d2c8c5559e5b2e879e15aa774f92d48864cc5385106088c9a0e992c3b764ca5713a05e282b19504b309b72bd0a1544e6e096af925a8c6387
  languageName: node
  linkType: hard
