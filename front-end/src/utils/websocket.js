import SockJS from 'sockjs-client'
import Stomp from 'stompjs'

/**
 * WebSocket连接管理器
 * 用于实时接收批量处理进度更新
 */
class WebSocketManager {
  constructor() {
    this.stompClient = null
    this.connected = false
    this.subscriptions = new Map()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
  }

  /**
   * 连接WebSocket
   */
  connect() {
    return new Promise((resolve, reject) => {
      try {
        const socket = new SockJS(process.env.NODE_ENV === 'development' ? 'http://localhost:9080/ws' : '/ws')
        this.stompClient = Stomp.over(socket)
        
        // 禁用调试日志
        this.stompClient.debug = null
        
        this.stompClient.connect({}, 
          (frame) => {
            console.log('WebSocket连接成功:', frame)
            this.connected = true
            this.reconnectAttempts = 0
            resolve(frame)
          },
          (error) => {
            console.error('WebSocket连接失败:', error)
            this.connected = false
            this.handleReconnect()
            reject(error)
          }
        )
      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.stompClient && this.connected) {
      // 取消所有订阅
      this.subscriptions.forEach((subscription) => {
        subscription.unsubscribe()
      })
      this.subscriptions.clear()
      
      this.stompClient.disconnect(() => {
        console.log('WebSocket连接已断开')
      })
      this.connected = false
    }
  }

  /**
   * 订阅任务进度更新
   * @param {string} taskId 任务ID
   * @param {function} callback 回调函数
   */
  subscribeTaskProgress(taskId, callback) {
    if (!this.connected) {
      console.warn('WebSocket未连接，无法订阅任务进度')
      return null
    }

    const destination = `/topic/batch-progress/${taskId}`
    const subscription = this.stompClient.subscribe(destination, (message) => {
      try {
        const data = JSON.parse(message.body)
        callback(data)
      } catch (error) {
        console.error('解析进度消息失败:', error)
      }
    })

    this.subscriptions.set(taskId, subscription)
    console.log(`已订阅任务进度: ${taskId}`)
    return subscription
  }

  /**
   * 取消订阅任务进度
   * @param {string} taskId 任务ID
   */
  unsubscribeTaskProgress(taskId) {
    const subscription = this.subscriptions.get(taskId)
    if (subscription) {
      subscription.unsubscribe()
      this.subscriptions.delete(taskId)
      console.log(`已取消订阅任务进度: ${taskId}`)
    }
  }

  /**
   * 处理重连
   */
  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect().catch(() => {
          // 重连失败，继续尝试
        })
      }, this.reconnectInterval)
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数')
    }
  }

  /**
   * 检查连接状态
   */
  isConnected() {
    return this.connected && this.stompClient && this.stompClient.connected
  }
}

// 创建全局实例
const wsManager = new WebSocketManager()

export default wsManager

/**
 * 便捷方法：订阅批量处理进度
 * @param {string} taskId 任务ID
 * @param {function} onProgress 进度回调
 * @param {function} onComplete 完成回调
 * @param {function} onError 错误回调
 */
export function subscribeBatchProgress(taskId, { onProgress, onComplete, onError }) {
  // 确保WebSocket已连接
  const connectAndSubscribe = () => {
    wsManager.subscribeTaskProgress(taskId, (data) => {
      console.log('收到进度更新:', data)
      
      if (data.type === 'completion' || data.type === 'save_completion') {
        // 处理完成通知（包括保存完成）
        if (onComplete) {
          onComplete(data)
        }
      } else if (data.progress === -1) {
        // 处理错误
        if (onError) {
          onError(data)
        }
      } else {
        // 处理进度更新
        if (onProgress) {
          onProgress(data)
        }
      }
    })
  }

  if (wsManager.isConnected()) {
    connectAndSubscribe()
  } else {
    wsManager.connect()
      .then(() => {
        connectAndSubscribe()
      })
      .catch((error) => {
        console.error('连接WebSocket失败:', error)
        if (onError) {
          onError({ message: 'WebSocket连接失败' })
        }
      })
  }
}

/**
 * 取消订阅批量处理进度
 * @param {string} taskId 任务ID
 */
export function unsubscribeBatchProgress(taskId) {
  wsManager.unsubscribeTaskProgress(taskId)
}

/**
 * 初始化WebSocket连接
 */
export function initWebSocket() {
  return wsManager.connect()
}

/**
 * 关闭WebSocket连接
 */
export function closeWebSocket() {
  wsManager.disconnect()
}
