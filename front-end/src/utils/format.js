/**
 * 格式化工具函数
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @param {string} format 格式，默认 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '-'
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return '-'
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化日期时间
 * @param {string|Date} datetime 日期时间
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(datetime) {
  return formatDate(datetime, 'YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化时间
 * @param {string|Date} time 时间
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time) {
  return formatDate(time, 'HH:mm:ss')
}

/**
 * 格式化金额
 * @param {number|string} amount 金额
 * @param {number} decimals 小数位数，默认2位
 * @param {string} currency 货币符号，默认￥
 * @returns {string} 格式化后的金额字符串
 */
export function formatAmount(amount, decimals = 2, currency = '￥') {
  if (amount === null || amount === undefined || amount === '') {
    return `${currency}0.00`
  }
  
  const num = parseFloat(amount)
  if (isNaN(num)) {
    return `${currency}0.00`
  }
  
  return `${currency}${num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })}`
}

/**
 * 格式化金额（不带货币符号）
 * @param {number|string} amount 金额
 * @param {number} decimals 小数位数，默认2位
 * @returns {string} 格式化后的金额字符串
 */
export function formatAmountPlain(amount, decimals = 2) {
  if (amount === null || amount === undefined || amount === '') {
    return '0.00'
  }
  
  const num = parseFloat(amount)
  if (isNaN(num)) {
    return '0.00'
  }
  
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 格式化百分比
 * @param {number|string} value 数值
 * @param {number} decimals 小数位数，默认2位
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercentage(value, decimals = 2) {
  if (value === null || value === undefined || value === '') {
    return '0.00%'
  }
  
  const num = parseFloat(value)
  if (isNaN(num)) {
    return '0.00%'
  }
  
  return `${(num * 100).toFixed(decimals)}%`
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @param {number} decimals 小数位数，默认2位
 * @returns {string} 格式化后的文件大小字符串
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 * @param {number|string} number 数字
 * @param {number} decimals 小数位数，默认0位
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(number, decimals = 0) {
  if (number === null || number === undefined || number === '') {
    return '0'
  }
  
  const num = parseFloat(number)
  if (isNaN(num)) {
    return '0'
  }
  
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 格式化手机号
 * @param {string} phone 手机号
 * @returns {string} 格式化后的手机号
 */
export function formatPhone(phone) {
  if (!phone) return '-'
  
  const phoneStr = String(phone)
  if (phoneStr.length === 11) {
    return phoneStr.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }
  
  return phoneStr
}

/**
 * 格式化身份证号
 * @param {string} idCard 身份证号
 * @param {boolean} mask 是否脱敏，默认true
 * @returns {string} 格式化后的身份证号
 */
export function formatIdCard(idCard, mask = true) {
  if (!idCard) return '-'
  
  const idCardStr = String(idCard)
  if (idCardStr.length === 18) {
    if (mask) {
      return idCardStr.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    } else {
      return idCardStr.replace(/(\d{6})(\d{8})(\d{4})/, '$1 $2 $3')
    }
  }
  
  return idCardStr
}

/**
 * 格式化银行卡号
 * @param {string} cardNo 银行卡号
 * @param {boolean} mask 是否脱敏，默认true
 * @returns {string} 格式化后的银行卡号
 */
export function formatBankCard(cardNo, mask = true) {
  if (!cardNo) return '-'
  
  const cardStr = String(cardNo)
  if (mask && cardStr.length >= 8) {
    const start = cardStr.substring(0, 4)
    const end = cardStr.substring(cardStr.length - 4)
    const middle = '*'.repeat(cardStr.length - 8)
    return `${start}${middle}${end}`
  }
  
  // 每4位添加空格
  return cardStr.replace(/(\d{4})(?=\d)/g, '$1 ')
}

/**
 * 格式化相对时间
 * @param {string|Date} date 日期
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return '-'
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return '-'
  
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`
  } else {
    return `${Math.floor(diff / year)}年前`
  }
}

/**
 * 格式化状态文本
 * @param {string|number} status 状态值
 * @param {Object} statusMap 状态映射表
 * @returns {string} 状态文本
 */
export function formatStatus(status, statusMap = {}) {
  return statusMap[status] || status || '-'
}

/**
 * 格式化枚举值
 * @param {string|number} value 枚举值
 * @param {Array} enumList 枚举列表 [{value, label}]
 * @returns {string} 枚举标签
 */
export function formatEnum(value, enumList = []) {
  const item = enumList.find(item => item.value === value)
  return item ? item.label : value || '-'
}

/**
 * 截断文本
 * @param {string} text 文本
 * @param {number} maxLength 最大长度，默认50
 * @param {string} suffix 后缀，默认'...'
 * @returns {string} 截断后的文本
 */
export function truncateText(text, maxLength = 50, suffix = '...') {
  if (!text) return '-'
  
  const textStr = String(text)
  if (textStr.length <= maxLength) {
    return textStr
  }
  
  return textStr.substring(0, maxLength - suffix.length) + suffix
}

/**
 * 格式化JSON字符串
 * @param {Object} obj JSON对象
 * @param {number} space 缩进空格数，默认2
 * @returns {string} 格式化后的JSON字符串
 */
export function formatJSON(obj, space = 2) {
  try {
    return JSON.stringify(obj, null, space)
  } catch (error) {
    return String(obj)
  }
}

/**
 * 格式化科目代码
 * @param {string} code 科目代码
 * @returns {string} 格式化后的科目代码
 */
export function formatSubjectCode(code) {
  if (!code) return '-'
  
  const codeStr = String(code)
  // 如果是4位数字，格式化为 XXXX
  if (/^\d{4}$/.test(codeStr)) {
    return codeStr
  }
  // 如果是6位数字，格式化为 XXXX.XX
  if (/^\d{6}$/.test(codeStr)) {
    return `${codeStr.substring(0, 4)}.${codeStr.substring(4)}`
  }
  // 如果是8位数字，格式化为 XXXX.XX.XX
  if (/^\d{8}$/.test(codeStr)) {
    return `${codeStr.substring(0, 4)}.${codeStr.substring(4, 6)}.${codeStr.substring(6)}`
  }
  
  return codeStr
}

/**
 * 格式化凭证号
 * @param {string} voucherNo 凭证号
 * @returns {string} 格式化后的凭证号
 */
export function formatVoucherNo(voucherNo) {
  if (!voucherNo) return '-'
  
  const voucherStr = String(voucherNo)
  // 如果是纯数字，补零到指定位数
  if (/^\d+$/.test(voucherStr)) {
    return voucherStr.padStart(6, '0')
  }
  
  return voucherStr
}

/**
 * 格式化置信度
 * @param {number} confidence 置信度 (0-1)
 * @returns {string} 格式化后的置信度字符串
 */
export function formatConfidence(confidence) {
  if (confidence === null || confidence === undefined) {
    return '-'
  }
  
  const num = parseFloat(confidence)
  if (isNaN(num)) {
    return '-'
  }
  
  return `${Math.round(num * 100)}%`
}

/**
 * 格式化AI处理时间
 * @param {number} milliseconds 毫秒数
 * @returns {string} 格式化后的处理时间
 */
export function formatProcessingTime(milliseconds) {
  if (!milliseconds || milliseconds < 0) {
    return '-'
  }
  
  if (milliseconds < 1000) {
    return `${milliseconds}ms`
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(milliseconds / 60000)
    const seconds = Math.floor((milliseconds % 60000) / 1000)
    return `${minutes}m${seconds}s`
  }
}

export default {
  formatDate,
  formatDateTime,
  formatTime,
  formatAmount,
  formatAmountPlain,
  formatPercentage,
  formatFileSize,
  formatNumber,
  formatPhone,
  formatIdCard,
  formatBankCard,
  formatRelativeTime,
  formatStatus,
  formatEnum,
  truncateText,
  formatJSON,
  formatSubjectCode,
  formatVoucherNo,
  formatConfidence,
  formatProcessingTime
}