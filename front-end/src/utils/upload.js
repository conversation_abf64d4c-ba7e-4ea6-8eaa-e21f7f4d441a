/**
 * 文件上传工具
 * 专门处理大文件上传，包含进度监控和错误重试
 */
import axios from 'axios'
import Hey<PERSON> from 'heyui'

// 创建专门用于文件上传的axios实例
const uploadRequest = axios.create({
  timeout: 600000, // 10分钟超时，适应大文件上传
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})

// 请求拦截器
uploadRequest.interceptors.request.use(
  config => {
    // 添加必要的header，与Ajax.js保持一致
    config.headers['X-Requested-With'] = 'XMLHttpRequest'
    // 添加author header（如果需要）
    const author = localStorage.getItem('author') || 'gson'
    config.headers['author'] = author
    return config
  },
  error => {
    console.error('上传请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
uploadRequest.interceptors.response.use(
  response => {
    const res = response.data
    if (response.status === 200) {
      return res
    } else {
      HeyUI.$Message.error(res.message || '上传失败')
      return Promise.reject(new Error(res.message || '上传失败'))
    }
  },
  error => {
    console.error('上传响应错误:', error)
    
    let message = '上传失败'
    if (error.response) {
      switch (error.response.status) {
        case 413:
          message = '文件过大，请选择较小的文件'
          break
        case 408:
          message = '上传超时，请检查网络连接'
          break
        case 500:
          message = '服务器错误，请稍后重试'
          break
        default:
          message = `上传失败 (${error.response.status})`
      }
    } else if (error.request) {
      message = '网络连接超时，请检查网络'
    } else if (error.code === 'ECONNABORTED') {
      message = '上传超时，请重试'
    }

    HeyUI.$Message.error(message)
    return Promise.reject(error)
  }
)

/**
 * 批量上传文件
 * @param {FileList} files 文件列表
 * @param {string} type 上传类型
 * @param {string} taskName 任务名称
 * @param {number} receiptsPerPage 每页回单数量
 * @param {function} onProgress 进度回调函数
 * @returns {Promise} 上传结果
 */
export function batchUpload(files, type, taskName, receiptsPerPage = null, onProgress = null) {
  const formData = new FormData()
  
  // 添加文件
  for (let i = 0; i < files.length; i++) {
    formData.append('files', files[i])
  }
  
  // 添加参数
  formData.append('type', type)
  if (taskName) {
    formData.append('taskName', taskName)
  }
  // 只有当receiptsPerPage不为null时才添加参数
  if (receiptsPerPage !== null && receiptsPerPage !== undefined) {
    formData.append('receiptsPerPage', receiptsPerPage)
  }
  
  const config = {
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.lengthComputable) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percentCompleted)
      }
    }
  }
  
  return uploadRequest.post('/api/batch/upload', formData, config)
}

/**
 * 单文件上传
 * @param {File} file 文件对象
 * @param {string} folder 上传文件夹
 * @param {function} onProgress 进度回调函数
 * @returns {Promise} 上传结果
 */
export function singleUpload(file, folder = 'uploads', onProgress = null) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('folder', folder)
  
  const config = {
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.lengthComputable) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percentCompleted)
      }
    }
  }
  
  return uploadRequest.post('/upload/single', formData, config)
}

/**
 * 检查文件大小和类型
 * @param {File} file 文件对象
 * @param {number} maxSize 最大文件大小(MB)
 * @param {Array} allowedTypes 允许的文件类型
 * @returns {Object} 检查结果
 */
export function validateFile(file, maxSize = 50, allowedTypes = ['pdf', 'jpg', 'jpeg', 'png']) {
  const result = {
    valid: true,
    message: ''
  }
  
  // 检查文件大小
  const fileSizeMB = file.size / (1024 * 1024)
  if (fileSizeMB > maxSize) {
    result.valid = false
    result.message = `文件大小不能超过 ${maxSize}MB`
    return result
  }
  
  // 检查文件类型
  const fileExtension = file.name.split('.').pop().toLowerCase()
  if (!allowedTypes.includes(fileExtension)) {
    result.valid = false
    result.message = `不支持的文件类型，仅支持: ${allowedTypes.join(', ')}`
    return result
  }
  
  return result
}

/**
 * 批量验证文件
 * @param {FileList} files 文件列表
 * @param {number} maxSize 最大文件大小(MB)
 * @param {Array} allowedTypes 允许的文件类型
 * @returns {Object} 验证结果
 */
export function validateFiles(files, maxSize = 50, allowedTypes = ['pdf', 'jpg', 'jpeg', 'png']) {
  const result = {
    valid: true,
    message: '',
    invalidFiles: []
  }
  
  for (let i = 0; i < files.length; i++) {
    const fileResult = validateFile(files[i], maxSize, allowedTypes)
    if (!fileResult.valid) {
      result.valid = false
      result.invalidFiles.push({
        name: files[i].name,
        message: fileResult.message
      })
    }
  }
  
  if (!result.valid) {
    result.message = `以下文件验证失败:\n${result.invalidFiles.map(f => `${f.name}: ${f.message}`).join('\n')}`
  }
  
  return result
}

export default uploadRequest
