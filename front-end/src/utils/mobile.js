/**
 * 移动端检测和工具函数
 */

/**
 * 检测是否为移动设备
 */
export function isMobile() {
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android', 'iphone', 'ipad', 'ipod', 'blackberry',
    'windows phone', 'webos', 'opera mini'
  ]

  // 检查User Agent中的移动设备关键词
  const hasUserAgentKeyword = mobileKeywords.some(keyword => userAgent.includes(keyword))

  // 检查是否为开发者工具的移动模式（通过检查特定的User Agent字符串）
  const isDevToolsMobile = window.navigator && window.navigator.userAgentData && window.navigator.userAgentData.mobile

  // 更严格的移动设备检测：主要依赖User Agent，避免误判桌面浏览器
  const isMobileDevice = hasUserAgentKeyword || isDevToolsMobile

  console.log('移动端检测:', {
    userAgent,
    hasUserAgentKeyword,
    isDevToolsMobile,
    isMobileDevice,
    windowWidth: window.innerWidth,
    windowHeight: window.innerHeight
  })

  return isMobileDevice
}

/**
 * 检测是否为微信浏览器
 */
export function isWeChat() {
  const userAgent = navigator.userAgent.toLowerCase()
  return userAgent.includes('micromessenger')
}

/**
 * 检测是否为iOS设备
 */
export function isIOS() {
  const userAgent = navigator.userAgent.toLowerCase()
  return /iphone|ipad|ipod/.test(userAgent)
}

/**
 * 检测是否为Android设备
 */
export function isAndroid() {
  const userAgent = navigator.userAgent.toLowerCase()
  return userAgent.includes('android')
}

/**
 * 获取设备类型
 */
export function getDeviceType() {
  if (isIOS()) return 'ios'
  if (isAndroid()) return 'android'
  if (isMobile()) return 'mobile'
  return 'desktop'
}

/**
 * 设置移动端视口
 */
export function setMobileViewport() {
  const viewport = document.querySelector('meta[name="viewport"]')
  if (viewport) {
    viewport.setAttribute('content', 
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )
  } else {
    const meta = document.createElement('meta')
    meta.name = 'viewport'
    meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
    document.head.appendChild(meta)
  }
}

/**
 * 防止移动端页面滚动穿透（优化版本）
 */
export function preventScrollThrough() {
  let startY = 0

  document.addEventListener('touchstart', (e) => {
    startY = e.touches[0].clientY
  }, { passive: true })

  document.addEventListener('touchmove', (e) => {
    const currentY = e.touches[0].clientY
    const element = e.target

    // 检查是否在可滚动元素内
    const scrollableParent = findScrollableParent(element)

    // 如果找到可滚动的父元素，允许滚动
    if (scrollableParent) {
      return // 允许正常滚动
    }

    // 检查是否是body或html的滚动
    if (element === document.body || element === document.documentElement ||
        element.closest('.mobile-app-frame') || element.closest('.mobile-bill-list') ||
        element.closest('.mobile-bill-add')) {
      return // 允许页面级别的滚动
    }

    // 只有在特定情况下才阻止滚动（比如模态框背景）
    if (element.closest('.modal-backdrop') || element.closest('.overlay')) {
      e.preventDefault()
    }
  }, { passive: false })
}

/**
 * 查找可滚动的父元素
 */
function findScrollableParent(element) {
  if (!element || element === document.body) {
    return null
  }
  
  const { overflow, overflowY } = window.getComputedStyle(element)
  const isScrollable = /(auto|scroll)/.test(overflow + overflowY)
  
  if (isScrollable && element.scrollHeight > element.clientHeight) {
    return element
  }
  
  return findScrollableParent(element.parentElement)
}

/**
 * 移动端触摸反馈
 */
export function addTouchFeedback(selector = '.mobile-btn, .mobile-list-item') {
  document.addEventListener('touchstart', (e) => {
    const element = e.target.closest(selector)
    if (element) {
      element.style.transform = 'scale(0.98)'
      element.style.transition = 'transform 0.1s ease'
    }
  })
  
  document.addEventListener('touchend', (e) => {
    const element = e.target.closest(selector)
    if (element) {
      setTimeout(() => {
        element.style.transform = ''
        element.style.transition = ''
      }, 100)
    }
  })
}

/**
 * 移动端安全区域适配
 */
export function setSafeArea() {
  const root = document.documentElement
  
  // 检测是否支持安全区域
  if (CSS.supports('padding-top: env(safe-area-inset-top)')) {
    root.style.setProperty('--safe-area-top', 'env(safe-area-inset-top)')
    root.style.setProperty('--safe-area-bottom', 'env(safe-area-inset-bottom)')
    root.style.setProperty('--safe-area-left', 'env(safe-area-inset-left)')
    root.style.setProperty('--safe-area-right', 'env(safe-area-inset-right)')
  } else {
    // 降级处理
    root.style.setProperty('--safe-area-top', '0px')
    root.style.setProperty('--safe-area-bottom', '0px')
    root.style.setProperty('--safe-area-left', '0px')
    root.style.setProperty('--safe-area-right', '0px')
  }
}

/**
 * 移动端键盘适配
 */
export function handleKeyboard() {
  let initialViewportHeight = window.innerHeight
  
  window.addEventListener('resize', () => {
    const currentViewportHeight = window.innerHeight
    const heightDifference = initialViewportHeight - currentViewportHeight
    
    // 如果高度减少超过150px，认为是键盘弹出
    if (heightDifference > 150) {
      document.body.classList.add('keyboard-open')
      document.documentElement.style.setProperty('--keyboard-height', `${heightDifference}px`)
    } else {
      document.body.classList.remove('keyboard-open')
      document.documentElement.style.setProperty('--keyboard-height', '0px')
    }
  })
}

/**
 * 移动端图片懒加载
 */
export function lazyLoadImages() {
  const images = document.querySelectorAll('img[data-src]')
  
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target
          img.src = img.dataset.src
          img.removeAttribute('data-src')
          imageObserver.unobserve(img)
        }
      })
    })
    
    images.forEach(img => imageObserver.observe(img))
  } else {
    // 降级处理
    images.forEach(img => {
      img.src = img.dataset.src
      img.removeAttribute('data-src')
    })
  }
}

/**
 * 移动端下拉刷新
 */
export function addPullToRefresh(callback, threshold = 60) {
  let startY = 0
  let currentY = 0
  let pulling = false
  let refreshing = false
  
  const container = document.body
  
  container.addEventListener('touchstart', (e) => {
    if (container.scrollTop === 0 && !refreshing) {
      startY = e.touches[0].clientY
      pulling = true
    }
  }, { passive: true })
  
  container.addEventListener('touchmove', (e) => {
    if (!pulling || refreshing) return
    
    currentY = e.touches[0].clientY
    const diff = currentY - startY
    
    if (diff > 0) {
      e.preventDefault()
      
      // 显示下拉提示
      const indicator = document.querySelector('.mobile-refresh-indicator')
      if (indicator) {
        indicator.style.transform = `translateY(${Math.min(diff, threshold)}px)`
        
        if (diff >= threshold) {
          indicator.classList.add('ready')
        } else {
          indicator.classList.remove('ready')
        }
      }
    }
  }, { passive: false })
  
  container.addEventListener('touchend', () => {
    if (!pulling || refreshing) return
    
    const diff = currentY - startY
    
    if (diff >= threshold) {
      refreshing = true
      callback().finally(() => {
        refreshing = false
        pulling = false
        
        const indicator = document.querySelector('.mobile-refresh-indicator')
        if (indicator) {
          indicator.style.transform = ''
          indicator.classList.remove('ready')
        }
      })
    } else {
      const indicator = document.querySelector('.mobile-refresh-indicator')
      if (indicator) {
        indicator.style.transform = ''
        indicator.classList.remove('ready')
      }
    }
    
    pulling = false
  }, { passive: true })
}

/**
 * 移动端初始化
 */
export function initMobile() {
  if (isMobile()) {
    setMobileViewport()
    setSafeArea()
    handleKeyboard()
    addTouchFeedback()
    preventScrollThrough()
    
    // 添加移动端标识类
    document.body.classList.add('mobile-device')
    document.body.classList.add(`device-${getDeviceType()}`)
    
    if (isWeChat()) {
      document.body.classList.add('wechat-browser')
    }
  }
}

export default {
  isMobile,
  isWeChat,
  isIOS,
  isAndroid,
  getDeviceType,
  setMobileViewport,
  preventScrollThrough,
  addTouchFeedback,
  setSafeArea,
  handleKeyboard,
  lazyLoadImages,
  addPullToRefresh,
  initMobile
}
