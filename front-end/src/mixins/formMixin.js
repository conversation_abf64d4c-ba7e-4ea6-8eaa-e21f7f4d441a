import moment from 'moment'
import { mapState } from 'vuex'

export default {
  data() {
    return {
      saving: false,
      loading: false
    }
  },
  computed: {
    ...mapState(['User']),
    isEdit() {
      return !!this.form.id
    }
  },
  methods: {
    // 通用保存方法
    async saveForm(apiService, successRoute, beforeSave = null) {
      const validResult = this.$refs.form.valid()
      if (!validResult.result) {
        this.$Message.error('请检查表单数据')
        return
      }
      
      this.saving = true
      
      try {
        // 执行保存前的处理
        if (beforeSave && typeof beforeSave === 'function') {
          beforeSave()
        }
        
        const apiCall = this.form.id 
          ? apiService.update(this.form.id, this.form)
          : apiService.save(this.form)
          
        await apiCall
        this.$Message('保存成功')
        
        if (successRoute) {
          this.$router.push(successRoute)
        }
      } catch (error) {
        console.error('保存失败:', error)

        // 提取更具体的错误信息
        let errorMessage = '保存失败，请检查数据'

        if (error.response && error.response.data) {
          const responseData = error.response.data

          // 检查是否是重复数据错误
          if (responseData.msg && (
            responseData.msg.includes('重复') ||
            responseData.msg.includes('已存在') ||
            responseData.msg.includes('duplicate')
          )) {
            errorMessage = responseData.msg
          } else if (responseData.message && (
            responseData.message.includes('重复') ||
            responseData.message.includes('已存在') ||
            responseData.message.includes('duplicate')
          )) {
            errorMessage = responseData.message
          } else if (responseData.msg) {
            errorMessage = responseData.msg
          } else if (responseData.message) {
            errorMessage = responseData.message
          }
        } else if (error.message) {
          errorMessage = error.message
        }

        // 显示友好的错误提示
        this.$Message.error(errorMessage)
      } finally {
        this.saving = false
      }
    },
    
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
      if (!date) return null; // 添加一个空值检查
      // 检查是否是 'YYYY年MM月DD日' 格式
      if (typeof date === 'string' && date.includes('年') && date.includes('月') && date.includes('日')) {
        return moment(date, 'YYYY年MM月DD日').format(format);
      }
      // 对于其他情况（如已经是Date对象、ISO格式字符串或时间戳），保持原有逻辑
      return moment(date).format(format);
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    }
  }
}