import Vue from 'vue';
import Router from 'vue-router';
import Home from './views/Home.vue';

Vue.use(Router);

const router = new Router({
	mode: 'history',
	base: process.env.BASE_URL,
	routes: [
		{
			path: '/',
			name: 'Home',
			component: Home,
			meta: {
				title: '财税智能体'
			}
		},
		{
			path: '/voucher',
			name: 'Voucher',
			component: () => import('./views/voucher/Index'),
			meta: {
				title: '凭证列表'
			}
		},
		{
			path: '/voucher/form/:voucherId(\\d+)?',
			name: 'VoucherForm',
			component: () => import('./views/voucher/VoucherForm'),
			props: true,
			meta: {
				title: '凭证信息'
			}
		},
		{
			path: '/check-out',
			name: 'CheckOut',
			component: () => import('./views/checkout/Index'),
			children: [
				{
					path: 'list',
					name: 'CheckList',
					component: () => import('./views/checkout/CheckList'),
					meta: {
						title: '结账'
					}
				},
				{
					path: 'un-check',
					name: 'UnCheckOut',
					component: () => import('./views/checkout/UnCheckOut'),
					meta: {
						title: '反结账'
					}
				}, {
					path: 'carry-forward/:checkYear(\\d+)/:checkMonth(\\d+)',
					name: 'CarryForward',
					props: true,
					component: () => import('./views/checkout/CarryForward'),
					meta: {
						title: '月末结转'
					}
				}, {
					path: 'check/:checkYear(\\d+)/:checkMonth(\\d+)',
					name: 'Check',
					props: true,
					component: () => import('./views/checkout/Check'),
					meta: {
						title: '检查'
					}
				}
			]
		},
		{
			path: '/account',
			name: 'Account',
			component: () => import('./views/setting/Account'),
			meta: {
				title: '账套'
			}
		},
		{
			path: '/subject',
			name: 'Subject',
			component: () => import('./views/setting/Subject'),
			meta: {
				title: '科目'
			}
		},
		{
			path: '/ai-accounting',
			name: 'AiAccountingPanel',
			component: () => import('./components/ai-accounting/AiAccountingPanel'),
			meta: {
				title: 'AI智能助手'
			}
		},
		{
			path: '/ai-dashboard',
			name: 'AiDashboard',
			component: () => import('./views/ai/AiDashboard'),
			meta: {
				title: 'AI智能助手总览'
			}
		},
		{
			path: '/ai-merge-engine',
			name: 'AiMergeEngine',
			component: () => import('./views/ai/AiMergeEngine'),
			meta: {
				title: 'AI智能归并'
			}
		},
		{
			path: '/ai-relation-engine',
			name: 'AiRelationEngine',
			component: () => import('./views/ai/AiRelationEngine'),
			meta: {
				title: 'AI智能关联'
			}
		},
		{
			path: '/ai-settings',
			name: 'AiSettings',
			component: () => import('./views/ai/AiSettings'),
			meta: {
				title: 'AI配置管理'
			}
		},
		{
			path: '/ai-config-test',
			name: 'AiConfigTest',
			component: () => import('./views/ai/AiConfigTest'),
			meta: {
				title: 'AI配置测试'
			}
		},
		{
			path: '/ai-config-simple',
			name: 'AiConfigSimple',
			component: () => import('./views/ai/AiConfigSimple'),
			meta: {
				title: 'AI配置(简化版)'
			}
		},
		// AI智能处理页面已删除，功能已整合到关联管理模块
		{
			path: '/ai/subject-enhancement',
			name: 'SubjectAiEnhancement',
			component: () => import('./views/ai/AiVoucherGenerator'),
			meta: {
				title: '科目AI增强'
			}
		},
		// 手机端路由
		{
			path: '/mobile',
			name: 'MobileIndex',
			component: () => import('./views/mobile/MobileIndex'),
			meta: {
				title: '财税智能体',
				mobile: true,
				requiredRoles: ['Manager', 'Director', 'Making', 'BillOnly']
			}
		},
		{
			path: '/mobile/login',
			name: 'MobileLogin',
			component: () => import('./views/mobile/MobileLogin'),
			meta: {
				title: '手机端登录',
				mobile: true,
				public: true
			}
		},
		{
			path: '/mobile/bills',
			name: 'MobileBillList',
			component: () => import('./views/mobile/BillList'),
			meta: {
				title: '我的票据',
				mobile: true,
				requiredRoles: ['Manager', 'Director', 'Making', 'BillOnly']
			}
		},
		{
			path: '/mobile/bill/add',
			name: 'MobileBillAdd',
			component: () => import('./views/mobile/BillAdd'),
			meta: {
				title: '新增票据',
				mobile: true,
				requiredRoles: ['Manager', 'Director', 'Making', 'BillOnly']
			}
		},

		{
			path: '/ai/analysis-history',
			name: 'AiAnalysisHistory',
			component: () => import('./components/ai-accounting/AiStatisticsPanel'),
			meta: {
				title: '科目AI分析历史'
			}
		},
		{
			path: '/initial',
			name: 'Initial',
			component: () => import('./views/setting/Initial'),
			meta: {
				title: '期初'
			}
		},
		{
			path: '/voucher-word',
			name: 'VoucherWord',
			component: () => import('./views/setting/VoucherWord'),
			meta: {
				title: '凭证字'
			}
		},
		{
			path: '/currency',
			name: 'Currency',
			component: () => import('./views/setting/Currency'),
			meta: {
				title: '币别'
			}
		},
		{
			path: '/assisting-accounting',
			name: 'AssistingAccounting',
			component: () => import('./views/setting/AssistingAccounting'),
			meta: {
				title: '辅助核算'
			},
			children: [
				{
					path: 'accounting-category',
					name: 'AccountingCategory',
					component: () => import('./views/setting/AssistingAccounting/AccountingCategory'),
					meta: {
						title: '辅助核算类别'
					}
				},
				{
					path: 'custom/:id(\\d+)',
					name: 'CategoryCustom',
					component: () => import('./views/setting/AssistingAccounting/CategoryCustom'),
					props: true,
					meta: {
						title: '自定类别'
					}
				}
			]
		},
		{
			path: '/template',
			component: () => import('./views/setting/template/Index'),
			meta: {
				title: '模板管理'
			},
			children: [{
				path: 'manager',
				name: 'TemplateManager',
				component: () => import('./views/setting/template/TemplateManager'),
				meta: {
					title: '模板管理'
				}
			}, {
				path: 'design/:templateId(\\d+)?',
				name: 'TemplateDesign',
				component: () => import('./views/setting/template/TemplateDesign'),
				props: true,
				meta: {
					title: '模板设计'
				}
			}]
		},
		{
			path: '/field-mapping-template',
			name: 'FieldMappingTemplate',
			component: () => import('./views/setting/FieldMappingTemplate'),
			meta: {
				title: '单据模版'
			}
		},
		{
			path: '/permission-setting',
			name: 'PermissionSetting',
			component: () => import('./views/setting/PermissionSetting'),
			meta: {
				title: '权限设置'
			}
		},
		{
			path: '/personal',
			name: 'Personal',
			component: () => import('./views/personal/Index'),
			meta: {
				title: '个人设置'
			},
			children: [
				{
					path: 'personal-setting',
					name: 'PersonalSetting',
					component: () => import('./views/personal/PersonalSetting'),
					meta: {
						title: '个人设置'
					}
				},
				{
					path: 'change-password',
					name: 'ChangePassword',
					component: () => import('./views/personal/ChangePassword'),
					meta: {
						title: '修改密码'
					}
				},
				{
					path: 'change-phone-number',
					name: 'ChangePhoneNumber',
					component: () => import('./views/personal/ChangePhoneNumber'),
					meta: {
						title: '修改手机'
					}
				},
				{
					path: 'binding-webchat',
					name: 'BindingWebchat',
					component: () => import('./views/personal/BindingWebchat'),
					meta: {
						title: '绑定微信'
					}
				}
			]
		},
		{
			path: '/account-book/detailed',
			name: 'DetailedAccounts',
			component: () => import('./views/accountbook/DetailedAccounts'),
			meta: {
				title: '明细账'
			}
		},
		{
			path: '/account-book/general-ledger',
			name: 'GeneralLedger',
			component: () => import('./views/accountbook/GeneralLedger'),
			meta: {
				title: '总账'
			}
		},
		{
			path: '/account-book/subject-balance',
			name: 'SubjectBalance',
			component: () => import('./views/accountbook/SubjectBalance'),
			meta: {
				title: '科目余额'
			}
		},
		{
			path: '/account-book/subject-summary',
			name: 'SubjectSummary',
			component: () => import('./views/accountbook/SubjectSummary'),
			meta: {
				title: '科目汇总'
			}
		},
		{
			path: '/account-book/auxiliary-accounting-balance',
			name: 'AuxiliaryAccountingBalance',
			component: () => import('./views/accountbook/AuxiliaryAccountingBalance'),
			meta: {
				title: '辅助核算余额'
			}
		},
		{
			path: '/account-book/auxiliary-accounting-detail',
			name: 'AuxiliaryAccountingDetail',
			component: () => import('./views/accountbook/AuxiliaryAccountingDetail'),
			meta: {
				title: '辅助核算明细账'
			}
		},
		{
			path: '/report',
			name: 'ReportList',
			component: () => import('./views/report/ReportList'),
			meta: {
				title: '报表'
			}
		},
		{
			path: '/report/view/:reportId(\\d+)',
			name: 'ReportView',
			props: true,
			component: () => import('./views/report/ReportView'),
			meta: {
				title: '报表数据'
			}
		},
		{
			path: '/report/template',
			name: 'ReportTemplate',
			component: () => import('./views/report/template/TemplateList'),
			meta: {
				title: '报表模板'
			}
		},
		{
			path: '/report/template/form/:templateId(\\d+)',
			name: 'TemplateForm',
			props: true,
			component: () => import('./views/report/template/TemplateForm'),
			meta: {
				title: '模板编辑'
			}
		},
		// 在现有路由配置中添加以下路由
		{
		  path: '/bills/bank-receipts',
		  name: 'BankReceiptsList',
		  component: () => import('./views/bills/BankReceiptsList'),
		  meta: {
		    title: '银行回单列表'
		  }
		},
		{
		  path: '/bills/bank-receipts/form/:bankReceiptsId(\\d+)?',
		  name: 'BankReceiptsForm',
		  component: () => import('./views/bills/BankReceiptsForm'),
		  props: true,
		  meta: {
		    title: '银行回单信息'
		  }
		},
		{
			path: '/bills/bill-list',
			name: 'BillList',
			component: () => import('./views/bills/BillList')
			},
			{
			path: '/bills/bill-form/:billId?',
			name: 'BillForm',
			component: () => import('./views/bills/BillForm')
		},
		{
			path: '/bills/batch-import',
			name: 'BatchImport',
			component: () => import('./views/bills/BatchImport'),
			meta: {
				title: '批量导入'
			}
		},
		{
			path: '/batch/progress/:taskId',
			name: 'BatchProgress',
			component: () => import('./views/batch/BatchProgress'),
			meta: {
				title: '批量处理进度'
			}
		},
		{
			path: '/batch/tasks',
			name: 'BatchTaskList',
			component: () => import('./views/batch/BatchTaskList'),
			meta: {
				title: '批量处理记录'
			}
		},
		{
			path: '/batch/preview/:taskId',
			name: 'BatchPreview',
			component: () => import('./views/batch/BatchPreviewPage'),
			props: true,
			meta: {
				title: '批量预览'
			}
		},
		{
			path: '/batch/results/:taskId',
			name: 'BatchResults',
			component: () => import('./views/batch/BatchResultsPage'),
			props: true,
			meta: {
				title: '批量处理结果'
			}
		},
		{
			path: '/merge-management',
			name: 'MergeManagement',
			component: () => import('./views/merge/MergeManagement'),
			meta: {
				title: '归并关联管理'
			}
		},
		{
			path: '/merge-rule-management',
			name: 'MergeRuleManagement',
			component: () => import('./views/merge/MergeRuleManagement'),
			meta: {
				title: '归并规则管理'
			}
		},
		{
			path: '/unified-query',
			name: 'UnifiedQuery',
			component: () => import('./views/merge/UnifiedQuery'),
			meta: {
				title: '统一查询'
			}
		},
		{
			path: '/merge-demo',
			name: 'MergeDemo',
			component: () => import('./views/merge/FunctionDemo'),
			meta: {
				title: '功能演示'
			}
		},
		{
			path: '/function-demo',
			name: 'FunctionDemo',
			component: () => import('./views/merge/FunctionDemo'),
			meta: {
				title: '功能演示'
			}
		},
		{
			path: '/relation-management',
			name: 'RelationManagement',
			component: () => import('./views/merge/RelationManagement'),
			meta: {
				title: '关联管理'
			}
		},

	]
});

// 路由守卫 - 检查移动端权限
router.beforeEach((to, from, next) => {
	console.log('Router Guard: 检查路由', to.path, to.meta);
	
	// 检查是否是移动端路由
	if (to.meta && to.meta.mobile) {
		console.log('Router Guard: 这是移动端路由');
		
		// 如果是公开页面（如登录页），直接放行
		if (to.meta.public) {
			console.log('Router Guard: 公开页面，直接放行');
			next();
			return;
		}
		
		// 检查用户是否登录
		const user = JSON.parse(sessionStorage.getItem('user') || 'null');
		console.log('Router Guard: 用户信息', user);
		
		if (!user) {
			console.log('Router Guard: 用户未登录，跳转到登录页');
			// 未登录，跳转到移动端登录页
			next('/mobile/login');
			return;
		}
		
		// 检查用户权限
		if (to.meta.requiredRoles && to.meta.requiredRoles.length > 0) {
			const userRole = user.role;
			console.log('Router Guard: 用户角色', userRole, '需要角色', to.meta.requiredRoles);
			
			if (!userRole || !to.meta.requiredRoles.includes(userRole)) {
				console.log('Router Guard: 权限不足');
				// 权限不足，跳转到移动端首页或显示错误
				alert('权限不足，无法访问该页面');
				next('/mobile');
				return;
			}
		}
		
		console.log('Router Guard: 权限检查通过');
	}
	
	next();
});

export default router;
