/* eslint-disable */
/*!
 * 51LA Analysis Javascript Software Development Kit
 * js-sdk-perf v1.7.07
 * Copyright © 2016-2021 51.la All Rights Reserved
 */
function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function o(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&c(e,t)}function a(e){return a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},a(e)}function c(e,t){return c=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},c(e,t)}function s(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function u(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=a(e);if(t){var o=a(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return s(this,n)}}function l(e){throw new TypeError('"'+e+'" is read-only')}function f(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var p=document.addEventListener?function(e,t,n){e.addEventListener(t,n,!0)}:document.attachEvent?function(e,t,n){e.attachEvent("on"+t,n)}:function(e,t,n){e["on"+t]=n};function h(e,t,n){for(var r=t.split(" "),o=0,i=r.length;o<i;o++)p(e,r[o],n)}var v=function(e){var t=e.split("/");return t=t[2]?t[2]:""},g=function(){return location&&location.host||""};var m=function(e,t){var n=Object.prototype.toString.call(e).substring(8).replace("]","");return t?n===t:n},y=function(){var e="";try{e=window.top.document.referrer}catch(t){if(window.parent)try{e=window.parent.document.referrer}catch(t){e=""}}return""===e&&(e=document.referrer),e},w=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},S={now:function(){return(new Date).valueOf()},delay:function(e,t){return-1===t?(e(),null):setTimeout(e,t||0)},getQuerys:function(e){if(!e)return"";var t={},n=[],r="",o="";try{var i=[];if(e.indexOf("?")>=0&&(i=e.substring(e.indexOf("?")+1,e.length).split("&")),i.length>0)for(var a in i)r=(n=i[a].split("="))[0],o=n[1],t[r]=o}catch(e){t={}}return t},isRobot:function(){var e=["nuhk","googlebot/","googlebot-image","yammybot","openbot","slurp","msnbot","ask jeeves/teoma","ia_archiver","baiduspider","bingbot/","adsbot"];if(!navigator||"string"!=typeof navigator.userAgent)return!1;try{for(var t=navigator.userAgent.toLowerCase(),n=0;n<e.length;n++){var r=e[n];if(t.lastIndexOf(r)>=0)return!0}}catch(e){}return!1},getCurDomain:g,getDomainByUrl:v,checkCurDomainUrl:function(e){if(v(e)===g())return!0;var t=function(){try{var e="mh_".concat(Math.random()),t=new RegExp("(^|;)\\s*".concat(e,"=12345")),n=new Date(0),r=document.domain.split("."),o=[];for(o.unshift(r.pop());r.length;){o.unshift(r.pop());var i=o.join("."),a="".concat(e,"=",12345,";domain=.").concat(i);if(document.cookie=a,t.test(document.cookie))return document.cookie="".concat(a,";expires=").concat(n),i}}catch(e){return null}}();return!!(t&&v(e).indexOf(t)>-1)||!/http[s]{0,1}:\/\/([\w.]+\/?)\S*/.test(e)},verifyConfig:function(e){if("sample"in e){var t=e.sample,n=t;t&&/^\d+(\.\d+)?%$/.test(t)&&(n=parseInt(100/parseFloat(t))),0<n&&1>n&&(n=parseInt(1/n)),n>=1&&n<=100?e.sample=n:delete e.sample}return e},typeCheck:m,needReport:function(e){return Math.random()<(e||1)},filterByRule:function e(t,n){if(!t)return"";if(!n)return t;var r=m(n);return"Function"===r?function(e,t,n){if("function"!=typeof e)return n;try{return e.apply(void 0,t)}catch(e){return n}}(n,[t],t):"Array"===r?(function(e,t){var n=0,r=e.length;if(m(e,"Array"))for(;n<r&&!1!==t.call(e[n],e[n],n);n++);else for(n in e)if(!1===t.call(e[n],e[n],n))break}(n,(function(n){t=e(t,n)})),t):"Object"===r?t.replace(n.rule,n.target||""):t.replace(n,"")},getRef:y,extend:w,filterPolyfill:function(e){return"function"!=typeof Array.prototype.filter?(Array.prototype.filter=function(e,t){var n=[];if("function"==typeof e)for(var r=0,o=this.length;r<o;r++)e.call(t,this[r],r,this)&&n.push(this[r]);return n},e):e},supportQuerySelector:function(){if(!document.querySelector){r=document.createStyleSheet(),o=function(e,t){var n,o=document.all,i=o.length,a=[];for(console.log("selector is ",e),r.addRule(e,"foo:bar"),n=0;n<i&&!("bar"===o[n].currentStyle.foo&&(a.push(o[n]),a.length>t));n+=1);return r.removeRule(0),a},document.querySelectorAll||document.querySelector||(document.querySelectorAll=function(e){return o(e,1/0)},document.querySelector=function(e){return o(e,1)[0]||null});for(var e=document.all,t=e.length,n=0;n<t;n+=1)!e[n].querySelector&&e[n].id&&(e[n].querySelector=function(e){return document.querySelector("#"+this.id+" "+e)})}var r,o},detectIE:function(){var e,t,n,r;return r=window.navigator.userAgent,window.ActiveXObject&&(t=r.indexOf("MSIE "))>0?parseInt(r.substring(t+5,r.indexOf(".",t)),10):r.indexOf("Trident/")>0?(n=r.indexOf("rv:"),parseInt(r.substring(n+3,r.indexOf(".",n)),10)):(e=r.indexOf("Edge/"))>0&&parseInt(r.substring(e+5,r.indexOf(".",e)),10)},guid:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})).toUpperCase()},noUint8Array:function(){return"undefined"==typeof Uint8Array||null===Uint8Array},ieVersion:function(){var e=navigator.userAgent;return!!(e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1)&&(new RegExp("MSIE (\\d+\\.\\d+);").test(e),parseFloat(RegExp.$1)<10||!("placeholder"in document.createElement("input"))||void 0)},getConnectionType:function(e){var t=e.connection||e.mozConnection||e.webkitConnection||e.oConnection;return/Android|webOS|iPhone|iPod|BlackBerry/i.test(e.userAgent)?t&&(t.effectiveType||t.type)?t.effectiveType||t.type:"wifi":t&&(t.effectiveType||t.type)?t.effectiveType||t.type:"unknown"},registerHistoryHandler:function(e){var t,n,r="";return t=window.history.pushState,n=window.history.replaceState,null!=t&&(window.history.pushState=function(){return r=window.location.toString(),t.apply(window.history,arguments),setTimeout((function(){return e()}),0)}),null!=n&&(window.history.replaceState=function(){return r=window.location.toString(),n.apply(window.history,arguments),setTimeout((function(){return e()}),0)}),null!=t&&(r=y(),"function"==typeof Object.defineProperty&&Object.defineProperty(document,"referrer",{get:function(){return r},configurable:!0}),h(window,"popstate",e)),h(window,"hashchange",e)}};function b(e,t,n,r){n=n||!1;var o,i,a=[];t=!!t&&JSON.stringify(t)||t;var c=S.detectIE()||NaN,s=[];if(S.detectIE()&&"string"==typeof t)for(var u=0;u<JSON.parse(t).length;u++){var l=JSON.parse(t)[u],f={};for(var d in l)f[d]=decodeURIComponent(encodeURIComponent(l[d]));s.push(JSON.parse(JSON.stringify(f)))}function p(e,t){return e.onload=null,e.onerror=null,e.onabort=null,t&&t()}function h(e,t){var n;return(n=document.createElement("img")).width=1,n.height=1,n.onload=function(){return p(n,t)},n.onerror=n.onabort=function(){return p(n)},n.src=e}return window.XMLHttpRequest&&7!==c?"withCredentials"in(o=new XMLHttpRequest)?(o.open("POST",e,!n),o.setRequestHeader("content-type","application/json;charset=UTF-8"),o.withCredentials=!0,o.onreadystatechange=function(){return n&&2===o.readyState&&o.abort(),4===o.readyState&&function(e){var t;return-1!==(t=a.indexOf(e))?a.splice(t,1):void 0}(o)&&200===o.status&&r&&r()},o.send(t),a.push(o)):"undefined"!=typeof XDomainRequest&&(n?function(t){for(var n=0;n<t.length;n++){var o=[],a={};for(var c in t[n])a[c]=decodeURIComponent(encodeURIComponent(t[n][c]));return o.push(JSON.parse(JSON.stringify(a))),"http:"===window.location.protocol&&(i=e.replace("https://","http://")+(e.indexOf("?")<0?"?":"&")+"data=".concat(encodeURIComponent(JSON.stringify(o)))),i.length<=2036&&h(i,r)}}(s):(o=new XDomainRequest,document.location.protocol,o.open("POST",e),o.onload=function(){return r&&r()},o.onerror=function(e){return{}},o.onprogress=function(){return{}},o.ontimeout=function(){return{}},o.send(t))):("http:"===window.location.protocol&&(i=e.replace("https://","http://")+(e.indexOf("?")<0?"?":"&")+"data=".concat(encodeURIComponent(JSON.stringify(s)))),i.length<=2036&&h(i,r))}var E={isStop:!0,queues:[],count:0,limit:100,add:function(e){this.count++,this.count>this.limit||this.queues.push(e)},fire:function(){this.queues&&0!==this.queues.length?(this.isStop=!1,function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(S.detectIE()||"function"!=typeof window.navigator.sendBeacon)b(e,t,n);else{window.navigator.sendBeacon("".concat(e),JSON.stringify(t))||!1||b(e,t,n)}}("//collect-v6.51.la/health/collect",this.queues),this.queues=[],this.fire()):this.isStop=!0}},R=function(){return"".concat(S.guid(),"-").concat(Math.random().toString(36).substr(-8))},x={value:R(),renew:function(){this.value=R()}},k=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function O(e){return"string"==typeof e&&k.test(e)}for(var L=[],T=0;T<256;++T)L.push((T+256).toString(16).substr(1));function A(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:case 3:return t^n^r;case 2:return t&n^t&r^n&r}}function U(e,t){return e<<t|e>>>32-t}var C=function(e,t,n){function r(e,r,o,i){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}(e)),"string"==typeof r&&(r=function(e){if(!O(e))throw TypeError("Invalid UUID");var t,n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n}(r)),16!==r.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var a=new Uint8Array(16+e.length);if(a.set(r),a.set(e,r.length),(a=n(a))[6]=15&a[6]|t,a[8]=63&a[8]|128,o){i=i||0;for(var c=0;c<16;++c)o[i+c]=a[c];return o}return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(L[e[t+0]]+L[e[t+1]]+L[e[t+2]]+L[e[t+3]]+"-"+L[e[t+4]]+L[e[t+5]]+"-"+L[e[t+6]]+L[e[t+7]]+"-"+L[e[t+8]]+L[e[t+9]]+"-"+L[e[t+10]]+L[e[t+11]]+L[e[t+12]]+L[e[t+13]]+L[e[t+14]]+L[e[t+15]]).toLowerCase();if(!O(n))throw TypeError("Stringified UUID is invalid");return n}(a)}try{r.name=e}catch(e){}return r.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",r.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",r}("v5",80,(function(e){var t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof e){var r=unescape(encodeURIComponent(e));e=[];for(var o=0;o<r.length;++o)e.push(r.charCodeAt(o))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var i=e.length/4+2,a=Math.ceil(i/16),c=new Array(a),s=0;s<a;++s){for(var u=new Uint32Array(16),l=0;l<16;++l)u[l]=e[64*s+4*l]<<24|e[64*s+4*l+1]<<16|e[64*s+4*l+2]<<8|e[64*s+4*l+3];c[s]=u}c[a-1][14]=8*(e.length-1)/Math.pow(2,32),c[a-1][14]=Math.floor(c[a-1][14]),c[a-1][15]=8*(e.length-1)&4294967295;for(var f=0;f<a;++f){for(var d=new Uint32Array(80),p=0;p<16;++p)d[p]=c[f][p];for(var h=16;h<80;++h)d[h]=U(d[h-3]^d[h-8]^d[h-14]^d[h-16],1);for(var v=n[0],g=n[1],m=n[2],y=n[3],w=n[4],S=0;S<80;++S){var b=Math.floor(S/20),E=U(v,5)+A(b,g,m,y)+w+t[b]+d[S]>>>0;w=y,y=m,m=U(g,30)>>>0,g=v,v=E}n[0]=n[0]+v>>>0,n[1]=n[1]+g>>>0,n[2]=n[2]+m>>>0,n[3]=n[3]+y>>>0,n[4]=n[4]+w>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]})),I=function(e){return decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(e).replace(/[-.+*]/g,"\\$&")+"\\s*\\=s*([^;]*).*$)|^.*$"),"$1"))||null},M=function(e,t,n,r,o,i){if(!e||/^(?:expires|max-age|path|domain|secure)$/i.test(e))return!1;var a="";if(n)switch(n.constructor){case Number:a=n===1/0?"; expires=Fri, 31 Dec 9999 23:59:59 GMT":"; max-age="+n;break;case String:a="; expires="+n;break;case Date:a="; expires="+n.toUTCString()}return document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+a+(o?"; domain="+o:"")+(r?"; path="+r:"")+(i?"; secure":""),!0},j=function(e,t){var n=window.navigator,r=window.screen,o=S.now(),i=window.location.href,a=document.getElementsByTagName("title"),c=a.length?a[0].innerHTML.replace(/^\s+|\s+$/g,""):"",s=S.getRef(),u=I("__51huid__"+(t||e));return u||(u=S.ieVersion()||S.noUint8Array()?S.guid():C("\n        ".concat(e,"\n        ").concat(n.userAgent,"\n        ").concat(o,"\n        ").concat(s,"\n        ").concat(r.width+"*"+r.height,"\n        ").concat(S.getConnectionType(n),"\n        ").concat(Math.random().toString(36).substr(-8),"\n      "),S.guid())),M("__51huid__"+(t||e),u,1/0,"/"),{id:e,uid:u,tt:c,cu:i,pu:s,rt:o,bid:x.value,ct:S.getConnectionType(n)}},q=function(){function e(t){n(this,e),this.id=t.id,this.ck=t.ck,this.sample=t.sample,this.apiHelper=t.apiHelper,this.urlHelper=t.urlHelper,this.ignoreApiStatus=t.ignoreApiStatus,this.ignoreVendor=t.ignoreVendor,this.vendorList=t.vendorList,this.ignoreApiStatus=t.ignoreApiStatus}return o(e,[{key:"record",value:function(e){this.handleRecord(e),setTimeout((function(){E.isStop&&E.fire()}),200)}},{key:"handleRecord",value:function(e){try{if(!this.id&&e)return;var t=e;if(!S.needReport(this.sample)&&"perf"!==t.dt)return;if(t.cu&&this.urlHelper&&(t.cu=S.filterByRule(t.cu,this.urlHelper)),this.ignoreApiStatus.length&&"api"===t.dt){for(var n=0;n<this.ignoreApiStatus.length;n++){var r=this.ignoreApiStatus[n];sendData.status===r&&l("api_flag")}!1}if(t.apiUrl&&this.apiHelper&&(t.apiUrl=S.filterByRule(t.apiUrl,this.apiHelper)),this.ignoreVendor&&("api"===t.dt||"resource"===t.dt||"js"===t.dt)){if(t.scriptUrl){for(var o=!1,i=0;i<this.vendorList.length;i++){var a=this.vendorList[i];S.getDomainByUrl(t.scriptUrl).indexOf(a)>-1&&(o=!0)}if(o)return}if(t.apiUrl){for(var c=!1,s=0;s<this.vendorList.length;s++){var u=this.vendorList[s];S.getDomainByUrl(t.apiUrl).indexOf(u)>-1&&(c=!0)}if(c)return}if(t.resourceUrl){for(var f=!1,d=0;d<this.vendorList.length;d++){var p=this.vendorList[d];S.getDomainByUrl(t.resourceUrl).indexOf(p)>-1&&(f=!0)}if(f)return}}E.add(S.extend(j(this.id,this.ck),t))}catch(e){}}}]),e}(),P=function(){function e(){n(this,e)}return o(e,null,[{key:"PV",get:function(){return"pv"}},{key:"JS_ERROR",get:function(){return"js"}},{key:"RESOURCE_ERROR",get:function(){return"resource"}},{key:"AJAX_ERROR",get:function(){return"api"}},{key:"PERFORMANCE",get:function(){return"perf"}},{key:"HIJACK",get:function(){return"hijack"}}]),e}(),H=function(e){i(r,q);var t=u(r);function r(e){return n(this,r),t.call(this,e)}return o(r,[{key:"handleError",value:function(){var e=arguments,t=this;window.onerror=function(n,r,o,i,a){try{return"Script error."!=n&&!r||(setTimeout((function(){var n={dt:P.JS_ERROR};if(i=i||window.event&&window.event.errorCharacter||0,n.scriptUrl=r,n.row=o,n.col=i,n.name="Unknown",a)if(a&&a.stack)n.content=a.stack.toString(),a.name&&(n.name=a.name),a.message&&(n.message=a.message);else if(e.callee){for(var c=[],s=e.callee.caller,u=3;s&&--u>0&&(c.push(s.toString()),s!==s.caller);)s=s.caller;c=c.join(","),n.content=a.stack.toString()}t.record(n)}),0),!0)}catch(a){}}}}]),r}(),N=function(e){if(window.XMLHttpRequest&&window.CustomEvent){var t=function(e){var t="function"==typeof window.CustomEvent?new CustomEvent(e,{detail:this}):function(e,t){t=t||{bubbles:!1,cancelable:!1,detail:null};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}(e,{detail:this});window.dispatchEvent(t)},n=function(t){if(o[t]&&!0!==o[t].uploadFlag){var n=S.now(),r=o[t].event.detail.responseURL,i=o[t].event.detail.status,a=n-o[t].timeStamp;200!==i&&r&&(e({apiUrl:r,status:i,loadTime:a}),o[t].uploadFlag=!0)}},r=window.XMLHttpRequest,o=[];window.XMLHttpRequest=function(){var e=new r;return e.addEventListener("loadstart",(function(){t.call(this,"ajaxLoadStart")}),!1),e.addEventListener("loadend",(function(){t.call(this,"ajaxLoadEnd")}),!1),e},window.addEventListener("ajaxLoadStart",(function(e){var t={timeStamp:S.now(),event:e,uploadFlag:!1};o.push(t)})),window.addEventListener("ajaxLoadEnd",(function(){for(var e=0;e<o.length;e++)!0!==o[e].uploadFlag&&o[e].event.detail.status>0&&n(e)}))}},_=function(e){i(r,q);var t=u(r);function r(e){return n(this,r),t.call(this,e)}return o(r,[{key:"handleError",value:function(){var e=this;N((function(t){var n=S.extend({dt:P.AJAX_ERROR},t);e.record(n)}))}}]),r}(),F=function(e){i(r,q);var t=u(r);function r(e){return n(this,r),t.call(this,e)}return o(r,[{key:"handleError",value:function(){var e=this;window.addEventListener("error",(function(t){try{if(!t)return;var n=t.target||t.srcElement;if(!(n instanceof HTMLScriptElement||n instanceof HTMLLinkElement||n instanceof HTMLImageElement))return;var r={};r.dt=P.RESOURCE_ERROR,r.resourceType=n.tagName.toLowerCase(),r.resourceUrl=n.src||n.href,r.resourceUrl!==window.self.location.href.replace(window.self.location.hash,"")&&e.record(r)}catch(e){}}),!0)}}]),r}(),J=function(e){i(r,q);var t=u(r);function r(e){return n(this,r),t.call(this,e)}return o(r,[{key:"handleError",value:function(){var e=this;window.addEventListener("unhandledrejection",(function(t){try{if(!t||!t.reason)return;var n={};n.dt=P.JS_ERROR,t.reason.config&&t.reason.config.url&&(n.scriptUrl=t.reason.config.url),t.reason&&t.reason.stack?(n.content=t.reason.stack.toString(),t.reason.name&&(n.name=t.reason.name),t.reason.message&&(n.message=t.reason.message)):n.content=t.reason.stack.toString(),e.record(n)}catch(e){}}),!0)}}]),r}(),D={getTiming:function(e){try{var t=window.performance||window.webkitPerformance||window.msPerformance||window.mozPerformance;if(void 0===t)return;var n=t.timing;if(n.loadEventEnd-n.navigationStart<0)return void setTimeout((function(){D.getTiming(e)}),200);var r={};for(var o in n)"Function"===S.typeCheck(n[o])||"Object"===S.typeCheck(n[o])||(r[o]=n[o]);e(n=r)}catch(e){}},getEntries:function(e){if(e=e||[],window.performance&&window.performance.getEntries){var t=[],n=window.performance.getEntries();return n&&0!=n.length?(n.forEach((function(n,r){var o={};e.indexOf(n.initiatorType)>-1&&(o.name=n.name,o.initiatorType=n.initiatorType,o.nextHopProtocol=n.nextHopProtocol,o.redirectTime=(n.redirectEnd-n.redirectStart).toFixed(2),o.dnsTime=(n.domainLookupEnd-n.domainLookupStart).toFixed(2),o.tcpTime=(n.connectEnd-n.connectStart).toFixed(2),o.ttfbTime=(n.responseStart-n.requestStart).toFixed(2),o.responseTime=(n.responseEnd-n.responseStart).toFixed(2),o.reqTotalTime=(n.responseEnd-n.requestStart).toFixed(2),t.push(o))})),t):t}}},B=function(e){i(r,q);var t=u(r);function r(e){return n(this,r),t.call(this,e)}return o(r,[{key:"handleTiming",value:function(){var e=this;D.getTiming((function(t){var n=S.extend({dt:P.PERFORMANCE},t);e.record(n)}))}},{key:"getSourceType",value:function(e){var t=[];return!1!==e.isRScript&&t.push("script"),!1!==e.isRCSS&&t.push("css"),!1!==e.isRFetch&&t.push("fetch"),!1!==e.isRXHR&&t.push("xmlhttprequest"),!1!==e.isRLink&&t.push("link"),!1!==e.isRIMG&&t.push("img"),t}}]),r}(),V=function e(t){if(t.id)return'id("'+t.id+'")';if(t===document.body)return t.tagName.toLowerCase();for(var n=0,r=t.parentNode&&t.parentNode.childNodes||[],o=0;o<r.length;o++){var i=r[o];if(i===t)return e(t.parentNode)+"/"+t.tagName.toLowerCase()+"["+(n+1)+"]";1===i.nodeType&&i.tagName===t.tagName&&n++}},X=window.self.document.addEventListener,$=function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200,o=r;return function(){var r=this,i=arguments,a=+new Date;t&&a-t<o?(clearTimeout(n),n=setTimeout((function(){t=a,e.apply(r,i)}),o)):(t=a,e.apply(this,i))}},z=function(e){if(!e)return{};var t={},n=e.target||e.srcElement;return t.tn=n.tagName?n.tagName.toLowerCase():"",t.nt=n.NodeType||0,t.xp=V(n),t.id=n.id||"",t.cn=n.className||"",t.url=location.href,t.tm=S.now(),t.type="click",t.pos=function(e){for(var t={},n=e.offsetLeft,r=e.offsetTop,o=e.offsetParent;o;)n+=o.offsetLeft||0,r+=o.offsetTop||0,o=o.offsetParent;return t.x=n,t.y=r,t}(n),t},Q=function(e){if(!e)return{};var t={type:"scroll"};return t.scrollX=window.scrollX,t.scrollY=window.scrollY,t},G={queues:[],getQueues:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return e=Math.abs(e)>this.queues.length?0:e,this.queues.slice(e)},addQueues:function(e){e&&Object.keys(e).length>=1&&this.queues.push(e)},getLength:function(){return this.queues.length}},K=function(e){i(r,q);var t=u(r);function r(e){var o;return n(this,r),(o=t.call(this,e)).jankFps=e.jankFps,o.maxReport=10,o}return o(r,[{key:"handleJank",value:function(){var e=this,t=this,n=0,r=this.maxReport,o=this.jankFps||20,i=0,a=[Date.now(),Date.now()],c=a[0],s=a[1],u=[],l=[];this.sendSpaPv&&S.registerHistoryHandler((function(){u=[],l=[],r=e.maxReport}));var f=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)},d=function e(){var a=Date.now(),d=a-s,p=Math.round(1e3/d);s=a,i+=1,a>1e3+c&&((p=Math.round(1e3*i/(a-c)))&&p<=o?u.push({time:(new Date).valueOf(),fps:p}):p>o&&(u.length>=3?(r--,l=G.getQueues(-5),t.record({dt:"stuck",pageLoadTime:n,userTrack:JSON.stringify(l)}),l=[],u=[]):(u=[],l=[])),i=0,c=a),r>=1&&f(e)};D.getTiming((function(e){n=e.loadEventEnd-e.navigationStart,d()}))}},{key:"getStaticTime",value:function(){var e,t=window.performance.getEntries().filter((function(e){return"script"===e.initiatorType}));e=Math.max.apply(Math,f(t.map((function(e){return e.responseEnd}))))-Math.min.apply(Math,f(t.map((function(e){return e.startTime}))));var n=window.performance.getEntries().filter((function(e){return"css"===e.initiatorType}));return{jsTime:e,styleTime:Math.max.apply(Math,f(n.map((function(e){return e.responseEnd}))))-Math.min.apply(Math,f(n.map((function(e){return e.startTime}))))}}}]),r}(),Y=function(e){i(r,q);var t=u(r);function r(e){return n(this,r),t.call(this,e)}return o(r,[{key:"handleMonitor",value:function(){var e,t=this;(!S.detectIE()||S.detectIE()>8)&&(window.open=(e=window.open,function(n,r,o){return n&&!S.checkCurDomainUrl(n)&&t.record({dt:"outlink",outLinkUrl:n,nw:!0}),r=r||"default_window_name",e.call(window,n,r,o)})),h(document,"click",(function(e){var n=e.target||e.srcElement;if("a"===n.nodeName.toLocaleLowerCase()){var r=n.getAttribute("href"),o="_blank"===n.getAttribute("target")||!1,i=n.getAttribute("rel")&&"nofollow"===n.getAttribute("rel");r&&!S.checkCurDomainUrl(r)&&t.record({dt:"outlink",outLinkUrl:r,nw:o,nofollow:i})}}))}}]),r}(),W=function(e){i(r,q);var t=u(r);function r(e){return n(this,r),t.call(this,e)}return o(r,[{key:"handleRouteChange",value:function(){var e=this;S.registerHistoryHandler((function(){x.renew();var t={dt:P.PV,bid:x.value};e.record(t)}))}}]),r}(),Z=function(e){i(r,q);var t=u(r);function r(e){return n(this,r),t.call(this,e)}return o(r,[{key:"handlePvReport",value:function(){var e={dt:P.PV,bid:x.value};this.record(e)}}]),r}(),ee=["cdn.bootcdn.net","lf26-cdn-tos.bytecdntp.com","lib.sinaapp.com","cdn.staticfile.org","upcdn.b0.upaiyun.com","lib.baomitu.com","ajax.aspnetcdn.com","cdn.jsdelivr.net","cdnjs.cloudflare.com","ajax.googleapis.com","g.alicdn.com",".51.la"],te=function(e,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=[];if(n&&n.length?r?(ee.forEach((function(e){return o.push(e)})),n.forEach((function(e){o.indexOf(e)<0&&o.push(e)}))):o=n:o=ee,void 0===e||""===e)return!1;for(var i=o.length,a=0;a<i;a++)if(o[a]instanceof RegExp){var c=new RegExp(o[a]);if(c.test(e.replace("https://","").replace("http://","")))return!0}else if(t(o[a])&&e.replace("https://","").replace("http://","").indexOf(o[a].replace("https://","").replace("http://",""))>-1)return!0;return!1},ne=function(e){i(a,q);var r=u(a);function a(e){var t;return n(this,a),(t=r.call(this,e)).safeList=e.suspiciousSafeList.concat([/chrome-extension:\/\//]),t}return o(a,[{key:"filter",value:function(e,n){if(e===this.safeList){if(void 0===n||""===n)return!0}else if(void 0===n||""===n)return!1;for(var r=e.length,o=0;o<r;o++){if(e[o]instanceof RegExp){if(new RegExp(e[o]).test(n.replace("https://","").replace("http://","")))return!0}else if(t(e[o])&&n.replace("https://","").replace("http://","").indexOf(e[o].replace("https://","").replace("http://",""))>-1)return!0}return!1}},{key:"interceptionStaticScript",value:function(){var e=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;if(e){var t=this,n=new e((function(e){e.forEach((function(e){for(var n=e.addedNodes,r=0;r<n.length;r++){var o=n[r];"SCRIPT"!==o.tagName&&"IFRAME"!==o.tagName||("IFRAME"!==o.tagName||S.checkCurDomainUrl(o.src)||te(o.src,t.vendorList||[],!0)||!o.src||t.filter(t.safeList,o.src)?o.src&&(S.checkCurDomainUrl(o.src)||te(o.src,t.vendorList||[],!0)||t.filter(t.safeList,o.src)||t.record({dt:"suspicious_js",url:o.src,suspiciousJsUrl:o.src})):t.record({dt:"suspicious_iframe",url:o.src,suspiciousIframeUrl:o.src}))}}))}));n.observe(document,{subtree:!0,childList:!0})}}}]),a}(),re=function(){function t(){n(this,t),this.id=0,this.ck=0,this.sample=1,this.sendJsError=!0,this.sendResource=!0,this.sendApi=!0,this.sendPerf=!0,this.sendOutLink=!0,this.urlHelper={},this.apiHelper={},this.ignoreApiStatus=[],this.sendSpaPv=!1,this.ignoreVendor=!0,this.vendorList=["sdk.51.la","ia.51.la","js.users.51.la","collect-v6.51.la","google-analytics.com","googletagmanager.com","googlesyndication.com","googleapis.com","doubleclick.net","www.google-analytics.com","googletagservices.com","googleadservices.com","gstatic.com","google.com","retcode.alicdn.com","arms-retcode.aliyuncs.com","log-global.aliyuncs.com","hm.baidu.com",".cnzz.com","zz.bdstatic.com","getclicky.com","growingio.com","meiqia.com","qq.com","idqqimg.com","gtimg.com","gtimg.cn"],this.sendJank=!0,this.sendSuspicious=!1}return o(t,[{key:"init",value:function(t){t=t||{},this.id=t.id||0,this.ck=t.ck||t.id||0,this.sample=t.sample||this.sample,this.sendJsError=!(!1===t.sendJsError),this.sendResource=!(!1===t.sendResource),this.sendApi=!(!1===t.sendApi),this.sendPerf=!(!1===t.sendPerf),this.sendOutLink=!(!1===t.sendOutLink),this.urlHelper=t.sendOutLink||{},this.apiHelper=t.apiHelper||{},this.sendSpaPv=!0===t.sendSpaPv,this.ignoreApiStatus=t.ignoreApiStatus||[],this.ignoreVendor=!(!1===t.ignoreVendor),this.sendJank=!(!1===t.sendJank),this.sendSuspicious=!0===t.sendSuspicious;var n={id:this.id,ck:this.ck,sample:this.sample,apiHelper:this.apiHelper,urlHelper:this.urlHelper,ignoreApiStatus:this.ignoreApiStatus,ignoreVendor:this.ignoreVendor,vendorList:this.vendorList,jankFps:t.jankFps||20,suspiciousSafeList:t.suspiciousSafeList||[]};S.isRobot()||(new Z(n).handlePvReport(),this.sendPerf&&new B(n).handleTiming(),this.sendJsError&&(new H(n).handleError(),new J(n).handleError()),this.sendApi&&new _(n).handleError(),this.sendResource&&new F(n).handleError(),this.sendOutLink&&new Y(n).handleMonitor(),this.sendSpaPv&&new W(n).handleRouteChange(),this.sendJank&&new K(n).handleJank(),this.sendSuspicious&&new ne(n).interceptionStaticScript(),function(t,n,r,o){t=t||window.self;var i=o&&o.capture,a=o&&o.handle,c=a?o.handle(e):null;a||("click"===n&&(c=function(e){return z(e)}),"scroll"===n&&(c=function(e){return Q(e)})),X?(o.throttle&&t.addEventListener(n,$((function(e){var t=c(e);r&&r(t)}),o.throttle),!0===i),!o.throttle&&t.addEventListener(n,(function(e){var t=c(e);r&&r(t)}),!0===i)):(o.throttle&&t.attachEvent(o.custom?n:"on".concat(n),$((function(e){e=e||window.event;var t=c(e);r&&r(t)}),o.throttle)),!o.throttle&&t.attachEvent(o.custom?n:"on".concat(n),(function(e){e=e||window.event;var t=c(e);r&&r(t)})))}(window.self.document,"click",(function(e){return G.addQueues(e)}),{capture:!0,throttle:300}))}}]),t}();window.LingQue=re;export{re as Monitor};
