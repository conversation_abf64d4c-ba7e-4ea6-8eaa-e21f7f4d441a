const Manager = [
	{
		title: '主页',
		key: 'Home',
		icon: 'h-icon-home'
	},
	{
		title: '票据',
		key: 'Bills',
		icon: 'h-icon-edit',
		children: [
			{
				title: '票据列表',
				key: 'BillList' // 链接到票据列表页面
			},
			{
				title: '银行回单列表',
				key: 'BankReceiptsList' // 链接到银行回单列表页面
			},
			{
				title: '批量上传识别',
				key: 'BatchImport' // 链接到批量导入页面
			},
			{
				title: '批量处理记录',
				key: 'BatchTaskList' // 链接到批量任务列表页面
			}
		]
	},
	{
		title: '归并关联',
		key: 'MergeRelation',
		icon: 'h-icon-link',
		children: [
			{
				title: '功能演示',
				key: 'MergeDemo'
			},
			{
				title: '归并票证',
				key: 'MergeManagement'
			},
			{
				title: '关联票据',
				key: 'RelationManagement'
			},
			{
				title: '归并规则',
				key: 'MergeRuleManagement'
			},
			{
				title: '统一查询',
				key: 'UnifiedQuery'
			}
		]
	},
	{
		title: 'AI智能助手',
		key: 'AiAccountingPanel',
		icon: 'h-icon-upload',
		children: [
			{
				title: 'AI助手总览',
				key: 'AiDashboard'
			},
			{
				title: 'AI智能归并',
				key: 'AiMergeEngine'
			},
			{
				title: 'AI智能关联',
				key: 'AiRelationEngine'
			},
			{
				title: '科目AI增强',
				key: 'SubjectAiEnhancement'
			},
			{
				title: '科目AI分析历史',
				key: 'AiAnalysisHistory'
			},
			{
				title: 'AI配置管理',
				key: 'AiConfigSimple'
			}
		]
	},
	{
		title: '凭证',
		key: 'vouchers',
		icon: 'h-icon-star',
		children: [
			{
				title: '新增凭证',
				key: 'VoucherForm'
			},
			{
				title: '凭证列表',
				key: 'Voucher'
			}
		]
	},
	{
		title: '帐薄',
		key: 'AccountBooks',
		icon: 'h-icon-task',
		children: [
			{
				title: '明细账',
				key: 'DetailedAccounts'
			},
			{
				title: '总账',
				key: 'GeneralLedger'
			},
			{
				title: '科目余额',
				key: 'SubjectBalance'
			},
			{
				title: '科目汇总',
				key: 'SubjectSummary'
			},
			{
				title: '核算项目明细账',
				key: 'AuxiliaryAccountingDetail'
			},
			{
				title: '核算项目余额',
				key: 'AuxiliaryAccountingBalance'
			}
		]
	},
	{
		title: '报表',
		key: 'ReportList',
		icon: 'h-icon-search'
	},
	{
		title: '结账',
		key: 'CheckList',
		icon: 'h-icon-complete',
	},
	{
		title: '设置',
		key: 'Setting',
		icon: 'h-icon-setting',
		children: [
			{
				title: '账套',
				key: 'Account'
			},
			{
				title: '科目',
				key: 'Subject'
			},
			{
				title: '期初',
				key: 'Initial'
			},
			{
				title: '币别',
				key: 'Currency'
			},
			{
				title: '凭证字',
				key: 'VoucherWord'
			}, {
				title: '辅助核算',
				key: 'AccountingCategory'
			}, {
				title: '单据模版',
				key: 'FieldMappingTemplate'
			}/*, {
				title: '凭证模板',
				key: 'TemplateManager'
			}*/, {
				title: '权限设置',
				key: 'PermissionSetting'
			}

		]
	}
];

const Making = [
	{
		title: '主页',
		key: 'Home',
		icon: 'icon-monitor'
	},
	{
		title: '凭证',
		key: 'vouchers',
		icon: 'icon-grid-2',
		children: [
			{
				title: '新增凭证',
				key: 'VoucherForm'
			},
			{
				title: '凭证列表',
				key: 'Voucher'
			}
		]
	},
	{
		title: '归并关联',
		key: 'MergeRelation',
		icon: 'icon-link',
		children: [
			{
				title: '功能演示',
				key: 'MergeDemo'
			},
			{
				title: '归并关联管理',
				key: 'MergeManagement'
			},
			{
				title: '统一查询',
				key: 'UnifiedQuery'
			}
		]
	},
	{
		title: '关联票据',
		key: 'RelationManagement',
		icon: 'icon-relation'
	},
	{
		title: 'AI智能助手',
		key: 'AiAccountingPanel',
		icon: 'icon-upload',
		children: [
			{
				title: 'AI助手总览',
				key: 'AiDashboard'
			},
			{
				title: 'AI智能归并',
				key: 'AiMergeEngine'
			},
			{
				title: 'AI智能关联',
				key: 'AiRelationEngine'
			},
			{
				title: '科目AI增强',
				key: 'SubjectAiEnhancement'
			},
			{
				title: '科目AI分析历史',
				key: 'AiAnalysisHistory'
			},
			{
				title: 'AI配置管理',
				key: 'AiConfigSimple'
			}
		]
	},
	{
		title: '结账',
		key: 'CheckList',
		icon: 'icon-disc',
	},
	{
		title: '帐薄',
		key: 'AccountBooks',
		icon: 'icon-paper',
		children: [
			{
				title: '明细账',
				key: 'DetailedAccounts'
			},
			{
				title: '总账',
				key: 'GeneralLedger'
			},
			{
				title: '科目余额',
				key: 'SubjectBalance'
			},
			{
				title: '科目汇总',
				key: 'SubjectSummary'
			},
			{
				title: '核算项目明细账',
				key: 'AuxiliaryAccountingDetail'
			},
			{
				title: '核算项目余额',
				key: 'AuxiliaryAccountingBalance'
			}
		]
	},
	{
		title: '报表',
		key: 'ReportList',
		icon: 'icon-bar-graph-2'
	},
	{
		title: '设置',
		key: 'Setting',
		icon: 'icon-cog',
		children: [
			{
				title: '账套',
				key: 'Account'
			}
		]
	}
];

const View = [
	{
		title: '主页',
		key: 'Home',
		icon: 'icon-monitor'
	},
	{
		title: '凭证',
		key: 'vouchers',
		icon: 'icon-grid-2',
		children: [
			{
				title: '凭证列表',
				key: 'Voucher'
			}
		]
	},
	{
		title: '归并关联',
		key: 'MergeRelation',
		icon: 'icon-link',
		children: [
			{
				title: '功能演示',
				key: 'MergeDemo'
			},
			{
				title: '统一查询',
				key: 'UnifiedQuery'
			}
		]
	},
	{
		title: '关联票据',
		key: 'RelationManagement',
		icon: 'icon-relation'
	},
	{
		title: 'AI智能助手',
		key: 'AiAccountingPanel',
		icon: 'icon-upload',
		children: [
			{
				title: 'AI助手总览',
				key: 'AiDashboard'
			},
			{
				title: 'AI智能归并',
				key: 'AiMergeEngine'
			},
			{
				title: 'AI智能关联',
				key: 'AiRelationEngine'
			},
			{
				title: '科目AI增强',
				key: 'SubjectAiEnhancement'
			},
			{
				title: '科目AI分析历史',
				key: 'AiAnalysisHistory'
			},
			{
				title: 'AI配置管理',
				key: 'AiConfigSimple'
			}
		]
	},
	{
		title: '帐薄',
		key: 'AccountBooks',
		icon: 'icon-paper',
		children: [
			{
				title: '明细账',
				key: 'DetailedAccounts'
			},
			{
				title: '总账',
				key: 'GeneralLedger'
			},
			{
				title: '科目余额',
				key: 'SubjectBalance'
			},
			{
				title: '科目汇总',
				key: 'SubjectSummary'
			},
			{
				title: '核算项目明细账',
				key: 'AuxiliaryAccountingDetail'
			},
			{
				title: '核算项目余额',
				key: 'AuxiliaryAccountingBalance'
			}
		]
	},
	{
		title: '报表',
		key: 'ReportList',
		icon: 'icon-bar-graph-2'
	},
	{
		title: '设置',
		key: 'Setting',
		icon: 'icon-cog',
		children: [
			{
				title: '账套',
				key: 'Account'
			}
		]
	}
];

export default {
	Manager,
	Making,
	View
};
