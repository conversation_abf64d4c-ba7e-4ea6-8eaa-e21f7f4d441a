/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : </li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月01日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
const apis = require.context('.', false, /.*\.js$/);

let apiObj = {};
apis.keys().forEach(key => {
	if (key === './index.js') return;
	apiObj[key.replace(/(\.\/)|(\.(js)$)/g, '')] = apis(key).default;
});

export default apiObj;
