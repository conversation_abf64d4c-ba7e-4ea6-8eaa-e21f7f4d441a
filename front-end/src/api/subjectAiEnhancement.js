/**
 * 科目AI增强相关API接口
 */
import request from '@/utils/request'

const API_BASE = '/api/subject-ai-enhancement'

export default {
  /**
   * 智能匹配科目
   * @param {Object} params - 匹配参数
   * @param {number} params.accountSetsId - 账套ID
   * @param {string} params.description - 业务描述
   * @param {number} params.amount - 金额
   * @param {string} params.businessType - 业务类型
   * @returns {Promise} 匹配结果
   */
  intelligentMatch(params) {
    return request({
      url: `${API_BASE}/intelligent-match`,
      method: 'post',
      data: params
    })
  },

  /**
   * 根据关键词搜索科目
   * @param {number} accountSetsId - 账套ID
   * @param {string} keyword - 关键词
   * @returns {Promise} 搜索结果
   */
  searchByKeyword(accountSetsId, keyword) {
    return request({
      url: `${API_BASE}/search`,
      method: 'get',
      params: {
        accountSetsId,
        keyword
      }
    })
  },

  /**
   * 获取最常用科目
   * @param {number} accountSetsId - 账套ID
   * @param {number} limit - 限制数量
   * @returns {Promise} 常用科目列表
   */
  getMostUsed(accountSetsId, limit = 10) {
    return request({
      url: `${API_BASE}/most-used`,
      method: 'get',
      params: {
        accountSetsId,
        limit
      }
    })
  },

  /**
   * 推荐科目
   * @param {number} accountSetsId - 账套ID
   * @param {number} minConfidence - 最小置信度
   * @param {number} limit - 限制数量
   * @returns {Promise} 推荐科目列表
   */
  recommend(accountSetsId, minConfidence = 0.7, limit = 5) {
    return request({
      url: `${API_BASE}/recommend`,
      method: 'get',
      params: {
        accountSetsId,
        minConfidence,
        limit
      }
    })
  },

  /**
   * 记录科目使用情况
   * @param {Object} params - 使用记录参数
   * @param {number} params.subjectId - 科目ID
   * @param {number} params.accountSetsId - 账套ID
   * @param {Object} params.matchingContext - 匹配上下文
   * @param {boolean} params.isCorrect - 是否正确匹配
   * @returns {Promise} 记录结果
   */
  recordUsage(params) {
    return request({
      url: `${API_BASE}/record-usage`,
      method: 'post',
      data: params
    })
  },

  /**
   * 更新AI描述和关键词
   * @param {Object} params - 更新参数
   * @param {number} params.subjectId - 科目ID
   * @param {number} params.accountSetsId - 账套ID
   * @param {string} params.aiDescription - AI描述
   * @param {string} params.aiKeywords - AI关键词
   * @returns {Promise} 更新结果
   */
  updateAiInfo(params) {
    return request({
      url: `${API_BASE}/update-ai-info`,
      method: 'put',
      data: params
    })
  },

  /**
   * 批量初始化AI增强信息
   * @param {number} accountSetsId - 账套ID
   * @returns {Promise} 初始化结果
   */
  initialize(accountSetsId) {
    return request({
      url: `${API_BASE}/initialize`,
      method: 'post',
      data: { accountSetsId }
    })
  },

  /**
   * 优化匹配规则
   * @param {number} accountSetsId - 账套ID
   * @returns {Promise} 优化结果
   */
  optimizeRules(accountSetsId) {
    return request({
      url: `${API_BASE}/optimize-rules`,
      method: 'post',
      data: { accountSetsId }
    })
  },

  /**
   * 获取AI增强信息
   * @param {number} subjectId - 科目ID
   * @param {number} accountSetsId - 账套ID
   * @returns {Promise} AI增强信息
   */
  getEnhancement(subjectId, accountSetsId) {
    return request({
      url: `${API_BASE}/enhancement`,
      method: 'get',
      params: {
        subjectId,
        accountSetsId
      }
    })
  },

  /**
   * 保存或更新增强信息
   * @param {Object} enhancement - 增强信息
   * @returns {Promise} 保存结果
   */
  saveEnhancement(enhancement) {
    return request({
      url: `${API_BASE}/enhancement`,
      method: 'post',
      data: enhancement
    })
  },

  /**
   * 更新状态（启用/禁用）
   * @param {number} id - 增强信息ID
   * @param {number} status - 状态（1启用，0禁用）
   * @returns {Promise} 更新结果
   */
  updateStatus(id, status) {
    return request({
      url: `${API_BASE}/status`,
      method: 'put',
      data: { id, status }
    })
  },

  /**
   * 获取所有启用的科目AI增强信息
   * @param {number} accountSetsId - 账套ID
   * @returns {Promise} 启用的增强信息列表
   */
  getAllEnabled(accountSetsId) {
    return request({
      url: `${API_BASE}/enabled`,
      method: 'get',
      params: { accountSetsId }
    })
  },

  /**
   * 分析使用统计
   * @param {number} accountSetsId - 账套ID
   * @param {string} startDate - 开始日期
   * @param {string} endDate - 结束日期
   * @returns {Promise} 统计分析结果
   */
  analyzeUsage(accountSetsId, startDate, endDate) {
    return request({
      url: `${API_BASE}/analyze-usage`,
      method: 'get',
      params: {
        accountSetsId,
        startDate,
        endDate
      }
    })
  },

  /**
   * 导出AI配置
   * @param {number} accountSetsId - 账套ID
   * @returns {Promise} 配置数据
   */
  exportConfig(accountSetsId) {
    return request({
      url: `${API_BASE}/export-config`,
      method: 'get',
      params: { accountSetsId }
    })
  },

  /**
   * 导入AI配置
   * @param {number} accountSetsId - 账套ID
   * @param {Object} config - 配置数据
   * @returns {Promise} 导入结果
   */
  importConfig(accountSetsId, config) {
    return request({
      url: `${API_BASE}/import-config`,
      method: 'post',
      data: {
        accountSetsId,
        config
      }
    })
  },

  /**
   * 获取科目匹配建议
   * @param {number} accountSetsId - 账套ID
   * @param {string} context - 上下文信息
   * @returns {Promise} 匹配建议
   */
  getMatchingSuggestions(accountSetsId, context) {
    return request({
      url: `${API_BASE}/matching-suggestions`,
      method: 'post',
      data: {
        accountSetsId,
        context
      }
    })
  },

  /**
   * 学习用户偏好
   * @param {Object} params - 学习参数
   * @param {number} params.accountSetsId - 账套ID
   * @param {Array} params.userActions - 用户行为数据
   * @returns {Promise} 学习结果
   */
  learnUserPreferences(params) {
    return request({
      url: `${API_BASE}/learn-preferences`,
      method: 'post',
      data: params
    })
  },

  /**
   * 获取科目AI增强列表（分页）
   * @param {Object} params - 查询参数
   * @param {number} params.accountSetsId - 账套ID
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页大小
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.status - 状态筛选
   * @returns {Promise} 分页结果
   */
  getEnhancementList(params) {
    return request({
      url: `${API_BASE}/list`,
      method: 'get',
      params
    })
  },

  /**
   * 批量更新置信度评分
   * @param {Object} params - 更新参数
   * @param {number} params.accountSetsId - 账套ID
   * @param {Array} params.updates - 更新数据列表
   * @returns {Promise} 更新结果
   */
  batchUpdateConfidence(params) {
    return request({
      url: `${API_BASE}/batch-update-confidence`,
      method: 'put',
      data: params
    })
  },

  /**
   * 删除AI增强信息
   * @param {number} id - 增强信息ID
   * @returns {Promise} 删除结果
   */
  deleteEnhancement(id) {
    return request({
      url: `${API_BASE}/enhancement/${id}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除AI增强信息
   * @param {Array} ids - 增强信息ID列表
   * @returns {Promise} 删除结果
   */
  batchDeleteEnhancement(ids) {
    return request({
      url: `${API_BASE}/batch-delete`,
      method: 'delete',
      data: { ids }
    })
  },

  /**
   * 重置AI增强信息
   * @param {number} id - 增强信息ID
   * @returns {Promise} 重置结果
   */
  resetEnhancement(id) {
    return request({
      url: `${API_BASE}/reset/${id}`,
      method: 'put'
    })
  },

  /**
   * 获取AI增强统计信息
   * @param {number} accountSetsId - 账套ID
   * @returns {Promise} 统计信息
   */
  getStatistics(accountSetsId) {
    return request({
      url: `${API_BASE}/statistics`,
      method: 'get',
      params: { accountSetsId }
    })
  },

  /**
   * 测试匹配规则
   * @param {Object} params - 测试参数
   * @param {number} params.subjectId - 科目ID
   * @param {string} params.testData - 测试数据
   * @returns {Promise} 测试结果
   */
  testMatchingRule(params) {
    return request({
      url: `${API_BASE}/test-rule`,
      method: 'post',
      data: params
    })
  },

  /**
   * 获取匹配历史
   * @param {Object} params - 查询参数
   * @param {number} params.accountSetsId - 账套ID
   * @param {number} params.subjectId - 科目ID（可选）
   * @param {string} params.startDate - 开始日期
   * @param {string} params.endDate - 结束日期
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页大小
   * @returns {Promise} 匹配历史
   */
  getMatchingHistory(params) {
    return request({
      url: `${API_BASE}/matching-history`,
      method: 'get',
      params
    })
  }
}