import <PERSON> from '../js/common/ajax'
import store from '../store' // 导入 store 来获取用户信息

export default {
  list(params) {
    return Ajax.get('/bank-receipts/list', params)
  },
  load(id) {
    return Ajax.get(`/bank-receipts/${id}`)
  },
  delete(id) {
    return Ajax.delete(`/bank-receipts/${id}`)
  },
  save(params = {}) {
    return Ajax.postJson('/bank-receipts', params)
  },
  update(id, params = {}) {
    return Ajax.put(`/bank-receipts`, params)
  },
  
  // 关联功能已移至关联管理模块
  // getRecommendedBills 和 getUnrelatedBills 方法已移除
  
  loadReceiptsNo(params) {
    return Ajax.get('/bank-receipts/receipts-no', params)
  },

  batchDelete(ids) {
    return Ajax.post('/bank-receipts/batchDelete', ids)
  },

  // 数据修复功能
  repairUnknownPayee() {
    return Ajax.post('/bank-receipt-repair/repair-unknown-payee')
  },

  repairSingleReceipt(receiptId) {
    return Ajax.post(`/bank-receipt-repair/repair-single/${receiptId}`)
  },

  repairBatchReceipts(receiptIds) {
    return Ajax.postJson('/bank-receipt-repair/repair-batch', receiptIds)
  },

  getRepairStatus() {
    return Ajax.get('/bank-receipt-repair/repair-status')
  },

  // 关联功能已移至关联管理模块
}
