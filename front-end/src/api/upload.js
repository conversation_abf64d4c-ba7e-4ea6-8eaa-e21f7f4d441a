import Ajax from '../js/common/ajax'

export default {
  // 上传文件
  file(formData, folder = 'bank-receipts') {
    const params = new URLSearchParams()
    params.append('folder', folder)
    return Ajax.post('/api/upload/file?' + params.toString(), formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  // 删除文件
  deleteFile(fileUrl) {
    return Ajax.delete('/api/upload/file', { fileUrl })
  },
  
  // 识别银行回单
  recognizeFile(fileUrl) {
    return Ajax.post('/api/upload/recognize', { fileUrl })
  },

  // 根据指定端点识别文件
  recognizeWithEndpoint(endpoint, fileUrl) {
    return Ajax.post(endpoint, { fileUrl })
  }
}