/**
 * 票据API
 */
import <PERSON> from '../js/common/ajax';
import Qs from "qs";

export default {
  list(params) {
    return Ajax.get("/bill/list", params)
  },
  load(id) {
    return Ajax.get(`/bill/${id}`)
  },
  loadBillNo(params) {
    return Ajax.get(`/bill/bill-no`, params)
  },
  delete(id) {
    return Ajax.delete(`/bill/${id}`)
  },
  save(params) {
    return Ajax.post("/bill", params)
  },
  update(id, params) {
    // 将id合并到params中
    const data = { ...params, id };
    return Ajax.put("/bill", data)
  },
  batchDelete(ids) {
    return Ajax.post("/bill/batchDelete", ids)
  }
}