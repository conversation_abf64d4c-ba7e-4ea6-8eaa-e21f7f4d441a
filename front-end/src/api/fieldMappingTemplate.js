/**
 * 字段映射模版API
 */
export default {
  /**
   * 获取模版列表
   * @param {Object} params 查询参数
   * @returns {Promise} 模版列表
   */
  getList(params = {}) {
    return Ajax.get('/field-mapping-template/list', params)
  },

  /**
   * 获取模版详情
   * @param {number} id 模版ID
   * @returns {Promise} 模版详情
   */
  getDetail(id) {
    return Ajax.get(`/field-mapping-template/${id}`)
  },

  /**
   * 创建模版
   * @param {Object} data 模版数据
   * @returns {Promise} 创建结果
   */
  create(data) {
    return Ajax.post('/field-mapping-template', data)
  },

  /**
   * 更新模版
   * @param {Object} data 模版数据
   * @returns {Promise} 更新结果
   */
  update(data) {
    return Ajax.put(`/field-mapping-template/${data.id}`, data)
  },

  /**
   * 删除模版
   * @param {number} id 模版ID
   * @returns {Promise} 删除结果
   */
  delete(id) {
    return Ajax.delete(`/field-mapping-template/${id}`)
  },

  /**
   * 批量删除模版
   * @param {Array} ids 模版ID数组
   * @returns {Promise} 删除结果
   */
  batchDelete(ids) {
    return Ajax.post('/field-mapping-template/batch-delete', { ids })
  },

  /**
   * 切换模版状态
   * @param {number} id 模版ID
   * @returns {Promise} 切换结果
   */
  toggleStatus(id) {
    return Ajax.put(`/field-mapping-template/${id}/toggle-status`)
  },

  /**
   * 获取模版统计信息
   * @returns {Promise} 统计信息
   */
  getStatistics() {
    return Ajax.get('/field-mapping-template/statistics')
  },

  /**
   * 测试模版
   * @param {number} id 模版ID
   * @param {Object} testData 测试数据
   * @returns {Promise} 测试结果
   */
  testTemplate(id, testData) {
    return Ajax.post(`/field-mapping-template/${id}/test`, testData)
  },

  /**
   * 导出模版
   * @param {Array} ids 模版ID数组
   * @returns {Promise} 导出结果
   */
  exportTemplates(ids = []) {
    return Ajax.post('/field-mapping-template/export', { ids }, {
      responseType: 'blob'
    })
  },

  /**
   * 导入模版
   * @param {FormData} formData 包含文件的FormData
   * @returns {Promise} 导入结果
   */
  importTemplates(formData) {
    return Ajax.post('/field-mapping-template/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取银行标识列表
   * @returns {Promise} 银行标识列表
   */
  getBankIdentifiers() {
    return Ajax.get('/field-mapping-template/bank-identifiers')
  },

  /**
   * 获取回单类型列表
   * @param {string} bankIdentifier 银行标识
   * @returns {Promise} 回单类型列表
   */
  getReceiptTypes(bankIdentifier) {
    return Ajax.get('/field-mapping-template/receipt-types', { bankIdentifier })
  },

  /**
   * 根据OCR数据匹配模版
   * @param {Object} ocrData OCR识别数据
   * @param {string} documentType 单据类型
   * @returns {Promise} 匹配结果
   */
  matchTemplate(ocrData, documentType) {
    return Ajax.post('/field-mapping-template/match', {
      ocrData,
      documentType
    })
  },

  /**
   * 应用模版进行字段映射
   * @param {number} templateId 模版ID
   * @param {Object} ocrData OCR识别数据
   * @returns {Promise} 映射结果
   */
  applyTemplate(templateId, ocrData) {
    return Ajax.post(`/field-mapping-template/${templateId}/apply`, {
      ocrData
    })
  },

  /**
   * 获取模版使用历史
   * @param {number} templateId 模版ID
   * @param {Object} params 查询参数
   * @returns {Promise} 使用历史
   */
  getUsageHistory(templateId, params = {}) {
    return Ajax.get(`/field-mapping-template/${templateId}/usage-history`, params)
  },

  /**
   * 更新模版成功率
   * @param {number} templateId 模版ID
   * @param {boolean} success 是否成功
   * @returns {Promise} 更新结果
   */
  updateSuccessRate(templateId, success) {
    return Ajax.post(`/field-mapping-template/${templateId}/update-success-rate`, {
      success
    })
  }
}
