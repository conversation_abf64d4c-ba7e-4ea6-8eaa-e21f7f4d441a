import request from '@/utils/request'

// 归并引擎API
export const mergeEngineApi = {
  // 预览票据归并结果
  previewDocumentMerge(ruleId) {
    return request({
      url: '/api/merge-engine/documents/preview',
      method: 'get',
      params: { ruleId }
    })
  },

  // 执行票据归并
  executeDocumentMerge(data) {
    return request({
      url: '/api/merge-engine/documents/execute',
      method: 'post',
      data
    })
  },

  // 预览银证归并结果
  previewReceiptMerge(ruleId) {
    return request({
      url: '/api/merge-engine/receipts/preview',
      method: 'get',
      params: { ruleId }
    })
  },

  // 执行银证归并
  executeReceiptMerge(data) {
    return request({
      url: '/api/merge-engine/receipts/execute',
      method: 'post',
      data
    })
  },

  // 手动票据归并
  manualDocumentMerge(documentIds, groupName) {
    return request({
      url: '/api/merge-engine/documents/manual-merge',
      method: 'post',
      params: { documentIds, groupName }
    })
  },

  // 手动银证归并
  manualReceiptMerge(receiptIds, groupName) {
    return request({
      url: '/api/merge-engine/receipts/manual-merge',
      method: 'post',
      params: { receiptIds, groupName }
    })
  },

  // 解散票据归并组
  unmergeDocumentGroup(groupId) {
    return request({
      url: `/api/merge-engine/documents/groups/${groupId}`,
      method: 'delete'
    })
  },

  // 解散银证归并组
  unmergeReceiptGroup(groupId) {
    return request({
      url: `/api/merge-engine/receipts/groups/${groupId}`,
      method: 'delete'
    })
  },

  // 增量归并新记录
  incrementalMerge(entityId, entityType) {
    return request({
      url: '/api/merge-engine/incremental-merge',
      method: 'post',
      params: { entityId, entityType }
    })
  },

  // 查询归并任务状态
  getTaskStatus(taskId) {
    return request({
      url: `/api/merge-engine/tasks/${taskId}`,
      method: 'get'
    })
  },

  // 获取未归并的票据
  getUnmergedDocuments() {
    return request({
      url: '/api/merge-engine/documents/unmerged',
      method: 'get'
    })
  },

  // 获取未归并的银证
  getUnmergedReceipts() {
    return request({
      url: '/api/merge-engine/receipts/unmerged',
      method: 'get'
    })
  }
}

// 关联管理API
export const relationApi = {
  // 创建关联关系
  createRelation(data) {
    return request({
      url: '/api/relations',
      method: 'post',
      data
    })
  },

  // 批量创建关联关系
  createBatchRelations(data) {
    return request({
      url: '/api/relations/batch',
      method: 'post',
      data
    })
  },

  // 查询票据相关关联
  queryDocumentRelations(entityId, entityType) {
    return request({
      url: `/api/relations/documents/${entityId}`,
      method: 'get',
      params: { entityType }
    })
  },

  // 查询银证相关关联
  queryReceiptRelations(entityId, entityType) {
    return request({
      url: `/api/relations/receipts/${entityId}`,
      method: 'get',
      params: { entityType }
    })
  },

  // 查询跨类型关联
  queryCrossTypeRelations(entityId, entityType) {
    return request({
      url: `/api/relations/cross-type/${entityId}`,
      method: 'get',
      params: { entityType }
    })
  },

  // 查询所有关联关系
  queryAllRelations(entityId, entityType) {
    return request({
      url: `/api/relations/all/${entityId}`,
      method: 'get',
      params: { entityType }
    })
  },

  // 删除关联关系
  deleteRelation(relationId) {
    return request({
      url: `/api/relations/${relationId}`,
      method: 'delete'
    })
  },

  // 删除实体的所有关联关系
  deleteAllRelationsByEntity(entityId, entityType) {
    return request({
      url: `/api/relations/entity/${entityId}`,
      method: 'delete',
      params: { entityType }
    })
  },

  // 验证关联关系有效性
  validateRelation(sourceType, sourceId, targetType, targetId) {
    return request({
      url: '/api/relations/validate',
      method: 'get',
      params: { sourceType, sourceId, targetType, targetId }
    })
  }
}

// 统一查询API
export const unifiedQueryApi = {
  // 票据统一查询
  queryDocuments(data) {
    return request({
      url: '/api/unified-query/documents',
      method: 'post',
      data
    })
  },

  // 银证统一查询
  queryReceipts(data) {
    return request({
      url: '/api/unified-query/receipts',
      method: 'post',
      data
    })
  },

  // 跨类型关联查询
  queryCrossTypeRelations(data) {
    return request({
      url: '/api/unified-query/cross-type-relations',
      method: 'post',
      data
    })
  }
}

// 归并规则API
export const mergeRuleApi = {
  // 获取归并规则列表
  getMergeRules(applicableEntity) {
    return request({
      url: '/api/merge-rules',
      method: 'get',
      params: { applicableEntity }
    })
  },

  // 获取归并规则详情
  getMergeRule(ruleId) {
    return request({
      url: `/api/merge-rules/${ruleId}`,
      method: 'get'
    })
  },

  // 创建归并规则
  createMergeRule(data) {
    return request({
      url: '/api/merge-rules',
      method: 'post',
      data
    })
  },

  // 更新归并规则
  updateMergeRule(ruleId, data) {
    return request({
      url: `/api/merge-rules/${ruleId}`,
      method: 'put',
      data
    })
  },

  // 删除归并规则
  deleteMergeRule(ruleId) {
    return request({
      url: `/api/merge-rules/${ruleId}`,
      method: 'delete'
    })
  },

  // 启用/禁用归并规则
  toggleMergeRule(ruleId, isActive) {
    return request({
      url: `/api/merge-rules/${ruleId}/toggle`,
      method: 'put',
      params: { isActive }
    })
  }
}

// 归并组管理API
export const mergeGroupApi = {
  // 获取票据归并组列表
  getDocumentGroups() {
    return request({
      url: '/api/merge-groups/documents',
      method: 'get'
    })
  },

  // 获取银证归并组列表
  getReceiptGroups() {
    return request({
      url: '/api/merge-groups/receipts',
      method: 'get'
    })
  },

  // 获取票据归并组详情
  getDocumentGroup(groupId) {
    return request({
      url: `/api/merge-groups/documents/${groupId}`,
      method: 'get'
    })
  },

  // 获取银证归并组详情
  getReceiptGroup(groupId) {
    return request({
      url: `/api/merge-groups/receipts/${groupId}`,
      method: 'get'
    })
  },

  // 获取票据归并组内的票据列表
  getDocumentGroupItems(groupId) {
    return request({
      url: `/api/merge-groups/documents/${groupId}/items`,
      method: 'get'
    })
  },

  // 获取银证归并组内的银证列表
  getReceiptGroupItems(groupId) {
    return request({
      url: `/api/merge-groups/receipts/${groupId}/items`,
      method: 'get'
    })
  },

  // 更新票据归并组信息
  updateDocumentGroup(groupId, data) {
    return request({
      url: `/api/merge-groups/documents/${groupId}`,
      method: 'put',
      data
    })
  },

  // 更新银证归并组信息
  updateReceiptGroup(groupId, data) {
    return request({
      url: `/api/merge-groups/receipts/${groupId}`,
      method: 'put',
      data
    })
  }
}

// 默认导出所有API
export default {
  mergeEngineApi,
  relationApi,
  unifiedQueryApi,
  mergeRuleApi,
  mergeGroupApi
}
