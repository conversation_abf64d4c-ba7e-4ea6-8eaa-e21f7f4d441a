/**
 * AI智能会计相关API接口
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
import request from '@/utils/request'

/**
 * AI会计服务API
 */
export const aiAccountingApi = {
  /**
   * 智能匹配银行回单对应的会计科目
   * @param {number} bankReceiptsId 银行回单ID
   * @returns {Promise} 匹配结果
   */
  matchSubjectsForBankReceipts(bankReceiptsId) {
    return request({
      url: `/ai-accounting/match-subjects/bank-receipts/${bankReceiptsId}`,
      method: 'post'
    })
  },

  /**
   * 智能匹配票据对应的会计科目
   * @param {number} billId 票据ID
   * @returns {Promise} 匹配结果
   */
  matchSubjectsForBill(billId) {
    return request({
      url: `/ai-accounting/match-subjects/bill/${billId}`,
      method: 'post'
    })
  },

  /**
   * 自动生成会计凭证
   * @param {number} bankReceiptsId 银行回单ID
   * @returns {Promise} 生成结果
   */
  generateVoucher(bankReceiptsId) {
    return request({
      url: `/ai-accounting/generate-voucher/${bankReceiptsId}`,
      method: 'post'
    })
  },

  /**
   * 批量自动生成凭证
   * @param {Array<number>} bankReceiptsIds 银行回单ID列表
   * @returns {Promise} 批量处理结果
   */
  batchGenerateVouchers(bankReceiptsIds) {
    return request({
      url: '/ai-accounting/batch-generate-vouchers',
      method: 'post',
      data: {
        bankReceiptsIds
      }
    })
  },

  /**
   * 预览匹配结果（不实际生成凭证）
   * @param {number} bankReceiptsId 银行回单ID
   * @returns {Promise} 预览结果
   */
  previewVoucher(bankReceiptsId) {
    return request({
      url: `/ai-accounting/preview-voucher/${bankReceiptsId}`,
      method: 'post'
    })
  },

  /**
   * 更新科目AI描述
   * @param {Object} data 更新数据
   * @param {number} data.subjectId 科目ID
   * @param {string} data.aiDescription AI描述
   * @param {Array<string>} data.keywords 关键词列表
   * @returns {Promise} 更新结果
   */
  updateSubjectAiDescription(data) {
    return request({
      url: '/ai-accounting/update-subject-ai-description',
      method: 'post',
      data
    })
  },

  /**
   * 获取AI匹配统计信息
   * @param {number} days 统计天数，默认30天
   * @returns {Promise} 统计结果
   */
  getMatchingStatistics(days = 30) {
    return request({
      url: '/ai-accounting/matching-statistics',
      method: 'get',
      params: { days }
    })
  },

  /**
   * 获取AI配置信息
   * @returns {Promise} 配置信息
   */
  getAiConfig() {
    return request({
      url: '/ai-accounting/config',
      method: 'get'
    })
  },

  /**
   * 更新AI配置
   * @param {Object} config 配置数据
   * @returns {Promise} 更新结果
   */
  updateAiConfig(config) {
    return request({
      url: '/ai-accounting/config',
      method: 'put',
      data: config
    })
  },

  /**
   * 获取科目匹配模板列表
   * @param {Object} params 查询参数
   * @returns {Promise} 模板列表
   */
  getMatchingTemplates(params = {}) {
    return request({
      url: '/ai-accounting/matching-templates',
      method: 'get',
      params
    })
  },

  /**
   * 创建科目匹配模板
   * @param {Object} template 模板数据
   * @returns {Promise} 创建结果
   */
  createMatchingTemplate(template) {
    return request({
      url: '/ai-accounting/matching-templates',
      method: 'post',
      data: template
    })
  },

  /**
   * 更新科目匹配模板
   * @param {number} templateId 模板ID
   * @param {Object} template 模板数据
   * @returns {Promise} 更新结果
   */
  updateMatchingTemplate(templateId, template) {
    return request({
      url: `/ai-accounting/matching-templates/${templateId}`,
      method: 'put',
      data: template
    })
  },

  /**
   * 删除科目匹配模板
   * @param {number} templateId 模板ID
   * @returns {Promise} 删除结果
   */
  deleteMatchingTemplate(templateId) {
    return request({
      url: `/ai-accounting/matching-templates/${templateId}`,
      method: 'delete'
    })
  },

  /**
   * 获取AI匹配历史记录
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.size 页大小
   * @param {string} params.businessType 业务类型
   * @param {string} params.startDate 开始日期
   * @param {string} params.endDate 结束日期
   * @returns {Promise} 历史记录
   */
  getMatchingHistory(params = {}) {
    return request({
      url: '/ai-accounting/matching-history',
      method: 'get',
      params
    })
  },

  /**
   * 提交用户反馈
   * @param {Object} feedback 反馈数据
   * @param {number} feedback.historyId 历史记录ID
   * @param {string} feedback.feedbackType 反馈类型：correct/incorrect/suggestion
   * @param {string} feedback.comment 反馈内容
   * @param {Object} feedback.correctedResult 修正结果（可选）
   * @returns {Promise} 提交结果
   */
  submitUserFeedback(feedback) {
    return request({
      url: '/ai-accounting/user-feedback',
      method: 'post',
      data: feedback
    })
  },

  /**
   * 训练AI模型（管理员功能）
   * @param {Object} params 训练参数
   * @returns {Promise} 训练结果
   */
  trainAiModel(params = {}) {
    return request({
      url: '/ai-accounting/train-model',
      method: 'post',
      data: params
    })
  },

  /**
   * 获取模型训练状态
   * @returns {Promise} 训练状态
   */
  getTrainingStatus() {
    return request({
      url: '/ai-accounting/training-status',
      method: 'get'
    })
  },

  /**
   * 导出AI匹配报告
   * @param {Object} params 导出参数
   * @param {string} params.startDate 开始日期
   * @param {string} params.endDate 结束日期
   * @param {string} params.format 导出格式：excel/pdf
   * @returns {Promise} 导出结果
   */
  exportMatchingReport(params) {
    return request({
      url: '/ai-accounting/export-report',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  },

  /**
   * 测试AI连接
   * @returns {Promise} 测试结果
   */
  testAiConnection() {
    return request({
      url: '/ai-accounting/test-connection',
      method: 'post'
    })
  },

  /**
   * 获取科目使用频率统计
   * @param {Object} params 查询参数
   * @returns {Promise} 统计结果
   */
  getSubjectUsageStatistics(params = {}) {
    return request({
      url: '/ai-accounting/subject-usage-statistics',
      method: 'get',
      params
    })
  },

  /**
   * 智能推荐科目描述
   * @param {number} subjectId 科目ID
   * @returns {Promise} 推荐结果
   */
  recommendSubjectDescription(subjectId) {
    return request({
      url: `/ai-accounting/recommend-description/${subjectId}`,
      method: 'post'
    })
  },

  /**
   * 批量更新科目AI信息
   * @param {Array} subjects 科目列表
   * @returns {Promise} 更新结果
   */
  batchUpdateSubjectAiInfo(subjects) {
    return request({
      url: '/ai-accounting/batch-update-subject-ai-info',
      method: 'post',
      data: { subjects }
    })
  }
}

/**
 * 票据相关API（已移至关联管理模块）
 * 这些功能现在通过关联管理模块提供
 */
export const billAiApi = {
  // 票据智能处理功能已整合到关联管理模块
  // 保留空对象以避免引用错误
}

export default {
  ...aiAccountingApi,
  bill: billAiApi
}