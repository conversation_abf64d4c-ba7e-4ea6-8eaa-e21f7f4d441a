import <PERSON> from '../js/common/ajax'

export default {
  /**
   * 批量上传文件
   * @param {FormData} formData 包含文件和参数的FormData
   * @param {Function} onProgress 上传进度回调
   * @returns {Promise} 上传结果
   */
  upload(formData, onProgress) {
    return Ajax.post('/batch/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    })
  },

  /**
   * 开始批量识别
   * @param {string} taskId 任务ID
   * @returns {Promise} 识别结果
   */
  startRecognize(taskId) {
    return Ajax.post('/batch/recognize', null, {
      params: { taskId }
    })
  },

  /**
   * 重新识别选中的记录（基于模版）
   * @param {string} taskId 任务ID
   * @param {Array} itemIds 要重新识别的记录ID数组
   * @returns {Promise} 识别结果
   */
  reRecognizeWithTemplate(taskId, itemIds) {
    return Ajax.post('/batch/re-recognize-with-template', {
      taskId,
      itemIds
    })
  },

  /**
   * 查询任务进度
   * @param {string} taskId 任务ID
   * @returns {Promise} 进度信息
   */
  getProgress(taskId) {
    return Ajax.get(`/batch/progress/${taskId}`)
  },

  /**
   * 获取任务列表
   * @param {Object} params 查询参数
   * @returns {Promise} 任务列表
   */
  getTasks(params = {}) {
    return Ajax.get('/batch/tasks', { params })
  },

  /**
   * 获取任务列表（分页）
   * @param {Object} params 查询参数
   * @returns {Promise} 任务列表
   */
  getTaskList(params = {}) {
    // 构建查询字符串，确保参数值正确
    const queryParams = new URLSearchParams()

    // 添加页码和大小参数，确保是数字
    if (params.page !== undefined && params.page !== null) {
      queryParams.append('page', String(params.page))
    }
    if (params.size !== undefined && params.size !== null) {
      queryParams.append('size', String(params.size))
    }

    // 添加字符串参数，只有非空时才添加
    if (params.importType && params.importType !== '') {
      queryParams.append('importType', String(params.importType))
    }
    if (params.status && params.status !== '') {
      queryParams.append('status', String(params.status))
    }
    if (params.startDate && params.startDate !== '') {
      queryParams.append('startDate', String(params.startDate))
    }
    if (params.endDate && params.endDate !== '') {
      queryParams.append('endDate', String(params.endDate))
    }

    const queryString = queryParams.toString()
    const url = queryString ? `/batch/task-list?${queryString}` : '/batch/task-list'

    console.log('构建的URL:', url)
    return Ajax.get(url)
  },

  /**
   * 获取任务详情
   * @param {string} taskId 任务ID
   * @returns {Promise} 任务详情
   */
  getTaskDetail(taskId) {
    return Ajax.get(`/batch/task/${taskId}`)
  },

  /**
   * 获取任务的识别结果列表
   * @param {string} taskId 任务ID
   * @param {Object} params 查询参数
   * @returns {Promise} 识别结果列表
   */
  getRecognitionResults(taskId, params = {}) {
    return Ajax.get(`/batch/results/${taskId}`, { params })
  },

  /**
   * 更新识别结果
   * @param {number} detailId 明细ID
   * @param {Object} data 更新的数据
   * @returns {Promise} 更新结果
   */
  updateRecognitionResult(detailId, data) {
    return Ajax.put(`/batch/result/${detailId}`, data)
  },

  /**
   * 更新单个项目数据（编辑功能）
   * @param {number} detailId 明细ID
   * @param {Object} data 更新的数据
   * @returns {Promise} 更新结果
   */
  updateItem(detailId, data) {
    return Ajax.put(`/batch/result/${detailId}`, { data })
  },

  /**
   * 批量更新识别结果
   * @param {string} taskId 任务ID
   * @param {Array} results 结果数组
   * @returns {Promise} 更新结果
   */
  batchUpdateResults(taskId, results) {
    return Ajax.put(`/batch/results/${taskId}`, { results })
  },

  /**
   * 重新识别单个图片
   * @param {number} detailId 明细ID
   * @returns {Promise} 识别结果
   */
  retryRecognition(detailId) {
    return Ajax.post(`/batch/retry/${detailId}`)
  },

  /**
   * 批量保存到业务表
   * @param {string} taskId 任务ID
   * @param {Array} selectedIds 选中的明细ID数组
   * @returns {Promise} 保存结果
   */
  batchSave(taskId, selectedIds = []) {
    return Ajax.post('/batch/save', {
      taskId,
      selectedIds
    })
  },

  /**
   * 取消任务
   * @param {string} taskId 任务ID
   * @returns {Promise} 取消结果
   */
  cancelTask(taskId) {
    return Ajax.post(`/batch/cancel/${taskId}`)
  },

  /**
   * 删除任务
   * @param {string} taskId 任务ID
   * @returns {Promise} 删除结果
   */
  deleteTask(taskId) {
    return Ajax.delete(`/batch/task/${taskId}`)
  },

  /**
   * 导出识别结果
   * @param {string} taskId 任务ID
   * @param {string} format 导出格式 (excel/csv)
   * @returns {Promise} 导出结果
   */
  exportResults(taskId, format = 'excel') {
    return Ajax.get(`/batch/export/${taskId}`, {
      params: { format },
      responseType: 'blob'
    })
  },

  /**
   * 获取任务统计信息
   * @param {Object} params 查询参数
   * @returns {Promise} 统计信息
   */
  getTaskStats(params = {}) {
    return Ajax.get('/batch/stats', { params })
  },

  /**
   * 拆分图片
   * @param {number} detailId 明细ID
   * @returns {Promise} 拆分结果
   */
  splitImage(detailId) {
    return Ajax.post('/batch/split-image', null, {
      params: { detailId }
    })
  },

  /**
   * 更改单个记录的识别类型并重新识别
   * @param {number} itemId 明细ID
   * @param {string} newType 新的识别类型：BANK_RECEIPT-银行回单，INVOICE-发票
   * @returns {Promise} 重新识别结果
   */
  changeTypeAndReRecognize(itemId, newType) {
    return Ajax.post('/batch/change-type-and-recognize', null, {
      params: { itemId, newType }
    })
  },


}
