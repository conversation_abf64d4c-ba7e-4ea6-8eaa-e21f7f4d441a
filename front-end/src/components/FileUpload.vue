<template>
  <div class="file-upload-wrapper">
    <FormItem :label="label" :prop="prop">
      <div class="file-upload-container">
        <input 
          type="file" 
          ref="fileInput" 
          @change="handleFileChange" 
          :disabled="disabled" 
          :accept="accept"
          style="display: none;"
        >
        <Button 
          @click="$refs.fileInput.click()" 
          :disabled="disabled"
          icon="h-icon-upload"
        >
          选择文件
        </Button>
        <span v-if="!fileName && !disabled" class="upload-tip">{{ uploadTip }}</span>
      </div>
      
      <div v-if="fileName" class="uploaded-file">
        <span class="file-info">
          <i class="h-icon-file"></i>
          {{ fileName }}
        </span>
        <div class="file-actions">
          <Button size="s" @click="previewFile" v-if="isPreviewable">预览</Button>
          <Button size="s" @click="recognizeFile" v-if="isRecognizable && enableRecognition" :loading="recognizing">识别</Button>
          <Button size="s" color="danger" @click="removeFile" v-if="!disabled">删除</Button>
        </div>
      </div>
      
      <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{width: uploadProgress + '%'}"></div>
        </div>
        <span class="progress-text">{{ uploadProgress }}%</span>
      </div>
    </FormItem>
    
    <!-- 文件预览模态框 -->
    <Modal v-model="showPreviewModal" title="文件预览" width="80%" :mask-closable="false">
      <div class="preview-container">
        <div v-if="isImageFile" class="image-preview">
          <img :src="fileUrl" :alt="fileName" class="preview-image" />
        </div>
        <div v-else-if="isPdfFile" class="pdf-preview">
          <iframe :src="fileUrl" class="preview-iframe"></iframe>
        </div>
        <div v-else class="unsupported-preview">
          <p>该文件类型不支持预览</p>
          <Button @click="downloadFile">下载文件</Button>
        </div>
      </div>
      <div slot="footer">
        <Button @click="showPreviewModal = false">关闭</Button>
        <Button type="primary" @click="downloadFile">下载</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'FileUpload',
  props: {
    label: {
      type: String,
      default: '附件上传'
    },
    prop: {
      type: String,
      default: 'fileUpload'
    },
    value: {
      type: String,
      default: ''
    },
    fileName: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: '.pdf,.jpg,.jpeg,.png'
    },
    uploadTip: {
      type: String,
      default: '支持PDF、图片格式，最大10MB'
    },
    maxSize: {
      type: Number,
      default: 10 * 1024 * 1024 // 10MB
    },
    allowedTypes: {
      type: Array,
      default: () => ['pdf', 'jpg', 'jpeg', 'png']
    },
    uploadApi: {
      type: String,
      required: true
    },
    recognitionApi: { // 新增 recognitionApi prop
      type: String,
      default: '' // 可以设置一个默认的识别API，或者留空
    },
    enableRecognition: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      uploadProgress: 0,
      showPreviewModal: false,
      recognizing: false,
      selectedFile: null
    }
  },
  computed: {
    fileUrl() {
      return this.value
    },
    isImageFile() {
      if (!this.fileName) return false
      const ext = this.fileName.toLowerCase().split('.').pop()
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)
    },
    isPdfFile() {
      if (!this.fileName) return false
      const ext = this.fileName.toLowerCase().split('.').pop()
      return ext === 'pdf'
    },
    isPreviewable() {
      return this.isImageFile || this.isPdfFile
    },
    isRecognizable() {
      if (!this.fileName) return false
      const ext = this.fileName.toLowerCase().split('.').pop()
      return ['jpg', 'jpeg', 'png', 'pdf'].includes(ext)
    }
  },
  methods: {
    handleFileChange(event) {
      const file = event.target.files[0]
      if (!file) return
      
      // 文件大小检查
      if (file.size > this.maxSize) {
        this.$Message.error(`文件大小不能超过${Math.round(this.maxSize / 1024 / 1024)}MB`)
        return
      }
      
      // 文件类型检查
      const fileExt = file.name.toLowerCase().split('.').pop()
      if (!this.allowedTypes.includes(fileExt)) {
        this.$Message.error('不支持的文件格式')
        return
      }
      
      this.selectedFile = file
      this.$emit('update:fileName', file.name)
      this.uploadFile(file)
    },
    
    uploadFile(file) {
      const formData = new FormData()
      formData.append('file', file)
      
      this.uploadProgress = 0
      
      this.$api.upload.file(formData, this.uploadApi).then(({data}) => {
        this.$emit('input', data.filePath)
        this.uploadProgress = 100
        this.$Message('文件上传成功')
        
        setTimeout(() => {
          this.uploadProgress = 0
        }, 1000)
      }).catch((error) => {
        this.$Message.error('文件上传失败: ' + (error.message || '未知错误'))
        this.removeFile()
      })
    },
    
    removeFile() {
      if (this.value) {
        this.$api.upload.deleteFile(this.value).catch(() => {})
      }
      
      this.selectedFile = null
      this.$emit('update:fileName', '')
      this.$emit('input', '')
      this.uploadProgress = 0
      this.$refs.fileInput.value = ''
    },
    
    previewFile() {
      if (this.value) {
        this.showPreviewModal = true
      }
    },
    
    downloadFile() {
      if (this.value && this.fileName) {
        const link = document.createElement('a')
        link.href = this.value
        link.download = this.fileName
        link.click()
      }
    },
    
    recognizeFile() {
      if (!this.value) {
        this.$Message.error('请先上传文件')
        return
      }
      
      this.recognizing = true
      this.$Message.loading('正在识别文件内容...')
      
      const apiService = this.$api.upload; // 或者其他相关的API服务
      let recognizePromise;

      if (this.recognitionApi) {
        // 如果 this.recognitionApi 不为空，则使用它，否则使用默认的识别接口
        const apiUrl = this.recognitionApi || '/api/upload/recognize'; // 如果 recognitionApi 为空，则使用默认
        
        // 例如，添加一个新方法：
        // recognizeWithEndpoint: (endpoint, fileUrl) => post(endpoint, { fileUrl })
        // 然后在这里调用：
        if (this.recognitionApi) {
            recognizePromise = this.$api.upload.recognizeWithEndpoint(this.recognitionApi, this.value);
        } else {
            // 如果没有提供 recognitionApi，则调用默认的银行回单识别
            recognizePromise = this.$api.upload.recognizeFile(this.value);
        }

      } else {
        // 如果没有提供 recognitionApi，则调用默认的银行回单识别 (旧逻辑)
        recognizePromise = this.$api.upload.recognizeFile(this.value);
      }

      recognizePromise.then(({data}) => {
        this.$Message.success('文件识别成功')
        this.$emit('recognition-result', data)
      }).catch(error => {
        this.$Message.error('文件识别失败：' + (error.message || '未知错误'))
      }).finally(() => {
        this.recognizing = false
      })
    }
  }
}
</script>

<style scoped>
.file-upload-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
}

.uploaded-file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.upload-progress {
  margin-top: 8px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #1890ff;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

.preview-container {
  text-align: center;
  max-height: 70vh;
  overflow: auto;
}

.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pdf-preview {
  width: 100%;
  height: 60vh;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 4px;
}

.unsupported-preview {
  padding: 40px;
  color: #666;
}

.unsupported-preview p {
  margin-bottom: 16px;
  font-size: 16px;
}
</style>