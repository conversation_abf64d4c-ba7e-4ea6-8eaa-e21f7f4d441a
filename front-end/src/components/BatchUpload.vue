<template>
  <div class="batch-upload">
    <!-- 上传区域 -->
    <div class="upload-section">
      <div
        class="upload-area"
        :class="{ 'drag-over': isDragOver }"
        @drop="handleDrop"
        @dragover.prevent="isDragOver = true"
        @dragleave="isDragOver = false"
      >
        <!-- 文件选择输入框 -->
        <input
          ref="fileInput"
          type="file"
          multiple
          accept=".pdf,.jpg,.jpeg,.png,.bmp"
          @change="handleFileSelect"
          style="display: none"
        >
        <!-- 文件夹选择输入框 -->
        <input
          ref="folderInput"
          type="file"
          webkitdirectory
          @change="handleFolderSelect"
          style="display: none"
        >
        
        <div class="upload-content">
          <i class="h-icon-upload upload-icon"></i>
          <h3>批量上传文件</h3>
          <p class="upload-hint">
            拖拽文件到此处或选择上传方式<br>
            支持PDF、JPG、PNG等格式，可同时选择多个文件
          </p>

          <!-- 上传方式选择按钮 -->
          <div class="upload-buttons">
            <button type="button" class="upload-btn" @click="selectFiles">
              <i class="h-icon-file"></i>
              选择文件
            </button>
            <button type="button" class="upload-btn" @click="selectFolder">
              <i class="h-icon-folder"></i>
              选择文件夹
            </button>
          </div>

          <div class="upload-tips">
            <span class="tip-item">• PDF文件会自动解析为多张图片</span>
            <span class="tip-item">• 建议单个文件不超过10MB</span>
            <span class="tip-item">• 最多同时上传20个文件</span>
            <span class="tip-item">• 文件夹上传会包含所有子文件</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件列表 -->
    <div class="file-list-section" v-if="fileList.length > 0">
      <div class="section-header">
        <h4>文件列表 ({{ fileList.length }})</h4>
        <div class="header-actions">
          <button class="btn-clear" @click="clearAll">清空</button>
        </div>
      </div>
      
      <div class="file-list">
        <div 
          v-for="(file, index) in fileList" 
          :key="file.id" 
          class="file-item"
          :class="{ 'error': file.status === 'error' }"
        >
          <div class="file-info">
            <i :class="getFileIcon(file.type)" class="file-icon"></i>
            <div class="file-details">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-meta">
                {{ formatFileSize(file.size) }} • {{ file.type.toUpperCase() }}
                <span v-if="file.status === 'error'" class="error-text">
                  - {{ file.error }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="file-actions">
            <span class="file-status" :class="file.status">
              {{ getStatusText(file.status) }}
            </span>
            <button class="btn-remove" @click="removeFile(index)">
              <i class="h-icon-close"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions-section" v-if="fileList.length > 0">
      <div class="upload-config">
        <div class="config-row">
          <label>任务名称：</label>
          <input
            v-model="taskName"
            type="text"
            class="task-name-input"
            placeholder="可选，不填写将自动生成"
          >
        </div>
        <div class="config-row">
          <label>单页单据数量：</label>
          <select v-model="receiptsPerPage" class="receipts-per-page-select">
            <option value="1">1张</option>
            <option value="2">2张</option>
            <option value="3">3张</option>
            <option value="4">4张</option>
          </select>
          <span class="config-hint">{{ importType === 'BANK_RECEIPT' ? '每页包含的银行回单数量' : '每页包含的发票数量' }}</span>
        </div>
      </div>
      
      <div class="action-buttons">
        <button 
          class="btn-upload" 
          :disabled="uploading || fileList.length === 0"
          @click="startUpload"
        >
          <i v-if="uploading" class="h-icon-loading"></i>
          {{ uploading ? '上传中...' : '开始上传' }}
        </button>
        <button class="btn-cancel" @click="cancel">取消</button>
      </div>
    </div>

    <!-- 上传进度 -->
    <div class="progress-section" v-if="uploading">
      <div class="progress-info">
        <span>正在上传文件...</span>
        <span>{{ uploadProgress }}%</span>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { batchUpload, validateFiles } from '@/utils/upload'

export default {
  name: 'BatchUpload',
  props: {
    // 默认导入类型
    defaultType: {
      type: String,
      default: 'BANK_RECEIPT'
    }
  },
  data() {
    return {
      fileList: [],
      isDragOver: false,
      uploading: false,
      uploadProgress: 0,
      taskName: '',
      receiptsPerPage: 1, // 默认每页1张单据
      maxFiles: 20,
      maxFileSize: 10 * 1024 * 1024 // 10MB
    }
  },
  computed: {
    canUpload() {
      return this.fileList.length > 0 && !this.uploading
    },

    importType() {
      return this.defaultType || 'BANK_RECEIPT'
    }
  },
  methods: {
    selectFiles() {
      this.$refs.fileInput.click()
    },

    selectFolder() {
      this.$refs.folderInput.click()
    },

    handleFileSelect(event) {
      const files = Array.from(event.target.files)
      this.addFiles(files)
      // 清空input，允许重复选择相同文件
      event.target.value = ''
    },

    handleFolderSelect(event) {
      const files = Array.from(event.target.files)
      console.log('选择的文件夹包含文件:', files.map(f => f.name))
      this.addFiles(files)
      // 清空input，允许重复选择相同文件夹
      event.target.value = ''
    },
    
    handleDrop(event) {
      event.preventDefault()
      this.isDragOver = false
      
      const files = Array.from(event.dataTransfer.files)
      this.addFiles(files)
    },
    
    addFiles(files) {
      if (this.fileList.length + files.length > this.maxFiles) {
        this.$Message.error(`最多只能上传${this.maxFiles}个文件`)
        return
      }

      // 使用新的文件验证工具
      const validation = validateFiles(files, 50, ['pdf', 'jpg', 'jpeg', 'png', 'bmp'])
      if (!validation.valid) {
        this.$Message.error(validation.message)
        return
      }

      files.forEach(file => {
        const fileItem = {
          id: Date.now() + Math.random(),
          name: file.name,
          size: file.size,
          type: this.getFileType(file.name),
          file: file,
          status: 'ready',
          error: null
        }

        this.fileList.push(fileItem)
      })
    },
    
    validateFile(file) {
      // 检查文件大小
      if (file.size > this.maxFileSize) {
        return {
          valid: false,
          error: '文件大小超过10MB限制'
        }
      }
      
      // 检查文件类型
      const allowedTypes = ['pdf', 'jpg', 'jpeg', 'png', 'bmp']
      const fileExtension = file.name.split('.').pop().toLowerCase()
      
      if (!allowedTypes.includes(fileExtension)) {
        return {
          valid: false,
          error: '不支持的文件格式'
        }
      }
      
      return { valid: true }
    },
    
    getFileType(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      return extension === 'pdf' ? 'pdf' : 'image'
    },
    
    getFileIcon(type) {
      return type === 'pdf' ? 'h-icon-file-pdf' : 'h-icon-file-image'
    },
    
    getStatusText(status) {
      const statusMap = {
        'ready': '就绪',
        'uploading': '上传中',
        'success': '成功',
        'error': '错误'
      }
      return statusMap[status] || status
    },
    
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    removeFile(index) {
      this.fileList.splice(index, 1)
    },
    
    clearAll() {
      this.fileList = []
    },
    
    async startUpload() {
      if (!this.canUpload) return
      
      try {
        this.uploading = true
        this.uploadProgress = 0
        
        // 标记文件为上传中状态
        this.fileList.forEach(fileItem => {
          if (fileItem.status !== 'error') {
            fileItem.status = 'uploading'
          }
        })
        
        // 使用新的上传工具
        const files = this.fileList
          .filter(fileItem => fileItem.status !== 'error')
          .map(fileItem => fileItem.file)

        const response = await batchUpload(
          files,
          this.importType,
          this.taskName.trim() || null,
          parseInt(this.receiptsPerPage), // 传递单页单据数量
          (progress) => {
            this.uploadProgress = progress
          }
        )

        if (response.code === 200) {
          // 显示成功提示
          this.$Message.success(`文件上传成功！任务名称: ${response.data.taskName}，文件数量: ${response.data.totalFiles} 个`)

          // 询问是否查看进度
          this.$Confirm('文件上传成功！系统正在后台处理，是否查看处理进度？').then(() => {
            // 跳转到进度页面
            this.$router.push(`/batch/progress/${response.data.taskId}`)
          }).catch(() => {
            // 清空文件列表，继续上传
            this.fileList = []
            this.taskName = ''
            this.$Message.info('您可以继续上传其他文件，或稍后在菜单中查看处理结果')
          })

          // 触发上传完成事件
          this.$emit('upload-success', response.data)

        } else {
          throw new Error(response.message || '上传失败')
        }
        
      } catch (error) {
        console.error('上传失败:', error)

        // 处理特定错误类型
        let errorMessage = '上传失败'
        if (error.code === 'ECONNABORTED') {
          errorMessage = '上传超时，请检查网络连接或减少文件数量'
        } else if (error.response && error.response.status === 413) {
          errorMessage = '文件过大，请选择较小的文件'
        } else if (error.message) {
          errorMessage = error.message
        }

        this.$Message.error(errorMessage)

        // 重置文件状态
        this.fileList.forEach(fileItem => {
          if (fileItem.status === 'uploading') {
            fileItem.status = 'ready'
          }
        })
        
      } finally {
        this.uploading = false
        this.uploadProgress = 0
      }
    },
    
    cancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.batch-upload {
  max-width: 800px;
  margin: 0 auto;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.upload-area:hover,
.upload-area.drag-over {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-content {
  pointer-events: none;
}

.upload-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.upload-area:hover .upload-icon,
.upload-area.drag-over .upload-icon {
  color: #1890ff;
}

.upload-hint {
  color: #666;
  margin: 16px 0;
  line-height: 1.5;
}

.upload-buttons {
  margin: 20px 0;
  display: flex;
  gap: 16px;
  justify-content: center;
  pointer-events: auto;
}

.upload-btn {
  padding: 12px 24px;
  border: 2px solid #007bff;
  background: white;
  color: #007bff;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-btn:hover {
  background: #007bff;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.upload-btn i {
  font-size: 16px;
}

.upload-tips {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 16px;
}

.tip-item {
  font-size: 12px;
  color: #999;
}

.file-list-section {
  margin-top: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  color: #333;
}

.btn-clear {
  background: none;
  border: 1px solid #d9d9d9;
  padding: 4px 12px;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
}

.btn-clear:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.file-list {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: white;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item.error {
  background: #fff2f0;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 24px;
  margin-right: 12px;
  color: #1890ff;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #999;
}

.error-text {
  color: #ff4d4f;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background: #f0f0f0;
  color: #666;
}

.file-status.ready {
  background: #e6f7ff;
  color: #1890ff;
}

.file-status.success {
  background: #f6ffed;
  color: #52c41a;
}

.file-status.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.btn-remove {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  padding: 4px;
}

.btn-remove:hover {
  color: #ff4d4f;
}

.actions-section {
  margin-top: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.upload-config {
  margin-bottom: 16px;
}

.config-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.config-row:last-child {
  margin-bottom: 0;
}

.upload-config label {
  font-weight: 500;
  color: #333;
  min-width: 120px;
}

.task-name-input {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.receipts-per-page-select {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  min-width: 80px;
}

.config-hint {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn-upload,
.btn-cancel {
  padding: 8px 24px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid;
  transition: all 0.3s ease;
}

.btn-upload {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.btn-upload:hover:not(:disabled) {
  background: #40a9ff;
  border-color: #40a9ff;
}

.btn-upload:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
  cursor: not-allowed;
}

.btn-cancel {
  background: white;
  border-color: #d9d9d9;
  color: #666;
}

.btn-cancel:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.progress-section {
  margin-top: 16px;
  padding: 16px;
  background: #f0f8ff;
  border-radius: 6px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}

.progress-bar {
  height: 6px;
  background: #e8e8e8;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}
</style>
