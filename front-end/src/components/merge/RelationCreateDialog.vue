<template>
  <el-dialog title="创建关联关系" :visible.sync="visible" width="600px" @close="handleClose">
    <el-form :model="relationForm" :rules="relationRules" ref="relationForm" label-width="120px">
      <!-- 源实体信息 -->
      <div class="entity-section">
        <h4>源实体</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="类型" prop="sourceType">
              <el-select v-model="relationForm.sourceType" placeholder="选择源类型" @change="onSourceTypeChange">
                <el-option label="票据" value="DOCUMENT"></el-option>
                <el-option label="票据组" value="DOCUMENT_GROUP"></el-option>
                <el-option label="银证" value="RECEIPT"></el-option>
                <el-option label="银证组" value="RECEIPT_GROUP"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="选择实体" prop="sourceId">
              <el-select 
                v-model="relationForm.sourceId" 
                placeholder="选择源实体" 
                filterable 
                remote
                :remote-method="searchSourceEntities"
                :loading="sourceLoading"
                @change="onSourceChange">
                <el-option
                  v-for="item in sourceOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id">
                  <span style="float: left">{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">¥{{ item.amount }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="selectedSource" class="entity-info">
          <el-tag size="small">{{ getEntityTypeText(relationForm.sourceType) }}</el-tag>
          <span class="entity-detail">{{ selectedSource.label }} - ¥{{ selectedSource.amount }}</span>
        </div>
      </div>

      <!-- 目标实体信息 -->
      <div class="entity-section">
        <h4>目标实体</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="类型" prop="targetType">
              <el-select v-model="relationForm.targetType" placeholder="选择目标类型" @change="onTargetTypeChange">
                <el-option label="票据" value="DOCUMENT"></el-option>
                <el-option label="票据组" value="DOCUMENT_GROUP"></el-option>
                <el-option label="银证" value="RECEIPT"></el-option>
                <el-option label="银证组" value="RECEIPT_GROUP"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="选择实体" prop="targetId">
              <el-select 
                v-model="relationForm.targetId" 
                placeholder="选择目标实体" 
                filterable 
                remote
                :remote-method="searchTargetEntities"
                :loading="targetLoading"
                @change="onTargetChange">
                <el-option
                  v-for="item in targetOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id">
                  <span style="float: left">{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">¥{{ item.amount }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="selectedTarget" class="entity-info">
          <el-tag size="small">{{ getEntityTypeText(relationForm.targetType) }}</el-tag>
          <span class="entity-detail">{{ selectedTarget.label }} - ¥{{ selectedTarget.amount }}</span>
        </div>
      </div>

      <!-- 关联信息 -->
      <div class="relation-section">
        <h4>关联信息</h4>
        <el-form-item label="关联类型" prop="relationType">
          <el-select v-model="relationForm.relationType" placeholder="选择关联类型">
            <el-option label="关联" value="ASSOCIATED"></el-option>
            <el-option label="匹配" value="MATCHED"></el-option>
            <el-option label="部分关联" value="PARTIAL"></el-option>
            <el-option label="完全关联" value="FULL"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="关联金额">
          <el-input-number 
            v-model="relationForm.relationAmount" 
            :precision="2" 
            :max="maxRelationAmount"
            placeholder="可选，部分关联时使用">
          </el-input-number>
          <span v-if="maxRelationAmount > 0" class="amount-hint">
            （最大金额：¥{{ maxRelationAmount }}）
          </span>
        </el-form-item>
        
        <el-form-item label="关联备注">
          <el-input 
            v-model="relationForm.relationNote" 
            type="textarea" 
            :rows="3"
            placeholder="可选，描述关联关系的详细信息">
          </el-input>
        </el-form-item>
      </div>

      <!-- 验证结果 -->
      <div v-if="validationResult" class="validation-result">
        <el-alert
          :title="validationResult.valid ? '关联关系有效' : '关联关系无效'"
          :type="validationResult.valid ? 'success' : 'error'"
          :description="validationResult.message"
          show-icon>
        </el-alert>
      </div>
    </el-form>

    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button @click="validateRelation" :loading="validating">验证关系</el-button>
      <el-button type="primary" @click="createRelation" :loading="creating" :disabled="!canCreate">
        创建关联
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { relationApi, unifiedQueryApi, mergeGroupApi } from '@/api/merge-relation'

export default {
  name: 'RelationCreateDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    // 预设的源实体信息
    presetSource: {
      type: Object,
      default: null
    }
  },
  
  data() {
    return {
      creating: false,
      validating: false,
      sourceLoading: false,
      targetLoading: false,
      
      relationForm: {
        sourceType: '',
        sourceId: '',
        targetType: '',
        targetId: '',
        relationType: 'ASSOCIATED',
        relationAmount: null,
        relationNote: ''
      },
      
      relationRules: {
        sourceType: [
          { required: true, message: '请选择源类型', trigger: 'change' }
        ],
        sourceId: [
          { required: true, message: '请选择源实体', trigger: 'change' }
        ],
        targetType: [
          { required: true, message: '请选择目标类型', trigger: 'change' }
        ],
        targetId: [
          { required: true, message: '请选择目标实体', trigger: 'change' }
        ],
        relationType: [
          { required: true, message: '请选择关联类型', trigger: 'change' }
        ]
      },
      
      sourceOptions: [],
      targetOptions: [],
      selectedSource: null,
      selectedTarget: null,
      validationResult: null
    }
  },
  
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    
    maxRelationAmount() {
      if (!this.selectedSource || !this.selectedTarget) return 0
      return Math.min(this.selectedSource.amount || 0, this.selectedTarget.amount || 0)
    },
    
    canCreate() {
      return this.relationForm.sourceId && 
             this.relationForm.targetId && 
             this.relationForm.sourceId !== this.relationForm.targetId &&
             (!this.validationResult || this.validationResult.valid)
    }
  },
  
  watch: {
    visible(val) {
      if (val) {
        this.resetForm()
        this.initPresetSource()
      }
    }
  },
  
  methods: {
    // 初始化预设源实体
    initPresetSource() {
      if (this.presetSource) {
        this.relationForm.sourceType = this.presetSource.entityType
        this.relationForm.sourceId = this.presetSource.entityId
        this.selectedSource = {
          id: this.presetSource.entityId,
          label: this.presetSource.entityNo || this.presetSource.entityName,
          amount: this.presetSource.amount || 0
        }
      }
    },
    
    // 源类型改变
    onSourceTypeChange() {
      this.relationForm.sourceId = ''
      this.selectedSource = null
      this.sourceOptions = []
      this.validationResult = null
    },
    
    // 目标类型改变
    onTargetTypeChange() {
      this.relationForm.targetId = ''
      this.selectedTarget = null
      this.targetOptions = []
      this.validationResult = null
    },
    
    // 源实体改变
    onSourceChange() {
      this.selectedSource = this.sourceOptions.find(item => item.id === this.relationForm.sourceId)
      this.validationResult = null
    },
    
    // 目标实体改变
    onTargetChange() {
      this.selectedTarget = this.targetOptions.find(item => item.id === this.relationForm.targetId)
      this.validationResult = null
    },
    
    // 搜索源实体
    async searchSourceEntities(query) {
      if (!this.relationForm.sourceType) return
      
      this.sourceLoading = true
      try {
        const entities = await this.searchEntities(this.relationForm.sourceType, query)
        this.sourceOptions = entities
      } catch (error) {
        console.error('搜索源实体失败:', error)
      } finally {
        this.sourceLoading = false
      }
    },
    
    // 搜索目标实体
    async searchTargetEntities(query) {
      if (!this.relationForm.targetType) return
      
      this.targetLoading = true
      try {
        const entities = await this.searchEntities(this.relationForm.targetType, query)
        this.targetOptions = entities
      } catch (error) {
        console.error('搜索目标实体失败:', error)
      } finally {
        this.targetLoading = false
      }
    },
    
    // 搜索实体
    async searchEntities(entityType, query) {
      try {
        let response
        const queryData = {
          condition: {
            keyword: query,
            entityTypes: [entityType]
          },
          pageParam: {
            pageNum: 1,
            pageSize: 20
          }
        }
        
        if (entityType.includes('DOCUMENT')) {
          response = await unifiedQueryApi.queryDocuments(queryData)
        } else if (entityType.includes('RECEIPT')) {
          response = await unifiedQueryApi.queryReceipts(queryData)
        }
        
        const records = response.data.records || []
        return records.map(record => ({
          id: record.entityId,
          label: record.entityNo,
          amount: record.amount || 0
        }))
      } catch (error) {
        console.error('搜索实体失败:', error)
        return []
      }
    },
    
    // 验证关联关系
    async validateRelation() {
      if (!this.relationForm.sourceId || !this.relationForm.targetId) {
        this.$Message.warning('请先选择源实体和目标实体')
        return
      }
      
      this.validating = true
      try {
        const response = await relationApi.validateRelation(
          this.relationForm.sourceType,
          this.relationForm.sourceId,
          this.relationForm.targetType,
          this.relationForm.targetId
        )
        
        this.validationResult = {
          valid: response.data,
          message: response.data ? '关联关系验证通过' : '关联关系验证失败，请检查实体是否存在或是否已有关联'
        }
      } catch (error) {
        this.validationResult = {
          valid: false,
          message: '验证失败: ' + error.message
        }
      } finally {
        this.validating = false
      }
    },
    
    // 创建关联关系
    async createRelation() {
      try {
        await this.$refs.relationForm.validate()
        
        this.creating = true
        
        const response = await relationApi.createRelation(this.relationForm)
        
        this.$Message.success('创建关联关系成功')
        this.$emit('relation-created', {
          relationId: response.data,
          ...this.relationForm
        })
        
        this.handleClose()
        
      } catch (error) {
        if (error.message) {
          this.$Message.error('创建关联关系失败: ' + error.message)
        }
      } finally {
        this.creating = false
      }
    },
    
    // 重置表单
    resetForm() {
      this.relationForm = {
        sourceType: '',
        sourceId: '',
        targetType: '',
        targetId: '',
        relationType: 'ASSOCIATED',
        relationAmount: null,
        relationNote: ''
      }
      this.sourceOptions = []
      this.targetOptions = []
      this.selectedSource = null
      this.selectedTarget = null
      this.validationResult = null
      
      if (this.$refs.relationForm) {
        this.$refs.relationForm.clearValidate()
      }
    },
    
    // 获取实体类型文本
    getEntityTypeText(type) {
      const textMap = {
        'DOCUMENT': '票据',
        'DOCUMENT_GROUP': '票据组',
        'RECEIPT': '银证',
        'RECEIPT_GROUP': '银证组'
      }
      return textMap[type] || type
    },
    
    // 关闭对话框
    handleClose() {
      this.visible = false
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.entity-section, .relation-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.entity-section h4, .relation-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.entity-info {
  margin-top: 10px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.entity-detail {
  margin-left: 10px;
  color: #606266;
}

.amount-hint {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.validation-result {
  margin-top: 15px;
}
</style>
