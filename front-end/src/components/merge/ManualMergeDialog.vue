<template>
  <el-dialog :title="dialogTitle" :visible.sync="visible" width="800px" @close="handleClose">
    <div class="manual-merge-dialog">
      <!-- 选中的项目列表 -->
      <div class="selected-items">
        <h4>已选择的{{ entityTypeName }}（{{ selectedItems.length }}项）</h4>
        <el-table :data="selectedItems" max-height="300">
          <el-table-column :prop="numberField" :label="numberLabel" width="150"></el-table-column>
          <el-table-column :prop="dateField" :label="dateLabel" width="120"></el-table-column>
          <el-table-column prop="amount" label="金额" width="120">
            <template slot-scope="scope">
              ¥{{ scope.row.amount }}
            </template>
          </el-table-column>
          <el-table-column prop="summary" label="摘要" min-width="200"></el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="removeItem(scope.$index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 归并信息 -->
      <div class="merge-info">
        <el-form :model="mergeForm" :rules="mergeRules" ref="mergeForm" label-width="100px">
          <el-form-item label="组名称" prop="groupName">
            <el-input v-model="mergeForm.groupName" placeholder="请输入归并组名称"></el-input>
          </el-form-item>
          <el-form-item label="组摘要">
            <el-input v-model="mergeForm.groupSummary" type="textarea" placeholder="可选，描述这个归并组"></el-input>
          </el-form-item>
        </el-form>

        <!-- 统计信息 -->
        <div class="merge-statistics">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-item">
                <span class="stat-label">项目数量：</span>
                <span class="stat-value">{{ selectedItems.length }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <span class="stat-label">总金额：</span>
                <span class="stat-value">¥{{ totalAmount }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <span class="stat-label">平均金额：</span>
                <span class="stat-value">¥{{ averageAmount }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="confirmMerge" :loading="merging" :disabled="selectedItems.length < 2">
        确认归并
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mergeEngineApi } from '@/api/merge-relation'

export default {
  name: 'ManualMergeDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    entityType: {
      type: String,
      required: true,
      validator: value => ['DOCUMENT', 'RECEIPT'].includes(value)
    },
    items: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      merging: false,
      mergeForm: {
        groupName: '',
        groupSummary: ''
      },
      mergeRules: {
        groupName: [
          { required: true, message: '请输入组名称', trigger: 'blur' },
          { min: 2, max: 50, message: '组名称长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    
    selectedItems() {
      return this.items || []
    },
    
    entityTypeName() {
      return this.entityType === 'DOCUMENT' ? '票据' : '银证'
    },
    
    dialogTitle() {
      return `手动${this.entityTypeName}归并`
    },
    
    numberField() {
      return this.entityType === 'DOCUMENT' ? 'billNo' : 'receiptsNo'
    },
    
    numberLabel() {
      return this.entityType === 'DOCUMENT' ? '票据编号' : '银证编号'
    },
    
    dateField() {
      return this.entityType === 'DOCUMENT' ? 'billDate' : 'receiptsDate'
    },
    
    dateLabel() {
      return this.entityType === 'DOCUMENT' ? '票据日期' : '银证日期'
    },
    
    totalAmount() {
      return this.selectedItems.reduce((sum, item) => sum + (item.amount || 0), 0).toFixed(2)
    },
    
    averageAmount() {
      if (this.selectedItems.length === 0) return '0.00'
      return (this.totalAmount / this.selectedItems.length).toFixed(2)
    }
  },
  
  watch: {
    visible(val) {
      if (val) {
        this.resetForm()
        this.generateDefaultGroupName()
      }
    }
  },
  
  methods: {
    // 移除项目
    removeItem(index) {
      const newItems = [...this.selectedItems]
      newItems.splice(index, 1)
      this.$emit('update:items', newItems)
    },
    
    // 生成默认组名称
    generateDefaultGroupName() {
      if (this.selectedItems.length > 0) {
        const firstItem = this.selectedItems[0]
        const dateStr = this.entityType === 'DOCUMENT' ? firstItem.billDate : firstItem.receiptsDate
        const date = new Date(dateStr).toLocaleDateString()
        this.mergeForm.groupName = `${this.entityTypeName}归并组-${date}`
      }
    },
    
    // 确认归并
    async confirmMerge() {
      try {
        await this.$refs.mergeForm.validate()
        
        if (this.selectedItems.length < 2) {
          this.$Message.warning('至少需要选择2个项目才能进行归并')
          return
        }
        
        this.merging = true
        
        const itemIds = this.selectedItems.map(item => item.id)
        let response
        
        if (this.entityType === 'DOCUMENT') {
          response = await mergeEngineApi.manualDocumentMerge(itemIds, this.mergeForm.groupName)
        } else {
          response = await mergeEngineApi.manualReceiptMerge(itemIds, this.mergeForm.groupName)
        }
        
        this.$Message.success(`${this.entityTypeName}归并成功`)
        this.$emit('merge-success', {
          groupId: response.data,
          groupName: this.mergeForm.groupName,
          items: this.selectedItems
        })
        
        this.handleClose()
        
      } catch (error) {
        if (error.message) {
          this.$Message.error(`归并失败: ${error.message}`)
        }
      } finally {
        this.merging = false
      }
    },
    
    // 重置表单
    resetForm() {
      this.mergeForm = {
        groupName: '',
        groupSummary: ''
      }
      if (this.$refs.mergeForm) {
        this.$refs.mergeForm.clearValidate()
      }
    },
    
    // 关闭对话框
    handleClose() {
      this.visible = false
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.manual-merge-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.selected-items {
  margin-bottom: 20px;
}

.selected-items h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.merge-info {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.merge-statistics {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}
</style>
