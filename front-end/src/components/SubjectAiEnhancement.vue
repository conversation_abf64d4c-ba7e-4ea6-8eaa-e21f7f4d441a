<template>
  <div class="ai-voucher-generator">
    <!-- 页面标题 -->
    <div class="page-header">
      <h-card class="header-card">
        <div class="header-content">
          <div class="title-section">
            <h1> 科目AI增强 - 智能凭证生成</h1>
            <p>基于票据和银行回单，通过大模型智能生成会计凭证</p>
          </div>
          <div class="stats-section">
            <h-descriptions :column="4" size="small">
              <h-descriptions-item label="待处理票据">{{ unprocessedBills.length }}张</h-descriptions-item>
              <h-descriptions-item label="待处理银行回单">{{ unprocessedReceipts.length }}张</h-descriptions-item>
              <h-descriptions-item label="今日生成凭证">{{ todayGeneratedCount }}张</h-descriptions-item>
              <h-descriptions-item label="AI准确率">{{ aiAccuracy }}%</h-descriptions-item>
            </h-descriptions>
          </div>
        </div>
      </h-card>
    </div>

    <!-- 数据源选择区 -->
    <h-card class="data-source-card">
      <div slot="title">
         数据源选择
      </div>

      <div class="source-tabs">
        <h-tabs v-model="activeSourceTab" @change="onSourceTabChange">
          <h-tab-pane label="单张票据" name="single-bill">
            <div class="single-source-section">
              <div class="source-selector">
                <h-select v-model="selectedBill" placeholder="选择票据" style="width: 300px;" @change="onBillSelect">
                  <h-option
                    v-for="bill in availableBills"
                    :key="bill.id"
                    :value="bill.id"
                    :label="`${bill.billNo} - ¥${bill.amount} - ${bill.summary}`"
                  />
                </h-select>
                <h-button type="primary" @click="generateFromSingleBill" :loading="generating" style="margin-left: 10px;">
                   生成凭证
                </h-button>
              </div>
            </div>
          </h-tab-pane>

          <h-tab-pane label="票据组" name="bill-group">
            <div class="group-source-section">
              <div class="source-selector">
                <h-select v-model="selectedBillGroup" placeholder="选择票据组" style="width: 300px;" @change="onBillGroupSelect">
                  <h-option
                    v-for="group in availableBillGroups"
                    :key="group.id"
                    :value="group.id"
                    :label="`${group.name} (${group.billCount}张票据)`"
                  />
                </h-select>
                <h-button type="primary" @click="generateFromBillGroup" :loading="generating" style="margin-left: 10px;">
                   生成凭证
                </h-button>
              </div>
            </div>
          </h-tab-pane>

          <h-tab-pane label="关联数据" name="related-data">
            <div class="related-source-section">
              <div class="source-selector">
                <h-select v-model="selectedRelation" placeholder="选择已关联的票据和银行回单" style="width: 400px;" @change="onRelationSelect">
                  <h-option
                    v-for="relation in availableRelations"
                    :key="relation.id"
                    :value="relation.id"
                    :label="`${relation.billNo} ↔ ${relation.receiptNo} - ¥${relation.amount}`"
                  />
                </h-select>
                <h-button type="primary" @click="generateFromRelation" :loading="generating" style="margin-left: 10px;">
                   生成凭证
                </h-button>
              </div>
            </div>
          </h-tab-pane>

          <h-tab-pane label="未关联数据" name="unrelated-data">
            <div class="unrelated-source-section">
              <div class="batch-selector">
                <h-checkbox-group v-model="selectedUnrelatedItems">
                  <div class="unrelated-items">
                    <div class="bills-section">
                      <h4>未关联票据</h4>
                      <h-checkbox
                        v-for="bill in unrelatedBills"
                        :key="`bill-${bill.id}`"
                        :value="`bill-${bill.id}`"
                      >
                        {{ bill.billNo }} - ¥{{ bill.amount }} - {{ bill.summary }}
                      </h-checkbox>
                    </div>
                    <div class="receipts-section">
                      <h4>未关联银行回单</h4>
                      <h-checkbox
                        v-for="receipt in unrelatedReceipts"
                        :key="`receipt-${receipt.id}`"
                        :value="`receipt-${receipt.id}`"
                      >
                        {{ receipt.receiptsNo }} - ¥{{ receipt.amount }} - {{ receipt.summary }}
                      </h-checkbox>
                    </div>
                  </div>
                </h-checkbox-group>
                <div class="batch-actions">
                  <h-button type="primary" @click="generateFromUnrelated" :loading="generating" :disabled="selectedUnrelatedItems.length === 0">
                     批量生成凭证 ({{ selectedUnrelatedItems.length }}项)
                  </h-button>
                </div>
              </div>
            </div>
          </h-tab-pane>
        </h-tabs>
      </div>
    </h-card>

    <!-- 智能匹配测试区 -->
    <h-card title="智能匹配测试" class="test-section">
      <div class="test-form">
        <h-form :model="testForm" :label-width="100">
          <h-row :gutter="20">
            <h-col :span="8">
              <h-form-item label="业务描述">
                <h-input 
                  v-model="testForm.description" 
                  placeholder="请输入业务描述，如：收到客户货款"
                  type="textarea"
                  :rows="3"
                />
              </h-form-item>
            </h-col>
            <h-col :span="6">
              <h-form-item label="金额">
                <h-input 
                  v-model="testForm.amount" 
                  placeholder="请输入金额"
                  type="number"
                />
              </h-form-item>
            </h-col>
            <h-col :span="6">
              <h-form-item label="业务类型">
                <h-select v-model="testForm.businessType" placeholder="选择业务类型">
                  <h-option value="销售收入">销售收入</h-option>
                  <h-option value="采购支出">采购支出</h-option>
                  <h-option value="费用支出">费用支出</h-option>
                  <h-option value="银行转账">银行转账</h-option>
                  <h-option value="现金收付">现金收付</h-option>
                </h-select>
              </h-form-item>
            </h-col>
            <h-col :span="4">
              <h-form-item>
                <h-button type="primary" @click="testIntelligentMatch" :loading="testLoading">
                  智能匹配
                </h-button>
              </h-form-item>
            </h-col>
          </h-row>
        </h-form>
      </div>

      <!-- 匹配结果 -->
      <div v-if="matchResults.length > 0" class="match-results">
        <h4>匹配结果：</h4>
        <div class="result-list">
          <div 
            v-for="(result, index) in matchResults" 
            :key="index"
            class="result-item"
            @click="selectMatch(result)"
          >
            <div class="subject-info">
              <span class="subject-code">{{ result.subjectCode }}</span>
              <span class="subject-name">{{ result.subjectName }}</span>
            </div>
            <div class="match-score">
              <h-progress 
                :percent="Math.round(result.confidenceScore * 100)" 
                :stroke-width="6"
                :show-text="false"
              />
              <span class="score-text">{{ Math.round(result.confidenceScore * 100) }}%</span>
            </div>
            <div class="actions">
              <h-button size="small" @click.stop="confirmMatch(result, true)">
                <h-icon name="check"></h-icon>
                正确
              </h-button>
              <h-button size="small" @click.stop="confirmMatch(result, false)">
                <h-icon name="close"></h-icon>
                错误
              </h-button>
            </div>
          </div>
        </div>
      </div>
    </h-card>

    <!-- 科目AI增强列表 -->
    <h-card title="科目AI增强配置" class="enhancement-list">
      <!-- 统计信息 -->
      <div class="statistics">
        <h-row :gutter="20">
          <h-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalSubjects }}</div>
              <div class="stat-label">总科目数</div>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.aiEnabledSubjects }}</div>
              <div class="stat-label">AI增强科目</div>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.averageConfidence }}%</div>
              <div class="stat-label">平均置信度</div>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalUsage }}</div>
              <div class="stat-label">总使用次数</div>
            </div>
          </h-col>
        </h-row>
      </div>

      <!-- 数据表格 -->
      <h-table 
        :data="enhancementList" 
        :loading="tableLoading"
        @selection-change="handleSelectionChange"
      >
        <h-table-column type="selection" width="50"></h-table-column>
        
        <h-table-column prop="subjectCode" label="科目编码" width="120">
          <template #default="{ row }">
            <span class="subject-code">{{ row.subjectCode }}</span>
          </template>
        </h-table-column>
        
        <h-table-column prop="subjectName" label="科目名称" width="150">
          <template #default="{ row }">
            <span class="subject-name">{{ row.subjectName }}</span>
          </template>
        </h-table-column>
        
        <h-table-column prop="aiDescription" label="AI描述" min-width="200">
          <template #default="{ row }">
            <div class="ai-description" :title="row.aiDescription">
              {{ row.aiDescription || '暂无描述' }}
            </div>
          </template>
        </h-table-column>
        
        <h-table-column prop="aiKeywords" label="关键词" width="180">
          <template #default="{ row }">
            <div class="keywords">
              <h-tag 
                v-for="keyword in getKeywordList(row.aiKeywords)" 
                :key="keyword"
                size="small"
                class="keyword-tag"
              >
                {{ keyword }}
              </h-tag>
            </div>
          </template>
        </h-table-column>
        
        <h-table-column prop="confidenceScore" label="置信度" width="120">
          <template #default="{ row }">
            <div class="confidence-score">
              <h-progress 
                :percent="Math.round(row.confidenceScore * 100)" 
                :stroke-width="6"
                :show-text="false"
              />
              <span class="score-text">{{ Math.round(row.confidenceScore * 100) }}%</span>
            </div>
          </template>
        </h-table-column>
        
        <h-table-column prop="usageFrequency" label="使用频率" width="100">
          <template #default="{ row }">
            <span class="usage-frequency">{{ row.usageFrequency || 0 }}</span>
          </template>
        </h-table-column>
        
        <h-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <h-switch 
              v-model="row.status" 
              :active-value="1" 
              :inactive-value="0"
              @change="handleStatusChange(row)"
            />
          </template>
        </h-table-column>
        
        <h-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <h-button size="small" @click="editEnhancement(row)">
              <h-icon name="edit"></h-icon>
              编辑
            </h-button>
            <h-button size="small" @click="viewDetails(row)">
              <h-icon name="eye"></h-icon>
              详情
            </h-button>
          </template>
        </h-table-column>
      </h-table>

      <!-- 分页 -->
      <div class="pagination">
        <h-pagination 
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </div>
    </h-card>

    <!-- 编辑对话框 -->
    <h-modal 
      v-model="editDialog.visible" 
      :title="editDialog.title"
      width="800px"
      @confirm="saveEnhancement"
    >
      <h-form :model="editForm" :label-width="120">
        <h-form-item label="科目信息">
          <div class="subject-display">
            <span class="code">{{ editForm.subjectCode }}</span>
            <span class="name">{{ editForm.subjectName }}</span>
          </div>
        </h-form-item>
        
        <h-form-item label="AI描述" required>
          <h-input 
            v-model="editForm.aiDescription" 
            type="textarea" 
            :rows="4"
            placeholder="请输入AI描述，用于帮助大模型理解科目用途"
          />
        </h-form-item>
        
        <h-form-item label="关键词" required>
          <h-input 
            v-model="editForm.aiKeywords" 
            placeholder="请输入关键词，用逗号分隔"
          />
          <div class="keywords-preview">
            <h-tag 
              v-for="keyword in getKeywordList(editForm.aiKeywords)" 
              :key="keyword"
              size="small"
              class="keyword-tag"
            >
              {{ keyword }}
            </h-tag>
          </div>
        </h-form-item>
        
        <h-form-item label="匹配规则">
          <h-input 
            v-model="editForm.matchingRules" 
            type="textarea" 
            :rows="3"
            placeholder="JSON格式的匹配规则配置（可选）"
          />
        </h-form-item>
        
        <h-form-item label="置信度">
          <h-slider 
            v-model="editForm.confidenceScore" 
            :min="0" 
            :max="1" 
            :step="0.01"
            :show-tooltip="true"
          />
        </h-form-item>
      </h-form>
    </h-modal>

    <!-- 详情对话框 -->
    <h-modal 
      v-model="detailDialog.visible" 
      title="科目AI增强详情"
      width="900px"
      :show-confirm="false"
    >
      <div class="enhancement-details">
        <h-descriptions :column="2" border>
          <h-descriptions-item label="科目编码">{{ detailForm.subjectCode }}</h-descriptions-item>
          <h-descriptions-item label="科目名称">{{ detailForm.subjectName }}</h-descriptions-item>
          <h-descriptions-item label="置信度">{{ Math.round(detailForm.confidenceScore * 100) }}%</h-descriptions-item>
          <h-descriptions-item label="使用频率">{{ detailForm.usageFrequency }}</h-descriptions-item>
          <h-descriptions-item label="最后匹配时间">{{ detailForm.lastMatchedDate }}</h-descriptions-item>
          <h-descriptions-item label="状态">{{ detailForm.status === 1 ? '启用' : '禁用' }}</h-descriptions-item>
          <h-descriptions-item label="AI描述" :span="2">
            {{ detailForm.aiDescription }}
          </h-descriptions-item>
          <h-descriptions-item label="关键词" :span="2">
            <div class="keywords">
              <h-tag 
                v-for="keyword in getKeywordList(detailForm.aiKeywords)" 
                :key="keyword"
                size="small"
                class="keyword-tag"
              >
                {{ keyword }}
              </h-tag>
            </div>
          </h-descriptions-item>
        </h-descriptions>
        
        <!-- 学习数据展示 -->
        <div v-if="detailForm.learningData" class="learning-data">
          <h4>学习数据</h4>
          <pre>{{ formatLearningData(detailForm.learningData) }}</pre>
        </div>
      </div>
    </h-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { Message } from 'heyui'
import api from '@/api/subjectAiEnhancement'

export default {
  name: 'SubjectAiEnhancement',
  setup() {
    // 响应式数据
    const searchKeyword = ref('')
    const tableLoading = ref(false)
    const testLoading = ref(false)
    const enhancementList = ref([])
    const matchResults = ref([])
    const selectedRows = ref([])
    
    // 测试表单
    const testForm = reactive({
      description: '',
      amount: null,
      businessType: ''
    })
    
    // 编辑对话框
    const editDialog = reactive({
      visible: false,
      title: '编辑AI增强信息'
    })
    
    const editForm = reactive({
      id: null,
      subjectId: null,
      subjectCode: '',
      subjectName: '',
      aiDescription: '',
      aiKeywords: '',
      matchingRules: '',
      confidenceScore: 0.5
    })
    
    // 详情对话框
    const detailDialog = reactive({
      visible: false
    })
    
    const detailForm = reactive({})
    
    // 分页
    const pagination = reactive({
      current: 1,
      pageSize: 20,
      total: 0
    })
    
    // 统计信息
    const statistics = reactive({
      totalSubjects: 0,
      aiEnabledSubjects: 0,
      averageConfidence: 0,
      totalUsage: 0
    })
    
    // 当前账套ID（从全局状态获取）
    const accountSetsId = computed(() => {
      // 这里应该从Vuex或其他状态管理获取当前账套ID
      return 1 // 临时硬编码
    })
    
    // 方法
    const loadEnhancementList = async () => {
      try {
        tableLoading.value = true
        const response = await api.getAllEnabled(accountSetsId.value)
        if (response.success) {
          enhancementList.value = response.data || []
          updateStatistics()
        } else {
          Message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        Message.error('加载数据失败')
      } finally {
        tableLoading.value = false
      }
    }
    
    const updateStatistics = () => {
      const list = enhancementList.value
      statistics.totalSubjects = list.length
      statistics.aiEnabledSubjects = list.filter(item => item.status === 1).length
      statistics.averageConfidence = list.length > 0 
        ? Math.round(list.reduce((sum, item) => sum + (item.confidenceScore || 0), 0) / list.length * 100)
        : 0
      statistics.totalUsage = list.reduce((sum, item) => sum + (item.usageFrequency || 0), 0)
    }
    
    const handleSearch = async () => {
      if (!searchKeyword.value.trim()) {
        await loadEnhancementList()
        return
      }
      
      try {
        tableLoading.value = true
        const response = await api.searchByKeyword(accountSetsId.value, searchKeyword.value)
        if (response.success) {
          enhancementList.value = response.data || []
          updateStatistics()
        }
      } catch (error) {
        console.error('搜索失败:', error)
        Message.error('搜索失败')
      } finally {
        tableLoading.value = false
      }
    }
    
    const testIntelligentMatch = async () => {
      if (!testForm.description.trim()) {
        Message.warning('请输入业务描述')
        return
      }
      
      try {
        testLoading.value = true
        const response = await api.intelligentMatch({
          accountSetsId: accountSetsId.value,
          description: testForm.description,
          amount: testForm.amount,
          businessType: testForm.businessType
        })
        
        if (response.success) {
          matchResults.value = response.data || []
          if (matchResults.value.length === 0) {
            Message.info('未找到匹配的科目')
          }
        } else {
          Message.error(response.message || '智能匹配失败')
        }
      } catch (error) {
        console.error('智能匹配失败:', error)
        Message.error('智能匹配失败')
      } finally {
        testLoading.value = false
      }
    }
    
    const confirmMatch = async (result, isCorrect) => {
      try {
        await api.recordUsage({
          subjectId: result.subjectId,
          accountSetsId: accountSetsId.value,
          matchingContext: {
            description: testForm.description,
            amount: testForm.amount,
            businessType: testForm.businessType
          },
          isCorrect
        })
        
        Message.success(isCorrect ? '已标记为正确匹配' : '已标记为错误匹配')
        
        // 重新加载数据以更新使用频率
        await loadEnhancementList()
      } catch (error) {
        console.error('记录匹配结果失败:', error)
        Message.error('记录匹配结果失败')
      }
    }
    
    const initializeAiEnhancement = async () => {
      try {
        const response = await api.initialize(accountSetsId.value)
        if (response.success) {
          Message.success(`成功初始化 ${response.data} 个科目的AI增强信息`)
          await loadEnhancementList()
        } else {
          Message.error(response.message || '初始化失败')
        }
      } catch (error) {
        console.error('初始化失败:', error)
        Message.error('初始化失败')
      }
    }
    
    const optimizeRules = async () => {
      try {
        const response = await api.optimizeRules(accountSetsId.value)
        if (response.success) {
          Message.success(`成功优化 ${response.data} 个科目的匹配规则`)
          await loadEnhancementList()
        } else {
          Message.error(response.message || '优化失败')
        }
      } catch (error) {
        console.error('优化失败:', error)
        Message.error('优化失败')
      }
    }
    
    const exportConfig = async () => {
      try {
        const response = await api.exportConfig(accountSetsId.value)
        if (response.success) {
          // 下载配置文件
          const blob = new Blob([JSON.stringify(response.data, null, 2)], {
            type: 'application/json'
          })
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `subject_ai_config_${accountSetsId.value}.json`
          a.click()
          URL.revokeObjectURL(url)
          
          Message.success('配置导出成功')
        } else {
          Message.error(response.message || '导出失败')
        }
      } catch (error) {
        console.error('导出失败:', error)
        Message.error('导出失败')
      }
    }
    
    const editEnhancement = (row) => {
      Object.assign(editForm, {
        id: row.id,
        subjectId: row.subjectId,
        subjectCode: row.subjectCode,
        subjectName: row.subjectName,
        aiDescription: row.aiDescription || '',
        aiKeywords: row.aiKeywords || '',
        matchingRules: row.matchingRules || '',
        confidenceScore: row.confidenceScore || 0.5
      })
      editDialog.visible = true
    }
    
    const saveEnhancement = async () => {
      try {
        const data = {
          id: editForm.id,
          subjectId: editForm.subjectId,
          accountSetsId: accountSetsId.value,
          aiDescription: editForm.aiDescription,
          aiKeywords: editForm.aiKeywords,
          matchingRules: editForm.matchingRules,
          confidenceScore: editForm.confidenceScore
        }
        
        const response = await api.saveEnhancement(data)
        if (response.success) {
          Message.success('保存成功')
          editDialog.visible = false
          await loadEnhancementList()
        } else {
          Message.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('保存失败:', error)
        Message.error('保存失败')
      }
    }
    
    const viewDetails = (row) => {
      Object.assign(detailForm, row)
      detailDialog.visible = true
    }
    
    const handleStatusChange = async (row) => {
      try {
        const response = await api.updateStatus(row.id, row.status)
        if (response.success) {
          Message.success('状态更新成功')
        } else {
          Message.error(response.message || '状态更新失败')
          // 恢复原状态
          row.status = row.status === 1 ? 0 : 1
        }
      } catch (error) {
        console.error('状态更新失败:', error)
        Message.error('状态更新失败')
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
      }
    }
    
    const handleSelectionChange = (selection) => {
      selectedRows.value = selection
    }
    
    const handlePageChange = () => {
      loadEnhancementList()
    }
    
    const getKeywordList = (keywords) => {
      if (!keywords) return []
      return keywords.split(',').map(k => k.trim()).filter(k => k)
    }
    
    const formatLearningData = (data) => {
      try {
        return JSON.stringify(JSON.parse(data), null, 2)
      } catch {
        return data
      }
    }
    
    // 生命周期
    onMounted(() => {
      loadEnhancementList()
    })
    
    return {
      // 数据
      searchKeyword,
      tableLoading,
      testLoading,
      enhancementList,
      matchResults,
      selectedRows,
      testForm,
      editDialog,
      editForm,
      detailDialog,
      detailForm,
      pagination,
      statistics,
      
      // 方法
      loadEnhancementList,
      handleSearch,
      testIntelligentMatch,
      confirmMatch,
      initializeAiEnhancement,
      optimizeRules,
      exportConfig,
      editEnhancement,
      saveEnhancement,
      viewDetails,
      handleStatusChange,
      handleSelectionChange,
      handlePageChange,
      getKeywordList,
      formatLearningData
    }
  }
}
</script>

<style scoped>
.subject-ai-enhancement {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header .description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.left-actions .h-button {
  margin-right: 12px;
}

.test-section {
  margin-bottom: 20px;
}

.test-form {
  margin-bottom: 20px;
}

.match-results {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.result-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.subject-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.subject-code {
  font-family: monospace;
  font-weight: bold;
  color: #409eff;
}

.subject-name {
  font-weight: 500;
}

.match-score {
  flex: 0 0 150px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-text {
  font-size: 12px;
  color: #666;
}

.actions {
  flex: 0 0 120px;
  display: flex;
  gap: 8px;
}

.statistics {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.ai-description {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.keyword-tag {
  margin: 0;
}

.confidence-score {
  display: flex;
  align-items: center;
  gap: 8px;
}

.usage-frequency {
  font-weight: 500;
  color: #409eff;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.subject-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.subject-display .code {
  font-family: monospace;
  font-weight: bold;
  color: #409eff;
}

.subject-display .name {
  font-weight: 500;
}

.keywords-preview {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.enhancement-details {
  max-height: 600px;
  overflow-y: auto;
}

.learning-data {
  margin-top: 20px;
}

.learning-data h4 {
  margin-bottom: 12px;
  color: #333;
}

.learning-data pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>