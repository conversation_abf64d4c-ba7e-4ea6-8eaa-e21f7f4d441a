<template>
  <div class="batch-preview">
    <!-- 头部信息 -->
    <div class="preview-header">
      <div class="task-info">
        <h3>{{ taskInfo.taskName }}</h3>
        <div class="task-meta">
          <span class="task-type">{{ getTypeText(taskInfo.importType) }}</span>
          <span class="task-status" :class="taskInfo.status">{{ getStatusText(taskInfo.status) }}</span>
          <span class="task-progress">{{ taskInfo.successCount || 0 }}/{{ taskInfo.totalCount || 0 }}</span>
        </div>
      </div>
      
      <div class="header-actions">
        <button 
          v-if="taskInfo.status === 'RECOGNIZING'" 
          class="btn-refresh" 
          @click="refreshProgress"
          :disabled="refreshing"
        >
          <i class="h-icon-refresh" :class="{ 'rotating': refreshing }"></i>
          刷新进度
        </button>
        
        <button 
          v-if="canStartRecognize" 
          class="btn-recognize" 
          @click="startRecognize"
          :disabled="recognizing"
        >
          <i v-if="recognizing" class="h-icon-loading"></i>
          {{ recognizing ? '识别中...' : '开始识别' }}
        </button>
        
        <button
          v-if="canBatchSave"
          class="btn-batch-change-type"
          @click="reRecognizeSelected"
          :disabled="selectedItems.length === 0 || reRecognizing"
        >
          <i v-if="reRecognizing" class="h-icon-loading"></i>
          {{ reRecognizing ? '重新识别中...' : '重新识别' }} ({{ selectedItems.length }})
        </button>



        <button
          v-if="canBatchSave"
          class="btn-save"
          @click="showBatchSaveModal"
          :disabled="selectedItems.length === 0"
        >
          批量保存 ({{ selectedItems.length }})
        </button>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-section" v-if="taskInfo.status === 'RECOGNIZING'">
      <div class="progress-info">
        <span>识别进度</span>
        <span>{{ taskInfo.progress || 0 }}%</span>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: (taskInfo.progress || 0) + '%' }"></div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar" v-if="resultList.length > 0">
      <div class="toolbar-left">
        <label class="select-all">
          <input 
            type="checkbox" 
            :checked="isAllSelected"
            @change="toggleSelectAll"
          >
          全选
        </label>
        
        <div class="filter-options">
          <label>状态筛选：</label>
          <select v-model="statusFilter" @change="filterResults">
            <option value="">全部</option>
            <option value="SUCCESS">识别成功</option>
            <option value="FAILED">识别失败</option>
          </select>
        </div>
      </div>
      
      <div class="toolbar-right">
        <span class="result-count">共 {{ filteredResults.length }} 条结果</span>
      </div>
    </div>

    <!-- 识别结果列表 -->
    <div class="result-list" v-if="filteredResults.length > 0">
      <div 
        v-for="(item, index) in paginatedResults" 
        :key="item.id" 
        class="result-item"
        :class="{ 
          'selected': selectedItems.includes(item.id),
          'error': item.status === 'FAILED' 
        }"
      >
        <!-- 选择框和图片预览 -->
        <div class="item-left">
          <label class="item-checkbox">
            <input 
              type="checkbox" 
              :checked="selectedItems.includes(item.id)"
              @change="toggleSelectItem(item.id)"
              :disabled="item.status === 'FAILED'"
            >
          </label>
          
          <div class="image-preview">
            <img :src="item.imageUrl" :alt="item.fileName" @click="previewImage(item)">
            <div class="image-info">
              <div class="image-name">{{ item.fileName }}</div>
              <div class="image-meta">
                {{ item.pageNumber ? `第${item.pageNumber}页` : '' }}
                {{ item.imageWidth }}×{{ item.imageHeight }}
              </div>
            </div>
          </div>
        </div>

        <!-- 识别结果表单 -->
        <div class="item-center">
          <div class="form-section">
            <!-- 银行回单表单 -->
            <template v-if="taskInfo.importType === 'BANK_RECEIPT'">
              <div class="form-row">
                <label>回单名称：</label>
                <input :value="getBankReceiptField(item.data, 'title')" type="text" class="form-input" readonly>
              </div>
              <div class="form-row">
                <label>金额：</label>
                <input :value="getBankReceiptField(item.data, 'amount')" type="number" step="0.01" class="form-input" readonly>
              </div>
              <div class="form-row">
                <label>收款方：</label>
                <input :value="getBankReceiptField(item.data, 'payeeName')" type="text" class="form-input" readonly>
              </div>
              <div class="form-row">
                <label>付款方：</label>
                <input :value="getBankReceiptField(item.data, 'payerName')" type="text" class="form-input" readonly>
              </div>
              <div class="form-row">
                <label>转账日期：</label>
                <input :value="getBankReceiptField(item.data, 'transferDate')" type="date" class="form-input" readonly>
              </div>
              <div class="form-row">
                <label>流水号：</label>
                <input :value="getBankReceiptField(item.data, 'serialNumber')" type="text" class="form-input" readonly>
              </div>
            </template>

            <!-- 发票表单 -->
            <template v-if="taskInfo.importType === 'INVOICE'">
              <div class="form-row">
                <label>发票号码：</label>
                <input v-model="item.data.invoice_number || item.data.invoiceNumber" type="text" class="form-input">
              </div>
              <div class="form-row">
                <label>开票日期：</label>
                <input :value="formatDateForInput(item.data.bill_date || item.data.invoiceDate)" type="date" class="form-input" readonly>
              </div>
              <div class="form-row">
                <label>开票方：</label>
                <input v-model="item.data.issuer || item.data.sellerName" type="text" class="form-input">
              </div>
              <div class="form-row">
                <label>收票方：</label>
                <input v-model="item.data.recipient || item.data.buyerName" type="text" class="form-input">
              </div>
              <div class="form-row">
                <label>发票类型：</label>
                <input :value="getInvoiceType(item.data)" type="text" class="form-input" readonly>
              </div>
              <div class="form-row">
                <label>合计金额：</label>
                <input v-model="item.data.amount || item.data.totalAmount" type="number" step="0.01" class="form-input">
              </div>
              <div class="form-row">
                <label>税额：</label>
                <input :value="formatNumberForInput(item.data.total_tax_amount || item.data.totalTaxAmount)" type="number" step="0.01" class="form-input" readonly>
              </div>
              <div class="form-row">
                <label>税率：</label>
                <input :value="formatTaxRateForInput(item.data.tax_rate || item.data.taxRate)" type="number" step="0.01" class="form-input" readonly>
              </div>
              <div class="form-row">
                <label>大写金额：</label>
                <input v-model="item.data.amount_in_words || item.data.amountInWords" type="text" class="form-input">
              </div>
            </template>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="item-right">
          <div class="item-status" :class="item.status">
            {{ getItemStatusText(item.status) }}
          </div>
          
          <div class="item-actions">
            <button
              v-if="item.status === 'FAILED'"
              class="btn-retry"
              @click="retryRecognition(item)"
              :disabled="item.retrying"
            >
              <i v-if="item.retrying" class="h-icon-loading"></i>
              {{ item.retrying ? '重试中' : '重新识别' }}
            </button>

            <button class="btn-edit" @click="editItem(item)">编辑</button>
            <button class="btn-remove" @click="removeItem(item.id)">删除</button>
          </div>
          
          <div class="confidence-score" v-if="item.confidenceScore">
            置信度: {{ (item.confidenceScore * 100).toFixed(1) }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="totalPages > 1">
      <button 
        class="page-btn" 
        :disabled="currentPage === 1"
        @click="currentPage = 1"
      >
        首页
      </button>
      <button 
        class="page-btn" 
        :disabled="currentPage === 1"
        @click="currentPage--"
      >
        上一页
      </button>
      <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
      <button 
        class="page-btn" 
        :disabled="currentPage === totalPages"
        @click="currentPage++"
      >
        下一页
      </button>
      <button 
        class="page-btn" 
        :disabled="currentPage === totalPages"
        @click="currentPage = totalPages"
      >
        末页
      </button>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="resultList.length === 0 && !loading">
      <i class="h-icon-empty"></i>
      <p>暂无识别结果</p>
    </div>

    <!-- 加载状态 -->
    <div class="loading-state" v-if="loading">
      <i class="h-icon-loading"></i>
      <p>加载中...</p>
    </div>





    <!-- 图片预览模态框 -->
    <ImagePreviewModal
      :visible="showImagePreview"
      :image-url="previewImageUrl"
      :image-info="previewImageInfo"
      @close="closeImagePreview"
      @view-original="handleViewOriginal"
    />
  </div>
</template>

<script>
import ImagePreviewModal from './ImagePreviewModal.vue'

export default {
  name: 'BatchPreview',
  components: {
    ImagePreviewModal
  },
  props: {
    taskId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      taskInfo: {},
      resultList: [],
      filteredResults: [],
      selectedItems: [],
      statusFilter: '',
      loading: false,
      refreshing: false,
      recognizing: false,
      reRecognizing: false,
      currentPage: 1,
      pageSize: 10,
      progressTimer: null,

      // 图片预览相关
      showImagePreview: false,
      previewImageUrl: '',
      previewImageInfo: {},


    }
  },
  computed: {
    canStartRecognize() {
      return this.taskInfo.status === 'PROCESSING'
    },
    
    canBatchSave() {
      // 允许在任务完成后或有成功记录时显示保存按钮
      const hasSuccessRecords = this.resultList.some(item => item.status === 'SUCCESS')
      const isTaskCompleted = ['COMPLETED', 'FAILED', 'PREVIEWING'].includes(this.taskInfo.status)
      return hasSuccessRecords && isTaskCompleted
    },
    
    isAllSelected() {
      const validItems = this.filteredResults.filter(item => item.status !== 'FAILED')
      return validItems.length > 0 && validItems.every(item => this.selectedItems.includes(item.id))
    },
    
    paginatedResults() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredResults.slice(start, end)
    },
    
    totalPages() {
      return Math.ceil(this.filteredResults.length / this.pageSize)
    }
  },
  mounted() {
    this.loadTaskInfo()
    this.loadResults()
  },
  beforeDestroy() {
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
  },
  methods: {
    // 格式化日期为input[type="date"]可用的格式
    formatDateForInput(dateStr) {
      if (!dateStr) return ''

      // 处理中文日期格式：2016年03月30日 -> 2016-03-30
      if (dateStr.includes('年') && dateStr.includes('月') && dateStr.includes('日')) {
        const match = dateStr.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/)
        if (match) {
          const [, year, month, day] = match
          return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
        }
      }

      // 处理标准日期格式：2016-03-30
      if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return dateStr
      }

      // 其他格式尝试解析
      try {
        const date = new Date(dateStr)
        if (!isNaN(date.getTime())) {
          return date.toISOString().split('T')[0]
        }
      } catch (e) {
        console.warn('无法解析日期格式:', dateStr)
      }

      return ''
    },

    // 格式化数字（去除货币符号）
    formatNumberForInput(numStr) {
      if (!numStr) return ''

      // 去除货币符号和空格
      const cleaned = String(numStr).replace(/[￥¥$€£,\s]/g, '')

      // 转换为数字
      const num = parseFloat(cleaned)
      return isNaN(num) ? '' : num
    },

    // 格式化税率（去除%符号）
    formatTaxRateForInput(rateStr) {
      if (!rateStr) return ''

      // 去除%符号和空格
      const cleaned = String(rateStr).replace(/[%\s]/g, '')

      // 转换为数字
      const num = parseFloat(cleaned)
      return isNaN(num) ? '' : num
    },

    async loadTaskInfo() {
      try {
        const response = await this.$api.batch.getProgress(this.taskId)
        if (response.success) {
          this.taskInfo = response.data
          
          // 如果正在识别，启动进度轮询
          if (this.taskInfo.status === 'RECOGNIZING') {
            this.startProgressPolling()
          }
        }
      } catch (error) {
        console.error('加载任务信息失败:', error)
        this.$Message.error('加载任务信息失败')
      }
    },
    
    async loadResults() {
      try {
        this.loading = true
        const response = await this.$api.batch.getRecognitionResults(this.taskId)
        console.log('🔍 [BatchPreview] 获取识别结果响应:', response)

        if (response.success) {
          this.resultList = response.data.map((item, index) => {
            console.log(`🔍 [BatchPreview] 处理第${index + 1}个项目:`, item)

            // 优先使用final_data，如果没有则使用parsed_data
            let data = {}
            try {
              if (item.finalData) {
                console.log(`📄 [BatchPreview] 项目${index + 1} 使用finalData:`, item.finalData)
                data = JSON.parse(item.finalData)
                console.log(`✅ [BatchPreview] 项目${index + 1} finalData解析结果:`, data)
              } else if (item.parsedData) {
                console.log(`📄 [BatchPreview] 项目${index + 1} 使用parsedData:`, item.parsedData)
                data = JSON.parse(item.parsedData)
                console.log(`✅ [BatchPreview] 项目${index + 1} parsedData解析结果:`, data)
              } else {
                console.warn(`⚠️ [BatchPreview] 项目${index + 1} 没有finalData或parsedData`)
              }
            } catch (e) {
              console.error(`❌ [BatchPreview] 项目${index + 1} 解析字段映射数据失败:`, e, item)
              data = {}
            }

            const processedItem = {
              ...item,
              data: data,
              retrying: false
            }
            console.log(`🎯 [BatchPreview] 项目${index + 1} 最终处理结果:`, processedItem)
            return processedItem
          })
          this.filterResults()
        }
      } catch (error) {
        console.error('❌ [BatchPreview] 加载识别结果失败:', error)
        this.$Message.error('加载识别结果失败')
      } finally {
        this.loading = false
      }
    },
    
    filterResults() {
      if (this.statusFilter) {
        this.filteredResults = this.resultList.filter(item => item.status === this.statusFilter)
      } else {
        this.filteredResults = [...this.resultList]
      }
      this.currentPage = 1
    },

    // 外部调用的方法：按状态过滤
    filterByStatus(status) {
      this.statusFilter = status
      this.filterResults()
    },
    
    startProgressPolling() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }
      
      this.progressTimer = setInterval(async () => {
        await this.refreshProgress()
        
        // 如果识别完成，停止轮询并重新加载结果
        if (this.taskInfo.status !== 'RECOGNIZING') {
          clearInterval(this.progressTimer)
          this.progressTimer = null
          await this.loadResults()
        }
      }, 2000)
    },
    
    async refreshProgress() {
      try {
        this.refreshing = true
        const response = await this.$api.batch.getProgress(this.taskId)
        if (response.success) {
          this.taskInfo = response.data
        }
      } catch (error) {
        console.error('刷新进度失败:', error)
      } finally {
        this.refreshing = false
      }
    },
    
    async startRecognize() {
      try {
        this.recognizing = true
        const response = await this.$api.batch.startRecognize(this.taskId)
        if (response.success) {
          this.$Message.success('开始批量识别')
          this.taskInfo.status = 'RECOGNIZING'
          this.startProgressPolling()
        } else {
          this.$Message.error(response.message || '开始识别失败')
        }
      } catch (error) {
        console.error('开始识别失败:', error)
        this.$Message.error('开始识别失败')
      } finally {
        this.recognizing = false
      }
    },

    async reRecognizeSelected() {
      if (this.selectedItems.length === 0) {
        this.$Message.warning('请选择要重新识别的记录')
        return
      }

      try {
        this.reRecognizing = true

        // 使用单个重试接口逐个处理选中的记录
        let successCount = 0
        let failCount = 0

        this.$Message.loading(`正在重新识别 ${this.selectedItems.length} 条记录...`, 0)

        for (const itemId of this.selectedItems) {
          try {
            const response = await this.$api.batch.retryRecognition(itemId)
            if (response.success) {
              successCount++
              // 更新对应的记录
              const item = this.resultList.find(r => r.id === itemId)
              if (item) {
                Object.assign(item, {
                  status: response.data.status,
                  data: response.data.data || {},
                  confidenceScore: response.data.confidence
                })
              }
            } else {
              failCount++
              console.warn(`重试记录 ${itemId} 失败:`, response.message)
            }
          } catch (error) {
            failCount++
            console.error(`重试记录 ${itemId} 出错:`, error)
          }
        }

        this.$Message.destroy()

        if (successCount > 0) {
          this.$Message.success(`成功重新识别 ${successCount} 条记录${failCount > 0 ? `，${failCount} 条失败` : ''}`)
          // 刷新过滤结果
          this.filterResults()
          // 清空选择
          this.selectedItems = []
        } else {
          this.$Message.error('所有记录重新识别都失败了')
        }
      } catch (error) {
        this.$Message.destroy()
        console.error('重新识别失败:', error)
        this.$Message.error('重新识别失败')
      } finally {
        this.reRecognizing = false
      }
    },
    
    toggleSelectAll() {
      const validItems = this.filteredResults.filter(item => item.status !== 'FAILED')
      if (this.isAllSelected) {
        this.selectedItems = this.selectedItems.filter(id => 
          !validItems.some(item => item.id === id)
        )
      } else {
        const newSelections = validItems.map(item => item.id)
        this.selectedItems = [...new Set([...this.selectedItems, ...newSelections])]
      }
    },
    
    toggleSelectItem(itemId) {
      const index = this.selectedItems.indexOf(itemId)
      if (index > -1) {
        this.selectedItems.splice(index, 1)
      } else {
        this.selectedItems.push(itemId)
      }
    },
    
    async retryRecognition(item) {
      try {
        console.log(`🔄 [BatchPreview] 开始重新识别项目:`, item)
        item.retrying = true
        const response = await this.$api.batch.retryRecognition(item.id)
        console.log(`🔄 [BatchPreview] 重新识别响应:`, response)

        if (response.success) {
          this.$Message.success('重新识别成功')
          console.log(`✅ [BatchPreview] 重新识别成功，更新数据:`, response.data)
          // 更新结果
          Object.assign(item, {
            status: response.data.status,
            data: response.data.data || {},
            confidenceScore: response.data.confidence
          })
          console.log(`🎯 [BatchPreview] 项目更新后:`, item)
        } else {
          console.error(`❌ [BatchPreview] 重新识别失败:`, response.message)
          this.$Message.error(response.message || '重新识别失败')
        }
      } catch (error) {
        console.error('❌ [BatchPreview] 重新识别异常:', error)
        this.$Message.error('重新识别失败')
      } finally {
        item.retrying = false
      }
    },
    
    editItem(item) {
      // 触发编辑事件，可以打开编辑弹窗
      this.$emit('edit-item', item)
    },
    
    removeItem(itemId) {
      const index = this.resultList.findIndex(item => item.id === itemId)
      if (index > -1) {
        this.resultList.splice(index, 1)
        this.filterResults()

        // 从选中列表中移除
        const selectedIndex = this.selectedItems.indexOf(itemId)
        if (selectedIndex > -1) {
          this.selectedItems.splice(selectedIndex, 1)
        }
      }
    },
    
    showBatchSaveModal() {
      this.$emit('batch-save', {
        taskId: this.taskId,
        selectedItems: this.selectedItems,
        selectedData: this.resultList.filter(item => this.selectedItems.includes(item.id))
      })
    },

    previewImage(item) {
      // 使用模态框预览图片
      this.previewImageUrl = item.imageUrl
      this.previewImageInfo = {
        fileName: item.fileName,
        pageNumber: item.pageNumber,
        imageWidth: item.imageWidth,
        imageHeight: item.imageHeight
      }
      this.showImagePreview = true
    },

    closeImagePreview() {
      this.showImagePreview = false
      this.previewImageUrl = ''
      this.previewImageInfo = {}
    },

    handleViewOriginal(originalImageUrl) {
      // 在当前模态框中显示原图
      this.previewImageUrl = originalImageUrl
      this.previewImageInfo = {
        ...this.previewImageInfo,
        fileName: this.previewImageInfo.fileName + ' (原图)'
      }
    },
    
    getTypeText(type) {
      const typeMap = {
        'BANK_RECEIPT': '银行回单',
        'INVOICE': '发票'
      }
      return typeMap[type] || type
    },

    // 获取发票类型，优先从OCR识别数据中提取
    getInvoiceType(itemData) {
      try {
        // 首先尝试从ocrRecognitionInfoJson中提取
        if (itemData.ocrRecognitionInfoJson) {
          const ocrData = JSON.parse(itemData.ocrRecognitionInfoJson)
          if (ocrData.recognitionData) {
            const invoiceType = ocrData.recognitionData['发票类型'] ||
                              ocrData.recognitionData['发票名称'] ||
                              ocrData.recognitionData['票据类型']
            if (invoiceType && invoiceType.trim() !== '') {
              return invoiceType
            }
          }
        }

        // 如果没有找到，使用原来的字段
        return itemData.type || itemData.invoiceType || '未知类型'
      } catch (error) {
        console.warn('解析发票类型失败:', error)
        return itemData.type || itemData.invoiceType || '未知类型'
      }
    },

    // 获取银行回单字段，从OCR识别数据中提取
    getBankReceiptField(itemData, fieldName) {
      try {
        console.log(`🔍 [getBankReceiptField] 获取字段 ${fieldName}，数据:`, itemData)

        // 根据字段名映射到实际的数据字段
        switch (fieldName) {
          case 'title':
            const title = itemData.title || itemData.receiptTitle || ''
            console.log(`📄 [getBankReceiptField] title: ${title}`)
            return title
          case 'amount':
            // 优先从直接字段获取，然后从rawOcrData获取
            let amount = itemData.amount || ''
            if (!amount && itemData.rawOcrData) {
              amount = itemData.rawOcrData['金额'] || itemData.rawOcrData['交易金额(小写)'] || ''
            }
            // 清理金额格式，移除CNY前缀和逗号
            if (amount && typeof amount === 'string') {
              amount = amount.replace(/^CNY\s*/, '').replace(/,/g, '')
            }
            console.log(`💰 [getBankReceiptField] amount: ${amount}`)
            return amount
          case 'payeeName':
            // 优先从直接字段获取，然后从rawOcrData的多个可能字段获取
            let payeeName = itemData.payeeName || ''
            if (!payeeName && itemData.rawOcrData) {
              payeeName = itemData.rawOcrData['收款人'] ||
                         itemData.rawOcrData['收款人名称'] ||
                         itemData.rawOcrData['收款方'] || ''
            }
            console.log(`👤 [getBankReceiptField] payeeName: ${payeeName}`)
            return payeeName
          case 'payerName':
            // 优先从直接字段获取，然后从rawOcrData的多个可能字段获取
            let payerName = itemData.payerName || ''
            if (!payerName && itemData.rawOcrData) {
              payerName = itemData.rawOcrData['付款人'] ||
                         itemData.rawOcrData['付款人名称'] ||
                         itemData.rawOcrData['付款方'] || ''
            }
            console.log(`👤 [getBankReceiptField] payerName: ${payerName}`)
            return payerName
          case 'transferDate':
            // 优先从直接字段获取，然后从rawOcrData获取
            let transferDate = itemData.transferDate || ''
            if (!transferDate && itemData.rawOcrData) {
              const dateStr = itemData.rawOcrData['记账日期'] ||
                             itemData.rawOcrData['转账日期'] ||
                             itemData.rawOcrData['交易日期'] || ''
              if (dateStr) {
                transferDate = this.formatDateForInput(dateStr)
              }
            }
            console.log(`📅 [getBankReceiptField] transferDate: ${transferDate}`)
            return transferDate
          case 'serialNumber':
            // 优先从直接字段获取，然后从rawOcrData的多个可能字段获取
            let serialNumber = itemData.serialNumber || ''
            if (!serialNumber && itemData.rawOcrData) {
              serialNumber = itemData.rawOcrData['会计流水号'] ||
                            itemData.rawOcrData['回单编号'] ||
                            itemData.rawOcrData['流水号'] ||
                            itemData.rawOcrData['交易流水'] || ''
            }
            console.log(`🔢 [getBankReceiptField] serialNumber: ${serialNumber}`)
            return serialNumber
          default:
            console.warn(`⚠️ [getBankReceiptField] 未知字段: ${fieldName}`)
            return ''
        }
      } catch (error) {
        console.error('❌ [getBankReceiptField] 获取银行回单字段失败:', error, fieldName, itemData)
        return ''
      }
    },
    
    getStatusText(status) {
      const statusMap = {
        'UPLOADING': '上传中',
        'PROCESSING': '处理中',
        'RECOGNIZING': '识别中',
        'PREVIEWING': '预览中',
        'SAVING': '保存中',
        'COMPLETED': '已完成',
        'FAILED': '失败',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    },
    
    getItemStatusText(status) {
      const statusMap = {
        'PENDING': '待处理',
        'PROCESSING': '处理中',
        'RECOGNIZING': '识别中',
        'SUCCESS': '成功',
        'FAILED': '失败',
        'SKIPPED': '跳过'
      }
      return statusMap[status] || status
    },




  }
}
</script>

<style scoped>
.batch-preview {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.task-info h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.task-meta {
  display: flex;
  gap: 12px;
  align-items: center;
}

.task-type,
.task-status,
.task-progress {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.task-type {
  background: #e6f7ff;
  color: #1890ff;
}

.task-status {
  background: #f0f0f0;
  color: #666;
}

.task-status.RECOGNIZING {
  background: #fff7e6;
  color: #fa8c16;
}

.task-status.PREVIEWING {
  background: #f6ffed;
  color: #52c41a;
}

.task-progress {
  background: #f0f8ff;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-refresh,
.btn-recognize,
.btn-save {
  padding: 8px 16px;
  border: 1px solid;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-refresh {
  background: white;
  border-color: #d9d9d9;
  color: #666;
}

.btn-refresh:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.btn-recognize {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.btn-recognize:hover:not(:disabled) {
  background: #40a9ff;
  border-color: #40a9ff;
}

.btn-rerecognize {
  background: #fa8c16;
  border-color: #fa8c16;
  color: white;
}

.btn-rerecognize:hover:not(:disabled) {
  background: #ffa940;
  border-color: #ffa940;
}

.btn-save {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.btn-save:hover:not(:disabled) {
  background: #40a9ff;
  border-color: #40a9ff;
}

.btn-refresh:disabled,
.btn-recognize:disabled,
.btn-save:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
  cursor: not-allowed;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f0f8ff;
  border-radius: 6px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}

.progress-bar {
  height: 6px;
  background: #e8e8e8;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.filter-options {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-options select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.result-count {
  color: #666;
  font-size: 14px;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.result-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.result-item.selected {
  border-color: #1890ff;
  background: #f0f8ff;
}

.result-item.error {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.item-left {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  min-width: 200px;
}

.item-checkbox {
  margin-top: 8px;
  cursor: pointer;
}

.image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.image-preview img {
  width: 120px;
  height: 80px;
  object-fit: cover;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
}

.image-preview img:hover {
  border-color: #1890ff;
}

.image-info {
  text-align: center;
}

.image-name {
  font-size: 12px;
  color: #333;
  margin-bottom: 2px;
  word-break: break-all;
}

.image-meta {
  font-size: 11px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 8px;
}

.split-badge {
  background: #52c41a;
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  font-weight: 500;
}

.original-link {
  margin-top: 4px;
}

.original-link a {
  color: #1890ff;
  font-size: 11px;
  text-decoration: none;
}

.original-link a:hover {
  text-decoration: underline;
}

.item-center {
  flex: 1;
}

.form-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.form-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-row label {
  min-width: 80px;
  font-size: 14px;
  color: #333;
}

.form-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.form-input:focus {
  border-color: #1890ff;
  outline: none;
}

.item-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  min-width: 120px;
}

.item-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.item-status.SUCCESS {
  background: #f6ffed;
  color: #52c41a;
}

.item-status.FAILED {
  background: #fff2f0;
  color: #ff4d4f;
}

.item-status.RECOGNIZING {
  background: #fff7e6;
  color: #fa8c16;
}

.item-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.btn-retry,
.btn-edit,
.btn-remove {
  padding: 4px 8px;
  border: 1px solid;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  text-align: center;
  min-width: 60px;
}

.btn-retry {
  background: #fa8c16;
  border-color: #fa8c16;
  color: white;
}

.btn-change-type {
  background: white;
  border-color: #1890ff;
  color: #1890ff;
}

.btn-edit {
  background: white;
  border-color: #d9d9d9;
  color: #666;
}

.btn-remove {
  background: white;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.btn-retry:hover:not(:disabled) {
  background: #ffa940;
  border-color: #ffa940;
}



.btn-edit:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.btn-remove:hover {
  background: #ff4d4f;
  color: white;
}

.confidence-score {
  font-size: 11px;
  color: #999;
  text-align: center;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
}

.page-btn:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.page-btn:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

.page-info {
  padding: 6px 12px;
  color: #666;
}

.empty-state,
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state i,
.loading-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.loading-state i {
  animation: rotate 1s linear infinite;
}

/* 类型更改模态框样式 */
.change-type-content {
  padding: 16px 0;
}

.current-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.current-info h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #333;
}

.current-info p {
  margin: 8px 0;
  font-size: 14px;
  color: #666;
}

.type-selection h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

.type-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-option {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 2px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.type-option:hover {
  border-color: #1890ff;
}

.type-option input[type="radio"] {
  margin-right: 12px;
}

.type-option input[type="radio"]:disabled {
  cursor: not-allowed;
}

.type-option:has(input:disabled) {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
}

.type-option:has(input:checked) {
  border-color: #1890ff;
  background: #f0f8ff;
}

.option-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
}

.option-text i {
  font-size: 16px;
  color: #1890ff;
}

.warning-note {
  margin-top: 24px;
  padding: 12px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.warning-note i {
  color: #fa8c16;
  font-size: 16px;
  margin-top: 2px;
}

.warning-note p {
  margin: 0;
  font-size: 14px;
  color: #fa8c16;
}

/* 批量更改类型按钮样式 */
.btn-batch-change-type {
  background: #ff9900;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 12px;
}

.btn-batch-change-type:hover {
  background: #e68a00;
}

.btn-batch-change-type:disabled {
  background: #c5c8ce;
  cursor: not-allowed;
}




</style>
