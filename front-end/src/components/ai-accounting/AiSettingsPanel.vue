<template>
  <div class="ai-settings-panel">
    <!-- AI模型配置 -->
    <h-card title="AI模型配置" class="config-card">
      <h-form :model="modelConfig" :rules="modelRules" ref="modelForm" label-width="120px">
        <h-row :gutter="24">
          <h-col :span="12">
            <h-form-item label="模型版本" prop="version">
              <h-select v-model="modelConfig.version" placeholder="选择模型版本">
                <h-option value="v2.1" label="v2.1 (推荐)" />
                <h-option value="v2.0" label="v2.0" />
                <h-option value="v1.9" label="v1.9" />
              </h-select>
            </h-form-item>
          </h-col>
          <h-col :span="12">
            <h-form-item label="置信度阈值" prop="confidenceThreshold">
              <h-slider
                v-model="modelConfig.confidenceThreshold"
                :min="0.5"
                :max="1.0"
                :step="0.01"
                show-input
              />
            </h-form-item>
          </h-col>
        </h-row>
        <h-row :gutter="24">
          <h-col :span="12">
            <h-form-item label="自动处理" prop="autoProcess">
              <h-switch v-model="modelConfig.autoProcess" />
              <span class="form-tip">开启后将自动处理置信度高于阈值的数据</span>
            </h-form-item>
          </h-col>
          <h-col :span="12">
            <h-form-item label="批量处理大小" prop="batchSize">
              <h-input-number
                v-model="modelConfig.batchSize"
                :min="1"
                :max="100"
                placeholder="批量处理大小"
              />
            </h-form-item>
          </h-col>
        </h-row>
      </h-form>
    </h-card>

    <!-- 科目匹配规则 -->
    <h-card title="科目匹配规则" class="rules-card" style="margin-top: 16px;">
      <template slot="extra">
        <h-button type="primary" size="small" @click="addRule">
          <i class="h-icon-plus"></i> 添加规则
        </h-button>
      </template>
      
      <h-table :data="matchingRules" stripe>
        <h-table-column prop="keyword" label="关键词" width="200" />
        <h-table-column prop="subjectCode" label="科目编码" width="120" />
        <h-table-column prop="subjectName" label="科目名称" width="200" />
        <h-table-column prop="priority" label="优先级" width="100" align="center">
          <template slot-scope="{ row }">
            <h-tag :type="getPriorityTagType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </h-tag>
          </template>
        </h-table-column>
        <h-table-column prop="enabled" label="状态" width="100" align="center">
          <template slot-scope="{ row }">
            <h-switch v-model="row.enabled" @change="updateRule(row)" />
          </template>
        </h-table-column>
        <h-table-column label="操作" width="150" fixed="right">
          <template slot-scope="{ row, $index }">
            <h-button size="small" @click="editRule(row, $index)">编辑</h-button>
            <h-button size="small" type="danger" @click="deleteRule($index)">删除</h-button>
          </template>
        </h-table-column>
      </h-table>
    </h-card>

    <!-- 处理策略配置 -->
    <h-card title="处理策略配置" class="strategy-card" style="margin-top: 16px;">
      <h-form :model="strategyConfig" label-width="150px">
        <h-row :gutter="24">
          <h-col :span="12">
            <h-form-item label="重复数据处理">
              <h-radio-group v-model="strategyConfig.duplicateHandling">
                <h-radio value="skip">跳过</h-radio>
                <h-radio value="overwrite">覆盖</h-radio>
                <h-radio value="merge">合并</h-radio>
              </h-radio-group>
            </h-form-item>
          </h-col>
          <h-col :span="12">
            <h-form-item label="错误数据处理">
              <h-radio-group v-model="strategyConfig.errorHandling">
                <h-radio value="ignore">忽略</h-radio>
                <h-radio value="manual">人工审核</h-radio>
                <h-radio value="retry">重试处理</h-radio>
              </h-radio-group>
            </h-form-item>
          </h-col>
        </h-row>
        <h-row :gutter="24">
          <h-col :span="12">
            <h-form-item label="数据备份">
              <h-switch v-model="strategyConfig.enableBackup" />
              <span class="form-tip">处理前自动备份原始数据</span>
            </h-form-item>
          </h-col>
          <h-col :span="12">
            <h-form-item label="日志记录级别">
              <h-select v-model="strategyConfig.logLevel">
                <h-option value="debug" label="调试" />
                <h-option value="info" label="信息" />
                <h-option value="warn" label="警告" />
                <h-option value="error" label="错误" />
              </h-select>
            </h-form-item>
          </h-col>
        </h-row>
      </h-form>
    </h-card>

    <!-- 系统监控 -->
    <h-card title="系统监控" class="monitor-card" style="margin-top: 16px;">
      <h-row :gutter="24">
        <h-col :span="6">
          <div class="monitor-item">
            <div class="monitor-label">CPU使用率</div>
            <div class="monitor-value">{{ systemStatus.cpuUsage }}%</div>
            <h-progress :percent="systemStatus.cpuUsage" :status="getProgressStatus(systemStatus.cpuUsage)" />
          </div>
        </h-col>
        <h-col :span="6">
          <div class="monitor-item">
            <div class="monitor-label">内存使用率</div>
            <div class="monitor-value">{{ systemStatus.memoryUsage }}%</div>
            <h-progress :percent="systemStatus.memoryUsage" :status="getProgressStatus(systemStatus.memoryUsage)" />
          </div>
        </h-col>
        <h-col :span="6">
          <div class="monitor-item">
            <div class="monitor-label">处理队列</div>
            <div class="monitor-value">{{ systemStatus.queueSize }}</div>
            <div class="monitor-desc">待处理任务数</div>
          </div>
        </h-col>
        <h-col :span="6">
          <div class="monitor-item">
            <div class="monitor-label">服务状态</div>
            <div class="monitor-value">
              <h-tag :type="systemStatus.serviceStatus === 'running' ? 'success' : 'danger'">
                {{ systemStatus.serviceStatus === 'running' ? '运行中' : '已停止' }}
              </h-tag>
            </div>
          </div>
        </h-col>
      </h-row>
    </h-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <h-button type="primary" @click="saveConfig" :loading="saveLoading">
        保存配置
      </h-button>
      <h-button @click="resetConfig">重置配置</h-button>
      <h-button @click="exportConfig">导出配置</h-button>
      <h-button @click="importConfig">导入配置</h-button>
    </div>

    <!-- 添加/编辑规则弹窗 -->
    <h-modal
      v-model="ruleDialog.visible"
      :title="ruleDialog.isEdit ? '编辑规则' : '添加规则'"
      width="500px"
    >
      <h-form :model="ruleDialog.form" :rules="ruleRules" ref="ruleForm" label-width="100px">
        <h-form-item label="关键词" prop="keyword">
          <h-input v-model="ruleDialog.form.keyword" placeholder="请输入关键词" />
        </h-form-item>
        <h-form-item label="科目编码" prop="subjectCode">
          <h-input v-model="ruleDialog.form.subjectCode" placeholder="请输入科目编码" />
        </h-form-item>
        <h-form-item label="科目名称" prop="subjectName">
          <h-input v-model="ruleDialog.form.subjectName" placeholder="请输入科目名称" />
        </h-form-item>
        <h-form-item label="优先级" prop="priority">
          <h-select v-model="ruleDialog.form.priority">
            <h-option value="high" label="高" />
            <h-option value="medium" label="中" />
            <h-option value="low" label="低" />
          </h-select>
        </h-form-item>
      </h-form>

      <template slot="footer">
        <h-button @click="ruleDialog.visible = false">取消</h-button>
        <h-button type="primary" @click="saveRule">确定</h-button>
      </template>
    </h-modal>
  </div>
</template>

<script>
export default {
  name: 'AiSettingsPanel',
  data() {
    return {
      saveLoading: false,
      modelConfig: {
        version: 'v2.1',
        confidenceThreshold: 0.85,
        autoProcess: true,
        batchSize: 20
      },
      strategyConfig: {
        duplicateHandling: 'skip',
        errorHandling: 'manual',
        enableBackup: true,
        logLevel: 'info'
      },
      systemStatus: {
        cpuUsage: 0,
        memoryUsage: 0,
        queueSize: 0,
        serviceStatus: 'running'
      },
      matchingRules: [],
      ruleDialog: {
        visible: false,
        isEdit: false,
        editIndex: -1,
        form: {
          keyword: '',
          subjectCode: '',
          subjectName: '',
          priority: 'medium'
        }
      },
      modelRules: {
        version: [
          { required: true, message: '请选择模型版本', trigger: 'change' }
        ],
        confidenceThreshold: [
          { required: true, message: '请设置置信度阈值', trigger: 'blur' }
        ],
        batchSize: [
          { required: true, message: '请设置批量处理大小', trigger: 'blur' }
        ]
      },
      ruleRules: {
        keyword: [
          { required: true, message: '请输入关键词', trigger: 'blur' }
        ],
        subjectCode: [
          { required: true, message: '请输入科目编码', trigger: 'blur' }
        ],
        subjectName: [
          { required: true, message: '请输入科目名称', trigger: 'blur' }
        ],
        priority: [
          { required: true, message: '请选择优先级', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.loadConfig()
    this.loadMatchingRules()
    this.startMonitoring()
  },
  beforeDestroy() {
    this.stopMonitoring()
  },
  methods: {
    /**
     * 加载配置
     */
    async loadConfig() {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 模拟加载配置数据
        this.modelConfig = {
          version: 'v2.1',
          confidenceThreshold: 0.85,
          autoProcess: true,
          batchSize: 20
        }
        
        this.strategyConfig = {
          duplicateHandling: 'skip',
          errorHandling: 'manual',
          enableBackup: true,
          logLevel: 'info'
        }
      } catch (error) {
        this.$Message.error('加载配置失败')
        console.error('加载配置失败:', error)
      }
    },

    /**
     * 加载匹配规则
     */
    async loadMatchingRules() {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))
        
        this.matchingRules = [
          {
            id: 1,
            keyword: '银行存款',
            subjectCode: '1002',
            subjectName: '银行存款',
            priority: 'high',
            enabled: true
          },
          {
            id: 2,
            keyword: '应收账款',
            subjectCode: '1131',
            subjectName: '应收账款',
            priority: 'high',
            enabled: true
          },
          {
            id: 3,
            keyword: '管理费用',
            subjectCode: '6602',
            subjectName: '管理费用',
            priority: 'medium',
            enabled: true
          },
          {
            id: 4,
            keyword: '销售费用',
            subjectCode: '6601',
            subjectName: '销售费用',
            priority: 'medium',
            enabled: false
          }
        ]
      } catch (error) {
        this.$Message.error('加载匹配规则失败')
        console.error('加载匹配规则失败:', error)
      }
    },

    /**
     * 开始系统监控
     */
    startMonitoring() {
      this.monitorTimer = setInterval(() => {
        this.updateSystemStatus()
      }, 5000)
      this.updateSystemStatus()
    },

    /**
     * 停止系统监控
     */
    stopMonitoring() {
      if (this.monitorTimer) {
        clearInterval(this.monitorTimer)
      }
    },

    /**
     * 更新系统状态
     */
    updateSystemStatus() {
      // 模拟系统状态数据
      this.systemStatus = {
        cpuUsage: Math.floor(Math.random() * 30) + 20,
        memoryUsage: Math.floor(Math.random() * 40) + 30,
        queueSize: Math.floor(Math.random() * 50),
        serviceStatus: 'running'
      }
    },

    /**
     * 保存配置
     */
    async saveConfig() {
      try {
        await this.$refs.modelForm.validate()
        this.saveLoading = true
        
        // 模拟保存配置
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        this.$Message.success('配置保存成功')
      } catch (error) {
        this.$Message.error('配置保存失败')
        console.error('配置保存失败:', error)
      } finally {
        this.saveLoading = false
      }
    },

    /**
     * 重置配置
     */
    resetConfig() {
      this.$Confirm('确定要重置所有配置到默认值吗？').then(() => {
        this.loadConfig()
        this.$Message.success('配置已重置')
      })
    },

    /**
     * 导出配置
     */
    exportConfig() {
      const config = {
        modelConfig: this.modelConfig,
        strategyConfig: this.strategyConfig,
        matchingRules: this.matchingRules
      }
      
      const blob = new Blob([JSON.stringify(config, null, 2)], {
        type: 'application/json'
      })
      
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'ai-config.json'
      a.click()
      URL.revokeObjectURL(url)
      
      this.$Message.success('配置导出成功')
    },

    /**
     * 导入配置
     */
    importConfig() {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.json'
      input.onchange = (event) => {
        const file = event.target.files[0]
        if (file) {
          const reader = new FileReader()
          reader.onload = (e) => {
            try {
              const config = JSON.parse(e.target.result)
              this.modelConfig = config.modelConfig || this.modelConfig
              this.strategyConfig = config.strategyConfig || this.strategyConfig
              this.matchingRules = config.matchingRules || this.matchingRules
              this.$Message.success('配置导入成功')
            } catch (error) {
              this.$Message.error('配置文件格式错误')
            }
          }
          reader.readAsText(file)
        }
      }
      input.click()
    },

    /**
     * 添加规则
     */
    addRule() {
      this.ruleDialog = {
        visible: true,
        isEdit: false,
        editIndex: -1,
        form: {
          keyword: '',
          subjectCode: '',
          subjectName: '',
          priority: 'medium'
        }
      }
    },

    /**
     * 编辑规则
     */
    editRule(rule, index) {
      this.ruleDialog = {
        visible: true,
        isEdit: true,
        editIndex: index,
        form: { ...rule }
      }
    },

    /**
     * 保存规则
     */
    async saveRule() {
      try {
        await this.$refs.ruleForm.validate()
        
        if (this.ruleDialog.isEdit) {
          // 编辑规则
          this.$set(this.matchingRules, this.ruleDialog.editIndex, {
            ...this.ruleDialog.form,
            id: this.matchingRules[this.ruleDialog.editIndex].id,
            enabled: this.matchingRules[this.ruleDialog.editIndex].enabled
          })
          this.$Message.success('规则更新成功')
        } else {
          // 添加规则
          const newRule = {
            ...this.ruleDialog.form,
            id: Date.now(),
            enabled: true
          }
          this.matchingRules.push(newRule)
          this.$Message.success('规则添加成功')
        }
        
        this.ruleDialog.visible = false
      } catch (error) {
        console.error('保存规则失败:', error)
      }
    },

    /**
     * 删除规则
     */
    deleteRule(index) {
      this.$Confirm('确定要删除这条规则吗？').then(() => {
        this.matchingRules.splice(index, 1)
        this.$Message.success('规则删除成功')
      })
    },

    /**
     * 更新规则
     */
    updateRule(rule) {
      // 模拟更新规则状态
      this.$Message.success(`规则"${rule.keyword}"状态已更新`)
    },

    /**
     * 获取优先级标签类型
     */
    getPriorityTagType(priority) {
      switch (priority) {
        case 'high': return 'danger'
        case 'medium': return 'warning'
        case 'low': return 'info'
        default: return 'info'
      }
    },

    /**
     * 获取优先级文本
     */
    getPriorityText(priority) {
      switch (priority) {
        case 'high': return '高'
        case 'medium': return '中'
        case 'low': return '低'
        default: return '未知'
      }
    },

    /**
     * 获取进度条状态
     */
    getProgressStatus(value) {
      if (value >= 80) return 'exception'
      if (value >= 60) return 'active'
      return 'success'
    }
  }
}
</script>

<style lang="less" scoped>
.ai-settings-panel {
  .config-card,
  .rules-card,
  .strategy-card,
  .monitor-card {
    margin-bottom: 16px;
  }
  
  .form-tip {
    margin-left: 8px;
    color: #999;
    font-size: 12px;
  }
  
  .monitor-item {
    text-align: center;
    
    .monitor-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
    
    .monitor-value {
      font-size: 24px;
      font-weight: 600;
      color: #1890ff;
      margin-bottom: 8px;
    }
    
    .monitor-desc {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }
  }
  
  .action-buttons {
    margin-top: 24px;
    text-align: center;
    
    .h-button {
      margin: 0 8px;
    }
  }
}
</style>