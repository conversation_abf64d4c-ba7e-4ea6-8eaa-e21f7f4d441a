<template>
  <div class="preview-matching-result">
    <!-- 银证基本信息 -->
    <div class="voucher-info">
      <h4 class="section-title">
        <i class="h-icon-file-text"></i>
        银证信息
      </h4>
      <h-row :gutter="16">
        <h-col :span="8">
          <div class="info-item">
            <label>银行回单号：</label>
            <span>{{ bankReceipts.receiptsNo }}</span>
          </div>
        </h-col>
        <h-col :span="8">
          <div class="info-item">
            <label>日期：</label>
            <span>{{ formatDate(bankReceipts.receiptsDate) }}</span>
          </div>
        </h-col>
        <h-col :span="8">
          <div class="info-item">
            <label>金额：</label>
            <span :class="getAmountClass(bankReceipts.type)">
              {{ formatAmount(bankReceipts.amount) }}
            </span>
          </div>
        </h-col>
      </h-row>
      <h-row :gutter="16" style="margin-top: 10px;">
        <h-col :span="12">
          <div class="info-item">
            <label>摘要：</label>
            <span>{{ bankReceipts.summary || '-' }}</span>
          </div>
        </h-col>
        <h-col :span="12">
          <div class="info-item">
            <label>类型：</label>
            <h-tag :type="getTypeTagType(bankReceipts.type)">
              {{ getTypeText(bankReceipts.type) }}
            </h-tag>
          </div>
        </h-col>
      </h-row>
    </div>

    <!-- AI匹配结果 -->
    <div class="matching-result">
      <h4 class="section-title">
        <i class="h-icon-robot"></i>
        AI匹配结果
        <h-tag 
          :type="matchingResult.success ? 'success' : 'danger'"
          style="margin-left: 10px;"
        >
          {{ matchingResult.success ? '匹配成功' : '匹配失败' }}
        </h-tag>
      </h4>

      <!-- 匹配成功时显示科目推荐 -->
      <div v-if="matchingResult.success" class="success-result">
        <!-- 置信度和分析 -->
        <div class="confidence-section">
          <h-row :gutter="16">
            <h-col :span="12">
              <div class="confidence-item">
                <label>整体置信度：</label>
                <h-progress 
                  :percentage="Math.round(matchingResult.confidence * 100)"
                  :color="getConfidenceColor(matchingResult.confidence)"
                  :show-text="true"
                />
              </div>
            </h-col>
            <h-col :span="12">
              <div class="confidence-item">
                <label>匹配质量：</label>
                <h-tag :type="getQualityTagType(matchingResult.confidence)">
                  {{ getQualityText(matchingResult.confidence) }}
                </h-tag>
              </div>
            </h-col>
          </h-row>
        </div>

        <!-- 推荐科目列表 -->
        <div class="subjects-section">
          <h5>推荐会计科目：</h5>
          <div class="subjects-grid">
            <!-- 借方科目 -->
            <div class="debit-subjects">
              <h6 class="subject-type-title debit-title">
                <i class="h-icon-arrow-up"></i>
                借方科目
              </h6>
              <div 
                v-for="(subject, index) in debitSubjects" 
                :key="'debit-' + index"
                class="subject-card debit-card"
              >
                <div class="subject-header">
                  <span class="subject-name">{{ subject.subject.name }}</span>
                  <span class="subject-code">({{ subject.subject.code }})</span>
                  <h-tag 
                    v-if="subject.isPrimary" 
                    type="success" 
                    size="small"
                    style="margin-left: 8px;"
                  >
                    主科目
                  </h-tag>
                </div>
                <div class="subject-details">
                  <div class="subject-amount">
                    金额：<span class="amount-text">{{ formatAmount(subject.amount) }}</span>
                  </div>
                  <div class="subject-confidence">
                    置信度：
                    <h-progress 
                      :percentage="Math.round(subject.confidence * 100)"
                      :color="getConfidenceColor(subject.confidence)"
                      size="small"
                      :show-text="false"
                      style="width: 60px; display: inline-block;"
                    />
                    <span style="margin-left: 8px;">{{ Math.round(subject.confidence * 100) }}%</span>
                  </div>
                  <div v-if="subject.reason" class="subject-reason">
                    原因：{{ subject.reason }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 贷方科目 -->
            <div class="credit-subjects">
              <h6 class="subject-type-title credit-title">
                <i class="h-icon-arrow-down"></i>
                贷方科目
              </h6>
              <div 
                v-for="(subject, index) in creditSubjects" 
                :key="'credit-' + index"
                class="subject-card credit-card"
              >
                <div class="subject-header">
                  <span class="subject-name">{{ subject.subject.name }}</span>
                  <span class="subject-code">({{ subject.subject.code }})</span>
                  <h-tag 
                    v-if="subject.isPrimary" 
                    type="success" 
                    size="small"
                    style="margin-left: 8px;"
                  >
                    主科目
                  </h-tag>
                </div>
                <div class="subject-details">
                  <div class="subject-amount">
                    金额：<span class="amount-text">{{ formatAmount(subject.amount) }}</span>
                  </div>
                  <div class="subject-confidence">
                    置信度：
                    <h-progress 
                      :percentage="Math.round(subject.confidence * 100)"
                      :color="getConfidenceColor(subject.confidence)"
                      size="small"
                      :show-text="false"
                      style="width: 60px; display: inline-block;"
                    />
                    <span style="margin-left: 8px;">{{ Math.round(subject.confidence * 100) }}%</span>
                  </div>
                  <div v-if="subject.reason" class="subject-reason">
                    原因：{{ subject.reason }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI分析和建议 -->
        <div v-if="matchingResult.aiSummary || matchingResult.reason" class="analysis-section">
          <h5>AI分析：</h5>
          <div class="analysis-content">
            <div v-if="matchingResult.reason" class="analysis-item">
              <label>匹配原因：</label>
              <p>{{ matchingResult.reason }}</p>
            </div>
            <div v-if="matchingResult.aiSummary" class="analysis-item">
              <label>摘要建议：</label>
              <p>{{ matchingResult.aiSummary }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 匹配失败时显示错误信息 -->
      <div v-else class="error-result">
        <h-alert
          type="error"
          :message="matchingResult.errorMessage || '匹配失败，请检查数据或联系管理员'"
          show-icon
        />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <h-button @click="$emit('cancel')">
        取消
      </h-button>
      <h-button 
        v-if="matchingResult.success && matchingResult.confidence >= 0.7"
        type="primary" 
        @click="confirmGenerate"
      >
        确认生成凭证
      </h-button>
      <h-button 
        v-else-if="matchingResult.success"
        type="warning" 
        @click="confirmGenerate"
      >
        强制生成凭证
      </h-button>
    </div>
  </div>
</template>

<script>
import { formatDate, formatAmount } from '@/utils/format'

export default {
  name: 'PreviewMatchingResult',
  props: {
    bankReceipts: {
      type: Object,
      required: true
    },
    matchingResult: {
      type: Object,
      required: true
    }
  },
  computed: {
    /**
     * 借方科目
     */
    debitSubjects() {
      if (!this.matchingResult.success || !this.matchingResult.debitSubjects) {
        return []
      }
      return this.matchingResult.debitSubjects
    },
    
    /**
     * 贷方科目
     */
    creditSubjects() {
      if (!this.matchingResult.success || !this.matchingResult.creditSubjects) {
        return []
      }
      return this.matchingResult.creditSubjects
    }
  },
  methods: {
    /**
     * 确认生成凭证
     */
    confirmGenerate() {
      if (this.matchingResult.confidence < 0.7) {
        this.$Confirm(`当前匹配置信度为 ${Math.round(this.matchingResult.confidence * 100)}%，低于推荐阈值70%。是否确认生成凭证？`).then(() => {
          this.$emit('confirm', this.bankReceipts)
        })
      } else {
        this.$emit('confirm', this.bankReceipts)
      }
    },
    
    /**
     * 获取置信度颜色
     */
    getConfidenceColor(confidence) {
      if (confidence >= 0.8) return '#52c41a'
      if (confidence >= 0.6) return '#faad14'
      return '#ff4d4f'
    },
    
    /**
     * 获取质量标签类型
     */
    getQualityTagType(confidence) {
      if (confidence >= 0.8) return 'success'
      if (confidence >= 0.6) return 'warning'
      return 'danger'
    },
    
    /**
     * 获取质量文本
     */
    getQualityText(confidence) {
      if (confidence >= 0.8) return '高质量匹配'
      if (confidence >= 0.6) return '中等质量匹配'
      return '低质量匹配'
    },
    
    /**
     * 获取类型标签类型
     */
    getTypeTagType(type) {
      const typeMap = {
        'income': 'success',
        'expense': 'danger',
        'transfer': 'info'
      }
      return typeMap[type] || 'default'
    },
    
    /**
     * 获取类型文本
     */
    getTypeText(type) {
      const typeMap = {
        'income': '收入',
        'expense': '支出',
        'transfer': '转账'
      }
      return typeMap[type] || type
    },
    
    /**
     * 获取金额样式类
     */
    getAmountClass(type) {
      return {
        'amount-income': type === 'income',
        'amount-expense': type === 'expense',
        'amount-transfer': type === 'transfer'
      }
    },
    
    // 格式化方法
    formatDate,
    formatAmount
  }
}
</script>

<style scoped>
.preview-matching-result {
  padding: 0;
}

.section-title {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 10px;
}

.section-title i {
  color: #1890ff;
}

.voucher-info {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-item {
  margin-bottom: 8px;
}

.info-item label {
  font-weight: 600;
  color: #666;
  margin-right: 8px;
}

.matching-result {
  margin-bottom: 25px;
}

.confidence-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 6px;
}

.confidence-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.confidence-item label {
  font-weight: 600;
  color: #666;
  min-width: 80px;
}

.subjects-section {
  margin-bottom: 20px;
}

.subjects-section h5 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.subjects-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.subject-type-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

.debit-title {
  color: #52c41a;
}

.credit-title {
  color: #ff4d4f;
}

.subject-card {
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  margin-bottom: 10px;
}

.debit-card {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.credit-card {
  background: #fff2f0;
  border-color: #ffb3b3;
}

.subject-header {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.subject-name {
  font-weight: 600;
  color: #333;
}

.subject-code {
  color: #666;
  font-size: 12px;
  margin-left: 8px;
}

.subject-details {
  font-size: 12px;
  color: #666;
}

.subject-details > div {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.amount-text {
  font-weight: 600;
  color: #333;
}

.subject-confidence {
  align-items: center;
}

.subject-reason {
  color: #999;
  font-style: italic;
}

.analysis-section {
  padding: 15px;
  background: #fafafa;
  border-radius: 6px;
}

.analysis-section h5 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.analysis-item {
  margin-bottom: 10px;
}

.analysis-item label {
  font-weight: 600;
  color: #666;
  display: block;
  margin-bottom: 5px;
}

.analysis-item p {
  margin: 0;
  line-height: 1.5;
  color: #333;
}

.error-result {
  padding: 20px;
}

.action-buttons {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

.action-buttons .h-button {
  margin-left: 10px;
}

/* 金额样式 */
.amount-income {
  color: #52c41a;
  font-weight: 600;
}

.amount-expense {
  color: #ff4d4f;
  font-weight: 600;
}

.amount-transfer {
  color: #1890ff;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subjects-grid {
    grid-template-columns: 1fr;
  }
  
  .subject-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .subject-code {
    margin-left: 0;
    margin-top: 5px;
  }
}
</style>