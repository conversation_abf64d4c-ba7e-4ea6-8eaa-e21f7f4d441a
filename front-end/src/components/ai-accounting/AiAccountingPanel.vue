<template>
  <div class="ai-accounting-panel">
    <!-- 头部标题 -->
    <div class="panel-header">
      <h3 class="panel-title">
        <i class="h-icon-robot"></i>
        AI智能会计助手
      </h3>
      <div class="panel-actions">
        <h-button size="s" @click="refreshData">
          <i class="h-icon-refresh"></i>
          刷新
        </h-button>
      </div>
    </div>

    <!-- 功能选项卡 -->
    <h-tabs v-model="activeTab" @change="onTabChange">
      <h-tab-pane name="statistics" title="历史与建议">
        <ai-analysis-history ref="statisticsPanel" />
      </h-tab-pane>
      <h-tab-pane name="settings" title="AI配置管理">
        <ai-settings-panel ref="settingsPanel" />
      </h-tab-pane>
      <h-tab-pane name="notice" title="功能说明">
        <div class="notice-panel">
          <h-alert type="info" title="功能整合说明">
            <p>银行回单智能处理和票据智能处理功能已整合到关联管理模块中。</p>
            <p>请使用归并关联功能来管理票据和银行回单的关联关系。</p>
          </h-alert>
        </div>
      </h-tab-pane>
    </h-tabs>
  </div>
</template>

<script>
// import BankReceiptsAiPanel from './BankReceiptsAiPanel.vue' // 已删除，功能整合到关联管理模块
// import BillAiPanel from './BillAiPanel.vue' // 已删除，功能整合到关联管理模块
import AiAnalysisHistory from './AiStatisticsPanel.vue'
import AiSettingsPanel from './AiSettingsPanel.vue'

export default {
  name: 'AiAccountingPanel',
  components: {
    // BankReceiptsAiPanel, // 已删除，功能整合到关联管理模块
    // BillAiPanel, // 已删除，功能整合到关联管理模块
    AiAnalysisHistory,
    AiSettingsPanel
  },
  data() {
    return {
      activeTab: 'statistics' // 默认显示统计分析
    }
  },
  mounted() {
    this.initializePanel()
  },
  methods: {
    /**
     * 初始化面板
     */
    initializePanel() {
      this.refreshData()
    },

    /**
     * 刷新数据
     */
    refreshData() {
      const currentPanel = this.getCurrentPanel()
      if (currentPanel && typeof currentPanel.refreshData === 'function') {
        currentPanel.refreshData()
      }
    },

    /**
     * 选项卡切换事件
     */
    onTabChange(tabName) {
      this.activeTab = tabName
      this.$nextTick(() => {
        this.refreshData()
      })
    },

    /**
     * 获取当前激活的面板组件
     */
    getCurrentPanel() {
      switch (this.activeTab) {
        case 'statistics':
          return this.$refs.statisticsPanel
        case 'settings':
          return this.$refs.settingsPanel
        case 'notice':
          return null // 功能说明面板不需要刷新
        default:
          return null
      }
    }
  }
}
</script>

<style lang="less" scoped>
.ai-accounting-panel {
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .panel-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;

      i {
        margin-right: 8px;
        color: #1890ff;
      }
    }
  }

  .notice-panel {
    padding: 20px;
    text-align: center;

    p {
      margin: 8px 0;
      color: #666;
      line-height: 1.6;
    }
  }
}
</style>

<style scoped>
.ai-accounting-panel {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e8e8e8;
}

.panel-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-title i {
  color: #1890ff;
  font-size: 20px;
}

.panel-actions {
  display: flex;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-accounting-panel {
    padding: 15px;
  }
  
  .panel-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .panel-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
