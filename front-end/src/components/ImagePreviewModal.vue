<template>
  <div class="image-preview-modal" v-if="visible" @click="handleBackdropClick">
    <div class="modal-content" @click.stop>
      <!-- 头部 -->
      <div class="modal-header">
        <h3>图片预览</h3>
        <button class="close-btn" @click="close">
          <i class="h-icon-close"></i>
        </button>
      </div>
      
      <!-- 图片信息 -->
      <div class="image-info" v-if="imageInfo">
        <div class="info-item">
          <span class="label">文件名:</span>
          <span class="value">{{ imageInfo.fileName || '未知' }}</span>
        </div>
        <div class="info-item" v-if="imageInfo.pageNumber">
          <span class="label">页码:</span>
          <span class="value">第{{ imageInfo.pageNumber }}页</span>
        </div>
        <div class="info-item" v-if="imageInfo.subImageIndex">
          <span class="label">拆分序号:</span>
          <span class="value">{{ imageInfo.subImageIndex }}</span>
        </div>
        <div class="info-item" v-if="imageInfo.imageWidth && imageInfo.imageHeight">
          <span class="label">尺寸:</span>
          <span class="value">{{ imageInfo.imageWidth }} × {{ imageInfo.imageHeight }}</span>
        </div>
        <div class="info-item" v-if="imageInfo.isSplit">
          <span class="label">状态:</span>
          <span class="value split-badge">已拆分</span>
        </div>
      </div>
      
      <!-- 图片容器 -->
      <div class="image-container">
        <div class="image-wrapper" v-if="imageUrl">
          <img 
            :src="imageUrl" 
            :alt="imageInfo ? imageInfo.fileName : '预览图片'"
            @load="handleImageLoad"
            @error="handleImageError"
            :style="imageStyle"
          >
        </div>
        <div class="loading" v-if="loading">
          <i class="h-icon-loading"></i>
          <p>加载中...</p>
        </div>
        <div class="error" v-if="error">
          <i class="h-icon-warning"></i>
          <p>{{ error }}</p>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="modal-actions">
        <button class="btn-zoom" @click="zoomIn" :disabled="scale >= maxScale">
          <i class="h-icon-plus"></i>
          放大
        </button>
        <button class="btn-zoom" @click="zoomOut" :disabled="scale <= minScale">
          <i class="h-icon-minus"></i>
          缩小
        </button>
        <button class="btn-reset" @click="resetZoom">
          <i class="h-icon-refresh"></i>
          重置
        </button>
        <button class="btn-download" @click="downloadImage">
          <i class="h-icon-download"></i>
          下载
        </button>
        <button v-if="imageInfo && imageInfo.originalImageUrl" class="btn-original" @click="viewOriginal">
          <i class="h-icon-eye"></i>
          查看原图
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImagePreviewModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    imageUrl: {
      type: String,
      default: ''
    },
    imageInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      error: '',
      scale: 1,
      minScale: 0.1,
      maxScale: 5,
      imageStyle: {
        transform: 'scale(1)',
        transformOrigin: 'center center'
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetState()
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
    },
    imageUrl() {
      if (this.imageUrl) {
        this.resetState()
      }
    },
    scale(newVal) {
      this.imageStyle = {
        transform: `scale(${newVal})`,
        transformOrigin: 'center center',
        transition: 'transform 0.2s ease'
      }
    }
  },
  mounted() {
    // 监听键盘事件
    document.addEventListener('keydown', this.handleKeydown)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)
    document.body.style.overflow = ''
  },
  methods: {
    close() {
      this.$emit('close')
    },
    
    handleBackdropClick() {
      this.close()
    },
    
    handleKeydown(event) {
      if (!this.visible) return
      
      switch (event.key) {
        case 'Escape':
          this.close()
          break
        case '+':
        case '=':
          this.zoomIn()
          break
        case '-':
          this.zoomOut()
          break
        case '0':
          this.resetZoom()
          break
      }
    },
    
    handleImageLoad() {
      this.loading = false
      this.error = ''
    },
    
    handleImageError() {
      this.loading = false
      this.error = '图片加载失败'
    },
    
    resetState() {
      this.loading = true
      this.error = ''
      this.scale = 1
    },
    
    zoomIn() {
      if (this.scale < this.maxScale) {
        this.scale = Math.min(this.scale * 1.2, this.maxScale)
      }
    },
    
    zoomOut() {
      if (this.scale > this.minScale) {
        this.scale = Math.max(this.scale / 1.2, this.minScale)
      }
    },
    
    resetZoom() {
      this.scale = 1
    },
    
    downloadImage() {
      if (!this.imageUrl) return
      
      const link = document.createElement('a')
      link.href = this.imageUrl
      link.download = this.imageInfo.fileName || 'image.png'
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    
    viewOriginal() {
      if (this.imageInfo && this.imageInfo.originalImageUrl) {
        this.$emit('view-original', this.imageInfo.originalImageUrl)
      }
    }
  }
}
</script>

<style scoped>
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #333;
}

.image-info {
  padding: 12px 20px;
  background: #f9f9f9;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.info-item .label {
  color: #666;
  margin-right: 4px;
}

.info-item .value {
  color: #333;
  font-weight: 500;
}

.split-badge {
  background: #52c41a;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
}

.image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  max-height: 60vh;
  overflow: auto;
  background: #f5f5f5;
  position: relative;
}

.image-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.image-wrapper img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: grab;
}

.image-wrapper img:active {
  cursor: grabbing;
}

.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
}

.loading i, .error i {
  font-size: 24px;
  margin-bottom: 8px;
}

.error {
  color: #ff4d4f;
}

.modal-actions {
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  gap: 8px;
  justify-content: center;
  background: #fafafa;
}

.modal-actions button {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s;
}

.modal-actions button:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.modal-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-download {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.btn-download:hover:not(:disabled) {
  background: #40a9ff;
  border-color: #40a9ff;
  color: white;
}

.btn-original {
  background: #52c41a;
  border-color: #52c41a;
  color: white;
}

.btn-original:hover:not(:disabled) {
  background: #73d13d;
  border-color: #73d13d;
  color: white;
}
</style>
