<template>
	<div id="app">
		<!-- 移动端布局：只有内容区域 -->
		<div v-if="isMobilePage" class="mobile-app-frame">
			<router-view v-if="isRouterAlive"></router-view>
		</div>

		<!-- 桌面端布局：完整的布局结构 -->
		<Layout v-else class="app-frame" :siderCollapsed="sliderCollapsed" :siderFixed="layoutConfig.siderFixed">
			<Sider :theme="layoutConfig.siderTheme">
				<appMenu :theme="layoutConfig.siderTheme"></appMenu>
			</Sider>
			<Layout :headerFixed="layoutConfig.headerFixed">
				<HHeader theme="white">
					<appHead @openSetting="openSetting=true" :layoutConfig="layoutConfig"></appHead>
				</HHeader>
				<SysTabs v-if="layoutConfig.showSystab" homePage="Home"></SysTabs>
				<Content>
					<div class="app-frame-content ">
						<router-view v-if="isRouterAlive"></router-view>
					</div>
					<HFooter>
						<appFooter></appFooter>
					</HFooter>
				</Content>
			</Layout>
		</Layout>
	</div>
</template>
<script>

	import appHead from './app/app-header';
	import appMenu from './app/app-menu';
	import appFooter from './app/app-footer';
	import {mapState} from 'vuex';
	import SysTabs from './app/sys-tabs/sys-tabs';

	export default {
		name: "FXY",
		provide() {
			return {
				reload: this.reload
			}
		},
		data() {
			return {
				openSetting: false,
				isRouterAlive: true,
				layoutConfig: {
					siderTheme: 'dark',
					showSystab: false,
					headerFixed: true,
					siderFixed: true
				}
			};
		},
		methods: {
			updateLayoutConfig({key, value}) {
				this.layoutConfig[key] = value;
			},
			reload() {
				this.isRouterAlive = false;
				this.$nextTick(() => {
					this.isRouterAlive = true;
				});
			}
		},
		computed: {
			...mapState(['sliderCollapsed']),
			// 判断是否为移动端页面
			isMobilePage() {
				return this.$route.meta && this.$route.meta.mobile === true;
			}
		},
		components: {
			appHead,
			appMenu,
			appFooter,
			SysTabs
		}
	};
</script>

<style scoped>
/* 移动端应用框架样式 */
.mobile-app-frame {
	width: 100%;
	min-height: 100vh;
	overflow-x: hidden;
	overflow-y: auto;
	background: #f5f5f5;
	-webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 确保移动端页面占满全屏 */
.mobile-app-frame > * {
	width: 100%;
	min-height: 100vh;
}
</style>
