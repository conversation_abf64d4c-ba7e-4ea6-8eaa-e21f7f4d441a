<template>
  <div class="ai-statistics-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h-card class="header-card">
        <div class="header-content">
          <div class="title-section">
            <h1>AI统计分析</h1>
            <p>全面分析AI处理效果，提供详细的统计报表和趋势分析</p>
          </div>
          <div class="time-filter">
            <h-date-picker
              v-model="dateRange"
              type="daterange"
              placeholder="选择时间范围"
              @change="handleDateChange"
            />
            <h-button type="primary" @click="refreshData" style="margin-left: 8px;">
              刷新数据
            </h-button>
          </div>
        </div>
      </h-card>
    </div>

    <!-- 概览统计卡片 -->
    <div class="overview-section">
      <h-row :gutter="24">
        <h-col :span="6">
          <h-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon processing-icon">
                <i class="h-icon-file-text"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overviewStats.totalProcessed }}</div>
                <div class="stat-label">总处理量</div>
                <div class="stat-trend" :class="getTrendClass(overviewStats.processedTrend)">
                  <i :class="getTrendIcon(overviewStats.processedTrend)"></i>
                  {{ Math.abs(overviewStats.processedTrend) }}%
                </div>
              </div>
            </div>
          </h-card>
        </h-col>
        <h-col :span="6">
          <h-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon accuracy-icon">
                <i class="h-icon-check-circle"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overviewStats.avgAccuracy }}%</div>
                <div class="stat-label">平均准确率</div>
                <div class="stat-trend" :class="getTrendClass(overviewStats.accuracyTrend)">
                  <i :class="getTrendIcon(overviewStats.accuracyTrend)"></i>
                  {{ Math.abs(overviewStats.accuracyTrend) }}%
                </div>
              </div>
            </div>
          </h-card>
        </h-col>
        <h-col :span="6">
          <h-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon time-icon">
                <i class="h-icon-clock"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overviewStats.avgProcessTime }}s</div>
                <div class="stat-label">平均处理时间</div>
                <div class="stat-trend" :class="getTrendClass(overviewStats.timeTrend)">
                  <i :class="getTrendIcon(overviewStats.timeTrend)"></i>
                  {{ Math.abs(overviewStats.timeTrend) }}%
                </div>
              </div>
            </div>
          </h-card>
        </h-col>
        <h-col :span="6">
          <h-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success-icon">
                <i class="h-icon-trophy"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overviewStats.successRate }}%</div>
                <div class="stat-label">成功率</div>
                <div class="stat-trend" :class="getTrendClass(overviewStats.successTrend)">
                  <i :class="getTrendIcon(overviewStats.successTrend)"></i>
                  {{ Math.abs(overviewStats.successTrend) }}%
                </div>
              </div>
            </div>
          </h-card>
        </h-col>
      </h-row>
    </div>

    <!-- 图表分析区域 -->
    <h-row :gutter="24">
      <!-- 处理量趋势图 -->
      <h-col :span="12">
        <h-card title="处理量趋势" class="chart-card">
          <template slot="extra">
            <h-radio-group v-model="processChartType" size="small">
              <h-radio-button value="daily">按日</h-radio-button>
              <h-radio-button value="weekly">按周</h-radio-button>
              <h-radio-button value="monthly">按月</h-radio-button>
            </h-radio-group>
          </template>
          <div class="chart-container" ref="processChart"></div>
        </h-card>
      </h-col>

      <!-- 准确率分布图 -->
      <h-col :span="12">
        <h-card title="准确率分布" class="chart-card">
          <template slot="extra">
            <h-select v-model="accuracyChartType" size="small" style="width: 100px;">
              <h-option value="pie" label="饼图" />
              <h-option value="bar" label="柱状图" />
            </h-select>
          </template>
          <div class="chart-container" ref="accuracyChart"></div>
        </h-card>
      </h-col>
    </h-row>

    <h-row :gutter="24" style="margin-top: 24px;">
      <!-- 处理类型统计 -->
      <h-col :span="8">
        <h-card title="处理类型统计" class="chart-card">
          <div class="chart-container" ref="typeChart"></div>
        </h-card>
      </h-col>

      <!-- 错误分析 -->
      <h-col :span="16">
        <h-card title="错误分析" class="error-analysis-card">
          <template slot="extra">
            <h-button size="small" @click="exportErrorReport">导出错误报告</h-button>
          </template>
          
          <h-table :data="errorAnalysis" size="small">
            <h-table-column prop="errorType" label="错误类型" width="150" />
            <h-table-column prop="count" label="出现次数" width="100" align="right" />
            <h-table-column prop="percentage" label="占比" width="80" align="right">
              <template slot-scope="{ row }">
                {{ row.percentage }}%
              </template>
            </h-table-column>
            <h-table-column prop="description" label="描述" />
            <h-table-column prop="suggestion" label="改进建议" />
          </h-table>
        </h-card>
      </h-col>
    </h-row>

    <!-- 详细数据表格 -->
    <div class="detail-section">
      <h-card title="详细处理记录" class="detail-card">
        <template slot="extra">
          <h-button @click="exportDetailData">导出数据</h-button>
        </template>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <h-row :gutter="16">
            <h-col :span="4">
              <h-select v-model="filterForm.type" placeholder="处理类型">
                <h-option value="" label="全部" />
                <h-option value="bank_receipts" label="银行回单处理" />
                <h-option value="bill" label="票据处理" />
                <h-option value="subject_match" label="科目匹配" />
              </h-select>
            </h-col>
            <h-col :span="4">
              <h-select v-model="filterForm.status" placeholder="处理状态">
                <h-option value="" label="全部" />
                <h-option value="success" label="成功" />
                <h-option value="failed" label="失败" />
                <h-option value="review" label="需审核" />
              </h-select>
            </h-col>
            <h-col :span="4">
              <h-input-number
                v-model="filterForm.minAccuracy"
                placeholder="最低准确率"
                :min="0"
                :max="100"
                style="width: 100%;"
              />
            </h-col>
            <h-col :span="4">
              <h-input
                v-model="filterForm.keyword"
                placeholder="关键词搜索"
                clearable
              />
            </h-col>
            <h-col :span="4">
              <h-button type="primary" @click="loadDetailData">搜索</h-button>
              <h-button @click="resetFilter">重置</h-button>
            </h-col>
          </h-row>
        </div>

        <!-- 数据表格 -->
        <h-table
          :data="detailData"
          :loading="detailLoading"
          stripe
        >
          <h-table-column prop="id" label="ID" width="80" />
          <h-table-column prop="type" label="处理类型" width="100">
            <template slot-scope="{ row }">
              <h-tag :type="getTypeTagType(row.type)">
                {{ getTypeText(row.type) }}
              </h-tag>
            </template>
          </h-table-column>
          <h-table-column prop="documentNo" label="单据编号" width="150" />
          <h-table-column prop="amount" label="金额" width="120" align="right">
            <template slot-scope="{ row }">
              ¥{{ formatAmount(row.amount) }}
            </template>
          </h-table-column>
          <h-table-column prop="accuracy" label="准确率" width="100" align="right">
            <template slot-scope="{ row }">
              <span :class="getAccuracyClass(row.accuracy)">
                {{ row.accuracy }}%
              </span>
            </template>
          </h-table-column>
          <h-table-column prop="processTime" label="处理时间" width="100" align="right">
            <template slot-scope="{ row }">
              {{ row.processTime }}s
            </template>
          </h-table-column>
          <h-table-column prop="status" label="状态" width="100">
            <template slot-scope="{ row }">
              <h-tag :type="getStatusTagType(row.status)">
                {{ getStatusText(row.status) }}
              </h-tag>
            </template>
          </h-table-column>
          <h-table-column prop="createTime" label="创建时间" width="150" />
          <h-table-column label="操作" width="120" fixed="right">
            <template slot-scope="{ row }">
              <h-button size="small" type="text" @click="viewDetail(row)">查看详情</h-button>
            </template>
          </h-table-column>
        </h-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <h-pagination
            v-model="pagination.current"
            :total="pagination.total"
            :page-size="pagination.pageSize"
            @change="loadDetailData"
            show-total
            show-size-changer
            @size-change="handleSizeChange"
          />
        </div>
      </h-card>
    </div>
  </div>
</template>

<script>
import { formatAmount } from '@/utils/format'

export default {
  name: 'AiStatistics',
  data() {
    return {
      detailLoading: false,
      dateRange: [],
      processChartType: 'daily',
      accuracyChartType: 'pie',
      overviewStats: {
        totalProcessed: 0,
        processedTrend: 0,
        avgAccuracy: 0,
        accuracyTrend: 0,
        avgProcessTime: 0,
        timeTrend: 0,
        successRate: 0,
        successTrend: 0
      },
      errorAnalysis: [],
      detailData: [],
      filterForm: {
        type: '',
        status: '',
        minAccuracy: null,
        keyword: ''
      },
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      }
    }
  },
  mounted() {
    this.initDateRange()
    this.loadOverviewStats()
    this.loadErrorAnalysis()
    this.loadDetailData()
    this.$nextTick(() => {
      this.initCharts()
    })
  },
  methods: {
    /**
     * 初始化日期范围（默认最近30天）
     */
    initDateRange() {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 30)
      this.dateRange = [start, end]
    },

    /**
     * 处理日期变化
     */
    handleDateChange() {
      this.refreshData()
    },

    /**
     * 刷新数据
     */
    refreshData() {
      this.loadOverviewStats()
      this.loadErrorAnalysis()
      this.loadDetailData()
      this.updateCharts()
    },

    /**
     * 加载概览统计
     */
    async loadOverviewStats() {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        this.overviewStats = {
          totalProcessed: 15678,
          processedTrend: 12.5,
          avgAccuracy: 96.8,
          accuracyTrend: 2.3,
          avgProcessTime: 2.4,
          timeTrend: -8.7,
          successRate: 94.2,
          successTrend: 5.1
        }
      } catch (error) {
        console.error('加载概览统计失败:', error)
      }
    },

    /**
     * 加载错误分析
     */
    async loadErrorAnalysis() {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))
        
        this.errorAnalysis = [
          {
            errorType: '科目匹配失败',
            count: 156,
            percentage: 35.2,
            description: '无法找到合适的会计科目',
            suggestion: '增加科目训练数据，优化匹配算法'
          },
          {
            errorType: '金额识别错误',
            count: 89,
            percentage: 20.1,
            description: '票据金额识别不准确',
            suggestion: '改进OCR识别模型，增加金额格式训练'
          },
          {
            errorType: '日期格式错误',
            count: 67,
            percentage: 15.1,
            description: '日期格式不标准或识别错误',
            suggestion: '标准化日期格式，增加日期解析规则'
          },
          {
            errorType: '票据类型误判',
            count: 45,
            percentage: 10.2,
            description: '票据类型分类不准确',
            suggestion: '优化票据分类模型，增加样本多样性'
          },
          {
            errorType: '其他错误',
            count: 86,
            percentage: 19.4,
            description: '其他未分类错误',
            suggestion: '详细分析错误原因，建立错误分类体系'
          }
        ]
      } catch (error) {
        console.error('加载错误分析失败:', error)
      }
    },

    /**
     * 加载详细数据
     */
    async loadDetailData() {
      this.detailLoading = true
      try {
        const params = {
          page: this.pagination.current,
          pageSize: this.pagination.pageSize,
          type: this.filterForm.type,
          status: this.filterForm.status,
          minAccuracy: this.filterForm.minAccuracy,
          keyword: this.filterForm.keyword,
          startDate: this.dateRange[0],
          endDate: this.dateRange[1]
        }

        // 调用真实API
        const response = await this.getDetailDataApi(params)
        this.detailData = response.data.records
        this.pagination.total = response.data.total
      } catch (error) {
        this.$Message.error('加载详细数据失败')
        console.error('加载详细数据失败:', error)
      } finally {
        this.detailLoading = false
      }
    },

    /**
     * 重置筛选条件
     */
    resetFilter() {
      this.filterForm = {
        type: '',
        status: '',
        minAccuracy: null,
        keyword: ''
      }
      this.pagination.current = 1
      this.loadDetailData()
    },

    /**
     * 处理页面大小变化
     */
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      this.loadDetailData()
    },

    /**
     * 查看详情
     */
    viewDetail(row) {
      // 根据类型导航到对应的详情页面
      if (row.type === 'bank_receipts') {
        this.$router.push(`/ai/bank-receipts?id=${row.id}`)
      } else if (row.type === 'bill') {
        this.$router.push(`/ai/bill?id=${row.id}`)
      }
    },

    /**
     * 导出错误报告
     */
    exportErrorReport() {
      this.$Message.success('错误报告导出功能开发中...')
    },

    /**
     * 导出详细数据
     */
    exportDetailData() {
      this.$Message.success('数据导出功能开发中...')
    },

    /**
     * 初始化图表
     */
    initCharts() {
      // 这里应该使用实际的图表库（如ECharts）来初始化图表
      // 由于没有引入图表库，这里只是模拟
      this.initProcessChart()
      this.initAccuracyChart()
      this.initTypeChart()
    },

    /**
     * 初始化处理量趋势图
     */
    initProcessChart() {
      // 模拟图表初始化
      console.log('初始化处理量趋势图')
    },

    /**
     * 初始化准确率分布图
     */
    initAccuracyChart() {
      // 模拟图表初始化
      console.log('初始化准确率分布图')
    },

    /**
     * 初始化处理类型统计图
     */
    initTypeChart() {
      // 模拟图表初始化
      console.log('初始化处理类型统计图')
    },

    /**
     * 更新图表
     */
    updateCharts() {
      // 模拟图表更新
      console.log('更新图表数据')
    },

    /**
     * 获取趋势样式类
     */
    getTrendClass(trend) {
      return trend > 0 ? 'trend-up' : 'trend-down'
    },

    /**
     * 获取趋势图标
     */
    getTrendIcon(trend) {
      return trend > 0 ? 'h-icon-arrow-up' : 'h-icon-arrow-down'
    },

    /**
     * 获取类型标签类型
     */
    getTypeTagType(type) {
      switch (type) {
        case 'bank_receipts': return 'primary'
        case 'bill': return 'success'
        case 'subject_match': return 'warning'
        default: return 'info'
      }
    },

    /**
     * 获取类型文本
     */
    getTypeText(type) {
      switch (type) {
        case 'bank_receipts': return '银行回单处理'
        case 'bill': return '票据处理'
        case 'subject_match': return '科目匹配'
        default: return '未知'
      }
    },

    /**
     * 获取状态标签类型
     */
    getStatusTagType(status) {
      switch (status) {
        case 'success': return 'success'
        case 'failed': return 'danger'
        case 'review': return 'warning'
        default: return 'info'
      }
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      switch (status) {
        case 'success': return '成功'
        case 'failed': return '失败'
        case 'review': return '需审核'
        default: return '处理中'
      }
    },

    /**
     * 获取准确率样式类
     */
    getAccuracyClass(accuracy) {
      if (accuracy >= 95) return 'accuracy-high'
      if (accuracy >= 85) return 'accuracy-medium'
      return 'accuracy-low'
    },

    /**
     * 格式化金额
     */
    formatAmount,

    /**
     * 获取详细数据API
     */
    async getDetailDataApi(params) {
      try {
        const response = await fetch('/api/ai-statistics/details', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(params)
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        return await response.json()
      } catch (error) {
        console.error('获取详细数据失败:', error)
        throw error
      }
    }
  }
}
</script>

<style lang="less" scoped>
.ai-statistics-page {
  padding: 20px;
  
  .page-header {
    margin-bottom: 24px;
    
    .header-card {
      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .title-section {
          h1 {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
            color: #1890ff;
          }
          
          p {
            margin: 0;
            color: #666;
            font-size: 14px;
          }
        }
      }
    }
  }
  
  .overview-section {
    margin-bottom: 24px;
    
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        
        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          
          i {
            font-size: 28px;
            color: white;
          }
          
          &.processing-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          &.accuracy-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }
          
          &.time-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
          
          &.success-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }
        
        .stat-info {
          flex: 1;
          
          .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
          }
          
          .stat-trend {
            font-size: 12px;
            font-weight: 600;
            
            &.trend-up {
              color: #52c41a;
            }
            
            &.trend-down {
              color: #ff4d4f;
            }
            
            i {
              margin-right: 2px;
            }
          }
        }
      }
    }
  }
  
  .chart-card {
    .chart-container {
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
      border-radius: 4px;
      color: #999;
      font-size: 14px;
    }
  }
  
  .error-analysis-card {
    .h-table {
      font-size: 12px;
    }
  }
  
  .detail-section {
    margin-top: 24px;
    
    .detail-card {
      .filter-section {
        margin-bottom: 16px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 4px;
      }
      
      .pagination-wrapper {
        margin-top: 16px;
        text-align: right;
      }
      
      .accuracy-high {
        color: #52c41a;
        font-weight: 600;
      }
      
      .accuracy-medium {
        color: #faad14;
        font-weight: 600;
      }
      
      .accuracy-low {
        color: #ff4d4f;
        font-weight: 600;
      }
    }
  }
}
</style>