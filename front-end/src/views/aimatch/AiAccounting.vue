<template>
  <div class="ai-accounting-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h-card class="header-card">
        <div class="header-content">
          <div class="title-section">
            <h1>AI智能助手</h1>
            <p>利用人工智能技术，提升财务处理效率和准确性</p>
          </div>
          <div class="stats-section">
            <h-row :gutter="16">
              <h-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.totalProcessed }}</div>
                  <div class="stat-label">累计处理</div>
                </div>
              </h-col>
              <h-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.todayProcessed }}</div>
                  <div class="stat-label">今日处理</div>
                </div>
              </h-col>
              <h-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.accuracy }}%</div>
                  <div class="stat-label">识别准确率</div>
                </div>
              </h-col>
              <h-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.timeSaved }}h</div>
                  <div class="stat-label">节省时间</div>
                </div>
              </h-col>
            </h-row>
          </div>
        </div>
      </h-card>
    </div>

    <!-- 功能模块 -->
    <div class="modules-section">
      <h-row :gutter="24">
        <!-- 银证智能处理功能已整合到关联管理模块 -->
        <h-col :span="12">
          <h-card class="module-card disabled-card">
            <div class="module-content">
              <div class="module-icon bank-icon">
                <i class="h-icon-bank"></i>
              </div>
              <div class="module-info">
                <h3>银证智能处理</h3>
                <p>功能已整合到关联管理模块中</p>
                <div class="module-stats">
                  <span>请使用关联管理功能</span>
                </div>
              </div>
              <div class="module-action">
                <h-button type="default" disabled>已整合</h-button>
              </div>
            </div>
          </h-card>
        </h-col>

        <!-- 票据智能处理功能已整合到关联管理模块 -->
        <h-col :span="12">
          <h-card class="module-card disabled-card">
            <div class="module-content">
              <div class="module-icon bill-icon">
                <i class="h-icon-file-text"></i>
              </div>
              <div class="module-info">
                <h3>票据智能处理</h3>
                <p>功能已整合到关联管理模块中</p>
                <div class="module-stats">
                  <span>请使用关联管理功能</span>
                </div>
              </div>
              <div class="module-action">
                <h-button type="default" disabled>已整合</h-button>
              </div>
            </div>
          </h-card>
        </h-col>
      </h-row>

      <h-row :gutter="24" style="margin-top: 24px;">
        <!-- 科目AI增强 -->
        <h-col :span="12">
          <h-card class="module-card" @click="navigateToModule('/ai/subject-enhancement')">
            <div class="module-content">
              <div class="module-icon subject-icon">
                <i class="h-icon-setting"></i>
              </div>
              <div class="module-info">
                <h3>科目AI增强</h3>
                <p>基于历史数据训练AI模型，提升科目匹配的智能化水平</p>
                <div class="module-stats">
                  <span>训练样本: {{ moduleStats.subjectEnhancement.sampleCount }}条</span>
                  <span>模型版本: v{{ moduleStats.subjectEnhancement.modelVersion }}</span>
                </div>
              </div>
              <div class="module-action">
                <h-button type="primary">配置管理</h-button>
              </div>
            </div>
          </h-card>
        </h-col>

        <!-- AI统计分析 -->
        <h-col :span="12">
          <h-card class="module-card" @click="navigateToModule('/ai/statistics')">
            <div class="module-content">
              <div class="module-icon stats-icon">
                <i class="h-icon-chart"></i>
              </div>
              <div class="module-info">
                <h3>AI统计分析</h3>
                <p>全面分析AI处理效果，提供详细的统计报表和趋势分析</p>
                <div class="module-stats">
                  <span>分析维度: {{ moduleStats.statistics.dimensionCount }}个</span>
                  <span>报表类型: {{ moduleStats.statistics.reportCount }}种</span>
                </div>
              </div>
              <div class="module-action">
                <h-button type="primary">查看报表</h-button>
              </div>
            </div>
          </h-card>
        </h-col>
      </h-row>
    </div>

    <!-- 最近处理记录 -->
    <div class="recent-section">
      <h-card title="最近处理记录" class="recent-card">
        <template slot="extra">
          <h-button type="text" @click="viewAllRecords">查看全部</h-button>
        </template>
        
        <h-table :data="recentRecords" :loading="recordsLoading">
          <h-table-column prop="type" label="处理类型" width="120">
            <template slot-scope="{ row }">
              <h-tag :type="getTypeTagType(row.type)">{{ getTypeText(row.type) }}</h-tag>
            </template>
          </h-table-column>
          <h-table-column prop="documentNo" label="单据编号" width="150" />
          <h-table-column prop="amount" label="金额" width="120" align="right">
            <template slot-scope="{ row }">
              ¥{{ formatAmount(row.amount) }}
            </template>
          </h-table-column>
          <h-table-column prop="confidence" label="置信度" width="100">
            <template slot-scope="{ row }">
              <span :class="getConfidenceClass(row.confidence)">
                {{ Math.round(row.confidence * 100) }}%
              </span>
            </template>
          </h-table-column>
          <h-table-column prop="status" label="状态" width="100">
            <template slot-scope="{ row }">
              <h-tag :type="getStatusTagType(row.status)">{{ getStatusText(row.status) }}</h-tag>
            </template>
          </h-table-column>
          <h-table-column prop="processTime" label="处理时间" width="150" />
          <h-table-column label="操作" width="120">
            <template slot-scope="{ row }">
              <h-button size="small" type="text" @click="viewDetail(row)">查看详情</h-button>
            </template>
          </h-table-column>
        </h-table>
      </h-card>
    </div>
  </div>
</template>

<script>
import { formatAmount } from '@/utils/format'

export default {
  name: 'AiAccounting',
  data() {
    return {
      recordsLoading: false,
      statistics: {
        totalProcessed: 0,
        todayProcessed: 0,
        accuracy: 0,
        timeSaved: 0
      },
      moduleStats: {
        bankReceipts: {
          todayCount: 0,
          accuracy: 0
        },
        bill: {
          todayCount: 0,
          accuracy: 0
        },
        subjectEnhancement: {
          sampleCount: 0,
          modelVersion: '1.0'
        },
        statistics: {
          dimensionCount: 8,
          reportCount: 12
        }
      },
      recentRecords: []
    }
  },
  mounted() {
    this.loadStatistics()
    this.loadRecentRecords()
  },
  methods: {
    /**
     * 加载统计数据
     */
    async loadStatistics() {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        
        this.statistics = {
          totalProcessed: 15678,
          todayProcessed: 234,
          accuracy: 96.8,
          timeSaved: 1250
        }
        
        this.moduleStats = {
          bankReceipts: {
            todayCount: 89,
            accuracy: 97.2
          },
          bill: {
            todayCount: 145,
            accuracy: 96.5
          },
          subjectEnhancement: {
            sampleCount: 25680,
            modelVersion: '2.1'
          },
          statistics: {
            dimensionCount: 8,
            reportCount: 12
          }
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    /**
     * 加载最近处理记录
     */
    async loadRecentRecords() {
      this.recordsLoading = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))
        
        this.recentRecords = [
          {
            id: 1,
            type: 'bank_receipts',
            documentNo: 'BV202312150001',
            amount: 50000,
            confidence: 0.98,
            status: 'completed',
            processTime: '2023-12-15 14:30:25'
          },
          {
            id: 2,
            type: 'bill',
            documentNo: 'BILL202312150002',
            amount: 12500,
            confidence: 0.95,
            status: 'completed',
            processTime: '2023-12-15 14:25:18'
          },
          {
            id: 3,
            type: 'bank_receipts',
            documentNo: 'BV202312150003',
            amount: 8900,
            confidence: 0.92,
            status: 'completed',
            processTime: '2023-12-15 14:20:45'
          },
          {
            id: 4,
            type: 'bill',
            documentNo: 'BILL202312150004',
            amount: 3200,
            confidence: 0.88,
            status: 'review_needed',
            processTime: '2023-12-15 14:15:32'
          },
          {
            id: 5,
            type: 'bank_receipts',
            documentNo: 'BV202312150005',
            amount: 75000,
            confidence: 0.99,
            status: 'completed',
            processTime: '2023-12-15 14:10:15'
          }
        ]
      } catch (error) {
        console.error('加载最近记录失败:', error)
        this.$Message.error('加载最近记录失败')
      } finally {
        this.recordsLoading = false
      }
    },

    /**
     * 导航到指定模块
     */
    navigateToModule(path) {
      this.$router.push(path)
    },

    /**
     * 查看所有记录
     */
    viewAllRecords() {
      this.$router.push('/ai/statistics')
    },

    /**
     * 查看详情
     */
    viewDetail(record) {
      // 根据类型导航到对应的详情页面
      if (record.type === 'bank_receipts') {
        this.$router.push(`/ai/bank-receipts?id=${record.id}`)
      } else if (record.type === 'bill') {
        this.$router.push(`/ai/bill?id=${record.id}`)
      }
    },

    /**
     * 获取类型标签类型
     */
    getTypeTagType(type) {
      switch (type) {
        case 'bank_receipts': return 'primary'
        case 'bill': return 'success'
        default: return 'info'
      }
    },

    /**
     * 获取类型文本
     */
    getTypeText(type) {
      switch (type) {
        case 'bank_receipts': return '银行回单处理'
        case 'bill': return '票据处理'
        default: return '未知'
      }
    },

    /**
     * 获取状态标签类型
     */
    getStatusTagType(status) {
      switch (status) {
        case 'completed': return 'success'
        case 'review_needed': return 'warning'
        case 'failed': return 'danger'
        default: return 'info'
      }
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      switch (status) {
        case 'completed': return '已完成'
        case 'review_needed': return '需审核'
        case 'failed': return '失败'
        default: return '处理中'
      }
    },

    /**
     * 获取置信度样式类
     */
    getConfidenceClass(confidence) {
      if (confidence >= 0.95) return 'confidence-high'
      if (confidence >= 0.85) return 'confidence-medium'
      return 'confidence-low'
    },

    /**
     * 格式化金额
     */
    formatAmount
  }
}
</script>

<style lang="less" scoped>
.ai-accounting-page {
  padding: 20px;
  
  .page-header {
    margin-bottom: 24px;
    
    .header-card {
      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .title-section {
          h1 {
            margin: 0 0 8px 0;
            font-size: 28px;
            font-weight: 600;
            color: #1890ff;
          }
          
          p {
            margin: 0;
            color: #666;
            font-size: 14px;
          }
        }
        
        .stats-section {
          .stat-item {
            text-align: center;
            
            .stat-number {
              font-size: 24px;
              font-weight: 600;
              color: #1890ff;
              margin-bottom: 4px;
            }
            
            .stat-label {
              font-size: 12px;
              color: #999;
            }
          }
        }
      }
    }
  }
  
  .modules-section {
    margin-bottom: 24px;
    
    .module-card {
      cursor: pointer;
      transition: all 0.3s ease;
      height: 160px;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        transform: translateY(-2px);
      }

      &.disabled-card {
        opacity: 0.6;
        cursor: not-allowed;

        &:hover {
          transform: none;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
      
      .module-content {
        display: flex;
        align-items: center;
        height: 100%;
        
        .module-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          
          i {
            font-size: 28px;
            color: white;
          }
          
          &.bank-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          &.bill-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }
          
          &.subject-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
          
          &.stats-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }
        
        .module-info {
          flex: 1;
          
          h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
          }
          
          p {
            margin: 0 0 12px 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
          }
          
          .module-stats {
            display: flex;
            gap: 16px;
            
            span {
              font-size: 12px;
              color: #999;
            }
          }
        }
        
        .module-action {
          margin-left: 16px;
        }
      }
    }
  }
  
  .recent-section {
    .recent-card {
      .confidence-high {
        color: #52c41a;
        font-weight: 600;
      }
      
      .confidence-medium {
        color: #faad14;
        font-weight: 600;
      }
      
      .confidence-low {
        color: #ff4d4f;
        font-weight: 600;
      }
    }
  }
}
</style>