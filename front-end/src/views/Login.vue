<template>
	<div class="login-vue">
		<div class="background-particles"></div>
		<div class="login-container">
			<!-- 左侧品牌展示区域 -->
			<div class="login-left">
				<!-- 科技装饰元素 -->
				<div class="tech-decoration">
					<div class="circuit-line"></div>
					<div class="data-flow"></div>
				</div>

				<!-- 品牌核心区域 -->
				<div class="brand-core">
					<div class="logo-section">
						<div class="logo-frame">
							<img src="https://www.aiform.com/logo-Photoroom.png" alt="AI财务" class="main-logo">
							<div class="logo-pulse"></div>
						</div>
						<div class="brand-text">
							<h1 class="system-title">AI模范智能财务</h1>
							<div class="version-tag">v1.0</div>
						</div>
					</div>

					<!-- 核心特性 -->
					<div class="core-features">
						<div class="feature-item">
							<div class="feature-icon">
								<i class="fa fa-lightbulb-o"></i>
							</div>
							<div class="feature-name">智能分析</div>
						</div>
						<div class="feature-item">
							<div class="feature-icon">
								<i class="fa fa-shield"></i>
							</div>
							<div class="feature-name">安全可靠</div>
						</div>
						<div class="feature-item">
							<div class="feature-icon">
								<i class="fa fa-rocket"></i>
							</div>
							<div class="feature-name">高效处理</div>
						</div>
					</div>
				</div>

				<!-- 数据可视化区域 -->
				<div class="data-visualization">
					<div class="stats-grid">
						<div class="stat-item">
							<div class="stat-number">99.9%</div>
							<div class="stat-label">系统稳定性</div>
						</div>
						<div class="stat-item">
							<div class="stat-number">10K+</div>
							<div class="stat-label">企业用户</div>
						</div>
						<div class="stat-item">
							<div class="stat-number">24/7</div>
							<div class="stat-label">技术支持</div>
						</div>
					</div>
				</div>

				<!-- 底部科技元素 -->
				<div class="tech-footer">
					<div class="connection-dots">
						<div class="dot active"></div>
						<div class="dot"></div>
						<div class="dot"></div>
						<div class="connection-line"></div>
					</div>
					<div class="system-status">
						<span class="status-indicator"></span>
						<span class="status-text">系统运行正常</span>
					</div>
				</div>
			</div>

			<!-- 右侧登录区域 -->
			<div class="login-right">
				<div class="login-content">
					<div class="login-header">
						<div class="header-decoration"></div>
						<h2 class="login-title">会员登录</h2>
						<p class="login-subtitle">MEMBER LOGIN</p>
						<div class="header-line"></div>
					</div>
					<div class="form-container">
						<component :is="currentComponent"></component>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import LoginForm from './login/LoginForm.vue';
	import ForgotPassword from './login/ForgotPassword.vue';
	import Registered from './login/Registered.vue';

	export default {
		name: 'Login',
		components: {
			LoginForm,
			ForgotPassword,
			Registered
		},
		data() {
			return {
				currentComponent: 'LoginForm'
			};
		}
	};
</script>
<style lang="less" scoped>
	@import "../styles/login.less";
</style>
