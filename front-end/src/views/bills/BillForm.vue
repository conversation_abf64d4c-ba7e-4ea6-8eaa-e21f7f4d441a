<template>
  <app-content style="position: relative;">
    <div class="mask" v-if="User.role =='View'"></div>
    <div class="professional-form">
      <div class="form-title">{{ isEdit ? '编辑票据' : '新增票据' }}</div>

      <!-- 文件上传区域 -->
      <FileUpload
        v-model="form.attachmentPath"
        :fileName.sync="uploadedFileName"
        :disabled="User.role =='View'"
        uploadApi="bill"
        :enableRecognition="true"
        recognitionApi="/api/upload/recognize-vat-invoice"
        @recognition-result="handleRecognitionResult"
        label="附件上传"
        prop="attachmentPath"
      />

      <Form ref="form" :labelWidth="0" mode="block" :rules="rules" :model="form">
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <div class="section-content grid-3">
            <FormItem label="票据编号" prop="billNo" class="readonly-field">
              <input type="text" v-model="form.billNo" placeholder="系统自动生成" readonly>
            </FormItem>
            <FormItem label="发票号码" prop="invoiceNumber">
              <input type="text" v-model="form.invoiceNumber" placeholder="请输入发票号码（可为空）" :disabled="User.role =='View'" class="h-input">
            </FormItem>
            <FormItem label="开票日期" prop="billDate" class="important-field">
              <DatePicker v-model="form.billDate" type="date" placeholder="选择日期" :disabled="User.role =='View'"></DatePicker>
            </FormItem>
          </div>
        </div>

        <!-- 主体信息 -->
        <div class="form-section">
          <div class="section-title">主体信息</div>
          <div class="section-content grid-2">
            <FormItem label="开票方" prop="issuer" class="important-field">
              <input type="text" v-model="form.issuer" placeholder="请输入开票方" :disabled="User.role =='View'" class="h-input">
            </FormItem>
            <FormItem label="收票方" prop="recipient">
              <input type="text" v-model="form.recipient" placeholder="请输入收票方" :disabled="User.role =='View'" class="h-input">
            </FormItem>
          </div>
        </div>

        <!-- 金额信息 -->
        <div class="form-section">
          <div class="section-title">金额信息</div>
          <div class="section-content grid-3">
            <FormItem label="合计金额" prop="amount" class="important-field">
              <div class="amount-input">
                <span class="currency-symbol">¥</span>
                <input type="number" v-model="form.amount" placeholder="0.00" :disabled="User.role =='View'" step="0.01" min="0">
              </div>
            </FormItem>
            <FormItem label="税率" prop="taxRate">
              <input type="number" v-model="form.taxRate" placeholder="请输入税率(%)" :disabled="User.role =='View'" step="0.01" min="0">
            </FormItem>
            <FormItem label="合计税额" prop="totalTaxAmount">
              <div class="amount-input">
                <span class="currency-symbol">¥</span>
                <input type="number" v-model="form.totalTaxAmount" placeholder="0.00" :disabled="User.role =='View'" step="0.01" min="0">
              </div>
            </FormItem>
          </div>
        </div>

        <!-- 分类信息 -->
        <div class="form-section">
          <div class="section-title">分类信息</div>
          <div class="section-content grid-3">
            <FormItem label="票据类型" prop="type" class="important-field">
              <Select v-model="form.type" :datas="typeOptions" :disabled="User.role =='View'"></Select>
            </FormItem>
            <FormItem label="状态" prop="status">
              <Select v-model="form.status" :datas="statusOptions" :disabled="User.role =='View'"></Select>
            </FormItem>
            <FormItem label="大写金额" prop="amountInWords">
              <input type="text" v-model="form.amountInWords" placeholder="请输入大写金额" :disabled="User.role =='View'" class="h-input">
            </FormItem>
          </div>
        </div>

        <!-- 详细描述 -->
        <div class="form-section">
          <div class="section-title">详细描述</div>
          <div class="section-content grid-full">
            <FormItem label="摘要" prop="summary" class="important-field">
              <textarea v-model="form.summary" placeholder="请输入摘要" :disabled="User.role =='View'"></textarea>
            </FormItem>
            <FormItem label="备注" prop="remark">
              <textarea v-model="form.remark" placeholder="请输入备注" :disabled="User.role =='View'"></textarea>
            </FormItem>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <Button v-if="User.role !='View'" color="primary" :loading="saving" @click="save">保存</Button>
          <Button @click="goBack">返回</Button>
        </div>
      </Form>
    </div>
  </app-content>
</template>

<script>
import moment from 'moment'
import FileUpload from '@/components/FileUpload.vue'
import formMixin from '@/mixins/formMixin.js'

export default {
  name: "BillForm",
  components: {
    FileUpload
  },
  mixins: [formMixin],
  data() {
    return {
      form: {
        id: null,
        billNo: '', // 票据编号 (系统生成)
        billDate: moment().format('YYYY-MM-DD'), // 开票日期
        type: '增值税专用发票', // 票据类型
        invoiceNumber: '', // 发票号码
        amount: 0, // 合计金额
        taxRate: 0, // 税率
        totalTaxAmount: 0, // 合计税额
        amountInWords: '', // 小写金额
        issuer: '', // 开票方
        recipient: '', // 收票方
        summary: '', // 摘要
        remark: '', // 备注
        status: '未使用', // 状态
        // 关联凭证功能已移至关联管理模块
        attachmentPath: '', // 附件路径
        ocrRecognitionInfo: '' // OCR识别信息
      },
      uploadedFileName: '',
      typeOptions: [
        {title: '增值税专用发票', key: '增值税专用发票'},
        {title: '增值税普通发票', key: '增值税普通发票'},
        {title: '收据', key: '收据'},
        {title: '其他', key: '其他'}
      ],
      statusOptions: [
        {title: '未使用', key: '未使用'},
        {title: '已使用', key: '已使用'},
        {title: '已作废', key: '已作废'}
      ],
      rules: {
        billDate: { required: true, message: '请选择开票日期' },
        type: { required: true, message: '请选择票据类型' },
        amount: { required: true, message: '请输入金额' },
        issuer: { required: true, message: '请输入开票方' },
        summary: { required: true, message: '请输入摘要' }
      }
    }
  },
  computed: {
    billId() {
      return this.$route.params.billId
    }
  },
  created() {
    if (this.billId) {
      this.loadData()
    } else {
      this.loadBillNo()
    }
  },
  methods: {
    loadData() {
      this.loading = true
      this.$api.bill.load(this.billId).then(({data}) => {
        console.log('加载的票据数据:', data) // 添加调试信息
        this.form = {
          ...data,
          billDate: this.formatDate(data.billDate),
          // 确保数值字段正确转换
          amount: data.amount || 0,
          taxRate: data.taxRate || 0,
          totalTaxAmount: data.totalTaxAmount || 0,
          amountInWords: data.amountInWords || '',
          ocrRecognitionInfo: data.ocrRecognitionInfo || ''
        }
        console.log('设置后的表单数据:', this.form) // 添加调试信息
        this.uploadedFileName = data.attachmentPath ? data.attachmentPath.split('/').pop() : ''
      }).finally(() => {
        this.loading = false
      })
    },
    
    loadBillNo() {
      const date = moment()
      this.$api.bill.loadBillNo({
        year: date.year(),
        month: date.month() + 1
      }).then(({data}) => {
        this.form.billNo = data
      }).catch(error => {
        console.error('获取票据编号失败:', error)
      })
    },
    
    save() {
      this.saveForm(
        this.$api.bill,
        '/bills/bill-list',
        () => {
          // 保存前处理日期格式
          if (this.form.billDate) {
            this.form.billDate = this.formatDate(this.form.billDate)
          }
          // 确保数值字段正确转换
          this.form.amount = parseFloat(this.form.amount) || 0
          this.form.taxRate = parseFloat(this.form.taxRate) || 0
          this.form.totalTaxAmount = parseFloat(this.form.totalTaxAmount) || 0

          console.log('保存前的表单数据:', this.form) // 添加调试信息
        }
      )
    },
    
    handleRecognitionResult(data) {
      console.log('票据OCR识别结果:', data) // 添加调试信息

      // 保存完整的OCR识别信息（优先使用易读格式）
      if (data.ocrRecognitionInfo) {
        this.form.ocrRecognitionInfo = data.ocrRecognitionInfo
        console.log('保存易读格式OCR识别信息:', this.form.ocrRecognitionInfo)
      } else if (data.ocrRecognitionInfoJson) {
        // 如果没有易读格式，使用JSON格式
        this.form.ocrRecognitionInfo = data.ocrRecognitionInfoJson
        console.log('保存JSON格式OCR识别信息:', this.form.ocrRecognitionInfo)
      } else if (data.rawOcrData) {
        // 如果都没有，但有原始数据，则保存原始数据
        this.form.ocrRecognitionInfo = JSON.stringify(data.rawOcrData, null, 2)
        console.log('保存原始OCR数据:', this.form.ocrRecognitionInfo)
      }

      // 处理发票号码（支持新旧字段名）
      if (data.invoice_number || data.invoiceNumber) {
        this.form.invoiceNumber = String(data.invoice_number || data.invoiceNumber).trim()
        console.log('更新发票号码:', this.form.invoiceNumber)
      }

      // 处理OCR识别结果 - 支持新的字段映射格式
      // 处理金额字段（支持新旧字段名）
      if (data.amount || data.totalAmount || data.amountInFigures) {
        const amount = data.amount || data.totalAmount || data.amountInFigures
        this.form.amount = parseFloat(String(amount).replace(/[^\d.]/g, '')) || 0
        console.log('更新合计金额:', this.form.amount)
      }

      // 处理税率
      if (data.tax_rate || data.taxRate) {
        const taxRate = data.tax_rate || data.taxRate
        this.form.taxRate = parseFloat(String(taxRate).replace(/[^\d.]/g, '')) || 0
        console.log('更新税率:', this.form.taxRate)
      }

      // 处理合计税额
      if (data.total_tax_amount || data.totalTaxAmount || data.taxAmount) {
        const taxAmount = data.total_tax_amount || data.totalTaxAmount || data.taxAmount
        this.form.totalTaxAmount = parseFloat(String(taxAmount).replace(/[^\d.]/g, '')) || 0
        console.log('更新合计税额:', this.form.totalTaxAmount)
      }

      // 处理大写金额
      if (data.amount_in_words || data.amountInWords || data.amountInFigures) {
        const amountWords = data.amount_in_words || data.amountInWords || data.amountInFigures
        this.form.amountInWords = String(amountWords).trim()
        console.log('更新大写金额:', this.form.amountInWords)
      }

      // 处理开票方（支持新旧字段名）
      if (data.issuer || data.sellerName) {
        this.form.issuer = String(data.issuer || data.sellerName).trim()
        console.log('更新开票方:', this.form.issuer)
      }

      // 处理收票方（支持新旧字段名）
      if (data.recipient || data.buyerName) {
        this.form.recipient = String(data.recipient || data.buyerName).trim()
        console.log('更新收票方:', this.form.recipient)
      }

      // 处理摘要（支持新旧字段名）
      if (data.summary || data.itemName) {
        this.form.summary = String(data.summary || data.itemName).trim()
        console.log('更新摘要:', this.form.summary)
      }

      // 处理开票日期（支持新旧字段名）
      if (data.bill_date || data.invoiceDate) {
        const dateValue = data.bill_date || data.invoiceDate
        this.form.billDate = this.formatDate(dateValue)
        console.log('更新日期:', this.form.billDate)
      }

      // 处理发票类型（支持新旧字段名）
      if (data.type || data.invoiceType) {
        const typeStr = String(data.type || data.invoiceType).toLowerCase()
        if (typeStr.includes('专用发票')) {
          this.form.type = '增值税专用发票'
        } else if (typeStr.includes('普通发票')) {
          this.form.type = '增值税普通发票'
        } else if (typeStr.includes('收据')) {
          this.form.type = '收据'
        } else if (typeStr.includes('发票')) {
          this.form.type = '发票'
        }
        console.log('更新类型:', this.form.type)
      }
      
      // 强制触发Vue响应式更新
      this.$forceUpdate()
      
      // 显示成功提示
      this.$Message.success('表单已更新识别结果')
    }
  }
}
</script>

<style scoped>
@import "../../styles/form-layout.css";

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 999;
}
</style>

