<template>
  <app-content style="position: relative;">
    <div class="mask" v-if="User.role =='View'"></div>
    <div class="professional-form">
      <div class="form-title">{{ isEdit ? '编辑银行回单' : '新增银行回单' }}</div>

      <Form ref="form" :labelWidth="0" mode="block" :rules="rules" :model="form">
        <!-- 文件上传区域 -->
        <div class="form-section">
          <div class="section-title">附件上传</div>
          <div class="section-content grid-full">
            <FileUpload
              v-model="form.filePath"
              :fileName.sync="uploadedFileName"
              :disabled="User.role =='View'"
              uploadApi="bank-receipts"
              :enableRecognition="true"
              recognitionApi="/api/upload/recognize-bank-receipt"
              @recognition-result="handleRecognitionResult"
              label="附件文件"
              prop="filePath"
            />
          </div>
        </div>
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <div class="section-content grid-4">
            <FormItem label="单据编号" prop="receiptsNo" class="readonly-field">
              <input type="text" v-model="form.receiptsNo" placeholder="系统自动生成" readonly>
            </FormItem>
            <FormItem label="回单名称" prop="receiptTitle" class="important-field">
              <input type="text" v-model="form.receiptTitle" placeholder="请输入回单名称" :disabled="User.role =='View'" class="h-input">
            </FormItem>
            <FormItem label="单据日期" prop="receiptsDate" class="important-field">
              <DatePicker v-model="form.receiptsDate" type="date" placeholder="选择日期" :disabled="User.role =='View'"></DatePicker>
            </FormItem>
            <FormItem label="转账日期" prop="transferDate">
              <DatePicker v-model="form.transferDate" type="date" placeholder="选择转账日期" :disabled="User.role =='View'"></DatePicker>
            </FormItem>
          </div>
        </div>

        <!-- 交易信息 -->
        <div class="form-section">
          <div class="section-title">交易信息</div>
          <div class="section-content grid-3">
            <FormItem label="流水号" prop="serialNumber">
              <input type="text" v-model="form.serialNumber" placeholder="请输入流水号" :disabled="User.role =='View'" class="h-input">
            </FormItem>
            <FormItem label="交易机构" prop="transactionInstitution">
              <input type="text" v-model="form.transactionInstitution" placeholder="请输入交易机构" :disabled="User.role =='View'" class="h-input">
            </FormItem>
            <FormItem label="类型" prop="type" class="important-field">
              <Select v-model="form.type" :datas="typeOptions" :disabled="User.role =='View'"></Select>
            </FormItem>
          </div>
        </div>

        <!-- 金额信息 -->
        <div class="form-section">
          <div class="section-title">金额信息</div>
          <div class="section-content grid-2">
            <FormItem label="金额" prop="amount" class="important-field">
              <div class="amount-input">
                <span class="currency-symbol">¥</span>
                <input type="number" v-model="form.amount" placeholder="0.00" :disabled="User.role =='View'" step="0.01" min="0">
              </div>
            </FormItem>
            <FormItem label="金额大写" prop="amountInWords">
              <input type="text" v-model="form.amountInWords" placeholder="请输入金额大写" :disabled="User.role =='View'">
            </FormItem>
          </div>
        </div>

        <!-- 收款方信息 -->
        <div class="form-section">
          <div class="section-title">收款方信息</div>
          <div class="section-content grid-3">
            <FormItem label="收款人姓名" prop="payeeName">
              <input type="text" v-model="form.payeeName" placeholder="请输入收款人姓名" :disabled="User.role =='View'">
            </FormItem>
            <FormItem label="收款人账号" prop="payeeAccount">
              <input type="text" v-model="form.payeeAccount" placeholder="请输入收款人账号" :disabled="User.role =='View'">
            </FormItem>
            <FormItem label="收款人开户行" prop="payeeBank">
              <input type="text" v-model="form.payeeBank" placeholder="请输入收款人开户行" :disabled="User.role =='View'">
            </FormItem>
          </div>
        </div>

        <!-- 付款方信息 -->
        <div class="form-section">
          <div class="section-title">付款方信息</div>
          <div class="section-content grid-3">
            <FormItem label="付款人姓名" prop="payerName">
              <input type="text" v-model="form.payerName" placeholder="请输入付款人姓名" :disabled="User.role =='View'">
            </FormItem>
            <FormItem label="付款人账号" prop="payerAccount">
              <input type="text" v-model="form.payerAccount" placeholder="请输入付款人账号" :disabled="User.role =='View'">
            </FormItem>
            <FormItem label="付款人开户行" prop="payerBank">
              <input type="text" v-model="form.payerBank" placeholder="请输入付款人开户行" :disabled="User.role =='View'">
            </FormItem>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="section-title">其他信息</div>
          <div class="section-content grid-3">
            <FormItem label="银行账户" prop="bankAccount">
              <input type="text" v-model="form.bankAccount" placeholder="根据回单名称自动赋值" :disabled="User.role =='View'">
            </FormItem>
            <FormItem label="支付方式" prop="paymentMethod">
              <input type="text" v-model="form.paymentMethod" placeholder="请输入支付方式" :disabled="User.role =='View'">
            </FormItem>
            <FormItem label="票据数量" prop="receiptNum">
              <input type="number" v-model="form.receiptNum" placeholder="请输入票据数量" :disabled="User.role =='View'" min="1">
            </FormItem>
          </div>
        </div>

        <!-- 详细描述 -->
        <div class="form-section">
          <div class="section-title">详细描述</div>
          <div class="section-content grid-full">
            <FormItem label="摘要" prop="summary" class="important-field">
              <textarea v-model="form.summary" placeholder="请输入摘要" :disabled="User.role =='View'"></textarea>
            </FormItem>
            <FormItem label="备注" prop="remark">
              <textarea v-model="form.remark" placeholder="请输入备注" :disabled="User.role =='View'"></textarea>
            </FormItem>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <Button v-if="User.role !='View'" color="primary" :loading="saving" @click="save">保存</Button>
          <Button @click="goBack">返回</Button>
        </div>
      </Form>
    </div>
  </app-content>
</template>

<script>
import moment from 'moment'
import FileUpload from '@/components/FileUpload.vue'
// import BillSelector from '@/components/BillSelector.vue' // 关联功能已移至关联管理模块
import formMixin from '@/mixins/formMixin.js'

export default {
  name: "BankReceiptsForm",
  components: {
    FileUpload
    // BillSelector // 关联功能已移至关联管理模块
  },
  mixins: [formMixin],
  data() {
    return {
      form: {
        id: null,
        receiptsNo: '',
        receiptsDate: moment().format('YYYY-MM-DD'),
        type: '收入',
        amount: 0,
        summary: '',
        remark: '',
        receiptNum: 1,
        bankAccount: '',
        paymentMethod: '',
        filePath: '',
        payeeName: '',
        payeeAccount: '',
        payeeBank: '',
        payerName: '',
        payerAccount: '',
        payerBank: '',
        transferDate: '',
        serialNumber: '',
        payerBankCode: '',
        payeeBankCode: '',
        amountInWords: '',
        transactionInstitution: '',
        receiptTitle: '',
        ocrRecognitionInfo: '',
        // 自动计算字段
        receiptsYear: null,
        receiptsMonth: null,
        createDate: null,
        createMember: null
      },
      uploadedFileName: '',
      // 关联功能已移至关联管理模块
      typeOptions: [
        {title: '收入', key: '收入'},
        {title: '支出', key: '支出'}
      ],
      rules: {
        receiptsDate: { required: true, message: '请选择单据日期' },
        type: { required: true, message: '请选择类型' },
        amount: { required: true, message: '请输入金额' },
        summary: { required: true, message: '请输入摘要' }
      }
    }
  },
  computed: {
    bankReceiptsId() {
      return this.$route.params.bankReceiptsId
    },
    receiptsNo() {
      return this.form.receiptsNo
    }
  },
  watch: {
    // 监听回单名称变化，自动设置银行账户
    'form.receiptTitle'() {
      this.updateBankAccount()
    },
    // 监听付款人账号变化
    'form.payerAccount'() {
      this.updateBankAccount()
    },
    // 监听收款人账号变化
    'form.payeeAccount'() {
      this.updateBankAccount()
    }
  },
  created() {
    if (this.bankReceiptsId) {
      this.loadData()
    } else {
      this.loadReceiptsNo()
    }
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const { data } = await this.$api.bankReceipts.load(this.bankReceiptsId)
        this.form = {
          ...data,
          receiptsDate: this.formatDate(data.receiptsDate)
        }
        this.uploadedFileName = data.filePath ? data.filePath.split('/').pop() : ''
        
        // 关联功能已移至关联管理模块
      } finally {
        this.loading = false
      }
    },
    
    // 关联功能已移至关联管理模块
    
    loadReceiptsNo() {
      const date = moment()
      this.$api.bankReceipts.loadReceiptsNo({
        year: date.year(),
        month: date.month() + 1
      }).then(({data}) => {
        this.form.receiptsNo = data
      })
    },
    
    async save() {
      try {
        // 保存银行回单基本信息
        await this.saveForm(
            this.$api.bankReceipts,
            null,
            () => {
                if (this.form.receiptsDate) {
                    this.form.receiptsDate = this.formatDate(this.form.receiptsDate);

                    // 根据单据日期自动计算年月
                    const receiptDate = moment(this.form.receiptsDate);
                    this.form.receiptsYear = receiptDate.year();
                    this.form.receiptsMonth = receiptDate.month() + 1; // moment的月份从0开始，需要+1
                }

                // 设置创建时间
                if (!this.form.createDate) {
                    this.form.createDate = moment().format('YYYY-MM-DD HH:mm:ss');
                }

                // 设置转账日期和提交日期的年月（如果有的话）
                if (this.form.transferDate) {
                    this.form.transferDate = this.formatDate(this.form.transferDate);
                }
                if (this.form.submissionDate) {
                    this.form.submissionDate = this.formatDate(this.form.submissionDate);
                }
            }
        );

        // 关联功能已移至关联管理模块

        this.$Message('保存成功');
        this.$router.push('/bills/bank-receipts');

    } catch (error) {
        console.error('保存失败:', error);
        this.$Message.error('保存失败，请检查数据');
    }
},

    // 关联功能已移至关联管理模块，删除相关方法

    handleRecognitionResult(data) {
      console.log('银行回单OCR识别结果:', data) // 添加调试信息

      // 保存完整的OCR识别信息（优先使用易读格式）
      if (data.ocrRecognitionInfo) {
        this.form.ocrRecognitionInfo = data.ocrRecognitionInfo
        console.log('保存易读格式OCR识别信息:', this.form.ocrRecognitionInfo)
      } else if (data.ocrRecognitionInfoJson) {
        // 如果没有易读格式，使用JSON格式
        this.form.ocrRecognitionInfo = data.ocrRecognitionInfoJson
        console.log('保存JSON格式OCR识别信息:', this.form.ocrRecognitionInfo)
      } else if (data.rawOcrData) {
        // 如果都没有，但有原始数据，则保存原始数据
        this.form.ocrRecognitionInfo = JSON.stringify(data.rawOcrData, null, 2)
        console.log('保存原始OCR数据:', this.form.ocrRecognitionInfo)
      }

      // 处理OCR识别结果 - 支持驼峰和下划线两种格式
      if (data.amount) {
        this.form.amount = this.formatAmountValue(data.amount) || 0
      }
      if (data.summary) {
        this.form.summary = String(data.summary).trim()
      }
      if (data.bankAccount || data.bank_account) {
        this.form.bankAccount = String(data.bankAccount || data.bank_account).trim()
      }
      if (data.paymentMethod || data.payment_method) {
        this.form.paymentMethod = String(data.paymentMethod || data.payment_method).trim()
      }
      if (data.receiptsDate || data.receipts_date) {
        this.form.receiptsDate = this.formatDate(data.receiptsDate || data.receipts_date)
      }

      // 新字段映射 - 支持驼峰和下划线两种格式，以及从rawOcrData提取
      if (data.payeeName || data.payee_name || this.getBankReceiptFieldFromRaw(data, 'payeeName')) {
        this.form.payeeName = String(data.payeeName || data.payee_name || this.getBankReceiptFieldFromRaw(data, 'payeeName')).trim()
      }
      if (data.payeeAccount || data.payee_account || this.getBankReceiptFieldFromRaw(data, 'payeeAccount')) {
        this.form.payeeAccount = String(data.payeeAccount || data.payee_account || this.getBankReceiptFieldFromRaw(data, 'payeeAccount')).trim()
      }
      if (data.payeeBank || data.payee_bank) {
        this.form.payeeBank = String(data.payeeBank || data.payee_bank).trim()
      }
      if (data.payerName || data.payer_name || this.getBankReceiptFieldFromRaw(data, 'payerName')) {
        this.form.payerName = String(data.payerName || data.payer_name || this.getBankReceiptFieldFromRaw(data, 'payerName')).trim()
      }
      if (data.payerAccount || data.payer_account || this.getBankReceiptFieldFromRaw(data, 'payerAccount')) {
        this.form.payerAccount = String(data.payerAccount || data.payer_account || this.getBankReceiptFieldFromRaw(data, 'payerAccount')).trim()
      }
      if (data.payerBank || data.payer_bank) {
        this.form.payerBank = String(data.payerBank || data.payer_bank).trim()
      }
      if (data.transferDate || data.transfer_date || data.transactionDate || this.getBankReceiptFieldFromRaw(data, 'transferDate')) {
        this.form.transferDate = this.formatDate(data.transferDate || data.transfer_date || data.transactionDate || this.getBankReceiptFieldFromRaw(data, 'transferDate'))
      }
      if (data.submissionDate) {
        this.form.submissionDate = this.formatDate(data.submissionDate)
      }
      if (data.serialNumber || data.serial_number || this.getBankReceiptFieldFromRaw(data, 'serialNumber')) {
        this.form.serialNumber = String(data.serialNumber || data.serial_number || this.getBankReceiptFieldFromRaw(data, 'serialNumber')).trim()
      }
      if (data.amountInWords || data.amount_in_words) {
        this.form.amountInWords = String(data.amountInWords || data.amount_in_words).trim()
      }
      if (data.transactionInstitution || data.transaction_institution || this.getBankReceiptFieldFromRaw(data, 'transactionInstitution')) {
        this.form.transactionInstitution = String(data.transactionInstitution || data.transaction_institution || this.getBankReceiptFieldFromRaw(data, 'transactionInstitution')).trim()
      }
      if (data.receiptTitle || data.receipt_title || this.getBankReceiptFieldFromRaw(data, 'receiptTitle')) {
        this.form.receiptTitle = String(data.receiptTitle || data.receipt_title || this.getBankReceiptFieldFromRaw(data, 'receiptTitle')).trim()
      }

      // 处理转账日期
      if (data.transfer_date) {
        this.form.transferDate = this.formatDate(data.transfer_date)
      }

      // 处理张数
      if (data.quantity) {
        this.form.receiptNum = parseInt(data.quantity) || 1
      }

      // 处理类型
      if (data.type) {
        const typeStr = String(data.type).trim()
        if (typeStr === '收入' || typeStr === '支出') {
          this.form.type = typeStr
        } else {
          // 兼容旧的判断逻辑
          const lowerTypeStr = typeStr.toLowerCase()
          if (lowerTypeStr.includes('借方') || lowerTypeStr.includes('支出') || lowerTypeStr.includes('付款')) {
            this.form.type = '支出'
          } else if (lowerTypeStr.includes('贷方') || lowerTypeStr.includes('收入') || lowerTypeStr.includes('收款')) {
            this.form.type = '收入'
          }
        }
      }

      // 处理备注字段
      if (data.remark) {
        this.form.remark = String(data.remark).trim()
      }

      // OCR识别完成后，自动设置银行账户
      this.$nextTick(() => {
        this.updateBankAccount()
      })

      // 关联功能已移至关联管理模块
    },

    formatAmount(amount) {
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    formatDate(date) {
      return moment(date).format('YYYY-MM-DD')
    },

    // 根据回单名称自动设置银行账户
    updateBankAccount() {
      if (!this.form.receiptTitle) {
        return
      }

      const receiptTitle = this.form.receiptTitle

      // 如果回单名称包含"借方"，使用付款人账号
      if (receiptTitle.includes('借方')) {
        this.form.bankAccount = this.form.payerAccount || ''
        console.log('检测到借方，设置银行账户为付款人账号:', this.form.bankAccount)
      }
      // 如果回单名称包含"贷方"，使用收款人账号
      else if (receiptTitle.includes('贷方')) {
        this.form.bankAccount = this.form.payeeAccount || ''
        console.log('检测到贷方，设置银行账户为收款人账号:', this.form.bankAccount)
      }
      // 如果都不包含，保持当前值不变
      else {
        console.log('回单名称不包含借方或贷方，银行账户保持不变')
      }
    },

    // 格式化金额值，去除货币符号和逗号
    formatAmountValue(amountValue) {
      if (!amountValue) return 0

      // 转换为字符串并去除货币符号、逗号等
      const cleanAmount = String(amountValue)
        .replace(/[CNY¥$€£,\s]/g, '') // 去除常见货币符号和逗号
        .trim()

      // 验证是否为有效数字
      const numericValue = parseFloat(cleanAmount)
      if (isNaN(numericValue)) {
        console.warn('无效的金额格式:', amountValue)
        return 0
      }

      return numericValue
    },

    // 从原始OCR数据中获取银行回单字段
    getBankReceiptFieldFromRaw(data, fieldName) {
      try {
        // 如果有rawOcrData，从中提取
        if (data.rawOcrData) {
          switch (fieldName) {
            case 'payeeName':
              return data.rawOcrData['收款人名称'] || data.rawOcrData['收款方'] || ''
            case 'payerName':
              return data.rawOcrData['付款人名称'] || data.rawOcrData['付款方'] || ''
            case 'payeeAccount':
              return data.rawOcrData['收款人账号'] || data.rawOcrData['收款账号'] || ''
            case 'payerAccount':
              return data.rawOcrData['付款人账号'] || data.rawOcrData['付款账号'] || ''
            case 'transferDate':
              const dateStr = data.rawOcrData['记账日期'] || data.rawOcrData['转账日期'] || data.rawOcrData['交易日期'] || ''
              return dateStr
            case 'serialNumber':
              return data.rawOcrData['会计流水号'] || data.rawOcrData['回单编号'] || data.rawOcrData['流水号'] || ''
            case 'transactionInstitution':
              return data.rawOcrData['机构'] || data.rawOcrData['开户行名称'] || data.rawOcrData['交易机构'] || ''
            case 'receiptTitle':
              return data.rawOcrData['回单类型'] || data.rawOcrData['回单名称'] || data.rawOcrData['业务类型'] || ''
            default:
              return ''
          }
        }
        return ''
      } catch (error) {
        console.warn('从原始OCR数据提取字段失败:', error, fieldName)
        return ''
      }
    }
  }
}
</script>

<style scoped>
@import "../../styles/form-layout.css";

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 999;
}

/* 关联票据相关样式已删除，功能移至关联管理模块 */
</style>
