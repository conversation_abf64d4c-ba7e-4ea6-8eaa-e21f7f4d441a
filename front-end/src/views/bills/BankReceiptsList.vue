<template>
  <app-content class="h-panel">
    <div class="h-panel-bar"><span class="h-panel-title">银行回单列表</span></div>
    <div class="margin-right-left margin-top">
      <account-date-choose v-model="accountDate"/>
      <div class="float-right">
        <Button color="primary" icon="h-icon-plus" @click="addBankReceipts">新增银行回单</Button>
        <Button :loading="loading" @click="exportData">导出</Button>
        <Button v-if="User.role!=='View'" color="yellow" :loading="repairLoading" @click="batchRepair">批量修复</Button>
        <Button v-if="User.role!=='View'" color="red" @click="batchDelete">删除</Button>
      </div>
    </div>
    <div class="h-panel-body">
      <table class="header">
        <tr>
          <th style="width: 50px"><input :checked="checkAll" type="checkbox" @click="checkAll=!checkAll"></th>
          <td style="width: 120px">单据编号</td>
          <td style="width: 100px">转账日期</td>
          <td style="width: 100px">类型</td>
          <td style="width: 120px">金额</td>
          <td style="width: 150px">收款人/付款人</td>
          <td>摘要</td>
          <td style="width: 120px">操作</td>
        </tr>
      </table>
      <table v-if="!datas.length">
        <tr>
          <td colspan="8" class="text-center padding">暂无数据</td>
        </tr>
      </table>
      <table class="details" v-for="data in datas" :key="data.id">
        <tr>
          <th style="width: 50px"><input :class="{'display':data._checked}" v-model="data._checked" type="checkbox"></th>
          <td style="width: 120px" class="text-ellipsis" :title="data.receiptsNo">{{ data.receiptsNo }}</td>
          <td style="width: 100px">{{ data.transferDate || data.receiptsDate }}</td>
          <td style="width: 100px">{{ data.type }}</td>
          <td style="width: 120px">{{ data.amount | numFormat }}</td>
          <td style="width: 150px" class="text-ellipsis" :title="getCounterpartyDisplay(data)">{{ getCounterpartyDisplay(data) }}</td>
          <td class="text-ellipsis" :title="data.summary">{{ data.summary }}</td>
          <td style="width: 120px" class="actions">
            <router-link tag="span" :to="{name:'BankReceiptsForm',params:{bankReceiptsId:data.id}}">查看</router-link>
            <router-link v-if="User.role!=='View'" tag="span" :to="{name:'BankReceiptsForm',params:{bankReceiptsId:data.id}}">修改</router-link>
            <span v-if="User.role!=='View' && needsRepair(data)" @click="repairSingle(data)" class="repair-btn">修复</span>
            <span v-if="User.role!=='View'" @click="remove(data)">删除</span>
          </td>
        </tr>
      </table>
      <Pagination
        v-model="pagination"
        @change="currentChange"
        @size-change="handleSizeChange"
        layout="sizes, pager"
        :page-sizes="[10, 20, 50]"
        small
        align="center"/>
    </div>


  </app-content>
</template>

<script>
  import moment from 'moment'
  import {mapState} from 'vuex'

  export default {
    name: "BankReceiptsList",
    components: {
    },
    data() {
      return {
        datas: [],
        accountDate: null,
        pagination: {
          page: 1,
          size: 10,
          total: 0
        },
        loading: false,
        repairLoading: false,
        checkAll: false
      };
    },
    watch: {
      checkAll(nval) {
        let data = Array.from(this.datas);
        data.forEach(val => val._checked = nval);
        this.datas = data;
      },
      accountDate() {
        this.pagination.page = 1;
        this.loadList();
      }
    },
    computed: {
      ...mapState(['User', 'currentAccountSets']),
      date() {
        return moment(this.accountDate);
      }
    },
    methods: {
      currentChange() {
        this.loadList();
      },
      handleSizeChange(size) {
        this.pagination.size = size;
        this.pagination.page = 1;
        this.loadList();
      },
      addBankReceipts() {
        this.$router.push({name: 'BankReceiptsForm'});
      },
      exportData() {
        // 导出功能实现
        this.$Message("导出功能开发中");
      },
      loadList() {
        this.loading = true;
        this.$api.bankReceipts.list({
          receipts_year: this.date.year(),
          receipts_month: this.date.month() + 1,
          page: this.pagination.page,
          size: this.pagination.size
        }).then(({data}) => {
          this.datas = data.records;
          this.pagination = {
            page: data.current,
            size: data.size,
            total: data.total
          }
        }).finally(() => {
          this.loading = false;
        });
      },
      remove(data) {
        this.$Confirm("确认删除?").then(() => {
          this.loading = true;
          this.$api.bankReceipts.delete(data.id).then(() => {
            this.loadList();
            this.$Message("删除成功");
          }).finally(() => {
            this.loading = false;
          });
        });
      },
      batchDelete() {
        let checked = this.datas.filter(value => value._checked);
        if (checked.length === 0) {
          this.$Message("请选择要删除的银行回单");
          return;
        }
        this.$Confirm(`确认删除选中的 ${checked.length} 条银行回单?`).then(() => {
          this.loading = true;
          let ids = checked.map(value => value.id);
          this.$api.bankReceipts.batchDelete(ids).then(() => {
            this.loadList();
            this.$Message("批量删除成功");
          }).finally(() => {
            this.loading = false;
          });
        });
      },
      getCounterpartyDisplay(data) {
        // 根据类型显示收款人或付款人信息
        if (data.type === '收入') {
          // 收入类型显示付款人信息
          return data.payerName || data.payerAccount || '未知付款人';
        } else {
          // 支出类型显示收款人信息
          return data.payeeName || data.payeeAccount || '未知收款人';
        }
      },
      needsRepair(data) {
        // 根据交易类型检查是否需要修复（只检查字段为空的情况）
        if (data.type === '支出') {
          // 支出类型：检查收款人是否为空
          return !data.payeeName;
        } else if (data.type === '收入') {
          // 收入类型：检查付款人是否为空
          return !data.payerName;
        }
        return false;
      },
      repairSingle(data) {
        this.$Confirm(`确认修复单据"${data.receiptsNo}"的数据？`).then(() => {
          this.loading = true;
          this.$api.bankReceipts.repairSingleReceipt(data.id).then(({data: result}) => {
            if (result.success) {
              this.$Message.success('修复成功');
              // 刷新列表
              this.loadList();
            } else {
              this.$Message.error(result.msg || result.message || '修复失败');
            }
          }).catch((error) => {
            console.error('修复失败:', error);
            this.$Message.error('修复失败: ' + (error.response && error.response.data && error.response.data.msg ? error.response.data.msg : error.message));
          }).finally(() => {
            this.loading = false;
          });
        });
      },
      batchRepair() {
        console.log('batchRepair方法被调用');
        // 获取当前页面需要修复的记录
        const needRepairRecords = this.datas.filter(data => this.needsRepair(data));
        console.log('需要修复的记录:', needRepairRecords);

        if (needRepairRecords.length === 0) {
          this.$Message.warning('当前页面没有需要修复的记录');
          return;
        }

        // 提取记录ID
        const receiptIds = needRepairRecords.map(record => record.id);
        console.log('提取的记录ID:', receiptIds);

        this.$Confirm(`确认批量修复当前页面的 ${needRepairRecords.length} 条"未知收款人/付款人"记录？\n\n修复后将从OCR数据中自动提取收款人/付款人信息。`).then(() => {
          console.log('用户确认批量修复');
          this.repairLoading = true;
          console.log('开始调用API，记录ID:', receiptIds);
          this.$api.bankReceipts.repairBatchReceipts(receiptIds).then(({data: result}) => {
            console.log('批量修复API响应:', result);
            if (result.success) {
              const repairedCount = result.data.repairedCount || 0;
              const totalRequested = result.data.totalRequested || receiptIds.length;
              if (repairedCount > 0) {
                this.$Message.success(`✅ 批量修复完成！成功修复 ${repairedCount} 条记录（共 ${totalRequested} 条）`);
              } else {
                this.$Message.success(`✅ 批量修复完成！当前 ${totalRequested} 条记录中没有可修复的数据`);
              }
              // 刷新列表
              this.loadList();
            } else {
              console.log('批量修复失败，响应数据:', result);
              this.$Message.error(result.msg || result.message || '批量修复失败');
            }
          }).catch((error) => {
            console.error('批量修复请求异常:', error);
            this.$Message.error('批量修复失败: ' + (error.response && error.response.data && error.response.data.msg ? error.response.data.msg : error.message));
          }).finally(() => {
            console.log('批量修复完成，设置loading为false');
            this.repairLoading = false;
          });
        }).catch(() => {
          console.log('用户取消了批量修复');
        });
      }
    },
    mounted() {
      // 初始化日期为当前账套日期
      this.accountDate = this.currentAccountSets.currentAccountDate;
      this.loadList();
    }
  };
</script>

<style lang="less">
  .details {
    margin-bottom: 10px;
    table-layout: fixed;
    width: 100%;

    .actions {
      span {
        margin-right: 5px;
        color: #1890ff;
        cursor: pointer;

        &.repair-btn {
          color: #faad14;
          font-weight: bold;
        }
      }
    }

    td, th {
      padding: 8px;
      text-align: left;
      word-wrap: break-word;
      overflow: hidden;
    }
  }

  .header {
    margin-bottom: 10px;
    background-color: #f8f8f8;
    table-layout: fixed;
    width: 100%;

    th, td {
      padding: 8px;
      text-align: left;
    }
  }

  .text-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: help;
  }

  .text-ellipsis:hover {
    position: relative;
  }

  .text-ellipsis:hover::after {
    content: attr(title);
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: normal;
    word-wrap: break-word;
    max-width: 300px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
</style>
