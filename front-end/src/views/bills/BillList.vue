<template>
  <app-content class="h-panel">
    <div class="h-panel-bar"><span class="h-panel-title">票据列表</span></div>
    <div class="margin-right-left margin-top">
      <account-date-choose v-model="accountDate"/>
      <div class="float-right">
        <Button color="primary" icon="h-icon-plus" @click="addBill">新增票据</Button>
        <Button :loading="loading" @click="exportData">导出</Button>
        <Button v-if="User.role!=='View'" color="red" @click="batchDelete">删除</Button>
      </div>
    </div>
    <div class="h-panel-body">
      <table class="header">
        <tr>
          <th style="width: 50px"><input :checked="checkAll" type="checkbox" @click="checkAll=!checkAll"></th>
          <td style="width: 120px">票据编号</td>
          <td style="width: 100px">日期</td>
          <td style="width: 100px">类型</td>
          <td style="width: 120px">金额</td>
          <td style="width: 150px">开票方</td>
          <td style="width: 150px">收票方</td>
          <td>摘要</td>
          <td style="width: 100px">状态</td>
          <td style="width: 120px">操作</td>
        </tr>
      </table>
      <table v-if="!datas.length">
        <tr>
          <td colspan="10" class="text-center padding">暂无数据</td>
        </tr>
      </table>
      <table v-for="data in datas" :key="data.id" class="details">
        <tr>
          <td style="width: 50px"><input v-model="data._checked" type="checkbox"></td>
          <td style="width: 120px">{{ data.billNo }}</td>
          <td style="width: 100px">{{ data.billDate | date }}</td>
          <td style="width: 100px">{{ data.type }}</td>
          <td style="width: 120px">{{ data.amount | currency }}</td>
          <td style="width: 150px">{{ data.issuer }}</td>
          <td style="width: 150px">{{ data.recipient }}</td>
          <td>{{ data.summary }}</td>
          <td style="width: 100px">
            <span :class="getStatusClass(data.status)">{{ data.status }}</span>
          </td>
          <td style="width: 120px" class="actions">
            <router-link tag="span" :to="{name:'BillForm',params:{billId:data.id}}">查看</router-link>
            <router-link v-if="User.role!=='View'" tag="span" :to="{name:'BillForm',params:{billId:data.id}}">修改</router-link>
            <span v-if="User.role!=='View'" @click="remove(data)">删除</span>
          </td>
        </tr>
      </table>
      <Pagination v-model="pagination" @change="currentChange" layout="pager" small align="center"/>
    </div>
  </app-content>
</template>

<script>
  import moment from 'moment'
  import {mapState} from 'vuex'

  export default {
    name: "BillList",
    data() {
      return {
        datas: [],
        accountDate: null,
        pagination: {
          page: 1,
          total: 0
        },
        loading: false,
        checkAll: false
      };
    },
    watch: {
      checkAll(nval) {
        let data = Array.from(this.datas);
        data.forEach(val => val._checked = nval);
        this.datas = data;
      },
      accountDate() {
        this.pagination.page = 1;
        this.loadList();
      }
    },
    computed: {
      ...mapState(['User', 'currentAccountSets']),
      date() {
        return moment(this.accountDate);
      }
    },
    methods: {
      currentChange() {
        this.loadList();
      },
      addBill() {
        this.$router.push({name: 'BillForm'});
      },
      exportData() {
        // 导出功能实现
        this.$Message("导出功能开发中");
      },
      loadList() {
        this.loading = true;
        this.$api.bill.list({
          bill_year: this.date.year(), 
          bill_month: this.date.month() + 1, 
          page: this.pagination.page
        }).then(({data}) => {
          this.datas = data.records;
          this.pagination = {
            page: data.current,
            size: data.size,
            total: data.total
          }
        }).finally(() => {
          this.loading = false;
        });
      },
      remove(data) {
        this.$Confirm("确认删除?").then(() => {
          this.loading = true;
          this.$api.bill.delete(data.id).then(() => {
            this.loadList();
            this.$Message("删除成功");
          }).finally(() => {
            this.loading = false;
          });
        });
      },
      batchDelete() {
        let checked = this.datas.filter(value => value._checked);
        if (checked.length === 0) {
          this.$Message("请选择要删除的票据");
          return;
        }
        this.$Confirm(`确认删除选中的 ${checked.length} 条票据?`).then(() => {
          this.loading = true;
          let ids = checked.map(value => value.id);
          this.$api.bill.batchDelete(ids).then(() => {
            this.loadList();
            this.$Message("批量删除成功");
          }).finally(() => {
            this.loading = false;
          });
        });
      },
      getStatusClass(status) {
        switch(status) {
          case '未使用':
            return 'status-unused';
          case '已使用':
            return 'status-used';
          case '已作废':
            return 'status-invalid';
          default:
            return '';
        }
      }
    },
    mounted() {
      // 初始化日期为当前账套日期
      this.accountDate = this.currentAccountSets.currentAccountDate;
      this.loadList();
    }
  };
</script>

<style lang="less">
  .details {
    margin-bottom: 10px;
    
    .actions {
      span {
        margin-right: 5px;
        color: #1890ff;
        cursor: pointer;
      }
    }
  }
  
  .header {
    margin-bottom: 10px;
    background-color: #f8f8f8;
    
    th, td {
      padding: 8px;
      text-align: left;
    }
  }
  
  .status-unused {
    color: #52c41a;
  }
  
  .status-used {
    color: #1890ff;
  }
  
  .status-invalid {
    color: #ff4d4f;
  }
</style>