<template>
  <div class="batch-import">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>批量导入</h2>
        <p class="page-description">
          支持PDF和图片的批量上传、OCR识别和数据导入，提高录入效率
        </p>
      </div>
      
      <div class="header-actions">
        <button class="btn-history" @click="showTaskHistory">
          <i class="h-icon-history"></i>
          历史任务
        </button>
      </div>
    </div>

    <!-- 导入类型选择 -->
    <div class="import-type-section">
      <div class="type-tabs">
        <div
          class="type-tab"
          :class="{ active: importType === 'BANK_RECEIPT' }"
          @click="setImportType('BANK_RECEIPT')"
        >
          <i class="h-icon-bank"></i>
          <span>银行回单</span>
        </div>
        <div
          class="type-tab"
          :class="{ active: importType === 'INVOICE' }"
          @click="setImportType('INVOICE')"
        >
          <i class="h-icon-receipt"></i>
          <span>发票</span>
        </div>
      </div>
    </div>

    <!-- 步骤指示器 -->
    <div class="steps-indicator">
      <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <div class="step-number">1</div>
        <div class="step-title">上传文件</div>
      </div>
      <div class="step-line" :class="{ completed: currentStep > 1 }"></div>
      <div class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <div class="step-number">2</div>
        <div class="step-title">OCR识别</div>
      </div>
      <div class="step-line" :class="{ completed: currentStep > 2 }"></div>
      <div class="step" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
        <div class="step-number">3</div>
        <div class="step-title">预览编辑</div>
      </div>
      <div class="step-line" :class="{ completed: currentStep > 3 }"></div>
      <div class="step" :class="{ active: currentStep >= 4 }">
        <div class="step-number">4</div>
        <div class="step-title">批量保存</div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 步骤1: 文件上传 -->
      <div v-if="currentStep === 1" class="step-content">
        <BatchUpload 
          :default-type="importType"
          @upload-success="handleUploadSuccess"
          @cancel="resetProcess"
        />
      </div>

      <!-- 步骤2-4: 批量预览和处理 -->
      <div v-if="currentStep >= 2" class="step-content">
        <BatchPreview 
          :task-id="currentTaskId"
          @edit-item="handleEditItem"
          @batch-save="handleBatchSave"
        />
      </div>
    </div>

    <!-- 任务历史弹窗 -->
    <Modal 
      v-model="showHistoryModal" 
      title="历史任务" 
      width="900"
      :mask-closable="false"
    >
      <div class="task-history">
        <div class="history-toolbar">
          <div class="filter-options">
            <label>类型筛选：</label>
            <select v-model="historyFilter.type" @change="loadTaskHistory">
              <option value="">全部</option>
              <option value="BANK_RECEIPT">银行回单</option>
              <option value="INVOICE">发票</option>
            </select>
            
            <label>状态筛选：</label>
            <select v-model="historyFilter.status" @change="loadTaskHistory">
              <option value="">全部</option>
              <option value="COMPLETED">已完成</option>
              <option value="FAILED">失败</option>
              <option value="RECOGNIZING">识别中</option>
            </select>
          </div>
          
          <button class="btn-refresh" @click="loadTaskHistory">
            <i class="h-icon-refresh"></i>
            刷新
          </button>
        </div>
        
        <div class="history-list">
          <div 
            v-for="task in taskHistory" 
            :key="task.taskId" 
            class="history-item"
            @click="continueTask(task)"
          >
            <div class="task-info">
              <div class="task-name">{{ task.taskName }}</div>
              <div class="task-meta">
                <span class="task-type">{{ getTypeText(task.importType) }}</span>
                <span class="task-time">{{ formatTime(task.createdTime) }}</span>
              </div>
            </div>
            
            <div class="task-progress">
              <div class="progress-text">
                {{ task.successCount || 0 }}/{{ task.totalCount || 0 }}
              </div>
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: (task.progress || 0) + '%' }"
                ></div>
              </div>
            </div>
            
            <div class="task-status" :class="task.status">
              {{ getStatusText(task.status) }}
            </div>
            
            <div class="task-actions">
              <button 
                v-if="task.status === 'COMPLETED'" 
                class="btn-view" 
                @click.stop="viewTask(task)"
              >
                查看
              </button>
              <button 
                v-if="['PROCESSING', 'RECOGNIZING', 'PREVIEWING'].includes(task.status)" 
                class="btn-continue" 
                @click.stop="continueTask(task)"
              >
                继续
              </button>
              <button 
                class="btn-delete" 
                @click.stop="deleteTask(task)"
              >
                删除
              </button>
            </div>
          </div>
        </div>
        
        <div class="empty-history" v-if="taskHistory.length === 0">
          <i class="h-icon-empty"></i>
          <p>暂无历史任务</p>
        </div>
      </div>
      
      <div slot="footer">
        <button class="btn-cancel" @click="showHistoryModal = false">关闭</button>
      </div>
    </Modal>

    <!-- 编辑项目弹窗 -->
    <Modal
      v-model="showEditModal"
      title="编辑识别结果"
      width="800"
      :mask-closable="false"
    >
      <div class="edit-form" v-if="editingItem">
        <Form ref="editForm" :labelWidth="120" mode="block" :model="editForm">
          <FormItem label="回单日期" required>
            <DatePicker v-model="editForm.receiptsDate" type="date" placeholder="选择日期" />
          </FormItem>
          <FormItem label="转账日期" required>
            <DatePicker v-model="editForm.transferDate" type="date" placeholder="选择转账日期" />
          </FormItem>
          <FormItem label="流水号">
            <input type="text" v-model="editForm.serialNumber" placeholder="请输入流水号">
          </FormItem>
          <FormItem label="交易机构">
            <input type="text" v-model="editForm.transactionInstitution" placeholder="请输入交易机构">
          </FormItem>
          <FormItem label="回单名称">
            <input type="text" v-model="editForm.receiptTitle" placeholder="请输入回单名称">
          </FormItem>
          <FormItem label="类型" required>
            <Select v-model="editForm.type" :datas="typeOptions"></Select>
          </FormItem>
          <FormItem label="金额" required>
            <input type="number" v-model="editForm.amount" placeholder="请输入金额" step="0.01">
          </FormItem>
          <FormItem label="金额大写">
            <input type="text" v-model="editForm.amountInWords" placeholder="请输入金额大写">
          </FormItem>
          <FormItem label="收款人姓名">
            <input type="text" v-model="editForm.payeeName" placeholder="请输入收款人姓名">
          </FormItem>
          <FormItem label="收款人账号">
            <input type="text" v-model="editForm.payeeAccount" placeholder="请输入收款人账号">
          </FormItem>
          <FormItem label="收款人开户行">
            <input type="text" v-model="editForm.payeeBank" placeholder="请输入收款人开户行">
          </FormItem>
          <FormItem label="付款人姓名">
            <input type="text" v-model="editForm.payerName" placeholder="请输入付款人姓名">
          </FormItem>
          <FormItem label="付款人账号">
            <input type="text" v-model="editForm.payerAccount" placeholder="请输入付款人账号">
          </FormItem>
          <FormItem label="付款人开户行">
            <input type="text" v-model="editForm.payerBank" placeholder="请输入付款人开户行">
          </FormItem>
          <FormItem label="摘要" required>
            <textarea v-model="editForm.summary" placeholder="请输入摘要"></textarea>
          </FormItem>
          <FormItem label="银行账户">
            <input type="text" v-model="editForm.bankAccount" placeholder="根据回单名称自动赋值">
          </FormItem>
          <FormItem label="支付方式">
            <input type="text" v-model="editForm.paymentMethod" placeholder="请输入支付方式">
          </FormItem>
          <FormItem label="票据数量">
            <input type="number" v-model="editForm.receiptNum" placeholder="请输入票据数量">
          </FormItem>
          <FormItem label="备注">
            <textarea v-model="editForm.remark" placeholder="请输入备注" rows="4" readonly></textarea>
          </FormItem>
        </Form>
      </div>

      <div slot="footer">
        <button class="btn-cancel" @click="showEditModal = false">取消</button>
        <button class="btn-save" @click="saveEditedItem">保存</button>
      </div>
    </Modal>

    <!-- 批量保存确认弹窗 -->
    <Modal 
      v-model="showSaveModal" 
      title="批量保存确认" 
      width="500"
      :mask-closable="false"
    >
      <div class="save-confirm" v-if="saveData">
        <p>确认要保存以下 {{ saveData.selectedItems.length }} 条记录吗？</p>
        <div class="save-summary">
          <div class="summary-item">
            <label>任务名称：</label>
            <span>{{ saveData.taskName }}</span>
          </div>
          <div class="summary-item">
            <label>导入类型：</label>
            <span>{{ getTypeText(saveData.importType) }}</span>
          </div>
          <div class="summary-item">
            <label>选中数量：</label>
            <span>{{ saveData.selectedItems.length }} 条</span>
          </div>
        </div>
      </div>
      
      <div slot="footer">
        <button class="btn-cancel" @click="showSaveModal = false">取消</button>
        <button class="btn-save" @click="confirmBatchSave" :disabled="saving">
          {{ saving ? '保存中...' : '确认保存' }}
        </button>
      </div>
    </Modal>
  </div>
</template>

<script>
import BatchUpload from '@/components/BatchUpload.vue'
import BatchPreview from '@/components/BatchPreview.vue'
import moment from 'moment'

export default {
  name: 'BatchImport',
  components: {
    BatchUpload,
    BatchPreview
  },
  data() {
    return {
      currentStep: 1,
      currentTaskId: '',
      importType: 'BANK_RECEIPT', // 默认银行回单
      
      // 历史任务相关
      showHistoryModal: false,
      taskHistory: [],
      historyFilter: {
        type: '',
        status: ''
      },
      
      // 编辑相关
      showEditModal: false,
      editingItem: null,
      editForm: {
        receiptsDate: '',
        transferDate: '',
        serialNumber: '',
        transactionInstitution: '',
        receiptTitle: '',
        amount: 0,
        type: '支出',
        amountInWords: '',
        payerName: '',
        payerAccount: '',
        payerBank: '',
        payeeName: '',
        payeeAccount: '',
        payeeBank: '',
        summary: '',
        bankAccount: '',
        paymentMethod: '',
        receiptNum: 1,
        remark: ''
      },

      // 类型选项
      typeOptions: [
        { key: '收入', title: '收入' },
        { key: '支出', title: '支出' }
      ],
      
      // 保存相关
      showSaveModal: false,
      saveData: null,
      saving: false
    }
  },
  mounted() {
    // 检查URL参数，看是否需要继续某个任务
    const taskId = this.$route.query.taskId
    if (taskId) {
      this.continueTaskById(taskId)
    }
    
    // 根据路由参数设置导入类型
    const type = this.$route.query.type
    if (type && ['BANK_RECEIPT', 'INVOICE'].includes(type)) {
      this.importType = type
    }
  },
  methods: {
    setImportType(type) {
      this.importType = type
      // 如果已经有任务在进行，需要重新开始
      if (this.currentTaskId) {
        this.resetProcess()
      }
    },

    handleUploadSuccess(uploadResult) {
      this.currentTaskId = uploadResult.taskId
      this.currentStep = 2
      
      // 更新URL，保存任务状态
      this.$router.replace({
        query: { 
          ...this.$route.query,
          taskId: uploadResult.taskId 
        }
      })
    },
    
    handleEditItem(item) {
      this.editingItem = item

      // 填充编辑表单
      const data = item.data || {}
      this.editForm = {
        receiptsDate: this.formatDate(data.receipts_date || data.receiptsDate),
        transferDate: this.formatDate(data.transfer_date || data.transferDate),
        serialNumber: data.serial_number || data.serialNumber || '',
        transactionInstitution: data.transaction_institution || data.transactionInstitution || '',
        receiptTitle: data.receipt_title || data.receiptTitle || '',
        amount: parseFloat(data.amount) || 0,
        type: data.type || '支出',
        amountInWords: data.amount_in_words || data.amountInWords || '',
        payerName: data.payer_name || data.payerName || '',
        payerAccount: data.payer_account || data.payerAccount || '',
        payerBank: data.payer_bank || data.payerBank || '',
        payeeName: data.payee_name || data.payeeName || '',
        payeeAccount: data.payee_account || data.payeeAccount || '',
        payeeBank: data.payee_bank || data.payeeBank || '',
        summary: data.summary || '',
        bankAccount: data.bank_account || data.bankAccount || '',
        paymentMethod: data.payment_method || data.paymentMethod || '',
        receiptNum: parseInt(data.receipt_num || data.receiptNum) || 1,
        remark: data.remark || ''
      }

      this.showEditModal = true
    },

    async saveEditedItem() {
      try {
        // 验证必填字段
        if (!this.editForm.transferDate) {
          this.$Message.error('转账日期不能为空')
          return
        }
        if (!this.editForm.summary) {
          this.$Message.error('摘要不能为空')
          return
        }
        if (!this.editForm.amount || this.editForm.amount <= 0) {
          this.$Message.error('金额必须大于0')
          return
        }

        // 更新项目数据
        const updatedData = {
          receipts_date: this.editForm.receiptsDate,
          transfer_date: this.editForm.transferDate,
          serial_number: this.editForm.serialNumber,
          transaction_institution: this.editForm.transactionInstitution,
          receipt_title: this.editForm.receiptTitle,
          amount: parseFloat(this.editForm.amount),
          type: this.editForm.type,
          amount_in_words: this.editForm.amountInWords,
          payer_name: this.editForm.payerName,
          payer_account: this.editForm.payerAccount,
          payer_bank: this.editForm.payerBank,
          payee_name: this.editForm.payeeName,
          payee_account: this.editForm.payeeAccount,
          payee_bank: this.editForm.payeeBank,
          summary: this.editForm.summary,
          bank_account: this.editForm.bankAccount,
          payment_method: this.editForm.paymentMethod,
          receipt_num: parseInt(this.editForm.receiptNum) || 1,
          remark: this.editForm.remark
        }

        // 调用API更新数据
        const response = await this.$api.batch.updateItem(this.editingItem.id, updatedData)
        if (response.success) {
          // 更新本地数据
          this.editingItem.data = updatedData
          this.$Message.success('保存成功')
          this.showEditModal = false
          this.editingItem = null
        } else {
          this.$Message.error(response.message || '保存失败')
        }
      } catch (error) {
        this.$Message.error('保存失败: ' + (error.message || '未知错误'))
      }
    },
    
    handleBatchSave(saveData) {
      this.saveData = saveData
      this.showSaveModal = true
    },
    
    async confirmBatchSave() {
      try {
        this.saving = true
        
        const response = await this.$api.batch.batchSave(
          this.saveData.taskId, 
          this.saveData.selectedItems
        )
        
        if (response.success) {
          this.$Message.success(`成功保存 ${response.data.successCount} 条记录`)
          this.showSaveModal = false
          this.currentStep = 4
        } else {
          this.$Message.error(response.message || '保存失败')
        }
        
      } catch (error) {
        this.$Message.error('保存失败: ' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    },
    
    showTaskHistory() {
      this.showHistoryModal = true
      this.loadTaskHistory()
    },
    
    async loadTaskHistory() {
      try {
        const response = await this.$api.batch.getTasks({
          type: this.historyFilter.type,
          status: this.historyFilter.status,
          pageSize: 50
        })
        
        if (response.success) {
          this.taskHistory = response.data.list || response.data
        }
      } catch (error) {
        this.$Message.error('加载历史任务失败')
      }
    },
    
    continueTask(task) {
      this.currentTaskId = task.taskId
      this.importType = task.importType
      
      // 根据任务状态决定步骤
      if (task.status === 'PROCESSING') {
        this.currentStep = 2
      } else if (['RECOGNIZING', 'PREVIEWING'].includes(task.status)) {
        this.currentStep = 3
      } else if (task.status === 'COMPLETED') {
        this.currentStep = 4
      }
      
      this.showHistoryModal = false
      
      // 更新URL
      this.$router.replace({
        query: { 
          taskId: task.taskId,
          type: task.importType
        }
      })
    },
    
    async continueTaskById(taskId) {
      try {
        const response = await this.$api.batch.getProgress(taskId)
        if (response.success) {
          this.continueTask(response.data)
        }
      } catch (error) {
        this.$Message.error('任务不存在或已过期')
        this.resetProcess()
      }
    },
    
    viewTask(task) {
      this.continueTask(task)
    },
    
    async deleteTask(task) {
      try {
        await this.$Confirm(`确定要删除任务"${task.taskName}"吗？此操作不可恢复。`)

        const response = await this.$api.batch.deleteTask(task.taskId)
        if (response.success) {
          this.$Message.success('删除成功')
          this.loadTaskHistory()
        } else {
          this.$Message.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$Message.error('删除失败')
        }
      }
    },
    
    resetProcess() {
      this.currentStep = 1
      this.currentTaskId = ''
      
      // 清除URL参数
      this.$router.replace({
        query: {
          type: this.importType
        }
      })
    },
    
    getTypeText(type) {
      const typeMap = {
        'BANK_RECEIPT': '银行回单',
        'INVOICE': '发票'
      }
      return typeMap[type] || type
    },
    
    getStatusText(status) {
      const statusMap = {
        'UPLOADING': '上传中',
        'PROCESSING': '处理中',
        'RECOGNIZING': '识别中',
        'PREVIEWING': '待预览',
        'SAVING': '保存中',
        'COMPLETED': '已完成',
        'PARTIAL_SUCCESS': '部分成功',
        'FAILED': '失败',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    },
    
    formatTime(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN')
    },

    formatDate(date) {
      if (!date) return ''
      if (typeof date === 'string' && date.includes('-')) {
        return date.split(' ')[0] // 去掉时间部分
      }
      return moment(date).format('YYYY-MM-DD')
    }
  }
}
</script>

<style scoped>
.batch-import {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.import-type-section {
  margin-bottom: 32px;
  display: flex;
  justify-content: center;
}

.type-tabs {
  display: flex;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
  gap: 4px;
}

.type-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #666;
  background: transparent;
}

.type-tab:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.type-tab.active {
  background: #1890ff;
  color: white;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.type-tab i {
  font-size: 16px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.btn-history {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  transition: all 0.3s ease;
}

.btn-history:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.steps-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: #1890ff;
  color: white;
}

.step.completed .step-number {
  background: #52c41a;
  color: white;
}

.step-title {
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
}

.step.active .step-title,
.step.completed .step-title {
  color: #333;
  font-weight: 500;
}

.step-line {
  width: 80px;
  height: 2px;
  background: #f0f0f0;
  transition: all 0.3s ease;
}

.step-line.completed {
  background: #52c41a;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.step-content {
  padding: 20px;
}

.task-history {
  max-height: 500px;
  overflow-y: auto;
}

.history-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.filter-options {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-options label {
  font-size: 14px;
  color: #333;
}

.filter-options select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.btn-refresh {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-refresh:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-item:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.task-info {
  flex: 1;
}

.task-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.task-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #999;
}

.task-type {
  padding: 2px 6px;
  background: #e6f7ff;
  color: #1890ff;
  border-radius: 3px;
}

.task-progress {
  min-width: 120px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  text-align: center;
}

.progress-bar {
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

.task-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.task-status.COMPLETED {
  background: #f6ffed;
  color: #52c41a;
}

.task-status.FAILED {
  background: #fff2f0;
  color: #ff4d4f;
}

.task-status.RECOGNIZING {
  background: #fff7e6;
  color: #fa8c16;
}

.task-actions {
  display: flex;
  gap: 6px;
}

.btn-view,
.btn-continue,
.btn-delete {
  padding: 4px 8px;
  border: 1px solid;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.btn-view,
.btn-continue {
  background: white;
  border-color: #1890ff;
  color: #1890ff;
}

.btn-view:hover,
.btn-continue:hover {
  background: #1890ff;
  color: white;
}

.btn-delete {
  background: white;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.btn-delete:hover {
  background: #ff4d4f;
  color: white;
}

.empty-history {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-history i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.save-confirm {
  padding: 20px 0;
}

.save-summary {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item label {
  font-weight: 500;
  color: #333;
}

.btn-cancel,
.btn-save {
  padding: 8px 16px;
  border: 1px solid;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-left: 8px;
}

.btn-cancel {
  background: white;
  border-color: #d9d9d9;
  color: #666;
}

.btn-cancel:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.btn-save {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.btn-save:hover:not(:disabled) {
  background: #40a9ff;
  border-color: #40a9ff;
}

.btn-save:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
  cursor: not-allowed;
}

/* 编辑表单样式 */
.edit-form {
  padding: 10px 0;
}

.native-form {
  max-width: 100%;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.form-col {
  flex: 1;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-label .required {
  color: #f56c6c;
  margin-left: 2px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
  transition: border-color 0.3s;
}

.form-input:focus {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-input[readonly] {
  background-color: #f5f7fa;
  cursor: not-allowed;
}

textarea.form-input {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

select.form-input {
  background-color: white;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 32px;
}
</style>
