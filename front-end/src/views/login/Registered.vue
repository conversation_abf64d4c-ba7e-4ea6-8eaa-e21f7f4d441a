<template>
	<div class="login-frame">
		<div class="login-name login-input">
			<input type="text" name="mobile" v-model="reg.mobile" autocomplete="off"/>
			<span class="placeholder" :class="{fixed: reg.mobile !== '' && reg.mobile != null}">输入手机号，快速注册</span>
		</div>
		<div class="login-password login-input">
			<input type="password" v-model="reg.password" placeholder="设置密码" autocomplete="off"/>
		</div>
		<div class="login-password login-input">
			<input type="password" v-model="reg.confirmPassword" placeholder="确认密码" autocomplete="off"/>
		</div>
		<div class="buttonDiv">
			<Button :loading="loading" block color="primary" size="l" @click="regSubmit">注册</Button>
		</div>
		<div class="margin" style="margin-bottom: 0 !important;">
			<span class="text-hover" @click="$emit('input','LoginForm')">返回登录</span>
		</div>
	</div>
</template>
<script>
	export default {
		name: 'Registered',
		data() {
			return {
				reg: {
					mobile: "",
					password: "",
					confirmPassword: "",
				},
				loading: false
			};
		},
		methods: {
		regSubmit() {
			if (!this.reg.mobile) {
				this.$notice.error('请输入手机号');
				return;
			}
			if (!this.reg.password) {
				this.$notice.error('请设置密码');
				return;
			}
			if (!this.reg.confirmPassword) {
				this.$notice.error('请确认密码');
				return;
			}
			if (this.reg.password !== this.reg.confirmPassword) {
				this.$notice.error('两次输入的密码不一致');
				return;
			}
			if (this.reg.password.length < 6) {
				this.$notice.error('密码长度不能少于6位');
				return;
			}

			this.loading = true;
			this.$api.common.register(this.reg).then(() => {
				window.location.replace('/');
			}).finally(() => {
				this.loading = false
			});
		}
		}
	}
</script>