<template>
  <div class="login-form">
    <div class="form-group">
      <div class="input-wrapper">
        <i class="fa fa-mobile input-icon"></i>
        <input
          type="text"
          name="mobile"
          v-model="login.mobile"
          autocomplete="off"
          placeholder="手机号"
          class="form-input"
        />
      </div>
    </div>

    <div class="form-group">
      <div class="input-wrapper">
        <i class="fa fa-lock input-icon"></i>
        <input
          type="password"
          name="password"
          v-model="login.password"
          @keyup.enter="submit"
          autocomplete="off"
          placeholder="密码"
          class="form-input"
        />
      </div>
    </div>

    <div class="form-options">
      <label class="remember-me">
        <input type="checkbox" v-model="rememberMe">
        <span class="checkmark"></span>
        记住密码
      </label>
      <a href="#" @click.prevent="$emit('input','ForgotPassword')" class="forgot-password">忘记密码？</a>
    </div>

    <div class="form-group">
      <button
        :disabled="loading"
        @click="submit"
        class="login-button"
        :class="{ 'loading': loading }"
      >
        <span v-if="!loading">登录</span>
        <span v-else>
          <i class="fa fa-spinner fa-spin"></i>
          登录中...
        </span>
      </button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'LoginForm',
  data() {
    return {
      login: {
        mobile: "",
        password: "",
      },
      loading: false,
      rememberMe: false
    }
  },
  methods: {
    submit() {
      if (this.login.mobile && this.login.password) {
        this.loading = true
        this.$api.common.login(this.login).then(() => {
          window.location.replace('/');
        }).catch(() => {
          this.loading = false
        });
      } else {
        this.$Message.error('请输入手机号和密码');
      }
    }
  }
}
</script>

<style lang="less" scoped>
	@import "../../styles/login-form.less";
</style>
