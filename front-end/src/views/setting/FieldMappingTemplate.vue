<template>
  <div class="field-mapping-template-vue">
    <app-content>
      <!-- 页面头部 -->
      <div class="page-header">
        <h2>单据模版管理</h2>
        <p class="page-description">管理OCR字段映射模版，提高识别准确性和效率</p>
      </div>

      <!-- 搜索和操作区域 -->
      <div class="search-section">
        <div class="search-form">
          <div class="search-item">
            <label>模版名称：</label>
            <input v-model="searchForm.templateName" placeholder="请输入模版名称" class="h-input">
          </div>
          <div class="search-item">
            <label>单据类型：</label>
            <h-select v-model="searchForm.documentType" placeholder="请选择单据类型" clearable :datas="documentTypeOptions"></h-select>
          </div>
          <div class="search-item">
            <label>银行标识：</label>
            <input v-model="searchForm.bankIdentifier" placeholder="请输入银行标识" class="h-input">
          </div>
          <div class="search-item">
            <label>状态：</label>
            <h-select v-model="searchForm.isActive" placeholder="请选择状态" clearable :datas="statusOptions"></h-select>
          </div>
          <div class="search-buttons">
            <h-button type="primary" @click="loadTemplates">搜索</h-button>
            <h-button @click="resetSearch">重置</h-button>
          </div>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <div class="table-header">
          <div class="table-title">模版列表</div>
          <div class="table-actions">
            <h-button type="primary" @click="showCreateModal">新增模版</h-button>
            <h-button type="danger" @click="batchDelete" :disabled="getSelectedIds().length === 0">批量删除</h-button>
          </div>
        </div>

        <table class="header">
          <tr>
            <th style="width: 50px"><input :checked="checkAll" type="checkbox" @click="checkAll=!checkAll"></th>
            <th style="width: 250px">模版名称</th>
            <th style="width: 120px">单据类型</th>
            <th style="width: 150px">银行标识</th>
            <th style="width: 150px">回单类型</th>
            <th style="width: 120px">成功率</th>
            <th style="width: 80px">状态</th>
            <th style="width: 150px">创建时间</th>
            <th style="width: 200px">操作</th>
          </tr>
        </table>
        <table v-if="!templateList.length">
          <tr>
            <td colspan="9" class="text-center padding">暂无数据</td>
          </tr>
        </table>
        <table class="details" v-for="template in templateList" :key="template.id">
          <tr>
            <td style="width: 50px"><input v-model="template._checked" type="checkbox"></td>
            <td style="width: 250px" class="text-ellipsis" :title="template.templateName">{{ template.templateName }}</td>
            <td style="width: 120px">
              <span class="document-type-tag" :class="template.documentType">
                {{ getDocumentTypeText(template.documentType) }}
              </span>
            </td>
            <td style="width: 150px" class="text-ellipsis" :title="template.bankIdentifier">{{ template.bankIdentifier || '-' }}</td>
            <td style="width: 150px" class="text-ellipsis" :title="template.receiptType">{{ template.receiptType || '-' }}</td>
            <td style="width: 120px">
              <div class="success-rate">
                <div class="rate-bar">
                  <div class="rate-fill" :style="{ width: template.successRate + '%' }"></div>
                </div>
                <span class="rate-text">{{ template.successRate }}%</span>
              </div>
            </td>
            <td style="width: 80px">
              <span class="status-tag" :class="template.isActive ? 'active' : 'inactive'">
                {{ template.isActive ? '启用' : '禁用' }}
              </span>
            </td>
            <td style="width: 150px">{{ template.createdTime }}</td>
            <td style="width: 200px" class="actions">
              <span @click="viewTemplate(template)">查看</span>
              <span @click="editTemplate(template)">编辑</span>
              <span @click="toggleStatus(template)">{{ template.isActive ? '禁用' : '启用' }}</span>
              <span @click="deleteTemplate(template)">删除</span>
            </td>
          </tr>
        </table>

        <!-- 分页 -->
        <div class="pagination-section">
          <Pagination 
            v-model="pagination" 
            @change="loadTemplates"
            :total="total"
          />
        </div>
      </div>

      <!-- 创建/编辑模版弹窗 -->
      <Modal 
        v-model="showModal" 
        :title="modalTitle"
        :width="800"
        @ok="saveTemplate"
        @cancel="cancelModal"
      >
        <Form :model="templateForm" :rules="formRules" ref="templateForm" :labelWidth="120">
          <FormItem label="模版名称" prop="templateName">
            <input v-model="templateForm.templateName" placeholder="请输入模版名称" class="h-input">
          </FormItem>
          
          <FormItem label="单据类型" prop="documentType">
            <h-select v-model="templateForm.documentType" placeholder="请选择单据类型" :datas="documentTypeOptions"></h-select>
          </FormItem>
          
          <FormItem label="银行标识" prop="bankIdentifier">
            <input v-model="templateForm.bankIdentifier" placeholder="请输入银行标识" class="h-input">
          </FormItem>
          
          <FormItem label="回单类型" prop="receiptType">
            <input v-model="templateForm.receiptType" placeholder="请输入回单类型" class="h-input">
          </FormItem>
          
          <FormItem label="字段映射规则" prop="mappingRules">
            <textarea 
              v-model="templateForm.mappingRules" 
              placeholder="请输入JSON格式的字段映射规则"
              rows="8"
              class="h-textarea"
            ></textarea>
          </FormItem>
          
          <FormItem label="状态" prop="isActive">
            <h-switch v-model="templateForm.isActive"></h-switch>
            <span class="form-help">启用后该模版将用于字段映射</span>
          </FormItem>
        </Form>
      </Modal>

      <!-- 查看模版详情弹窗 -->
      <Modal 
        v-model="showViewModal" 
        title="模版详情"
        :width="900"
        :hasFooter="false"
      >
        <div class="template-detail" v-if="viewingTemplate">
          <div class="detail-section">
            <h3>基本信息</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <label>模版名称：</label>
                <span>{{ viewingTemplate.templateName }}</span>
              </div>
              <div class="detail-item">
                <label>单据类型：</label>
                <span>{{ getDocumentTypeText(viewingTemplate.documentType) }}</span>
              </div>
              <div class="detail-item">
                <label>银行标识：</label>
                <span>{{ viewingTemplate.bankIdentifier || '-' }}</span>
              </div>
              <div class="detail-item">
                <label>回单类型：</label>
                <span>{{ viewingTemplate.receiptType || '-' }}</span>
              </div>
              <div class="detail-item">
                <label>字段数量：</label>
                <span>{{ viewingTemplate.fieldCount }}</span>
              </div>
              <div class="detail-item">
                <label>使用次数：</label>
                <span>{{ viewingTemplate.usageCount }}</span>
              </div>
              <div class="detail-item">
                <label>成功率：</label>
                <span>{{ viewingTemplate.successRate }}%</span>
              </div>
              <div class="detail-item">
                <label>状态：</label>
                <span class="status-tag" :class="viewingTemplate.isActive ? 'active' : 'inactive'">
                  {{ viewingTemplate.isActive ? '启用' : '禁用' }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="detail-section">
            <h3>字段映射规则</h3>
            <pre class="mapping-rules">{{ formatMappingRules(viewingTemplate.mappingRules) }}</pre>
          </div>

          <div class="detail-section" v-if="viewingTemplate.sampleOcrData">
            <h3>样本OCR数据</h3>
            <pre class="sample-data">{{ formatSampleData(viewingTemplate.sampleOcrData) }}</pre>
          </div>
        </div>
      </Modal>
    </app-content>
  </div>
</template>

<script>
export default {
  name: 'FieldMappingTemplate',
  data() {
    return {
      loading: false,
      templateList: [],
      total: 0,
      selectedIds: [],
      checkAll: false,
      
      // 搜索表单
      searchForm: {
        templateName: '',
        documentType: '',
        bankIdentifier: '',
        isActive: null
      },
      
      // 分页
      pagination: {
        page: 1,
        size: 20
      },
      
      // 弹窗相关
      showModal: false,
      showViewModal: false,
      modalTitle: '',
      isEdit: false,
      viewingTemplate: null,
      
      // 表单数据
      templateForm: {
        id: null,
        templateName: '',
        documentType: '',
        bankIdentifier: '',
        receiptType: '',
        mappingRules: '',
        isActive: true
      },
      

      
      // 表单验证规则
      formRules: {
        templateName: { required: true, message: '请输入模版名称' },
        documentType: { required: true, message: '请选择单据类型' },
        mappingRules: { required: true, message: '请输入字段映射规则' }
      },

      // 下拉选项数据
      documentTypeOptions: [
        { key: 'BANK_RECEIPT', title: '银行回单' },
        { key: 'INVOICE', title: '发票' }
      ],
      statusOptions: [
        { key: true, title: '启用' },
        { key: false, title: '禁用' }
      ]
    }
  },

  watch: {
    checkAll(nval) {
      let data = Array.from(this.templateList);
      data.forEach(val => val._checked = nval);
      this.templateList = data;
    }
  },

  mounted() {
    this.loadTemplates()
  },
  
  methods: {
    // 加载模版列表
    async loadTemplates() {
      try {
        this.loading = true
        // 过滤空值参数
        const params = {}
        Object.keys(this.searchForm).forEach(key => {
          if (this.searchForm[key] !== '' && this.searchForm[key] !== null && this.searchForm[key] !== undefined) {
            params[key] = this.searchForm[key]
          }
        })
        params.page = this.pagination.page
        params.size = this.pagination.size
        
        const response = await this.$api.fieldMappingTemplate.getList(params)
        if (response.success) {
          this.templateList = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.$Message.error(response.message || '加载模版列表失败')
        }
      } catch (error) {
        console.error('加载模版列表失败:', error)
        this.$Message.error('加载模版列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        templateName: '',
        documentType: '',
        bankIdentifier: '',
        isActive: null
      }
      this.pagination.page = 1
      this.loadTemplates()
    },
    
    // 显示创建弹窗
    showCreateModal() {
      this.isEdit = false
      this.modalTitle = '新增模版'
      this.templateForm = {
        id: null,
        templateName: '',
        documentType: '',
        bankIdentifier: '',
        receiptType: '',
        mappingRules: '',
        isActive: true
      }
      this.showModal = true
    },
    
    // 编辑模版
    editTemplate(template) {
      this.isEdit = true
      this.modalTitle = '编辑模版'
      this.templateForm = { ...template }
      this.showModal = true
    },
    
    // 查看模版详情
    viewTemplate(template) {
      this.viewingTemplate = template
      this.showViewModal = true
    },
    
    // 保存模版
    async saveTemplate() {
      try {
        const valid = await this.$refs.templateForm.validate()
        if (!valid) return
        
        // 验证JSON格式
        try {
          JSON.parse(this.templateForm.mappingRules)
        } catch (e) {
          this.$Message.error('字段映射规则必须是有效的JSON格式')
          return
        }
        
        const api = this.isEdit ? 
          this.$api.fieldMappingTemplate.update : 
          this.$api.fieldMappingTemplate.create
          
        const response = await api(this.templateForm)
        if (response.success) {
          this.$Message.success(this.isEdit ? '更新成功' : '创建成功')
          this.showModal = false
          this.loadTemplates()
        } else {
          this.$Message.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('保存模版失败:', error)
        this.$Message.error('保存失败')
      }
    },
    
    // 取消弹窗
    cancelModal() {
      this.showModal = false
    },
    
    // 切换状态
    async toggleStatus(template) {
      try {
        const response = await this.$api.fieldMappingTemplate.toggleStatus(template.id)
        if (response.success) {
          this.$Message.success('状态更新成功')
          this.loadTemplates()
        } else {
          this.$Message.error(response.message || '状态更新失败')
        }
      } catch (error) {
        console.error('状态更新失败:', error)
        this.$Message.error('状态更新失败')
      }
    },
    
    // 删除模版
    deleteTemplate(template) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除模版"${template.templateName}"吗？`,
        onOk: async () => {
          try {
            const response = await this.$api.fieldMappingTemplate.delete(template.id)
            if (response.success) {
              this.$Message.success('删除成功')
              this.loadTemplates()
            } else {
              this.$Message.error(response.message || '删除失败')
            }
          } catch (error) {
            console.error('删除失败:', error)
            this.$Message.error('删除失败')
          }
        }
      })
    },
    
    // 批量删除
    batchDelete() {
      const selectedIds = this.getSelectedIds()
      if (selectedIds.length === 0) {
        this.$Message.warning('请选择要删除的模版')
        return
      }

      this.$Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的${selectedIds.length}个模版吗？`,
        onOk: async () => {
          try {
            const response = await this.$api.fieldMappingTemplate.batchDelete(selectedIds)
            if (response.success) {
              this.$Message.success('批量删除成功')
              this.checkAll = false
              this.loadTemplates()
            } else {
              this.$Message.error(response.message || '批量删除失败')
            }
          } catch (error) {
            console.error('批量删除失败:', error)
            this.$Message.error('批量删除失败')
          }
        }
      })
    },
    
    // 获取选中的模版IDs
    getSelectedIds() {
      return this.templateList.filter(item => item._checked).map(item => item.id)
    },
    
    // 获取单据类型文本
    getDocumentTypeText(type) {
      const typeMap = {
        'BANK_RECEIPT': '银行回单',
        'INVOICE': '发票'
      }
      return typeMap[type] || type
    },
    
    // 格式化映射规则
    formatMappingRules(rules) {
      try {
        return JSON.stringify(JSON.parse(rules), null, 2)
      } catch (e) {
        return rules
      }
    },
    
    // 格式化样本数据
    formatSampleData(data) {
      try {
        return JSON.stringify(JSON.parse(data), null, 2)
      } catch (e) {
        return data
      }
    }
  }
}
</script>

<style lang="less" scoped>
// 表格样式
.details {
  margin-bottom: 10px;
  table-layout: fixed;
  width: 100%;

  .actions {
    span {
      margin-right: 5px;
      color: #1890ff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  td, th {
    padding: 8px;
    text-align: left;
    word-wrap: break-word;
    overflow: hidden;
  }
}

.header {
  margin-bottom: 10px;
  background-color: #f8f8f8;
  table-layout: fixed;
  width: 100%;

  th, td {
    padding: 8px;
    text-align: left;
  }
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: help;
}

.text-ellipsis:hover {
  position: relative;
}

.text-ellipsis:hover::after {
  content: attr(title);
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: normal;
  word-wrap: break-word;
  max-width: 300px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.field-mapping-template-vue {
  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 500;
      color: #333;
    }

    .page-description {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .search-section {
    background: #fff;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);

    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: end;

      .search-item {
        display: flex;
        flex-direction: column;
        min-width: 200px;

        label {
          margin-bottom: 4px;
          font-size: 14px;
          color: #333;
        }
      }

      .search-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }

  .table-section {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;

      .table-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .table-actions {
        display: flex;
        gap: 8px;
      }
    }

    .pagination-section {
      padding: 20px;
      display: flex;
      justify-content: center;
    }
  }

  .document-type-tag {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;

    &.BANK_RECEIPT {
      background: #e6f7ff;
      color: #1890ff;
    }

    &.INVOICE {
      background: #f6ffed;
      color: #52c41a;
    }
  }

  .status-tag {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;

    &.active {
      background: #f6ffed;
      color: #52c41a;
    }

    &.inactive {
      background: #fff2e8;
      color: #fa8c16;
    }
  }

  .success-rate {
    display: flex;
    align-items: center;
    gap: 8px;

    .rate-bar {
      width: 60px;
      height: 6px;
      background: #f0f0f0;
      border-radius: 3px;
      overflow: hidden;

      .rate-fill {
        height: 100%;
        background: linear-gradient(90deg, #52c41a, #73d13d);
        transition: width 0.3s ease;
      }
    }

    .rate-text {
      font-size: 12px;
      color: #666;
      min-width: 35px;
    }
  }

  .template-detail {
    .detail-section {
      margin-bottom: 24px;

      h3 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 500;
        color: #333;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 8px;
      }

      .detail-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;

        .detail-item {
          display: flex;

          label {
            min-width: 100px;
            color: #666;
            font-weight: 500;
          }

          span {
            color: #333;
          }
        }
      }

      .mapping-rules,
      .sample-data {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 16px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
        color: #333;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 300px;
        overflow-y: auto;
      }
    }
  }

  .form-help {
    margin-left: 8px;
    font-size: 12px;
    color: #999;
  }
}
</style>
