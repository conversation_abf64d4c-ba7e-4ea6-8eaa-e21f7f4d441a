<template>
	<app-content class="h-panel">
		<Tabs :datas="tabDatas" v-model="selected" @change="change"></Tabs>
		<div class="padding-top padding-right text-right">
			<Button @click="downloadTemplate" :loading="downloadLoading" color="green">下载模板</Button>
			<Button :loading="importLoading" @click="doUpload">
				<input ref="file" type="file" accept=".xlsx,.xls,.csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv" style="display: none" @change="fileChange($event)">
				导入
			</Button>
			<Button @click="trialBalance">试算平衡</Button>
			<Button @click="checkBalance" color="blue">平衡检查</Button>
		</div>

		<!-- 期初余额汇总信息 -->
		<div class="padding balance-summary" v-if="balanceSummary">
			<Row>
				<Cell :width="6">
					<div class="summary-card">
						<h4>期初余额汇总</h4>
						<div class="summary-item">
							<span class="label">借方合计：</span>
							<span class="amount debit">{{balanceSummary.totalDebit | numFormat}}</span>
						</div>
						<div class="summary-item">
							<span class="label">贷方合计：</span>
							<span class="amount credit">{{balanceSummary.totalCredit | numFormat}}</span>
						</div>
						<div class="summary-item">
							<span class="label">差额：</span>
							<span class="amount" :class="{'error': balanceSummary.difference > 0}">{{balanceSummary.difference | numFormat}}</span>
						</div>
						<div class="summary-item">
							<span class="label">平衡状态：</span>
							<span class="status" :class="{'balanced': balanceSummary.isBalanced, 'unbalanced': !balanceSummary.isBalanced}">
								{{balanceSummary.isBalanced ? '平衡' : '不平衡'}}
							</span>
						</div>
					</div>
				</Cell>
				<Cell :width="6">
					<div class="summary-card">
						<h4>科目统计</h4>
						<div class="summary-item">
							<span class="label">科目总数：</span>
							<span class="count">{{balanceSummary.totalSubjects}}</span>
						</div>
						<div class="summary-item">
							<span class="label">末级科目：</span>
							<span class="count">{{balanceSummary.leafSubjects}}</span>
						</div>
						<div class="summary-item">
							<span class="label">有余额科目：</span>
							<span class="count">{{balanceSummary.subjectsWithBalance}}</span>
						</div>
					</div>
				</Cell>
			</Row>
		</div>

		<div class="h-panel-body padding">
			<table class="cus-table initial-balance-table">
				<thead class="header">
				<tr v-if="!isUnit">
					<td width="120">科目编码</td>
					<td nowrap>科目名称</td>
					<td width="80" nowrap>余额方向</td>
					<td width="150" nowrap>借方金额</td>
					<td width="150" nowrap>贷方金额</td>
					<td width="150" nowrap>期初余额</td>
					<td width="100" nowrap>操作</td>
				</tr>
				<template v-else>
					<tr>
						<td width="120" rowspan="2">科目编码</td>
						<td rowspan="2" nowrap>科目名称</td>
						<td width="80" rowspan="2" nowrap>余额方向</td>
						<td width="60" rowspan="2" nowrap>单位</td>
						<td colspan="2">期初余额</td>
						<td width="100" rowspan="2" nowrap>操作</td>
					</tr>
					<tr>
						<td width="120">数量</td>
						<td width="150">金额</td>
					</tr>
				</template>
				</thead>
				<tbody>
				<tr v-for="(item,i) in datas" :key="i" :class="{'parent-row': !item.leaf, 'leaf-row': item.leaf, 'has-balance': hasBalance(item)}">
					<td :style="{paddingLeft:(item.level!=1?(item.level-1)*20:10)+'px'}" class="code-cell">
						<span class="subject-code">{{item.code}}</span>
						<span v-if="!item.leaf" class="parent-indicator" title="父级科目">📁</span>
						<span v-else class="leaf-indicator" title="末级科目">📄</span>
					</td>
					<td nowrap class="name-cell">
						<span class="subject-name">{{item.name}}</span>
						<i @click="addDetails(item)" class="fa fa-plus text-hover auxiliary-btn" v-if="item.auxiliaryAccounting" v-tooltip content="点击+号<br/>添加辅助明细"></i>
						<span v-if="item.auxiliaryAccounting" class="auxiliary-tag">辅助</span>
					</td>
					<td class="text-center direction-cell">
						<span class="balance-direction" :class="{'debit-direction': item.balanceDirection === '借', 'credit-direction': item.balanceDirection === '贷'}">
							{{item.balanceDirection}}
						</span>
					</td>

					<!-- 数量列（仅在有单位时显示） -->
					<td class="text-center" v-if="isUnit" :class="{'edit-hover':(!item._numEdit && item.leaf && !item.auxiliaryAccounting && item.unit), 'readonly': !item.leaf}">
						<template v-if="item.unit && item.leaf && !item.auxiliaryAccounting">
							<div class="editblock quantity-edit-block" v-if="!item._numEdit" @dblclick="showInput($event,item,'_numEdit',i)">{{item.num|numFormat}}</div>
							<NumberInput :id="'_numEdit'+i" v-width="120" v-if="item._numEdit" v-model="item.num" @change="balanceChange(item,'_numEdit')"/>
						</template>
						<template v-else-if="!item.leaf">
							<span class="parent-quantity" title="父级科目数量由子科目自动汇总">{{item.num|numFormat}}</span>
						</template>
						<template v-else>
							<span class="readonly-quantity">{{item.num|numFormat}}</span>
						</template>
					</td>

					<!-- 借方金额列 -->
					<td class="text-right amount-cell" :class="{'edit-hover':(!item._debitEdit && item.leaf && !item.auxiliaryAccounting), 'readonly': !item.leaf}">
						<template v-if="item.leaf && !item.auxiliaryAccounting">
							<div class="editblock debit-edit-block" :data-code="item.code" v-if="!item._debitEdit" @dblclick="showInput($event,item,'_debitEdit',i)">
								{{getDebitAmount(item)|numFormat}}
							</div>
							<NumberInput :id="'_debitEdit'+i" v-width="130" v-if="item._debitEdit" v-model="item.debitAmount" @change="debitAmountChange(item,'_debitEdit')"/>
						</template>
						<template v-else-if="!item.leaf">
							<span class="parent-amount debit-amount" title="父级科目借方金额由子科目自动汇总">{{getDebitAmount(item)|numFormat}}</span>
						</template>
						<template v-else>
							<span class="readonly-amount debit-amount">{{getDebitAmount(item)|numFormat}}</span>
						</template>
					</td>

					<!-- 贷方金额列 -->
					<td class="text-right amount-cell" :class="{'edit-hover':(!item._creditEdit && item.leaf && !item.auxiliaryAccounting), 'readonly': !item.leaf}">
						<template v-if="item.leaf && !item.auxiliaryAccounting">
							<div class="editblock credit-edit-block" :data-code="item.code" v-if="!item._creditEdit" @dblclick="showInput($event,item,'_creditEdit',i)">
								{{getCreditAmount(item)|numFormat}}
							</div>
							<NumberInput :id="'_creditEdit'+i" v-width="130" v-if="item._creditEdit" v-model="item.creditAmount" @change="creditAmountChange(item,'_creditEdit')"/>
						</template>
						<template v-else-if="!item.leaf">
							<span class="parent-amount credit-amount" title="父级科目贷方金额由子科目自动汇总">{{getCreditAmount(item)|numFormat}}</span>
						</template>
						<template v-else>
							<span class="readonly-amount credit-amount">{{getCreditAmount(item)|numFormat}}</span>
						</template>
					</td>

					<!-- 期初余额列（计算得出） -->
					<td class="text-right balance-cell">
						<span class="initial-balance" :class="{'debit-balance': getInitialBalance(item) > 0, 'credit-balance': getInitialBalance(item) < 0, 'zero-balance': getInitialBalance(item) === 0}">
							{{getInitialBalance(item)|numFormat}}
						</span>
					</td>

					<!-- 操作列 -->
					<td class="text-center action-cell">
						<template v-if="item.leaf && !item.auxiliaryAccounting">
							<Button size="s" @click="clearBalance(item)" v-if="hasBalance(item)" title="清空余额">清空</Button>
							<Button size="s" @click="copyFromTemplate(item)" color="blue" title="从模板复制">模板</Button>
						</template>
						<template v-else-if="!item.leaf">
							<span class="parent-action" title="父级科目不可操作">-</span>
						</template>
						<template v-else>
							<span class="auxiliary-action" title="辅助核算科目请点击+号添加明细">辅助</span>
						</template>
					</td>
				</tr>
				<tr v-if="!datas.length">
					<td :colspan="isUnit ? 7 : 6" class="text-center padding no-data">暂无数据</td>
				</tr>
				</tbody>
			</table>
		</div>
		<Modal v-model="showModal" hasCloseIcon hasDivider>
			<div slot="header">期初试算结果</div>
			<Row class="result">
				<Cell :width="12" class="text-center" style="border-right: 1px solid #eeeeee">
					<div class="font-bold margin">期初余额</div>
					<div class="pic-body beginningBalance" :class="{left:beginningBalance.借<beginningBalance.贷,right:beginningBalance.借>beginningBalance.贷}">
						<span class="span1">{{beginningBalance.借|numFormat}}</span>
						<span class="span2">{{beginningBalance.借-beginningBalance.贷|numFormat}}</span>
						<span class="span3">{{beginningBalance.贷|numFormat}}</span>
						<div class="pic"></div>
					</div>
				</Cell>
				<Cell :width="12" class="text-center">
					<div class="font-bold margin">资产负债表期初</div>
					<div class="pic-body liabilities" :class="{left:liabilities.资产<liabilities.权益,right:liabilities.资产>liabilities.权益}">
						<span class="span1">{{liabilities.资产|numFormat}}</span>
						<span class="span2">{{liabilities.资产-liabilities.权益|numFormat}}</span>
						<span class="span3">{{liabilities.权益|numFormat}}</span>
						<div class="pic "></div>
					</div>
				</Cell>
			</Row>
		</Modal>
		<Modal v-model="showDetailModal" hasCloseIcon hasDivider>
			<div slot="header">增加明细</div>
			<Form v-width="400" :labelWidth="60" class="padding-right margin-top">
				<FormItem label="科目">
					<Select @change="subjectChange" v-model="auxiliary.subjectId" :datas="auxiliarys" :deletable="false" titleName="name" keyName="subjectId"></Select>
				</FormItem>
				<FormItem v-for="item in auxiliaryAccounting" :label="item.name" :key="item.id">
					<Select v-model="auxiliary.auxiliary[item.id]" :datas="auxiliaryAccountingData[auxiliary.subjectId]?auxiliaryAccountingData[auxiliary.subjectId][item.id]:[]" :deletable="false" titleName="name" keyName="id">
						<template slot-scope="{item}" slot="item">{{item.code}}-{{item.name}}</template>
					</Select>
				</FormItem>
			</Form>
			<div class="text-center margin-top">
				<Button color="green" @click="saveAuxiliary" :loading="loading">增加</Button>
				<Button @click="showDetailModal=false">取消</Button>
			</div>
		</Modal>
	</app-content>
</template>

<script>
	import {mapState} from 'vuex';
	import jQuery from 'jquery';

	const emptyForm = {
		"id": "0",
		"code": "",
		"name": "",
		"mnemonicCode": "",
		"balanceDirection": "",
		"status": "0",
		"parentId": "0"
	};

	export default {
		name: "Initial",
		data() {
			return {
				tabDatas: [
					{
						title: '资产',
						key: 'CodeAssets'
					}, {
						title: '负债',
						key: 'CodeLiabilities'
					}, {
						title: '权益',
						key: 'CodeInterest'
					}, {
						title: '成本',
						key: 'CodeCost'
					}, {
						title: '损益',
						key: 'CodeLoss'
					}],
				type: '资产',
				datas: [],
				form: Object.assign({}, emptyForm),
				showForm: false,
				showDetailModal: false,
				selected: 'CodeAssets',
				showModal: false,
				loading: false,
				downloadLoading: false,
				importLoading: false,
				temps: {},
				auxiliary: {subjectId: "", auxiliary: {}},
				auxiliaryAccounting: [],
				auxiliaryAccountingData: {},
				beginningBalance: {借: 0, 贷: 0},
				liabilities: {权益: 0, 资产: 0},
				balanceSummary: null, // 期初余额汇总信息
			};
		},
		computed: {
			...mapState(['currentAccountSets']),
			isUnit() {
				return this.datas.filter(value => value.unit).length > 0;
			},
			auxiliarys() {
				return this.datas.filter(value => value.auxiliaryAccounting)
			}
		},
		watch: {
			type() {
				this.loadList();
			}
		},
		methods: {
			showInput(e, item, type, i) {
				this.$set(item, type, true);
				this.$nextTick(() => {
					jQuery('.h-input', '#' + type + i).focus().select()
				});
			},
			// 获取借方金额
			getDebitAmount(item) {
				return item.debitAmount || 0;
			},
			// 获取贷方金额
			getCreditAmount(item) {
				return item.creditAmount || 0;
			},
			// 计算期初余额
			getInitialBalance(item) {
				const debit = this.getDebitAmount(item);
				const credit = this.getCreditAmount(item);
				if (item.balanceDirection === '借') {
					return debit - credit;
				} else {
					return credit - debit;
				}
			},
			// 检查是否有余额
			hasBalance(item) {
				return this.getDebitAmount(item) > 0 || this.getCreditAmount(item) > 0;
			},
			// 清空余额
			clearBalance(item) {
				this.$Modal.confirm({
					title: '确认清空',
					content: `确定要清空科目 ${item.code}-${item.name} 的期初余额吗？`,
					onOk: () => {
						item.debitAmount = 0;
						item.creditAmount = 0;
						this.saveBalance(item);
					}
				});
			},
			// 从模板复制
			copyFromTemplate(item) {
				// TODO: 实现从模板复制功能
				this.$Message.info('模板复制功能开发中...');
			},
			// 借方金额变更
			debitAmountChange(item, target) {
				// 如果输入借方金额，清空贷方金额
				if (item.debitAmount && item.debitAmount > 0) {
					item.creditAmount = 0;
				}
				this.saveBalance(item);
				this.$set(item, target, false);
				this.updateBalanceSummary();
			},
			// 贷方金额变更
			creditAmountChange(item, target) {
				// 如果输入贷方金额，清空借方金额
				if (item.creditAmount && item.creditAmount > 0) {
					item.debitAmount = 0;
				}
				this.saveBalance(item);
				this.$set(item, target, false);
				this.updateBalanceSummary();
			},
			// 保存余额
			saveBalance(item) {
				let params = {
					summary: "期初",
					subjectName: `${item.code}-${item.name}`,
					subjectCode: item.code,
					subjectId: item.subjectId,
					accountSetsId: item.accountSetsId,
					debitAmount: item.debitAmount || null,
					creditAmount: item.creditAmount || null,
					num: item.num || null
				};

				this.$api.setting.initialBalance.save(params).then(() => {
					this.$Message.success('保存成功');
					this.updateBalanceSummary();
				}).catch(error => {
					this.$Message.error('保存失败：' + (error.message || '未知错误'));
				});
			},
			// 更新余额汇总
			updateBalanceSummary() {
				let totalDebit = 0;
				let totalCredit = 0;
				let totalSubjects = this.datas.length;
				let leafSubjects = 0;
				let subjectsWithBalance = 0;

				this.datas.forEach(item => {
					if (item.leaf) {
						leafSubjects++;
					}

					const debit = this.getDebitAmount(item);
					const credit = this.getCreditAmount(item);

					if (debit > 0 || credit > 0) {
						subjectsWithBalance++;
					}

					totalDebit += debit;
					totalCredit += credit;
				});

				const difference = Math.abs(totalDebit - totalCredit);
				const isBalanced = difference < 0.01; // 允许0.01的误差

				this.balanceSummary = {
					totalDebit,
					totalCredit,
					difference,
					isBalanced,
					totalSubjects,
					leafSubjects,
					subjectsWithBalance
				};
			},
			// 平衡检查
			checkBalance() {
				this.$api.setting.checkOut.detailedInitialCheck().then(({data}) => {
					this.$Modal.info({
						title: '期初平衡检查结果',
						width: 600,
						render: (h) => {
							return h('div', [
								h('div', {style: 'margin-bottom: 15px;'}, [
									h('strong', '基本信息：'),
									h('div', `借方总额：${this.$options.filters.numFormat(data.totalDebitAmount)}`),
									h('div', `贷方总额：${this.$options.filters.numFormat(data.totalCreditAmount)}`),
									h('div', `差额：${this.$options.filters.numFormat(data.difference)}`),
									h('div', {
										style: data.isBalanced ? 'color: green;' : 'color: red;'
									}, `状态：${data.isBalanced ? '平衡' : '不平衡'}`)
								]),
								data.report ? h('div', [
									h('strong', '详细报告：'),
									h('pre', {style: 'white-space: pre-wrap; font-size: 12px; margin-top: 10px;'}, data.report)
								]) : null
							]);
						}
					});
				}).catch(error => {
					this.$Message.error('检查失败：' + (error.message || '未知错误'));
				});
			},
			addDetails(item) {
				this.auxiliary.subjectId = item.subjectId;
				this.subjectChange(item);
				this.showDetailModal = true;
			},
			addTemp(data) {
				this.temps[data.id] = data;
				return data.id;
			},
			loadList() {
				this.loading = true;
				this.$api.setting.initialBalance.list(this.type).then(({data}) => {
					// 处理数据，确保每个科目都有必要的字段
					this.datas = data.map(item => ({
						...item,
						debitAmount: item.debitAmount || 0,
						creditAmount: item.creditAmount || 0,
						num: item.num || 0
					}));
					this.loading = false;

					this.$nextTick(() => {
						this.calParent();
						this.updateBalanceSummary();
					});
				}).catch(error => {
					this.loading = false;
					this.$Message.error('加载数据失败：' + (error.message || '未知错误'));
				});
			},
			calParent() {
				let cals = {'balance': 'beginBalance', 'debit': 'cumulativeDebit', 'credit': 'cumulativeCredit'};
				Object.keys(cals).forEach(key => {
					jQuery(`span.${key}`).each((i, item) => {
						let {code, index} = item.dataset;
						let total = 0;
						jQuery(`.${key}-edit-block[data-code^=${code}]`).each((i, numItem) => {
							if (numItem.dataset.num) {
								total += Number(numItem.dataset.num)
							}
						});
						this.datas[index][cals[key]] = total;
					});
				});

				this.datas = Array.from(this.datas);
			},
			change(data) {
				this.type = data.title;
			},
			balanceChange(balance, target) {
				// 这个方法主要用于数量编辑
				let params = {
					summary: "期初",
					subjectName: `${balance.code}-${balance.name}`,
					subjectCode: balance.code,
					subjectId: balance.subjectId,
					accountSetsId: balance.accountSetsId,
					debitAmount: balance.debitAmount || null,
					creditAmount: balance.creditAmount || null,
					num: balance.num || null
				};

				this.$api.setting.initialBalance.save(params).then(({data}) => {
					this.$set(balance, target, false);
					this.$nextTick(() => {
						this.calParent();
						this.updateBalanceSummary();
					});
					this.$Message.success('保存成功');
				}).catch(error => {
					this.$Message.error('保存失败：' + (error.message || '未知错误'));
				});
			},
			trialBalance() {
				this.$api.setting.initialBalance.trialBalance().then(({data}) => {
					this.showModal = true;
					this.beginningBalance = Object.assign(this.beginningBalance, data.beginningBalance);
					this.liabilities = Object.assign(this.liabilities, data.liabilities);
				});
			},
			subjectChange(subject) {
				this.auxiliaryAccounting = JSON.parse(subject.auxiliaryAccounting);
				this.loadAuxiliaryAccountingData(subject);
			},
			loadAuxiliaryAccountingData(subject) {
				if (!this.auxiliaryAccountingData[subject.subjectId]) {
					this.$api.setting.accountingCategoryDetails.loadAuxiliaryAccountingData(this.auxiliaryAccounting).then(({data}) => {
						this.$set(this.auxiliaryAccountingData, subject.subjectId, data);
					});
				}
			},
			saveAuxiliary() {
				if (this.auxiliary.subjectId && Object.keys(this.auxiliary.auxiliary).length === this.auxiliaryAccounting.length) {
					this.loading = true;
					this.$api.setting.initialBalance.saveAuxiliary(this.auxiliary).then(({data}) => {
						this.loadList();
						this.showDetailModal = false;
					}).finally(() => {
						this.loading = false;
					});
				}
			},
			downloadTemplate() {
				this.downloadLoading = true;
				console.log('开始下载模板...');

				try {
					const downloadUrl = '/api/initial-balance/template';
					console.log('下载URL:', downloadUrl);

					// 使用window.open在新窗口中打开下载链接
					const downloadWindow = window.open(downloadUrl, '_blank');

					// 检查是否成功打开窗口
					if (downloadWindow) {
						console.log('下载窗口已打开');
						this.$Message("模板下载已开始");

						// 短暂延迟后关闭窗口（如果是下载，窗口会自动关闭）
						setTimeout(() => {
							try {
								downloadWindow.close();
							} catch (e) {
								// 忽略关闭窗口的错误
							}
						}, 2000);
					} else {
						throw new Error('无法打开下载窗口，可能被浏览器阻止');
					}

				} catch (error) {
					console.error('下载模板失败:', error);
					this.$Message.error("模板下载失败: " + error.message);
				} finally {
					console.log('模板下载完成，重置loading状态');
					this.downloadLoading = false;
				}
			},
			doUpload() {
				this.$refs.file.click();
			},
			fileChange(e) {
				if (this.$refs.file.files.length) {
					let formData = new FormData();
					formData.append('file', this.$refs.file.files[0]);
					this.importLoading = true;
					this.$api.setting.initialBalance.import(formData).then(({data}) => {
						this.$Message("期初余额导入成功~");
						this.loadList();
					}).catch((error) => {
						this.$Message.error("导入失败: " + (error.message || "未知错误"));
					}).finally(() => {
						this.importLoading = false;
					});
					this.$refs.file.value = "";
				}
			}
		},
		mounted() {
			if (this.currentAccountSets.accountingStandards !== 0) {
				this.tabDatas = [{
					title: '资产',
					key: 'CodeAssets'
				}, {
					title: '负债',
					key: 'CodeLiabilities'
				}, {
					title: '共同',
					key: 'Common'
				}, {
					title: '权益',
					key: 'CodeInterest'
				}, {
					title: '成本',
					key: 'CodeCost'
				}, {
					title: '损益',
					key: 'CodeLoss'
				}];
			}
			this.loadList();
		}
	};
</script>
<style lang="less" scoped>
	// 隐藏借方累计、贷方累计和年初余额列
	.cus-table {
		// 隐藏表头中的借方累计、贷方累计、年初余额列
		thead tr td:nth-child(5),
		thead tr td:nth-child(6),
		thead tr td:nth-child(7) {
			display: none;
		}
		
		// 隐藏数据行中对应的列
		tbody tr td:nth-child(5),
		tbody tr td:nth-child(6),
		tbody tr td:nth-child(7) {
			display: none;
		}
		
		// 对于有单位的情况，需要隐藏更多列
		// 当isUnit为true时，表格结构会有所不同
		// 隐藏借方累计数量、借方累计金额、贷方累计数量、贷方累计金额、年初余额数量、年初余额金额
		thead tr:nth-child(2) td:nth-child(3),
		thead tr:nth-child(2) td:nth-child(4),
		thead tr:nth-child(2) td:nth-child(5),
		thead tr:nth-child(2) td:nth-child(6),
		thead tr:nth-child(2) td:nth-child(7),
		thead tr:nth-child(2) td:nth-child(8) {
			display: none;
		}
		
		// 隐藏有单位情况下数据行的对应列
		tbody tr td:nth-child(7),
		tbody tr td:nth-child(8),
		tbody tr td:nth-child(9),
		tbody tr td:nth-child(10),
		tbody tr td:nth-child(11),
		tbody tr td:nth-child(12) {
			display: none;
		}
	}

	.result {
		width: 500px;
		height: 250px;

		.pic {
			width: 238px;
			height: 120px;
			background-image: url("../../assets/settings.png");
			background-repeat: no-repeat;
			margin: 20px auto;
		}


		.pic-body {
			margin-top: 50px;
			position: relative;

			&.beginningBalance {
				.pic {
					background-position: -160px 0px;
				}

				span {
					position: absolute;
				}

				.span1 {
					top: 0;
					left: 22px;
				}

				.span2 {
					top: 50px;
					left: 0;
					width: 100%;
				}

				.span3 {
					top: 0;
					right: 10px;
				}

				&.right {
					.pic {
						height: 130px;
						background-position: -160px -132px;
					}

					.span1 {
						top: 25px;
					}

					.span3 {
						top: -25px;
					}
				}

				&.left {
					.pic {
						height: 130px;
						background-position: -160px -272px;
					}

					.span1 {
						top: -25px;
					}

					.span3 {
						top: 25px;
					}
				}
			}

			&.liabilities {
				.pic {
					background-position: -420px 5px;
				}

				span {
					position: absolute;
				}

				.span1 {
					top: 0;
					left: 22px;
				}

				.span2 {
					top: 50px;
					left: 0;
					width: 100%;
				}

				.span3 {
					top: 0;
					right: 10px;
				}

				&.right {
					.pic {
						height: 130px;
						background-position: -420px -132px;
					}

					.span1 {
						top: 25px;
					}

					.span3 {
						top: -25px;
					}
				}

				&.left {
					.pic {
						height: 130px;
						background-position: -420px -272px;
					}

					.span1 {
						top: -25px;
					}

					.span3 {
						top: 25px;
					}
				}
			}
		}
	}

	.editblock {
		width: 100%;
		height: 100%;
		line-height: 33px;
	}

	/* 期初余额页面样式 */
	.initial-balance-table {
		.parent-row {
			background-color: #f8f9fa;
			font-weight: 500;
		}

		.leaf-row {
			&.has-balance {
				background-color: #fff3cd;
			}
		}

		.code-cell {
			.subject-code {
				font-family: 'Courier New', monospace;
				font-weight: 500;
			}

			.parent-indicator, .leaf-indicator {
				margin-left: 5px;
				font-size: 12px;
			}
		}

		.name-cell {
			.subject-name {
				font-weight: 500;
			}

			.auxiliary-btn {
				margin-left: 8px;
				color: #007bff;
				cursor: pointer;

				&:hover {
					color: #0056b3;
				}
			}

			.auxiliary-tag {
				background-color: #17a2b8;
				color: white;
				font-size: 10px;
				padding: 2px 4px;
				border-radius: 2px;
				margin-left: 5px;
			}
		}

		.direction-cell {
			.balance-direction {
				padding: 2px 8px;
				border-radius: 3px;
				font-weight: 500;

				&.debit-direction {
					background-color: #d4edda;
					color: #155724;
				}

				&.credit-direction {
					background-color: #f8d7da;
					color: #721c24;
				}
			}
		}

		.amount-cell {
			.editblock {
				min-height: 24px;
				padding: 4px 8px;
				border: 1px solid transparent;
				border-radius: 3px;

				&:hover {
					border-color: #007bff;
					background-color: #f8f9fa;
				}
			}

			.debit-edit-block {
				background-color: #d4edda;
			}

			.credit-edit-block {
				background-color: #f8d7da;
			}

			.parent-amount {
				color: #6c757d;
				font-style: italic;
				cursor: help;
			}

			.readonly-amount {
				color: #6c757d;
			}
		}

		.balance-cell {
			.initial-balance {
				font-weight: 600;
				padding: 4px 8px;
				border-radius: 3px;

				&.debit-balance {
					background-color: #d4edda;
					color: #155724;
				}

				&.credit-balance {
					background-color: #f8d7da;
					color: #721c24;
				}

				&.zero-balance {
					color: #6c757d;
				}
			}
		}

		.action-cell {
			.parent-action, .auxiliary-action {
				color: #6c757d;
				font-style: italic;
			}
		}

		.readonly {
			background-color: #f8f9fa;
			color: #6c757d;
		}
	}

	/* 余额汇总卡片样式 */
	.balance-summary {
		background-color: #f8f9fa;
		border-radius: 6px;
		margin-bottom: 15px;

		.summary-card {
			background: white;
			border-radius: 6px;
			padding: 15px;
			box-shadow: 0 2px 4px rgba(0,0,0,0.1);

			h4 {
				margin: 0 0 15px 0;
				color: #333;
				font-size: 16px;
				border-bottom: 2px solid #007bff;
				padding-bottom: 5px;
			}

			.summary-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 8px;

				.label {
					font-weight: 500;
					color: #555;
				}

				.amount {
					font-family: 'Courier New', monospace;
					font-weight: 600;

					&.debit {
						color: #155724;
					}

					&.credit {
						color: #721c24;
					}

					&.error {
						color: #dc3545;
					}
				}

				.count {
					font-weight: 600;
					color: #007bff;
				}

				.status {
					font-weight: 600;
					padding: 2px 8px;
					border-radius: 3px;

					&.balanced {
						background-color: #d4edda;
						color: #155724;
					}

					&.unbalanced {
						background-color: #f8d7da;
						color: #721c24;
					}
				}
			}
		}
	}

	.edit-hover:hover {
		cursor: pointer;
		background-color: @gray2-color;
	}
</style>
