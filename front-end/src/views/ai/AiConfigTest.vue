<template>
  <div class="ai-config-test">
    <h-card>
      <h2 slot="title">🧪 AI配置测试页面</h2>
      
      <div class="test-section">
        <h3>基础输入测试</h3>
        
        <div class="form-group">
          <label>启用AI功能:</label>
          <h-switch v-model="testConfig.enabled"></h-switch>
          <span>当前值: {{ testConfig.enabled }}</span>
        </div>
        
        <div class="form-group">
          <label>API地址:</label>
          <h-input 
            v-model="testConfig.baseUrl" 
            placeholder="请输入API地址" 
            style="width: 400px;">
          </h-input>
          <span>当前值: {{ testConfig.baseUrl }}</span>
        </div>
        
        <div class="form-group">
          <label>API密钥:</label>
          <h-input 
            v-model="testConfig.apiKey" 
            type="password" 
            placeholder="请输入API密钥" 
            style="width: 400px;">
          </h-input>
          <span>当前值: {{ testConfig.apiKey ? '***已设置***' : '未设置' }}</span>
        </div>
        
        <div class="form-group">
          <label>普通文本:</label>
          <input 
            v-model="testConfig.normalInput" 
            type="text" 
            placeholder="原生input测试" 
            style="width: 400px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
          <span>当前值: {{ testConfig.normalInput }}</span>
        </div>
        
        <div class="actions">
          <h-button type="primary" @click="showConfig">显示配置</h-button>
          <h-button @click="clearConfig">清空配置</h-button>
        </div>
        
        <div class="config-display" v-if="showConfigData">
          <h4>当前配置数据:</h4>
          <pre>{{ JSON.stringify(testConfig, null, 2) }}</pre>
        </div>
      </div>
    </h-card>
  </div>
</template>

<script>
export default {
  name: 'AiConfigTest',
  data() {
    return {
      testConfig: {
        enabled: false,
        baseUrl: '',
        apiKey: '',
        normalInput: ''
      },
      showConfigData: false
    }
  },
  
  methods: {
    showConfig() {
      this.showConfigData = true
      console.log('当前配置:', this.testConfig)
    },
    
    clearConfig() {
      this.testConfig = {
        enabled: false,
        baseUrl: '',
        apiKey: '',
        normalInput: ''
      }
      this.showConfigData = false
    }
  }
}
</script>

<style lang="less" scoped>
.ai-config-test {
  padding: 20px;
}

.test-section {
  padding: 20px;
}

.form-group {
  margin: 20px 0;
  display: flex;
  align-items: center;
  gap: 15px;
}

.form-group label {
  min-width: 100px;
  font-weight: bold;
}

.form-group span {
  color: #666;
  font-size: 12px;
}

.actions {
  margin: 30px 0;
}

.actions .h-btn {
  margin-right: 10px;
}

.config-display {
  margin: 20px 0;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 4px;
}

.config-display pre {
  margin: 0;
  font-size: 12px;
}
</style>
