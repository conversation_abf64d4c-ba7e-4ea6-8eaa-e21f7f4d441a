<template>
  <div class="ai-config-simple">
    <div class="config-card">
      <h2>🤖 AI智能助手配置</h2>
      <p class="subtitle">配置OpenAI兼容的API接口，启用AI智能功能</p>
      
      <!-- 配置说明 -->
      <div class="config-help">
        <h3>📋 配置说明</h3>
        <div class="help-content">
          <p><strong>常用服务商配置示例：</strong></p>
          <ul>
            <li><strong>DeepSeek：</strong> https://api.deepseek.com/v1</li>
            <li><strong>行业智能体：</strong> https://dashscope.aliyuncs.com/compatible-mode/v1</li>
            <li><strong>公司专属智能体：</strong> https://api.openai.com/v1</li>
          </ul>
          <p><strong>API密钥格式：</strong> 通常以 sk- 开头，请从对应服务商官网获取</p>
        </div>
      </div>

      <!-- AI服务状态 -->
      <div class="ai-status" :class="aiStatus.connected ? 'connected' : 'disconnected'">
        <div class="status-icon">
          {{ aiStatus.connected ? '✅' : '❌' }}
        </div>
        <div class="status-content">
          <h4>{{ aiStatus.connected ? 'AI服务已连接' : 'AI服务未连接' }}</h4>
          <p>{{ aiStatus.message }}</p>
        </div>
      </div>
      
      <!-- 配置表单 -->
      <div class="config-form">
        <h3>🛠️ AI配置</h3>
        
        <div class="form-group">
          <label class="form-label">启用AI功能:</label>
          <label class="switch">
            <input type="checkbox" v-model="config.enabled" @change="onEnabledChange">
            <span class="slider"></span>
          </label>
          <span class="status-text">{{ config.enabled ? '已启用' : '未启用' }}</span>
        </div>
        
        <div class="form-group">
          <label class="form-label">API地址:</label>
          <div class="input-container">
            <input 
              v-model="config.baseUrl" 
              type="text"
              placeholder="https://api.openai.com/v1" 
              class="text-input">
            <div class="quick-buttons">
              <button type="button" class="quick-btn" @click="setQuickUrl('https://api.deepseek.com/v1')">DeepSeek</button>
              <button type="button" class="quick-btn" @click="setQuickUrl('https://dashscope.aliyuncs.com/compatible-mode/v1')">行业智能体</button>
              <button type="button" class="quick-btn" @click="setQuickUrl('https://api.openai.com/v1')">公司专属智能体</button>
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">API密钥:</label>
          <input
            v-model="config.apiKey"
            type="password"
            placeholder="请输入API密钥"
            class="text-input">
        </div>

        <div class="form-group">
          <label class="form-label">AI模型:</label>
          <div class="input-container">
            <div class="model-input-wrapper">
              <input
                v-model="config.defaultModel"
                type="text"
                placeholder="请输入模型ID或从下拉列表选择"
                class="text-input model-input"
                list="model-list">
              <datalist id="model-list">
                <option v-for="model in availableModels" :key="model.id" :value="model.id">
                  {{ model.name || model.id }} ({{ model.owned_by || 'Unknown' }})
                </option>
              </datalist>
              <button type="button" class="dropdown-btn" @click="showModelDropdown = !showModelDropdown">
                {{ showModelDropdown ? '▲' : '▼' }}
              </button>
            </div>

            <!-- 自定义下拉列表 -->
            <div v-if="showModelDropdown" class="custom-dropdown">
              <div class="dropdown-header">
                <span>可用模型列表 ({{ availableModels.length }})</span>
                <button type="button" class="close-btn" @click="showModelDropdown = false">✕</button>
              </div>
              <div class="dropdown-content">
                <div
                  v-for="model in availableModels"
                  :key="model.id"
                  class="dropdown-item"
                  @click="selectModel(model)">
                  <div class="model-id">{{ model.id }}</div>
                  <div class="model-info">
                    <span v-if="model.name && model.name !== model.id" class="model-name">{{ model.name }}</span>
                    <span v-if="model.owned_by" class="model-owner">{{ model.owned_by }}</span>
                  </div>
                </div>
                <div v-if="availableModels.length === 0" class="dropdown-empty">
                  暂无可用模型，请先刷新模型列表
                </div>
              </div>
            </div>

            <div class="model-buttons">
              <button type="button" class="model-btn" @click="loadModels" :disabled="loadingModels">
                {{ loadingModels ? '⏳ 加载中...' : '🔄 刷新模型' }}
              </button>
              <button type="button" class="model-btn" @click="autoSelectModel">🤖 智能推荐</button>
              <button type="button" class="model-btn" @click="showModelHelp">❓ 模型说明</button>
            </div>

            <div class="model-tip">
              💡 您可以直接输入模型ID，或点击"🔄 刷新模型"获取可用模型列表
            </div>
          </div>
        </div>
        
        <div class="form-actions">
          <button type="button" class="btn btn-primary" @click="testConnection" :disabled="testing">
            {{ testing ? '测试中...' : '🔍 测试连接' }}
          </button>
          <button type="button" class="btn btn-success" @click="saveConfig" :disabled="saving">
            {{ saving ? '保存中...' : '💾 保存配置' }}
          </button>
          <button type="button" class="btn btn-secondary" @click="resetConfig">
            🔄 重置配置
          </button>
        </div>
        
        <!-- 当前配置显示 -->
        <div class="current-config">
          <h4>当前配置:</h4>
          <div class="config-item">
            <span class="config-label">启用状态:</span>
            <span class="config-value" :class="config.enabled ? 'enabled' : 'disabled'">
              {{ config.enabled ? '已启用' : '未启用' }}
            </span>
          </div>
          <div class="config-item">
            <span class="config-label">API地址:</span>
            <span class="config-value">{{ config.baseUrl || '未设置' }}</span>
          </div>
          <div class="config-item">
            <span class="config-label">API密钥:</span>
            <span class="config-value">{{ config.apiKey ? '***已设置***' : '未设置' }}</span>
          </div>
          <div class="config-item">
            <span class="config-label">AI模型:</span>
            <span class="config-value">{{ config.defaultModel || '未设置' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiConfigSimple',
  data() {
    return {
      config: {
        enabled: false,
        baseUrl: 'https://api.openai.com/v1',
        apiKey: '',
        defaultModel: 'gpt-3.5-turbo'
      },
      
      aiStatus: {
        connected: false,
        message: '未配置AI服务'
      },

      availableModels: [],
      loadingModels: false,
      showModelDropdown: false,
      testing: false,
      saving: false
    }
  },
  
  mounted() {
    this.loadConfig()
    this.checkAiStatus()
  },
  
  methods: {
    // 加载配置
    async loadConfig() {
      try {
        const response = await fetch('/api/ai-config/get', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success && result.data) {
          this.config = { ...this.config, ...result.data }
        }
      } catch (error) {
        console.error('加载AI配置失败:', error)
      }
    },
    
    // 保存配置
    async saveConfig() {
      this.saving = true
      try {
        const response = await fetch('/api/ai-config/save', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(this.config)
        })
        
        const result = await response.json()
        if (result.success) {
          alert('AI配置保存成功！')
          // 保存成功后更新AI状态
          this.checkAiStatus()
        } else {
          alert('保存失败: ' + result.message)
        }
      } catch (error) {
        alert('保存失败: ' + error.message)
      } finally {
        this.saving = false
      }
    },
    
    // 测试连接
    async testConnection() {
      // 验证配置
      if (!this.config.baseUrl || !this.config.apiKey) {
        alert('请先填写API地址和API密钥')
        return
      }

      this.testing = true
      try {
        // 先尝试获取模型列表来测试连接
        const modelsResponse = await fetch('/api/ai-config/models', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            baseUrl: this.config.baseUrl,
            apiKey: this.config.apiKey
          })
        })

        const modelsResult = await modelsResponse.json()
        if (modelsResult.success && modelsResult.data && modelsResult.data.length > 0) {
          this.aiStatus.connected = true
          this.aiStatus.message = 'AI服务连接正常'
          this.availableModels = modelsResult.data

          // 如果没有选择模型，自动选择第一个
          if (!this.config.defaultModel && modelsResult.data.length > 0) {
            this.config.defaultModel = modelsResult.data[0].id
          }

          alert(`✅ 连接测试成功！\n\n发现 ${modelsResult.data.length} 个可用模型:\n${modelsResult.data.map(m => '• ' + m.id).join('\n')}`)

          // 测试成功后检查状态
          this.checkAiStatus()
        } else {
          // 如果模型列表失败，尝试原来的测试接口
          const response = await fetch('/api/ai-config/test', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(this.config)
          })

          const result = await response.json()
          if (result.success) {
            this.aiStatus.connected = true
            this.aiStatus.message = 'AI服务连接正常'
            alert('✅ AI服务连接测试成功！\n\n' + result.data)
            this.checkAiStatus()
          } else {
            this.aiStatus.connected = false
            this.aiStatus.message = result.message || '连接失败'
            alert('❌ 连接测试失败:\n\n' + result.message + '\n\n请检查:\n1. API地址是否正确\n2. API密钥是否有效\n3. 网络连接是否正常')
          }
        }
      } catch (error) {
        this.aiStatus.connected = false
        this.aiStatus.message = '连接异常: ' + error.message
        alert('❌ 连接测试异常:\n\n' + error.message + '\n\n可能的原因:\n1. 后端服务未启动\n2. 网络连接问题\n3. API服务不可用')
      } finally {
        this.testing = false
      }
    },
    
    // 重置配置
    resetConfig() {
      if (confirm('确定要重置所有AI配置吗？此操作不可撤销。')) {
        this.config = {
          enabled: false,
          baseUrl: 'https://api.openai.com/v1',
          apiKey: '',
          defaultModel: 'gpt-3.5-turbo'
        }
        alert('配置已重置')
      }
    },
    
    // 启用状态变化
    onEnabledChange() {
      if (this.config.enabled && !this.config.apiKey) {
        alert('请先配置API密钥')
      }
    },
    
    // 快速设置API地址
    setQuickUrl(url) {
      this.config.baseUrl = url
      this.loadModels() // 加载对应服务的模型列表
      alert('API地址已设置为: ' + url)
    },

    // 选择模型
    selectModel(model) {
      this.config.defaultModel = model.id
      this.showModelDropdown = false
      alert('已选择模型: ' + model.id)
    },

    // 加载模型列表
    async loadModels() {
      if (!this.config.baseUrl || !this.config.apiKey) {
        alert('请先填写API地址和API密钥')
        return
      }

      this.loadingModels = true
      try {
        const response = await fetch('/api/ai-config/models', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            baseUrl: this.config.baseUrl,
            apiKey: this.config.apiKey
          })
        })

        const result = await response.json()
        if (result.success && result.data) {
          this.availableModels = result.data
          alert(`✅ 成功加载 ${result.data.length} 个模型`)

          // 如果当前没有选择模型，自动选择第一个
          if (!this.config.defaultModel && result.data.length > 0) {
            this.config.defaultModel = result.data[0].id
          }
        } else {
          alert('❌ 加载模型列表失败:\n\n' + result.message)
        }
      } catch (error) {
        alert('❌ 加载模型列表异常:\n\n' + error.message)
      } finally {
        this.loadingModels = false
      }
    },

    // 智能推荐模型
    autoSelectModel() {
      if (this.availableModels.length === 0) {
        alert('请先加载模型列表')
        return
      }

      const baseUrl = this.config.baseUrl.toLowerCase()
      let recommendedModel = null

      if (baseUrl.includes('deepseek')) {
        recommendedModel = this.availableModels.find(m => m.id.includes('chat')) || this.availableModels[0]
      } else if (baseUrl.includes('dashscope')) {
        recommendedModel = this.availableModels.find(m => m.id.includes('turbo')) || this.availableModels[0]
      } else if (baseUrl.includes('openai')) {
        recommendedModel = this.availableModels.find(m => m.id.includes('3.5-turbo')) || this.availableModels[0]
      } else {
        recommendedModel = this.availableModels[0]
      }

      if (recommendedModel) {
        this.config.defaultModel = recommendedModel.id
        alert('🤖 已智能推荐模型: ' + recommendedModel.id)
      } else {
        alert('无法找到合适的模型，请手动选择')
      }
    },

    // 显示模型说明
    showModelHelp() {
      if (this.availableModels.length > 0) {
        let helpText = '🤖 当前可用模型列表：\n\n'
        this.availableModels.forEach((model, index) => {
          helpText += `${index + 1}. ${model.id}\n`
          if (model.name && model.name !== model.id) {
            helpText += `   名称: ${model.name}\n`
          }
          if (model.owned_by) {
            helpText += `   提供商: ${model.owned_by}\n`
          }
          helpText += '\n'
        })
        helpText += '💡 使用建议：\n'
        helpText += '• 点击"🔄 刷新模型"获取最新模型列表\n'
        helpText += '• 点击"🤖 智能推荐"自动选择合适模型\n'
        helpText += '• 选择包含"chat"或"turbo"的模型通常性能较好'
        alert(helpText)
      } else {
        const helpText = `
🤖 模型选择说明：

📋 如何获取模型列表：
1. 填写正确的API地址和密钥
2. 点击"🔄 刷新模型"按钮
3. 系统将自动从API获取可用模型

💡 常见模型类型：
• chat 模型 - 适合对话和通用任务
• turbo 模型 - 快速响应，性价比高
• plus/max 模型 - 更强性能，适合复杂任务
• coder 模型 - 专门用于代码相关任务

🔧 操作建议：
• 优先使用"刷新模型"获取真实可用模型
• 使用"智能推荐"快速选择合适模型
• 不同服务商的模型名称和特性可能不同
        `
        alert(helpText)
      }
    },

    // 检查AI服务状态
    async checkAiStatus() {
      try {
        const response = await fetch('/api/ai-config/status', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success && result.data) {
          // 检查配置完整性和启用状态
          const hasValidConfig = result.data.hasValidConfig || (this.config.baseUrl && this.config.apiKey && this.config.defaultModel)
          this.aiStatus.connected = result.data.enabled && hasValidConfig

          if (this.aiStatus.connected) {
            this.aiStatus.message = `AI服务已连接 (模型: ${result.data.model || this.config.defaultModel})`
          } else if (!result.data.enabled) {
            this.aiStatus.message = 'AI功能未启用，请保存配置后启用'
          } else if (!hasValidConfig) {
            this.aiStatus.message = 'AI配置不完整，请完善配置信息'
          } else {
            this.aiStatus.message = 'AI服务配置有误或连接失败'
          }
        }
      } catch (error) {
        console.error('检查AI状态失败:', error)
        // 如果后端状态检查失败，但有完整配置，则显示为配置完成但未验证
        const hasValidConfig = this.config.baseUrl && this.config.apiKey && this.config.defaultModel
        if (hasValidConfig) {
          this.aiStatus.connected = false
          this.aiStatus.message = 'AI配置已完成，请点击"测试连接"验证'
        } else {
          this.aiStatus.connected = false
          this.aiStatus.message = '请完善AI配置信息'
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.ai-config-simple {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.config-card {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.config-card h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.subtitle {
  color: #666;
  font-size: 14px;
  margin-bottom: 30px;
}

.config-help {
  margin: 20px 0;
  padding: 20px;
  background: #e8f4fd;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.config-help h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.help-content p {
  margin: 10px 0;
  color: #666;
  font-size: 14px;
}

.help-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.help-content li {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.ai-status {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  border: 2px solid;
  margin: 20px 0;
}

.ai-status.connected {
  border-color: #67c23a;
  background: #f0f9ff;
}

.ai-status.disconnected {
  border-color: #f56c6c;
  background: #fef0f0;
}

.status-icon {
  font-size: 24px;
  margin-right: 15px;
}

.status-content h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.status-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.config-form {
  margin: 30px 0;
}

.config-form h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.form-group {
  margin: 20px 0;
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.form-label {
  min-width: 100px;
  font-weight: bold;
  color: #333;
  padding-top: 8px;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.text-input {
  width: 400px;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  background-color: #fff;
  transition: border-color 0.2s;
}

.text-input:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.text-input::placeholder {
  color: #c0c4cc;
}

.quick-buttons {
  display: flex;
  gap: 8px;
}

.quick-btn {
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.select-input {
  width: 400px;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  background-color: #fff;
  transition: border-color 0.2s;
}

.select-input:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.model-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.model-btn {
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.model-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.model-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.model-input {
  flex: 1;
  padding-right: 40px;
}

.dropdown-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 12px;
  padding: 4px;
}

.dropdown-btn:hover {
  color: #409eff;
}

.custom-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  z-index: 1000;
  max-height: 300px;
  overflow: hidden;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-size: 12px;
  color: #666;
}

.close-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
}

.close-btn:hover {
  color: #f56c6c;
}

.dropdown-content {
  max-height: 250px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f5f7fa;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background: #f5f7fa;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.model-id {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.model-info {
  display: flex;
  gap: 10px;
  margin-top: 2px;
}

.model-name {
  color: #666;
  font-size: 12px;
}

.model-owner {
  color: #409eff;
  font-size: 12px;
}

.dropdown-empty {
  padding: 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

.model-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.status-text {
  color: #666;
  font-size: 14px;
  padding-top: 8px;
}

/* 自定义开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 22px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #dcdfe6;
  transition: 0.3s;
  border-radius: 22px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #409eff;
}

input:checked + .slider:before {
  transform: translateX(22px);
}

.form-actions {
  margin: 30px 0;
  display: flex;
  gap: 15px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #409eff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #66b1ff;
}

.btn-success {
  background: #67c23a;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #85ce61;
}

.btn-secondary {
  background: #909399;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #a6a9ad;
}

.current-config {
  margin: 30px 0;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.current-config h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.config-item {
  display: flex;
  margin: 8px 0;
}

.config-label {
  min-width: 80px;
  color: #666;
  font-size: 14px;
}

.config-value {
  color: #333;
  font-size: 14px;
}

.config-value.enabled {
  color: #67c23a;
  font-weight: bold;
}

.config-value.disabled {
  color: #f56c6c;
}
</style>
