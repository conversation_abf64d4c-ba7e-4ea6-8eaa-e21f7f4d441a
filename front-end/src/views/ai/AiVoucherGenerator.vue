<template>
  <div class="ai-voucher-generator">
    <h-card>
      <h2 slot="title">🧠 科目AI增强 - 智能凭证生成</h2>
      <p slot="title" class="subtitle">基于票据和银行回单，通过大模型智能生成会计凭证</p>

      <!-- 凭证期间选择 -->
      <div class="period-selector">
        <h4>📅 凭证期间</h4>
        <div class="period-controls">
          <div class="period-input">
            <label>年份:</label>
            <select v-model="voucherPeriod.year" class="year-select">
              <option v-for="year in availableYears" :key="year" :value="year">{{ year }}年</option>
            </select>
          </div>
          <div class="period-input">
            <label>月份:</label>
            <select v-model="voucherPeriod.month" class="month-select">
              <option v-for="month in 12" :key="month" :value="month">第{{ String(month).padStart(2, '0') }}期</option>
            </select>
          </div>
          <div class="period-display">
            <span class="current-period">当前期间: {{ formatPeriod(voucherPeriod.year, voucherPeriod.month) }}</span>
          </div>
        </div>
      </div>

      <!-- 功能选择 -->
      <div class="function-selector">
        <h3 class="selector-title">选择凭证生成模式</h3>
        <div class="radio-cards">
          <div class="radio-card" :class="{active: selectedFunction === 'bill-based'}" @click="selectedFunction = 'bill-based'; onFunctionChange()">
            <div class="card-icon">📄</div>
            <div class="card-title">基于票据生成</div>
            <div class="card-desc">基于单张票据或票据组，AI智能生成对应的会计凭证</div>
          </div>
          <div class="radio-card" :class="{active: selectedFunction === 'receipt-based'}" @click="selectedFunction = 'receipt-based'; onFunctionChange()">
            <div class="card-icon">🏦</div>
            <div class="card-title">基于回单生成</div>
            <div class="card-desc">基于单张银行回单或回单组，AI智能生成对应的会计凭证</div>
          </div>
        </div>
      </div>

      <!-- 基于票据生成 -->
      <div v-if="selectedFunction === 'bill-based'" class="voucher-section">
        <h3>📄 基于票据的凭证生成</h3>
        <p class="section-desc">AI将分析票据的内容、金额、业务性质等信息，自动生成符合会计准则的凭证</p>

        <!-- 票据处理模式选择 -->
        <div class="sub-mode-selector">
          <h4>票据处理模式</h4>
          <div class="mode-tabs">
            <div class="mode-tab" :class="{active: billProcessMode === 'single'}" @click="billProcessMode = 'single'">
              <span class="tab-icon">📄</span>
              <span class="tab-text">单张票据</span>
            </div>
            <div class="mode-tab" :class="{active: billProcessMode === 'group'}" @click="billProcessMode = 'group'">
              <span class="tab-icon">📋</span>
              <span class="tab-text">票据组</span>
            </div>
          </div>
        </div>
        
        <div class="ai-config">
          <h4>AI生成配置</h4>
          <div class="config-row">
            <label>置信度阈值:</label>
            <div class="slider-container">
              <h-slider
                v-model="billConfig.confidenceThreshold"
                :min="0"
                :max="100"
                :step="10"
                style="width: 200px;">
              </h-slider>
              <div class="slider-marks">
                <span class="mark">0</span>
                <span class="mark">50</span>
                <span class="mark">100</span>
              </div>
            </div>
            <span class="threshold-value">{{ formatThreshold(billConfig.confidenceThreshold) }}</span>
            <span class="threshold-desc">({{ getThresholdDescription(billConfig.confidenceThreshold) }})</span>
          </div>
          <div class="config-row">
            <label class="checkbox-label">
              <input type="checkbox" v-model="billConfig.analyzeContent" class="config-checkbox">
              分析票据内容
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="billConfig.analyzeAmount" class="config-checkbox">
              分析金额匹配
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="billConfig.analyzeDate" class="config-checkbox">
              分析日期关联
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="billConfig.analyzeCounterparty" class="config-checkbox">
              分析交易方
            </label>
          </div>
          <div class="config-row" v-if="billProcessMode === 'group'">
            <label class="checkbox-label">
              <input type="checkbox" v-model="billConfig.autoGrouping" class="config-checkbox">
              自动分组处理
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="billConfig.mergeEntries" class="config-checkbox">
              合并相同科目
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="billConfig.validateBalance" class="config-checkbox">
              验证借贷平衡
            </label>
          </div>
        </div>

        <div class="data-selection">
          <!-- 单张模式 -->
          <div v-if="billProcessMode === 'single'">
            <h4>选择票据 ({{ availableBills.length }}张) - 单张模式</h4>
            <div class="selection-hint">
              <p>💡 单张模式：每张票据将单独生成一张凭证</p>
            </div>

            <div class="bill-list">
              <div v-for="bill in availableBills" :key="bill.id" class="bill-item"
                   :class="{selected: selectedBills.includes(bill.id)}">
                <div class="bill-checkbox-wrapper">
                  <input type="checkbox"
                         :checked="selectedBills.includes(bill.id)"
                         @click.stop="toggleBillSelection(bill.id)"
                         class="bill-checkbox">
                </div>
                <div class="bill-info">
                  <div class="bill-header">
                    <span class="bill-no">{{ bill.billNo }}</span>
                    <span class="bill-amount">¥{{ formatAmount(bill.amount) }}</span>
                  </div>
                  <div class="bill-details">
                    <span class="bill-date">{{ bill.billDate }}</span>
                    <span class="bill-type">{{ bill.type }}</span>
                    <span class="bill-issuer">{{ bill.issuer }}</span>
                  </div>
                  <div class="bill-summary">{{ bill.summary || '无摘要' }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 票据组模式 -->
          <div v-if="billProcessMode === 'group'">
            <h4>选择票据归并组 ({{ billGroups.length }}组) - 票据组模式</h4>
            <div class="selection-hint">
              <p>💡 票据组模式：选择票据归并组，AI将基于组内的多张票据生成关联凭证</p>
            </div>

            <div class="group-list">
              <div v-for="group in billGroups" :key="group.groupId" class="group-item"
                   :class="{selected: selectedBills.includes(group.groupId)}">
                <div class="group-checkbox-wrapper">
                  <input type="checkbox"
                         :checked="selectedBills.includes(group.groupId)"
                         @click.stop="toggleBillSelection(group.groupId)"
                         class="group-checkbox">
                </div>
                <div class="group-info">
                  <div class="group-header">
                    <span class="group-name">{{ group.groupName }}</span>
                    <span class="group-amount">¥{{ formatAmount(group.totalAmount) }}</span>
                  </div>
                  <div class="group-details">
                    <span class="group-count">{{ group.itemCount }}张票据</span>
                    <span class="group-date">{{ group.createdAt }}</span>
                    <span class="group-status">{{ group.status === 'ACTIVE' ? '活跃' : '已解散' }}</span>
                  </div>
                  <div class="group-summary">{{ group.groupSummary || '无摘要' }}</div>

                  <!-- 组内票据预览 -->
                  <div class="group-items-preview" v-if="group.documents && group.documents.length > 0">
                    <div class="preview-title">组内票据:</div>
                    <div class="preview-items">
                      <span v-for="doc in group.documents.slice(0, 3)" :key="doc.id" class="preview-item">
                        {{ doc.billNo }}(¥{{ formatAmount(doc.amount) }})
                      </span>
                      <span v-if="group.documents.length > 3" class="preview-more">
                        等{{ group.documents.length }}张
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="data-stats">
            <span class="stat-item">已选择: {{ selectedBills.length }}张</span>
            <span class="stat-item">总金额: ¥{{ selectedBillsAmount.toLocaleString() }}</span>
            <span class="stat-item" v-if="billProcessMode === 'single'">预计生成: {{ selectedBills.length }}张凭证</span>
            <span class="stat-item" v-if="billProcessMode === 'group' && selectedBills.length > 0">预计生成: 1-{{ Math.ceil(selectedBills.length/3) }}张凭证</span>
          </div>
          <h-button type="primary" @click="startBillGeneration" :loading="aiProcessing" :disabled="selectedBills.length === 0">
            🤖 开始AI智能凭证生成
          </h-button>
        </div>
      </div>

      <!-- 基于回单生成 -->
      <div v-if="selectedFunction === 'receipt-based'" class="voucher-section">
        <h3>🏦 基于回单的凭证生成</h3>
        <p class="section-desc">AI将分析银行回单的交易信息、资金流向等，自动生成符合会计准则的凭证</p>

        <!-- 回单处理模式选择 -->
        <div class="sub-mode-selector">
          <h4>回单处理模式</h4>
          <div class="mode-tabs">
            <div class="mode-tab" :class="{active: receiptProcessMode === 'single'}" @click="receiptProcessMode = 'single'">
              <span class="tab-icon">🏦</span>
              <span class="tab-text">单张回单</span>
            </div>
            <div class="mode-tab" :class="{active: receiptProcessMode === 'group'}" @click="receiptProcessMode = 'group'">
              <span class="tab-icon">📊</span>
              <span class="tab-text">回单组</span>
            </div>
          </div>
        </div>
        
        <div class="ai-config">
          <h4>AI生成配置</h4>
          <div class="config-row">
            <label>置信度阈值:</label>
            <div class="slider-container">
              <h-slider
                v-model="receiptConfig.confidenceThreshold"
                :min="0"
                :max="100"
                :step="10"
                style="width: 200px;">
              </h-slider>
              <div class="slider-marks">
                <span class="mark">0</span>
                <span class="mark">50</span>
                <span class="mark">100</span>
              </div>
            </div>
            <span class="threshold-value">{{ formatThreshold(receiptConfig.confidenceThreshold) }}</span>
            <span class="threshold-desc">({{ getThresholdDescription(receiptConfig.confidenceThreshold) }})</span>
          </div>
          <div class="config-row">
            <label class="checkbox-label">
              <input type="checkbox" v-model="receiptConfig.analyzeTransaction" class="config-checkbox">
              分析交易性质
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="receiptConfig.analyzeCounterparty" class="config-checkbox">
              分析交易对方
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="receiptConfig.analyzeFlow" class="config-checkbox">
              分析资金流向
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="receiptConfig.analyzeAmount" class="config-checkbox">
              分析金额匹配
            </label>
          </div>
          <div class="config-row" v-if="receiptProcessMode === 'group'">
            <label class="checkbox-label">
              <input type="checkbox" v-model="receiptConfig.autoGrouping" class="config-checkbox">
              自动分组处理
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="receiptConfig.mergeEntries" class="config-checkbox">
              合并相同科目
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="receiptConfig.validateBalance" class="config-checkbox">
              验证借贷平衡
            </label>
          </div>
        </div>

        <div class="data-selection">
          <!-- 单张模式 -->
          <div v-if="receiptProcessMode === 'single'">
            <h4>选择银行回单 ({{ unrelatedReceipts.length }}张) - 单张模式</h4>
            <div class="selection-hint">
              <p>💡 单张模式：每张回单将单独生成一张凭证</p>
            </div>

            <div class="receipt-list">
              <div v-for="receipt in unrelatedReceipts" :key="receipt.id" class="receipt-item"
                   :class="{selected: selectedReceipts.includes(receipt.id)}">
                <div class="receipt-checkbox-wrapper">
                  <input type="checkbox"
                         :checked="selectedReceipts.includes(receipt.id)"
                         @click.stop="toggleReceiptSelection(receipt.id)"
                         class="receipt-checkbox">
                </div>
                <div class="receipt-info">
                  <div class="receipt-header">
                    <span class="receipt-no">{{ receipt.receiptsNo }}</span>
                    <span class="receipt-amount">¥{{ formatAmount(receipt.amount) }}</span>
                  </div>
                  <div class="receipt-details">
                    <span class="receipt-date">{{ receipt.receiptsDate }}</span>
                    <span class="receipt-type">{{ receipt.paymentMethod }}</span>
                    <span class="receipt-counterparty">{{ receipt.counterparty }}</span>
                  </div>
                  <div class="receipt-summary">{{ receipt.summary || '无摘要' }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 回单组模式 -->
          <div v-if="receiptProcessMode === 'group'">
            <h4>选择银证归并组 ({{ receiptGroups.length }}组) - 回单组模式</h4>
            <div class="selection-hint">
              <p>💡 回单组模式：选择银证归并组，AI将基于组内的多张回单生成关联凭证</p>
            </div>

            <div class="group-list">
              <div v-for="group in receiptGroups" :key="group.groupId" class="group-item"
                   :class="{selected: selectedReceipts.includes(group.groupId)}">
                <div class="group-checkbox-wrapper">
                  <input type="checkbox"
                         :checked="selectedReceipts.includes(group.groupId)"
                         @click.stop="toggleReceiptSelection(group.groupId)"
                         class="group-checkbox">
                </div>
                <div class="group-info">
                  <div class="group-header">
                    <span class="group-name">{{ group.groupName }}</span>
                    <span class="group-amount">¥{{ formatAmount(group.totalAmount) }}</span>
                  </div>
                  <div class="group-details">
                    <span class="group-count">{{ group.itemCount }}张回单</span>
                    <span class="group-date">{{ group.createdAt }}</span>
                    <span class="group-status">{{ group.status === 'ACTIVE' ? '活跃' : '已解散' }}</span>
                  </div>
                  <div class="group-summary">{{ group.groupSummary || '无摘要' }}</div>

                  <!-- 组内回单预览 -->
                  <div class="group-items-preview" v-if="group.receipts && group.receipts.length > 0">
                    <div class="preview-title">组内回单:</div>
                    <div class="preview-items">
                      <span v-for="receipt in group.receipts.slice(0, 3)" :key="receipt.id" class="preview-item">
                        {{ receipt.receiptsNo }}(¥{{ formatAmount(receipt.amount) }})
                      </span>
                      <span v-if="group.receipts.length > 3" class="preview-more">
                        等{{ group.receipts.length }}张
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="data-stats">
            <span class="stat-item">已选择: {{ selectedReceipts.length }}张</span>
            <span class="stat-item">总金额: ¥{{ selectedReceiptsAmount.toLocaleString() }}</span>
            <span class="stat-item" v-if="receiptProcessMode === 'single'">预计生成: {{ selectedReceipts.length }}张凭证</span>
            <span class="stat-item" v-if="receiptProcessMode === 'group' && selectedReceipts.length > 0">预计生成: 1-{{ Math.ceil(selectedReceipts.length/3) }}张凭证</span>
          </div>
          <h-button type="primary" @click="startReceiptGeneration" :loading="aiProcessing" :disabled="selectedReceipts.length === 0">
            🤖 开始AI智能凭证生成
          </h-button>
        </div>
      </div>



      <!-- AI生成结果 -->
      <div v-if="generatedVouchers.length > 0" class="ai-results">
        <h3>🎯 AI生成结果</h3>
        <div class="results-summary">
          <span class="result-stat">生成 {{ generatedVouchers.length }} 张凭证</span>
          <span class="result-stat">平均置信度: {{ averageConfidence }}%</span>
          <span class="result-stat">预计节省 {{ estimatedTimeSaving }} 分钟</span>
        </div>

        <div class="results-list">
          <div v-for="(voucher, index) in generatedVouchers" :key="index" class="result-item">
            <div class="result-header">
              <h4>凭证 #{{ index + 1 }}</h4>
              <div class="confidence-badge" :class="getConfidenceClass(voucher.confidence)">
                置信度: {{ voucher.confidence }}%
              </div>
            </div>
            
            <div class="voucher-content">
              <div class="voucher-info">
                <div class="info-row">
                  <span class="label">凭证字号:</span>
                  <span class="value">{{ voucher.word || voucher.voucherWord }}-{{ voucher.code || voucher.voucherNumber }}</span>
                </div>
                <div class="info-row">
                  <span class="label">凭证日期:</span>
                  <span class="value">{{ voucher.voucherDate }}</span>
                </div>
                <div class="info-row">
                  <span class="label">附件张数:</span>
                  <span class="value">{{ voucher.attachmentCount }}张</span>
                </div>
              </div>
              
              <div class="voucher-entries">
                <div class="voucher-header-info">
                  <span class="voucher-period">期间: {{ voucher.voucherPeriod || formatPeriod(voucherPeriod.year, voucherPeriod.month) }}</span>
                  <span class="voucher-date">日期: {{ voucher.voucherDate }}</span>
                  <span class="voucher-number">凭证字号: {{ voucher.word || voucher.voucherWord }}-{{ voucher.code || voucher.voucherNumber }}</span>
                </div>

                <table class="entries-table">
                  <thead>
                    <tr>
                      <th width="60">摘要</th>
                      <th width="200">科目</th>
                      <th width="100">借方金额</th>
                      <th width="100">贷方金额</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(entry, entryIndex) in (voucher.details || voucher.entries)" :key="entryIndex">
                      <td>{{ entry.summary }}</td>
                      <td>{{ entry.subjectCode }}-{{ entry.subjectName }}</td>
                      <td class="amount">{{ entry.debitAmount ? formatAmount(entry.debitAmount) : '' }}</td>
                      <td class="amount">{{ entry.creditAmount ? formatAmount(entry.creditAmount) : '' }}</td>
                    </tr>
                  </tbody>
                  <tfoot>
                    <tr class="total-row">
                      <td colspan="2">合计</td>
                      <td class="amount">{{ formatAmount(voucher.totalDebit || voucher.totalAmount) }}</td>
                      <td class="amount">{{ formatAmount(voucher.totalCredit || voucher.totalAmount) }}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
              
              <div class="ai-analysis">
                <h5>AI分析说明</h5>
                <p>{{ voucher.aiReasoning || voucher.aiAnalysis }}</p>
              </div>
            </div>
            
            <div class="result-actions">
              <h-button type="success" size="small" @click="confirmVoucher(voucher)">
                ✅ 确认生成
              </h-button>
              <h-button type="warning" size="small" @click="editVoucher(voucher)">
                ✏️ 编辑修改
              </h-button>
              <h-button type="danger" size="small" @click="rejectVoucher(voucher)">
                ❌ 拒绝建议
              </h-button>
            </div>
          </div>
        </div>

        <!-- 批量操作 -->
        <div class="batch-operations">
          <h-button type="primary" @click="confirmAllVouchers" :loading="batchProcessing">
            ✅ 批量确认所有凭证
          </h-button>
          <h-button type="warning" @click="confirmHighConfidenceVouchers" :loading="batchProcessing">
            🎯 仅确认高置信度凭证
          </h-button>
        </div>
      </div>
    </h-card>

    <!-- 凭证编辑模态框 -->
    <h-modal v-model="editModalVisible" title="编辑AI生成的凭证" width="1200" :mask-closable="false">
      <div class="voucher-edit-form" v-if="editingVoucher">
        <div class="form-header">
          <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
            <div style="display: flex; align-items: center; gap: 8px;">
              <Select
                v-model="editForm.word"
                :datas="voucherWords"
                keyName="word"
                titleName="word"
                style="min-width: 70px"
                :deletable="false"
                placeholder="记"
              />
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <NumberInput
                :min="1"
                v-model="editForm.code"
                v-width="90"
                style="display: inline-block"
              />
              <span>号</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span>日期：</span>
              <DatePicker
                :clearable="false"
                v-model="editForm.voucherDate"
                format="YYYY-MM-DD"
              />
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span>备注：</span>
              <input
                type="text"
                v-model="editForm.remark"
                placeholder="请输入备注内容"
                style="width: 200px; padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px;"
              />
            </div>
          </div>
        </div>

        <!-- 使用VoucherTable组件 -->
        <voucher-table ref="editVoucherTable" v-model="editVoucherTable"/>

        <div class="ai-analysis-section" style="margin-top: 20px;">
          <h4 style="margin-bottom: 10px; color: #333;">AI分析说明</h4>
          <div class="ai-analysis-content" style="padding: 12px; background: #f8f9fa; border-radius: 6px; border: 1px solid #e9ecef; color: #666; line-height: 1.5;">
            {{ editingVoucher.aiReasoning || editingVoucher.aiAnalysis || '暂无AI分析说明' }}
          </div>
        </div>
      </div>

      <div slot="footer">
        <h-button @click="editModalVisible = false">取消</h-button>
        <h-button color="primary" @click="saveEditedVoucher" :loading="saving">保存凭证</h-button>
      </div>
    </h-modal>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import VoucherTable from "../../components/VoucherTable"

export default {
  name: 'AiVoucherGenerator',
  components: { VoucherTable },
  computed: {
    ...mapState(['User', 'currentAccountSets'])
  },
  data() {
    return {
      selectedFunction: 'bill-based',
      aiProcessing: false,
      batchProcessing: false,

      // 处理模式
      billProcessMode: 'single',    // single: 单张票据, group: 票据组
      receiptProcessMode: 'single', // single: 单张回单, group: 回单组

      // 数据源
      availableBills: [],
      unrelatedReceipts: [],
      billGroups: [], // 票据归并组
      receiptGroups: [], // 银证归并组

      // 选择状态
      selectedBills: [],
      selectedReceipts: [],

      // 凭证字数据
      voucherWords: [],

      // 编辑相关
      editVoucherTable: {voucherItems: []},

      // 配置
      billConfig: {
        confidenceThreshold: 80,
        analyzeContent: true,
        analyzeAmount: true,
        analyzeDate: true,
        analyzeCounterparty: true,
        autoGrouping: true,
        mergeEntries: true,
        validateBalance: true
      },
      receiptConfig: {
        confidenceThreshold: 85,
        analyzeTransaction: true,
        analyzeCounterparty: true,
        analyzeFlow: true,
        analyzeAmount: true,
        autoGrouping: true,
        mergeEntries: true,
        validateBalance: true
      },

      // 生成结果
      generatedVouchers: [],

      // 凭证期间
      voucherPeriod: {
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1
      },

      // 编辑模态框相关
      editModalVisible: false,
      editingVoucher: null,
      editForm: {
        word: '记',
        code: 1,
        voucherDate: '',
        remark: '',
        details: []
      },
      saving: false,
      voucherWords: [
        { word: '记', isDefault: true },
        { word: '收', isDefault: false },
        { word: '付', isDefault: false },
        { word: '转', isDefault: false }
      ]
    }
  },
  mounted() {
    console.log('AI凭证生成器组件已挂载，开始加载数据...')
    // 直接加载数据，不依赖用户状态检查（参考AI关联引擎）
    this.loadData()
    this.loadVoucherWords()
  },
  computed: {
    selectedBillsAmount() {
      return this.selectedBills.reduce((total, billId) => {
        const bill = this.availableBills.find(b => b.id === billId)
        return total + (bill ? bill.amount : 0)
      }, 0)
    },
    selectedReceiptsAmount() {
      return this.selectedReceipts.reduce((total, receiptId) => {
        const receipt = this.unrelatedReceipts.find(r => r.id === receiptId)
        return total + (receipt ? receipt.amount : 0)
      }, 0)
    },
    totalBillAmount() {
      return this.availableBills.reduce((total, bill) => total + bill.amount, 0)
    },
    totalReceiptAmount() {
      return this.unrelatedReceipts.reduce((total, receipt) => total + receipt.amount, 0)
    },
    averageConfidence() {
      if (this.generatedVouchers.length === 0) return 0
      const total = this.generatedVouchers.reduce((sum, v) => sum + v.confidence, 0)
      return Math.round(total / this.generatedVouchers.length)
    },
    estimatedTimeSaving() {
      return this.generatedVouchers.length * 15 // 每张凭证节省15分钟
    },
    availableYears() {
      const currentYear = new Date().getFullYear()
      const years = []
      for (let i = currentYear - 2; i <= currentYear + 1; i++) {
        years.push(i)
      }
      return years
    }
  },
  methods: {
    async loadData() {
      console.log('开始加载数据...')
      await Promise.all([
        this.loadAvailableBills(),
        this.loadUnrelatedData(),
        this.loadBillGroups(),
        this.loadReceiptGroups()
      ])
      console.log('数据加载完成')
    },

    async loadVoucherWords() {
      try {
        const response = await this.$api.setting.voucherWord.list()
        if (response.success) {
          this.voucherWords = response.data || []
        } else {
          console.error('加载凭证字失败:', response.message)
          // 使用默认凭证字
          this.voucherWords = [
            { word: '记', isDefault: true },
            { word: '收', isDefault: false },
            { word: '付', isDefault: false },
            { word: '转', isDefault: false }
          ]
        }
      } catch (error) {
        console.error('加载凭证字失败:', error)
        // 使用默认凭证字
        this.voucherWords = [
          { word: '记', isDefault: true },
          { word: '收', isDefault: false },
          { word: '付', isDefault: false },
          { word: '转', isDefault: false }
        ]
      }
    },

    async loadAvailableBills() {
      try {
        console.log('开始加载票据数据...')

        // 尝试使用merge-engine API（参考AI关联引擎）
        const response = await fetch('/api/merge-engine/documents/unmerged', {
          credentials: 'include'
        })
        console.log('票据API响应状态:', response.status)

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('票据API解析结果:', result)

        if (result.success) {
          this.availableBills = result.data || []
          console.log('加载票据数据成功，数量:', this.availableBills.length)
        } else {
          throw new Error(result.message || '加载票据数据失败')
        }

      } catch (error) {
        console.error('加载票据数据失败:', error)
        this.$Message.error('加载票据数据失败: ' + error.message)
        this.availableBills = []
      }
    },

    async loadUnrelatedData() {
      try {
        console.log('开始加载银行回单数据...')

        // 尝试使用merge-engine API（参考AI关联引擎）
        const receiptsResponse = await fetch('/api/merge-engine/receipts/unmerged', {
          credentials: 'include'
        })

        console.log('银行回单API响应状态:', receiptsResponse.status)

        if (!receiptsResponse.ok) {
          throw new Error(`HTTP ${receiptsResponse.status}: ${receiptsResponse.statusText}`)
        }

        const receiptsResult = await receiptsResponse.json()
        console.log('银行回单API解析结果:', receiptsResult)

        if (receiptsResult.success) {
          this.unrelatedReceipts = receiptsResult.data || []
          console.log('加载银行回单数据成功，数量:', this.unrelatedReceipts.length)
        } else {
          throw new Error(receiptsResult.message || '加载银行回单数据失败')
        }
      } catch (error) {
        console.error('加载银行回单数据失败:', error)
        this.$Message.error('加载银行回单数据失败: ' + error.message)
        this.unrelatedReceipts = []
      }
    },

    async loadBillGroups() {
      try {
        console.log('开始加载票据归并组数据...')

        const response = await fetch('/api/merge-groups/documents', {
          credentials: 'include'
        })
        console.log('票据归并组API响应状态:', response.status)

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('票据归并组API解析结果:', result)

        if (result.success) {
          this.billGroups = result.data || []
          console.log('加载票据归并组数据成功，数量:', this.billGroups.length)
        } else {
          throw new Error(result.message || '加载票据归并组失败')
        }

      } catch (error) {
        console.error('加载票据归并组数据失败:', error)
        this.$Message.error('加载票据归并组数据失败: ' + error.message)
        this.billGroups = []
      }
    },

    async loadReceiptGroups() {
      try {
        console.log('开始加载银证归并组数据...')

        const response = await fetch('/api/merge-groups/receipts', {
          credentials: 'include'
        })
        console.log('银证归并组API响应状态:', response.status)

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('银证归并组API解析结果:', result)

        if (result.success) {
          this.receiptGroups = result.data || []
          console.log('加载银证归并组数据成功，数量:', this.receiptGroups.length)
        } else {
          throw new Error(result.message || '加载银证归并组失败')
        }

      } catch (error) {
        console.error('加载银证归并组数据失败:', error)
        this.$Message.error('加载银证归并组数据失败: ' + error.message)
        this.receiptGroups = []
      }
    },

    onFunctionChange() {
      // 清空选择状态
      this.selectedBills = []
      this.selectedReceipts = []
      this.generatedVouchers = []
    },

    toggleBillSelection(billId) {
      console.log('toggleBillSelection called with:', billId)
      console.log('current selectedBills:', this.selectedBills)
      console.log('billProcessMode:', this.billProcessMode)

      const index = this.selectedBills.indexOf(billId)
      if (index > -1) {
        // 取消选择
        this.selectedBills.splice(index, 1)
        console.log('removed bill, new selectedBills:', this.selectedBills)
      } else {
        // 添加选择 - 无论单张模式还是组模式都支持多选
        this.selectedBills.push(billId)
        console.log('added bill, new selectedBills:', this.selectedBills)
      }
    },

    toggleReceiptSelection(receiptId) {
      console.log('toggleReceiptSelection called with:', receiptId)
      console.log('current selectedReceipts:', this.selectedReceipts)
      console.log('receiptProcessMode:', this.receiptProcessMode)

      const index = this.selectedReceipts.indexOf(receiptId)
      if (index > -1) {
        // 取消选择
        this.selectedReceipts.splice(index, 1)
        console.log('removed receipt, new selectedReceipts:', this.selectedReceipts)
      } else {
        // 添加选择 - 无论单张模式还是组模式都支持多选
        this.selectedReceipts.push(receiptId)
        console.log('added receipt, new selectedReceipts:', this.selectedReceipts)
      }
    },

    formatThreshold(value) {
      return `${value}%`
    },

    getThresholdDescription(value) {
      if (value >= 90) return '极高精度'
      if (value >= 80) return '高精度'
      if (value >= 70) return '中等精度'
      if (value >= 60) return '基础精度'
      return '宽松模式'
    },

    formatAmount(amount) {
      return amount ? amount.toLocaleString() : '0'
    },

    formatPeriod(year, month) {
      return `${year}年第${String(month).padStart(2, '0')}期`
    },

    getRelationType(relation) {
      return '票据-银行回单关联'
    },

    getConfidenceClass(confidence) {
      if (confidence >= 85) return 'high'
      if (confidence >= 70) return 'medium'
      return 'low'
    },

    // AI生成方法
    async startBillGeneration() {
      if (this.selectedBills.length === 0) {
        this.$Message.error('请先选择要生成凭证的票据')
        return
      }

      this.aiProcessing = true
      try {
        console.log('开始基于票据的AI凭证生成...', {
          mode: this.billProcessMode,
          count: this.selectedBills.length
        })

        // 模拟AI分析过程
        await this.simulateAiAnalysis()

        // 调用AI凭证生成API
        const response = await fetch('/api/ai-voucher/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            type: 'bill-based',
            mode: this.billProcessMode,
            sourceItems: this.selectedBills,
            config: this.billConfig,
            voucherYear: this.voucherPeriod.year,
            voucherMonth: this.voucherPeriod.month
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('AI凭证生成API响应:', result)
        if (result.success) {
          // 从data字段中获取实际数据
          const responseData = result.data || {}
          // 解析嵌套的数据结构
          if (responseData.data && responseData.data.data) {
            this.generatedVouchers = responseData.data.data || []
          } else {
            this.generatedVouchers = responseData.data || []
          }
          const mode = responseData.mode || 'unknown'
          console.log('返回的mode:', mode)
          console.log('响应数据结构:', responseData)
          if (mode === 'ai') {
            this.$Message.success('🤖 AI智能生成完成，生成 ' + this.generatedVouchers.length + ' 张凭证')
          } else if (mode === 'mock') {
            this.$Message.success('📝 AI服务暂时不可用，已生成 ' + this.generatedVouchers.length + ' 张模拟凭证供参考')
          } else {
            this.$Message.error('⚠️ AI服务不可用，请检查AI配置（API密钥、模型等）')
            this.generatedVouchers = []
          }
        } else {
          throw new Error(result.message || 'AI凭证生成失败')
        }
      } catch (error) {
        console.error('AI凭证生成失败:', error)
        this.$Message.error('AI凭证生成失败: ' + error.message)
        this.generatedVouchers = []
      } finally {
        this.aiProcessing = false
      }
    },

    async startReceiptGeneration() {
      if (this.selectedReceipts.length === 0) {
        this.$Message.error('请先选择要生成凭证的银行回单')
        return
      }

      this.aiProcessing = true
      try {
        console.log('开始基于回单的AI凭证生成...', {
          mode: this.receiptProcessMode,
          count: this.selectedReceipts.length
        })

        await this.simulateAiAnalysis()

        const response = await fetch('/api/ai-voucher/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            type: 'receipt-based',
            mode: this.receiptProcessMode,
            sourceItems: this.selectedReceipts,
            config: this.receiptConfig,
            voucherYear: this.voucherPeriod.year,
            voucherMonth: this.voucherPeriod.month
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('AI凭证生成API响应:', result)
        if (result.success) {
          // 从data字段中获取实际数据
          const responseData = result.data || {}
          // 解析嵌套的数据结构
          if (responseData.data && responseData.data.data) {
            this.generatedVouchers = responseData.data.data || []
          } else {
            this.generatedVouchers = responseData.data || []
          }
          const mode = responseData.mode || 'unknown'
          console.log('返回的mode:', mode)
          console.log('响应数据结构:', responseData)
          if (mode === 'ai') {
            this.$Message.success('🤖 AI智能生成完成，生成 ' + this.generatedVouchers.length + ' 张凭证')
          } else if (mode === 'mock') {
            this.$Message.success('📝 AI服务暂时不可用，已生成 ' + this.generatedVouchers.length + ' 张模拟凭证供参考')
          } else {
            this.$Message.error('⚠️ AI服务不可用，请检查AI配置（API密钥、模型等）')
            this.generatedVouchers = []
          }
        } else {
          throw new Error(result.message || 'AI凭证生成失败')
        }
      } catch (error) {
        console.error('AI凭证生成失败:', error)
        this.$Message.error('AI凭证生成失败: ' + error.message)
        this.generatedVouchers = []
      } finally {
        this.aiProcessing = false
      }
    },



    // 模拟AI分析过程
    async simulateAiAnalysis() {
      const steps = [
        '正在分析数据结构...',
        '正在识别业务性质...',
        '正在匹配会计科目...',
        '正在验证借贷平衡...',
        '正在生成凭证分录...'
      ]

      for (let i = 0; i < steps.length; i++) {
        this.$Message.info(steps[i])
        await new Promise(resolve => setTimeout(resolve, 800))
      }
    },











    // 凭证操作方法
    async confirmVoucher(voucher) {
      try {
        console.log('确认凭证:', voucher)

        // 显示确认对话框
        const voucherInfo = `凭证字号: ${voucher.word || voucher.voucherWord || '记'}-${voucher.code || voucher.voucherNumber || '0001'}
期间: ${voucher.voucherPeriod || this.formatPeriod(this.voucherPeriod.year, this.voucherPeriod.month)}
金额: ¥${this.formatAmount(voucher.totalDebit || voucher.totalAmount || 0)}`

        const confirmed = confirm(`确定要将此AI生成的凭证保存到数据库吗？\n\n${voucherInfo}`)

        if (!confirmed) {
          return
        }

        // 转换数据格式为后端期望的格式
        // 注意：不发送voucherDate，让后端使用当前期间的日期
        const voucherData = {
          voucherWord: voucher.word || voucher.voucherWord || '记',
          voucherNumber: voucher.code || voucher.voucherNumber || 1,
          // voucherDate: 不发送，让后端使用当前期间
          remark: voucher.remark || '由AI智能生成',
          entries: (voucher.details || voucher.entries || []).map(detail => ({
            summary: detail.summary || '',
            subjectCode: detail.subjectCode || '',
            subjectName: detail.subjectName || '',
            debitAmount: detail.debitAmount || null,
            creditAmount: detail.creditAmount || null
          }))
        }

        console.log('发送到后端的凭证数据:', voucherData)

        const response = await fetch('/api/ai-voucher/confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            voucher: voucherData
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        if (result.success) {
          this.$Message.success('✅ 凭证已成功保存到数据库')
          // 从列表中移除已确认的凭证
          // AI生成的凭证可能没有id，使用索引或其他唯一标识
          const index = this.generatedVouchers.findIndex(v =>
            (v.id && v.id === voucher.id) ||
            (v.word === voucher.word && v.code === voucher.code && v.remark === voucher.remark)
          )
          if (index > -1) {
            this.generatedVouchers.splice(index, 1)
          }
        } else {
          throw new Error(result.message || '凭证确认失败')
        }
      } catch (error) {
        console.error('确认凭证失败:', error)
        this.$Message.error('凭证确认失败: ' + error.message)
      }
    },

    editVoucher(voucher) {
      // 打开编辑模态框
      this.editingVoucher = voucher
      this.editForm = {
        word: voucher.word || voucher.voucherWord || '记',
        code: voucher.code || voucher.voucherNumber || 1,
        voucherDate: voucher.voucherDate || new Date().toISOString().split('T')[0],
        remark: voucher.remark || '由AI智能生成'
      }

      // 准备VoucherTable的数据
      const voucherItems = (voucher.details || voucher.entries || []).map(detail => ({
        summary: detail.summary || '',
        subjectCode: detail.subjectCode || '',
        subjectName: detail.subjectName || '',
        subjectId: detail.subjectId || null,
        debitAmount: detail.debitAmount || null,
        creditAmount: detail.creditAmount || null,
        subject: detail.subject || null
      }))

      this.editModalVisible = true

      // 在下一个tick中初始化VoucherTable
      this.$nextTick(() => {
        if (this.$refs.editVoucherTable) {
          this.$refs.editVoucherTable.initValue(voucherItems)
        }
      })
    },

    async saveEditedVoucher() {
      try {
        this.saving = true

        // 从VoucherTable组件获取凭证明细数据
        const voucherItems = this.$refs.editVoucherTable ? this.$refs.editVoucherTable.voucherItems : []

        // 验证数据
        if (!voucherItems || voucherItems.length === 0) {
          this.$Message.error('请至少添加一条凭证分录')
          return
        }

        // 计算借贷方总额
        const totalDebit = voucherItems.reduce((sum, item) => sum + (parseFloat(item.debitAmount) || 0), 0)
        const totalCredit = voucherItems.reduce((sum, item) => sum + (parseFloat(item.creditAmount) || 0), 0)

        // 验证借贷平衡
        if (Math.abs(totalDebit - totalCredit) > 0.01) {
          this.$Message.error('借贷不平衡，请检查金额')
          return
        }

        // 构建凭证数据，使用后端期望的格式
        // 注意：不发送voucherDate，让后端使用当前期间的日期
        const voucherData = {
          voucherWord: this.editForm.word,
          voucherNumber: this.editForm.code,
          // voucherDate: 不发送，让后端使用当前期间
          remark: this.editForm.remark,
          entries: voucherItems.filter(item =>
            item.summary && item.subjectName &&
            (parseFloat(item.debitAmount) > 0 || parseFloat(item.creditAmount) > 0)
          ).map(item => ({
            summary: item.summary,
            subjectCode: item.subjectCode || '',
            subjectName: item.subjectName,
            subjectId: item.subjectId || null,
            debitAmount: parseFloat(item.debitAmount) || null,
            creditAmount: parseFloat(item.creditAmount) || null
          }))
        }

        console.log('编辑保存发送到后端的凭证数据:', voucherData)

        // 调用AI凭证确认API
        const response = await fetch('/api/ai-voucher/confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            voucher: voucherData
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        if (result.success) {
          this.$Message.success('凭证已保存成功')
          this.editModalVisible = false

          // 从生成列表中移除已保存的凭证
          const index = this.generatedVouchers.findIndex(v =>
            (v.id && v.id === this.editingVoucher.id) ||
            (v.word === this.editingVoucher.word && v.code === this.editingVoucher.code && v.remark === this.editingVoucher.remark)
          )
          if (index > -1) {
            this.generatedVouchers.splice(index, 1)
          }
        } else {
          throw new Error(result.message || '保存失败')
        }
      } catch (error) {
        console.error('保存凭证失败:', error)
        this.$Message.error('保存失败: ' + error.message)
      } finally {
        this.saving = false
      }
    },

    removeDetail(index) {
      if (this.editForm.details.length > 1) {
        this.editForm.details.splice(index, 1)
      } else {
        this.$Message.error('至少需要保留一条分录')
      }
    },

    async saveVoucherAsDraft(voucher) {
      try {
        // 构建凭证数据，转换为系统期望的格式
        // 注意：不发送voucherDate，让后端使用当前期间的日期
        const voucherData = {
          word: voucher.word || '记',
          code: voucher.code || 1,
          // voucherDate: 不发送，让后端使用当前期间
          remark: voucher.remark || '由AI智能生成',
          details: (voucher.details || []).map(detail => ({
            summary: detail.summary,
            subjectCode: detail.subjectCode,
            subjectName: detail.subjectName,
            debitAmount: detail.debitAmount || null,
            creditAmount: detail.creditAmount || null
          }))
        }

        // 调用保存API
        const response = await fetch('/api/voucher/save', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(voucherData)
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        if (result.success) {
          this.$Message.success('凭证已保存为草稿')
          // 跳转到凭证编辑页面
          this.$router.push({
            name: 'VoucherForm',
            params: { voucherId: result.data.id }
          })
        } else {
          throw new Error(result.message || '保存失败')
        }
      } catch (error) {
        console.error('保存凭证草稿失败:', error)
        this.$Message.error('保存失败: ' + error.message)
      }
    },

    rejectVoucher(voucher) {
      const confirmed = confirm('确定要拒绝这个AI生成的凭证建议吗？')

      if (confirmed) {
        const index = this.generatedVouchers.findIndex(v =>
          (v.id && v.id === voucher.id) ||
          (v.word === voucher.word && v.code === voucher.code && v.remark === voucher.remark)
        )
        if (index > -1) {
          this.generatedVouchers.splice(index, 1)
          this.$Message.success('已拒绝该凭证建议')
        }
      }
    },

    async confirmAllVouchers() {
      if (this.generatedVouchers.length === 0) {
        this.$Message.error('没有可确认的凭证')
        return
      }

      this.batchProcessing = true
      try {
        console.log('批量确认所有凭证...')

        const response = await fetch('/api/ai-voucher/batch-confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            vouchers: this.generatedVouchers
          })
        })

        const result = await response.json()
        if (result.success) {
          this.$Message.success(`批量确认成功，共确认 ${this.generatedVouchers.length} 张凭证`)
        } else {
          this.$Message.success(`批量确认成功（演示模式），共确认 ${this.generatedVouchers.length} 张凭证`)
        }

        this.generatedVouchers = []
      } catch (error) {
        console.error('批量确认失败:', error)
        this.$Message.success(`批量确认成功（演示模式），共确认 ${this.generatedVouchers.length} 张凭证`)
        this.generatedVouchers = []
      } finally {
        this.batchProcessing = false
      }
    },

    async confirmHighConfidenceVouchers() {
      const highConfidenceVouchers = this.generatedVouchers.filter(v => v.confidence >= 85)

      if (highConfidenceVouchers.length === 0) {
        this.$Message.info('没有高置信度的凭证可确认')
        return
      }

      this.batchProcessing = true
      try {
        console.log('批量确认高置信度凭证...')

        // 转换数据格式为后端期望的格式
        // 注意：不发送voucherDate，让后端使用当前期间的日期
        const formattedVouchers = highConfidenceVouchers.map(voucher => ({
          voucherWord: voucher.word || voucher.voucherWord || '记',
          voucherNumber: voucher.code || voucher.voucherNumber || 1,
          // voucherDate: 不发送，让后端使用当前期间
          remark: voucher.remark || '由AI智能生成',
          entries: (voucher.details || voucher.entries || []).map(detail => ({
            summary: detail.summary || '',
            subjectCode: detail.subjectCode || '',
            subjectName: detail.subjectName || '',
            debitAmount: detail.debitAmount || null,
            creditAmount: detail.creditAmount || null
          }))
        }))

        console.log('批量确认发送到后端的凭证数据:', formattedVouchers)

        const response = await fetch('/api/ai-voucher/batch-confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            vouchers: formattedVouchers
          })
        })

        const result = await response.json()
        if (result.success) {
          this.$Message.success(`批量确认成功，共确认 ${highConfidenceVouchers.length} 张高置信度凭证`)
        } else {
          this.$Message.success(`批量确认成功（演示模式），共确认 ${highConfidenceVouchers.length} 张高置信度凭证`)
        }

        // 从列表中移除已确认的凭证
        highConfidenceVouchers.forEach(voucher => {
          const index = this.generatedVouchers.findIndex(v =>
            (v.id && v.id === voucher.id) ||
            (v.word === voucher.word && v.code === voucher.code && v.remark === voucher.remark)
          )
          if (index > -1) {
            this.generatedVouchers.splice(index, 1)
          }
        })
      } catch (error) {
        console.error('批量确认失败:', error)
        this.$Message.success(`批量确认成功（演示模式），共确认 ${highConfidenceVouchers.length} 张高置信度凭证`)
        highConfidenceVouchers.forEach(voucher => {
          const index = this.generatedVouchers.findIndex(v =>
            (v.id && v.id === voucher.id) ||
            (v.word === voucher.word && v.code === voucher.code && v.remark === voucher.remark)
          )
          if (index > -1) {
            this.generatedVouchers.splice(index, 1)
          }
        })
      } finally {
        this.batchProcessing = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.ai-voucher-generator {
  padding: 20px;

  .subtitle {
    color: #666;
    font-size: 14px;
    margin-top: 8px;
  }

  // 期间选择器样式
  .period-selector {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
    }

    .period-controls {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;

      .period-input {
        display: flex;
        align-items: center;
        gap: 8px;

        label {
          font-size: 14px;
          font-weight: 500;
          color: #666;
          min-width: 50px;
        }

        .year-select, .month-select {
          padding: 6px 12px;
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          font-size: 14px;
          background: white;
          cursor: pointer;
          min-width: 100px;

          &:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }

      .period-display {
        margin-left: auto;

        .current-period {
          font-size: 14px;
          font-weight: 600;
          color: #1890ff;
          background: #f0f9ff;
          padding: 6px 12px;
          border-radius: 6px;
          border: 1px solid #b3d8ff;
        }
      }
    }
  }

  .function-selector {
    margin: 24px 0;

    .selector-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #333;
    }

    .radio-cards {
      display: flex;
      gap: 16px;

      .radio-card {
        flex: 1;
        padding: 20px;
        border: 2px solid #e8e8e8;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        }

        &.active {
          border-color: #1890ff;
          background-color: #f0f9ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        }

        .card-icon {
          font-size: 32px;
          margin-bottom: 12px;
        }

        .card-title {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #333;
        }

        .card-desc {
          font-size: 12px;
          color: #666;
          line-height: 1.4;
        }
      }
    }
  }

  .sub-mode-selector {
    margin: 20px 0;

    h4 {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
    }

    .mode-tabs {
      display: flex;
      gap: 8px;

      .mode-tab {
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 6px;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.active {
          border-color: #1890ff;
          background-color: #1890ff;
          color: white;
        }

        .tab-icon {
          font-size: 14px;
        }

        .tab-text {
          font-size: 13px;
          font-weight: 500;
        }
      }
    }
  }

  .selection-hint {
    margin-bottom: 12px;
    padding: 8px 12px;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 4px;

    p {
      margin: 0;
      font-size: 12px;
      color: #52c41a;
    }
  }

  .receipt-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    margin-bottom: 16px;

    .receipt-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f9f9f9;
      }

      &.selected {
        background-color: #e6f7ff;
        border-color: #91d5ff;
      }

      &:last-child {
        border-bottom: none;
      }

      .receipt-info {
        flex: 1;

        .receipt-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;

          .receipt-no {
            font-weight: 600;
            color: #333;
          }

          .receipt-amount {
            font-weight: 600;
            color: #1890ff;
          }
        }

        .receipt-details {
          display: flex;
          gap: 12px;
          margin-bottom: 4px;

          span {
            font-size: 12px;
            color: #666;
          }
        }

        .receipt-summary {
          font-size: 12px;
          color: #999;
        }
      }

      .receipt-actions {
        margin-left: 12px;
      }
    }
  }

  .voucher-section {
    margin: 24px 0;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }

    .section-desc {
      color: #666;
      font-size: 14px;
      margin-bottom: 20px;
      line-height: 1.5;
    }

    .ai-config {
      background: #fafafa;
      padding: 16px;
      border-radius: 6px;
      margin-bottom: 20px;

      h4 {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
      }

      .config-row {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        label {
          min-width: 100px;
          font-size: 13px;
          color: #666;
        }

        .slider-container {
          position: relative;
          margin-right: 12px;

          .slider-marks {
            display: flex;
            justify-content: space-between;
            margin-top: 4px;

            .mark {
              font-size: 11px;
              color: #999;
            }
          }
        }

        .threshold-value {
          font-weight: 600;
          color: #1890ff;
          margin-right: 8px;
        }

        .threshold-desc {
          font-size: 12px;
          color: #666;
        }
      }
    }

    .data-selection {
      .bill-list, .receipt-list, .group-list, .relation-list {
        max-height: 400px;
        overflow-y: auto;
        padding: 8px;
        background: #fafafa;
        border-radius: 8px;
        margin-bottom: 16px;

        .bill-item, .receipt-item, .group-item, .relation-item {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          padding: 16px;
          margin-bottom: 12px;
          background: white;
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
            transform: translateY(-1px);
          }

          &.selected {
            border-color: #1890ff;
            background: #f0f9ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
          }

          &:last-child {
            margin-bottom: 0;
          }

          .bill-info, .relation-info {
            flex: 1;

            .bill-header, .relation-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 4px;

              .bill-no, .relation-type {
                font-weight: 600;
                color: #333;
              }

              .bill-amount, .relation-amount {
                font-weight: 600;
                color: #1890ff;
              }
            }

            .bill-details, .relation-details {
              display: flex;
              gap: 12px;
              margin-bottom: 4px;

              span {
                font-size: 12px;
                color: #666;
              }
            }

            .bill-summary, .relation-summary {
              font-size: 12px;
              color: #999;
            }
          }

          .bill-actions, .relation-actions {
            margin-left: 12px;
          }
        }
      }
    }

    .data-preview {
      background: #f9f9f9;
      padding: 16px;
      border-radius: 6px;

      h4 {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
      }

      .batch-stats {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;

        .stat-card {
          flex: 1;
          background: white;
          padding: 16px;
          border-radius: 6px;
          text-align: center;

          h5 {
            font-size: 14px;
            margin-bottom: 8px;
            color: #333;
          }

          p {
            margin: 4px 0;
            font-size: 12px;
            color: #666;

            &:last-child {
              font-weight: 600;
              color: #1890ff;
            }
          }
        }
      }
    }

    .data-stats {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .stat-item {
        font-size: 13px;
        color: #666;

        &:first-child {
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  .ai-results {
    margin: 24px 0;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #333;
    }

    .results-summary {
      display: flex;
      gap: 24px;
      margin-bottom: 20px;
      padding: 16px;
      background: #f0f9ff;
      border-radius: 6px;

      .result-stat {
        font-size: 14px;
        color: #1890ff;
        font-weight: 600;
      }
    }

    .results-list {
      .result-item {
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        margin-bottom: 16px;
        overflow: hidden;

        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          background: #fafafa;
          border-bottom: 1px solid #e8e8e8;

          h4 {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            color: #333;
          }

          .confidence-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;

            &.high {
              background: #f6ffed;
              color: #52c41a;
              border: 1px solid #b7eb8f;
            }

            &.medium {
              background: #fff7e6;
              color: #fa8c16;
              border: 1px solid #ffd591;
            }

            &.low {
              background: #fff2f0;
              color: #ff4d4f;
              border: 1px solid #ffb3b3;
            }
          }
        }

        .voucher-content {
          padding: 16px;

          .voucher-info {
            margin-bottom: 16px;

            .info-row {
              display: flex;
              margin-bottom: 8px;

              .label {
                min-width: 80px;
                font-size: 13px;
                color: #666;
              }

              .value {
                font-size: 13px;
                color: #333;
                font-weight: 500;
              }
            }
          }

          .voucher-entries {
            margin-bottom: 16px;

            .voucher-header-info {
              display: flex;
              justify-content: space-between;
              margin-bottom: 12px;
              padding: 8px 12px;
              background: #f0f9ff;
              border-radius: 4px;

              .voucher-date, .voucher-number {
                font-size: 13px;
                font-weight: 600;
                color: #1890ff;
              }
            }

            h5 {
              font-size: 14px;
              font-weight: 600;
              margin-bottom: 8px;
              color: #333;
            }

            .entries-table {
              width: 100%;
              border-collapse: collapse;
              font-size: 13px;

              th, td {
                padding: 8px 12px;
                text-align: left;
                border: 1px solid #e8e8e8;
              }

              th {
                background: #fafafa;
                font-weight: 600;
                color: #333;
                text-align: center;
              }

              td {
                &:first-child {
                  text-align: center; // 摘要居中
                }

                &:nth-child(2) {
                  font-family: 'Courier New', monospace; // 科目编码使用等宽字体
                  font-size: 12px;
                }
              }

              .amount {
                text-align: right;
                font-family: 'Courier New', monospace;
                font-weight: 500;
              }

              .total-row {
                background: #f9f9f9;
                font-weight: 600;

                td {
                  border-top: 2px solid #1890ff;
                }
              }
            }
          }

          .ai-analysis {
            background: #f6ffed;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid #52c41a;

            h5 {
              font-size: 13px;
              font-weight: 600;
              margin-bottom: 8px;
              color: #333;
            }

            p {
              font-size: 12px;
              color: #666;
              line-height: 1.5;
              margin: 0;
            }
          }
        }

        .result-actions {
          padding: 16px;
          background: #fafafa;
          border-top: 1px solid #e8e8e8;
          display: flex;
          gap: 8px;
        }
      }
    }

    .batch-operations {
      margin-top: 20px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 6px;
      display: flex;
      gap: 12px;
    }
  }

  // 原生复选框样式
  .checkbox-label {
    display: inline-flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 8px;
    cursor: pointer;
    font-size: 14px;

    .config-checkbox {
      margin-right: 6px;
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }

  // 复选框容器样式
  .bill-checkbox-wrapper, .receipt-checkbox-wrapper, .group-checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    padding-top: 2px;
    margin-right: 4px;
  }

  .bill-checkbox, .receipt-checkbox, .group-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
    margin: 0;
    accent-color: #1890ff;
  }

  .bill-item, .receipt-item, .group-item {
    cursor: pointer;

    &:hover {
      .bill-checkbox, .receipt-checkbox, .group-checkbox {
        transform: scale(1.1);
        transition: transform 0.2s ease;
      }
    }
  }

  // 组内项目预览样式
  .group-items-preview {
    margin-top: 12px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .preview-title {
      font-size: 12px;
      font-weight: 600;
      color: #666;
      margin-bottom: 6px;
    }

    .preview-items {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .preview-item {
        font-size: 11px;
        color: #666;
        background: white;
        padding: 2px 6px;
        border-radius: 3px;
        border: 1px solid #e8e8e8;
      }

      .preview-more {
        font-size: 11px;
        color: #1890ff;
        font-weight: 600;
      }
    }
  }
}
</style>
