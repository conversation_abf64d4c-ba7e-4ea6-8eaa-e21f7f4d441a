<template>
  <div class="ai-merge-engine">
    <h-card>
      <h2 slot="title">🤖 AI智能归并引擎</h2>
      <p slot="title" class="subtitle">利用大语言模型智能分析票据和银行回单，自动识别相似内容并进行归并</p>
      
      <!-- 功能选择 -->
      <div class="function-selector">
        <h3 class="selector-title">选择归并模式</h3>
        <div class="radio-cards">
          <div class="radio-card" :class="{active: selectedFunction === 'bill-merge'}" @click="selectedFunction = 'bill-merge'; onFunctionChange()">
            <div class="card-icon">📄</div>
            <div class="card-title">票据智能归并</div>
            <div class="card-desc">分析票据内容、金额、日期等信息进行智能归并</div>
          </div>
          <div class="radio-card" :class="{active: selectedFunction === 'receipt-merge'}" @click="selectedFunction = 'receipt-merge'; onFunctionChange()">
            <div class="card-icon">🏦</div>
            <div class="card-title">银行回单智能归并</div>
            <div class="card-desc">分析银行回单交易信息、金额、时间等进行归并</div>
          </div>
          <div class="radio-card" :class="{active: selectedFunction === 'mixed-merge'}" @click="selectedFunction = 'mixed-merge'; onFunctionChange()">
            <div class="card-icon">🔄</div>
            <div class="card-title">混合智能归并</div>
            <div class="card-desc">同时分析票据和银行回单，识别业务关联性</div>
          </div>
        </div>
      </div>

      <!-- 票据智能归并 -->
      <div v-if="selectedFunction === 'bill-merge'" class="merge-section">
        <h3>📄 票据智能归并</h3>
        <p class="section-desc">AI将分析票据的内容、金额、日期、交易方等信息，自动识别相似票据并建议归并</p>
        
        <div class="ai-config">
          <h4>AI分析配置</h4>
          <div class="config-row">
            <label>相似度阈值:</label>
            <div class="slider-container">
              <h-slider
                v-model="billMergeConfig.similarityThreshold"
                :min="0"
                :max="100"
                :step="10"
                style="width: 200px;">
              </h-slider>
              <div class="slider-marks">
                <span class="mark">0</span>
                <span class="mark">50</span>
                <span class="mark">100</span>
              </div>
            </div>
            <span class="threshold-value">{{ formatThreshold(billMergeConfig.similarityThreshold) }}</span>
            <span class="threshold-desc">({{ getThresholdDescription(billMergeConfig.similarityThreshold) }})</span>
          </div>
          <div class="config-row">
            <h-checkbox v-model="billMergeConfig.analyzeContent">分析票据内容</h-checkbox>
            <h-checkbox v-model="billMergeConfig.analyzeAmount">分析金额相似性</h-checkbox>
            <h-checkbox v-model="billMergeConfig.analyzeDate">分析日期关联性</h-checkbox>
            <h-checkbox v-model="billMergeConfig.analyzeCounterparty">分析交易方</h-checkbox>
          </div>
        </div>

        <div class="data-preview">
          <h4>待分析票据 ({{ bills.length }}张)</h4>
          <div class="data-stats">
            <span class="stat-item">总金额: ¥{{ totalBillAmount.toLocaleString() }}</span>
            <span class="stat-item">日期范围: {{ billDateRange }}</span>
          </div>
          <h-button type="primary" @click="startBillAiMerge" :loading="aiProcessing">
            🤖 开始AI智能归并分析
          </h-button>
        </div>
      </div>

      <!-- 银行回单智能归并 -->
      <div v-if="selectedFunction === 'receipt-merge'" class="merge-section">
        <h3>🏦 银行回单智能归并</h3>
        <p class="section-desc">AI将分析银行回单的交易信息、金额、时间等，自动识别相关回单并建议归并</p>
        
        <div class="ai-config">
          <h4>AI分析配置</h4>
          <div class="config-row">
            <label>相似度阈值:</label>
            <div class="slider-container">
              <h-slider
                v-model="receiptMergeConfig.similarityThreshold"
                :min="0"
                :max="100"
                :step="10"
                style="width: 200px;">
              </h-slider>
              <div class="slider-marks">
                <span class="mark">0</span>
                <span class="mark">50</span>
                <span class="mark">100</span>
              </div>
            </div>
            <span class="threshold-value">{{ formatThreshold(receiptMergeConfig.similarityThreshold) }}</span>
            <span class="threshold-desc">({{ getThresholdDescription(receiptMergeConfig.similarityThreshold) }})</span>
          </div>
          <div class="config-row">
            <h-checkbox v-model="receiptMergeConfig.analyzeSummary">分析摘要内容</h-checkbox>
            <h-checkbox v-model="receiptMergeConfig.analyzeAmount">分析金额匹配</h-checkbox>
            <h-checkbox v-model="receiptMergeConfig.analyzeTime">分析时间序列</h-checkbox>
            <h-checkbox v-model="receiptMergeConfig.analyzeAccount">分析账户信息</h-checkbox>
          </div>
        </div>

        <div class="data-preview">
          <h4>待分析银行回单 ({{ receipts.length }}张)</h4>
          <div class="data-stats">
            <span class="stat-item">总金额: ¥{{ totalReceiptAmount.toLocaleString() }}</span>
            <span class="stat-item">日期范围: {{ receiptDateRange }}</span>
          </div>
          <h-button type="primary" @click="startReceiptAiMerge" :loading="aiProcessing">
            🤖 开始AI智能归并分析
          </h-button>
        </div>
      </div>

      <!-- 混合智能归并 -->
      <div v-if="selectedFunction === 'mixed-merge'" class="merge-section">
        <h3>🔄 混合智能归并</h3>
        <p class="section-desc">AI将同时分析票据和银行回单，识别业务关联性，进行跨类型智能归并</p>
        
        <div class="ai-config">
          <h4>AI分析配置</h4>
          <div class="config-row">
            <label>关联度阈值:</label>
            <div class="slider-container">
              <h-slider
                v-model="mixedMergeConfig.relationThreshold"
                :min="0"
                :max="100"
                :step="10"
                style="width: 200px;">
              </h-slider>
              <div class="slider-marks">
                <span class="mark">0</span>
                <span class="mark">50</span>
                <span class="mark">100</span>
              </div>
            </div>
            <span class="threshold-value">{{ formatThreshold(mixedMergeConfig.relationThreshold) }}</span>
            <span class="threshold-desc">({{ getThresholdDescription(mixedMergeConfig.relationThreshold) }})</span>
          </div>
          <div class="config-row">
            <h-checkbox v-model="mixedMergeConfig.crossTypeAnalysis">跨类型关联分析</h-checkbox>
            <h-checkbox v-model="mixedMergeConfig.businessLogicAnalysis">业务逻辑分析</h-checkbox>
            <h-checkbox v-model="mixedMergeConfig.timeSequenceAnalysis">时间序列分析</h-checkbox>
          </div>
        </div>

        <div class="data-preview">
          <h4>数据概览</h4>
          <div class="mixed-stats">
            <div class="stat-card">
              <h5>📄 票据</h5>
              <p>{{ bills.length }}张</p>
              <p>¥{{ totalBillAmount.toLocaleString() }}</p>
            </div>
            <div class="stat-card">
              <h5>🏦 银行回单</h5>
              <p>{{ receipts.length }}张</p>
              <p>¥{{ totalReceiptAmount.toLocaleString() }}</p>
            </div>
          </div>
          <h-button type="primary" @click="startMixedAiMerge" :loading="aiProcessing">
            🤖 开始混合智能归并分析
          </h-button>
        </div>
      </div>

      <!-- AI分析结果 -->
      <div v-if="aiResults.length > 0" class="ai-results">
        <h3>🎯 AI分析结果</h3>
        <div class="results-summary">
          <span class="result-stat">发现 {{ aiResults.length }} 个归并建议</span>
          <span class="result-stat">预计节省 {{ estimatedTimeSaving }} 分钟</span>
          <span class="result-stat">置信度: {{ averageConfidence }}%</span>
        </div>

        <div class="results-list">
          <div v-for="(result, index) in aiResults" :key="index" class="result-item">
            <div class="result-header">
              <h4>归并建议 #{{ index + 1 }}</h4>
              <div class="confidence-badge" :class="getConfidenceClass(result.confidence)">
                置信度: {{ result.confidence }}%
              </div>
            </div>
            
            <div class="result-content">
              <div class="merge-preview">
                <h5>📋 归并项目 ({{ result.items.length }}项)</h5>
                <div class="items-list">
                  <div v-for="item in result.items" :key="item.id" class="merge-item">
                    <span class="item-type">{{ item.type === 'bill' ? '📄' : '🏦' }}</span>
                    <span class="item-no">{{ item.no }}</span>
                    <span class="item-amount">¥{{ item.amount.toLocaleString() }}</span>
                    <span class="item-summary">{{ item.summary }}</span>
                  </div>
                </div>
              </div>
              
              <div class="ai-reasoning">
                <h5>🤖 AI分析理由</h5>
                <p>{{ result.reasoning }}</p>
              </div>
              
              <div class="result-actions">
                <h-button type="success" size="small" @click="acceptMerge(result)">
                  ✅ 接受建议
                </h-button>
                <h-button type="warning" size="small" @click="modifyMerge(result)">
                  ✏️ 修改建议
                </h-button>
                <h-button type="danger" size="small" @click="rejectMerge(result)">
                  ❌ 拒绝建议
                </h-button>
              </div>
            </div>
          </div>
        </div>

        <div class="batch-actions">
          <h-button type="primary" @click="acceptAllMerges" :loading="batchProcessing">
            ✅ 批量接受所有建议
          </h-button>
          <h-button @click="clearResults">
            🗑️ 清空结果
          </h-button>
        </div>
      </div>

      <!-- AI处理状态 -->
      <div v-if="aiProcessing" class="ai-processing">
        <div class="processing-animation">
          <div class="ai-brain">🧠</div>
          <div class="processing-text">{{ processingStatus }}</div>
        </div>
        <h-progress :percent="processingProgress" :show-text="true"></h-progress>
      </div>
    </h-card>

    <!-- 修改建议对话框 -->
    <h-modal v-model="modifyDialogVisible" title="修改归并建议" width="900" @on-ok="saveModifiedSuggestion" @on-cancel="cancelModify">
      <div v-if="currentModifyResult" class="modify-suggestion-form">
        <!-- 基本信息 -->
        <div class="form-section">
          <h4>📝 基本信息</h4>
          <div class="form-row">
            <div class="form-item half">
              <label>归并组名称：</label>
              <input
                type="text"
                v-model="currentModifyResult.groupName"
                placeholder="请输入归并组名称"
                class="form-input"
              />
            </div>
            <div class="form-item half">
              <label>置信度：</label>
              <input
                type="number"
                v-model="currentModifyResult.confidence"
                placeholder="0-100"
                min="0"
                max="100"
                class="form-input"
              />
            </div>
          </div>
          <div class="form-item">
            <label>归并理由：</label>
            <textarea
              v-model="currentModifyResult.reasoning"
              rows="3"
              placeholder="请输入归并理由"
              class="form-textarea"
            ></textarea>
          </div>
        </div>

        <!-- 已选择的项目 -->
        <div class="form-section">
          <h4>✅ 已选择的项目 ({{ selectedItems.length }}个)</h4>
          <div class="selected-items">
            <div v-for="(item, index) in selectedItems" :key="item.id" class="selected-item-card">
              <div class="item-info">
                <span class="item-type">{{ item.type === 'bill' ? '票据' : '银证' }}</span>
                <span class="item-no">{{ item.no }}</span>
                <span class="item-amount">¥{{ item.amount }}</span>
                <span class="item-summary">{{ item.summary }}</span>
              </div>
              <div class="item-actions">
                <button class="btn btn-small" @click="moveItemUp(index)" :disabled="index === 0">↑</button>
                <button class="btn btn-small" @click="moveItemDown(index)" :disabled="index === selectedItems.length - 1">↓</button>
                <button class="btn btn-small btn-danger" @click="removeItem(index)">移除</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 可添加的项目 -->
        <div class="form-section">
          <h4>➕ 可添加的项目</h4>
          <div class="available-items">
            <div v-for="item in availableItems" :key="item.id" class="available-item-card" @click="addItem(item)">
              <div class="item-info">
                <span class="item-type">{{ item.type === 'bill' ? '票据' : '银证' }}</span>
                <span class="item-no">{{ item.no }}</span>
                <span class="item-amount">¥{{ item.amount }}</span>
                <span class="item-summary">{{ item.summary }}</span>
              </div>
              <div class="similarity-score" v-if="item.similarity">
                相似度: {{ item.similarity }}%
              </div>
            </div>
          </div>
        </div>

        <!-- 预览效果 -->
        <div class="form-section">
          <h4>👁️ 预览效果</h4>
          <div class="preview-info">
            <div class="preview-item">
              <label>总金额：</label>
              <span class="preview-value">¥{{ totalAmount }}</span>
            </div>
            <div class="preview-item">
              <label>项目数量：</label>
              <span class="preview-value">{{ selectedItems.length }}个</span>
            </div>
            <div class="preview-item">
              <label>平均相似度：</label>
              <span class="preview-value">{{ averageSimilarity }}%</span>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="modal-footer">
        <button class="btn btn-default" @click="cancelModify">取消</button>
        <button class="btn btn-warning" @click="resetToOriginal">重置</button>
        <button
          class="btn btn-primary"
          @click="saveModifiedSuggestion"
          :disabled="selectedItems.length < 2"
        >
          保存并执行 ({{ selectedItems.length }}个项目)
        </button>
      </div>
    </h-modal>
  </div>
</template>

<script>
export default {
  name: 'AiMergeEngine',
  data() {
    return {
      selectedFunction: 'bill-merge',
      
      // 数据
      bills: [],
      receipts: [],
      
      // AI配置
      billMergeConfig: {
        similarityThreshold: 80, // 改为0-100范围
        analyzeContent: true,
        analyzeAmount: true,
        analyzeDate: true,
        analyzeCounterparty: true
      },
      receiptMergeConfig: {
        similarityThreshold: 80, // 改为0-100范围
        analyzeSummary: true,
        analyzeAmount: true,
        analyzeTime: true,
        analyzeAccount: true
      },
      mixedMergeConfig: {
        relationThreshold: 75, // 改为0-100范围
        crossTypeAnalysis: true,
        businessLogicAnalysis: true,
        timeSequenceAnalysis: true
      },
      
      // AI处理状态
      aiProcessing: false,
      processingStatus: '',
      processingProgress: 0,
      batchProcessing: false,
      
      // AI结果
      aiResults: [],

      // 修改建议对话框
      modifyDialogVisible: false,
      currentModifyResult: null,
      selectedItems: [], // 已选择的项目
      availableItems: [], // 可添加的项目
      originalItems: [] // 原始项目备份
    }
  },
  
  computed: {
    totalBillAmount() {
      return this.bills.reduce(function(sum, bill) { return sum + (bill.amount || 0) }, 0)
    },
    
    totalReceiptAmount() {
      return this.receipts.reduce(function(sum, receipt) { return sum + (receipt.amount || 0) }, 0)
    },
    
    billDateRange() {
      if (this.bills.length === 0) return '无数据'
      const dates = this.bills.map(function(b) {
        return new Date(b.billDate || b.bill_date)
      }).filter(function(d) {
        return !isNaN(d.getTime())
      }).sort()
      if (dates.length === 0) return '无数据'
      const start = dates[0].toLocaleDateString()
      const end = dates[dates.length - 1].toLocaleDateString()
      return start === end ? start : start + ' ~ ' + end
    },

    receiptDateRange() {
      if (this.receipts.length === 0) return '无数据'
      const dates = this.receipts.map(function(r) {
        return new Date(r.receiptsDate || r.receipts_date)
      }).filter(function(d) {
        return !isNaN(d.getTime())
      }).sort()
      if (dates.length === 0) return '无数据'
      const start = dates[0].toLocaleDateString()
      const end = dates[dates.length - 1].toLocaleDateString()
      return start === end ? start : start + ' ~ ' + end
    },
    
    averageConfidence() {
      if (this.aiResults.length === 0) return 0
      const total = this.aiResults.reduce(function(sum, r) { return sum + r.confidence }, 0)
      return Math.round(total / this.aiResults.length)
    },
    
    estimatedTimeSaving() {
      return this.aiResults.length * 5 // 每个归并建议预计节省5分钟
    },

    // 修改建议相关计算属性
    totalAmount() {
      return this.selectedItems.reduce((sum, item) => sum + (item.amount || 0), 0)
    },

    averageSimilarity() {
      if (this.selectedItems.length === 0) return 0
      const total = this.selectedItems.reduce((sum, item) => sum + (item.similarity || 85), 0)
      return Math.round(total / this.selectedItems.length)
    }
  },
  
  mounted() {
    this.loadData()
    // 确保阈值初始化为正确的格式
    this.normalizeThresholds()
  },


  
  methods: {
    // 格式化阈值显示
    formatThreshold(value) {
      return Number(value).toFixed(0) + '%'
    },

    // 获取阈值描述
    getThresholdDescription(value) {
      const threshold = Number(value)
      if (threshold <= 20) return '非常宽松'
      if (threshold <= 40) return '宽松'
      if (threshold <= 60) return '适中'
      if (threshold <= 80) return '严格'
      return '非常严格'
    },

    // 初始化时标准化所有阈值（只在初始化时使用）
    normalizeThresholds() {
      // 确保初始值是合理的（0-100范围）
      if (this.billMergeConfig.similarityThreshold > 100) this.billMergeConfig.similarityThreshold = 80
      if (this.receiptMergeConfig.similarityThreshold > 100) this.receiptMergeConfig.similarityThreshold = 80
      if (this.mixedMergeConfig.relationThreshold > 100) this.mixedMergeConfig.relationThreshold = 75
    },

    // 转换配置为后端API格式（0-100转为0-1）
    convertConfigForApi(config) {
      const converted = { ...config }
      if (converted.similarityThreshold !== undefined) {
        converted.similarityThreshold = converted.similarityThreshold / 100
      }
      if (converted.relationThreshold !== undefined) {
        converted.relationThreshold = converted.relationThreshold / 100
      }
      return converted
    },

    // 加载数据
    async loadData() {
      await Promise.all([
        this.loadBills(),
        this.loadReceipts()
      ])
    },
    
    async loadBills() {
      try {
        const response = await fetch('/api/merge-engine/documents/unmerged', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.bills = result.data || []
        }
      } catch (error) {
        console.error('加载票据失败:', error)
      }
    },

    async loadReceipts() {
      try {
        const response = await fetch('/api/merge-engine/receipts/unmerged', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.receipts = result.data || []
        }
      } catch (error) {
        console.error('加载银行回单失败:', error)
      }
    },

    // 功能切换
    onFunctionChange() {
      this.clearResults()
    },

    // 开始票据AI归并
    async startBillAiMerge() {
      if (this.bills.length === 0) {
        this.$Message.warning('没有可分析的票据数据')
        return
      }

      this.aiProcessing = true
      this.processingProgress = 0
      this.processingStatus = '正在分析票据内容...'

      try {
        // 模拟AI分析过程
        await this.simulateAiAnalysis()

        // 调用AI归并API
        const response = await fetch('/api/ai-merge/analyze-bills', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            bills: this.bills,
            config: this.convertConfigForApi(this.billMergeConfig)
          })
        })

        const result = await response.json()
        if (result.success) {
          this.aiResults = result.data || []
          this.$Message.success('AI分析完成，发现 ' + this.aiResults.length + ' 个归并建议')
        } else {
          // 如果API不存在，使用模拟数据
          this.aiResults = this.generateMockBillMergeResults()
          this.$Message.success('AI分析完成（演示模式），发现 ' + this.aiResults.length + ' 个归并建议')
        }
      } catch (error) {
        console.error('AI分析失败:', error)
        // 使用模拟数据作为fallback
        this.aiResults = this.generateMockBillMergeResults()
        this.$Message.success('AI分析完成（演示模式），发现 ' + this.aiResults.length + ' 个归并建议')
      } finally {
        this.aiProcessing = false
        this.processingProgress = 100
      }
    },

    // 开始银行回单AI归并
    async startReceiptAiMerge() {
      if (this.receipts.length === 0) {
        this.$Message.warning('没有可分析的银行回单数据')
        return
      }

      this.aiProcessing = true
      this.processingProgress = 0
      this.processingStatus = '正在分析银行回单内容...'

      try {
        await this.simulateAiAnalysis()

        const response = await fetch('/api/ai-merge/analyze-receipts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            receipts: this.receipts,
            config: this.convertConfigForApi(this.receiptMergeConfig)
          })
        })

        const result = await response.json()
        if (result.success) {
          this.aiResults = result.data || []
          this.$Message.success('AI分析完成，发现 ' + this.aiResults.length + ' 个归并建议')
        } else {
          this.aiResults = this.generateMockReceiptMergeResults()
          this.$Message.success('AI分析完成（演示模式），发现 ' + this.aiResults.length + ' 个归并建议')
        }
      } catch (error) {
        console.error('AI分析失败:', error)
        this.aiResults = this.generateMockReceiptMergeResults()
        this.$Message.success('AI分析完成（演示模式），发现 ' + this.aiResults.length + ' 个归并建议')
      } finally {
        this.aiProcessing = false
        this.processingProgress = 100
      }
    },

    // 开始混合AI归并
    async startMixedAiMerge() {
      if (this.bills.length === 0 && this.receipts.length === 0) {
        this.$Message.warning('没有可分析的数据')
        return
      }

      this.aiProcessing = true
      this.processingProgress = 0
      this.processingStatus = '正在进行跨类型关联分析...'

      try {
        await this.simulateAiAnalysis()

        const response = await fetch('/api/ai-merge/analyze-mixed', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            bills: this.bills,
            receipts: this.receipts,
            config: this.convertConfigForApi(this.mixedMergeConfig)
          })
        })

        const result = await response.json()
        if (result.success) {
          this.aiResults = result.data || []
          this.$Message.success('AI分析完成，发现 ' + this.aiResults.length + ' 个归并建议')
        } else {
          this.aiResults = this.generateMockMixedMergeResults()
          this.$Message.success('AI分析完成（演示模式），发现 ' + this.aiResults.length + ' 个归并建议')
        }
      } catch (error) {
        console.error('AI分析失败:', error)
        this.aiResults = this.generateMockMixedMergeResults()
        this.$Message.success('AI分析完成（演示模式），发现 ' + this.aiResults.length + ' 个归并建议')
      } finally {
        this.aiProcessing = false
        this.processingProgress = 100
      }
    },

    // 模拟AI分析过程
    async simulateAiAnalysis() {
      const steps = [
        '正在加载大语言模型...',
        '正在分析文档内容...',
        '正在计算相似度矩阵...',
        '正在识别业务关联...',
        '正在生成归并建议...'
      ]

      for (let i = 0; i < steps.length; i++) {
        this.processingStatus = steps[i]
        this.processingProgress = (i + 1) * 20
        await new Promise(function(resolve) { setTimeout(resolve, 800) })
      }
    },

    // 生成模拟票据归并结果
    generateMockBillMergeResults() {
      if (this.bills.length < 2) return []

      const results = []

      // 模拟相似票据归并
      if (this.bills.length >= 3) {
        results.push({
          confidence: 92,
          type: 'bill',
          groupName: '相似票据归并组-' + Date.now(),
          items: this.bills.slice(0, 3).map(function(bill) {
            return {
              id: bill.id,
              type: 'bill',
              no: bill.billNo,
              amount: bill.amount,
              summary: bill.summary
            }
          }),
          reasoning: '这些票据具有相同的交易方和相似的业务内容，AI分析认为它们属于同一笔业务的不同环节，建议归并处理。金额匹配度95%，时间关联度88%，内容相似度92%。'
        })
      }

      // 模拟金额匹配归并
      if (this.bills.length >= 2) {
        results.push({
          confidence: 85,
          type: 'bill',
          groupName: '金额匹配归并组-' + Date.now(),
          items: this.bills.slice(-2).map(function(bill) {
            return {
              id: bill.id,
              type: 'bill',
              no: bill.billNo,
              amount: bill.amount,
              summary: bill.summary
            }
          }),
          reasoning: '这两张票据的金额完全匹配，且交易时间相近，AI推断可能是同一笔交易的正本和副本，或者是相关的配套票据。'
        })
      }

      return results
    },

    // 生成模拟银行回单归并结果
    generateMockReceiptMergeResults() {
      if (this.receipts.length < 2) return []

      const results = []

      if (this.receipts.length >= 2) {
        results.push({
          confidence: 88,
          type: 'receipt',
          groupName: '银证归并组-' + Date.now(),
          items: this.receipts.slice(0, 2).map(function(receipt) {
            return {
              id: receipt.id,
              type: 'receipt',
              no: receipt.receiptsNo,
              amount: receipt.amount,
              summary: receipt.summary
            }
          }),
          reasoning: '这些银行回单显示了连续的转账操作，具有相同的收付款方和相似的摘要信息，AI判断它们属于同一业务流程的不同步骤。'
        })
      }

      return results
    },

    // 生成模拟混合归并结果
    generateMockMixedMergeResults() {
      const results = []

      if (this.bills.length > 0 && this.receipts.length > 0) {
        const mixedItems = []

        if (this.bills.length > 0) {
          mixedItems.push({
            id: this.bills[0].id,
            type: 'bill',
            no: this.bills[0].billNo,
            amount: this.bills[0].amount,
            summary: this.bills[0].summary
          })
        }

        if (this.receipts.length > 0) {
          mixedItems.push({
            id: this.receipts[0].id,
            type: 'receipt',
            no: this.receipts[0].receiptsNo,
            amount: this.receipts[0].amount,
            summary: this.receipts[0].summary
          })
        }

        results.push({
          confidence: 78,
          type: 'mixed-merge',
          items: mixedItems,
          reasoning: 'AI通过跨类型分析发现，这张票据和银行回单在金额、时间和业务内容上高度匹配，很可能是同一笔业务的票据凭证和银行流水记录，建议建立关联关系。'
        })
      }

      return results
    },

    // 获取置信度样式类
    getConfidenceClass(confidence) {
      if (confidence >= 85) return 'high'
      if (confidence >= 70) return 'medium'
      return 'low'
    },

    // 接受归并建议
    async acceptMerge(result) {
      try {
        // 构建请求数据
        const requestData = {
          type: result.type || 'bill', // 默认为票据类型
          items: result.items || result.billIds || result.receiptIds || [],
          groupName: result.groupName || result.name || '归并组',
          reasoning: result.reasoning || result.reason || 'AI智能归并建议'
        }

        console.log('接受归并建议，请求数据:', requestData)

        const response = await fetch('/api/ai-merge/accept-suggestion', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(requestData)
        })

        const apiResult = await response.json()
        if (apiResult.success) {
          this.$Message.success('✅ 归并建议已执行: ' + (apiResult.data.message || '归并完成'))
          // 重新加载数据
          this.loadBills()
          this.loadReceipts()
        } else {
          this.$Message.error('❌ 执行归并失败: ' + apiResult.msg)
        }

        // 从结果中移除已处理的项
        this.aiResults = this.aiResults.filter(function(r) { return r !== result })

      } catch (error) {
        console.error('执行归并失败:', error)
        this.$Message.error('❌ 执行归并失败: ' + error.message)
        // 仍然从结果中移除，避免重复处理
        this.aiResults = this.aiResults.filter(function(r) { return r !== result })
      }
    },

    // 修改归并建议
    modifyMerge(result) {
      this.currentModifyResult = { ...result }

      // 初始化选中的项目
      this.selectedItems = [...(result.items || [])]
      this.originalItems = [...(result.items || [])]

      // 计算可添加的项目
      this.calculateAvailableItems()

      this.modifyDialogVisible = true
    },

    // 计算可添加的项目
    calculateAvailableItems() {
      const selectedIds = this.selectedItems.map(item => item.id)
      const currentType = this.selectedItems.length > 0 ? this.selectedItems[0].type : 'bill'

      // 根据类型获取所有可用项目
      const allItems = currentType === 'bill' ? this.bills : this.receipts

      // 过滤掉已选择的项目
      this.availableItems = allItems
        .filter(item => !selectedIds.includes(item.id))
        .map(item => ({
          id: item.id,
          type: currentType,
          no: item.billNo || item.receiptsNo,
          amount: item.amount,
          summary: item.summary,
          similarity: this.calculateSimilarity(item) // 计算与已选项目的相似度
        }))
        .sort((a, b) => b.similarity - a.similarity) // 按相似度排序
    },

    // 计算相似度（简化版本）
    calculateSimilarity(item) {
      if (this.selectedItems.length === 0) return 85

      // 基于金额相似度
      const amounts = this.selectedItems.map(selected => selected.amount || 0)
      const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length
      const amountSimilarity = Math.max(0, 100 - Math.abs(item.amount - avgAmount) / avgAmount * 100)

      // 基于摘要相似度（简化）
      const summaries = this.selectedItems.map(selected => selected.summary || '')
      const summarySimilarity = summaries.some(summary =>
        summary && item.summary &&
        (summary.includes(item.summary.substring(0, 3)) || item.summary.includes(summary.substring(0, 3)))
      ) ? 80 : 60

      return Math.round((amountSimilarity * 0.6 + summarySimilarity * 0.4))
    },

    // 保存修改后的建议
    async saveModifiedSuggestion() {
      try {
        // 验证修改后的数据
        if (!this.currentModifyResult.groupName || this.currentModifyResult.groupName.trim() === '') {
          this.$Message.error('请输入归并组名称')
          return
        }

        if (!this.currentModifyResult.reasoning || this.currentModifyResult.reasoning.trim() === '') {
          this.$Message.error('请输入归并理由')
          return
        }

        if (this.selectedItems.length < 2) {
          this.$Message.error('至少需要选择2个项目进行归并')
          return
        }

        // 构建请求数据
        const requestData = {
          type: this.selectedItems[0].type || 'bill',
          items: this.selectedItems,
          groupName: this.currentModifyResult.groupName.trim(),
          reasoning: this.currentModifyResult.reasoning.trim()
        }

        console.log('保存修改后的归并建议，请求数据:', requestData)

        const response = await fetch('/api/ai-merge/accept-suggestion', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(requestData)
        })

        const apiResult = await response.json()
        if (apiResult.success) {
          this.$Message.success(`✅ 修改后的归并建议已执行: ${apiResult.data.message || '归并完成'}`)

          // 关闭对话框
          this.cancelModify()

          // 从结果中移除原始建议
          const originalResult = this.aiResults.find(r =>
            JSON.stringify(r.items || []) === JSON.stringify(this.originalItems || [])
          )
          if (originalResult) {
            this.aiResults = this.aiResults.filter(r => r !== originalResult)
          }

          // 重新加载数据
          this.loadBills()
          this.loadReceipts()

          // 显示修改摘要
          this.$Message.info(`📊 归并摘要: ${this.selectedItems.length}个项目，总金额¥${this.totalAmount}`)
        } else {
          this.$Message.error('❌ 执行修改后的归并失败: ' + apiResult.msg)
        }

      } catch (error) {
        console.error('保存修改后的归并建议失败:', error)
        this.$Message.error('❌ 保存失败: ' + error.message)
      }
    },

    // 添加项目到选中列表
    addItem(item) {
      this.selectedItems.push(item)
      this.calculateAvailableItems()
      this.$Message.success(`已添加 ${item.no}`)
    },

    // 从选中列表移除项目
    removeItem(index) {
      const item = this.selectedItems[index]
      this.selectedItems.splice(index, 1)
      this.calculateAvailableItems()
      this.$Message.info(`已移除 ${item.no}`)
    },

    // 上移项目
    moveItemUp(index) {
      if (index > 0) {
        const item = this.selectedItems.splice(index, 1)[0]
        this.selectedItems.splice(index - 1, 0, item)
      }
    },

    // 下移项目
    moveItemDown(index) {
      if (index < this.selectedItems.length - 1) {
        const item = this.selectedItems.splice(index, 1)[0]
        this.selectedItems.splice(index + 1, 0, item)
      }
    },

    // 重置到原始状态
    resetToOriginal() {
      this.selectedItems = [...this.originalItems]
      this.calculateAvailableItems()
      this.$Message.info('已重置到原始建议')
    },

    // 取消修改
    cancelModify() {
      this.modifyDialogVisible = false
      this.currentModifyResult = null
      this.selectedItems = []
      this.availableItems = []
      this.originalItems = []
    },

    // 拒绝归并建议
    rejectMerge(result) {
      this.aiResults = this.aiResults.filter(function(r) { return r !== result })
      this.$Message.info('已拒绝该归并建议')
    },

    // 批量接受所有建议
    async acceptAllMerges() {
      if (this.aiResults.length === 0) {
        this.$Message.warning('没有可处理的建议')
        return
      }

      this.batchProcessing = true

      try {
        for (const result of this.aiResults) {
          await this.acceptMerge(result)
          await new Promise(function(resolve) { setTimeout(resolve, 500) })
        }

        this.$Message.success('所有归并建议已批量处理完成')
        this.aiResults = []

      } catch (error) {
        this.$Message.error('批量处理失败: ' + error.message)
      } finally {
        this.batchProcessing = false
      }
    },

    // 清空结果
    clearResults() {
      this.aiResults = []
      this.processingProgress = 0
      this.processingStatus = ''
    }
  }
}
</script>

<style lang="less" scoped>
.ai-merge-engine {
  padding: 20px;
}

.subtitle {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.function-selector {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.selector-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.radio-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.radio-card {
  padding: 20px;
  background: #fff;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.radio-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.radio-card.active {
  border-color: #007bff;
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.2);
}

.radio-card.active::before {
  content: '✓';
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: #007bff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.card-icon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.card-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.merge-section {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: white;
}

.section-desc {
  color: #666;
  margin-bottom: 20px;
}

.ai-config {
  margin: 20px 0;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 6px;
}

.config-row {
  margin: 15px 0;
  display: flex;
  align-items: center;
  gap: 15px;
}

.config-row label {
  min-width: 100px;
  font-weight: bold;
  color: #333;
}

/* 滑动杆容器样式 */
.slider-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.slider-marks {
  display: flex;
  justify-content: space-between;
  width: 200px;
  margin-top: 5px;
  font-size: 12px;
  color: #999;
}

.slider-marks .mark {
  position: relative;
  text-align: center;
  width: 30px;
}

.threshold-value {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
  min-width: 30px;
  text-align: center;
}

.threshold-desc {
  font-size: 12px;
  color: #666;
  margin-left: 5px;
  font-style: italic;
}

.data-preview {
  margin: 20px 0;
  padding: 15px;
  background: #fafafa;
  border-radius: 6px;
}

.data-stats, .results-summary {
  margin: 10px 0;
  display: flex;
  gap: 20px;
}

.stat-item, .result-stat {
  padding: 5px 10px;
  background: white;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.mixed-stats {
  display: flex;
  gap: 20px;
  margin: 15px 0;
}

.stat-card {
  flex: 1;
  padding: 15px;
  background: white;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e8e8e8;
}

.ai-results {
  margin: 30px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.result-item {
  margin: 15px 0;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.confidence-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.confidence-badge.high { background: #d4edda; color: #155724; }
.confidence-badge.medium { background: #fff3cd; color: #856404; }
.confidence-badge.low { background: #f8d7da; color: #721c24; }

.merge-preview {
  margin: 15px 0;
}

.items-list {
  max-height: 200px;
  overflow-y: auto;
}

.merge-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  margin: 5px 0;
  background: #f8f9fa;
  border-radius: 4px;
}

.item-type {
  font-size: 16px;
}

.item-no {
  font-weight: bold;
  min-width: 100px;
}

.item-amount {
  color: #e74c3c;
  font-weight: bold;
  min-width: 80px;
}

.item-summary {
  color: #666;
  flex: 1;
}

.ai-reasoning {
  margin: 15px 0;
  padding: 10px;
  background: #e8f4fd;
  border-radius: 4px;
}

.result-actions {
  margin: 15px 0;
  display: flex;
  gap: 10px;
}

.batch-actions {
  margin: 20px 0;
  text-align: center;
}

.ai-processing {
  margin: 30px 0;
  padding: 20px;
  text-align: center;
  background: #f0f9ff;
  border-radius: 8px;
}

.processing-animation {
  margin-bottom: 20px;
}

.ai-brain {
  font-size: 48px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.processing-text {
  margin: 10px 0;
  font-size: 16px;
  color: #409eff;
}

/* 修改建议对话框样式 */
.modify-suggestion-form {
  padding: 10px 0;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 25px;
  padding: 15px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background: #fafafa;
}

.form-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-item {
  margin-bottom: 15px;
}

.form-item.half {
  flex: 1;
}

.form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

/* 原生输入框样式 */
.form-input, .form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  background-color: #fff;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  box-sizing: border-box;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-input:hover, .form-textarea:hover {
  border-color: #c0c4cc;
}

.form-input::placeholder, .form-textarea::placeholder {
  color: #c0c4cc;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
  line-height: 1.5;
}

/* 原生按钮样式 */
.btn {
  display: inline-block;
  padding: 8px 16px;
  margin: 0 4px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
  color: #606266;
  user-select: none;
}

.btn:hover {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.btn:active {
  background-color: #3a8ee6;
  border-color: #3a8ee6;
  color: #fff;
}

.btn:disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.btn:disabled:hover {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

/* 按钮尺寸 */
.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

/* 按钮类型 */
.btn-primary {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

.btn-primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.btn-primary:active {
  background-color: #3a8ee6;
  border-color: #3a8ee6;
}

.btn-primary:disabled {
  background-color: #a0cfff;
  border-color: #a0cfff;
}

.btn-danger {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: #fff;
}

.btn-danger:hover {
  background-color: #f78989;
  border-color: #f78989;
}

.btn-danger:active {
  background-color: #dd6161;
  border-color: #dd6161;
}

.btn-warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: #fff;
}

.btn-warning:hover {
  background-color: #ebb563;
  border-color: #ebb563;
}

.btn-warning:active {
  background-color: #cf9236;
  border-color: #cf9236;
}

.btn-default {
  background-color: #fff;
  border-color: #dcdfe6;
  color: #606266;
}

.btn-default:hover {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
  color: #409eff;
}

/* 模态框footer样式 */
.modal-footer {
  text-align: right;
  padding: 10px 0;
}

.modal-footer .btn {
  margin-left: 8px;
}

/* 已选择项目样式 */
.selected-items {
  max-height: 200px;
  overflow-y: auto;
}

.selected-item-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 8px;
  background: #e8f4fd;
  border: 1px solid #409eff;
  border-radius: 6px;
  transition: all 0.3s;
}

.selected-item-card:hover {
  background: #d4edda;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 可添加项目样式 */
.available-items {
  max-height: 250px;
  overflow-y: auto;
}

.available-item-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  margin-bottom: 6px;
  background: #f8f9fa;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.available-item-card:hover {
  background: #fff3cd;
  border-color: #ffc107;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.item-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.item-type {
  background: #409eff;
  color: white;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  min-width: 40px;
  text-align: center;
}

.item-no {
  font-weight: bold;
  color: #333;
  min-width: 120px;
}

.item-amount {
  color: #f56c6c;
  font-weight: bold;
  min-width: 80px;
}

.item-summary {
  color: #666;
  font-size: 12px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-actions {
  display: flex;
  gap: 5px;
}

.similarity-score {
  background: #28a745;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  white-space: nowrap;
}

/* 预览效果样式 */
.preview-info {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #e8f5e8;
  border-radius: 6px;
  border: 1px solid #28a745;
}

.preview-item label {
  margin: 0;
  font-weight: bold;
  color: #333;
}

.preview-value {
  color: #28a745;
  font-weight: bold;
}
</style>
