<template>
  <div class="ai-settings">
    <h-card>
      <h2 slot="title">⚙️ AI配置管理</h2>
      <p slot="title" class="subtitle">配置OpenAI兼容的API接口，启用AI智能功能</p>
      
      <!-- AI服务状态 -->
      <div class="ai-status">
        <h3>🔌 AI服务状态</h3>
        <div class="status-card" :class="aiStatus.connected ? 'connected' : 'disconnected'">
          <div class="status-icon">
            {{ aiStatus.connected ? '✅' : '❌' }}
          </div>
          <div class="status-content">
            <h4>{{ aiStatus.connected ? 'AI服务已连接' : 'AI服务未连接' }}</h4>
            <p>{{ aiStatus.message }}</p>
          </div>
          <div class="status-actions">
            <h-button type="primary" size="small" @click="testConnection" :loading="testing">
              🔍 测试连接
            </h-button>
          </div>
        </div>
      </div>
      
      <!-- 配置说明 -->
      <div class="config-help">
        <h3>📋 配置说明</h3>
        <div class="help-content">
          <p><strong>常用服务商配置示例：</strong></p>
          <ul>
            <li><strong>OpenAI官方：</strong> https://api.openai.com/v1</li>
            <li><strong>DeepSeek：</strong> https://api.deepseek.com/v1</li>
            <li><strong>通义千问：</strong> https://dashscope.aliyuncs.com/compatible-mode/v1</li>
          </ul>
          <p><strong>API密钥格式：</strong> 通常以 sk- 开头，请从对应服务商官网获取</p>
        </div>
      </div>

      <!-- 基础配置 -->
      <div class="config-section">
        <h3>🛠️ AI配置</h3>

        <div class="form-item">
          <label class="form-label">启用AI功能:</label>
          <label class="switch">
            <input type="checkbox" v-model="config.enabled" @change="onEnabledChange">
            <span class="slider"></span>
          </label>
          <span class="form-tip">开启后可使用AI智能归并和关联功能</span>
        </div>

        <div class="form-item">
          <label class="form-label">API地址:</label>
          <div class="input-group">
            <input
              v-model="config.baseUrl"
              type="text"
              placeholder="https://api.openai.com/v1"
              class="native-input">
            <div class="quick-urls">
              <span class="quick-label">快速选择:</span>
              <h-button size="small" @click="setQuickUrl('https://api.openai.com/v1')">OpenAI</h-button>
              <h-button size="small" @click="setQuickUrl('https://api.deepseek.com/v1')">DeepSeek</h-button>
              <h-button size="small" @click="setQuickUrl('https://dashscope.aliyuncs.com/compatible-mode/v1')">通义千问</h-button>
            </div>
          </div>
          <span class="form-tip">OpenAI兼容的API接口地址</span>
        </div>

        <div class="form-item">
          <label class="form-label">API密钥:</label>
          <input
            v-model="config.apiKey"
            type="password"
            placeholder="请输入API密钥"
            class="native-input">
          <span class="form-tip">请妥善保管您的API密钥</span>
        </div>

        <div class="form-item">
          <label class="form-label">连接测试:</label>
          <h-button type="primary" @click="testConnection" :loading="testing">
            🔍 测试连接
          </h-button>
          <span class="form-tip">验证API配置是否正确</span>
        </div>

        <div class="current-config">
          <h4>当前配置:</h4>
          <p>启用状态: {{ config.enabled ? '已启用' : '未启用' }}</p>
          <p>API地址: {{ config.baseUrl || '未设置' }}</p>
          <p>API密钥: {{ config.apiKey ? '***已设置***' : '未设置' }}</p>
        </div>
      </div>
      

      
      <!-- 操作按钮 -->
      <div class="actions">
        <h-button type="primary" size="large" @click="saveConfig" :loading="saving">
          💾 保存配置
        </h-button>
        <h-button size="large" @click="resetConfig">
          🔄 重置配置
        </h-button>
        <h-button type="success" size="large" @click="testAiFunction" :loading="testingFunction">
          🧪 测试AI功能
        </h-button>
      </div>
      

    </h-card>
  </div>
</template>

<script>
export default {
  name: 'AiSettings',
  data() {
    return {
      config: {
        enabled: false,
        baseUrl: 'https://api.openai.com/v1',
        apiKey: ''
      },
      
      aiStatus: {
        connected: false,
        message: '未配置AI服务'
      },
      
      testing: false,
      saving: false,
      testingFunction: false
    }
  },
  
  mounted() {
    this.loadConfig()
  },
  
  methods: {
    // 加载配置
    async loadConfig() {
      try {
        const response = await fetch('/api/ai-config/get', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success && result.data) {
          this.config = { ...this.config, ...result.data }
        }
      } catch (error) {
        console.error('加载AI配置失败:', error)
      }
    },
    
    // 保存配置
    async saveConfig() {
      this.saving = true
      try {
        const response = await fetch('/api/ai-config/save', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(this.config)
        })
        
        const result = await response.json()
        if (result.success) {
          this.$Message.success('AI配置保存成功')
        } else {
          this.$Message.error('保存失败: ' + result.message)
        }
      } catch (error) {
        this.$Message.error('保存失败: ' + error.message)
      } finally {
        this.saving = false
      }
    },
    
    // 测试连接
    async testConnection() {
      this.testing = true
      try {
        const response = await fetch('/api/ai-config/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(this.config)
        })
        
        const result = await response.json()
        if (result.success) {
          this.aiStatus.connected = true
          this.aiStatus.message = 'AI服务连接正常'
          this.$Message.success('AI服务连接测试成功')
        } else {
          this.aiStatus.connected = false
          this.aiStatus.message = result.message || '连接失败'
          this.$Message.error('连接测试失败: ' + result.message)
        }
      } catch (error) {
        this.aiStatus.connected = false
        this.aiStatus.message = '连接异常: ' + error.message
        this.$Message.error('连接测试异常: ' + error.message)
      } finally {
        this.testing = false
      }
    },
    
    // 测试AI功能
    async testAiFunction() {
      this.testingFunction = true
      try {
        const response = await fetch('/api/ai-config/test-function', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(this.config)
        })
        
        const result = await response.json()
        if (result.success) {
          this.$Message.success('AI功能测试成功: ' + result.data)
        } else {
          this.$Message.error('AI功能测试失败: ' + result.message)
        }
      } catch (error) {
        this.$Message.error('AI功能测试异常: ' + error.message)
      } finally {
        this.testingFunction = false
      }
    },
    
    // 重置配置
    resetConfig() {
      this.$Confirm('确定要重置所有AI配置吗？此操作不可撤销。').then(() => {
        this.config = {
          enabled: false,
          baseUrl: 'https://api.openai.com/v1',
          apiKey: ''
        }
        this.$Message.success('配置已重置')
      })
    },
    
    // 启用状态变化
    onEnabledChange(enabled) {
      if (enabled && !this.config.apiKey) {
        this.$Message.warning('请先配置API密钥')
      }
    },

    // 快速设置API地址
    setQuickUrl(url) {
      this.config.baseUrl = url
      this.$Message.success('API地址已设置为: ' + url)
    }
  }
}
</script>

<style lang="less" scoped>
.ai-settings {
  padding: 20px;
}

.subtitle {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.ai-status {
  margin: 20px 0;
}

.status-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  border: 2px solid;
  
  &.connected {
    border-color: #67c23a;
    background: #f0f9ff;
  }
  
  &.disconnected {
    border-color: #f56c6c;
    background: #fef0f0;
  }
}

.status-icon {
  font-size: 32px;
  margin-right: 15px;
}

.status-content {
  flex: 1;
}

.status-content h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.status-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.config-help {
  margin: 20px 0;
  padding: 20px;
  background: #e8f4fd;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.config-help h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.help-content p {
  margin: 10px 0;
  color: #666;
  font-size: 14px;
}

.help-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.help-content li {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.config-section {
  margin: 30px 0;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.config-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.form-item {
  margin: 20px 0;
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.form-label {
  min-width: 100px;
  font-weight: bold;
  color: #333;
  padding-top: 8px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.current-config {
  margin: 30px 0;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.current-config h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.current-config p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.native-input {
  width: 400px;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  background-color: #fff;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.native-input:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.native-input::placeholder {
  color: #c0c4cc;
}

/* 自定义开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 22px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #dcdfe6;
  transition: 0.3s;
  border-radius: 22px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #409eff;
}

input:checked + .slider:before {
  transform: translateX(22px);
}

.form-tip {
  margin-left: 10px;
  color: #999;
  font-size: 12px;
}

.quick-urls {
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-label {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.actions {
  margin: 30px 0;
  text-align: center;
}

.actions .h-btn {
  margin: 0 10px;
}
</style>
