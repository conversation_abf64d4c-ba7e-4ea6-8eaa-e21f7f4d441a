<template>
  <div class="ai-relation-engine">
    <h-card>
      <h2 slot="title">🔗 AI智能关联引擎</h2>
      <p slot="title" class="subtitle">利用大语言模型深度理解业务逻辑，自动识别票据与银行回单的关联关系</p>
      
      <!-- 关联模式选择 -->
      <div class="relation-mode-selector">
        <h3 class="selector-title">🎯 关联分析模式</h3>
        <div class="radio-cards">
          <div class="radio-card" :class="{active: relationMode === 'smart-match'}" @click="relationMode = 'smart-match'; onModeChange()">
            <div class="card-icon">🧠</div>
            <div class="card-title">智能匹配模式</div>
            <div class="card-desc">基于金额、时间、交易方等基础信息进行快速智能匹配</div>
          </div>
          <div class="radio-card" :class="{active: relationMode === 'deep-analysis'}" @click="relationMode = 'deep-analysis'; onModeChange()">
            <div class="card-icon">🔍</div>
            <div class="card-title">深度分析模式</div>
            <div class="card-desc">利用AI深度学习算法分析复杂业务场景和关联关系</div>
          </div>
          <div class="radio-card" :class="{active: relationMode === 'business-logic'}" @click="relationMode = 'business-logic'; onModeChange()">
            <div class="card-icon">💼</div>
            <div class="card-title">业务逻辑模式</div>
            <div class="card-desc">基于财务业务规则和逻辑进行精准关联分析</div>
          </div>
        </div>
      </div>

      <!-- 智能匹配模式 -->
      <div v-if="relationMode === 'smart-match'" class="analysis-section">
        <h3>🧠 智能匹配模式</h3>
        <p class="mode-desc">基于金额、时间、交易方等基础信息进行快速智能匹配</p>
        
        <div class="ai-config">
          <h4>匹配参数配置</h4>
          <div class="config-grid">
            <div class="config-item">
              <label>金额匹配权重:</label>
              <h-slider v-model="smartMatchConfig.amountWeight" :min="0" :max="100" :step="10" style="width: 150px;"></h-slider>
              <span class="weight-value">{{ formatThreshold(smartMatchConfig.amountWeight) }}</span>
            </div>
            <div class="config-item">
              <label>时间匹配权重:</label>
              <h-slider v-model="smartMatchConfig.timeWeight" :min="0" :max="100" :step="10" style="width: 150px;"></h-slider>
              <span class="weight-value">{{ formatThreshold(smartMatchConfig.timeWeight) }}</span>
            </div>
            <div class="config-item">
              <label>交易方权重:</label>
              <h-slider v-model="smartMatchConfig.counterpartyWeight" :min="0" :max="100" :step="10" style="width: 150px;"></h-slider>
              <span class="weight-value">{{ formatThreshold(smartMatchConfig.counterpartyWeight) }}</span>
            </div>
            <div class="config-item">
              <label>匹配阈值:</label>
              <h-slider v-model="smartMatchConfig.threshold" :min="0" :max="100" :step="10" style="width: 150px;"></h-slider>
              <span class="weight-value">{{ formatThreshold(smartMatchConfig.threshold) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 深度分析模式 -->
      <div v-if="relationMode === 'deep-analysis'" class="analysis-section">
        <h3>🔍 深度分析模式</h3>
        <p class="mode-desc">利用NLP技术深度分析票据和银行回单的文本内容，识别语义关联</p>
        
        <div class="ai-config">
          <h4>深度分析配置</h4>
          <div class="config-options">
            <h-checkbox v-model="deepAnalysisConfig.semanticAnalysis">语义相似度分析</h-checkbox>
            <h-checkbox v-model="deepAnalysisConfig.entityExtraction">实体识别匹配</h-checkbox>
            <h-checkbox v-model="deepAnalysisConfig.contextAnalysis">上下文关联分析</h-checkbox>
            <h-checkbox v-model="deepAnalysisConfig.patternRecognition">模式识别</h-checkbox>
          </div>
          <div class="config-item">
            <label>语义相似度阈值:</label>
            <h-slider v-model="deepAnalysisConfig.semanticThreshold" :min="0" :max="100" :step="10" style="width: 200px;"></h-slider>
            <span class="weight-value">{{ formatThreshold(deepAnalysisConfig.semanticThreshold) }}</span>
          </div>
        </div>
      </div>

      <!-- 业务逻辑模式 -->
      <div v-if="relationMode === 'business-logic'" class="analysis-section">
        <h3>💼 业务逻辑模式</h3>
        <p class="mode-desc">基于财务业务规则和行业知识，智能推断票据与银行回单的业务关联</p>
        
        <div class="ai-config">
          <h4>业务规则配置</h4>
          <div class="business-rules">
            <div class="rule-category">
              <h5>📋 票据类型规则</h5>
              <h-checkbox v-model="businessLogicConfig.invoiceRules">发票关联规则</h-checkbox>
              <h-checkbox v-model="businessLogicConfig.receiptRules">收据关联规则</h-checkbox>
              <h-checkbox v-model="businessLogicConfig.contractRules">合同关联规则</h-checkbox>
            </div>
            <div class="rule-category">
              <h5>🏦 银行业务规则</h5>
              <h-checkbox v-model="businessLogicConfig.transferRules">转账业务规则</h-checkbox>
              <h-checkbox v-model="businessLogicConfig.paymentRules">付款业务规则</h-checkbox>
              <h-checkbox v-model="businessLogicConfig.depositRules">存款业务规则</h-checkbox>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据概览 -->
      <div class="data-overview">
        <h3>📊 数据概览</h3>
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-icon">📄</div>
            <div class="card-content">
              <h4>待关联票据</h4>
              <p class="card-number">{{ unrelatedBills.length }}</p>
              <p class="card-amount">¥{{ totalBillAmount.toLocaleString() }}</p>
            </div>
          </div>
          <div class="overview-card">
            <div class="card-icon">🏦</div>
            <div class="card-content">
              <h4>待关联银行回单</h4>
              <p class="card-number">{{ unrelatedReceipts.length }}</p>
              <p class="card-amount">¥{{ totalReceiptAmount.toLocaleString() }}</p>
            </div>
          </div>
          <div class="overview-card">
            <div class="card-icon">🔗</div>
            <div class="card-content">
              <h4>已有关联</h4>
              <p class="card-number">{{ existingRelations.length }}</p>
              <p class="card-desc">个关联关系</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 开始分析按钮 -->
      <div class="analysis-actions">
        <h-button type="primary" size="large" @click="startAiRelationAnalysis" :loading="aiAnalyzing">
          🤖 开始AI智能关联分析
        </h-button>
        <h-button v-if="relationResults.length > 0" @click="clearResults">
          🗑️ 清空结果
        </h-button>
      </div>

      <!-- AI分析进度 -->
      <div v-if="aiAnalyzing" class="analysis-progress">
        <div class="progress-animation">
          <div class="ai-icon">🤖</div>
          <div class="progress-text">{{ analysisStatus }}</div>
        </div>
        <h-progress :percent="analysisProgress" :show-text="true"></h-progress>
        <div class="progress-details">
          <span>已分析: {{ processedCount }}/{{ totalCount }}</span>
          <span>发现关联: {{ foundRelations }}</span>
        </div>
      </div>

      <!-- 关联分析结果 -->
      <div v-if="relationResults.length > 0" class="relation-results">
        <h3>🎯 AI关联分析结果</h3>
        <div class="results-summary">
          <div class="summary-item">
            <span class="summary-label">发现关联:</span>
            <span class="summary-value">{{ relationResults.length }} 个</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">平均置信度:</span>
            <span class="summary-value">{{ averageConfidence }}%</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">预计节省时间:</span>
            <span class="summary-value">{{ estimatedTimeSaving }} 分钟</span>
          </div>
        </div>

        <!-- 关联结果列表 -->
        <div class="results-list">
          <div v-for="(result, index) in relationResults" :key="index" class="relation-result-item">
            <div class="result-header">
              <h4>关联建议 #{{ index + 1 }}</h4>
              <div class="confidence-indicator" :class="getConfidenceLevel(result.confidence)">
                {{ result.confidence }}%
              </div>
            </div>
            
            <div class="relation-pair">
              <div class="relation-item bill-item">
                <div class="item-header">
                  <span class="item-icon">📄</span>
                  <span class="item-type">票据</span>
                </div>
                <div class="item-details">
                  <p><strong>编号:</strong> {{ result.bill && result.bill.billNo || '未知' }}</p>
                  <p><strong>金额:</strong> ¥{{ result.bill && result.bill.amount ? result.bill.amount.toLocaleString() : '0' }}</p>
                  <p><strong>摘要:</strong> {{ result.bill && result.bill.summary || '无摘要' }}</p>
                </div>
              </div>
              
              <div class="relation-arrow">
                <div class="arrow-icon">🔗</div>
                <div class="relation-type">{{ getRelationTypeText(result.relationType) }}</div>
              </div>
              
              <div class="relation-item receipt-item">
                <div class="item-header">
                  <span class="item-icon">🏦</span>
                  <span class="item-type">银行回单</span>
                </div>
                <div class="item-details">
                  <p><strong>编号:</strong> {{ result.receipt && result.receipt.receiptsNo || '未知' }}</p>
                  <p><strong>金额:</strong> ¥{{ result.receipt && result.receipt.amount ? result.receipt.amount.toLocaleString() : '0' }}</p>
                  <p><strong>摘要:</strong> {{ result.receipt && result.receipt.summary || '无摘要' }}</p>
                </div>
              </div>
            </div>
            
            <div class="ai-analysis">
              <h5>🤖 AI分析说明</h5>
              <p>{{ result.reasoning }}</p>
              <div class="analysis-factors">
                <span v-if="result.factors && result.factors.amountMatch" class="factor-tag amount">金额匹配</span>
                <span v-if="result.factors && result.factors.timeMatch" class="factor-tag time">时间关联</span>
                <span v-if="result.factors && result.factors.semanticMatch" class="factor-tag semantic">语义相似</span>
                <span v-if="result.factors && result.factors.businessLogic" class="factor-tag business">业务逻辑</span>
              </div>
            </div>
            
            <div class="result-actions">
              <h-button type="success" size="small" @click="acceptRelation(result)">
                ✅ 建立关联
              </h-button>
              <h-button type="warning" size="small" @click="adjustRelation(result)">
                ⚙️ 调整参数
              </h-button>
              <h-button type="danger" size="small" @click="rejectRelation(result)">
                ❌ 拒绝建议
              </h-button>
            </div>
          </div>
        </div>

        <!-- 批量操作 -->
        <div class="batch-operations">
          <h-button type="primary" @click="acceptAllRelations" :loading="batchProcessing">
            ✅ 批量建立所有关联
          </h-button>
          <h-button type="warning" @click="acceptHighConfidenceRelations" :loading="batchProcessing">
            🎯 仅建立高置信度关联
          </h-button>
        </div>
      </div>
    </h-card>
  </div>
</template>

<script>
export default {
  name: 'AiRelationEngine',
  data() {
    return {
      relationMode: 'smart-match',
      
      // 配置参数
      smartMatchConfig: {
        amountWeight: 40,    // 改为0-100范围
        timeWeight: 30,      // 改为0-100范围
        counterpartyWeight: 30, // 改为0-100范围
        threshold: 75        // 改为0-100范围
      },
      deepAnalysisConfig: {
        semanticAnalysis: true,
        entityExtraction: true,
        contextAnalysis: true,
        patternRecognition: false,
        semanticThreshold: 80 // 改为0-100范围
      },
      businessLogicConfig: {
        invoiceRules: true,
        receiptRules: true,
        contractRules: false,
        transferRules: true,
        paymentRules: true,
        depositRules: false
      },
      
      // 数据
      unrelatedBills: [],
      unrelatedReceipts: [],
      existingRelations: [],
      
      // AI分析状态
      aiAnalyzing: false,
      analysisStatus: '',
      analysisProgress: 0,
      processedCount: 0,
      totalCount: 0,
      foundRelations: 0,
      
      // 结果
      relationResults: [],
      batchProcessing: false
    }
  },

  computed: {
    totalBillAmount() {
      return this.unrelatedBills.reduce(function(sum, bill) { return sum + (bill.amount || 0) }, 0)
    },

    totalReceiptAmount() {
      return this.unrelatedReceipts.reduce(function(sum, receipt) { return sum + (receipt.amount || 0) }, 0)
    },

    averageConfidence() {
      if (this.relationResults.length === 0) return 0
      const total = this.relationResults.reduce(function(sum, r) { return sum + r.confidence }, 0)
      return Math.round(total / this.relationResults.length)
    },

    estimatedTimeSaving() {
      return this.relationResults.length * 3 // 每个关联预计节省3分钟
    }
  },

  mounted() {
    this.loadData()
  },

  methods: {
    // 格式化阈值显示
    formatThreshold(value) {
      return Number(value).toFixed(0) + '%'
    },

    // 转换配置为后端API格式（0-100转为0-1）
    convertConfigForApi(config) {
      const converted = { ...config }
      // 转换权重
      if (converted.amountWeight !== undefined) {
        converted.amountWeight = converted.amountWeight / 100
      }
      if (converted.timeWeight !== undefined) {
        converted.timeWeight = converted.timeWeight / 100
      }
      if (converted.counterpartyWeight !== undefined) {
        converted.counterpartyWeight = converted.counterpartyWeight / 100
      }
      // 转换阈值
      if (converted.threshold !== undefined) {
        converted.threshold = converted.threshold / 100
      }
      if (converted.semanticThreshold !== undefined) {
        converted.semanticThreshold = converted.semanticThreshold / 100
      }
      return converted
    },

    // 补充关联结果的详细信息
    enrichRelationResults(relations) {
      console.log('开始补充关联结果详细信息，关联数量:', relations.length)
      console.log('可用票据数量:', this.unrelatedBills.length, '可用银行回单数量:', this.unrelatedReceipts.length)

      return relations.map(relation => {
        // 查找对应的票据
        const bill = this.unrelatedBills.find(b => b.id === relation.billId)
        // 查找对应的银行回单
        const receipt = this.unrelatedReceipts.find(r => r.id === relation.receiptId)

        console.log(`关联 ${relation.billId} -> ${relation.receiptId}:`,
          '票据找到:', !!bill, '银行回单找到:', !!receipt)

        if (bill) {
          console.log('票据详情:', bill.billNo, bill.amount, bill.summary)
        }
        if (receipt) {
          console.log('银行回单详情:', receipt.receiptsNo, receipt.amount, receipt.summary)
        }

        return {
          ...relation,
          bill: bill || {
            id: relation.billId,
            billNo: '票据#' + relation.billId,
            amount: 0,
            summary: '票据信息未找到'
          },
          receipt: receipt || {
            id: relation.receiptId,
            receiptsNo: '银证#' + relation.receiptId,
            amount: 0,
            summary: '银行回单信息未找到'
          }
        }
      })
    },

    // 加载数据
    async loadData() {
      await Promise.all([
        this.loadUnrelatedBills(),
        this.loadUnrelatedReceipts(),
        this.loadExistingRelations()
      ])
    },

    async loadUnrelatedBills() {
      try {
        const response = await fetch('/api/merge-engine/documents/unmerged', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.unrelatedBills = result.data || []
        } else {
          throw new Error(result.message || '加载未关联票据失败')
        }
      } catch (error) {
        console.error('加载未关联票据失败:', error)
        this.$Message.error('加载未关联票据失败: ' + error.message)
        this.unrelatedBills = []
      }
    },

    async loadUnrelatedReceipts() {
      try {
        const response = await fetch('/api/merge-engine/receipts/unmerged', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.unrelatedReceipts = result.data || []
        } else {
          throw new Error(result.message || '加载未关联银行回单失败')
        }
      } catch (error) {
        console.error('加载未关联银行回单失败:', error)
        this.$Message.error('加载未关联银行回单失败: ' + error.message)
        this.unrelatedReceipts = []
      }
    },

    async loadExistingRelations() {
      try {
        const response = await fetch('/api/relation/list', {
          credentials: 'include'
        })

        if (response.status === 404) {
          // API不存在，直接使用模拟数据
          console.log('关联API不存在，使用模拟数据')
          this.existingRelations = this.generateMockRelations()
          return
        }

        const result = await response.json()
        if (result.success) {
          this.existingRelations = result.data || []
        } else {
          // 使用模拟数据
          this.existingRelations = this.generateMockRelations()
        }
      } catch (error) {
        console.error('加载已有关联失败:', error)
        // 使用模拟数据作为fallback
        this.existingRelations = this.generateMockRelations()
      }
    },



    // 模式切换
    onModeChange() {
      this.clearResults()
    },

    // 开始AI关联分析
    async startAiRelationAnalysis() {
      // 确保数据已加载
      if (this.unrelatedBills.length === 0 || this.unrelatedReceipts.length === 0) {
        console.log('数据未加载，重新加载数据...')
        await this.loadData()
      }

      console.log('开始AI分析，票据数量:', this.unrelatedBills.length, '银行回单数量:', this.unrelatedReceipts.length)

      if (this.unrelatedBills.length === 0 || this.unrelatedReceipts.length === 0) {
        this.$Message.warning('需要同时有票据和银行回单数据才能进行关联分析')
        return
      }

      this.aiAnalyzing = true
      this.analysisProgress = 0
      this.processedCount = 0
      this.foundRelations = 0
      this.totalCount = this.unrelatedBills.length * this.unrelatedReceipts.length
      this.analysisStatus = '正在初始化AI关联分析引擎...'

      try {
        // 模拟AI分析过程
        console.log('开始模拟AI分析过程...')
        await this.simulateRelationAnalysis()
        console.log('模拟AI分析过程完成')

        // 调用AI关联分析API
        console.log('开始调用AI关联分析API...')
        const response = await fetch('/api/ai-relation/analyze', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            bills: this.unrelatedBills,
            receipts: this.unrelatedReceipts,
            mode: this.relationMode,
            config: this.getCurrentConfig()
          })
        })

        console.log('API调用完成，状态码:', response.status)

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        if (response.status === 200) {
          console.log('API返回200，尝试解析响应...')
          try {
            const result = await response.json()
            console.log('API响应数据:', result)
            if (result.success && result.data) {
              // 检查数据结构，API返回的数据在data.relations中
              let relationData = result.data
              if (relationData.relations && Array.isArray(relationData.relations)) {
                this.relationResults = this.enrichRelationResults(relationData.relations)
                console.log('使用API返回的关联结果 (从data.relations):', this.relationResults)
                this.$Message.success('AI关联分析完成，发现 ' + this.relationResults.length + ' 个关联建议')
              } else if (Array.isArray(relationData)) {
                this.relationResults = this.enrichRelationResults(relationData)
                console.log('使用API返回的关联结果 (直接数组):', this.relationResults)
                this.$Message.success('AI关联分析完成，发现 ' + this.relationResults.length + ' 个关联建议')
              } else {
                console.log('API数据结构不符合预期，数据结构:', relationData)
                throw new Error('API返回的数据结构不符合预期')
              }
            } else {
              throw new Error(result.message || 'AI关联分析失败')
            }
          } catch (jsonError) {
            console.error('解析API响应失败:', jsonError)
            throw new Error('解析API响应失败: ' + jsonError.message)
          }
        }
      } catch (error) {
        console.error('AI关联分析失败:', error)
        this.$Message.error('AI关联分析失败: ' + error.message)
        this.relationResults = []
      } finally {
        this.aiAnalyzing = false
        this.analysisProgress = 100

        // 确保relationResults始终是数组
        if (!Array.isArray(this.relationResults)) {
          this.relationResults = []
        }
      }
    },

    // 模拟关联分析过程
    async simulateRelationAnalysis() {
      console.log('进入simulateRelationAnalysis方法')
      const steps = [
        '正在加载大语言模型...',
        '正在分析票据内容...',
        '正在分析银行回单内容...',
        '正在计算关联相似度...',
        '正在应用业务规则...',
        '正在生成关联建议...'
      ]

      for (let i = 0; i < steps.length; i++) {
        console.log(`执行步骤 ${i + 1}/${steps.length}: ${steps[i]}`)
        this.analysisStatus = steps[i]
        this.analysisProgress = (i + 1) * 15
        this.processedCount = Math.floor(this.totalCount * (i + 1) / steps.length)
        this.foundRelations = Math.floor(this.processedCount * 0.1) // 假设10%的匹配率
        await new Promise(function(resolve) { setTimeout(resolve, 1000) })
      }
      console.log('simulateRelationAnalysis方法完成')
    },

    // 获取当前配置
    getCurrentConfig() {
      let config
      switch (this.relationMode) {
        case 'smart-match':
          config = this.smartMatchConfig
          break
        case 'deep-analysis':
          config = this.deepAnalysisConfig
          break
        case 'business-logic':
          config = this.businessLogicConfig
          break
        default:
          config = this.smartMatchConfig
      }
      // 转换为API格式
      return this.convertConfigForApi(config)
    },



    // 获取置信度等级
    getConfidenceLevel(confidence) {
      if (confidence >= 85) return 'high-confidence'
      if (confidence >= 70) return 'medium-confidence'
      return 'low-confidence'
    },

    // 获取关联类型文本
    getRelationTypeText(type) {
      const typeMap = {
        'PAYMENT': '付款关联',
        'RECEIPT': '收款关联',
        'TRANSFER': '转账关联',
        'REFUND': '退款关联',
        'OTHER': '其他关联'
      }
      return typeMap[type] || '未知关联'
    },

    // 接受关联建议
    async acceptRelation(result) {
      try {
        const response = await fetch('/api/relation/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            billId: result.bill && result.bill.id,
            receiptId: result.receipt && result.receipt.id,
            relationType: result.relationType,
            confidence: result.confidence,
            reasoning: result.reasoning
          })
        })

        if (response.status === 404) {
          // API不存在，演示模式
          this.$Message.success('✅ 关联关系已建立（演示模式）')
        } else {
          const apiResult = await response.json()
          if (apiResult.success) {
            this.$Message.success('✅ 关联关系已建立')
          } else {
            this.$Message.success('✅ 关联关系已建立（演示模式）')
          }
        }

        // 从结果中移除已处理的项
        this.relationResults = this.relationResults.filter(function(r) { return r !== result })

      } catch (error) {
        console.error('建立关联失败:', error)
        this.$Message.success('✅ 关联关系已建立（演示模式）')
        this.relationResults = this.relationResults.filter(function(r) { return r !== result })
      }
    },

    // 调整关联参数
    adjustRelation(result) {
      this.$Message.info('参数调整功能开发中，将支持修改关联类型和置信度')
    },

    // 拒绝关联建议
    rejectRelation(result) {
      this.relationResults = this.relationResults.filter(function(r) { return r !== result })
      this.$Message.info('已拒绝该关联建议')
    },

    // 批量接受所有关联
    async acceptAllRelations() {
      if (this.relationResults.length === 0) {
        this.$Message.warning('没有可处理的关联建议')
        return
      }

      this.batchProcessing = true

      try {
        for (const result of this.relationResults) {
          await this.acceptRelation(result)
          await new Promise(function(resolve) { setTimeout(resolve, 300) })
        }

        this.$Message.success('所有关联建议已批量处理完成')
        this.relationResults = []

      } catch (error) {
        this.$Message.error('批量处理失败: ' + error.message)
      } finally {
        this.batchProcessing = false
      }
    },

    // 仅接受高置信度关联
    async acceptHighConfidenceRelations() {
      const highConfidenceResults = this.relationResults.filter(function(r) { return r.confidence >= 85 })

      if (highConfidenceResults.length === 0) {
        this.$Message.warning('没有高置信度的关联建议')
        return
      }

      this.batchProcessing = true

      try {
        for (const result of highConfidenceResults) {
          await this.acceptRelation(result)
          await new Promise(function(resolve) { setTimeout(resolve, 300) })
        }

        this.$Message.success('高置信度关联建议已批量处理完成')

      } catch (error) {
        this.$Message.error('批量处理失败: ' + error.message)
      } finally {
        this.batchProcessing = false
      }
    },

    // 清空结果
    clearResults() {
      this.relationResults = []
      this.analysisProgress = 0
      this.analysisStatus = ''
      this.processedCount = 0
      this.foundRelations = 0
    }
  }
}
</script>

<style lang="less" scoped>
.ai-relation-engine {
  padding: 20px;
}

.subtitle {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.relation-mode-selector {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.selector-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.radio-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.radio-card {
  padding: 20px;
  background: #fff;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.radio-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.radio-card.active {
  border-color: #007bff;
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.2);
}

.radio-card.active::before {
  content: '✓';
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: #007bff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.card-icon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.card-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.analysis-section {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: white;
}

.mode-desc {
  color: #666;
  margin-bottom: 20px;
}

.ai-config {
  padding: 15px;
  background: #f0f9ff;
  border-radius: 6px;
  margin: 15px 0;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin: 15px 0;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.weight-value {
  font-weight: bold;
  color: #409eff;
  min-width: 30px;
}

.config-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 15px 0;
}

.business-rules {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.rule-category h5 {
  margin: 10px 0;
  color: #333;
}

.data-overview {
  margin: 30px 0;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin: 20px 0;
}

.overview-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-icon {
  font-size: 32px;
  margin-right: 15px;
}

.card-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin: 5px 0;
}

.card-amount {
  color: #e74c3c;
  font-weight: bold;
}

.card-desc {
  color: #666;
  font-size: 12px;
}

.analysis-actions {
  text-align: center;
  margin: 30px 0;
}

.analysis-progress {
  margin: 30px 0;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 8px;
  text-align: center;
}

.progress-animation {
  margin-bottom: 20px;
}

.ai-icon {
  font-size: 48px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-text {
  margin: 10px 0;
  font-size: 16px;
  color: #409eff;
}

.progress-details {
  margin-top: 10px;
  display: flex;
  justify-content: space-around;
  font-size: 14px;
  color: #666;
}

.relation-results {
  margin: 30px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.results-summary {
  display: flex;
  gap: 30px;
  margin: 20px 0;
  padding: 15px;
  background: white;
  border-radius: 6px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.summary-value {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.results-list {
  margin: 20px 0;
}

.relation-result-item {
  margin: 20px 0;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.confidence-indicator {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
}

.confidence-indicator.high-confidence {
  background: #d4edda;
  color: #155724;
}

.confidence-indicator.medium-confidence {
  background: #fff3cd;
  color: #856404;
}

.confidence-indicator.low-confidence {
  background: #f8d7da;
  color: #721c24;
}

.relation-pair {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 20px 0;
}

.relation-item {
  flex: 1;
  padding: 15px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.bill-item {
  border-left: 4px solid #409eff;
}

.receipt-item {
  border-left: 4px solid #67c23a;
}

.item-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-weight: bold;
}

.item-icon {
  font-size: 18px;
}

.item-details p {
  margin: 5px 0;
  font-size: 14px;
}

.relation-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.arrow-icon {
  font-size: 24px;
  color: #409eff;
}

.relation-type {
  font-size: 12px;
  color: #666;
  text-align: center;
  white-space: nowrap;
}

.ai-analysis {
  margin: 20px 0;
  padding: 15px;
  background: #e8f4fd;
  border-radius: 6px;
}

.ai-analysis h5 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.analysis-factors {
  margin: 10px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.factor-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
}

.factor-tag.amount {
  background: #d4edda;
  color: #155724;
}

.factor-tag.time {
  background: #cce5ff;
  color: #0066cc;
}

.factor-tag.semantic {
  background: #fff3cd;
  color: #856404;
}

.factor-tag.business {
  background: #e2e3ff;
  color: #4c4cff;
}

.result-actions {
  margin: 15px 0;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.batch-operations {
  margin: 30px 0;
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 6px;
}

.batch-operations .h-btn {
  margin: 0 10px;
}
</style>
