<template>
  <div class="unified-query">
    <div class="page-header">
      <h2>统一查询</h2>
      <p>跨票据和银证的统一查询和关联分析</p>
    </div>

    <!-- 查询条件 -->
    <div class="query-card">
      <div class="card-header">
        <span>查询条件</span>
        <Button size="small" @click="resetQuery">重置</Button>
      </div>

      <div class="query-form">
        <div class="form-row">
          <label>查询类型：</label>
          <Select v-model="queryType" :datas="queryTypeOptions"
                  style="width: 200px;" @change="onQueryTypeChange" />
        </div>

        <div class="form-row" v-if="queryType !== 'cross-relations'">
          <label>关键字：</label>
          <input v-model="queryForm.keyword" placeholder="编号、摘要、交易方"
                 class="form-input" style="width: 200px;" />
        </div>

        <div class="form-row">
          <label>日期范围：</label>
          <DatePicker v-model="dateRange" type="daterange"
                      placeholder="选择日期范围" style="width: 250px;" />
        </div>

        <div class="form-row">
          <label>金额范围：</label>
          <input v-model.number="queryForm.minAmount" type="number"
                 placeholder="最小金额" class="form-input" style="width: 120px;" />
          <span style="margin: 0 10px;">-</span>
          <input v-model.number="queryForm.maxAmount" type="number"
                 placeholder="最大金额" class="form-input" style="width: 120px;" />
        </div>

        <div class="form-row" v-if="queryType !== 'cross-relations'">
          <label>包含关联：</label>
          <Checkbox v-model="queryForm.includeRelations">显示关联信息</Checkbox>
        </div>

        <div class="form-row" v-if="queryType !== 'cross-relations'">
          <label>仅有关联：</label>
          <Checkbox v-model="queryForm.onlyWithRelations">只显示有关联的记录</Checkbox>
        </div>

        <div class="form-row" v-if="queryType !== 'cross-relations'">
          <label>归并状态：</label>
          <Select v-model="queryForm.mergeStatus" :datas="mergeStatusOptions"
                  style="width: 150px;" />
        </div>

        <div class="form-row" v-if="queryType === 'cross-relations'">
          <label>关联类型：</label>
          <Select v-model="queryForm.relationType" :datas="relationTypeOptions"
                  style="width: 150px;" />
        </div>

        <div class="form-row" v-if="queryType === 'cross-relations'">
          <label>金额匹配：</label>
          <Select v-model="queryForm.amountMatch" :datas="amountMatchOptions"
                  style="width: 150px;" />
        </div>

        <div class="form-row">
          <Button color="primary" @click="executeQuery" :loading="queryLoading">
            <i class="h-icon-search"></i> 查询
          </Button>
          <Button @click="exportResults" :disabled="!queryResults.length">
            <i class="h-icon-download"></i> 导出
          </Button>
          <Button @click="showAnalysisPanel" style="margin-left: 10px;" icon="h-icon-chart">数据分析</Button>
        </div>
      </div>
    </div>

    <!-- 查询结果 -->
    <div class="result-card">
      <div class="card-header">
        <span>查询结果 ({{ pagination.total }} 条)</span>
      </div>

      <!-- 票据查询结果 -->
      <table v-if="queryType === 'documents'" class="h-table h-table-border">
        <thead>
          <tr>
            <th style="width: 100px">类型</th>
            <th style="width: 150px">编号</th>
            <th style="width: 120px">日期</th>
            <th style="width: 100px">类型名称</th>
            <th style="width: 120px">金额</th>
            <th style="width: 150px">交易方</th>
            <th>摘要</th>
            <th style="width: 100px">归并组</th>
            <th style="width: 150px">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in queryResults" :key="item.entityId">
            <td>
              <span class="entity-type" :class="item.entityType ? item.entityType.toLowerCase() : ''">
                {{ item.entityType === 'DOCUMENT' ? '票据' : '票据组' }}
              </span>
            </td>
            <td>{{ item.entityNo }}</td>
            <td>{{ item.entityDate }}</td>
            <td>{{ item.entityTypeName }}</td>
            <td>¥{{ formatAmount(item.amount) }}</td>
            <td>{{ item.counterparty }}</td>
            <td>{{ item.summary }}</td>
            <td>
              <span v-if="item.groupId" class="group-badge">已归并</span>
              <span v-else>-</span>
            </td>
            <td>
              <Button size="small" @click="viewRelations(item)">查看关联</Button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 银证查询结果 -->
      <table v-if="queryType === 'receipts'" class="h-table h-table-border">
        <thead>
          <tr>
            <th style="width: 100px">类型</th>
            <th style="width: 150px">编号</th>
            <th style="width: 120px">日期</th>
            <th style="width: 100px">类型名称</th>
            <th style="width: 120px">金额</th>
            <th style="width: 150px">交易方</th>
            <th>摘要</th>
            <th style="width: 100px">归并组</th>
            <th style="width: 150px">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in queryResults" :key="item.entityId">
            <td>
              <span class="entity-type" :class="item.entityType ? item.entityType.toLowerCase() : ''">
                {{ item.entityType === 'RECEIPT' ? '银证' : '银证组' }}
              </span>
            </td>
            <td>{{ item.entityNo }}</td>
            <td>{{ item.entityDate }}</td>
            <td>{{ item.entityTypeName }}</td>
            <td>¥{{ formatAmount(item.amount) }}</td>
            <td>{{ item.counterparty }}</td>
            <td>{{ item.summary }}</td>
            <td>
              <span v-if="item.groupId" class="group-badge">已归并</span>
              <span v-else>-</span>
            </td>
            <td>
              <Button size="small" @click="viewRelations(item)">查看关联</Button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 跨类型关联查询结果 -->
      <table v-if="queryType === 'cross-relations'" class="h-table h-table-border">
        <thead>
          <tr>
            <th style="width: 100px">关联类型</th>
            <th style="width: 200px">源</th>
            <th style="width: 200px">目标</th>
            <th style="width: 120px">关联金额</th>
            <th>关联备注</th>
            <th style="width: 150px">创建时间</th>
            <th style="width: 100px">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in queryResults" :key="item.relationId">
            <td>
              <span class="relation-type">{{ getRelationTypeText(item.relationType) }}</span>
            </td>
            <td>
              <div>
                <span class="entity-type" :class="item.sourceType ? item.sourceType.toLowerCase() : ''">
                  {{ getEntityTypeText(item.sourceType) }}
                </span>
                <span style="margin-left: 5px;">{{ item.sourceName }}</span>
              </div>
            </td>
            <td>
              <div>
                <span class="entity-type" :class="item.targetType ? item.targetType.toLowerCase() : ''">
                  {{ getEntityTypeText(item.targetType) }}
                </span>
                <span style="margin-left: 5px;">{{ item.targetName }}</span>
              </div>
            </td>
            <td>
              <span v-if="item.relationAmount">¥{{ formatAmount(item.relationAmount) }}</span>
              <span v-else>-</span>
            </td>
            <td>{{ item.relationNote }}</td>
            <td>{{ formatDate(item.createdAt) }}</td>
            <td>
              <Button size="small" color="red" @click="deleteRelation(item.relationId)">删除</Button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 分页 -->
      <div v-if="queryResults.length > 0" class="pagination-wrapper">
        <Pagination
          v-model="pagination.pageNum"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          :page-size-options="[10, 20, 50, 100]"
          @change="handleCurrentChange"
          @page-size-change="handleSizeChange"
          show-total
          show-size-changer
          show-quick-jumper />
      </div>
    </div>

    <!-- 数据分析面板 -->
    <Modal v-model="analysisDialogVisible" title="数据分析" width="900px">
      <div class="analysis-panel">
        <div class="analysis-summary">
          <h4>查询结果统计</h4>
          <div class="summary-grid">
            <div class="summary-item">
              <div class="summary-value">{{ analysisData.totalCount }}</div>
              <div class="summary-label">总记录数</div>
            </div>
            <div class="summary-item">
              <div class="summary-value">{{ analysisData.totalAmount }}</div>
              <div class="summary-label">总金额</div>
            </div>
            <div class="summary-item">
              <div class="summary-value">{{ analysisData.relationCount }}</div>
              <div class="summary-label">关联记录数</div>
            </div>
            <div class="summary-item">
              <div class="summary-value">{{ analysisData.mergeCount }}</div>
              <div class="summary-label">归并记录数</div>
            </div>
          </div>
        </div>

        <div class="analysis-charts">
          <div class="chart-section">
            <h4>金额分布</h4>
            <div class="amount-distribution">
              <div v-for="range in analysisData.amountRanges" :key="range.label" class="amount-range">
                <div class="range-label">{{ range.label }}</div>
                <div class="range-bar">
                  <div class="range-fill" :style="{ width: range.percentage + '%' }"></div>
                </div>
                <div class="range-count">{{ range.count }}条</div>
              </div>
            </div>
          </div>

          <div class="chart-section">
            <h4>关联状态分布</h4>
            <div class="relation-distribution">
              <div class="relation-pie">
                <div class="pie-item" v-for="item in analysisData.relationDistribution" :key="item.label">
                  <div class="pie-color" :style="{ backgroundColor: item.color }"></div>
                  <span>{{ item.label }}: {{ item.count }}条 ({{ item.percentage }}%)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer">
        <Button @click="analysisDialogVisible = false">关闭</Button>
        <Button color="primary" @click="exportAnalysis">导出分析报告</Button>
      </div>
    </Modal>

    <!-- 关联详情对话框 -->
    <Modal v-model="relationDialogVisible" title="关联详情" width="700px">
      <table class="h-table h-table-border">
        <thead>
          <tr>
            <th style="width: 100px">关联类型</th>
            <th>关联对象</th>
            <th style="width: 120px">关联金额</th>
            <th style="width: 150px">备注</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="relation in entityRelations" :key="relation.relationId">
            <td>{{ getRelationTypeText(relation.relationType) }}</td>
            <td>
              <div>
                <span class="entity-type" :class="getOtherEntityType(relation) ? getOtherEntityType(relation).toLowerCase() : ''">
                  {{ getEntityTypeText(getOtherEntityType(relation)) }}
                </span>
                <span style="margin-left: 5px;">{{ getOtherEntityName(relation) }}</span>
              </div>
            </td>
            <td>
              <span v-if="relation.relationAmount">¥{{ formatAmount(relation.relationAmount) }}</span>
              <span v-else>-</span>
            </td>
            <td>{{ relation.relationNote }}</td>
          </tr>
        </tbody>
      </table>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'UnifiedQuery',
  data() {
    return {
      queryType: 'documents',
      queryLoading: false,
      queryResults: [],

      queryForm: {
        keyword: '',
        minAmount: null,
        maxAmount: null,
        includeRelations: false,
        onlyWithRelations: false,
        mergeStatus: 'all',
        relationType: 'all',
        amountMatch: 'all'
      },

      dateRange: [],

      queryTypeOptions: [
        { key: 'documents', title: '票据查询' },
        { key: 'receipts', title: '银证查询' },
        { key: 'cross-relations', title: '跨类型关联查询' }
      ],

      mergeStatusOptions: [
        { key: 'all', title: '全部' },
        { key: 'merged', title: '已归并' },
        { key: 'unmerged', title: '未归并' }
      ],

      relationTypeOptions: [
        { key: 'all', title: '全部类型' },
        { key: 'PAYMENT', title: '付款关联' },
        { key: 'RECEIPT', title: '收款关联' },
        { key: 'TRANSFER', title: '转账关联' },
        { key: 'OTHER', title: '其他关联' }
      ],

      amountMatchOptions: [
        { key: 'all', title: '全部' },
        { key: 'matched', title: '金额匹配' },
        { key: 'unmatched', title: '金额不匹配' }
      ],

      pagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0
      },

      // 关联详情
      relationDialogVisible: false,
      relationLoading: false,
      entityRelations: [],
      currentEntity: null,

      // 数据分析
      analysisDialogVisible: false,
      analysisData: {
        totalCount: 0,
        totalAmount: '¥0.00',
        relationCount: 0,
        mergeCount: 0,
        amountRanges: [],
        relationDistribution: []
      }
    }
  },

  mounted() {
    // 页面加载时执行一次查询
    this.executeQuery()
  },
  
  methods: {
    // 查询类型改变
    onQueryTypeChange() {
      this.queryResults = []
      this.pagination.total = 0
    },
    
    // 执行查询
    async executeQuery() {
      this.queryLoading = true
      try {
        const queryData = this.buildQueryData()
        let url = ''

        switch (this.queryType) {
          case 'documents':
            url = '/api/unified-query/documents'
            break
          case 'receipts':
            url = '/api/unified-query/receipts'
            break
          case 'cross-relations':
            url = '/api/unified-query/relations'
            break
        }

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(queryData)
        })

        const result = await response.json()
        if (result.success) {
          this.queryResults = result.data.records || result.data || []
          this.pagination.total = result.data.total || this.queryResults.length
          console.log('查询成功:', this.queryResults.length, '条记录')
        } else {
          this.$Message.error('查询失败: ' + result.msg)
        }

      } catch (error) {
        this.$Message.error('查询失败: ' + error.message)
        console.error('查询失败:', error)
      } finally {
        this.queryLoading = false
      }
    },
    
    // 构建查询数据
    buildQueryData() {
      const condition = {
        keyword: this.queryForm.keyword,
        minAmount: this.queryForm.minAmount,
        maxAmount: this.queryForm.maxAmount,
        includeRelations: this.queryForm.includeRelations,
        onlyWithRelations: this.queryForm.onlyWithRelations
      }
      
      if (this.dateRange && this.dateRange.length === 2) {
        condition.startDate = this.dateRange[0]
        condition.endDate = this.dateRange[1]
      }
      
      return {
        condition,
        pageParam: {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          orderBy: 'created_at',
          orderDirection: 'DESC'
        }
      }
    },
    
    // 重置查询
    resetQuery() {
      this.queryForm = {
        keyword: '',
        minAmount: null,
        maxAmount: null,
        includeRelations: false,
        onlyWithRelations: false
      }
      this.dateRange = []
      this.pagination.pageNum = 1
      this.queryResults = []
      this.pagination.total = 0
    },
    
    // 查看关联关系
    async viewRelations(entity) {
      this.currentEntity = entity
      this.relationDialogVisible = true
      this.relationLoading = true

      try {
        const response = await fetch(`/api/relations/entity/${entity.entityId}/${entity.entityType}`, {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.entityRelations = result.data || []
        } else {
          this.$Message.error('获取关联关系失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('获取关联关系失败: ' + error.message)
      } finally {
        this.relationLoading = false
      }
    },

    // 删除关联关系
    async deleteRelation(relationId) {
      try {
        await this.$confirm('确定要删除这个关联关系吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await fetch(`/api/relations/${relationId}`, {
          method: 'DELETE',
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('删除关联关系成功')
          this.executeQuery()
        } else {
          this.$Message.error('删除关联关系失败: ' + result.msg)
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$Message.error('删除关联关系失败: ' + error.message)
        }
      }
    },
    
    // 导出结果
    exportResults() {
      // TODO: 实现导出功能
      this.$Message.info('导出功能开发中...')
    },
    
    // 分页处理
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.executeQuery()
    },
    
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.executeQuery()
    },
    
    // 获取实体类型标签样式
    getEntityTypeTag(type) {
      if (!type) return 'info'
      const tagMap = {
        'DOCUMENT': 'primary',
        'DOCUMENT_GROUP': 'primary',
        'RECEIPT': 'success',
        'RECEIPT_GROUP': 'success'
      }
      return tagMap[type] || 'info'
    },
    
    // 获取实体类型文本
    getEntityTypeText(type) {
      if (!type) return '未知'
      const textMap = {
        'DOCUMENT': '票据',
        'DOCUMENT_GROUP': '票据组',
        'RECEIPT': '银证',
        'RECEIPT_GROUP': '银证组'
      }
      return textMap[type] || type
    },
    
    // 获取关联类型文本
    getRelationTypeText(type) {
      if (!type) return '未知'
      const textMap = {
        'ASSOCIATED': '关联',
        'MATCHED': '匹配',
        'PARTIAL': '部分关联',
        'FULL': '完全关联'
      }
      return textMap[type] || type
    },
    
    // 获取关联对象的类型和名称
    getOtherEntityType(relation) {
      if (!relation) return null
      if (this.currentEntity) {
        return relation.sourceType === this.currentEntity.entityType ? relation.targetType : relation.sourceType
      }
      return relation.targetType
    },

    getOtherEntityName(relation) {
      if (!relation) return ''
      if (this.currentEntity) {
        return relation.sourceType === this.currentEntity.entityType ? relation.targetName : relation.sourceName
      }
      return relation.targetName
    },

    // 格式化金额
    formatAmount(amount) {
      if (!amount) return '0.00'
      return parseFloat(amount).toFixed(2)
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      return new Date(dateStr).toLocaleString('zh-CN')
    },

    // 显示数据分析面板
    showAnalysisPanel() {
      this.generateAnalysisData()
      this.analysisDialogVisible = true
    },

    // 生成分析数据
    generateAnalysisData() {
      const results = this.queryResults

      // 基本统计
      this.analysisData.totalCount = results.length

      // 计算总金额
      const totalAmount = results.reduce((sum, item) => {
        return sum + (parseFloat(item.amount) || 0)
      }, 0)
      this.analysisData.totalAmount = '¥' + totalAmount.toFixed(2)

      // 关联记录数
      this.analysisData.relationCount = results.filter(item =>
        item.relationCount > 0 || item.hasRelations
      ).length

      // 归并记录数
      this.analysisData.mergeCount = results.filter(item =>
        item.groupId || item.isMerged
      ).length

      // 金额分布
      this.generateAmountDistribution(results)

      // 关联状态分布
      this.generateRelationDistribution(results)
    },

    // 生成金额分布数据
    generateAmountDistribution(results) {
      const ranges = [
        { label: '0-1000', min: 0, max: 1000, count: 0 },
        { label: '1000-5000', min: 1000, max: 5000, count: 0 },
        { label: '5000-10000', min: 5000, max: 10000, count: 0 },
        { label: '10000-50000', min: 10000, max: 50000, count: 0 },
        { label: '50000以上', min: 50000, max: Infinity, count: 0 }
      ]

      results.forEach(item => {
        const amount = parseFloat(item.amount) || 0
        for (let range of ranges) {
          if (amount >= range.min && amount < range.max) {
            range.count++
            break
          }
        }
      })

      const maxCount = Math.max(...ranges.map(r => r.count))
      ranges.forEach(range => {
        range.percentage = maxCount > 0 ? (range.count / maxCount) * 100 : 0
      })

      this.analysisData.amountRanges = ranges
    },

    // 生成关联状态分布数据
    generateRelationDistribution(results) {
      const distribution = [
        { label: '有关联', count: 0, color: '#52c41a' },
        { label: '无关联', count: 0, color: '#ff4d4f' },
        { label: '已归并', count: 0, color: '#1890ff' }
      ]

      results.forEach(item => {
        if (item.groupId || item.isMerged) {
          distribution[2].count++
        } else if (item.relationCount > 0 || item.hasRelations) {
          distribution[0].count++
        } else {
          distribution[1].count++
        }
      })

      const total = results.length
      distribution.forEach(item => {
        item.percentage = total > 0 ? Math.round((item.count / total) * 100) : 0
      })

      this.analysisData.relationDistribution = distribution
    },

    // 导出分析报告
    exportAnalysis() {
      const data = this.analysisData
      const content = `数据分析报告

总记录数: ${data.totalCount}
总金额: ${data.totalAmount}
关联记录数: ${data.relationCount}
归并记录数: ${data.mergeCount}

金额分布:
${data.amountRanges.map(r => `${r.label}: ${r.count}条`).join('\n')}

关联状态分布:
${data.relationDistribution.map(r => `${r.label}: ${r.count}条 (${r.percentage}%)`).join('\n')}
      `

      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = '数据分析报告.txt'
      a.click()
      URL.revokeObjectURL(url)

      this.$Message.success('分析报告导出成功')
    }
  }
}
</script>

<style lang="less" scoped>
.unified-query {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.query-card {
  margin-bottom: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: white;
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #f5f7fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.query-form {
  padding: 20px;
}

.form-row {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.form-row label {
  width: 100px;
  text-align: right;
  margin-right: 15px;
  font-weight: bold;
}

.form-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.result-card {
  min-height: 400px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: white;
}

.entity-type {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.entity-type.document {
  background-color: #409eff;
}

.entity-type.receipt {
  background-color: #67c23a;
}

.entity-type.document_group {
  background-color: #e6a23c;
}

.entity-type.receipt_group {
  background-color: #f56c6c;
}

.group-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: #67c23a;
  color: white;
}

.relation-type {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: #909399;
  color: white;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 数据分析面板样式 */
.analysis-panel {
  padding: 20px;
}

.analysis-summary {
  margin-bottom: 30px;
}

.analysis-summary h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.summary-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.summary-label {
  font-size: 14px;
  color: #666;
}

.analysis-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.chart-section {
  background: #fafafa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.chart-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
}

.amount-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.amount-range {
  display: flex;
  align-items: center;
  gap: 10px;
}

.range-label {
  width: 80px;
  font-size: 12px;
  color: #666;
}

.range-bar {
  flex: 1;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.range-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  transition: width 0.3s ease;
}

.range-count {
  width: 40px;
  font-size: 12px;
  color: #666;
  text-align: right;
}

.relation-distribution {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.relation-pie {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.pie-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.pie-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}
</style>
