<template>
  <app-content class="h-panel">
    <div class="h-panel-bar">
      <span class="h-panel-title">关联管理</span>
    </div>

    <div class="h-panel-body">
      <div class="margin-bottom">
        <Button @click="activeTab = 'create'" :class="{'h-btn-primary': activeTab === 'create'}">创建关联</Button>
        <Button @click="activeTab = 'list'" :class="{'h-btn-primary': activeTab === 'list'}">关联列表</Button>
        <Button @click="activeTab = 'batch'" :class="{'h-btn-primary': activeTab === 'batch'}">批量关联</Button>
        <Button @click="activeTab = 'ai'" :class="{'h-btn-primary': activeTab === 'ai'}">🤖 AI智能关联</Button>
      </div>

      <!-- 创建关联 -->
      <div v-if="activeTab === 'create'" class="relation-create-panel">
        <h3>创建关联关系</h3>
        <div class="relation-form">
          <div class="form-section">
            <h4>银行回单选择</h4>
            <div class="selection-mode">
              <Radio v-model="receiptSelectionMode" value="single">单张回单</Radio>
              <Radio v-model="receiptSelectionMode" value="group">回单组合</Radio>
              <Radio v-model="receiptSelectionMode" value="existing-group">已归并组</Radio>
              <Radio v-model="receiptSelectionMode" value="multiple-groups">多个归并组</Radio>
            </div>
            
            <!-- 单张回单选择 -->
            <div v-if="receiptSelectionMode === 'single'" class="single-receipt-selection">
              <div class="search-box">
                <input v-model="receiptSearchKeyword" placeholder="搜索回单编号、摘要、交易方..." 
                       class="form-input" @input="searchReceipts" />
                <Button @click="searchReceipts" icon="h-icon-search">搜索</Button>
              </div>
              
              <table class="h-table h-table-border selection-table">
                <thead>
                  <tr>
                    <th style="width: 50px">选择</th>
                    <th style="width: 120px">回单编号</th>
                    <th style="width: 100px">日期</th>
                    <th style="width: 80px">类型</th>
                    <th style="width: 100px">金额</th>
                    <th style="width: 120px">交易方</th>
                    <th>摘要</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="receipt in filteredReceipts" :key="receipt.id">
                    <td>
                      <Radio v-model="selectedReceiptId" :value="receipt.id" 
                             @change="onReceiptSelected(receipt)"></Radio>
                    </td>
                    <td>{{ receipt.receiptsNo }}</td>
                    <td>{{ receipt.receiptsDate }}</td>
                    <td>{{ receipt.type }}</td>
                    <td>¥{{ receipt.amount }}</td>
                    <td>{{ receipt.counterparty }}</td>
                    <td>{{ receipt.summary }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- 回单组合选择 -->
            <div v-if="receiptSelectionMode === 'group'" class="group-receipt-selection">
              <div class="search-box">
                <input v-model="receiptSearchKeyword" placeholder="搜索回单编号、摘要、交易方..." 
                       class="form-input" @input="searchReceipts" />
                <Button @click="searchReceipts" icon="h-icon-search">搜索</Button>
              </div>
              
              <table class="h-table h-table-border selection-table">
                <thead>
                  <tr>
                    <th style="width: 50px">选择</th>
                    <th style="width: 120px">回单编号</th>
                    <th style="width: 100px">日期</th>
                    <th style="width: 80px">类型</th>
                    <th style="width: 100px">金额</th>
                    <th style="width: 120px">交易方</th>
                    <th>摘要</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="receipt in filteredReceipts" :key="receipt.id">
                    <td>
                      <Checkbox v-model="selectedReceiptIds" :value="receipt.id" 
                                @change="onReceiptGroupSelected"></Checkbox>
                    </td>
                    <td>{{ receipt.receiptsNo }}</td>
                    <td>{{ receipt.receiptsDate }}</td>
                    <td>{{ receipt.type }}</td>
                    <td>¥{{ receipt.amount }}</td>
                    <td>{{ receipt.counterparty }}</td>
                    <td>{{ receipt.summary }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 已归并组选择 -->
            <div v-if="receiptSelectionMode === 'existing-group'" class="existing-group-selection">
              <div class="search-box">
                <Button @click="loadReceiptGroups" icon="h-icon-refresh">刷新归并组</Button>
              </div>

              <table class="h-table h-table-border selection-table">
                <thead>
                  <tr>
                    <th style="width: 50px">选择</th>
                    <th style="width: 150px">组名称</th>
                    <th style="width: 100px">组内数量</th>
                    <th style="width: 100px">总金额</th>
                    <th style="width: 120px">创建时间</th>
                    <th>组摘要</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="group in receiptGroups" :key="group.groupId">
                    <td>
                      <Radio v-model="selectedReceiptGroupId" :value="group.groupId"
                             @change="onReceiptGroupSelected(group)"></Radio>
                    </td>
                    <td>{{ group.groupName }}</td>
                    <td>{{ group.itemCount }}个</td>
                    <td>¥{{ group.totalAmount }}</td>
                    <td>{{ group.createdAt }}</td>
                    <td>{{ group.groupSummary }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 多个归并组选择 -->
            <div v-if="receiptSelectionMode === 'multiple-groups'" class="multiple-groups-selection">
              <div class="search-box">
                <Button @click="loadReceiptGroups" icon="h-icon-refresh">刷新归并组</Button>
              </div>

              <table class="h-table h-table-border selection-table">
                <thead>
                  <tr>
                    <th style="width: 50px">选择</th>
                    <th style="width: 150px">组名称</th>
                    <th style="width: 100px">组内数量</th>
                    <th style="width: 100px">总金额</th>
                    <th style="width: 120px">创建时间</th>
                    <th>组摘要</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="group in receiptGroups" :key="group.groupId">
                    <td>
                      <Checkbox v-model="selectedReceiptGroupIds" :value="group.groupId"
                                @change="onMultipleReceiptGroupsSelected"></Checkbox>
                    </td>
                    <td>{{ group.groupName }}</td>
                    <td>{{ group.itemCount }}个</td>
                    <td>¥{{ group.totalAmount }}</td>
                    <td>{{ group.createdAt }}</td>
                    <td>{{ group.groupSummary }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="form-section">
            <h4>票据选择</h4>
            <div class="selection-mode">
              <Radio v-model="billSelectionMode" value="single">单张票据</Radio>
              <Radio v-model="billSelectionMode" value="group">票据组合</Radio>
              <Radio v-model="billSelectionMode" value="existing-group">已归并组</Radio>
              <Radio v-model="billSelectionMode" value="multiple-groups">多个归并组</Radio>
            </div>
            
            <!-- 单张票据选择 -->
            <div v-if="billSelectionMode === 'single'" class="single-bill-selection">
              <div class="search-box">
                <input v-model="billSearchKeyword" placeholder="搜索票据编号、摘要、交易方..." 
                       class="form-input" @input="searchBills" />
                <Button @click="searchBills" icon="h-icon-search">搜索</Button>
                <Button @click="loadRecommendedBills" color="primary" icon="h-icon-magic">智能推荐</Button>
              </div>
              
              <table class="h-table h-table-border selection-table">
                <thead>
                  <tr>
                    <th style="width: 50px">选择</th>
                    <th style="width: 120px">票据编号</th>
                    <th style="width: 100px">日期</th>
                    <th style="width: 80px">类型</th>
                    <th style="width: 100px">金额</th>
                    <th style="width: 120px">交易方</th>
                    <th>摘要</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="bill in filteredBills" :key="bill.id"
                      :class="{'recommended-row': bill.isRecommended}">
                    <td>
                      <Radio v-model="selectedBillId" :value="bill.id"
                             @change="onBillSelected(bill)"></Radio>
                    </td>
                    <td>
                      {{ bill.billNo }}
                      <span v-if="bill.isRecommended" class="recommended-tag">推荐</span>
                    </td>
                    <td>{{ bill.billDate }}</td>
                    <td>{{ bill.type }}</td>
                    <td>¥{{ bill.amount }}</td>
                    <td>{{ bill.recipient || bill.issuer }}</td>
                    <td>{{ bill.summary }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- 票据组合选择 -->
            <div v-if="billSelectionMode === 'group'" class="group-bill-selection">
              <div class="search-box">
                <input v-model="billSearchKeyword" placeholder="搜索票据编号、摘要、交易方..." 
                       class="form-input" @input="searchBills" />
                <Button @click="searchBills" icon="h-icon-search">搜索</Button>
                <Button @click="loadRecommendedBills" color="primary" icon="h-icon-magic">智能推荐</Button>
              </div>
              
              <table class="h-table h-table-border selection-table">
                <thead>
                  <tr>
                    <th style="width: 50px">选择</th>
                    <th style="width: 120px">票据编号</th>
                    <th style="width: 100px">日期</th>
                    <th style="width: 80px">类型</th>
                    <th style="width: 100px">金额</th>
                    <th style="width: 120px">交易方</th>
                    <th>摘要</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="bill in filteredBills" :key="bill.id"
                      :class="{'recommended-row': bill.isRecommended}">
                    <td>
                      <Checkbox v-model="selectedBillIds" :value="bill.id"
                                @change="onBillGroupSelected"></Checkbox>
                    </td>
                    <td>
                      {{ bill.billNo }}
                      <span v-if="bill.isRecommended" class="recommended-tag">推荐</span>
                    </td>
                    <td>{{ bill.billDate }}</td>
                    <td>{{ bill.type }}</td>
                    <td>¥{{ bill.amount }}</td>
                    <td>{{ bill.recipient || bill.issuer }}</td>
                    <td>{{ bill.summary }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 已归并组选择 -->
            <div v-if="billSelectionMode === 'existing-group'" class="existing-group-selection">
              <div class="search-box">
                <Button @click="loadBillGroups" icon="h-icon-refresh">刷新归并组</Button>
              </div>

              <table class="h-table h-table-border selection-table">
                <thead>
                  <tr>
                    <th style="width: 50px">选择</th>
                    <th style="width: 150px">组名称</th>
                    <th style="width: 100px">组内数量</th>
                    <th style="width: 100px">总金额</th>
                    <th style="width: 120px">创建时间</th>
                    <th>组摘要</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="group in billGroups" :key="group.groupId">
                    <td>
                      <Radio v-model="selectedBillGroupId" :value="group.groupId"
                             @change="onBillGroupSelected(group)"></Radio>
                    </td>
                    <td>{{ group.groupName }}</td>
                    <td>{{ group.itemCount }}个</td>
                    <td>¥{{ group.totalAmount }}</td>
                    <td>{{ group.createdAt }}</td>
                    <td>{{ group.groupSummary }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 多个归并组选择 -->
            <div v-if="billSelectionMode === 'multiple-groups'" class="multiple-groups-selection">
              <div class="search-box">
                <Button @click="loadBillGroups" icon="h-icon-refresh">刷新归并组</Button>
              </div>

              <table class="h-table h-table-border selection-table">
                <thead>
                  <tr>
                    <th style="width: 50px">选择</th>
                    <th style="width: 150px">组名称</th>
                    <th style="width: 100px">组内数量</th>
                    <th style="width: 100px">总金额</th>
                    <th style="width: 120px">创建时间</th>
                    <th>组摘要</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="group in billGroups" :key="group.groupId">
                    <td>
                      <Checkbox v-model="selectedBillGroupIds" :value="group.groupId"
                                @change="onMultipleBillGroupsSelected"></Checkbox>
                    </td>
                    <td>{{ group.groupName }}</td>
                    <td>{{ group.itemCount }}个</td>
                    <td>¥{{ group.totalAmount }}</td>
                    <td>{{ group.createdAt }}</td>
                    <td>{{ group.groupSummary }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 关联信息 -->
          <div class="form-section">
            <h4>关联信息</h4>
            <div class="relation-info">
              <div class="form-row">
                <label>关联类型：</label>
                <Select v-model="relationForm.relationType" :datas="relationTypeOptions" 
                        style="width: 200px;" />
              </div>
              <div class="form-row">
                <label>关联金额：</label>
                <input v-model.number="relationForm.relationAmount" type="number" 
                       placeholder="关联金额" class="form-input" style="width: 200px;" />
              </div>
              <div class="form-row">
                <label>关联备注：</label>
                <textarea v-model="relationForm.relationNote" placeholder="关联备注" 
                          class="form-textarea" rows="3"></textarea>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <Button color="primary" @click="createRelation" :loading="creating">
              创建关联
            </Button>
            <Button @click="resetForm">重置</Button>
          </div>
        </div>
      </div>

      <!-- 关联列表 -->
      <div v-if="activeTab === 'list'" class="relation-list-panel">
        <h3>关联关系列表</h3>
        <div class="toolbar margin-bottom">
          <Button @click="loadRelations" icon="h-icon-refresh">刷新</Button>
          <Button @click="exportRelations" icon="h-icon-download">导出</Button>
        </div>

        <table class="h-table h-table-border">
          <thead>
            <tr>
              <th style="width: 80px">关联ID</th>
              <th style="width: 200px">源实体</th>
              <th style="width: 200px">目标实体</th>
              <th style="width: 100px">关联类型</th>
              <th style="width: 120px">金额匹配</th>
              <th style="width: 120px">创建时间</th>
              <th style="width: 150px">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="relation in relations" :key="relation.relationId">
              <td>{{ relation.relationId ? relation.relationId.substring(0, 8) : '-' }}</td>
              <td>
                <div class="entity-summary">
                  <div class="entity-type-badge" :class="getEntityTypeClass(relation.sourceType)">
                    {{ formatEntityType(relation.sourceType) }}
                  </div>
                  <div class="entity-info">
                    <div class="entity-id">ID: {{ relation.sourceId }}</div>
                    <div class="entity-preview" v-if="relation.sourcePreview">
                      {{ relation.sourcePreview.billNo || relation.sourcePreview.receiptsNo || '-' }}
                      <span v-if="relation.sourcePreview.amount" class="amount-preview">
                        ¥{{ relation.sourcePreview.amount }}
                      </span>
                    </div>
                  </div>
                </div>
              </td>
              <td>
                <div class="entity-summary">
                  <div class="entity-type-badge" :class="getEntityTypeClass(relation.targetType)">
                    {{ formatEntityType(relation.targetType) }}
                  </div>
                  <div class="entity-info">
                    <div class="entity-id">ID: {{ relation.targetId }}</div>
                    <div class="entity-preview" v-if="relation.targetPreview">
                      {{ relation.targetPreview.billNo || relation.targetPreview.receiptsNo || '-' }}
                      <span v-if="relation.targetPreview.amount" class="amount-preview">
                        ¥{{ relation.targetPreview.amount }}
                      </span>
                    </div>
                  </div>
                </div>
              </td>
              <td>
                <span class="relation-type-badge">{{ relation.relationType }}</span>
              </td>
              <td>
                <span v-if="relation.sourcePreview && relation.targetPreview && relation.sourcePreview.amount === relation.targetPreview.amount"
                      class="match-status match-yes">✓ 匹配</span>
                <span v-else class="match-status match-no">✗ 不匹配</span>
              </td>
              <td>{{ formatDate(relation.createdAt) }}</td>
              <td>
                <Button size="s" @click="showRelationDetail(relation)" icon="h-icon-eye">详情</Button>
                <Button size="s" color="red" @click="deleteRelation(relation.relationId)" icon="h-icon-trash">删除</Button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 批量关联 -->
      <div v-if="activeTab === 'batch'" class="batch-relation-panel">
        <h3>批量关联</h3>
        <div class="batch-form">
          <div class="form-section">
            <h4>批量关联规则</h4>
            <div class="batch-rules">
              <div class="form-row">
                <Checkbox v-model="batchRules.amountMatch">金额匹配</Checkbox>
                <span class="rule-desc">自动匹配相同金额的回单和票据</span>
              </div>
              <div class="form-row">
                <Checkbox v-model="batchRules.dateMatch">日期匹配</Checkbox>
                <span class="rule-desc">匹配日期相近的回单和票据（±3天）</span>
              </div>
              <div class="form-row">
                <Checkbox v-model="batchRules.counterpartyMatch">交易方匹配</Checkbox>
                <span class="rule-desc">匹配相同交易方的回单和票据</span>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <Button color="primary" @click="executeBatchRelation" :loading="batchProcessing">
              执行批量关联
            </Button>
            <Button @click="previewBatchRelation">预览结果</Button>
          </div>
        </div>
      </div>

      <!-- AI智能关联 -->
      <div v-if="activeTab === 'ai'" class="ai-relation-panel">
        <div class="ai-info-panel margin-bottom">
          <h4>🤖 AI智能关联</h4>
          <p>AI将自动分析票据和银证数据，识别它们之间的关联关系，基于金额、日期、交易对手、用途等因素进行智能匹配。</p>
        </div>

        <div class="toolbar margin-bottom">
          <Button @click="loadUnrelatedDataForAi" icon="h-icon-refresh">刷新数据</Button>
          <Button color="primary" @click="analyzeRelationsWithAi"
                  :disabled="!hasAnalysisData() || aiAnalyzing"
                  :loading="aiAnalyzing">
            {{ aiAnalyzing ? '🤖 AI分析中...' : '🔍 AI智能关联分析' }} ({{ getAnalysisDataSummary() }})
          </Button>
          <Button color="green" @click="executeSelectedAiRelations"
                  :disabled="!aiRelationSuggestions || selectedAiRelations.length === 0">
            ⚡ 执行选中关联 ({{ selectedAiRelations.length }}个关联)
          </Button>
          <Button color="orange" @click="autoRelateAllWithAi" :disabled="!hasAnalysisData()">
            🚀 一键智能关联
          </Button>
        </div>

        <!-- AI分析范围选择 -->
        <div class="ai-analysis-scope margin-bottom">
          <h4>AI分析范围</h4>
          <div class="scope-options">
            <Checkbox v-model="aiAnalysisScope.includeIndividualItems">包含单个票据和银证</Checkbox>
            <Checkbox v-model="aiAnalysisScope.includeGroups">包含已归并组</Checkbox>
            <Checkbox v-model="aiAnalysisScope.crossTypeAnalysis">跨类型智能分析</Checkbox>
          </div>
          <div class="scope-description">
            <p v-if="aiAnalysisScope.includeIndividualItems">✓ 分析未关联的单个票据和银证之间的关联关系</p>
            <p v-if="aiAnalysisScope.includeGroups">✓ 分析已归并组与单个项目、组与组之间的关联关系</p>
            <p v-if="aiAnalysisScope.crossTypeAnalysis">✓ 智能分析一对多、多对一、多对多等复杂关联模式</p>
          </div>
        </div>

        <!-- AI分析结果 -->
        <div v-if="aiRelationSuggestions" class="ai-suggestions margin-bottom">
          <h4>AI关联建议</h4>
          <div class="suggestion-summary margin-bottom">
            <span class="h-tag">建议关联数: {{ aiRelationSuggestions.relations.length }}</span>
            <span class="h-tag h-tag-green">{{ aiRelationSuggestions.message }}</span>
            <span class="h-tag h-tag-orange">已选择: {{ selectedAiRelations.length }}个关联</span>
            <span class="h-tag h-tag-blue">分析类型: {{ getAnalysisTypesSummary() }}</span>
          </div>

          <div class="suggestion-actions margin-bottom">
            <Button size="s" @click="selectAllAiRelations">全选</Button>
            <Button size="s" @click="clearAiRelationSelection">清空选择</Button>
            <Button size="s" @click="selectHighConfidenceRelations">选择高置信度(≥80%)</Button>
          </div>

          <table class="h-table h-table-border">
            <thead>
              <tr>
                <th style="width: 50px">
                  <Checkbox v-model="selectAllRelationsFlag" @change="toggleSelectAllRelations">全选</Checkbox>
                </th>
                <th style="width: 100px">关联模式</th>
                <th style="width: 120px">源实体</th>
                <th style="width: 120px">目标实体</th>
                <th style="width: 100px">关联类型</th>
                <th style="width: 100px">置信度</th>
                <th>关联原因</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(relation, index) in aiRelationSuggestions.relations" :key="index">
                <td>
                  <Checkbox v-model="selectedAiRelations" :value="index"></Checkbox>
                </td>
                <td>
                  <span class="relation-mode-tag" :class="getRelationModeClass(relation.relationMode)">
                    {{ getRelationModeText(relation.relationMode) }}
                  </span>
                </td>
                <td>{{ formatEntityDisplay(relation.sourceType, relation.sourceId, relation.sourceName) }}</td>
                <td>{{ formatEntityDisplay(relation.targetType, relation.targetId, relation.targetName) }}</td>
                <td>{{ relation.relationType }}</td>
                <td>
                  <span class="confidence-score" :class="getConfidenceClass(relation.confidence)">
                    {{ (relation.confidence * 100).toFixed(1) }}%
                  </span>
                </td>
                <td>{{ relation.reason }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 数据概览 -->
        <div class="data-overview">
          <div class="overview-section">
            <h4>未关联票据 ({{ aiUnrelatedBills.length }}个)</h4>
            <div class="data-table-container">
              <table class="h-table h-table-border">
                <thead>
                  <tr>
                    <th style="width: 80px">ID</th>
                    <th style="width: 120px">票据编号</th>
                    <th style="width: 100px">日期</th>
                    <th style="width: 100px">金额</th>
                    <th>摘要</th>
                    <th style="width: 120px">开票方</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="bill in aiUnrelatedBills.slice(0, 10)" :key="bill.id">
                    <td>{{ bill.id }}</td>
                    <td>{{ bill.billNo }}</td>
                    <td>{{ bill.billDate }}</td>
                    <td>¥{{ bill.amount }}</td>
                    <td>{{ bill.summary }}</td>
                    <td>{{ bill.issuer }}</td>
                  </tr>
                </tbody>
              </table>
              <div v-if="aiUnrelatedBills.length > 10" class="more-data-hint">
                还有 {{ aiUnrelatedBills.length - 10 }} 个票据...
              </div>
            </div>
          </div>

          <div class="overview-section">
            <h4>未关联银证 ({{ aiUnrelatedReceipts.length }}个)</h4>
            <div class="data-table-container">
              <table class="h-table h-table-border">
                <thead>
                  <tr>
                    <th style="width: 80px">ID</th>
                    <th style="width: 120px">银证编号</th>
                    <th style="width: 100px">日期</th>
                    <th style="width: 100px">金额</th>
                    <th>摘要</th>
                    <th style="width: 120px">交易方</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="receipt in aiUnrelatedReceipts.slice(0, 10)" :key="receipt.id">
                    <td>{{ receipt.id }}</td>
                    <td>{{ receipt.receiptsNo }}</td>
                    <td>{{ receipt.receiptsDate }}</td>
                    <td>¥{{ receipt.amount }}</td>
                    <td>{{ receipt.summary }}</td>
                    <td>{{ receipt.counterparty }}</td>
                  </tr>
                </tbody>
              </table>
              <div v-if="aiUnrelatedReceipts.length > 10" class="more-data-hint">
                还有 {{ aiUnrelatedReceipts.length - 10 }} 个银证...
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 关联详情浮窗 -->
    <Modal v-model="showDetailModal" title="关联关系详情" :width="800">
      <div v-if="selectedRelation" class="relation-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>关联ID:</label>
              <span>{{ selectedRelation.relationId }}</span>
            </div>
            <div class="detail-item">
              <label>关联类型:</label>
              <span>{{ selectedRelation.relationType }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间:</label>
              <span>{{ formatDate(selectedRelation.createdAt) }}</span>
            </div>
            <div class="detail-item">
              <label>创建人:</label>
              <span>{{ selectedRelation.createdBy || '系统' }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>源实体信息</h4>
          <div class="entity-info">
            <div class="entity-card">
              <div class="entity-header">
                <span class="entity-type">{{ formatEntityType(selectedRelation.sourceType) }}</span>
                <span class="entity-id">ID: {{ selectedRelation.sourceId }}</span>
              </div>
              <div class="entity-details" v-if="sourceEntityDetail">
                <div class="detail-row" v-if="sourceEntityDetail.billNo || sourceEntityDetail.receiptsNo">
                  <label>编号:</label>
                  <span>{{ sourceEntityDetail.billNo || sourceEntityDetail.receiptsNo }}</span>
                </div>
                <div class="detail-row" v-if="sourceEntityDetail.amount">
                  <label>金额:</label>
                  <span class="amount">¥{{ sourceEntityDetail.amount }}</span>
                </div>
                <div class="detail-row" v-if="sourceEntityDetail.billDate || sourceEntityDetail.receiptsDate">
                  <label>日期:</label>
                  <span>{{ sourceEntityDetail.billDate || sourceEntityDetail.receiptsDate }}</span>
                </div>
                <div class="detail-row" v-if="sourceEntityDetail.summary">
                  <label>摘要:</label>
                  <span>{{ sourceEntityDetail.summary }}</span>
                </div>
                <div class="detail-row" v-if="sourceEntityDetail.issuer || sourceEntityDetail.counterparty">
                  <label>{{ sourceEntityDetail.issuer ? '开票方' : '交易方' }}:</label>
                  <span>{{ sourceEntityDetail.issuer || sourceEntityDetail.counterparty }}</span>
                </div>
              </div>
              <div v-else class="loading-text">加载中...</div>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>目标实体信息</h4>
          <div class="entity-info">
            <div class="entity-card">
              <div class="entity-header">
                <span class="entity-type">{{ formatEntityType(selectedRelation.targetType) }}</span>
                <span class="entity-id">ID: {{ selectedRelation.targetId }}</span>
              </div>
              <div class="entity-details" v-if="targetEntityDetail">
                <div class="detail-row" v-if="targetEntityDetail.billNo || targetEntityDetail.receiptsNo">
                  <label>编号:</label>
                  <span>{{ targetEntityDetail.billNo || targetEntityDetail.receiptsNo }}</span>
                </div>
                <div class="detail-row" v-if="targetEntityDetail.amount">
                  <label>金额:</label>
                  <span class="amount">¥{{ targetEntityDetail.amount }}</span>
                </div>
                <div class="detail-row" v-if="targetEntityDetail.billDate || targetEntityDetail.receiptsDate">
                  <label>日期:</label>
                  <span>{{ targetEntityDetail.billDate || targetEntityDetail.receiptsDate }}</span>
                </div>
                <div class="detail-row" v-if="targetEntityDetail.summary">
                  <label>摘要:</label>
                  <span>{{ targetEntityDetail.summary }}</span>
                </div>
                <div class="detail-row" v-if="targetEntityDetail.issuer || targetEntityDetail.counterparty">
                  <label>{{ targetEntityDetail.issuer ? '开票方' : '交易方' }}:</label>
                  <span>{{ targetEntityDetail.issuer || targetEntityDetail.counterparty }}</span>
                </div>
              </div>
              <div v-else class="loading-text">加载中...</div>
            </div>
          </div>
        </div>

        <div class="detail-section" v-if="selectedRelation.relationNote">
          <h4>关联备注</h4>
          <div class="relation-note">
            {{ selectedRelation.relationNote }}
          </div>
        </div>
      </div>

      <div slot="footer">
        <Button @click="showDetailModal = false">关闭</Button>
        <Button color="red" @click="deleteRelationFromDetail">删除此关联</Button>
      </div>
    </Modal>
  </app-content>
</template>

<script>
export default {
  name: 'RelationManagement',
  data() {
    return {
      activeTab: 'create',
      
      // 选择模式
      receiptSelectionMode: 'single', // single, group
      billSelectionMode: 'single', // single, group
      
      // 数据
      receipts: [],
      bills: [],
      relations: [],
      receiptGroups: [],
      billGroups: [],
      
      // 搜索
      receiptSearchKeyword: '',
      billSearchKeyword: '',
      
      // 选择的项目
      selectedReceiptId: null,
      selectedReceiptIds: [],
      selectedBillId: null,
      selectedBillIds: [],

      // 组选择状态
      selectedReceiptGroupId: null,
      selectedBillGroupId: null,
      selectedReceiptGroupIds: [],
      selectedBillGroupIds: [],
      
      // 关联表单
      relationForm: {
        relationType: 'FULL',
        relationAmount: null,
        relationNote: ''
      },
      
      // 关联类型选项
      relationTypeOptions: [
        { key: 'FULL', title: '完全关联' },
        { key: 'PARTIAL', title: '部分关联' }
      ],
      
      // 状态
      creating: false,
      batchProcessing: false,
      
      // 批量关联规则
      batchRules: {
        amountMatch: true,
        dateMatch: false,
        counterpartyMatch: false
      },

      // AI智能关联
      aiUnrelatedBills: [],
      aiUnrelatedReceipts: [],
      aiRelationSuggestions: null,

      // AI分析状态
      aiAnalyzing: false,

      // AI建议选择
      selectedAiRelations: [],

      // AI分析范围
      aiAnalysisScope: {
        includeIndividualItems: true,  // 包含单个票据和银证
        includeGroups: true,           // 包含已归并组
        crossTypeAnalysis: true        // 跨类型智能分析
      },

      // 详情浮窗
      showDetailModal: false,
      selectedRelation: null,
      sourceEntityDetail: null,
      targetEntityDetail: null
    }
  },
  
  computed: {
    filteredReceipts() {
      if (!this.receiptSearchKeyword) {
        return this.receipts
      }
      const keyword = this.receiptSearchKeyword.toLowerCase()
      return this.receipts.filter(function(receipt) {
        return receipt.receiptsNo.toLowerCase().includes(keyword) ||
               receipt.summary.toLowerCase().includes(keyword) ||
               (receipt.counterparty && receipt.counterparty.toLowerCase().includes(keyword))
      })
    },

    filteredBills() {
      if (!this.billSearchKeyword) {
        return this.bills
      }
      const keyword = this.billSearchKeyword.toLowerCase()
      return this.bills.filter(function(bill) {
        return bill.billNo.toLowerCase().includes(keyword) ||
               bill.summary.toLowerCase().includes(keyword) ||
               (bill.recipient && bill.recipient.toLowerCase().includes(keyword)) ||
               (bill.issuer && bill.issuer.toLowerCase().includes(keyword))
      })
    },

    selectAllRelationsFlag: {
      get() {
        if (!this.aiRelationSuggestions || this.aiRelationSuggestions.relations.length === 0) {
          return false
        }
        return this.selectedAiRelations.length === this.aiRelationSuggestions.relations.length
      },
      set(value) {
        // 这个setter会被toggleSelectAllRelations方法处理
      }
    }
  },
  
  mounted() {
    this.loadReceipts()
    this.loadBills()
    this.loadRelations()
    this.loadReceiptGroups()
    this.loadBillGroups()
    // 加载AI关联数据
    this.loadUnrelatedDataForAi()
  },

  methods: {
    // 加载银行回单
    async loadReceipts() {
      try {
        const response = await fetch('/api/merge-engine/unmerged-receipts', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.receipts = result.data || []
          console.log('加载银行回单成功:', this.receipts.length)
        }
      } catch (error) {
        this.$Message.error('加载银行回单失败: ' + error.message)
        console.error('加载银行回单失败:', error)
      }
    },

    // 加载票据
    async loadBills() {
      try {
        const response = await fetch('/api/merge-engine/unmerged-documents', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.bills = result.data || []
          console.log('加载票据成功:', this.bills.length)
        }
      } catch (error) {
        this.$Message.error('加载票据失败: ' + error.message)
        console.error('加载票据失败:', error)
      }
    },

    // 加载关联关系
    async loadRelations() {
      try {
        const response = await fetch('/api/entity-relations/list?page=1&size=100', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.relations = result.data.records || result.data || []
          console.log('加载关联关系成功:', this.relations.length)

          // 为每个关联获取实体预览信息
          await this.loadEntityPreviews()
        } else {
          this.$Message.error('加载关联关系失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('加载关联关系失败: ' + error.message)
        console.error('加载关联关系失败:', error)
      }
    },

    // 加载实体预览信息
    async loadEntityPreviews() {
      for (let relation of this.relations) {
        try {
          // 获取源实体预览
          if (relation.sourceType && relation.sourceId) {
            relation.sourcePreview = await this.getEntityPreview(relation.sourceType, relation.sourceId)
          }

          // 获取目标实体预览
          if (relation.targetType && relation.targetId) {
            relation.targetPreview = await this.getEntityPreview(relation.targetType, relation.targetId)
          }
        } catch (error) {
          console.warn('获取实体预览失败:', error)
        }
      }
    },

    // 获取单个实体的预览信息
    async getEntityPreview(entityType, entityId) {
      try {
        let url = ''
        if (entityType === 'DOCUMENT') {
          url = `/api/bills/${entityId}`
        } else if (entityType === 'RECEIPT') {
          url = `/api/bank-receipts/${entityId}`
        } else {
          return null
        }

        const response = await fetch(url, {
          credentials: 'include'
        })
        const result = await response.json()

        if (result.success && result.data) {
          const entity = result.data
          return {
            billNo: entity.billNo,
            receiptsNo: entity.receiptsNo,
            amount: entity.amount,
            billDate: entity.billDate,
            receiptsDate: entity.receiptsDate,
            issuer: entity.issuer,
            counterparty: entity.counterparty,
            summary: entity.summary
          }
        }
        return null
      } catch (error) {
        console.warn('获取实体预览失败:', error)
        return null
      }
    },

    // 搜索银行回单
    searchReceipts() {
      // 搜索逻辑已在computed中实现
      console.log('搜索银行回单:', this.receiptSearchKeyword)
    },

    // 搜索票据
    searchBills() {
      // 搜索逻辑已在computed中实现
      console.log('搜索票据:', this.billSearchKeyword)
    },

    // 银行回单选择事件
    onReceiptSelected(receipt) {
      console.log('选择银行回单:', receipt)
      this.loadRecommendedBills()
    },

    // 银行回单组合选择事件
    onReceiptGroupSelected() {
      console.log('选择银行回单组合:', this.selectedReceiptIds)
      this.loadRecommendedBills()
    },

    // 票据选择事件
    onBillSelected(bill) {
      console.log('选择票据:', bill)
    },

    // 票据组合选择事件
    onBillGroupSelected() {
      console.log('选择票据组合:', this.selectedBillIds)
    },

    // 加载推荐票据
    async loadRecommendedBills() {
      if (this.receiptSelectionMode === 'single' && !this.selectedReceiptId) {
        return
      }
      if (this.receiptSelectionMode === 'group' && this.selectedReceiptIds.length === 0) {
        return
      }

      try {
        // 获取选中的银行回单信息
        let selectedReceipts = []
        if (this.receiptSelectionMode === 'single') {
          selectedReceipts = this.receipts.filter(function(r) { return r.id === this.selectedReceiptId }.bind(this))
        } else {
          selectedReceipts = this.receipts.filter(function(r) { return this.selectedReceiptIds.includes(r.id) }.bind(this))
        }

        if (selectedReceipts.length === 0) {
          return
        }

        // 计算总金额和主要交易方
        const totalAmount = selectedReceipts.reduce(function(sum, r) { return sum + (r.amount || 0) }, 0)
        const mainCounterparty = selectedReceipts[0].counterparty

        const response = await fetch(`/api/ai-relation/recommended-bills?accountSetsId=66&amount=${totalAmount}&counterparty=${encodeURIComponent(mainCounterparty || '')}&type=ALL`, {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          // 将推荐的票据标记出来
          const recommendedBillIds = (result.data || []).map(function(b) { return b.id })
          this.bills = this.bills.map(function(bill) {
            return Object.assign({}, bill, {
              isRecommended: recommendedBillIds.includes(bill.id)
            })
          })
          console.log('加载推荐票据成功:', (result.data && result.data.length) || 0)
        }
      } catch (error) {
        console.error('加载推荐票据失败:', error)
      }
    },

    // 加载归并组
    async loadReceiptGroups() {
      try {
        const response = await fetch('/api/merge-groups/receipts', {
          method: 'GET',
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.receiptGroups = result.data || []
          console.log('加载银行回单归并组成功:', this.receiptGroups.length)
        } else {
          this.$Message.error('加载银行回单归并组失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('加载银行回单归并组失败: ' + error.message)
      }
    },

    async loadBillGroups() {
      try {
        const response = await fetch('/api/merge-groups/documents', {
          method: 'GET',
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.billGroups = result.data || []
          console.log('加载票据归并组成功:', this.billGroups.length)
        } else {
          this.$Message.error('加载票据归并组失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('加载票据归并组失败: ' + error.message)
      }
    },

    // 组选择事件处理
    onReceiptGroupSelected(group) {
      console.log('选择银行回单组:', group)
    },

    onBillGroupSelected(group) {
      console.log('选择票据组:', group)
    },

    onMultipleReceiptGroupsSelected() {
      console.log('选择多个银行回单组:', this.selectedReceiptGroupIds)
    },

    onMultipleBillGroupsSelected() {
      console.log('选择多个票据组:', this.selectedBillGroupIds)
    },

    // 创建关联
    async createRelation() {
      // 验证银行回单选择
      if (this.receiptSelectionMode === 'single' && !this.selectedReceiptId) {
        this.$Message.error('请选择银行回单')
        return
      }
      if (this.receiptSelectionMode === 'group' && this.selectedReceiptIds.length === 0) {
        this.$Message.error('请选择银行回单')
        return
      }
      if (this.receiptSelectionMode === 'existing-group' && !this.selectedReceiptGroupId) {
        this.$Message.error('请选择银行回单归并组')
        return
      }
      if (this.receiptSelectionMode === 'multiple-groups' && this.selectedReceiptGroupIds.length === 0) {
        this.$Message.error('请选择银行回单归并组')
        return
      }

      // 验证票据选择
      if (this.billSelectionMode === 'single' && !this.selectedBillId) {
        this.$Message.error('请选择票据')
        return
      }
      if (this.billSelectionMode === 'group' && this.selectedBillIds.length === 0) {
        this.$Message.error('请选择票据')
        return
      }
      if (this.billSelectionMode === 'existing-group' && !this.selectedBillGroupId) {
        this.$Message.error('请选择票据归并组')
        return
      }
      if (this.billSelectionMode === 'multiple-groups' && this.selectedBillGroupIds.length === 0) {
        this.$Message.error('请选择票据归并组')
        return
      }

      this.creating = true

      try {
        // 构建关联数据
        const relationData = {
          receiptSelectionMode: this.receiptSelectionMode,
          billSelectionMode: this.billSelectionMode,
          selectedReceiptId: this.selectedReceiptId,
          selectedReceiptIds: this.selectedReceiptIds,
          selectedBillId: this.selectedBillId,
          selectedBillIds: this.selectedBillIds,
          selectedReceiptGroupId: this.selectedReceiptGroupId,
          selectedReceiptGroupIds: this.selectedReceiptGroupIds,
          selectedBillGroupId: this.selectedBillGroupId,
          selectedBillGroupIds: this.selectedBillGroupIds,
          relationType: this.relationForm.relationType,
          relationAmount: this.relationForm.relationAmount,
          relationNote: this.relationForm.relationNote
        }

        // 调用新的关联创建API
        const response = await fetch('/api/entity-relations/create-complex', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(relationData)
        })

        const result = await response.json()
        if (!result.success) {
          throw new Error(result.msg || '创建关联失败')
        }

        this.$Message.success('关联创建成功')
        this.resetForm()
        this.loadRelations()

      } catch (error) {
        this.$Message.error('创建关联失败: ' + error.message)
        console.error('创建关联失败:', error)
      } finally {
        this.creating = false
      }
    },

    // 重置表单
    resetForm() {
      this.selectedReceiptId = null
      this.selectedReceiptIds = []
      this.selectedBillId = null
      this.selectedBillIds = []
      this.selectedReceiptGroupId = null
      this.selectedReceiptGroupIds = []
      this.selectedBillGroupId = null
      this.selectedBillGroupIds = []
      this.relationForm = {
        relationType: 'FULL',
        relationAmount: null,
        relationNote: ''
      }
      this.receiptSearchKeyword = ''
      this.billSearchKeyword = ''
    },

    // 查看关联详情
    // 显示关联详情浮窗
    async showRelationDetail(relation) {
      this.selectedRelation = relation
      this.sourceEntityDetail = null
      this.targetEntityDetail = null
      this.showDetailModal = true

      // 异步加载实体详情
      await this.loadEntityDetails()
    },

    // 加载实体详情信息
    async loadEntityDetails() {
      if (!this.selectedRelation) return

      try {
        // 加载源实体详情
        this.sourceEntityDetail = await this.loadEntityDetail(
          this.selectedRelation.sourceType,
          this.selectedRelation.sourceId
        )

        // 加载目标实体详情
        this.targetEntityDetail = await this.loadEntityDetail(
          this.selectedRelation.targetType,
          this.selectedRelation.targetId
        )
      } catch (error) {
        console.error('加载实体详情失败:', error)
      }
    },

    // 加载单个实体详情
    async loadEntityDetail(entityType, entityId) {
      try {
        let apiUrl = ''

        if (entityType === 'DOCUMENT') {
          // 加载票据详情
          apiUrl = `/api/bill/${entityId}`
        } else if (entityType === 'RECEIPT') {
          // 加载银证详情
          apiUrl = `/api/bank-receipts/${entityId}`
        } else if (entityType === 'DOCUMENT_GROUP') {
          // 加载票据组详情
          apiUrl = `/api/merge-groups/documents/${entityId}`
        } else if (entityType === 'RECEIPT_GROUP') {
          // 加载银证组详情
          apiUrl = `/api/merge-groups/receipts/${entityId}`
        } else {
          return { error: '未知实体类型' }
        }

        const response = await fetch(apiUrl, {
          credentials: 'include'
        })
        const result = await response.json()

        if (result.success) {
          return result.data
        } else {
          return { error: result.msg || '加载失败' }
        }
      } catch (error) {
        console.error('加载实体详情失败:', error)
        return { error: error.message }
      }
    },

    // 从详情浮窗删除关联
    deleteRelationFromDetail() {
      if (this.selectedRelation) {
        this.deleteRelation(this.selectedRelation.relationId)
        this.showDetailModal = false
      }
    },

    viewRelationDetail(relation) {
      // 保留原有的简单提示方法，以防其他地方调用
      const sourceType = this.formatEntityType(relation.sourceType)
      const targetType = this.formatEntityType(relation.targetType)
      this.$Message.info(`关联详情：${sourceType}:${relation.sourceId} <-> ${targetType}:${relation.targetId}`)
    },

    // 删除关联
    async deleteRelation(relationId) {
      var self = this
      this.$Confirm('确定要删除这个关联关系吗？').then(async function() {
        try {
          const response = await fetch(`/api/entity-relations/${relationId}`, {
            method: 'DELETE',
            credentials: 'include'
          })
          const result = await response.json()
          if (result.success) {
            self.$Message.success('关联关系删除成功')
            self.loadRelations()
          } else {
            self.$Message.error('删除失败: ' + result.msg)
          }
        } catch (error) {
          self.$Message.error('删除关联关系失败: ' + error.message)
        }
      })
    },

    // 导出关联关系
    exportRelations() {
      this.$Message.info('导出功能开发中')
    },

    // 预览批量关联结果
    previewBatchRelation() {
      this.$Message.info('批量关联预览功能开发中')
    },

    // AI分析范围相关方法
    hasAnalysisData() {
      const hasIndividualItems = this.aiUnrelatedBills.length > 0 || this.aiUnrelatedReceipts.length > 0
      const hasGroups = this.receiptGroups.length > 0 || this.billGroups.length > 0

      if (this.aiAnalysisScope.includeIndividualItems && hasIndividualItems) return true
      if (this.aiAnalysisScope.includeGroups && hasGroups) return true

      return false
    },

    getAnalysisDataSummary() {
      const parts = []

      if (this.aiAnalysisScope.includeIndividualItems) {
        parts.push(`${this.aiUnrelatedBills.length}票据 × ${this.aiUnrelatedReceipts.length}银证`)
      }

      if (this.aiAnalysisScope.includeGroups) {
        parts.push(`${this.billGroups.length}票据组 × ${this.receiptGroups.length}银证组`)
      }

      return parts.join(' + ') || '无数据'
    },

    getAnalysisTypesSummary() {
      if (!this.aiRelationSuggestions || !this.aiRelationSuggestions.relations) return ''

      const types = new Set()
      this.aiRelationSuggestions.relations.forEach(relation => {
        types.add(this.getRelationModeText(relation.relationMode))
      })

      return Array.from(types).join(', ')
    },

    getRelationModeText(mode) {
      const modeMap = {
        'SINGLE_TO_SINGLE': '一对一',
        'SINGLE_TO_GROUP': '一对组',
        'GROUP_TO_SINGLE': '组对一',
        'GROUP_TO_GROUP': '组对组',
        'SINGLE_TO_MERGED_GROUP': '一对归并组',
        'MERGED_GROUP_TO_SINGLE': '归并组对一',
        'GROUP_TO_MERGED_GROUP': '组对归并组',
        'MERGED_GROUP_TO_GROUP': '归并组对组',
        'MERGED_GROUP_TO_MERGED_GROUP': '归并组对归并组',
        'MULTIPLE_GROUPS_TO_SINGLE': '多组对一',
        'SINGLE_TO_MULTIPLE_GROUPS': '一对多组',
        'MULTIPLE_GROUPS_TO_MULTIPLE_GROUPS': '多组对多组'
      }
      return modeMap[mode] || mode
    },

    getRelationModeClass(mode) {
      if (mode && mode.includes('MULTIPLE')) return 'mode-complex'
      if (mode && mode.includes('GROUP')) return 'mode-group'
      return 'mode-simple'
    },

    formatEntityDisplay(type, id, name) {
      const typeMap = {
        'DOCUMENT': '票据',
        'RECEIPT': '银证',
        'DOCUMENT_GROUP': '票据组',
        'RECEIPT_GROUP': '银证组'
      }
      const typeText = typeMap[type] || type
      return name ? `${typeText}:${name}` : `${typeText}:${id}`
    },

    formatEntityType(type) {
      const typeMap = {
        'DOCUMENT': '票据',
        'RECEIPT': '银证',
        'DOCUMENT_GROUP': '票据组',
        'RECEIPT_GROUP': '银证组'
      }
      return typeMap[type] || type
    },

    getEntityTypeClass(type) {
      const classMap = {
        'DOCUMENT': 'document',
        'RECEIPT': 'receipt',
        'DOCUMENT_GROUP': 'document-group',
        'RECEIPT_GROUP': 'receipt-group'
      }
      return classMap[type] || 'document'
    },

    formatDate(dateStr) {
      if (!dateStr) return '-'
      try {
        const date = new Date(dateStr)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (e) {
        return dateStr
      }
    },

    // AI智能关联方法
    async loadUnrelatedDataForAi() {
      try {
        // 加载未关联的票据
        const billsResponse = await fetch('/api/ai-relation/unrelated/bills?limit=50', {
          credentials: 'include'
        })
        const billsResult = await billsResponse.json()
        if (billsResult.success) {
          this.aiUnrelatedBills = billsResult.data || []
        }

        // 加载未关联的银证
        const receiptsResponse = await fetch('/api/ai-relation/unrelated/receipts?limit=50', {
          credentials: 'include'
        })
        const receiptsResult = await receiptsResponse.json()
        if (receiptsResult.success) {
          this.aiUnrelatedReceipts = receiptsResult.data || []
        }

        this.$Message.success(`加载了${this.aiUnrelatedBills.length}个未关联票据和${this.aiUnrelatedReceipts.length}个未关联银证`)
      } catch (error) {
        this.$Message.error('加载未关联数据失败: ' + error.message)
      }
    },

    async analyzeRelationsWithAi() {
      if (this.aiAnalyzing) return

      try {
        this.aiAnalyzing = true
        this.$Message.info('🤖 AI正在进行智能关联分析，请稍候...')

        // 构建分析请求数据
        const analysisData = {
          analysisScope: this.aiAnalysisScope,
          individualItems: {},
          groups: {}
        }

        // 包含单个项目
        if (this.aiAnalysisScope.includeIndividualItems) {
          analysisData.individualItems = {
            billIds: this.aiUnrelatedBills.map(bill => bill.id),
            receiptIds: this.aiUnrelatedReceipts.map(receipt => receipt.id)
          }
        }

        // 包含归并组
        if (this.aiAnalysisScope.includeGroups) {
          analysisData.groups = {
            billGroupIds: this.billGroups.map(group => group.groupId),
            receiptGroupIds: this.receiptGroups.map(group => group.groupId)
          }
        }

        const response = await fetch('/api/ai-relation/analyze-complex', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(analysisData)
        })
        const result = await response.json()
        if (result.success) {
          this.aiRelationSuggestions = result.data
          this.selectedAiRelations = [] // 重置选择
          this.$Message.success('✅ AI智能分析完成: ' + result.data.message)
        } else {
          this.$Message.error('❌ AI分析失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('❌ AI分析失败: ' + error.message)
      } finally {
        this.aiAnalyzing = false
      }
    },

    // AI关联选择相关方法
    selectAllAiRelations() {
      if (this.aiRelationSuggestions && this.aiRelationSuggestions.relations) {
        this.selectedAiRelations = this.aiRelationSuggestions.relations.map((_, index) => index)
      }
    },

    clearAiRelationSelection() {
      this.selectedAiRelations = []
    },

    selectHighConfidenceRelations() {
      if (this.aiRelationSuggestions && this.aiRelationSuggestions.relations) {
        this.selectedAiRelations = this.aiRelationSuggestions.relations
          .map((relation, index) => ({ relation, index }))
          .filter(item => item.relation.confidence >= 0.8)
          .map(item => item.index)
      }
    },

    toggleSelectAllRelations() {
      if (this.selectedAiRelations.length === this.aiRelationSuggestions.relations.length) {
        this.clearAiRelationSelection()
      } else {
        this.selectAllAiRelations()
      }
    },

    // 执行选中的AI关联
    async executeSelectedAiRelations() {
      if (this.selectedAiRelations.length === 0) {
        this.$Message.error('请先选择要执行的关联')
        return
      }

      try {
        const selectedRelations = this.selectedAiRelations.map(index => this.aiRelationSuggestions.relations[index])

        this.$Message.info(`正在执行${selectedRelations.length}个关联操作...`)

        const response = await fetch('/api/ai-relation/execute-selected-complex', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            selectedRelations: selectedRelations
          })
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('✅ 选中关联执行成功: ' + result.data.message)
          this.aiRelationSuggestions = null
          this.selectedAiRelations = []
          this.loadUnrelatedDataForAi()
          this.loadRelations()
        } else {
          this.$Message.error('❌ 选中关联失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('❌ 选中关联失败: ' + error.message)
      }
    },

    async executeAiRelations() {
      try {
        const billIds = this.aiUnrelatedBills.map(bill => bill.id)
        const receiptIds = this.aiUnrelatedReceipts.map(receipt => receipt.id)

        const response = await fetch('/api/ai-relation/execute', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            billIds: billIds,
            receiptIds: receiptIds
          })
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('AI关联完成: ' + result.data.message)
          this.aiRelationSuggestions = null
          this.loadUnrelatedDataForAi()
          this.loadRelations()
        } else {
          this.$Message.error('AI关联失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('AI关联失败: ' + error.message)
      }
    },

    async autoRelateAllWithAi() {
      this.$Confirm('确定要执行一键智能关联吗？AI将自动分析并关联所有未关联的票据和银证。').then(async () => {
        try {
          const response = await fetch('/api/ai-relation/auto-relate-all?limit=50', {
            method: 'POST',
            credentials: 'include'
          })
          const result = await response.json()
          if (result.success) {
            this.$Message.success('一键智能关联完成: ' + result.data.message)
            this.loadUnrelatedDataForAi()
            this.loadRelations()
          } else {
            this.$Message.error('一键关联失败: ' + result.msg)
          }
        } catch (error) {
          this.$Message.error('一键关联失败: ' + error.message)
        }
      })
    },

    getConfidenceClass(confidence) {
      if (confidence >= 0.8) return 'confidence-high'
      if (confidence >= 0.6) return 'confidence-medium'
      return 'confidence-low'
    },

    // 执行批量关联
    async executeBatchRelation() {
      this.batchProcessing = true

      try {
        // 模拟批量关联逻辑
        await new Promise(function(resolve) { setTimeout(resolve, 2000) })
        this.$Message.success('批量关联执行成功')
        this.loadRelations()
      } catch (error) {
        this.$Message.error('批量关联失败: ' + error.message)
      } finally {
        this.batchProcessing = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.relation-create-panel, .relation-list-panel, .batch-relation-panel {
  padding: 20px;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.form-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.selection-mode {
  margin-bottom: 15px;
}

.search-box {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  align-items: center;
}

.form-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  flex: 1;
}

.selection-table {
  max-height: 300px;
  overflow-y: auto;
}

.relation-info {
  padding: 15px;
  background: white;
  border-radius: 4px;
}

.form-row {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.form-row label {
  width: 100px;
  text-align: right;
  margin-right: 15px;
  font-weight: bold;
}

.form-textarea {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 300px;
  resize: vertical;
}

.form-actions {
  text-align: center;
  padding: 20px 0;
}

.toolbar {
  margin-bottom: 20px;
}

.batch-rules {
  padding: 15px;
  background: white;
  border-radius: 4px;
}

.rule-desc {
  margin-left: 10px;
  color: #666;
  font-size: 12px;
}

/* 推荐样式 */
.recommended-row {
  background-color: #f0f9ff !important;
  border-left: 3px solid #409eff;
}

.recommended-tag {
  display: inline-block;
  background: #409eff;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 5px;
}

/* AI智能关联样式 */
.ai-relation-panel {
  padding: 20px;
}

.ai-info-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.ai-info-panel h4 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.ai-info-panel p {
  margin: 0;
  opacity: 0.9;
}

.ai-suggestions {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.suggestion-summary {
  margin-bottom: 15px;
}

.suggestion-summary .h-tag {
  margin-right: 10px;
}

.suggestion-actions {
  text-align: right;
  margin-bottom: 15px;
}

.suggestion-actions .h-btn {
  margin-left: 8px;
}

.toolbar .h-btn {
  margin-right: 8px;
}

.confidence-score {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.confidence-high {
  background: #d4edda;
  color: #155724;
}

.confidence-medium {
  background: #fff3cd;
  color: #856404;
}

.confidence-low {
  background: #f8d7da;
  color: #721c24;
}

.data-overview {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.overview-section {
  flex: 1;
}

.overview-section h4 {
  margin-bottom: 15px;
  color: #495057;
}

.data-table-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 6px;
}

.more-data-hint {
  text-align: center;
  padding: 10px;
  background: #f8f9fa;
  color: #6c757d;
  font-style: italic;
  border-top: 1px solid #dee2e6;
}

.ai-analysis-scope {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 16px;
}

.ai-analysis-scope h4 {
  margin: 0 0 12px 0;
  color: #1e40af;
  font-size: 16px;
}

.scope-options {
  margin-bottom: 12px;
}

.scope-options .h-checkbox {
  margin-right: 20px;
}

.scope-description {
  font-size: 12px;
  color: #6b7280;
}

.scope-description p {
  margin: 4px 0;
}

.relation-mode-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-align: center;
}

.mode-simple {
  background: #dbeafe;
  color: #1e40af;
}

.mode-group {
  background: #fef3c7;
  color: #d97706;
}

.mode-complex {
  background: #fce7f3;
  color: #be185d;
}

/* 关联详情浮窗样式 */
.relation-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #1890ff;
  font-size: 16px;
  font-weight: 600;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-weight: 600;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.detail-item span {
  color: #333;
}

.entity-info {
  margin-top: 12px;
}

.entity-card {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
}

.entity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.entity-type {
  background: #1890ff;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.entity-id {
  color: #666;
  font-size: 12px;
  font-family: monospace;
}

.entity-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-row label {
  font-weight: 600;
  color: #666;
  margin-right: 8px;
  min-width: 60px;
  font-size: 12px;
}

.detail-row span {
  color: #333;
  font-size: 12px;
}

.detail-row .amount {
  color: #f5222d;
  font-weight: 600;
}

.loading-text {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}

.relation-note {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 12px;
  color: #586069;
  font-size: 14px;
  line-height: 1.5;
}

/* 关联列表增强样式 */
.entity-summary {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.entity-type-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  color: white;
}

.entity-type-badge.document {
  background: #52c41a;
}

.entity-type-badge.receipt {
  background: #1890ff;
}

.entity-type-badge.document-group {
  background: #722ed1;
}

.entity-type-badge.receipt-group {
  background: #eb2f96;
}

.entity-info {
  margin-top: 4px;
}

.entity-id {
  font-size: 11px;
  color: #999;
  font-family: monospace;
}

.entity-preview {
  font-size: 12px;
  color: #333;
  margin-top: 2px;
}

.amount-preview {
  color: #f5222d;
  font-weight: 600;
  margin-left: 8px;
}

.relation-type-badge {
  background: #faad14;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.match-status {
  font-size: 12px;
  font-weight: 600;
}

.match-yes {
  color: #52c41a;
}

.match-no {
  color: #ff4d4f;
}
</style>
