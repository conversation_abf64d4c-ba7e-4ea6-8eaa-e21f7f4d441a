<template>
  <div class="function-demo">
    <div class="page-header">
      <h2>功能演示</h2>
      <p>票据银证归并系统功能演示和操作指南</p>
    </div>

    <!-- 功能导航 -->
    <div class="demo-nav">
      <div class="nav-item" 
           v-for="demo in demoList" 
           :key="demo.key"
           :class="{ active: currentDemo === demo.key }"
           @click="switchDemo(demo.key)">
        <i :class="demo.icon"></i>
        <span>{{ demo.title }}</span>
      </div>
    </div>

    <!-- 演示内容 -->
    <div class="demo-content">
      <!-- 系统概览 -->
      <div v-if="currentDemo === 'overview'" class="demo-section">
        <h3>系统概览</h3>
        <div class="overview-grid">
          <div class="overview-card">
            <div class="card-icon">
              <i class="h-icon-menu"></i>
            </div>
            <div class="card-content">
              <h4>票据管理</h4>
              <p>支持各类票据的录入、查询和管理，包括发票、收据等</p>
              <ul>
                <li>票据信息录入</li>
                <li>票据分类管理</li>
                <li>票据状态跟踪</li>
              </ul>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="card-icon">
              <i class="h-icon-complete"></i>
            </div>
            <div class="card-content">
              <h4>银证管理</h4>
              <p>银行流水和证券交易记录的统一管理</p>
              <ul>
                <li>银行流水导入</li>
                <li>证券交易记录</li>
                <li>资金流向分析</li>
              </ul>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="card-icon">
              <i class="h-icon-link"></i>
            </div>
            <div class="card-content">
              <h4>关联归并</h4>
              <p>智能识别票据与银证之间的关联关系</p>
              <ul>
                <li>自动关联匹配</li>
                <li>手动关联建立</li>
                <li>关联关系管理</li>
              </ul>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="card-icon">
              <i class="h-icon-search"></i>
            </div>
            <div class="card-content">
              <h4>统一查询</h4>
              <p>跨票据和银证的统一查询分析</p>
              <ul>
                <li>多维度查询</li>
                <li>关联关系查看</li>
                <li>数据导出功能</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作流程 -->
      <div v-if="currentDemo === 'workflow'" class="demo-section">
        <h3>操作流程</h3>
        <div class="workflow-steps">
          <div class="step-item" v-for="(step, index) in workflowSteps" :key="index">
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <h4>{{ step.title }}</h4>
              <p>{{ step.description }}</p>
              <div class="step-actions">
                <Button size="small" color="primary" @click="goToPage(step.route)">
                  {{ step.action }}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能特性 -->
      <div v-if="currentDemo === 'features'" class="demo-section">
        <h3>功能特性</h3>
        <div class="features-list">
          <div class="feature-group" v-for="group in featureGroups" :key="group.title">
            <h4>{{ group.title }}</h4>
            <div class="feature-items">
              <div class="feature-item" v-for="feature in group.features" :key="feature.name">
                <div class="feature-icon">
                  <i :class="feature.icon"></i>
                </div>
                <div class="feature-info">
                  <h5>{{ feature.name }}</h5>
                  <p>{{ feature.description }}</p>
                </div>
                <div class="feature-status">
                  <span class="status-badge" :class="feature.status">
                    {{ getStatusText(feature.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据统计 -->
      <div v-if="currentDemo === 'statistics'" class="demo-section">
        <h3>数据统计</h3>
        <div class="statistics-actions">
          <Button @click="loadStatistics" icon="h-icon-refresh">刷新统计</Button>
        </div>

        <div class="statistics-grid">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.totalDocuments }}</div>
            <div class="stat-label">票据总数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ statistics.totalReceipts }}</div>
            <div class="stat-label">银证总数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ statistics.totalRelations }}</div>
            <div class="stat-label">关联关系</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ statistics.mergedGroups }}</div>
            <div class="stat-label">归并组数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ statistics.totalRules }}</div>
            <div class="stat-label">归并规则</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ statistics.totalDocumentGroups }}</div>
            <div class="stat-label">票据归并组</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ statistics.totalReceiptGroups }}</div>
            <div class="stat-label">银证归并组</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ statistics.activeRules }}</div>
            <div class="stat-label">活跃规则</div>
          </div>
        </div>

        <div class="chart-section">
          <h4>数据趋势</h4>
          <div class="chart-placeholder">
            <p>图表功能开发中...</p>
            <p>将展示票据、银证录入趋势和关联关系统计</p>
          </div>
        </div>
      </div>

      <!-- 使用指南 -->
      <div v-if="currentDemo === 'guide'" class="demo-section">
        <h3>使用指南</h3>
        <div class="guide-content">
          <div class="guide-section">
            <h4>快速开始</h4>
            <ol>
              <li>首先录入或导入票据数据</li>
              <li>录入或导入银证数据</li>
              <li>使用自动归并功能建立关联</li>
              <li>手动调整和完善关联关系</li>
              <li>使用统一查询进行数据分析</li>
            </ol>
          </div>
          
          <div class="guide-section">
            <h4>最佳实践</h4>
            <ul>
              <li>定期备份数据，确保数据安全</li>
              <li>建立规范的数据录入流程</li>
              <li>合理设置归并规则，提高自动匹配准确率</li>
              <li>定期检查和维护关联关系</li>
              <li>充分利用查询功能进行数据分析</li>
            </ul>
          </div>
          
          <div class="guide-section">
            <h4>常见问题</h4>
            <div class="faq-list">
              <div class="faq-item">
                <h5>Q: 如何提高自动归并的准确率？</h5>
                <p>A: 可以通过调整归并规则参数，如金额容差、时间窗口等，并确保数据录入的规范性。</p>
              </div>
              <div class="faq-item">
                <h5>Q: 误建立的关联关系如何删除？</h5>
                <p>A: 在关联关系管理页面或统一查询页面，可以查看和删除不正确的关联关系。</p>
              </div>
              <div class="faq-item">
                <h5>Q: 如何导出查询结果？</h5>
                <p>A: 在统一查询页面执行查询后，点击"导出"按钮即可导出Excel格式的数据。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速演示 -->
      <div v-if="currentDemo === 'quick-demo'" class="demo-section">
        <h3>快速演示</h3>
        <div class="quick-demo-grid">
          <div class="demo-card">
            <div class="demo-icon">
              <i class="h-icon-magic"></i>
            </div>
            <div class="demo-content">
              <h4>手动归并演示</h4>
              <p>选择测试数据进行手动归并操作</p>
              <Button color="primary" @click="showManualMergeDemo">开始演示</Button>
            </div>
          </div>

          <div class="demo-card">
            <div class="demo-icon">
              <i class="h-icon-link"></i>
            </div>
            <div class="demo-content">
              <h4>关联创建演示</h4>
              <p>创建票据和银证之间的关联关系</p>
              <Button color="primary" @click="showRelationCreateDemo">开始演示</Button>
            </div>
          </div>

          <div class="demo-card">
            <div class="demo-icon">
              <i class="h-icon-settings"></i>
            </div>
            <div class="demo-content">
              <h4>规则配置演示</h4>
              <p>演示归并规则的配置和使用</p>
              <Button color="primary" @click="showRuleConfigDemo">开始演示</Button>
            </div>
          </div>

          <div class="demo-card">
            <div class="demo-icon">
              <i class="h-icon-search"></i>
            </div>
            <div class="demo-content">
              <h4>查询分析演示</h4>
              <p>演示统一查询和数据分析功能</p>
              <Button color="primary" @click="showQueryDemo">开始演示</Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 手动归并演示对话框 -->
    <Modal v-model="manualMergeVisible" title="手动归并演示" width="800px">
      <div class="merge-demo-content">
        <h4>演示数据</h4>
        <table class="h-table h-table-border">
          <thead>
            <tr>
              <th style="width: 50px">选择</th>
              <th>编号</th>
              <th>日期</th>
              <th>金额</th>
              <th>摘要</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in demoItems" :key="item.id">
              <td>
                <Checkbox v-model="selectedDemoItems" :value="item.id"></Checkbox>
              </td>
              <td>{{ item.billNo }}</td>
              <td>{{ item.billDate }}</td>
              <td>¥{{ item.amount }}</td>
              <td>{{ item.summary }}</td>
            </tr>
          </tbody>
        </table>

        <div class="demo-actions">
          <div class="form-row">
            <label>归并组名称：</label>
            <input v-model="demoGroupName" placeholder="请输入归并组名称"
                   class="form-input" style="width: 300px;" />
          </div>
          <Button color="primary" @click="executeDemoMerge"
                  :disabled="selectedDemoItems.length < 2">
            执行演示归并
          </Button>
        </div>
      </div>
    </Modal>

    <!-- 关联创建演示对话框 -->
    <Modal v-model="relationCreateVisible" title="关联创建演示" width="700px">
      <div class="relation-demo-content">
        <h4>创建票据与银证的关联关系</h4>
        <div class="relation-form">
          <div class="form-row">
            <label>源类型：</label>
            <Select v-model="demoRelation.sourceType" :datas="entityTypeOptions"
                    style="width: 150px;" />
          </div>
          <div class="form-row">
            <label>目标类型：</label>
            <Select v-model="demoRelation.targetType" :datas="entityTypeOptions"
                    style="width: 150px;" />
          </div>
          <div class="form-row">
            <label>关联类型：</label>
            <Select v-model="demoRelation.relationType" :datas="relationTypeOptions"
                    style="width: 200px;" />
          </div>
          <div class="form-row">
            <label>关联金额：</label>
            <input v-model.number="demoRelation.amount" type="number"
                   placeholder="关联金额" class="form-input" style="width: 150px;" />
          </div>
          <div class="form-row">
            <label>关联备注：</label>
            <textarea v-model="demoRelation.note" placeholder="关联备注"
                      class="form-textarea" rows="3"></textarea>
          </div>
          <Button color="primary" @click="executeDemoRelation">
            创建演示关联
          </Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'FunctionDemo',
  data() {
    return {
      currentDemo: 'overview',
      
      demoList: [
        { key: 'overview', title: '系统概览', icon: 'h-icon-home' },
        { key: 'workflow', title: '操作流程', icon: 'h-icon-edit' },
        { key: 'features', title: '功能特性', icon: 'h-icon-star' },
        { key: 'statistics', title: '数据统计', icon: 'h-icon-calendar' },
        { key: 'quick-demo', title: '快速演示', icon: 'h-icon-location' },
        { key: 'guide', title: '使用指南', icon: 'h-icon-help' }
      ],
      
      workflowSteps: [
        {
          title: '数据录入',
          description: '录入或导入票据和银证数据',
          action: '开始录入',
          route: '/merge-management'
        },
        {
          title: '设置规则',
          description: '配置自动归并规则',
          action: '管理规则',
          route: '/merge-rule-management'
        },
        {
          title: '自动归并',
          description: '执行自动归并匹配',
          action: '执行归并',
          route: '/merge-management'
        },
        {
          title: '手动调整',
          description: '手动建立和调整关联关系',
          action: '管理关联',
          route: '/merge-relation-management'
        },
        {
          title: '查询分析',
          description: '使用统一查询进行数据分析',
          action: '开始查询',
          route: '/unified-query'
        }
      ],
      
      featureGroups: [
        {
          title: '核心功能',
          features: [
            { name: '票据管理', description: '票据录入、编辑、删除', icon: 'h-icon-file-text', status: 'completed' },
            { name: '银证管理', description: '银证录入、编辑、删除', icon: 'h-icon-credit-card', status: 'completed' },
            { name: '自动归并', description: '基于规则的自动关联', icon: 'h-icon-magic', status: 'completed' },
            { name: '手动归并', description: '手动建立关联关系', icon: 'h-icon-hand-paper', status: 'completed' }
          ]
        },
        {
          title: '高级功能',
          features: [
            { name: '规则管理', description: '归并规则配置管理', icon: 'h-icon-settings', status: 'completed' },
            { name: '关联管理', description: '关联关系查看管理', icon: 'h-icon-link', status: 'completed' },
            { name: '统一查询', description: '跨类型数据查询', icon: 'h-icon-search', status: 'completed' },
            { name: '数据导出', description: '查询结果导出', icon: 'h-icon-download', status: 'development' }
          ]
        },
        {
          title: '扩展功能',
          features: [
            { name: '数据可视化', description: '图表展示数据趋势', icon: 'h-icon-bar-chart', status: 'planning' },
            { name: '批量操作', description: '批量数据处理', icon: 'h-icon-layers', status: 'planning' },
            { name: '权限管理', description: '用户权限控制', icon: 'h-icon-user-check', status: 'planning' },
            { name: 'API接口', description: '开放API接口', icon: 'h-icon-code', status: 'planning' }
          ]
        }
      ],
      
      statistics: {
        totalDocuments: 0,
        totalReceipts: 0,
        totalRelations: 0,
        mergedGroups: 0,
        totalRules: 0,
        totalDocumentGroups: 0,
        totalReceiptGroups: 0,
        activeRules: 0
      },

      // 演示数据
      manualMergeVisible: false,
      relationCreateVisible: false,
      selectedDemoItems: [],
      demoGroupName: '演示归并组',
      demoItems: [
        {
          id: 'demo-1',
          billNo: 'DEMO-001',
          billDate: '2025-06-17',
          amount: 1000.00,
          summary: '演示票据1 - 办公用品采购'
        },
        {
          id: 'demo-2',
          billNo: 'DEMO-002',
          billDate: '2025-06-17',
          amount: 1500.00,
          summary: '演示票据2 - 设备采购'
        },
        {
          id: 'demo-3',
          billNo: 'DEMO-003',
          billDate: '2025-06-18',
          amount: 800.00,
          summary: '演示票据3 - 服务费'
        }
      ],

      demoRelation: {
        sourceType: 'DOCUMENT',
        targetType: 'RECEIPT',
        relationType: 'AMOUNT_MATCH',
        amount: 1000.00,
        note: '演示关联关系'
      },

      entityTypeOptions: [
        { key: 'DOCUMENT', title: '票据' },
        { key: 'RECEIPT', title: '银证' }
      ],

      relationTypeOptions: [
        { key: 'AMOUNT_MATCH', title: '金额匹配' },
        { key: 'DATE_MATCH', title: '日期匹配' },
        { key: 'MANUAL_LINK', title: '手动关联' }
      ]
    }
  },
  
  mounted() {
    this.loadStatistics()
  },
  
  methods: {
    switchDemo(key) {
      this.currentDemo = key
    },
    
    goToPage(route) {
      this.$router.push(route)
    },
    
    getStatusText(status) {
      const statusMap = {
        'completed': '已完成',
        'development': '开发中',
        'planning': '规划中'
      }
      return statusMap[status] || status
    },
    
    async loadStatistics() {
      try {
        // 加载统计数据
        const response = await fetch('/api/statistics/overview', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.statistics = { ...this.statistics, ...result.data }
        }

        // 加载归并规则数量
        const rulesResponse = await fetch('/api/merge-rules/list', {
          credentials: 'include'
        })
        const rulesResult = await rulesResponse.json()
        if (rulesResult.success) {
          this.statistics.totalRules = (rulesResult.data || []).length
        }

        // 加载归并组数量
        const docGroupsResponse = await fetch('/api/merge-groups/documents', {
          credentials: 'include'
        })
        const docGroupsResult = await docGroupsResponse.json()
        if (docGroupsResult.success) {
          this.statistics.totalDocumentGroups = (docGroupsResult.data || []).length
        }

        const receiptGroupsResponse = await fetch('/api/merge-groups/receipts', {
          credentials: 'include'
        })
        const receiptGroupsResult = await receiptGroupsResponse.json()
        if (receiptGroupsResult.success) {
          this.statistics.totalReceiptGroups = (receiptGroupsResult.data || []).length
        }

        console.log('统计数据加载成功:', this.statistics)
      } catch (error) {
        console.error('加载统计数据失败:', error)
        this.$Message.error('加载统计数据失败: ' + error.message)
        // 设置默认值
        this.statistics = {
          totalDocuments: 0,
          totalReceipts: 0,
          totalRelations: 0,
          mergedGroups: 0,
          totalRules: 0,
          totalDocumentGroups: 0,
          totalReceiptGroups: 0,
          activeRules: 0
        }
      }
    },

    // 显示手动归并演示
    showManualMergeDemo() {
      this.selectedDemoItems = []
      this.demoGroupName = '演示归并组'
      this.manualMergeVisible = true
    },

    // 显示关联创建演示
    showRelationCreateDemo() {
      this.demoRelation = {
        sourceType: 'DOCUMENT',
        targetType: 'RECEIPT',
        relationType: 'AMOUNT_MATCH',
        amount: 1000.00,
        note: '演示关联关系'
      }
      this.relationCreateVisible = true
    },

    // 显示规则配置演示
    showRuleConfigDemo() {
      this.$Message.info('规则配置演示：请前往"归并规则管理"页面查看和配置归并规则')
      this.$router.push('/merge-rule-management')
    },

    // 显示查询演示
    showQueryDemo() {
      this.$Message.info('查询分析演示：请前往"统一查询"页面体验查询功能')
      this.$router.push('/unified-query')
    },

    // 执行演示归并
    executeDemoMerge() {
      if (this.selectedDemoItems.length < 2) {
        this.$Message.error('请至少选择2个项目进行归并')
        return
      }

      // 模拟归并操作
      setTimeout(() => {
        this.$Message.success(`演示归并成功！已将 ${this.selectedDemoItems.length} 个项目归并到"${this.demoGroupName}"`)
        this.manualMergeVisible = false
        this.loadStatistics()
      }, 1000)
    },

    // 执行演示关联
    executeDemoRelation() {
      if (!this.demoRelation.sourceType || !this.demoRelation.targetType) {
        this.$Message.error('请选择源类型和目标类型')
        return
      }

      // 模拟关联创建
      setTimeout(() => {
        this.$Message.success(`演示关联创建成功！已创建${this.demoRelation.sourceType}与${this.demoRelation.targetType}的关联关系`)
        this.relationCreateVisible = false
        this.loadStatistics()
      }, 1000)
    }
  }
}
</script>

<style lang="less" scoped>
.function-demo {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.demo-nav {
  display: flex;
  margin-bottom: 30px;
  border-bottom: 1px solid #e8e8e8;
}

.nav-item {
  padding: 15px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-item:hover {
  background-color: #f5f7fa;
}

.nav-item.active {
  color: #409eff;
  border-bottom-color: #409eff;
}

.demo-content {
  min-height: 500px;
}

.demo-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 20px;
}

/* 系统概览样式 */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  background: white;
  transition: box-shadow 0.3s;
}

.overview-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 15px;
}

.card-content h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.card-content p {
  margin: 0 0 15px 0;
  color: #606266;
  line-height: 1.5;
}

.card-content ul {
  margin: 0;
  padding-left: 20px;
  color: #909399;
}

.card-content li {
  margin-bottom: 5px;
}

/* 操作流程样式 */
.workflow-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: white;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.step-content p {
  margin: 0 0 15px 0;
  color: #606266;
  line-height: 1.5;
}

/* 功能特性样式 */
.features-list {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.feature-group h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.feature-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: white;
}

.feature-icon {
  font-size: 24px;
  color: #409eff;
  flex-shrink: 0;
}

.feature-info {
  flex: 1;
}

.feature-info h5 {
  margin: 0 0 5px 0;
  color: #303133;
}

.feature-info p {
  margin: 0;
  color: #606266;
  font-size: 12px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.status-badge.completed {
  background-color: #67c23a;
}

.status-badge.development {
  background-color: #e6a23c;
}

.status-badge.planning {
  background-color: #909399;
}

/* 数据统计样式 */
.statistics-actions {
  margin-bottom: 20px;
  text-align: right;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.stat-card {
  text-align: center;
  padding: 30px 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: white;
}

.stat-number {
  font-size: 36px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 10px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.chart-section {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  background: white;
}

.chart-section h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: 4px;
  color: #909399;
}

/* 使用指南样式 */
.guide-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.guide-section {
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: white;
}

.guide-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.guide-section ol,
.guide-section ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  line-height: 1.6;
}

.guide-section li {
  margin-bottom: 8px;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.faq-item {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 6px;
}

.faq-item h5 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.faq-item p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

/* 快速演示样式 */
.quick-demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.demo-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: white;
  transition: box-shadow 0.3s;
}

.demo-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.demo-icon {
  font-size: 32px;
  color: #409eff;
  flex-shrink: 0;
}

.demo-content {
  flex: 1;
}

.demo-content h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.demo-content p {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

/* 演示对话框样式 */
.merge-demo-content,
.relation-demo-content {
  padding: 20px 0;
}

.merge-demo-content h4,
.relation-demo-content h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.demo-actions {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

.relation-form {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 6px;
}

.form-row {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.form-row label {
  width: 100px;
  text-align: right;
  margin-right: 15px;
  font-weight: bold;
}

.form-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-textarea {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 300px;
  resize: vertical;
}
</style>
