<template>
  <div class="merge-rule-management">
    <div class="page-header">
      <h2>归并规则管理</h2>
      <p>管理票据和银证的归并规则配置</p>
    </div>

    <div class="toolbar margin-bottom">
      <Button color="primary" @click="showCreateRuleDialog">
        <i class="h-icon-plus"></i> 创建规则
      </Button>
      <Button @click="loadMergeRules" icon="h-icon-refresh">刷新</Button>
    </div>

    <!-- 规则列表 -->
    <table class="h-table h-table-border">
      <thead>
        <tr>
          <th style="width: 200px">规则名称</th>
          <th style="width: 120px">适用类型</th>
          <th style="width: 100px">状态</th>
          <th>规则描述</th>
          <th style="width: 150px">创建时间</th>
          <th style="width: 200px">操作</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="rule in mergeRules" :key="rule.ruleId">
          <td>{{ rule.ruleName }}</td>
          <td>
            <span class="rule-type" :class="rule.applicableEntity.toLowerCase()">
              {{ getEntityTypeText(rule.applicableEntity) }}
            </span>
          </td>
          <td>
            <span class="status-badge" :class="rule.isActive ? 'active' : 'inactive'">
              {{ rule.isActive ? '启用' : '禁用' }}
            </span>
          </td>
          <td>{{ rule.ruleDescription }}</td>
          <td>{{ formatDate(rule.createdAt) }}</td>
          <td>
            <Button size="small" @click="viewRuleDetail(rule)">详情</Button>
            <Button size="small" color="primary" @click="editRule(rule)">编辑</Button>
            <Button size="small" :color="rule.isActive ? 'yellow' : 'green'"
                    @click="toggleRule(rule)">
              {{ rule.isActive ? '禁用' : '启用' }}
            </Button>
            <Button size="small" color="red" @click="deleteRule(rule.ruleId)">删除</Button>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- 创建/编辑规则对话框 -->
    <Modal v-model="ruleDialogVisible" :title="dialogTitle" width="700px"
           @on-ok="saveRule" @on-cancel="cancelRuleDialog">
      <div class="rule-form">
        <div class="form-row">
          <label>规则名称：</label>
          <input v-model="ruleForm.ruleName" placeholder="请输入规则名称"
                 class="form-input" style="width: 300px;" />
        </div>

        <div class="form-row">
          <label>规则描述：</label>
          <textarea v-model="ruleForm.ruleDescription" placeholder="请输入规则描述"
                    class="form-textarea" rows="3"></textarea>
        </div>

        <div class="form-row">
          <label>适用类型：</label>
          <Select v-model="ruleForm.applicableEntity" :datas="entityTypeOptions"
                  style="width: 200px;" />
        </div>

        <div class="form-row">
          <label>规则类型：</label>
          <Select v-model="ruleForm.ruleType" :datas="ruleTypeOptions"
                  style="width: 250px;" @change="onRuleTypeChange" />
        </div>

        <!-- 规则参数配置 -->
        <div v-if="ruleForm.ruleType === 'SAME_PERSON_DATE_TYPE'" class="rule-params">
          <h4>同人同日同类归并参数</h4>
          <div class="form-row">
            <label>日期容差(天)：</label>
            <input v-model.number="ruleForm.dateTolerance" type="number" min="0" max="30"
                   class="form-input" style="width: 100px;" />
          </div>
          <div class="form-row">
            <label>金额容差：</label>
            <input v-model.number="ruleForm.amountTolerance" type="number" min="0" step="0.01"
                   class="form-input" style="width: 150px;" />
          </div>
        </div>

        <div v-if="ruleForm.ruleType === 'AMOUNT_RANGE'" class="rule-params">
          <h4>金额范围归并参数</h4>
          <div class="form-row">
            <label>最小金额：</label>
            <input v-model.number="ruleForm.minAmount" type="number" min="0" step="0.01"
                   class="form-input" style="width: 150px;" />
          </div>
          <div class="form-row">
            <label>最大金额：</label>
            <input v-model.number="ruleForm.maxAmount" type="number" min="0" step="0.01"
                   class="form-input" style="width: 150px;" />
          </div>
          <div class="form-row">
            <label>容差百分比：</label>
            <input v-model.number="ruleForm.tolerancePercent" type="number" min="0" max="100" step="0.1"
                   class="form-input" style="width: 100px;" />
            <span>%</span>
          </div>
        </div>

        <div v-if="ruleForm.ruleType === 'TIME_WINDOW'" class="rule-params">
          <h4>时间窗口归并参数</h4>
          <div class="form-row">
            <label>时间窗口(天)：</label>
            <input v-model.number="ruleForm.windowDays" type="number" min="1" max="365"
                   class="form-input" style="width: 100px;" />
          </div>
          <div class="form-row">
            <label>相同交易方：</label>
            <Checkbox v-model="ruleForm.sameCounterparty">要求相同交易方</Checkbox>
          </div>
        </div>

        <div class="form-row">
          <label>启用状态：</label>
          <Checkbox v-model="ruleForm.isActive">启用此规则</Checkbox>
        </div>
      </div>
    </Modal>

    <!-- 规则详情对话框 -->
    <Modal v-model="detailDialogVisible" title="规则详情" width="600px">
      <div v-if="selectedRule" class="rule-detail">
        <div class="detail-row">
          <label>规则名称：</label>
          <span>{{ selectedRule.ruleName }}</span>
        </div>
        <div class="detail-row">
          <label>规则描述：</label>
          <span>{{ selectedRule.ruleDescription }}</span>
        </div>
        <div class="detail-row">
          <label>适用类型：</label>
          <span class="rule-type" :class="selectedRule.applicableEntity.toLowerCase()">
            {{ getEntityTypeText(selectedRule.applicableEntity) }}
          </span>
        </div>
        <div class="detail-row">
          <label>状态：</label>
          <span class="status-badge" :class="selectedRule.isActive ? 'active' : 'inactive'">
            {{ selectedRule.isActive ? '启用' : '禁用' }}
          </span>
        </div>
        <div class="detail-row">
          <label>创建时间：</label>
          <span>{{ formatDate(selectedRule.createdAt) }}</span>
        </div>
        <div class="detail-row">
          <label>规则逻辑：</label>
          <pre class="rule-logic-display">{{ formatRuleLogic(selectedRule.ruleLogic) }}</pre>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'MergeRuleManagement',
  data() {
    return {
      mergeRules: [],
      rulesLoading: false,
      ruleDialogVisible: false,
      detailDialogVisible: false,
      isEditing: false,
      selectedRule: null,

      ruleForm: {
        ruleId: '',
        ruleName: '',
        ruleDescription: '',
        applicableEntity: 'DOCUMENT',
        ruleType: '',
        // 规则参数
        dateTolerance: 0,
        amountTolerance: 0,
        minAmount: 0,
        maxAmount: 10000,
        tolerancePercent: 5,
        windowDays: 7,
        sameCounterparty: true,
        isActive: true
      },

      entityTypeOptions: [
        { key: 'DOCUMENT', title: '票据' },
        { key: 'RECEIPT', title: '银证' },
        { key: 'BOTH', title: '通用' }
      ],

      ruleTypeOptions: [
        { key: 'SAME_PERSON_DATE_TYPE', title: '同人同日同类归并' },
        { key: 'AMOUNT_RANGE', title: '金额范围归并' },
        { key: 'TIME_WINDOW', title: '时间窗口归并' }
      ]
    }
  },
  
  computed: {
    dialogTitle() {
      return this.isEditing ? '编辑规则' : '创建规则'
    }
  },
  
  mounted() {
    this.loadMergeRules()
  },
  
  methods: {
    // 加载归并规则
    async loadMergeRules() {
      this.rulesLoading = true
      try {
        const response = await fetch('/api/merge-rules/list', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.mergeRules = result.data || []
          console.log('加载归并规则成功:', this.mergeRules.length)
        } else {
          this.$Message.error('加载归并规则失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('加载归并规则失败: ' + error.message)
        console.error('加载归并规则失败:', error)
      } finally {
        this.rulesLoading = false
      }
    },

    // 显示创建规则对话框
    showCreateRuleDialog() {
      this.isEditing = false
      this.resetRuleForm()
      this.ruleDialogVisible = true
    },

    // 编辑规则
    editRule(rule) {
      this.isEditing = true
      this.ruleForm = { ...rule }
      this.parseRuleLogic(rule.ruleLogic)
      this.ruleDialogVisible = true
    },

    // 查看规则详情
    viewRuleDetail(rule) {
      this.selectedRule = rule
      this.detailDialogVisible = true
    },

    // 切换规则状态
    async toggleRule(rule) {
      try {
        const response = await fetch(`/api/merge-rules/${rule.ruleId}/toggle-status`, {
          method: 'POST',
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success(`规则已${rule.isActive ? '禁用' : '启用'}`)
          rule.isActive = !rule.isActive
        } else {
          this.$Message.error('切换规则状态失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('切换规则状态失败: ' + error.message)
      }
    },
    
    // 删除规则
    async deleteRule(ruleId) {
      try {
        await this.$confirm('确定要删除这个归并规则吗？删除后无法恢复。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await fetch(`/api/merge-rules/${ruleId}/delete`, {
          method: 'DELETE',
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('删除规则成功')
          this.loadMergeRules()
        } else {
          this.$Message.error('删除规则失败: ' + result.msg)
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$Message.error('删除规则失败: ' + error.message)
        }
      }
    },

    // 保存规则
    async saveRule() {
      try {
        if (!this.ruleForm.ruleName.trim()) {
          this.$Message.error('请输入规则名称')
          return
        }
        if (!this.ruleForm.ruleDescription.trim()) {
          this.$Message.error('请输入规则描述')
          return
        }
        if (!this.ruleForm.ruleType) {
          this.$Message.error('请选择规则类型')
          return
        }

        const ruleData = {
          ruleName: this.ruleForm.ruleName,
          ruleDescription: this.ruleForm.ruleDescription,
          applicableEntity: this.ruleForm.applicableEntity,
          ruleLogic: this.buildRuleLogic(),
          isActive: this.ruleForm.isActive
        }

        const url = this.isEditing
          ? '/api/merge-rules/update'
          : '/api/merge-rules/create'

        if (this.isEditing) {
          ruleData.ruleId = this.ruleForm.ruleId
        }

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(ruleData)
        })

        const result = await response.json()
        if (result.success) {
          this.$Message.success(this.isEditing ? '更新规则成功' : '创建规则成功')
          this.ruleDialogVisible = false
          this.loadMergeRules()
        } else {
          this.$Message.error('保存规则失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('保存规则失败: ' + error.message)
      }
    },

    // 取消对话框
    cancelRuleDialog() {
      this.ruleDialogVisible = false
    },
    
    // 重置表单
    resetRuleForm() {
      this.ruleForm = {
        ruleId: '',
        ruleName: '',
        ruleDescription: '',
        applicableEntity: 'BOTH',
        ruleType: '',
        dateTolerance: 0,
        amountTolerance: 0,
        minAmount: 0,
        maxAmount: 10000,
        tolerancePercent: 5,
        windowDays: 7,
        sameCounterparty: true
      }
    },
    
    // 规则类型改变
    onRuleTypeChange() {
      // 可以在这里设置不同规则类型的默认参数
    },
    
    // 构建规则逻辑JSON
    buildRuleLogic() {
      const logic = {
        type: this.ruleForm.ruleType
      }
      
      switch (this.ruleForm.ruleType) {
        case 'SAME_PERSON_DATE_TYPE':
          logic.fields = ['submitter', 'date', 'type']
          logic.conditions = {
            date_tolerance: this.ruleForm.dateTolerance,
            amount_tolerance: this.ruleForm.amountTolerance
          }
          break
        case 'AMOUNT_RANGE':
          logic.conditions = {
            min_amount: this.ruleForm.minAmount,
            max_amount: this.ruleForm.maxAmount,
            tolerance_percent: this.ruleForm.tolerancePercent
          }
          break
        case 'TIME_WINDOW':
          logic.conditions = {
            window_days: this.ruleForm.windowDays,
            same_counterparty: this.ruleForm.sameCounterparty
          }
          break
      }
      
      return JSON.stringify(logic)
    },
    
    // 解析规则逻辑
    parseRuleLogic(ruleLogicStr) {
      try {
        const logic = JSON.parse(ruleLogicStr)
        this.ruleForm.ruleType = logic.type
        
        if (logic.conditions) {
          const conditions = logic.conditions
          this.ruleForm.dateTolerance = conditions.date_tolerance || 0
          this.ruleForm.amountTolerance = conditions.amount_tolerance || 0
          this.ruleForm.minAmount = conditions.min_amount || 0
          this.ruleForm.maxAmount = conditions.max_amount || 10000
          this.ruleForm.tolerancePercent = conditions.tolerance_percent || 5
          this.ruleForm.windowDays = conditions.window_days || 7
          this.ruleForm.sameCounterparty = conditions.same_counterparty !== false
        }
      } catch (error) {
        console.error('解析规则逻辑失败:', error)
      }
    },
    
    // 格式化规则逻辑显示
    formatRuleLogic(ruleLogicStr) {
      try {
        const logic = JSON.parse(ruleLogicStr)
        return JSON.stringify(logic, null, 2)
      } catch (error) {
        return ruleLogicStr
      }
    },
    
    // 获取实体类型标签样式
    getEntityTypeTag(type) {
      const tagMap = {
        'DOCUMENT': 'primary',
        'RECEIPT': 'success',
        'BOTH': 'warning'
      }
      return tagMap[type] || 'info'
    },
    
    // 获取实体类型文本
    getEntityTypeText(type) {
      const textMap = {
        'DOCUMENT': '票据',
        'RECEIPT': '银证',
        'BOTH': '两者'
      }
      return textMap[type] || type
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return date.toLocaleDateString()
    }
  }
}
</script>

<style lang="less" scoped>
.merge-rule-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  margin-bottom: 20px;
}

.toolbar Button {
  margin-right: 10px;
}

.rule-type {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.rule-type.document {
  background-color: #409eff;
}

.rule-type.receipt {
  background-color: #67c23a;
}

.rule-type.both {
  background-color: #e6a23c;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.status-badge.active {
  background-color: #67c23a;
}

.status-badge.inactive {
  background-color: #f56c6c;
}

.rule-form {
  padding: 20px 0;
}

.form-row {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
}

.form-row label {
  width: 120px;
  text-align: right;
  margin-right: 15px;
  line-height: 32px;
  font-weight: bold;
}

.form-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-textarea {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 400px;
  resize: vertical;
}

.rule-params {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.rule-params h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.rule-detail {
  padding: 20px 0;
}

.detail-row {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.detail-row label {
  width: 100px;
  text-align: right;
  margin-right: 15px;
  font-weight: bold;
  color: #606266;
}

.rule-logic-display {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
