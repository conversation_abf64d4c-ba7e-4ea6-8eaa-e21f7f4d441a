<template>
  <div class="mobile-bill-detail">
    <!-- 顶部导航栏 -->
    <div class="navbar">
      <div class="nav-left">
        <button class="back-btn" @click="goBack">
          <i class="icon-back"></i>
        </button>
        <h1>票据详情</h1>
      </div>
      <div class="nav-right">
        <button class="edit-btn" @click="editBill">
          <i class="icon-edit"></i>
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>

    <div v-else-if="bill" class="detail-content">
      <!-- 票据图片 -->
      <div v-if="bill.attachmentPath" class="image-section">
        <div class="section-title">
          <i class="icon-image"></i>
          <span>票据图片</span>
        </div>
        <div class="image-container">
          <img :src="bill.attachmentPath" alt="票据图片" @click="previewImage" />
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="info-section">
        <div class="section-title">
          <i class="icon-info"></i>
          <span>基本信息</span>
        </div>
        
        <div class="info-grid">
          <div class="info-item">
            <span class="label">票据编号</span>
            <span class="value">{{ bill.billNo }}</span>
          </div>
          
          <div class="info-item">
            <span class="label">摘要</span>
            <span class="value">{{ bill.summary || '无摘要' }}</span>
          </div>
          
          <div class="info-item">
            <span class="label">金额</span>
            <span class="value amount">¥{{ formatAmount(bill.amount) }}</span>
          </div>
          
          <div class="info-item">
            <span class="label">票据类型</span>
            <span class="value">{{ bill.billType || '普通票据' }}</span>
          </div>
          
          <div class="info-item">
            <span class="label">开票日期</span>
            <span class="value">{{ formatDate(bill.billDate) }}</span>
          </div>
          
          <div class="info-item">
            <span class="label">状态</span>
            <span class="value status" :class="getStatusClass(bill.status)">
              {{ bill.status || '未使用' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 开票信息 -->
      <div v-if="bill.issuer || bill.taxNumber" class="info-section">
        <div class="section-title">
          <i class="icon-company"></i>
          <span>开票信息</span>
        </div>
        
        <div class="info-grid">
          <div v-if="bill.issuer" class="info-item">
            <span class="label">开票单位</span>
            <span class="value">{{ bill.issuer }}</span>
          </div>
          
          <div v-if="bill.taxNumber" class="info-item">
            <span class="label">税号</span>
            <span class="value">{{ bill.taxNumber }}</span>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div v-if="bill.remark" class="info-section">
        <div class="section-title">
          <i class="icon-note"></i>
          <span>备注信息</span>
        </div>
        
        <div class="remark-content">
          {{ bill.remark }}
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <button class="action-btn primary" @click="editBill">
          <i class="icon-edit"></i>
          编辑票据
        </button>
        
        <button 
          class="action-btn danger" 
          @click="deleteBill"
          v-if="bill.status === '未使用'"
        >
          <i class="icon-delete"></i>
          删除票据
        </button>
      </div>
    </div>

    <div v-else class="error-state">
      <div class="error-icon">❌</div>
      <p>票据不存在或已被删除</p>
      <button class="back-btn-secondary" @click="goBack">
        返回列表
      </button>
    </div>

    <!-- 图片预览模态框 -->
    <div v-if="showImagePreview" class="image-preview-modal" @click="closeImagePreview">
      <div class="preview-content">
        <img :src="bill.attachmentPath" alt="票据图片" />
        <button class="close-btn" @click="closeImagePreview">
          <i class="icon-close"></i>
        </button>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="message" class="message" :class="messageType">
      {{ message }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileBillDetail',
  data() {
    return {
      bill: null,
      loading: true,
      showImagePreview: false,
      message: '',
      messageType: 'info'
    }
  },
  mounted() {
    this.loadBillDetail()
  },
  methods: {
    async loadBillDetail() {
      try {
        this.loading = true
        const billId = this.$route.params.id
        
        const response = await this.$api.bill.get(billId)
        
        if (response.success) {
          this.bill = response.data
        } else {
          this.showMessage('加载失败: ' + (response.message || '未知错误'), 'error')
        }
      } catch (error) {
        console.error('加载票据详情失败:', error)
        this.showMessage('网络错误，请检查连接', 'error')
      } finally {
        this.loading = false
      }
    },

    goBack() {
      this.$router.go(-1)
    },

    editBill() {
      this.$router.push(`/mobile/bill/edit/${this.bill.id}`)
    },

    async deleteBill() {
      if (!confirm('确定要删除这张票据吗？删除后无法恢复。')) {
        return
      }

      try {
        const response = await this.$api.bill.delete(this.bill.id)
        
        if (response.success) {
          this.showMessage('票据删除成功', 'success')
          setTimeout(() => {
            this.$router.push('/mobile/bills')
          }, 1000)
        } else {
          this.showMessage('删除失败: ' + (response.message || '未知错误'), 'error')
        }
      } catch (error) {
        console.error('删除票据失败:', error)
        this.showMessage('删除失败，请检查网络连接', 'error')
      }
    },

    previewImage() {
      this.showImagePreview = true
    },

    closeImagePreview() {
      this.showImagePreview = false
    },

    formatAmount(amount) {
      if (!amount) return '0.00'
      return parseFloat(amount).toFixed(2)
    },

    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },

    getStatusClass(status) {
      const statusMap = {
        '未使用': 'status-unused',
        '已使用': 'status-used',
        '已作废': 'status-invalid'
      }
      return statusMap[status] || 'status-unused'
    },

    showMessage(text, type = 'info') {
      this.message = text
      this.messageType = type
      setTimeout(() => {
        this.clearMessage()
      }, 3000)
    },

    clearMessage() {
      this.message = ''
      this.messageType = 'info'
    }
  }
}
</script>

<style scoped>
.mobile-bill-detail {
  min-height: 100vh;
  background: #f5f5f5;
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.back-btn, .edit-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.back-btn:hover, .edit-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.nav-left h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.detail-content {
  padding: 20px;
}

.loading-state, .error-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.back-btn-secondary {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 20px;
}

.info-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.image-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-container {
  text-align: center;
}

.image-container img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.info-grid {
  display: grid;
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
  margin-right: 15px;
}

.value {
  color: #333;
  font-weight: 600;
  text-align: right;
  word-break: break-all;
}

.value.amount {
  color: #f44336;
  font-size: 18px;
}

.status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.status-unused {
  background: #e3f2fd;
  color: #1976d2;
}

.status-used {
  background: #e8f5e8;
  color: #388e3c;
}

.status-invalid {
  background: #ffebee;
  color: #d32f2f;
}

.remark-content {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  color: #666;
  line-height: 1.5;
}

.action-section {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.action-btn {
  flex: 1;
  padding: 16px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.danger {
  background: #f44336;
  color: white;
}

.action-btn:active {
  transform: scale(0.98);
}

.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.preview-content {
  position: relative;
  max-width: 100%;
  max-height: 100%;
}

.preview-content img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
}

.close-btn {
  position: absolute;
  top: -40px;
  right: 0;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 18px;
}

.message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 1000;
  animation: slideDown 0.3s ease;
}

.message.success {
  background: #4caf50;
}

.message.error {
  background: #f44336;
}

.message.info {
  background: #2196f3;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图标字体 */
.icon-back::before { content: '←'; }
.icon-edit::before { content: '✏️'; }
.icon-image::before { content: '🖼️'; }
.icon-info::before { content: 'ℹ️'; }
.icon-company::before { content: '🏢'; }
.icon-note::before { content: '📝'; }
.icon-delete::before { content: '🗑️'; }
.icon-close::before { content: '✖️'; }

@media (max-width: 480px) {
  .detail-content {
    padding: 15px;
  }
  
  .navbar {
    padding: 12px 15px;
  }
  
  .nav-left h1 {
    font-size: 18px;
  }
  
  .action-section {
    flex-direction: column;
  }
}
</style>
