<template>
  <div class="mobile-index">
    <div class="loading-container">
      <div class="logo">
        <img src="@/assets/logo.png" alt="财税智能体" />
        <h1>财税智能体</h1>
        <p>手机端</p>
      </div>
      
      <div class="loading-spinner"></div>
      <p class="loading-text">正在加载...</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileIndex',
  mounted() {
    this.checkAuthAndRedirect()
  },
  methods: {
    async checkAuthAndRedirect() {
      console.log('MobileIndex: 开始检查登录状态')

      try {
        // 检查用户登录状态
        const response = await this.$api.common.init()
        console.log('MobileIndex: 登录状态检查结果', response)

        if (response.success && response.data) {
          // 已登录，直接跳转到票据列表（手机端核心功能）
          console.log('MobileIndex: 用户已登录，跳转到票据列表')
          this.$router.replace('/mobile/bills')
        } else {
          // 未登录，跳转到登录页
          console.log('MobileIndex: 用户未登录，跳转到登录页')
          this.$router.replace('/mobile/login')
        }
      } catch (error) {
        console.error('MobileIndex: 检查登录状态失败:', error)
        // 出错时跳转到登录页
        console.log('MobileIndex: 出错，跳转到登录页')
        this.$router.replace('/mobile/login')
      }
    }
  }
}
</script>

<style scoped>
.mobile-index {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading-container {
  text-align: center;
  color: white;
}

.logo {
  margin-bottom: 40px;
}

.logo img {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.logo h1 {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo p {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-text {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
