<template>
  <div class="mobile-login">
    <div class="login-header">
      <div class="logo">
        <img src="@/assets/logo.png" alt="财税智能体" />
        <h1>财税智能体</h1>
      </div>
    </div>

    <div class="login-form">
      <div class="form-title">
        <h2>登录账号</h2>
        <p>欢迎回来</p>
      </div>

      <form @submit.prevent="handleSubmit">
        <div class="input-group">
          <div class="input-wrapper">
            <i class="icon-phone"></i>
            <input
              type="tel"
              v-model="form.mobile"
              placeholder="请输入手机号"
              maxlength="11"
              required
            />
          </div>
        </div>

        <div class="input-group">
          <div class="input-wrapper">
            <i class="icon-lock"></i>
            <input
              :type="showPassword ? 'text' : 'password'"
              v-model="form.password"
              placeholder="请输入密码"
              required
            />
            <i 
              class="icon-eye" 
              :class="{ 'icon-eye-slash': !showPassword }"
              @click="togglePassword"
            ></i>
          </div>
        </div>



        <button
          type="submit"
          class="login-btn"
          :disabled="loading"
          :class="{ loading: loading }"
        >
          <span v-if="!loading">登录</span>
          <span v-else>
            <i class="icon-spinner"></i>
            登录中...
          </span>
        </button>
      </form>


    </div>

    <!-- 消息提示 -->
    <div v-if="message" class="message" :class="messageType">
      {{ message }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileLogin',
  data() {
    return {
      showPassword: false,
      loading: false,
      message: '',
      messageType: 'info',
      form: {
        mobile: '',
        password: ''
      }
    }
  },
  methods: {
    togglePassword() {
      this.showPassword = !this.showPassword
    },

    validateForm() {
      if (!this.form.mobile) {
        this.showMessage('请输入手机号', 'error')
        return false
      }

      if (!/^1[3-9]\d{9}$/.test(this.form.mobile)) {
        this.showMessage('请输入正确的手机号格式', 'error')
        return false
      }

      if (!this.form.password) {
        this.showMessage('请输入密码', 'error')
        return false
      }

      if (this.form.password.length < 6) {
        this.showMessage('密码长度不能少于6位', 'error')
        return false
      }

      return true
    },

    async handleSubmit() {
      if (!this.validateForm()) {
        return
      }

      this.loading = true
      this.clearMessage()

      try {
        await this.login()
      } catch (error) {
        console.error('登录失败:', error)
      } finally {
        this.loading = false
      }
    },

    async login() {
      try {
        console.log('MobileLogin: 开始登录请求')
        const response = await this.$api.common.login({
          mobile: this.form.mobile,
          password: this.form.password
        })
        
        console.log('MobileLogin: 登录响应', response)

        if (response.success) {
          this.showMessage('登录成功', 'success')
          
          // 获取用户信息
          try {
            console.log('MobileLogin: 获取用户信息')
            const initResponse = await this.$api.common.init()
            console.log('MobileLogin: 用户信息响应', initResponse)
            
            if (initResponse.success && initResponse.data) {
              // 保存用户信息到sessionStorage
              sessionStorage.setItem('user', JSON.stringify(initResponse.data))
              console.log('MobileLogin: 用户信息已保存，准备跳转')
              
              // 延时跳转，确保数据保存完成
              setTimeout(() => {
                console.log('MobileLogin: 执行跳转到 /mobile/bills')
                this.$router.replace('/mobile/bills')
              }, 100)
            } else {
              console.error('MobileLogin: 获取用户信息失败，但仍尝试跳转')
              setTimeout(() => {
                this.$router.replace('/mobile/bills')
              }, 100)
            }
          } catch (error) {
            console.error('MobileLogin: 获取用户信息异常:', error)
            // 即使获取用户信息失败，也尝试跳转
            setTimeout(() => {
              this.$router.replace('/mobile/bills')
            }, 100)
          }
        } else {
          this.showMessage(response.message || '登录失败', 'error')
        }
      } catch (error) {
        console.error('MobileLogin: 登录请求失败:', error)
        this.showMessage('登录失败，请检查网络连接', 'error')
      }
    },

    showMessage(text, type = 'info') {
      this.message = text
      this.messageType = type
      setTimeout(() => {
        this.clearMessage()
      }, 3000)
    },

    clearMessage() {
      this.message = ''
      this.messageType = 'info'
    }
  }
}
</script>

<style scoped>
.mobile-login {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}

.login-header {
  text-align: center;
  margin-top: 60px;
  margin-bottom: 40px;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.logo img {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.logo h1 {
  color: white;
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.login-form {
  background: white;
  border-radius: 20px;
  padding: 30px 25px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.form-title {
  text-align: center;
  margin-bottom: 30px;
}

.form-title h2 {
  color: #333;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.form-title p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.input-group {
  margin-bottom: 20px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-wrapper i {
  padding: 0 15px;
  color: #999;
  font-size: 18px;
}

.input-wrapper input {
  flex: 1;
  padding: 16px 0;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #333;
  outline: none;
}

.input-wrapper input::placeholder {
  color: #999;
}

.icon-eye {
  cursor: pointer;
  transition: color 0.3s ease;
}

.icon-eye:hover {
  color: #667eea;
}

.login-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.login-btn.loading {
  background: #ccc;
}

.form-footer {
  text-align: center;
  margin-top: 25px;
}

.form-footer p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.form-footer a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.form-footer a:hover {
  text-decoration: underline;
}

.message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 1000;
  animation: slideDown 0.3s ease;
}

.message.success {
  background: #4caf50;
}

.message.error {
  background: #f44336;
}

.message.info {
  background: #2196f3;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 图标字体 */
.icon-phone::before { content: '📱'; }
.icon-lock::before { content: '🔒'; }
.icon-eye::before { content: '👁'; }
.icon-eye-slash::before { content: '🙈'; }
.icon-spinner::before { content: '⏳'; }

@media (max-width: 480px) {
  .mobile-login {
    padding: 15px;
  }
  
  .login-header {
    margin-top: 40px;
    margin-bottom: 30px;
  }
  
  .logo h1 {
    font-size: 24px;
  }
  
  .login-form {
    padding: 25px 20px;
  }
}
</style>
