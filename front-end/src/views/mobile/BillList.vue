<template>
  <div class="mobile-bill-list">
    <!-- 顶部导航栏 -->
    <div class="navbar">
      <div class="nav-center">
        <div class="logo">
          <img src="https://www.aiform.com/logo-Photoroom.png" alt="财税智能体" />
        </div>
        <h1>我的票据</h1>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <div class="search-input">
        <i class="icon-search"></i>
        <input 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索票据编号、摘要..."
          @input="handleSearch"
        />
        <i v-if="searchKeyword" class="icon-clear" @click="clearSearch"></i>
      </div>
    </div>

    <!-- 简化筛选栏 -->
    <div class="filter-bar">
      <div class="filter-tabs">
        <button
          v-for="status in statusOptions"
          :key="status.value"
          class="filter-tab"
          :class="{ active: selectedStatus === status.value }"
          @click="selectStatus(status.value)"
        >
          {{ status.label }}
        </button>
      </div>
    </div>

    <!-- 票据列表 -->
    <div class="bill-list" v-if="!loading">
      <div v-if="filteredBills.length === 0" class="empty-state">
        <div class="empty-icon">📄</div>
        <p>暂无票据数据</p>
        <button class="add-btn-secondary" @click="goToAdd">
          添加第一张票据
        </button>
      </div>
      
      <div v-else>
        <div
          v-for="bill in filteredBills"
          :key="bill.id"
          class="bill-item"
        >
          <div class="bill-header">
            <div class="bill-info">
              <h3 class="bill-title">{{ bill.summary || '无摘要' }}</h3>
              <p class="bill-no">{{ bill.billNo }}</p>
            </div>
            <div class="bill-amount">
              <span class="amount">¥{{ formatAmount(bill.amount) }}</span>
              <span class="status" :class="getStatusClass(bill.status)">
                {{ bill.status || '未使用' }}
              </span>
            </div>
          </div>
          
          <div class="bill-details">
            <div class="detail-item">
              <span class="label">日期:</span>
              <span class="value">{{ formatDate(bill.billDate) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">类型:</span>
              <span class="value">{{ bill.billType || '普通票据' }}</span>
            </div>
          </div>

          <div v-if="bill.attachmentPath" class="bill-attachment">
            <i class="icon-attachment"></i>
            <span>有附件</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>

    <!-- 浮动添加按钮 -->
    <button class="fab" @click="goToAdd">
      <i class="icon-plus"></i>
    </button>

    <!-- 下拉刷新提示 -->
    <div v-if="refreshing" class="refresh-indicator">
      <div class="refresh-spinner"></div>
      <span>刷新中...</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileBillList',
  data() {
    return {
      bills: [],
      loading: true,
      refreshing: false,
      searchKeyword: '',
      selectedStatus: 'all',
      statusOptions: [
        { label: '全部', value: 'all' },
        { label: '未使用', value: '未使用' },
        { label: '已使用', value: '已使用' },
        { label: '已作废', value: '已作废' }
      ]
    }
  },
  computed: {
    filteredBills() {
      let filtered = this.bills

      // 状态筛选
      if (this.selectedStatus !== 'all') {
        filtered = filtered.filter(bill => 
          (bill.status || '未使用') === this.selectedStatus
        )
      }

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(bill => 
          (bill.billNo && bill.billNo.toLowerCase().includes(keyword)) ||
          (bill.summary && bill.summary.toLowerCase().includes(keyword)) ||
          (bill.billType && bill.billType.toLowerCase().includes(keyword))
        )
      }

      return filtered.sort((a, b) => new Date(b.billDate) - new Date(a.billDate))
    }
  },
  mounted() {
    this.loadBills()
    this.setupPullToRefresh()
  },
  methods: {
    async loadBills() {
      try {
        this.loading = true
        const response = await this.$api.bill.list({
          page: 1,
          size: 100 // 手机端一次加载更多数据
        })

        if (response.success) {
          this.bills = response.data.records || []
        } else {
          this.showMessage('加载失败: ' + (response.message || '未知错误'), 'error')
        }
      } catch (error) {
        console.error('加载票据列表失败:', error)
        this.showMessage('网络错误，请检查连接', 'error')
      } finally {
        this.loading = false
      }
    },

    async refreshBills() {
      this.refreshing = true
      await this.loadBills()
      this.refreshing = false
    },

    setupPullToRefresh() {
      let startY = 0
      let currentY = 0
      let pulling = false

      const container = this.$el

      container.addEventListener('touchstart', (e) => {
        if (container.scrollTop === 0) {
          startY = e.touches[0].clientY
          pulling = true
        }
      })

      container.addEventListener('touchmove', (e) => {
        if (!pulling) return
        
        currentY = e.touches[0].clientY
        const diff = currentY - startY

        if (diff > 60 && !this.refreshing) {
          this.refreshBills()
          pulling = false
        }
      })

      container.addEventListener('touchend', () => {
        pulling = false
      })
    },

    handleSearch() {
      // 搜索是响应式的，通过computed属性自动处理
    },

    clearSearch() {
      this.searchKeyword = ''
    },

    selectStatus(status) {
      this.selectedStatus = status
    },

    goToAdd() {
      this.$router.push('/mobile/bill/add')
    },

    formatAmount(amount) {
      if (!amount) return '0.00'
      return parseFloat(amount).toFixed(2)
    },

    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },

    getStatusClass(status) {
      const statusMap = {
        '未使用': 'status-unused',
        '已使用': 'status-used',
        '已作废': 'status-invalid'
      }
      return statusMap[status] || 'status-unused'
    },

    showMessage(text, type = 'info') {
      // 简单的消息提示实现
      const message = document.createElement('div')
      message.className = `mobile-message ${type}`
      message.textContent = text
      document.body.appendChild(message)
      
      setTimeout(() => {
        document.body.removeChild(message)
      }, 3000)
    }
  }
}
</script>

<style scoped>
.mobile-bill-list {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px; /* 为FAB留出空间 */
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-center {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-center h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}



.search-bar {
  padding: 15px 20px;
  background: white;
  border-bottom: 1px solid #eee;
}

.search-input {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 0 15px;
}

.search-input i {
  color: #999;
  margin-right: 10px;
}

.search-input input {
  flex: 1;
  padding: 12px 0;
  border: none;
  background: transparent;
  font-size: 16px;
  outline: none;
}

.icon-clear {
  cursor: pointer;
  margin-left: 10px;
  margin-right: 0;
}

.filter-bar {
  background: white;
  padding: 10px 20px;
  border-bottom: 1px solid #eee;
}

.filter-tabs {
  display: flex;
  gap: 10px;
}

.filter-tab {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.bill-list {
  padding: 0 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.add-btn-secondary {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 20px;
}

.bill-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin: 10px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bill-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.bill-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.bill-no {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.bill-amount {
  text-align: right;
}

.amount {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.status-unused {
  background: #e3f2fd;
  color: #1976d2;
}

.status-used {
  background: #e8f5e8;
  color: #388e3c;
}

.status-invalid {
  background: #ffebee;
  color: #d32f2f;
}

.bill-details {
  display: flex;
  gap: 20px;
  margin-bottom: 8px;
}

.detail-item {
  font-size: 14px;
}

.label {
  color: #666;
  margin-right: 4px;
}

.value {
  color: #333;
}

.bill-attachment {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #667eea;
  margin-top: 8px;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  z-index: 1000;
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5);
}

.refresh-indicator {
  position: fixed;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 10px 20px;
  border-radius: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000;
}

.refresh-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图标字体 */
.icon-search::before { content: '🔍'; }
.icon-clear::before { content: '✖️'; }
.icon-plus::before { content: '+'; }
.icon-attachment::before { content: '📎'; }

/* 消息提示样式 */
.mobile-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 1000;
  animation: slideDown 0.3s ease;
}

.mobile-message.success {
  background: #4caf50;
}

.mobile-message.error {
  background: #f44336;
}

.mobile-message.info {
  background: #2196f3;
}

@media (max-width: 480px) {
  .navbar {
    padding: 12px 15px;
  }

  .nav-center h1 {
    font-size: 18px;
  }

  .logo img {
    width: 28px;
    height: 28px;
  }

  .search-bar, .filter-bar, .bill-list {
    padding-left: 15px;
    padding-right: 15px;
  }

  .fab {
    bottom: 20px;
    right: 20px;
  }
}
</style>
