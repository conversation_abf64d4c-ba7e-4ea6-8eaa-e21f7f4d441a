# 财税智能体 - 手机端

## 功能概述

手机端提供了简化的票据管理功能，专为移动设备优化，只包含核心的票据录入和查看功能。

## 核心功能

### 1. 用户认证
- **登录页面** (`/mobile/login`)
  - 手机号 + 密码登录
  - 注册新账号
  - 自动记住登录状态
  - 响应式表单验证

### 2. 票据列表 (`/mobile/bills`)
- **票据展示**
  - 卡片式列表展示
  - 显示票据摘要、金额、状态
  - 支持下拉刷新
  - 实时搜索和状态筛选

- **快速操作**
  - 顶部新增按钮
  - 浮动新增按钮
  - 简洁的界面设计

### 3. 新增票据 (`/mobile/bill/add`)
- **图片上传**
  - 支持拍照或从相册选择
  - 自动压缩和优化
  - 实时预览

- **OCR识别**
  - 自动识别发票信息
  - 智能填充表单字段
  - 支持各类发票格式

- **简化表单**
  - 票据编号（自动生成）
  - 摘要（必填）
  - 金额（必填）
  - 开票日期
  - 备注信息

## 技术特性

### 移动端优化
- **响应式设计**
  - 适配各种屏幕尺寸
  - 支持横屏和竖屏
  - 安全区域适配（iPhone X系列）

- **触摸体验**
  - 触摸反馈动画
  - 防止误触
  - 手势支持

- **性能优化**
  - 图片懒加载
  - 组件按需加载
  - 缓存策略

### 用户体验
- **加载状态**
  - 骨架屏加载
  - 进度指示器
  - 错误状态处理

- **交互反馈**
  - 消息提示
  - 确认对话框
  - 操作结果反馈

- **离线支持**
  - 本地数据缓存
  - 网络状态检测
  - 离线提示

## 页面结构

```
/mobile
├── /                    # 入口页面（自动重定向到票据列表）
├── /login              # 登录页面
├── /bills              # 票据列表（主页面）
└── /bill/add           # 新增票据
```

## 组件架构

### 公共组件
- **MobileNavbar** - 移动端导航栏
- **MobileCard** - 卡片容器
- **MobileButton** - 按钮组件
- **MobileInput** - 输入框组件
- **MobileMessage** - 消息提示

### 页面组件
- **MobileIndex** - 入口页面（重定向）
- **MobileLogin** - 登录页面
- **BillList** - 票据列表（主页面）
- **BillAdd** - 新增票据

## 样式系统

### CSS架构
- **mobile.less** - 移动端专用样式
- **响应式断点** - 适配不同设备
- **主题变量** - 统一色彩和字体
- **动画效果** - 流畅的交互动画

### 设计规范
- **色彩系统** - 主色调 #667eea
- **字体大小** - 16px基准，适配移动端
- **间距系统** - 8px基准网格
- **圆角规范** - 12px标准圆角

## API集成

### 认证接口
- `POST /api/login` - 用户登录
- `POST /api/register` - 用户注册
- `POST /api/logout` - 退出登录
- `GET /api/init` - 初始化用户信息

### 票据接口
- `GET /api/bill/list` - 获取票据列表
- `POST /api/bill/save` - 保存票据
- `GET /api/bill/{id}` - 获取票据详情
- `PUT /api/bill/{id}` - 更新票据
- `DELETE /api/bill/{id}` - 删除票据

### 文件接口
- `POST /api/upload/file` - 上传文件
- `POST /api/upload/recognize-vat-invoice` - OCR识别

## 浏览器兼容性

### 支持的浏览器
- **iOS Safari** 12+
- **Android Chrome** 70+
- **微信浏览器** 7.0+
- **支付宝浏览器** 10.0+

### 功能降级
- **不支持OCR** - 手动输入
- **不支持拍照** - 文件选择
- **不支持触摸** - 鼠标操作

## 部署说明

### 开发环境
```bash
# 启动前端服务
cd front-end
yarn serve

# 访问手机端
http://localhost:8081/mobile
```

### 生产环境
```bash
# 构建生产版本
yarn build

# 部署到服务器
# 配置nginx反向代理
# 启用HTTPS（必需，相机功能要求）
```

## 安全考虑

### 数据安全
- **HTTPS传输** - 所有API调用加密
- **Token认证** - 安全的用户认证
- **文件上传** - 类型和大小限制
- **输入验证** - 前后端双重验证

### 隐私保护
- **图片处理** - 本地压缩，减少传输
- **敏感信息** - 不在本地存储
- **权限控制** - 最小权限原则

## 性能指标

### 加载性能
- **首屏加载** < 2秒
- **页面切换** < 500ms
- **图片加载** < 1秒
- **OCR识别** < 5秒

### 用户体验
- **操作响应** < 100ms
- **网络超时** 30秒
- **错误恢复** 自动重试
- **离线缓存** 7天

## 使用说明

### 访问地址
- **手机端入口：** http://localhost:8081/mobile
- **登录页面：** http://localhost:8081/mobile/login
- **票据列表：** http://localhost:8081/mobile/bills
- **新增票据：** http://localhost:8081/mobile/bill/add

### 使用流程
1. **访问入口** → 打开 `/mobile` 自动检测登录状态
2. **用户登录** → 手机号+密码登录或注册新账号
3. **查看票据** → 浏览票据列表，支持搜索筛选
4. **新增票据** → 点击新增按钮，拍照上传，OCR自动识别，填写信息
5. **保存票据** → 验证信息后保存到系统

### 设计理念
手机端专注于票据的录入和查看，去除复杂的管理功能，提供简洁高效的移动体验。

## 问题反馈

如有问题或建议，请联系开发团队。
