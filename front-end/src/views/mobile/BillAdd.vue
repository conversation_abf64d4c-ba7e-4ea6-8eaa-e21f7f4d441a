<template>
  <div class="mobile-bill-add">
    <!-- 顶部导航栏 -->
    <div class="navbar">
      <div class="nav-left">
        <button class="back-btn" @click="goBack">
          <i class="icon-back"></i>
        </button>
      </div>
      <div class="nav-center">
        <div class="logo">
          <img src="@/assets/logo.png" alt="财税智能体" />
        </div>
        <h1>新增票据</h1>
      </div>
      <div class="nav-right">
        <button
          class="save-btn"
          @click="saveBill"
          :disabled="saving || !canSave"
          :class="{ saving: saving }"
        >
          {{ saving ? '保存中...' : '保存' }}
        </button>
      </div>
    </div>

    <div class="form-container">
      <!-- 图片上传区域 -->
      <div class="upload-section">
        <div class="section-title">
          <i class="icon-camera"></i>
          <span>票据图片</span>
        </div>
        
        <div class="upload-area" @click="triggerFileInput">
          <div v-if="!form.attachmentPath" class="upload-placeholder">
            <div class="upload-icon">📷</div>
            <p>点击拍照或选择图片</p>
            <p class="upload-tip">支持OCR自动识别</p>
          </div>
          
          <div v-else class="uploaded-image">
            <img :src="form.attachmentPath" alt="票据图片" />
            <div class="image-actions">
              <button class="action-btn" @click.stop="recognizeImage" :disabled="recognizing">
                <i class="icon-scan"></i>
                {{ recognizing ? '识别中...' : 'OCR识别' }}
              </button>
              <button class="action-btn delete" @click.stop="removeImage">
                <i class="icon-delete"></i>
                删除
              </button>
            </div>
          </div>
        </div>
        
        <input 
          ref="fileInput"
          type="file" 
          accept="image/*" 
          capture="environment"
          style="display: none"
          @change="handleFileSelect"
        />
      </div>

      <!-- 表单区域 -->
      <div class="form-section">
        <div class="form-group">
          <label class="form-label">票据编号</label>
          <input 
            type="text" 
            v-model="form.billNo" 
            placeholder="系统自动生成"
            readonly
            class="form-input readonly"
          />
        </div>

        <div class="form-group">
          <label class="form-label required">摘要</label>
          <input 
            type="text" 
            v-model="form.summary" 
            placeholder="请输入票据摘要"
            class="form-input"
            maxlength="100"
          />
        </div>

        <div class="form-group">
          <label class="form-label required">金额</label>
          <div class="amount-input">
            <span class="currency">¥</span>
            <input 
              type="number" 
              v-model="form.amount" 
              placeholder="0.00"
              step="0.01"
              min="0"
              class="form-input"
            />
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">开票日期</label>
          <input
            type="date"
            v-model="form.billDate"
            class="form-input"
          />
        </div>

        <div class="form-group">
          <label class="form-label">备注</label>
          <textarea 
            v-model="form.remark" 
            placeholder="请输入备注信息"
            class="form-textarea"
            rows="3"
            maxlength="200"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="uploading || recognizing" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p>{{ uploading ? '上传中...' : '识别中...' }}</p>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="message" class="message" :class="messageType">
      {{ message }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileBillAdd',
  data() {
    return {
      form: {
        billNo: '',
        summary: '',
        amount: '',
        billDate: new Date().toISOString().split('T')[0],
        remark: '',
        attachmentPath: ''
      },
      uploading: false,
      recognizing: false,
      saving: false,
      message: '',
      messageType: 'info'
    }
  },
  computed: {
    canSave() {
      return this.form.summary && this.form.amount && parseFloat(this.form.amount) > 0
    }
  },
  mounted() {
    this.generateBillNo()
  },
  methods: {
    goBack() {
      if (this.hasChanges()) {
        if (confirm('有未保存的更改，确定要离开吗？')) {
          this.$router.go(-1)
        }
      } else {
        this.$router.go(-1)
      }
    },

    hasChanges() {
      return this.form.summary || this.form.amount || this.form.attachmentPath || this.form.remark
    },

    async generateBillNo() {
      try {
        const response = await this.$api.bill.loadBillNo({
          billDate: this.form.billDate
        })
        if (response.success) {
          this.form.billNo = response.data
        }
      } catch (error) {
        console.error('生成票据编号失败:', error)
      }
    },

    triggerFileInput() {
      this.$refs.fileInput.click()
    },

    async handleFileSelect(event) {
      const file = event.target.files[0]
      if (!file) return

      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        this.showMessage('请选择图片文件', 'error')
        return
      }

      // 验证文件大小 (10MB)
      if (file.size > 10 * 1024 * 1024) {
        this.showMessage('图片大小不能超过10MB', 'error')
        return
      }

      await this.uploadFile(file)
    },

    async uploadFile(file) {
      try {
        this.uploading = true
        
        const formData = new FormData()
        formData.append('file', file)
        formData.append('folder', 'bills')

        const response = await this.$api.upload.file(formData, 'bills')
        
        if (response.success) {
          this.form.attachmentPath = response.data.filePath
          this.showMessage('图片上传成功', 'success')
          
          // 自动触发OCR识别
          setTimeout(() => {
            this.recognizeImage()
          }, 500)
        } else {
          this.showMessage('上传失败: ' + (response.message || '未知错误'), 'error')
        }
      } catch (error) {
        console.error('上传失败:', error)
        this.showMessage('上传失败，请检查网络连接', 'error')
      } finally {
        this.uploading = false
        // 清空文件输入框
        this.$refs.fileInput.value = ''
      }
    },

    async recognizeImage() {
      if (!this.form.attachmentPath) {
        this.showMessage('请先上传图片', 'error')
        return
      }

      try {
        this.recognizing = true
        
        const response = await this.$api.upload.recognizeWithEndpoint(
          '/api/upload/recognize-vat-invoice',
          this.form.attachmentPath
        )

        if (response.success && response.data) {
          this.fillFormFromOCR(response.data)
          this.showMessage('OCR识别成功', 'success')
        } else {
          this.showMessage('OCR识别失败: ' + (response.message || '未知错误'), 'error')
        }
      } catch (error) {
        console.error('OCR识别失败:', error)
        this.showMessage('OCR识别失败，请检查网络连接', 'error')
      } finally {
        this.recognizing = false
      }
    },

    fillFormFromOCR(ocrData) {
      // 根据OCR结果填充表单（简化版）
      if (ocrData.totalAmount || ocrData.amount) {
        this.form.amount = ocrData.totalAmount || ocrData.amount
      }

      if (ocrData.invoiceDate || ocrData.date) {
        this.form.billDate = ocrData.invoiceDate || ocrData.date
      }

      // 如果没有摘要，生成简单的摘要
      if (!this.form.summary) {
        if (ocrData.sellerName || ocrData.issuer) {
          this.form.summary = `${ocrData.sellerName || ocrData.issuer}开具的票据`
        } else {
          this.form.summary = '票据'
        }
      }
    },

    removeImage() {
      if (confirm('确定要删除这张图片吗？')) {
        this.form.attachmentPath = ''
        this.showMessage('图片已删除', 'info')
      }
    },

    async saveBill() {
      if (!this.validateForm()) {
        return
      }

      try {
        this.saving = true
        
        const billData = {
          ...this.form,
          amount: parseFloat(this.form.amount),
          status: '未使用'
        }

        const response = await this.$api.bill.save(billData)
        
        if (response.success) {
          this.showMessage('票据保存成功', 'success')
          setTimeout(() => {
            this.$router.push('/mobile/bills')
          }, 1000)
        } else {
          this.showMessage('保存失败: ' + (response.message || '未知错误'), 'error')
        }
      } catch (error) {
        console.error('保存票据失败:', error)
        this.showMessage('保存失败，请检查网络连接', 'error')
      } finally {
        this.saving = false
      }
    },

    validateForm() {
      if (!this.form.summary) {
        this.showMessage('请输入票据摘要', 'error')
        return false
      }

      if (!this.form.amount || parseFloat(this.form.amount) <= 0) {
        this.showMessage('请输入有效的金额', 'error')
        return false
      }

      if (!this.form.billDate) {
        this.showMessage('请选择开票日期', 'error')
        return false
      }

      return true
    },

    showMessage(text, type = 'info') {
      this.message = text
      this.messageType = type
      setTimeout(() => {
        this.clearMessage()
      }, 3000)
    },

    clearMessage() {
      this.message = ''
      this.messageType = 'info'
    }
  }
}
</script>

<style scoped>
.mobile-bill-add {
  min-height: 100vh;
  background: #f5f5f5;
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-left {
  display: flex;
  align-items: center;
  min-width: 60px;
}

.nav-center {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: center;
}

.nav-right {
  min-width: 80px;
  display: flex;
  justify-content: flex-end;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.nav-center h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.save-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn:hover:not(:disabled) {
  background: white;
}

.save-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.save-btn.saving {
  background: #ccc;
  color: #666;
}

.form-container {
  padding: 20px;
}

.upload-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  font-weight: 600;
  color: #333;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.upload-placeholder {
  color: #666;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.uploaded-image {
  position: relative;
}

.uploaded-image img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.image-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:not(.delete) {
  background: #667eea;
  color: white;
}

.action-btn.delete {
  background: #f44336;
  color: white;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-label.required::after {
  content: ' *';
  color: #f44336;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.readonly {
  background: #f8f9fa;
  color: #666;
}

.amount-input {
  position: relative;
  display: flex;
  align-items: center;
}

.currency {
  position: absolute;
  left: 16px;
  color: #666;
  font-weight: 600;
  z-index: 1;
}

.amount-input .form-input {
  padding-left: 40px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 1000;
  animation: slideDown 0.3s ease;
}

.message.success {
  background: #4caf50;
}

.message.error {
  background: #f44336;
}

.message.info {
  background: #2196f3;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 图标字体 */
.icon-back::before { content: '←'; }
.icon-camera::before { content: '📷'; }
.icon-scan::before { content: '🔍'; }
.icon-delete::before { content: '🗑️'; }

@media (max-width: 480px) {
  .form-container {
    padding: 15px;
  }

  .navbar {
    padding: 12px 15px;
  }

  .nav-center h1 {
    font-size: 18px;
  }

  .logo img {
    width: 28px;
    height: 28px;
  }
}
</style>
