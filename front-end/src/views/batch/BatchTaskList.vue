<template>
  <div class="batch-task-list-container">
    <div class="page-header">
      <h2>批量处理记录</h2>
      <div class="header-actions">
        <h-button @click="refreshList" :loading="loading">刷新</h-button>
        <h-button type="primary" @click="goToBatchImport">批量上传</h-button>
      </div>
    </div>

    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <label>任务类型：</label>
          <select v-model="filters.importType" @change="loadTaskList" class="filter-select">
            <option value="">全部</option>
            <option value="BANK_RECEIPT">银行回单</option>
            <option value="INVOICE">发票</option>
          </select>
        </div>
        <div class="filter-item">
          <label>状态：</label>
          <select v-model="filters.status" @change="loadTaskList" class="filter-select">
            <option value="">全部</option>
            <option value="UPLOADING">上传中</option>
            <option value="PROCESSING">处理中</option>
            <option value="RECOGNIZING">识别中</option>
            <option value="PREVIEWING">待预览</option>
            <option value="SAVING">保存中</option>
            <option value="COMPLETED">已完成</option>
            <option value="FAILED">失败</option>
            <option value="CANCELLED">已取消</option>
          </select>
        </div>
      </div>
    </div>

    <div class="task-list">
      <div v-if="loading && taskList.length === 0" class="loading-container">
        <h-loading size="large">
          <div>加载中...</div>
        </h-loading>
      </div>

      <div v-else-if="taskList.length === 0" class="empty-container">
        <div class="empty-content">
          <i class="fa fa-inbox empty-icon"></i>
          <h3>暂无批量处理记录</h3>
          <p>您还没有进行过批量上传操作</p>
          <h-button type="primary" @click="goToBatchImport">开始批量上传</h-button>
        </div>
      </div>

      <div v-else class="task-cards">
        <div 
          v-for="task in taskList" 
          :key="task.taskId" 
          class="task-card"
          :class="getTaskCardClass(task.status)"
        >
          <div class="task-header">
            <div class="task-title">
              <h4>{{ task.taskName }}</h4>
              <span class="task-type">{{ getImportTypeText(task.importType) }}</span>
            </div>
            <div class="task-actions">
              <h-button
                size="small"
                @click="viewProgress(task.taskId)"
                :type="task.status === 'PROCESSING' ? 'primary' : 'default'"
              >
                {{ task.status === 'PROCESSING' ? '查看进度' : '查看详情' }}
              </h-button>
              <h-button
                v-if="task.status === 'COMPLETED'"
                size="small"
                type="success"
                @click="viewResults(task.taskId)"
              >
                查看结果
              </h-button>
              <h-button
                v-if="canDeleteTask(task.status)"
                size="small"
                type="error"
                @click="deleteTask(task)"
              >
                删除
              </h-button>
            </div>
          </div>

          <div class="task-info">
            <div class="info-row">
              <span class="info-label">任务ID:</span>
              <span class="info-value">{{ task.taskId }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">创建时间:</span>
              <span class="info-value">{{ formatTime(task.createdTime) }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">状态:</span>
              <span class="status-badge" :class="getStatusClass(task.status)">
                {{ getStatusText(task.status) }}
              </span>
            </div>
          </div>

          <div class="task-stats">
            <div class="stat-item">
              <div class="stat-value">{{ task.totalFiles || 0 }}</div>
              <div class="stat-label">文件数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ task.totalImages || 0 }}</div>
              <div class="stat-label">图片数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ task.successCount || 0 }}</div>
              <div class="stat-label">成功</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ task.failedCount || 0 }}</div>
              <div class="stat-label">失败</div>
            </div>
          </div>

          <div class="task-progress" v-if="task.status === 'PROCESSING' || task.status === 'UPLOADING'">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: (task.progressPercentage || 0) + '%' }"
              ></div>
            </div>
            <span class="progress-text">{{ (task.progressPercentage || 0) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="taskList.length > 0">
      <h-pagination
        :total="total"
        :current="currentPage"
        :size="pageSize"
        @change="handlePageChange"
        show-total
        show-sizer
        @sizechange="handlePageSizeChange"
        :size-options="[10, 20, 50, 100]"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'BatchTaskList',
  data() {
    return {
      loading: false,
      taskList: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      filters: {
        importType: '',
        status: ''
      }
    }
  },
  mounted() {
    this.loadTaskList()
  },
  methods: {
    async loadTaskList() {
      this.loading = true
      try {
        // 构建扁平的参数对象，避免嵌套结构
        const params = {
          page: this.currentPage,
          size: this.pageSize,
          importType: this.filters.importType || '',
          status: this.filters.status || ''
        }

        const response = await this.$api.batch.getTaskList(params)
        if (response.success) {
          this.taskList = response.data.records || []
          this.total = response.data.total || 0
        }
      } catch (error) {
        this.$Message.error('加载任务列表失败')
      } finally {
        this.loading = false
      }
    },

    refreshList() {
      this.loadTaskList()
    },

    handlePageChange(page) {
      this.currentPage = page
      this.loadTaskList()
    },

    handlePageSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1
      this.loadTaskList()
    },

    viewProgress(taskId) {
      this.$router.push(`/batch/progress/${taskId}`)
    },

    viewResults(taskId) {
      // 跳转到结果页面或显示结果详情
      this.$router.push(`/batch/results/${taskId}`)
    },

    goToBatchImport() {
      this.$router.push('/bills/batch-import')
    },

    getImportTypeText(type) {
      const typeMap = {
        'BANK_RECEIPT': '银行回单',
        'INVOICE': '发票'
      }
      return typeMap[type] || type
    },

    getStatusText(status) {
      const statusMap = {
        'UPLOADING': '上传中',
        'PROCESSING': '处理中',
        'RECOGNIZING': '识别中',
        'PREVIEWING': '待预览',
        'SAVING': '保存中',
        'COMPLETED': '已完成',
        'PARTIAL_SUCCESS': '部分成功',
        'FAILED': '失败',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    },

    getStatusClass(status) {
      const classMap = {
        'UPLOADING': 'status-uploading',
        'PROCESSING': 'status-processing',
        'RECOGNIZING': 'status-processing',
        'PREVIEWING': 'status-previewing',
        'SAVING': 'status-processing',
        'COMPLETED': 'status-completed',
        'PARTIAL_SUCCESS': 'status-partial-success',
        'FAILED': 'status-failed',
        'CANCELLED': 'status-cancelled'
      }
      return classMap[status] || ''
    },

    getTaskCardClass(status) {
      return {
        'task-processing': status === 'PROCESSING',
        'task-completed': status === 'COMPLETED',
        'task-failed': status === 'FAILED'
      }
    },

    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    },

    canDeleteTask(status) {
      // 可以删除的状态：失败、待预览、已完成、部分成功、已取消
      return ['FAILED', 'PREVIEWING', 'COMPLETED', 'PARTIAL_SUCCESS', 'CANCELLED'].includes(status)
    },

    deleteTask(task) {
      this.$Confirm(`确定要删除任务"${task.taskName}"吗？此操作不可恢复。`).then(() => {
        this.loading = true
        this.$api.batch.deleteTask(task.taskId).then(() => {
          this.loadTaskList()
          this.$Message('删除成功')
        }).catch((error) => {
          this.$Message.error('删除失败')
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.batch-task-list-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #333;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .filter-section {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    
    .filter-row {
      display: flex;
      gap: 20px;
      align-items: center;
      
      .filter-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        label {
          font-weight: 500;
          color: #666;
          white-space: nowrap;
        }
      }
    }
  }

  .loading-container, .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;

    .empty-content {
      text-align: center;

      .empty-icon {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 16px;
      }

      h3 {
        color: #666;
        margin-bottom: 8px;
      }

      p {
        color: #999;
        margin-bottom: 20px;
      }
    }
  }

  .task-cards {
    display: grid;
    gap: 16px;

    .task-card {
      background: white;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      padding: 20px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-color: #1890ff;
      }

      &.task-processing {
        border-left: 4px solid #1890ff;
      }

      &.task-completed {
        border-left: 4px solid #52c41a;
      }

      &.task-failed {
        border-left: 4px solid #ff4d4f;
      }

      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;

        .task-title {
          flex: 1;

          h4 {
            margin: 0 0 4px 0;
            color: #333;
            font-size: 16px;
          }

          .task-type {
            background: #f0f0f0;
            color: #666;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
          }
        }

        .task-actions {
          display: flex;
          gap: 8px;
        }
      }

      .task-info {
        margin-bottom: 16px;

        .info-row {
          display: flex;
          margin-bottom: 8px;

          .info-label {
            width: 80px;
            color: #666;
            font-size: 13px;
          }

          .info-value {
            flex: 1;
            color: #333;
            font-size: 13px;
            font-family: monospace;
          }

          .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;

            &.status-uploading {
              background: #e6f7ff;
              color: #1890ff;
            }

            &.status-processing {
              background: #fff7e6;
              color: #fa8c16;
            }

            &.status-previewing {
              background: #f0f5ff;
              color: #597ef7;
            }

            &.status-completed {
              background: #f6ffed;
              color: #52c41a;
            }

            &.status-partial-success {
              background: #fff7e6;
              color: #fa8c16;
            }

            &.status-failed {
              background: #fff2f0;
              color: #ff4d4f;
            }

            &.status-cancelled {
              background: #f5f5f5;
              color: #8c8c8c;
            }
          }
        }
      }

      .task-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 16px;

        .stat-item {
          text-align: center;

          .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .task-progress {
        display: flex;
        align-items: center;
        gap: 12px;

        .progress-bar {
          flex: 1;
          height: 6px;
          background: #f0f0f0;
          border-radius: 3px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #40a9ff);
            transition: width 0.3s ease;
          }
        }

        .progress-text {
          font-size: 12px;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 24px;
    text-align: center;
  }

  .filter-select {
    width: 150px;
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    color: #606266;
    background-color: #fff;
    outline: none;
    transition: border-color 0.2s;

    &:focus {
      border-color: #409eff;
    }

    &:hover {
      border-color: #c0c4cc;
    }
  }
}
</style>
