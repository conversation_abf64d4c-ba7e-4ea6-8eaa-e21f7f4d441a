<template>
  <div class="batch-results-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>批量处理结果</h2>
        <div class="breadcrumb">
          <router-link to="/batch/tasks">批量处理记录</router-link>
          <span class="separator">></span>
          <span class="current">处理结果</span>
        </div>
      </div>
      <div class="header-right">
        <h-button type="text" @click="goBack">
          <i class="h-icon-arrow-left"></i>
          返回
        </h-button>
      </div>
    </div>

    <!-- 任务信息 -->
    <div class="task-info-card" v-if="taskInfo">
      <div class="task-header">
        <h3>{{ taskInfo.taskName }}</h3>
        <div class="task-status" :class="getStatusClass(taskInfo.status)">
          {{ getStatusText(taskInfo.status) }}
        </div>
      </div>
      <div class="task-meta">
        <div class="meta-item">
          <span class="label">任务类型：</span>
          <span class="value">{{ getImportTypeText(taskInfo.importType) }}</span>
        </div>
        <div class="meta-item">
          <span class="label">创建时间：</span>
          <span class="value">{{ formatTime(taskInfo.createdTime) }}</span>
        </div>
        <div class="meta-item">
          <span class="label">总数量：</span>
          <span class="value">{{ taskInfo.totalImages || 0 }}</span>
        </div>
        <div class="meta-item">
          <span class="label">成功：</span>
          <span class="value success">{{ taskInfo.successCount || 0 }}</span>
        </div>
        <div class="meta-item">
          <span class="label">失败：</span>
          <span class="value error">{{ taskInfo.failedCount || 0 }}</span>
        </div>
      </div>
    </div>

    <!-- 结果列表 -->
    <div class="results-container">
      <div class="results-header">
        <h4>处理结果详情</h4>
        <div class="filter-controls">
          <select v-model="statusFilter" @change="filterResults" class="status-filter">
            <option value="">全部状态</option>
            <option value="SUCCESS">成功</option>
            <option value="FAILED">失败</option>
            <option value="SKIPPED">跳过</option>
          </select>
        </div>
      </div>

      <div v-if="loading" class="loading-container">
        <h-loading size="large">
          <div>加载中...</div>
        </h-loading>
      </div>

      <div v-else-if="filteredResults.length === 0" class="empty-container">
        <div class="empty-content">
          <i class="fa fa-inbox empty-icon"></i>
          <h3>暂无结果数据</h3>
          <p>该任务还没有处理结果</p>
        </div>
      </div>

      <div v-else class="results-list">
        <div 
          v-for="item in paginatedResults" 
          :key="item.id" 
          class="result-item"
          :class="getItemStatusClass(item.status)"
        >
          <div class="item-header">
            <div class="item-info">
              <span class="item-name">{{ item.fileName || `图片 ${item.id}` }}</span>
              <span class="item-status" :class="getItemStatusClass(item.status)">
                {{ getItemStatusText(item.status) }}
              </span>
            </div>
            <div class="item-actions">
              <h-button size="small" @click="viewDetail(item)">查看详情</h-button>
              <h-button 
                v-if="item.imageUrl" 
                size="small" 
                type="text" 
                @click="viewImage(item)"
              >
                查看图片
              </h-button>
            </div>
          </div>
          
          <div class="item-content" v-if="item.data">
            <div class="data-preview">
              <div class="data-item" v-for="(value, key) in getPreviewData(item.data)" :key="key">
                <span class="data-label">{{ getFieldLabel(key) }}：</span>
                <span class="data-value">{{ value || '-' }}</span>
              </div>
            </div>
          </div>

          <div class="item-footer" v-if="item.errorMessage">
            <div class="error-message">
              <i class="fa fa-exclamation-triangle"></i>
              {{ item.errorMessage }}
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="filteredResults.length > 0">
        <h-pagination
          :total="filteredResults.length"
          :current="currentPage"
          :size="pageSize"
          @change="handlePageChange"
          show-total
          show-sizer
          @sizechange="handlePageSizeChange"
          :size-options="[10, 20, 50]"
        />
      </div>
    </div>

    <!-- 详情模态框 -->
    <h-modal 
      v-model="showDetailModal" 
      title="处理结果详情" 
      width="800px"
    >
      <div v-if="selectedItem" class="detail-content">
        <div class="detail-section">
          <h5>基本信息</h5>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">文件名：</span>
              <span class="value">{{ selectedItem.fileName || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">处理状态：</span>
              <span class="value" :class="getItemStatusClass(selectedItem.status)">
                {{ getItemStatusText(selectedItem.status) }}
              </span>
            </div>
            <div class="detail-item">
              <span class="label">置信度：</span>
              <span class="value">{{ selectedItem.confidenceScore || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">处理时间：</span>
              <span class="value">{{ selectedItem.processingTime ? selectedItem.processingTime + 'ms' : '-' }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section" v-if="selectedItem.data">
          <h5>识别结果</h5>
          <div class="json-viewer">
            <pre>{{ JSON.stringify(selectedItem.data, null, 2) }}</pre>
          </div>
        </div>

        <div class="detail-section" v-if="selectedItem.errorMessage">
          <h5>错误信息</h5>
          <div class="error-detail">
            {{ selectedItem.errorMessage }}
          </div>
        </div>
      </div>
      
      <div slot="footer">
        <h-button @click="showDetailModal = false">关闭</h-button>
      </div>
    </h-modal>

    <!-- 图片查看模态框 -->
    <h-modal 
      v-model="showImageModal" 
      title="查看图片" 
      width="600px"
    >
      <div v-if="selectedItem && selectedItem.imageUrl" class="image-viewer">
        <img :src="selectedItem.imageUrl" alt="处理图片" style="max-width: 100%; height: auto;" />
      </div>
      
      <div slot="footer">
        <h-button @click="showImageModal = false">关闭</h-button>
      </div>
    </h-modal>
  </div>
</template>

<script>
export default {
  name: 'BatchResultsPage',
  props: {
    taskId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      taskInfo: null,
      resultList: [],
      filteredResults: [],
      loading: false,
      statusFilter: '',
      currentPage: 1,
      pageSize: 20,
      
      // 模态框
      showDetailModal: false,
      showImageModal: false,
      selectedItem: null
    }
  },
  computed: {
    paginatedResults() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredResults.slice(start, end)
    }
  },
  mounted() {
    this.loadTaskInfo()
    this.loadResults()
  },
  methods: {
    async loadTaskInfo() {
      try {
        const response = await this.$api.batch.getProgress(this.taskId)
        if (response.success) {
          this.taskInfo = response.data
        }
      } catch (error) {
        this.$Message.error('加载任务信息失败')
      }
    },

    async loadResults() {
      try {
        this.loading = true
        const response = await this.$api.batch.getRecognitionResults(this.taskId)
        if (response.success) {
          this.resultList = response.data.map(item => ({
            ...item,
            data: this.parseData(item)
          }))
          this.filterResults()
        }
      } catch (error) {
        this.$Message.error('加载结果失败')
      } finally {
        this.loading = false
      }
    },

    parseData(item) {
      try {
        if (item.finalData) {
          return JSON.parse(item.finalData)
        } else if (item.parsedData) {
          return JSON.parse(item.parsedData)
        }
        return null
      } catch (e) {
        return null
      }
    },

    filterResults() {
      if (this.statusFilter) {
        this.filteredResults = this.resultList.filter(item => item.status === this.statusFilter)
      } else {
        this.filteredResults = [...this.resultList]
      }
      this.currentPage = 1
    },

    handlePageChange(page) {
      this.currentPage = page
    },

    handlePageSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1
    },

    viewDetail(item) {
      this.selectedItem = item
      this.showDetailModal = true
    },

    viewImage(item) {
      this.selectedItem = item
      this.showImageModal = true
    },

    goBack() {
      this.$router.go(-1)
    },

    getPreviewData(data) {
      if (!data) return {}

      // 根据导入类型显示不同的主要字段
      let mainFields = []
      if (this.taskInfo.importType === 'INVOICE') {
        mainFields = ['amount', 'invoice_number', 'issuer', 'recipient', 'type', 'bill_date', 'total_tax_amount', 'summary']
      } else {
        mainFields = ['amount', 'payee_name', 'payer_name', 'summary', 'receipts_date', 'transfer_date']
      }

      const preview = {}

      mainFields.forEach(field => {
        if (data[field] !== undefined) {
          preview[field] = data[field]
        }
        // 支持旧字段名的兼容性
        const oldFieldMap = {
          'amount': ['totalAmount'],
          'invoice_number': ['invoiceNumber'],
          'issuer': ['sellerName'],
          'recipient': ['buyerName'],
          'type': ['invoiceType'],
          'bill_date': ['invoiceDate'],
          'total_tax_amount': ['totalTaxAmount'],
          'payee_name': ['payeeName'],
          'payer_name': ['payerName'],
          'receipts_date': ['receiptsDate'],
          'transfer_date': ['transferDate']
        }

        if (oldFieldMap[field]) {
          oldFieldMap[field].forEach(oldField => {
            if (data[oldField] !== undefined && preview[field] === undefined) {
              preview[field] = data[oldField]
            }
          })
        }
      })

      return preview
    },

    getFieldLabel(key) {
      const labels = {
        'amount': '金额',
        'payee_name': '收款人',
        'payer_name': '付款人',
        'summary': '摘要',
        'receipts_date': '单据日期',
        'transfer_date': '转账日期',
        'type': '类型',
        'serial_number': '流水号',
        // 发票字段
        'invoice_number': '发票号码',
        'issuer': '开票方',
        'recipient': '收票方',
        'bill_date': '开票日期',
        'total_tax_amount': '税额',
        'tax_rate': '税率',
        'amount_in_words': '大写金额'
      }
      return labels[key] || key
    },

    getImportTypeText(type) {
      const typeMap = {
        'BANK_RECEIPT': '银行回单',
        'INVOICE': '发票'
      }
      return typeMap[type] || type
    },

    getStatusText(status) {
      const statusMap = {
        'UPLOADING': '上传中',
        'PROCESSING': '处理中',
        'RECOGNIZING': '识别中',
        'PREVIEWING': '待预览',
        'SAVING': '保存中',
        'COMPLETED': '已完成',
        'PARTIAL_SUCCESS': '部分成功',
        'FAILED': '失败',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    },

    getStatusClass(status) {
      const classMap = {
        'UPLOADING': 'status-uploading',
        'PROCESSING': 'status-processing',
        'RECOGNIZING': 'status-processing',
        'PREVIEWING': 'status-previewing',
        'SAVING': 'status-processing',
        'COMPLETED': 'status-completed',
        'FAILED': 'status-failed',
        'CANCELLED': 'status-cancelled'
      }
      return classMap[status] || ''
    },

    getItemStatusText(status) {
      const statusMap = {
        'PENDING': '待处理',
        'PROCESSING': '处理中',
        'RECOGNIZING': '识别中',
        'SUCCESS': '成功',
        'FAILED': '失败',
        'SKIPPED': '跳过'
      }
      return statusMap[status] || status
    },

    getItemStatusClass(status) {
      const classMap = {
        'PENDING': 'item-pending',
        'PROCESSING': 'item-processing',
        'RECOGNIZING': 'item-processing',
        'SUCCESS': 'item-success',
        'FAILED': 'item-failed',
        'SKIPPED': 'item-skipped'
      }
      return classMap[status] || ''
    },

    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="less" scoped>
.batch-results-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  .header-left {
    h2 {
      margin: 0 0 8px 0;
      color: #333;
    }

    .breadcrumb {
      font-size: 14px;
      color: #666;

      a {
        color: #1890ff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      .separator {
        margin: 0 8px;
      }

      .current {
        color: #333;
      }
    }
  }
}

.task-info-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      color: #333;
    }

    .task-status {
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;

      &.status-completed {
        background: #f6ffed;
        color: #52c41a;
      }

      &.status-processing {
        background: #e6f7ff;
        color: #1890ff;
      }

      &.status-failed {
        background: #fff2f0;
        color: #ff4d4f;
      }

      &.status-previewing {
        background: #fff7e6;
        color: #fa8c16;
      }
    }
  }

  .task-meta {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;

    .meta-item {
      display: flex;
      align-items: center;

      .label {
        color: #666;
        margin-right: 4px;
      }

      .value {
        color: #333;
        font-weight: 500;

        &.success {
          color: #52c41a;
        }

        &.error {
          color: #ff4d4f;
        }
      }
    }
  }
}

.results-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h4 {
      margin: 0;
      color: #333;
    }

    .status-filter {
      padding: 6px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      font-size: 14px;
      color: #606266;
      background-color: #fff;
      outline: none;

      &:focus {
        border-color: #409eff;
      }
    }
  }
}

.loading-container, .empty-container {
  text-align: center;
  padding: 60px 20px;
}

.empty-content {
  .empty-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    color: #666;
  }

  p {
    margin: 0;
    color: #999;
  }
}

.results-list {
  .result-item {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    margin-bottom: 12px;
    overflow: hidden;

    &.item-success {
      border-left: 4px solid #52c41a;
    }

    &.item-failed {
      border-left: 4px solid #ff4d4f;
    }

    &.item-processing {
      border-left: 4px solid #1890ff;
    }

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #e8e8e8;

      .item-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .item-name {
          font-weight: 500;
          color: #333;
        }

        .item-status {
          padding: 2px 8px;
          border-radius: 3px;
          font-size: 12px;

          &.item-success {
            background: #f6ffed;
            color: #52c41a;
          }

          &.item-failed {
            background: #fff2f0;
            color: #ff4d4f;
          }

          &.item-processing {
            background: #e6f7ff;
            color: #1890ff;
          }
        }
      }

      .item-actions {
        display: flex;
        gap: 8px;
      }
    }

    .item-content {
      padding: 16px;

      .data-preview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;

        .data-item {
          display: flex;
          align-items: center;

          .data-label {
            color: #666;
            margin-right: 8px;
            min-width: 80px;
          }

          .data-value {
            color: #333;
            font-weight: 500;
          }
        }
      }
    }

    .item-footer {
      padding: 12px 16px;
      background: #fff2f0;
      border-top: 1px solid #e8e8e8;

      .error-message {
        color: #ff4d4f;
        font-size: 14px;

        i {
          margin-right: 8px;
        }
      }
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.detail-content {
  .detail-section {
    margin-bottom: 24px;

    h5 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 16px;
      border-bottom: 1px solid #e8e8e8;
      padding-bottom: 8px;
    }

    .detail-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .detail-item {
        display: flex;
        align-items: center;

        .label {
          color: #666;
          margin-right: 8px;
          min-width: 80px;
        }

        .value {
          color: #333;
          font-weight: 500;
        }
      }
    }

    .json-viewer {
      background: #f5f5f5;
      border-radius: 4px;
      padding: 12px;
      max-height: 300px;
      overflow-y: auto;

      pre {
        margin: 0;
        font-size: 12px;
        line-height: 1.4;
        color: #333;
      }
    }

    .error-detail {
      background: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 4px;
      padding: 12px;
      color: #ff4d4f;
    }
  }
}

.image-viewer {
  text-align: center;

  img {
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
}
</style>
