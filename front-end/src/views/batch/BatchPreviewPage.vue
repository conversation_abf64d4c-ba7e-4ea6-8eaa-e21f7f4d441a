<template>
  <div class="batch-preview-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>批量预览</h2>
        <div class="breadcrumb">
          <router-link to="/bills/batch-import">批量导入</router-link>
          <span class="separator">></span>
          <span class="current">批量预览</span>
        </div>
      </div>
      <div class="header-right">
        <h-button type="text" @click="goBack">
          <i class="h-icon-arrow-left"></i>
          返回
        </h-button>
      </div>
    </div>

    <!-- 批量预览组件 -->
    <div class="preview-container">
      <BatchPreview
        ref="batchPreview"
        :task-id="taskId"
        @edit-item="handleEditItem"
        @batch-save="handleBatchSave"
      />
    </div>

    <!-- 编辑模态框 -->
    <h-modal 
      v-model="showEditModal" 
      title="编辑识别结果" 
      width="800px"
      :mask-closable="false"
    >
      <div v-if="editingItem" class="edit-form">
        <!-- 图片预览 -->
        <div class="image-preview">
          <img :src="editingItem.imageUrl" alt="识别图片" />
        </div>
        
        <!-- 编辑表单 -->
        <div class="form-content">
          <form class="edit-form-native">
            <!-- 银行回单表单 -->
            <template v-if="taskInfo.importType === 'BANK_RECEIPT'">
              <div class="form-item">
                <label>单据日期:</label>
                <input
                  type="date"
                  v-model="editForm.receiptsDate"
                  class="form-input"
                />
              </div>
              <div class="form-item">
                <label>转账日期:</label>
                <input
                  type="date"
                  v-model="editForm.transferDate"
                  class="form-input"
                />
              </div>
              <div class="form-item">
                <label>流水号:</label>
                <input
                  type="text"
                  v-model="editForm.serialNumber"
                  class="form-input"
                  placeholder="请输入流水号"
                />
              </div>
              <div class="form-item">
                <label>交易机构:</label>
                <input
                  type="text"
                  v-model="editForm.transactionInstitution"
                  class="form-input"
                  placeholder="请输入交易机构"
                />
              </div>
              <div class="form-item">
                <label>回单名称:</label>
                <input
                  type="text"
                  v-model="editForm.receiptTitle"
                  class="form-input"
                  placeholder="请输入回单名称"
                />
              </div>
              <div class="form-item">
                <label>付款人:</label>
                <input
                  type="text"
                  v-model="editForm.payerName"
                  class="form-input"
                  placeholder="请输入付款人"
                />
              </div>
              <div class="form-item">
                <label>付款账号:</label>
                <input
                  type="text"
                  v-model="editForm.payerAccount"
                  class="form-input"
                  placeholder="请输入付款账号"
                />
              </div>
              <div class="form-item">
                <label>收款人:</label>
                <input
                  type="text"
                  v-model="editForm.payeeName"
                  class="form-input"
                  placeholder="请输入收款人"
                />
              </div>
              <div class="form-item">
                <label>收款账号:</label>
                <input
                  type="text"
                  v-model="editForm.payeeAccount"
                  class="form-input"
                  placeholder="请输入收款账号"
                />
              </div>
            </template>

            <!-- 发票表单 -->
            <template v-if="taskInfo.importType === 'INVOICE'">
              <div class="form-item">
                <label>发票号码:</label>
                <input
                  type="text"
                  v-model="editForm.invoice_number || editForm.invoiceNumber"
                  @input="updateField('invoice_number', $event.target.value)"
                  class="form-input"
                  placeholder="请输入发票号码"
                />
              </div>
              <div class="form-item">
                <label>开票日期:</label>
                <input
                  type="date"
                  v-model="editForm.bill_date || editForm.invoiceDate"
                  @input="updateField('bill_date', $event.target.value)"
                  class="form-input"
                />
              </div>
              <div class="form-item">
                <label>销售方:</label>
                <input
                  type="text"
                  v-model="editForm.issuer || editForm.sellerName"
                  @input="updateField('issuer', $event.target.value)"
                  class="form-input"
                  placeholder="请输入销售方名称"
                />
              </div>
              <div class="form-item">
                <label>购买方:</label>
                <input
                  type="text"
                  v-model="editForm.recipient || editForm.buyerName"
                  @input="updateField('recipient', $event.target.value)"
                  class="form-input"
                  placeholder="请输入购买方名称"
                />
              </div>
              <div class="form-item">
                <label>发票类型:</label>
                <select
                  v-model="editForm.type || editForm.invoiceType"
                  @change="updateField('type', $event.target.value)"
                  class="form-input"
                >
                  <option value="">请选择发票类型</option>
                  <option value="增值税专用发票">增值税专用发票</option>
                  <option value="增值税普通发票">增值税普通发票</option>
                  <option value="电子发票(普通发票)">电子发票(普通发票)</option>
                  <option value="电子发票(专用发票)">电子发票(专用发票)</option>
                  <option value="收据">收据</option>
                  <option value="其他">其他</option>
                </select>
              </div>
              <div class="form-item">
                <label>税额:</label>
                <input
                  type="number"
                  v-model="editForm.total_tax_amount || editForm.totalTaxAmount"
                  @input="updateField('total_tax_amount', parseFloat($event.target.value) || 0)"
                  class="form-input"
                  placeholder="请输入税额"
                  step="0.01"
                />
              </div>
              <div class="form-item">
                <label>税率:</label>
                <input
                  type="number"
                  v-model="editForm.tax_rate || editForm.taxRate"
                  @input="updateField('tax_rate', parseFloat($event.target.value) || 0)"
                  class="form-input"
                  placeholder="请输入税率"
                  step="0.01"
                />
              </div>
              <div class="form-item">
                <label>大写金额:</label>
                <input
                  type="text"
                  v-model="editForm.amount_in_words || editForm.amountInWords"
                  @input="updateField('amount_in_words', $event.target.value)"
                  class="form-input"
                  placeholder="请输入大写金额"
                />
              </div>
            </template>

            <!-- 通用字段 -->
            <div class="form-item">
              <label>{{ taskInfo.importType === 'INVOICE' ? '合计金额:' : '金额:' }}</label>
              <input
                type="number"
                v-model="editForm.amount || editForm.totalAmount"
                @input="updateField('amount', parseFloat($event.target.value) || 0)"
                class="form-input"
                :placeholder="taskInfo.importType === 'INVOICE' ? '请输入合计金额' : '请输入金额'"
                step="0.01"
              />
            </div>
            <div class="form-item">
              <label>摘要:</label>
              <input
                type="text"
                v-model="editForm.summary"
                class="form-input"
                placeholder="请输入摘要"
              />
            </div>
            <div class="form-item" v-if="taskInfo.importType === 'INVOICE'">
              <label>发票类型:</label>
              <select v-model="editForm.type || editForm.invoiceType" @change="updateField('type', $event.target.value)" class="form-input">
                <option value="">请选择发票类型</option>
                <option value="增值税专用发票">增值税专用发票</option>
                <option value="增值税普通发票">增值税普通发票</option>
                <option value="收据">收据</option>
                <option value="其他">其他</option>
              </select>
            </div>
            <div class="form-item" v-else>
              <label>类型:</label>
              <select v-model="editForm.type" class="form-input">
                <option value="">请选择类型</option>
                <option value="收入">收入</option>
                <option value="支出">支出</option>
              </select>
            </div>
            <div class="form-item">
              <label>备注:</label>
              <textarea
                v-model="editForm.remark"
                class="form-textarea"
                placeholder="请输入备注信息"
                rows="3"
              ></textarea>
            </div>
            <div class="form-item">
              <label>OCR识别信息:</label>
              <textarea
                v-model="editForm.ocrRecognitionInfo"
                class="form-textarea ocr-info"
                placeholder="OCR识别的原始信息"
                rows="12"
                readonly
              ></textarea>
            </div>
          </form>
        </div>
      </div>
      
      <div slot="footer">
        <h-button @click="cancelEdit">取消</h-button>
        <h-button type="primary" @click="saveEdit" :loading="saving">保存</h-button>
      </div>
    </h-modal>

    <!-- 批量保存确认模态框 -->
    <h-modal 
      v-model="showBatchSaveModal" 
      title="批量保存确认" 
      width="600px"
    >
      <div class="batch-save-content">
        <p>确认要保存选中的 {{ batchSaveData.selectedItems.length }} 条记录到{{ getTargetTableText() }}吗？</p>
        <div class="save-summary">
          <div class="summary-item">
            <span class="label">成功识别：</span>
            <span class="value">{{ successCount }} 条</span>
          </div>
          <div class="summary-item">
            <span class="label">识别失败：</span>
            <span class="value">{{ failedCount }} 条</span>
          </div>
        </div>
      </div>
      
      <div slot="footer">
        <h-button @click="cancelBatchSave">取消</h-button>
        <h-button type="primary" @click="confirmBatchSave" :loading="batchSaving">
          确认保存
        </h-button>
      </div>
    </h-modal>
  </div>
</template>

<script>
import BatchPreview from '../../components/BatchPreview.vue'

export default {
  name: 'BatchPreviewPage',
  components: {
    BatchPreview
  },
  props: {
    taskId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      // 任务信息
      taskInfo: {},

      // 编辑相关
      showEditModal: false,
      editingItem: null,
      editForm: {},
      saving: false,

      // 批量保存相关
      showBatchSaveModal: false,
      batchSaveData: {
        taskId: '',
        selectedItems: [],
        selectedData: []
      },
      batchSaving: false
    }
  },
  computed: {
    successCount() {
      return this.batchSaveData.selectedData.filter(item => item.status === 'SUCCESS').length
    },
    
    failedCount() {
      return this.batchSaveData.selectedData.filter(item => item.status === 'FAILED').length
    }
  },
  mounted() {
    this.loadTaskInfo()

    // 检查是否需要高亮失败记录
    if (this.$route.query.highlightFailed === 'true') {
      this.$Message.info('已自动筛选失败的记录，您可以选择失败的记录进行重新识别')
      // 等待BatchPreview组件加载完成后，自动筛选失败记录
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.batchPreview && this.$refs.batchPreview.filterByStatus) {
            this.$refs.batchPreview.filterByStatus('FAILED')
          }
        }, 1000)
      })
    }
  },
  methods: {
    async loadTaskInfo() {
      try {
        const response = await this.$api.batch.getProgress(this.taskId)
        if (response.success) {
          this.taskInfo = response.data
        }
      } catch (error) {
        // 加载任务信息失败
      }
    },

    getTargetTableText() {
      if (this.taskInfo.importType === 'INVOICE') {
        return '发票表'
      } else {
        return '银行回单表'
      }
    },

    getJumpConfirmContent() {
      return '批量保存已完成！是否跳转到任务列表查看所有批量导入任务？'
    },

    getJumpRoute() {
      // 跳转到任务列表页面
      return '/batch/tasks'
    },

    checkForMixedTypes() {
      // 检查是否有记录更改了类型
      if (!this.$refs.batchPreview || !this.$refs.batchPreview.resultList) {
        return false
      }

      const resultList = this.$refs.batchPreview.resultList
      const originalType = this.taskInfo.importType

      return resultList.some(item => {
        if (item.data && item.data.actualType) {
          return item.data.actualType !== originalType
        }
        return false
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    
    handleEditItem(item) {
      this.editingItem = item

      // 根据导入类型初始化不同的表单数据
      if (this.taskInfo.importType === 'BANK_RECEIPT') {
        // 银行回单字段映射
        this.editForm = this.initBankReceiptEditForm(item)
      } else {
        // 发票字段映射
        this.editForm = this.initInvoiceEditForm(item)
      }

      this.showEditModal = true
    },

    // 初始化银行回单编辑表单
    initBankReceiptEditForm(item) {
      const data = item.data || {}

      return {
        // 基本字段
        amount: this.formatAmountValue(data.amount) || '',
        summary: data.summary || '',
        type: data.type || '',

        // 银行回单特有字段 - 支持下划线和驼峰两种格式，优先从rawOcrData提取
        receiptsDate: this.formatDateForInput(data.receiptsDate || data.receipts_date || ''),
        transferDate: this.formatDateForInput(data.transferDate || data.transfer_date || data.transactionDate || this.getBankReceiptFieldFromRaw(data, 'transferDate')),
        serialNumber: data.serialNumber || data.serial_number || this.getBankReceiptFieldFromRaw(data, 'serialNumber'),
        transactionInstitution: data.transactionInstitution || data.transaction_institution || this.getBankReceiptFieldFromRaw(data, 'transactionInstitution'),
        receiptTitle: data.receiptTitle || data.receipt_title || this.getBankReceiptFieldFromRaw(data, 'receiptTitle'),
        payerName: data.payerName || data.payer_name || this.getBankReceiptFieldFromRaw(data, 'payerName'),
        payerAccount: data.payerAccount || data.payer_account || this.getBankReceiptFieldFromRaw(data, 'payerAccount'),
        payeeName: data.payeeName || data.payee_name || this.getBankReceiptFieldFromRaw(data, 'payeeName'),
        payeeAccount: data.payeeAccount || data.payee_account || this.getBankReceiptFieldFromRaw(data, 'payeeAccount'),
        amountInWords: data.amountInWords || data.amount_in_words || '',

        // OCR信息
        ocrRecognitionInfo: this.formatOcrDataToRemark(data.ocrRecognitionInfo || item.originalData || data)
      }
    },

    // 初始化发票编辑表单
    initInvoiceEditForm(item) {
      const data = item.data || {}

      // 获取正确的发票类型（优先使用OCR识别的发票类型，而不是业务类型）
      let invoiceType = data.type || data.invoiceType || ''

      // 如果type是业务类型（如"支出"），尝试从OCR数据中获取真正的发票类型
      if (invoiceType === '支出' || invoiceType === '收入') {
        // 从OCR识别信息中提取发票类型
        const ocrInfo = data.ocrRecognitionInfoJson
        if (ocrInfo) {
          try {
            const ocrData = typeof ocrInfo === 'string' ? JSON.parse(ocrInfo) : ocrInfo
            if (ocrData.recognitionData && ocrData.recognitionData['发票类型']) {
              invoiceType = ocrData.recognitionData['发票类型']
            } else if (ocrData.recognitionData && ocrData.recognitionData['发票名称']) {
              invoiceType = ocrData.recognitionData['发票名称']
            }
          } catch (e) {
            // 解析OCR数据失败，使用默认值
          }
        }
      }

      return {
        ...data,
        type: invoiceType, // 使用正确的发票类型
        receiptsDate: data.receiptsDate ? this.formatDateForInput(data.receiptsDate) : '',
        transferDate: data.transferDate ? this.formatDateForInput(data.transferDate) : '',
        bill_date: data.bill_date ? this.formatDateForInput(data.bill_date) : '',
        tax_rate: data.tax_rate ? this.formatTaxRateForInput(data.tax_rate) : '',
        total_tax_amount: data.total_tax_amount ? this.formatNumberForInput(data.total_tax_amount) : '',
        remark: data.remark || '', // 保持用户备注为空，让用户自己填写
        ocrRecognitionInfo: this.formatOcrDataToRemark(data.ocrRecognitionInfo || item.originalData || data) // OCR信息只读显示
      }
    },

    // 从原始OCR数据中获取银行回单字段
    getBankReceiptFieldFromRaw(data, fieldName) {
      try {
        // 如果有rawOcrData，从中提取
        if (data.rawOcrData) {
          switch (fieldName) {
            case 'payeeName':
              return data.rawOcrData['收款人名称'] || data.rawOcrData['收款方'] || ''
            case 'payerName':
              return data.rawOcrData['付款人名称'] || data.rawOcrData['付款方'] || ''
            case 'payeeAccount':
              return data.rawOcrData['收款人账号'] || data.rawOcrData['收款账号'] || ''
            case 'payerAccount':
              return data.rawOcrData['付款人账号'] || data.rawOcrData['付款账号'] || ''
            case 'transferDate':
              const dateStr = data.rawOcrData['记账日期'] || data.rawOcrData['转账日期'] || data.rawOcrData['交易日期'] || ''
              return this.formatDateForInput(dateStr)
            case 'serialNumber':
              return data.rawOcrData['会计流水号'] || data.rawOcrData['回单编号'] || data.rawOcrData['流水号'] || ''
            case 'transactionInstitution':
              return data.rawOcrData['机构'] || data.rawOcrData['开户行名称'] || data.rawOcrData['交易机构'] || ''
            case 'receiptTitle':
              return data.rawOcrData['回单类型'] || data.rawOcrData['回单名称'] || data.rawOcrData['业务类型'] || ''
            default:
              return ''
          }
        }
        return ''
      } catch (error) {
        console.warn('从原始OCR数据提取字段失败:', error, fieldName)
        return ''
      }
    },

    updateField(fieldName, value) {
      // 更新字段值，支持新旧字段名
      this.$set(this.editForm, fieldName, value)
    },
    
    cancelEdit() {
      this.showEditModal = false
      this.editingItem = null
      this.editForm = {}
    },
    
    async saveEdit() {
      try {
        this.saving = true
        
        // 格式化日期
        const formData = {
          ...this.editForm,
          receiptsDate: this.editForm.receiptsDate || null,
          transferDate: this.editForm.transferDate || null
        }
        
        const response = await this.$api.batch.updateRecognitionResult(this.editingItem.id, formData)
        
        if (response.success) {
          this.$Message.success('保存成功')
          this.cancelEdit()
          // 刷新预览组件
          if (this.$refs.batchPreview && this.$refs.batchPreview.loadResults) {
            this.$refs.batchPreview.loadResults()
          }
        } else {
          this.$Message.error(response.message || '保存失败')
        }
      } catch (error) {
        this.$Message.error('保存失败')
      } finally {
        this.saving = false
      }
    },
    
    handleBatchSave(data) {
      this.batchSaveData = data
      this.showBatchSaveModal = true
    },
    
    cancelBatchSave() {
      this.showBatchSaveModal = false
      this.batchSaveData = {
        taskId: '',
        selectedItems: [],
        selectedData: []
      }
    },
    
    async confirmBatchSave() {
      try {
        this.batchSaving = true

        const response = await this.$api.batch.batchSave(
          this.batchSaveData.taskId,
          this.batchSaveData.selectedItems
        )

        if (response.success) {
          // 新的异步保存响应
          this.$Message.success('批量保存已开始，正在处理中...')
          this.cancelBatchSave()

          // 设置WebSocket监听保存完成通知
          this.setupSaveProgressListener(this.batchSaveData.taskId)

          // 刷新任务信息以显示"保存中"状态
          this.$refs.batchPreview.loadTaskInfo()

          // 备用跳转逻辑：如果WebSocket没有正常工作，5秒后检查任务状态
          setTimeout(() => {
            this.checkTaskStatusAndJump(this.batchSaveData.taskId)
          }, 5000)

        } else {
          this.$Message.error(response.message || '批量保存失败')
        }
      } catch (error) {
        this.$Message.error('批量保存失败')
      } finally {
        this.batchSaving = false
      }
    },

    setupSaveProgressListener(taskId) {
      // 导入WebSocket工具
      import('../../utils/websocket').then(({ subscribeBatchProgress }) => {
        subscribeBatchProgress(taskId, {
          onProgress: (data) => {
            // 可以在这里显示进度条或更新状态
            if (data.message) {
              this.$Message.info(data.message)
            }
          },
          onComplete: (data) => {
            if (data.type === 'save_completion') {
              this.$Message.success(data.message)
              // 刷新任务状态
              this.$refs.batchPreview.loadTaskInfo()

              // 询问用户是否跳转到相关页面
              this.showJumpConfirmation()
            }
          },
          onError: (data) => {
            this.$Message.error('保存失败: ' + data.message)
            // 刷新任务状态
            this.$refs.batchPreview.loadTaskInfo()
          }
        })
      })
    },

    // 检查任务状态并跳转（备用逻辑）
    async checkTaskStatusAndJump(taskId) {
      try {
        const response = await this.$http.get(`/api/batch/tasks/${taskId}`)
        if (response.success && response.data) {
          const task = response.data
          // 如果任务已完成，显示跳转确认
          if (task.status === 'COMPLETED') {
            this.showJumpConfirmation()
          }
        }
      } catch (error) {
        console.warn('检查任务状态失败:', error)
      }
    },

    // 显示跳转确认对话框
    showJumpConfirmation() {
      this.$Modal.confirm({
        title: '保存完成',
        content: this.getJumpConfirmContent(),
        onOk: () => {
          this.$router.push(this.getJumpRoute())
        },
        onCancel: () => {
          // 用户选择留在当前页面
        }
      })
    },

    // 格式化金额值，去除货币符号和逗号
    formatAmountValue(amountValue) {
      if (!amountValue) return ''

      // 转换为字符串并去除货币符号、逗号等
      const cleanAmount = String(amountValue)
        .replace(/[CNY¥$€£,\s]/g, '') // 去除常见货币符号和逗号
        .trim()

      // 验证是否为有效数字
      const numericValue = parseFloat(cleanAmount)
      if (isNaN(numericValue)) {
        console.warn('无效的金额格式:', amountValue)
        return ''
      }

      return numericValue.toString()
    },

    // 格式化日期为input[type="date"]需要的格式
    formatDateForInput(dateValue) {
      if (!dateValue) return ''

      // 处理中文日期格式：2016年03月30日 -> 2016-03-30
      if (dateValue.includes('年') && dateValue.includes('月') && dateValue.includes('日')) {
        const match = dateValue.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/)
        if (match) {
          const [, year, month, day] = match
          return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
        }
      }

      // 处理标准日期格式：2016-03-30
      if (dateValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return dateValue
      }

      try {
        const date = new Date(dateValue)
        if (isNaN(date.getTime())) return ''

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')

        return `${year}-${month}-${day}`
      } catch (error) {
        return ''
      }
    },

    // 格式化数字（去除货币符号）
    formatNumberForInput(numStr) {
      if (!numStr) return ''

      // 去除货币符号和空格
      const cleaned = String(numStr).replace(/[￥¥$€£,\s]/g, '')

      // 转换为数字
      const num = parseFloat(cleaned)
      return isNaN(num) ? '' : num
    },

    // 格式化税率（去除%符号）
    formatTaxRateForInput(rateStr) {
      if (!rateStr) return ''

      // 去除%符号和空格
      const cleaned = String(rateStr).replace(/[%\s]/g, '')

      // 转换为数字
      const num = parseFloat(cleaned)
      return isNaN(num) ? '' : num
    },

    // 将OCR原始数据格式化为易读的备注信息
    formatOcrDataToRemark(originalData) {
      if (!originalData) return ''

      try {
        // 如果是字符串，尝试解析为JSON并格式化
        if (typeof originalData === 'string') {
          try {
            const parsed = JSON.parse(originalData)
            return this.formatParsedOcrData(parsed)
          } catch (e) {
            // 如果解析失败，直接返回原字符串
            return originalData
          }
        }

        // 如果是对象，直接格式化
        if (originalData && typeof originalData === 'object') {
          return this.formatParsedOcrData(originalData)
        }

        return ''
      } catch (error) {
        return originalData ? String(originalData) : ''
      }
    },

    // 格式化解析后的OCR数据
    formatParsedOcrData(data) {
      if (!data) return ''

      const lines = []
      lines.push('=== OCR识别信息 ===')

      // 添加基本信息
      if (data.source) {
        lines.push(`识别来源: ${data.source}`)
      }
      if (data.timestamp) {
        const date = new Date(data.timestamp)
        lines.push(`识别时间: ${date.toLocaleString()}`)
      }
      if (data.fieldCount) {
        lines.push(`识别字段数: ${data.fieldCount}`)
      }
      lines.push('')

      // 格式化识别数据
      if (data.recognitionData) {
        this.formatRecognitionData(lines, data.recognitionData)
      } else if (data.rawOcrData) {
        this.formatRawOcrData(lines, data.rawOcrData, 'UNKNOWN')
      } else {
        // 直接显示所有字段
        Object.entries(data).forEach(([key, value]) => {
          if (key !== 'source' && key !== 'timestamp' && key !== 'fieldCount' && value) {
            lines.push(`${key}: ${value}`)
          }
        })
      }

      return lines.join('\n')
    },

    // 格式化分类后的识别数据
    formatRecognitionData(lines, recognitionData) {
      const categories = [
        { key: '基本信息', title: '--- 基本信息 ---' },
        { key: '账户信息', title: '--- 账户信息 ---' },
        { key: '金额信息', title: '--- 金额信息 ---' },
        { key: '日期信息', title: '--- 日期信息 ---' },
        { key: '其他信息', title: '--- 其他信息 ---' }
      ]

      categories.forEach(({ key, title }) => {
        if (recognitionData[key]) {
          lines.push(title)
          Object.entries(recognitionData[key]).forEach(([field, value]) => {
            if (value) {
              lines.push(`${field}: ${value}`)
            }
          })
          lines.push('')
        }
      })

      // 处理未分类的字段
      const processedKeys = categories.map(c => c.key)
      const remainingEntries = Object.entries(recognitionData).filter(([key]) =>
        !processedKeys.includes(key)
      )

      if (remainingEntries.length > 0) {
        lines.push('--- 其他识别字段 ---')
        remainingEntries.forEach(([key, value]) => {
          if (value && typeof value === 'object') {
            lines.push(`${key}:`)
            Object.entries(value).forEach(([subKey, subValue]) => {
              if (subValue) {
                lines.push(`  ${subKey}: ${subValue}`)
              }
            })
          } else if (value) {
            lines.push(`${key}: ${value}`)
          }
        })
      }
    },

    // 格式化原始OCR数据（用于向后兼容）
    formatRawOcrData(lines, rawOcrData, documentType) {
      if (!rawOcrData || typeof rawOcrData !== 'object') return

      if (documentType === 'BANK_RECEIPT') {
        this.formatBankReceiptFields(lines, rawOcrData)
      } else if (documentType === 'INVOICE') {
        this.formatInvoiceFields(lines, rawOcrData)
      } else {
        this.formatGenericFields(lines, rawOcrData)
      }
    },

    // 获取文档类型名称
    getDocumentTypeName(documentType) {
      const typeNames = {
        'BANK_RECEIPT': '银行回单',
        'INVOICE': '增值税发票',
        'UNKNOWN': '未知类型'
      }
      return typeNames[documentType] || documentType
    },

    // 格式化银行回单字段
    formatBankReceiptFields(lines, rawOcrData) {
      const bankFields = [
        { key: '回单名称', field: 'receiptTitle' },
        { key: '流水号', field: 'serialNumber' },
        { key: '交易机构', field: 'transactionInstitution' },
        { key: '单据日期', field: 'receiptsDate' },
        { key: '转账日期', field: 'transferDate' },
        { key: '付款人', field: 'payerName' },
        { key: '付款账号', field: 'payerAccount' },
        { key: '收款人', field: 'payeeName' },
        { key: '收款账号', field: 'payeeAccount' },
        { key: '金额', field: 'amount' },
        { key: '摘要', field: 'summary' },
        { key: '类型', field: 'type' }
      ]

      lines.push('--- 银行回单信息 ---')
      bankFields.forEach(({ key, field }) => {
        const value = rawOcrData[field] || this.findFieldByName(rawOcrData, field)
        if (value) {
          lines.push(`${key}: ${value}`)
        }
      })

      // 添加其他未匹配的字段
      this.addUnmatchedFields(lines, rawOcrData, bankFields.map(f => f.field))
    },

    // 格式化发票字段
    formatInvoiceFields(lines, rawOcrData) {
      const invoiceFields = [
        { key: '发票代码', field: 'invoiceCode' },
        { key: '发票号码', field: 'invoiceNumber' },
        { key: '开票日期', field: 'invoiceDate' },
        { key: '购买方名称', field: 'buyerName' },
        { key: '购买方税号', field: 'buyerTaxId' },
        { key: '销售方名称', field: 'sellerName' },
        { key: '销售方税号', field: 'sellerTaxId' },
        { key: '合计金额', field: 'totalAmount' },
        { key: '合计税额', field: 'totalTax' },
        { key: '价税合计', field: 'totalAmountWithTax' },
        { key: '税率', field: 'taxRate' },
        { key: '备注', field: 'remark' }
      ]

      lines.push('--- 发票信息 ---')
      invoiceFields.forEach(({ key, field }) => {
        const value = rawOcrData[field] || this.findFieldByName(rawOcrData, field)
        if (value) {
          lines.push(`${key}: ${value}`)
        }
      })

      // 添加其他未匹配的字段
      this.addUnmatchedFields(lines, rawOcrData, invoiceFields.map(f => f.field))
    },

    // 格式化通用字段
    formatGenericFields(lines, rawOcrData) {
      lines.push('--- 识别字段 ---')
      Object.entries(rawOcrData).forEach(([key, value]) => {
        if (value) {
          lines.push(`${key}: ${value}`)
        }
      })
    },

    // 根据字段名查找值
    findFieldByName(rawOcrData, fieldName) {
      // 尝试直接匹配
      if (rawOcrData[fieldName]) return rawOcrData[fieldName]

      // 尝试模糊匹配
      for (const [key, value] of Object.entries(rawOcrData)) {
        if (key.toLowerCase().includes(fieldName.toLowerCase()) ||
            fieldName.toLowerCase().includes(key.toLowerCase())) {
          return value
        }
      }
      return null
    },

    // 添加未匹配的字段
    addUnmatchedFields(lines, rawOcrData, matchedFields) {
      const unmatchedEntries = Object.entries(rawOcrData).filter(([key]) =>
        !matchedFields.includes(key) && !matchedFields.some(field =>
          key.toLowerCase().includes(field.toLowerCase()) ||
          field.toLowerCase().includes(key.toLowerCase())
        )
      )

      if (unmatchedEntries.length > 0) {
        lines.push('')
        lines.push('--- 其他识别字段 ---')
        unmatchedEntries.forEach(([key, value]) => {
          if (value) {
            lines.push(`${key}: ${value}`)
          }
        })
      }
    }
  }
}
</script>

<style scoped>
.batch-preview-page {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.breadcrumb {
  color: #666;
  font-size: 14px;
}

.breadcrumb a {
  color: #1890ff;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.separator {
  margin: 0 8px;
}

.current {
  color: #333;
}

.preview-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.edit-form {
  display: flex;
  gap: 20px;
}

.image-preview {
  flex: 0 0 300px;
}

.image-preview img {
  width: 100%;
  height: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-content {
  flex: 1;
}

.batch-save-content {
  padding: 20px 0;
}

.save-summary {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
}

.value {
  font-weight: 600;
  color: #333;
}

/* 原生表单样式 */
.edit-form-native {
  max-height: 400px;
  overflow-y: auto;
}

.form-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 12px;
}

.form-item label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  color: #333;
  line-height: 32px;
  text-align: right;
}

.form-input, .form-textarea {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  transition: border-color 0.3s ease;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-textarea.ocr-info {
  background-color: #f8f9fa !important;
  color: #666 !important;
  font-family: 'Courier New', monospace !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  border-color: #e9ecef !important;
  max-height: 300px !important;
  min-height: 200px !important;
  height: 300px !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  resize: none !important;
}
</style>
