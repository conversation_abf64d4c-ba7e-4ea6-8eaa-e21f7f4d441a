import Vue from 'vue';
import Hey<PERSON> from 'heyui';
import App from './views/App.vue';
import Login from './views/Login.vue';
import router from './router';
import store from './store';
import api from './api'
import { isMobile, initMobile } from './utils/mobile';
import AppContent from './components/AppContent';
import AccountDateChoose from './components/AccountDateChoose';
import SubMenu from './components/common/sub-menu';
import Chart from './views/app/chart/echarts';

import './js/config/dict';
import './js/common/filters';

if (process.env.NODE_ENV !== 'development') {
	require('./reporter');
}

require('font-awesome/css/font-awesome.css');
require('./styles/app.less');
require('./styles/mobile.less');

Vue.use(HeyUI);
Vue.prototype.$api = api;

Vue.component("app-content", AppContent);
Vue.component("account-date-choose", AccountDateChoose);
Vue.component('sub-menu', SubMenu);
Vue.component('Chart', Chart);

Vue.config.productionTip = false;

// 初始化移动端功能
initMobile();

// 移动端重定向逻辑
router.beforeEach((to, from, next) => {
	const isMobileDevice = isMobile();
	const isMobilePage = to.meta && to.meta.mobile;
	const isDesktopPage = !isMobilePage;

	// 调试信息
	console.log('路由导航:', {
		path: to.path,
		isMobileDevice,
		isMobilePage,
		isDesktopPage,
		userAgent: navigator.userAgent,
		windowWidth: window.innerWidth
	});

	// 如果是移动设备访问桌面页面，重定向到移动端
	// 但是如果直接访问 /mobile 路径，则不重定向
	if (isMobileDevice && isDesktopPage && !to.path.startsWith('/mobile')) {
		console.log('重定向到移动端:', to.path, '→ /mobile');
		next('/mobile');
		return;
	}

	// 如果是桌面设备访问移动端页面，重定向到桌面端
	if (!isMobileDevice && isMobilePage) {
		console.log('重定向到桌面端:', to.path, '→ /');
		next('/');
		return;
	}

	next();
});

const VueConfig = {
	router,
	store,
};

store.dispatch("init").then(() => {
	//实例化界面
	new Vue(Object.assign(VueConfig, {
		render: h => h(App)
	})).$mount('#app');
}).catch(() => {
	// 根据设备类型选择登录页面
	const isMobileDevice = isMobile();
	console.log('用户未登录，设备类型:', isMobileDevice ? '移动端' : '桌面端');

	if (isMobileDevice) {
		// 移动端用户未登录，直接跳转到移动端登录页
		// 先设置路由到移动端登录页，避免路由守卫死循环
		router.replace('/mobile/login');
		new Vue(Object.assign(VueConfig, {
			render: h => h(App)
		})).$mount('#app');
	} else {
		// 桌面端用户未登录，显示PC端登录页
		new Vue(Object.assign(VueConfig, {
			render: h => h(Login)
		})).$mount('#app');
	}
});
