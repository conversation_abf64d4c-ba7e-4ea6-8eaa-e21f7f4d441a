.h-panel {
  border: none;

  &-title {
    color: @dark-color;
  }

  &-bar {
    padding: 10px @margin;
  }

  &-tabs-bar {
    .h-tabs-default > .h-tabs-item {
      padding: 16px 15px;
      font-size: 18px;
    }
  }

  &-bar-s {
    padding-top: 8px;
    padding-bottom: 8px;

    .h-panel-title {
      font-size: 15px;
    }
  }

  &-body {
    padding: @margin;
  }
}

.label.h-col {
  line-height: 32px;
}

.h-menu-white .h-menu-li .h-menu-li-selected {
  background-color: #f0f6ff;
}

.h-table {
  td, th {
    font-size: 12px;
  }

  th {
    height: 35px;
  }

  td {
    height: 32px;
  }
}

.h-tree-li {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.nowrap {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* 深色模式覆盖样式 */
@media (prefers-color-scheme: dark) {
  .h-panel {
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;

    &-title {
      color: #ffffff !important;
    }

    &-body {
      background: #1e1e1e !important;
      color: #ffffff !important;
    }
  }

  .h-menu-white {
    background: #1e1e1e !important;
    
    .h-menu-li {
      color: #ffffff !important;
      
      .h-menu-li-selected {
        background-color: #3788ee !important;
        color: #ffffff !important;
      }
      
      &:hover {
        background-color: #404040 !important;
        color: #ffffff !important;
      }
    }
  }

  .h-table {
    background: #1e1e1e !important;
    color: #ffffff !important;
    
    td, th {
      background: #1e1e1e !important;
      color: #ffffff !important;
      border-color: #404040 !important;
    }
    
    th {
      background: #2d2d2d !important;
      color: #ffffff !important;
    }
    
    tbody tr {
      &:nth-child(even) {
        background: #2d2d2d !important;
        
        td {
          background: #2d2d2d !important;
          color: #ffffff !important;
        }
      }
      
      &:hover {
        background: #404040 !important;
        
        td {
          background: #404040 !important;
          color: #ffffff !important;
        }
      }
    }
  }

  .h-tree-li {
    color: #ffffff !important;
    
    &:hover {
      background: #404040 !important;
      color: #ffffff !important;
    }
    
    &.h-tree-li-selected {
      background: #3788ee !important;
      color: #ffffff !important;
    }
  }
}