// 登录页面样式文件
// 2024-2025现代化色彩系统

// CSS变量定义
:root {
  /* 2024-2025现代化色彩系统 */
  --primary-blue: #1e40af;
  --secondary-blue: #3b82f6;
  --primary-indigo: #6366F1;
  --primary-purple: #8B5CF6;
  --primary-pink: #EC4899;

  /* 强调色 */
  --accent-blue: #1d4ed8;
  --accent-cyan: #06b6d4;
  --accent-purple: #8B5CF6;
  --accent-green: #10b981;
  --accent-orange: #F59E0B;
  --accent-pink: #EC4899;
  --deep-purple: #6366f1;

  /* 渐变色 - 深蓝色系 */
  --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%);
  --gradient-secondary: linear-gradient(45deg, #06b6d4, #1d4ed8);
  --gradient-accent: linear-gradient(90deg, #8B5CF6, #06B6D4);
  --gradient-warm: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  --gradient-cool: linear-gradient(135deg, #1e40af 0%, #60a5fa 100%);

  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-dark: #0f172a;

  /* 文本色 */
  --text-primary: #1a202c;
  --text-secondary: #2d3748;
  --text-light: #4a5568;
  --text-white: #ffffff;

  /* 玻璃拟态效果 */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(20px);

  /* 现代阴影 */
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);

  /* 兼容性变量 */
  --primary-color: #3B82F6;
  --border: rgba(255, 255, 255, 0.2);
  --hover-background-color: rgba(255, 255, 255, 0.1);
  --white-color: #ffffff;
  --gray2-color: #64748b;
  --gray3-color: #94a3b8;
  --dark3-color: #1e293b;
  --dark4-color: #0f172a;
}

// 登录页面主容器
.login-vue {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;

  /* 动态背景粒子 */
  .background-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    animation: particleFloat 20s ease-in-out infinite;
    z-index: 1;
  }
}

// 登录容器
.login-container {
  display: flex;
  width: 100%;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  box-shadow: var(--shadow-large);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  overflow: hidden;
  position: relative;
  z-index: 2;
}

// 左侧内容区域
.login-left {
  flex: 1;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%);
  color: #ffffff;
  padding: 60px 50px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  min-height: 100vh;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
      radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 80% 70%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, transparent 40%, rgba(255, 255, 255, 0.03) 50%, transparent 60%);
    animation: backgroundShift 20s ease-in-out infinite;
    z-index: 1;
  }

  // 科技装饰元素
  .tech-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    pointer-events: none;

    .circuit-line {
      position: absolute;
      top: 20%;
      left: 10%;
      width: 80%;
      height: 2px;
      background: linear-gradient(90deg, transparent 0%, rgba(16, 185, 129, 0.6) 50%, transparent 100%);
      animation: dataFlow 3s ease-in-out infinite;

      &::before {
        content: '';
        position: absolute;
        top: -1px;
        left: 0;
        width: 20px;
        height: 4px;
        background: rgba(16, 185, 129, 0.8);
        border-radius: 2px;
        animation: circuitPulse 3s ease-in-out infinite;
      }
    }

    .data-flow {
      position: absolute;
      bottom: 30%;
      right: 15%;
      width: 60%;
      height: 1px;
      background: linear-gradient(270deg, transparent 0%, rgba(59, 130, 246, 0.5) 50%, transparent 100%);
      animation: dataFlow 4s ease-in-out infinite reverse;
    }
  }

  // 品牌核心区域
  .brand-core {
    position: relative;
    z-index: 3;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;

    .logo-section {
      margin-bottom: 50px;

      .logo-frame {
        position: relative;
        display: inline-block;
        margin-bottom: 30px;

        .main-logo {
          width: 120px;
          height: auto;
          filter: brightness(0) invert(1) drop-shadow(0 0 15px rgba(255, 255, 255, 0.4));
          transition: all 0.4s ease;
          animation: logoFloat 6s ease-in-out infinite;
        }

        .logo-pulse {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 140px;
          height: 140px;
          border: 2px solid rgba(16, 185, 129, 0.3);
          border-radius: 50%;
          animation: logoPulse 3s ease-in-out infinite;
        }
      }

      .brand-text {
        .system-title {
          font-size: 32px;
          font-weight: 700;
          margin: 0;
          letter-spacing: 2px;
          color: #ffffff;
          text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
          margin-bottom: 10px;
        }

        .version-tag {
          display: inline-block;
          padding: 4px 12px;
          background: rgba(16, 185, 129, 0.2);
          border: 1px solid rgba(16, 185, 129, 0.4);
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
          color: #10B981;
          letter-spacing: 1px;
          text-shadow: none;
        }
      }
    }

    // 核心特性
    .core-features {
      display: flex;
      gap: 30px;
      justify-content: center;
      flex-wrap: wrap;

      .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        padding: 20px;
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 16px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        cursor: pointer;
        min-width: 100px;

        &:hover {
          transform: translateY(-5px);
          background: rgba(255, 255, 255, 0.12);
          border-color: rgba(16, 185, 129, 0.4);
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);

          .feature-icon-wrapper {
            transform: scale(1.1);
            background: rgba(16, 185, 129, 0.2);
          }
        }

        .feature-icon-wrapper {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          i {
            font-size: 18px;
            color: #ffffff;
          }
        }

        span {
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }
      }
   }

   // 科技装饰元素
   .tech-decoration {
     position: absolute;
     top: 0;
     left: 0;
     right: 0;
     bottom: 0;
     pointer-events: none;
     z-index: 1;

     .circuit-line {
       position: absolute;
       width: 2px;
       height: 100px;
       background: linear-gradient(to bottom, transparent, var(--accent-cyan), transparent);
       animation: circuitFlow 3s ease-in-out infinite;

       &:nth-child(1) {
         top: 20%;
         left: 10%;
         animation-delay: 0s;
       }

       &:nth-child(2) {
         top: 60%;
         right: 15%;
         animation-delay: 1s;
       }

       &:nth-child(3) {
         top: 40%;
         left: 5%;
         animation-delay: 2s;
       }
     }

     .data-flow {
        position: absolute;
        width: 4px;
        height: 4px;
        background: var(--accent-blue);
        border-radius: 50%;
        animation: dataFlow 4s linear infinite;
        box-shadow: 0 0 10px var(--accent-blue);

       &:nth-child(4) {
         top: 30%;
         left: 20%;
         animation-delay: 0s;
       }

       &:nth-child(5) {
         top: 70%;
         right: 25%;
         animation-delay: 2s;
       }
     }
   }

   // 品牌核心区域
   .brand-core {
     text-align: center;
     margin-bottom: 60px;
     position: relative;
     z-index: 3;

     .logo-container {
       margin-bottom: 24px;
       position: relative;

       .logo {
          width: 80px;
          height: 80px;
          margin: 0 auto;
          display: block;
          filter: drop-shadow(0 0 20px rgba(29, 78, 216, 0.3));
          animation: logoFloat 6s ease-in-out infinite;
        }
     }

     .system-title {
       font-size: 32px;
       font-weight: 700;
       color: #ffffff;
       margin-bottom: 8px;
       text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
       letter-spacing: 2px;
     }

     .version-tag {
        display: inline-block;
        padding: 6px 16px;
        background: rgba(29, 78, 216, 0.2);
        border: 1px solid rgba(29, 78, 216, 0.3);
        border-radius: 20px;
        color: var(--accent-blue);
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 1px;
        text-transform: uppercase;
        backdrop-filter: blur(10px);
      }
   }

   // 核心特性
   .core-features {
     display: flex;
     justify-content: space-between;
     margin-bottom: 50px;
     position: relative;
     z-index: 3;

     .feature-item {
       flex: 1;
       text-align: center;
       padding: 0 15px;

       .feature-icon {
         width: 50px;
         height: 50px;
         margin: 0 auto 16px;
         background: rgba(255, 255, 255, 0.1);
         border-radius: 50%;
         display: flex;
         align-items: center;
         justify-content: center;
         font-size: 20px;
         color: var(--accent-cyan);
         border: 1px solid rgba(255, 255, 255, 0.2);
         transition: all 0.3s ease;
         backdrop-filter: blur(10px);

         &:hover {
            transform: scale(1.1);
            background: rgba(29, 78, 216, 0.2);
            border-color: var(--accent-blue);
            color: var(--accent-blue);
          }
       }

       .feature-name {
         font-size: 14px;
         color: #ffffff;
         font-weight: 500;
         margin-bottom: 8px;
       }

       .feature-desc {
         font-size: 12px;
         color: rgba(255, 255, 255, 0.7);
         line-height: 1.4;
       }
     }
   }

   // 数据可视化区域
   .data-visualization {
     margin-bottom: 40px;
     position: relative;
     z-index: 3;

     .data-grid {
       display: grid;
       grid-template-columns: repeat(3, 1fr);
       gap: 20px;

       .data-card {
         background: rgba(255, 255, 255, 0.05);
         border: 1px solid rgba(255, 255, 255, 0.1);
         border-radius: 12px;
         padding: 20px;
         text-align: center;
         backdrop-filter: blur(10px);
         transition: all 0.3s ease;

         &:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(29, 78, 216, 0.3);
          }

         .data-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--accent-blue);
            margin-bottom: 8px;
            text-shadow: 0 0 10px rgba(29, 78, 216, 0.3);
          }

         .data-label {
           font-size: 12px;
           color: rgba(255, 255, 255, 0.8);
           text-transform: uppercase;
           letter-spacing: 1px;
         }
       }
     }
   }

   // 底部科技元素
   .tech-footer {
     position: absolute;
     bottom: 30px;
     left: 50%;
     transform: translateX(-50%);
     display: flex;
     align-items: center;
     gap: 20px;
     z-index: 3;

     .connection-dots {
       display: flex;
       gap: 8px;

       .dot {
         width: 8px;
         height: 8px;
         border-radius: 50%;
         background: rgba(255, 255, 255, 0.3);
         animation: dotPulse 2s ease-in-out infinite;

         &:nth-child(1) { animation-delay: 0s; }
         &:nth-child(2) { animation-delay: 0.3s; }
         &:nth-child(3) { animation-delay: 0.6s; }
         &:nth-child(4) { animation-delay: 0.9s; }
       }
     }

     .system-status {
       font-size: 11px;
       color: rgba(255, 255, 255, 0.6);
       letter-spacing: 1px;
       text-transform: uppercase;

       .status-indicator {
          display: inline-block;
          width: 6px;
          height: 6px;
          background: var(--accent-blue);
          border-radius: 50%;
          margin-right: 6px;
          animation: statusBlink 2s ease-in-out infinite;
        }
     }
   }
 }

 // 新增动画
 @keyframes circuitFlow {
   0%, 100% {
     opacity: 0.3;
     transform: scaleY(0.5);
   }
   50% {
     opacity: 1;
     transform: scaleY(1);
   }
 }

 @keyframes dataFlow {
   0% {
     transform: translateY(0) scale(0.5);
     opacity: 0;
   }
   50% {
     transform: translateY(-50px) scale(1);
     opacity: 1;
   }
   100% {
     transform: translateY(-100px) scale(0.5);
     opacity: 0;
   }
 }

 @keyframes logoFloat {
   0%, 100% {
     transform: translateY(0);
   }
   50% {
     transform: translateY(-10px);
   }
 }

 @keyframes dotPulse {
   0%, 100% {
     opacity: 0.3;
     transform: scale(1);
   }
   50% {
     opacity: 1;
     transform: scale(1.2);
   }
 }

 @keyframes statusBlink {
   0%, 100% {
     opacity: 1;
   }
   50% {
     opacity: 0.3;
   }
 }

  // 数据可视化区域
  .data-visualization {
    position: relative;
    z-index: 3;
    margin: 40px 0;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      padding: 30px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      backdrop-filter: blur(15px);

      .stat-item {
        text-align: center;
        padding: 15px;

        .stat-number {
          font-size: 24px;
          font-weight: 700;
          color: #10B981;
          margin-bottom: 8px;
          text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
        }

        .stat-label {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 400;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  // 底部科技元素
  .tech-footer {
    position: relative;
    z-index: 3;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;

    .connection-dots {
      display: flex;
      align-items: center;
      gap: 8px;
      position: relative;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;

        &.active {
          background: #10B981;
          box-shadow: 0 0 10px rgba(16, 185, 129, 0.6);
          animation: dotPulse 2s ease-in-out infinite;
        }
      }

      .connection-line {
        position: absolute;
        top: 50%;
        left: 12px;
        width: calc(100% - 24px);
        height: 1px;
        background: linear-gradient(90deg, #10B981 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
        transform: translateY(-50%);
        z-index: -1;
      }
    }

    .system-status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8);

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #10B981;
        animation: statusBlink 2s ease-in-out infinite;
      }

      .status-text {
        font-weight: 400;
        letter-spacing: 0.5px;
      }
    }
  }

}

// 右侧登录区域
.login-right {
  flex: 0 0 520px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 60px;
  position: relative;
  border-left: 1px solid rgba(255, 255, 255, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
      radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    z-index: 1;
  }

  .login-content {
    max-width: 400px;
    width: 100%;
    position: relative;
    z-index: 1;
  }

  .login-header {
    text-align: center;
    margin-bottom: 40px;

    .header-decoration {
      width: 60px;
      height: 4px;
      background: var(--gradient-accent);
      border-radius: 2px;
      margin: 0 auto 24px;
      animation: decorationGlow 2s ease-in-out infinite;
    }

    .login-title {
      font-size: 36px;
      color: #1a202c;
      margin-bottom: 12px;
      font-weight: 700;
      letter-spacing: -0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .login-subtitle {
      color: #4a5568;
      font-size: 13px;
      letter-spacing: 3px;
      text-transform: uppercase;
      font-weight: 500;
      margin-bottom: 24px;
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    }

    .header-line {
      width: 100%;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, var(--text-secondary) 50%, transparent 100%);
      opacity: 0.3;
    }
  }

  .form-container {
    position: relative;
    z-index: 1;
  }
}

// 动画效果
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(-10px) rotate(240deg);
  }
}

@keyframes backgroundShift {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  50% {
    transform: translateX(20px) translateY(-20px);
  }
}

@keyframes logoGlow {
  0%, 100% {
    filter: brightness(0) invert(1) drop-shadow(0 0 20px rgba(255, 255, 255, 0.3));
  }
  50% {
    filter: brightness(0) invert(1) drop-shadow(0 0 30px rgba(255, 255, 255, 0.5));
  }
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes decorationGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.8), 0 0 40px rgba(59, 130, 246, 0.3);
  }
}

@keyframes dotFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}