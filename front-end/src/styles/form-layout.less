// 专业表单布局样式
@import "./var.less";

// 表单容器样式
.professional-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .form-title {
    font-size: 24px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e2e8f0;
  }
}

// 表单分组样式
.form-section {
  margin-bottom: 32px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 16px;
    padding: 8px 0;
    border-left: 4px solid @primary-color;
    padding-left: 12px;
    background: #f7fafc;
    margin-left: -12px;
    padding-right: 12px;
  }

  .section-content {
    display: grid;
    gap: 20px 24px;
    
    // 默认两列布局
    &.grid-2 {
      grid-template-columns: 1fr 1fr;
    }
    
    // 三列布局
    &.grid-3 {
      grid-template-columns: 1fr 1fr 1fr;
    }
    
    // 四列布局
    &.grid-4 {
      grid-template-columns: 1fr 1fr 1fr 1fr;
    }
    
    // 全宽布局
    &.grid-full {
      grid-template-columns: 1fr;
    }

    // 响应式设计
    @media (max-width: 1024px) {
      &.grid-4 {
        grid-template-columns: 1fr 1fr;
      }
    }

    @media (max-width: 768px) {
      &.grid-2, &.grid-3, &.grid-4 {
        grid-template-columns: 1fr;
      }
      gap: 16px;
    }
  }
}

// 表单项样式优化
.professional-form {
  .h-form-item {
    margin-bottom: 0;
    
    .h-form-item-label {
      font-weight: 500;
      color: #4a5568;
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 6px;
      
      &.required::after {
        content: " *";
        color: #e53e3e;
        font-weight: bold;
      }
    }

    .h-form-item-content {
      .h-input, input, textarea, .h-select, .h-datepicker {
        width: 100%;
        height: 40px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 0 12px;
        font-size: 14px;
        transition: all 0.2s ease;
        background: #ffffff;

        &:focus {
          outline: none;
          border-color: @primary-color;
          box-shadow: 0 0 0 3px rgba(55, 136, 238, 0.1);
        }

        &:hover {
          border-color: #9ca3af;
        }

        &[readonly] {
          background: #f9fafb;
          color: #6b7280;
          cursor: not-allowed;
        }

        &[disabled] {
          background: #f3f4f6;
          color: #9ca3af;
          cursor: not-allowed;
        }
      }

      textarea {
        height: 80px;
        padding: 12px;
        resize: vertical;
        line-height: 1.5;
      }

      // 金额输入框特殊样式
      .amount-input {
        position: relative;
        
        .currency-symbol {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: #6b7280;
          font-weight: 500;
          z-index: 1;
        }
        
        input {
          padding-left: 32px;
        }
      }
    }
  }
}

// 文件上传区域样式
.file-upload-section {
  margin-bottom: 24px;
  padding: 20px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  text-align: center;
  transition: all 0.2s ease;

  &:hover {
    border-color: @primary-color;
    background: #f0f9ff;
  }

  .upload-icon {
    font-size: 48px;
    color: #9ca3af;
    margin-bottom: 12px;
  }

  .upload-text {
    font-size: 16px;
    color: #4a5568;
    margin-bottom: 8px;
  }

  .upload-hint {
    font-size: 14px;
    color: #6b7280;
  }

  &.has-file {
    border-color: @green-color;
    background: #f0fdf4;
    
    .upload-icon {
      color: @green-color;
    }
  }
}

// 按钮组样式
.form-actions {
  margin-top: 40px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
  text-align: center;

  .h-btn {
    min-width: 100px;
    height: 40px;
    margin: 0 8px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;

    &.h-btn-primary {
      background: @primary-color;
      border-color: @primary-color;
      
      &:hover {
        background: darken(@primary-color, 10%);
        border-color: darken(@primary-color, 10%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(55, 136, 238, 0.3);
      }
    }

    &.h-btn-default {
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 特殊字段样式
.readonly-field {
  .h-input, input {
    background: #f8fafc !important;
    color: #64748b !important;
    font-weight: 500;
  }
}

.important-field {
  .h-form-item-label {
    color: @primary-color;
    font-weight: 600;
  }
}

// 金额显示样式
.amount-display {
  font-size: 18px;
  font-weight: 600;
  color: @primary-color;
}

// 状态标签样式
.status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  
  &.status-unused {
    background: #dbeafe;
    color: #1e40af;
  }
  
  &.status-used {
    background: #dcfce7;
    color: #166534;
  }
  
  &.status-cancelled {
    background: #fee2e2;
    color: #991b1b;
  }
}

// 深色主题适配
.dark-theme {
  .professional-form {
    background: #1a202c;
    color: #e2e8f0;

    .form-title {
      color: #f7fafc;
      border-bottom-color: #4a5568;
    }

    .section-title {
      color: #e2e8f0;
      background: #2d3748;
    }

    .h-form-item-label {
      color: #cbd5e0;
    }

    .h-input, input, textarea, .h-select, .h-datepicker {
      background: #2d3748;
      border-color: #4a5568;
      color: #e2e8f0;

      &:focus {
        border-color: @primary-color;
        background: #374151;
      }

      &[readonly] {
        background: #374151;
        color: #9ca3af;
      }
    }
  }

  .file-upload-section {
    background: #2d3748;
    border-color: #4a5568;

    &:hover {
      background: #374151;
    }
  }

  .form-actions {
    border-top-color: #4a5568;
  }
}
