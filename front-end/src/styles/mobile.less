/* 手机端专用样式 */

/* 基础重置 */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

input, textarea {
  -webkit-user-select: text;
  user-select: text;
}

/* 防止页面缩放 */
html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

/* 移动端视口设置 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f5f5f5;
}

/* 移动端安全区域适配 */
.mobile-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 移动端导航栏 */
.mobile-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 移动端按钮样式 */
.mobile-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 44px; /* iOS推荐的最小触摸目标 */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.mobile-btn:active {
  transform: scale(0.98);
}

.mobile-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.mobile-btn.secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
}

.mobile-btn.danger {
  background: #f44336;
  color: white;
}

.mobile-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 移动端表单样式 */
.mobile-form-group {
  margin-bottom: 20px;
}

.mobile-form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.mobile-form-input {
  width: 100%;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 12px;
  font-size: 16px;
  background: white;
  transition: all 0.3s ease;
  box-sizing: border-box;
  min-height: 44px;
}

.mobile-form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 移动端卡片样式 */
.mobile-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin: 10px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 移动端列表样式 */
.mobile-list-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin: 10px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.mobile-list-item:active {
  transform: scale(0.98);
}

/* 移动端加载动画 */
.mobile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.mobile-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: mobile-spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes mobile-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移动端消息提示 */
.mobile-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 1000;
  animation: mobile-slide-down 0.3s ease;
  max-width: 90%;
  text-align: center;
}

.mobile-message.success {
  background: #4caf50;
}

.mobile-message.error {
  background: #f44336;
}

.mobile-message.info {
  background: #2196f3;
}

.mobile-message.warning {
  background: #ff9800;
}

@keyframes mobile-slide-down {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 移动端浮动按钮 */
.mobile-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5);
}

.mobile-fab:active {
  transform: scale(0.95);
}

/* 移动端空状态 */
.mobile-empty {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.mobile-empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.mobile-empty-text {
  font-size: 16px;
  margin-bottom: 20px;
}

/* 移动端搜索框 */
.mobile-search {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 0 15px;
  margin: 15px 20px;
}

.mobile-search input {
  flex: 1;
  padding: 12px 0;
  border: none;
  background: transparent;
  font-size: 16px;
  outline: none;
}

.mobile-search .icon {
  color: #999;
  margin-right: 10px;
}

/* 移动端标签页 */
.mobile-tabs {
  display: flex;
  background: white;
  border-bottom: 1px solid #eee;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.mobile-tab {
  flex: 1;
  padding: 15px 20px;
  text-align: center;
  border: none;
  background: transparent;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 80px;
}

.mobile-tab.active {
  color: #667eea;
  border-bottom: 2px solid #667eea;
}

/* 移动端下拉刷新 */
.mobile-pull-refresh {
  position: relative;
  overflow: hidden;
}

.mobile-refresh-indicator {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 20px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: top 0.3s ease;
}

.mobile-refresh-indicator.visible {
  top: 20px;
}

/* 移动端图片预览 */
.mobile-image-preview {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 移动端响应式断点 */
@media (max-width: 480px) {
  .mobile-navbar {
    padding: 12px 15px;
  }
  
  .mobile-card,
  .mobile-list-item {
    margin: 8px 0;
    padding: 16px;
  }
  
  .mobile-fab {
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .mobile-form-input {
    padding: 14px;
  }
  
  .mobile-btn {
    padding: 10px 20px;
    font-size: 15px;
  }
}

/* 移动端横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-navbar {
    padding: 8px 15px;
  }
  
  .mobile-card,
  .mobile-list-item {
    padding: 12px;
    margin: 6px 0;
  }
}

/* 移动端深色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background: #121212;
    color: #ffffff;
  }
  
  .mobile-card,
  .mobile-list-item {
    background: #1e1e1e;
    color: #ffffff;
  }
  
  .mobile-form-input {
    background: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }
  
  .mobile-search {
    background: #2d2d2d;
  }
  
  .mobile-tabs {
    background: #1e1e1e;
    border-bottom-color: #404040;
  }
}
