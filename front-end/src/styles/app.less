@import (less) "./common.less";
@import (less) "~heyui/themes/common.less";
@import (less) "./overwrite.less";
@import (less) "./fonts/style.less";

body {
  background: #f3f6f8;
  color: rgb(47, 47, 47);
  font-weight: 400;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

p {
  margin: 8px 0;
}

pre {
  white-space: pre-wrap;
}

@frame-box-shadow: rgba(0, 21, 41, .08);

#app {
  .app-frame {
    min-height: 100vh;
  }

  .h-layout-sider {
    z-index: 2;
    box-shadow: 0 1px 1px @frame-box-shadow;
  }

  .h-layout-header {
    overflow: hidden;
    box-shadow: 0 1px 1px 0 @frame-box-shadow;
  }

  .h-layout-sider-collapsed {
    .app-logo {
      padding-left: 5px;
    }

    .h-layout-header-fixed {
      .sys-tabs-vue {
        left: @layout-sider-collapse-width;
      }
    }
  }

  .h-layout-header-fixed {
    .sys-tabs-vue {
      position: fixed;
      top: @layout-header-height;
      right: 0;
      z-index: 2;
      left: @layout-sider-width;
    }

    .sys-tabs-vue + .h-layout-content {
      margin-top: 45px;
    }
  }

  .h-layout-sider-fixed .h-layout-header-fixed {
    .h-layout-content {
      overflow: auto;
      height: calc(~"100vh - @{layout-header-height}");
    }

    .sys-tabs-vue + .h-layout-content {
      height: calc(~"100vh - @{layout-header-height} - @{sys-tabs-height}");
    }
  }

  .h-layout-sider-theme-dark .app-logo a {
    color: #FFF;
  }

}

@media (max-width: 900px) {
  #app {
    .app-header-info {
      .h-autocomplete, .app-header-icon-item {
        display: none;
      }
    }

    .h-layout {
      padding-left: 0;

      .app-menu-mask {
        position: fixed;
        left: @layout-sider-width;
        right: 0;
        top: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.2);
        z-index: 1;
      }

      &.h-layout-sider-collapsed {
        > .h-layout-sider {
          transform: translateX(-@layout-sider-collapse-width);
          overflow: hidden;
        }

        .app-menu-mask {
          display: none;
        }
      }
    }

    .h-layout-content {
      -webkit-overflow-scrolling: touch;
    }

    .h-layout-header-fixed .h-layout-header {
      left: 0 !important;
    }

    .sys-tabs-vue {
      left: 0 !important;
    }
  }
}


.frame-page,
.frame-flex-page {
  margin: 20px;
  .clearfix;

  &.frame-flex-page {
    display: flex;
    padding: 10px 0;
  }

  .frame-left {
    width: 224px;
    border-right: @border;
    margin-right: -1px;
  }

  .frame-main {
    flex: 1;
    border-left: @border;
    padding: 8px 40px;

    .subframe-title {
      font-size: 20px;
      color: rgba(0, 0, 0, 85);
      line-height: 28px;
      font-weight: 500;
      margin-bottom: 12px;
    }
  }
}

.actions {
  text-align: center;

  span, a {
    color: @primary-color;
    cursor: pointer;
    margin-right: 10px;

    &:hover {
      text-decoration: underline;
      color: darken(@primary-color, 20%);
    }

    &:last-child {
      margin-right: 0;
    }
  }
}

.cus-table {
  width: 100%;
  border-collapse: collapse;

  td {
    padding: 0 8px;
    border: 1px solid #e2e2e2;
    font-size: 12px;
    height: 32px;

    &.tip {
      background-color: #FFEEEF;
    }
  }

  tbody tr:nth-child(even) {
    background-color: #f8fbf8;
  }

  tbody tr:hover {
    background-color: #F0F6FF;
  }

  .header {
    td {
      background-color: #F5F5F5;
      text-align: center;
      font-weight: bold;
      height: 35px;
    }
  }
}

.small-td td {
  height: 32px !important;
}

/*在谷歌下移除input[number]的上下箭头*/
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}

/*在firefox下移除input[number]的上下箭头*/
input[type="number"] {
  -moz-appearance: textfield;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background: #121212 !important;
    color: #ffffff !important;
  }

  #app {
    .h-layout-content {
      background: #121212;
      color: #ffffff;
    }

    .h-layout-header {
      background: #1e1e1e !important;
      color: #ffffff !important;
      border-bottom: 1px solid #404040;
    }

    .h-layout-sider {
      background: #1e1e1e !important;
    }

    .app-frame-content {
      background: #121212;
      color: #ffffff;
    }
  }

  .frame-page,
  .frame-flex-page {
    background: #121212;
    color: #ffffff;

    .frame-main {
      background: #121212;
      color: #ffffff;
      border-left: 1px solid #404040;

      .subframe-title {
        color: #ffffff;
      }
    }

    .frame-left {
      background: #121212;
      color: #ffffff;
      border-right: 1px solid #404040;
    }
  }

  .cus-table {
    background: #1e1e1e;
    color: #ffffff;

    td {
      border: 1px solid #404040;
      color: #ffffff;

      &.tip {
        background-color: #2d1b1c;
        color: #ffffff;
      }
    }

    tbody tr:nth-child(even) {
      background-color: #2d2d2d;
    }

    tbody tr:hover {
      background-color: #404040;
    }

    .header {
      td {
        background-color: #2d2d2d;
        color: #ffffff;
      }
    }
  }

  /* HeyUI组件深色模式适配 */
  .h-panel {
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;

    .h-panel-title {
      color: #ffffff !important;
    }

    .h-panel-body {
      background: #1e1e1e !important;
      color: #ffffff !important;
    }
  }

  /* 表格深色模式 - 使用更强的选择器 */
  .h-table,
  table.h-table,
  .h-table-border {
    background: #1e1e1e !important;
    color: #ffffff !important;

    th, td {
      background: #1e1e1e !important;
      color: #ffffff !important;
      border: 1px solid #404040 !important;
    }

    thead th {
      background: #2d2d2d !important;
      color: #ffffff !important;
    }

    tbody tr {
      background: #1e1e1e !important;
      color: #ffffff !important;

      td {
        background: #1e1e1e !important;
        color: #ffffff !important;
      }

      &:nth-child(even) {
        background: #2d2d2d !important;

        td {
          background: #2d2d2d !important;
          color: #ffffff !important;
        }
      }

      &:hover {
        background: #404040 !important;

        td {
          background: #404040 !important;
          color: #ffffff !important;
        }
      }
    }
  }

  /* 表格内的所有文本元素 */
  .h-table * {
    color: #ffffff !important;
  }

  table.h-table * {
    color: #ffffff !important;
  }

  .h-table-border * {
    color: #ffffff !important;
  }

  .h-input {
    background: #2d2d2d !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;

    &:focus {
      border-color: #3788ee !important;
    }
  }

  .h-select {
    background: #2d2d2d !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;
  }

  .h-button {
    background: #2d2d2d !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;

    &.h-btn-primary {
      background: #3788ee !important;
      color: #ffffff !important;
      border-color: #3788ee !important;
    }

    &:hover {
      background: #404040 !important;
    }
  }

  .h-form {
    background: #1e1e1e !important;
    color: #ffffff !important;

    .h-form-item-label {
      color: #ffffff !important;
    }
  }

  .h-modal {
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;

    .h-modal-header {
      background: #2d2d2d !important;
      color: #ffffff !important;
      border-bottom: 1px solid #404040 !important;
    }

    .h-modal-body {
      background: #1e1e1e !important;
      color: #ffffff !important;
    }
  }

  .h-menu {
    background: #1e1e1e !important;

    .h-menu-item {
      color: #ffffff !important;

      &:hover {
        background: #404040 !important;
      }

      &.h-menu-item-selected {
        background: #3788ee !important;
        color: #ffffff !important;
      }
    }
  }

  /* 通用文本颜色修复 - 精确的选择器 */
  body, .h-layout-content, .frame-page, .frame-flex-page {
    color: #ffffff !important;
  }

  p, span, div:not(.h-input):not(.h-select):not(.h-button), label, li, ul, ol {
    color: #ffffff !important;
  }

  /* 确保深色背景元素的文字为白色 */
  .h-layout-sider, .h-layout-header, .h-panel, .h-modal, .h-form {
    color: #ffffff !important;
  }

  /* 确保深色背景元素内的所有文字为白色 */
  .h-layout-sider *, .h-layout-header *, .h-panel *, .h-modal *, .h-form * {
    color: #ffffff !important;
  }

  /* 链接颜色 */
  a {
    color: #60a5fa !important;

    &:hover {
      color: #93c5fd !important;
    }
  }

  /* 表单标签 */
  .label {
    color: #ffffff !important;
  }

  /* 确保表格内容可见 */
  table, .h-table, .h-table-border {
    color: #ffffff !important;
  }

  /* 表格行背景 */
  table tr, .h-table tr, .h-table-border tr {
    background: #1e1e1e !important;

    &:nth-child(even) {
      background: #2d2d2d !important;
    }

    &:hover {
      background: #404040 !important;
    }
  }

  /* 表格单元格 */
  table td, table th, .h-table td, .h-table th, .h-table-border td, .h-table-border th {
    color: #ffffff !important;
    border-color: #404040 !important;
  }

  /* 处理可能有白色背景的元素 */
  .h-input, .h-select, .h-textarea, .h-datepicker {
    background: #2d2d2d !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;
  }

  /* 下拉选项 */
  .h-select-dropdown, .h-autocomplete-dropdown {
    background: #2d2d2d !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;
  }

  .h-select-dropdown .h-select-item, .h-autocomplete-dropdown .h-autocomplete-item {
    color: #ffffff !important;
    
    &:hover {
      background: #404040 !important;
      color: #ffffff !important;
    }
  }

  /* 确保所有白色背景元素在深色模式下可见 */
  [style*="background: white"], [style*="background-color: white"], 
  [style*="background: #fff"], [style*="background-color: #fff"],
  [style*="background: #ffffff"], [style*="background-color: #ffffff"] {
    background: #2d2d2d !important;
    color: #ffffff !important;
  }

  /* 处理内联样式中的白色文字 */
  [style*="color: white"], [style*="color: #fff"], [style*="color: #ffffff"] {
    color: #000000 !important;
  }
}