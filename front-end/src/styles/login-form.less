// 登录表单样式文件

// LESS变量定义
@primary-blue: #2563EB;
@secondary-blue: #3B82F6;
@accent-green: #10B981;
@text-primary: #1a202c;
@text-secondary: #2d3748;
@border-color: #4a5568;
@error-color: #EF4444;
@success-color: #10B981;

// 登录表单样式
.login-form {
  .input-wrapper {
    position: relative;
    margin-bottom: 24px;

    .input-icon {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      color: @text-secondary;
      font-size: 18px;
      z-index: 2;
      transition: color 0.3s ease;
    }

    .form-input {
      width: 100%;
      height: 52px;
      padding: 0 16px 0 48px;
      border: 2px solid rgba(203, 213, 225, 0.6);
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.9);
      font-size: 16px;
      color: @text-primary;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10px);

      &::placeholder {
        color: rgba(148, 163, 184, 0.8);
        font-weight: 400;
      }

      &:focus {
        outline: none;
        border-color: @primary-blue;
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(59, 130, 246, 0.15);
        transform: translateY(-1px);

        + .input-icon {
          color: @primary-blue;
        }
      }

      &:hover {
        border-color: rgba(59, 130, 246, 0.4);
        background: rgba(255, 255, 255, 0.95);
      }
    }
  }

  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    font-size: 14px;

    .remember-me {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: @text-secondary;

      input[type="checkbox"] {
        display: none;
      }

      .checkmark {
        width: 16px;
        height: 16px;
        border: 2px solid @border-color;
        border-radius: 3px;
        margin-right: 8px;
        position: relative;
        transition: all 0.3s ease;

        &::after {
          content: '';
          position: absolute;
          left: 4px;
          top: 1px;
          width: 4px;
          height: 8px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
      }

      input[type="checkbox"]:checked + .checkmark {
        background-color: @primary-blue;
        border-color: @primary-blue;

        &::after {
          opacity: 1;
        }
      }

      &:hover .checkmark {
        border-color: @primary-blue;
      }
    }

    .forgot-password {
      color: @accent-green;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .login-button {
    width: 100%;
    height: 48px;
    background: linear-gradient(135deg, @primary-blue 0%, @secondary-blue 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(30, 58, 95, 0.3);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
    }

    &:disabled,
    &.loading {
      opacity: 0.7;
      cursor: not-allowed;
      transform: none;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }

    .fa-spinner {
      margin-right: 8px;
    }
  }
}