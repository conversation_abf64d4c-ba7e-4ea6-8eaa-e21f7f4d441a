/* 专业表单布局样式 */

/* 表单容器样式 */
.professional-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.professional-form .form-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

/* 表单分组样式 */
.form-section {
  margin-bottom: 32px;
}

.form-section .section-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  padding: 8px 0;
  border-left: 4px solid #3788ee;
  padding-left: 12px;
  background: #f7fafc;
  margin-left: -12px;
  padding-right: 12px;
}

.form-section .section-content {
  display: grid;
  gap: 20px 24px;
}

/* 网格布局 */
.section-content.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.section-content.grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

.section-content.grid-4 {
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.section-content.grid-full {
  grid-template-columns: 1fr;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .section-content.grid-4 {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .section-content.grid-2,
  .section-content.grid-3,
  .section-content.grid-4 {
    grid-template-columns: 1fr;
  }
  .section-content {
    gap: 16px;
  }
}

/* 表单项样式优化 */
.professional-form .h-form-item {
  margin-bottom: 0;
}

.professional-form .h-form-item .h-form-item-label {
  font-weight: 500;
  color: #4a5568;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 6px;
}

.professional-form .h-form-item .h-form-item-label.required::after {
  content: " *";
  color: #e53e3e;
  font-weight: bold;
}

.professional-form .h-form-item .h-form-item-content .h-input,
.professional-form .h-form-item .h-form-item-content input,
.professional-form .h-form-item .h-form-item-content textarea,
.professional-form .h-form-item .h-form-item-content .h-select,
.professional-form .h-form-item .h-form-item-content .h-datepicker {
  width: 100%;
  height: 40px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0 12px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #ffffff;
  box-sizing: border-box;
}

.professional-form .h-form-item .h-form-item-content .h-input:focus,
.professional-form .h-form-item .h-form-item-content input:focus,
.professional-form .h-form-item .h-form-item-content textarea:focus,
.professional-form .h-form-item .h-form-item-content .h-select:focus,
.professional-form .h-form-item .h-form-item-content .h-datepicker:focus {
  outline: none;
  border-color: #3788ee;
  box-shadow: 0 0 0 3px rgba(55, 136, 238, 0.1);
}

.professional-form .h-form-item .h-form-item-content .h-input:hover,
.professional-form .h-form-item .h-form-item-content input:hover,
.professional-form .h-form-item .h-form-item-content textarea:hover,
.professional-form .h-form-item .h-form-item-content .h-select:hover,
.professional-form .h-form-item .h-form-item-content .h-datepicker:hover {
  border-color: #9ca3af;
}

.professional-form .h-form-item .h-form-item-content input[readonly],
.professional-form .h-form-item .h-form-item-content textarea[readonly] {
  background: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.professional-form .h-form-item .h-form-item-content input[disabled],
.professional-form .h-form-item .h-form-item-content textarea[disabled] {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

.professional-form .h-form-item .h-form-item-content textarea {
  height: 80px;
  padding: 12px;
  resize: vertical;
  line-height: 1.5;
}

/* 金额输入框特殊样式 */
.professional-form .amount-input {
  position: relative;
}

.professional-form .amount-input .currency-symbol {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-weight: 500;
  z-index: 1;
}

.professional-form .amount-input input {
  padding-left: 32px;
}

/* 文件上传区域样式 */
.file-upload-section {
  margin-bottom: 24px;
  padding: 20px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  text-align: center;
  transition: all 0.2s ease;
}

.file-upload-section:hover {
  border-color: #3788ee;
  background: #f0f9ff;
}

.file-upload-section .upload-icon {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 12px;
}

.file-upload-section .upload-text {
  font-size: 16px;
  color: #4a5568;
  margin-bottom: 8px;
}

.file-upload-section .upload-hint {
  font-size: 14px;
  color: #6b7280;
}

.file-upload-section.has-file {
  border-color: #10b981;
  background: #f0fdf4;
}

.file-upload-section.has-file .upload-icon {
  color: #10b981;
}

/* 按钮组样式 */
.form-actions {
  margin-top: 40px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
  text-align: center;
}

.form-actions .h-btn {
  min-width: 100px;
  height: 40px;
  margin: 0 8px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.form-actions .h-btn.h-btn-primary {
  background: #3788ee;
  border-color: #3788ee;
}

.form-actions .h-btn.h-btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 136, 238, 0.3);
}

.form-actions .h-btn.h-btn-default:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 特殊字段样式 */
.readonly-field .h-input,
.readonly-field input {
  background: #f8fafc !important;
  color: #64748b !important;
  font-weight: 500;
}

.important-field .h-form-item-label {
  color: #3788ee;
  font-weight: 600;
}

/* 金额显示样式 */
.amount-display {
  font-size: 18px;
  font-weight: 600;
  color: #3788ee;
}

/* 状态标签样式 */
.status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-tag.status-unused {
  background: #dbeafe;
  color: #1e40af;
}

.status-tag.status-used {
  background: #dcfce7;
  color: #166534;
}

.status-tag.status-cancelled {
  background: #fee2e2;
  color: #991b1b;
}
