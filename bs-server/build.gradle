plugins {
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'java'
}

group 'cn.gson.financial'
version '0.1'

sourceCompatibility = 1.8

repositories {
    maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
    mavenCentral()
}

dependencies {
    implementation project(':kernel')
    runtimeOnly('com.mysql:mysql-connector-j:8.3.0')
    implementation('com.itextpdf:itextpdf:5.5.13.1')
    implementation('org.apache.poi:poi:4.1.0')
    implementation('org.apache.poi:poi-ooxml:4.1.0')
    // PDF处理依赖
    implementation('org.apache.pdfbox:pdfbox:2.0.29')
    implementation('org.apache.pdfbox:pdfbox-tools:2.0.29')
    implementation 'cn.dev33:sa-token-spring-boot-starter:1.29.0'
    implementation 'cn.dev33:sa-token-dao-redis:1.29.0'
    implementation 'cn.hutool:hutool-all:5.7.19'
    implementation 'org.apache.commons:commons-pool2'
    implementation('org.apache.poi:poi-ooxml-schemas:4.1.0')
    implementation('com.belerweb:pinyin4j:2.5.1')
    implementation('org.springframework.boot:spring-boot-starter-web')
    // WebSocket支持
    implementation('org.springframework.boot:spring-boot-starter-websocket')
    implementation 'com.tencentcloudapi:tencentcloud-sdk-java:3.1.1277'
    // Swagger API文档
    implementation 'io.springfox:springfox-swagger2:2.9.2'
    implementation 'io.springfox:springfox-swagger-ui:2.9.2'
    // FastJSON
    implementation 'com.alibaba:fastjson:1.2.83'
    // MyBatis Plus分页插件
    implementation 'com.baomidou:mybatis-plus-extension:3.2.0'
    testImplementation('org.springframework.boot:spring-boot-starter-test')
}

bootJar {
    delete("${project.projectDir}/build/libs")
    archivesBaseName = 'financial'

    launchScript()
}
