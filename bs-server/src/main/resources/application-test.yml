spring:
  datasource:
    url: ************************************************************************************************************************************
    password: jk2d6_jvRTkFELC
    username: fxy
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  redis:
    # Redis数据库索引（默认为0）
    database: 10
    # Redis服务器地址
    host: ***********
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: redis_yMY38f
    # 连接超时时间
    timeout: 10s

logging:
  level:
    "cn.gson.financial.kernel.model.mapper": info
  path: /data/logs/financial

mybatis-plus:
  type-aliases-package: cn.gson.financial.kernel.model.entity
  configuration:
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true
  mapper-locations:
    - classpath:/mappers/*Mapper.xml
