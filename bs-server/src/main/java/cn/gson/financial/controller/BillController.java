package cn.gson.financial.controller;

import cn.gson.financial.annotation.Permissions;
import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.common.Roles;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BillAiResult;
import cn.gson.financial.kernel.service.BillService;
import cn.gson.financial.kernel.service.BillAiResultService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/bill")
@AllArgsConstructor
public class BillController extends BaseController {

    private final BillService service;
    private final BillAiResultService billAiResultService;

    @GetMapping("/list")
    @Permissions({Roles.Manager, Roles.Director, Roles.Making, Roles.Cashier, Roles.View, Roles.BillOnly})
    public JsonResult list(Integer bill_year, Integer bill_month, Integer page) {
        LambdaQueryWrapper<Bill> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Bill::getAccountSetsId, this.accountSetsId);
        
        if (bill_year != null) {
            queryWrapper.eq(Bill::getBillYear, bill_year);
        }
        
        if (bill_month != null) {
            queryWrapper.eq(Bill::getBillMonth, bill_month);
        }
        
        queryWrapper.orderByDesc(Bill::getBillDate);
        queryWrapper.orderByDesc(Bill::getBillNo);
        
        IPage<Bill> pageResult = this.service.page(new Page<>(page == null ? 1 : page, 10), queryWrapper);
        
        // 获取票据对应的AI处理结果
        List<Bill> bills = pageResult.getRecords();
        List<Integer> billIds = bills.stream().map(Bill::getId).collect(Collectors.toList());
        
        // 批量查询AI处理结果
        Map<Integer, BillAiResult> aiResultMap = new HashMap<>();
        if (!billIds.isEmpty()) {
            for (Integer billId : billIds) {
                BillAiResult aiResult = billAiResultService.getByBillId(billId);
                if (aiResult != null) {
                    aiResultMap.put(billId, aiResult);
                }
            }
        }
        
        // 将AI处理结果合并到票据数据中
        List<Map<String, Object>> enrichedBills = bills.stream().map(bill -> {
            Map<String, Object> billMap = new HashMap<>();
            // 复制票据基本信息
            billMap.put("id", bill.getId());
            billMap.put("billNo", bill.getBillNo());
            billMap.put("billDate", bill.getBillDate());
            billMap.put("type", bill.getType());
            billMap.put("amount", bill.getAmount());
            billMap.put("issuer", bill.getIssuer());
            billMap.put("recipient", bill.getRecipient());
            billMap.put("summary", bill.getSummary());
            billMap.put("remark", bill.getRemark());
            billMap.put("status", bill.getStatus());
            billMap.put("createTime", bill.getCreateTime());
            billMap.put("updateTime", bill.getUpdateTime());
            
            // 添加AI处理结果信息
            BillAiResult aiResult = aiResultMap.get(bill.getId());
            if (aiResult != null) {
                billMap.put("aiProcessed", aiResult.getAiProcessed());
                billMap.put("confidence", aiResult.getConfidence());
                billMap.put("aiAnalysis", aiResult.getAiAnalysis());
                billMap.put("suggestedSubjects", aiResult.getSuggestedSubjects());
                billMap.put("aiStatus", "completed");
            } else {
                billMap.put("aiProcessed", false);
                billMap.put("confidence", null);
                billMap.put("aiAnalysis", "");
                billMap.put("suggestedSubjects", "");
                billMap.put("aiStatus", "pending");
            }
            
            return billMap;
        }).collect(Collectors.toList());
        
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("records", enrichedBills);
        result.put("total", pageResult.getTotal());
        result.put("size", pageResult.getSize());
        result.put("current", pageResult.getCurrent());
        result.put("pages", pageResult.getPages());
        
        return JsonResult.successful(result);
    }

    @PostMapping
    @Permissions({Roles.Manager, Roles.Director, Roles.Making, Roles.BillOnly})
    public JsonResult save(@RequestBody Bill bill) {
        Bill result = this.service.save(this.accountSetsId, bill, this.currentUser);
        return JsonResult.successful(result);
    }

    @PutMapping
    @Permissions({Roles.Manager, Roles.Director, Roles.Making, Roles.BillOnly})
    public JsonResult update(@RequestBody Bill bill) {
        Bill result = this.service.update(this.accountSetsId, bill);
        return JsonResult.successful(result);
    }

    @DeleteMapping("{id}")
    @Permissions({Roles.Manager, Roles.Director, Roles.Making})
    public JsonResult delete(@PathVariable Integer id) {
        this.service.removeById(id);
        return JsonResult.successful();
    }

    @PostMapping("batchDelete")
    @Permissions({Roles.Manager, Roles.Director, Roles.Making})
    public JsonResult batchDelete(@RequestBody Integer[] checked) {
        if (checked != null && checked.length > 0) {
            for (Integer id : checked) {
                this.service.removeById(id);
            }
        }
        return JsonResult.successful();
    }

    @GetMapping("{id}")
    @Permissions({Roles.Manager, Roles.Director, Roles.Making, Roles.Cashier, Roles.View, Roles.BillOnly})
    public JsonResult load(@PathVariable Integer id) {
        Bill bill = this.service.getById(id);
        return JsonResult.successful(bill);
    }

    @GetMapping("/bill-no")
    @Permissions({Roles.Manager, Roles.Director, Roles.Making, Roles.BillOnly})
    public JsonResult loadBillNo() {
        String billNo = this.service.generateBillNo(this.accountSetsId, new java.util.Date());
        return JsonResult.successful(billNo);
    }
}