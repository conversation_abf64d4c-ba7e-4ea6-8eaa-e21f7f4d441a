package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.service.AiMergeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * AI智能归并控制器
 */
@Slf4j
@RestController
@RequestMapping("/ai-merge")
@Api(tags = "AI智能归并管理")
public class AiMergeController extends BaseController {

    @Autowired
    private AiMergeService aiMergeService;

    @PostMapping("/documents/analyze")
    @ApiOperation("AI分析票据归并建议")
    public JsonResult analyzeDocumentMerge(@RequestBody List<Integer> billIds) {
        try {
            log.info("AI分析票据归并建议，账套ID: {}, 票据数量: {}", this.accountSetsId, billIds.size());
            
            // 获取票据列表
            List<Bill> bills = aiMergeService.getUnmergedDocumentsForAi(this.accountSetsId, null);
            
            // 如果指定了票据ID，则过滤
            if (billIds != null && !billIds.isEmpty()) {
                bills = bills.stream()
                    .filter(bill -> billIds.contains(bill.getId()))
                    .collect(java.util.stream.Collectors.toList());
            }
            
            Map<String, Object> result = aiMergeService.analyzeMergeDocumentSuggestions(this.accountSetsId, bills);
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("AI分析票据归并建议失败", e);
            return JsonResult.failure("AI分析失败: " + e.getMessage());
        }
    }

    @PostMapping("/documents/execute")
    @ApiOperation("AI执行票据归并")
    public JsonResult executeDocumentMerge(@RequestBody List<Integer> billIds) {
        try {
            log.info("AI执行票据归并，账套ID: {}, 票据数量: {}", this.accountSetsId, billIds.size());

            // 获取票据列表
            List<Bill> bills = aiMergeService.getUnmergedDocumentsForAi(this.accountSetsId, null);

            // 如果指定了票据ID，则过滤
            if (billIds != null && !billIds.isEmpty()) {
                bills = bills.stream()
                    .filter(bill -> billIds.contains(bill.getId()))
                    .collect(java.util.stream.Collectors.toList());
            }

            Map<String, Object> result = aiMergeService.aiMergeDocuments(this.accountSetsId, bills, this.currentUser.getId());
            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("AI执行票据归并失败", e);
            return JsonResult.failure("AI归并失败: " + e.getMessage());
        }
    }

    @PostMapping("/documents/execute-selected")
    @ApiOperation("AI执行选中的票据归并")
    public JsonResult executeSelectedDocumentMerge(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> selectedGroups = (List<Map<String, Object>>) request.get("selectedGroups");
            @SuppressWarnings("unchecked")
            List<Integer> billIds = (List<Integer>) request.get("billIds");

            log.info("AI执行选中票据归并，账套ID: {}, 选中组数: {}, 票据数量: {}",
                    this.accountSetsId, selectedGroups.size(), billIds.size());

            Map<String, Object> result = aiMergeService.executeSelectedDocumentMerge(
                    this.accountSetsId, selectedGroups, this.currentUser.getId());
            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("AI执行选中票据归并失败", e);
            return JsonResult.failure("选中归并失败: " + e.getMessage());
        }
    }

    @PostMapping("/receipts/analyze")
    @ApiOperation("AI分析银证归并建议")
    public JsonResult analyzeReceiptMerge(@RequestBody List<Integer> receiptIds) {
        try {
            log.info("AI分析银证归并建议，账套ID: {}, 银证数量: {}", this.accountSetsId, receiptIds.size());
            
            // 获取银证列表
            List<BankReceipts> receipts = aiMergeService.getUnmergedReceiptsForAi(this.accountSetsId, null);
            
            // 如果指定了银证ID，则过滤
            if (receiptIds != null && !receiptIds.isEmpty()) {
                receipts = receipts.stream()
                    .filter(receipt -> receiptIds.contains(receipt.getId()))
                    .collect(java.util.stream.Collectors.toList());
            }
            
            Map<String, Object> result = aiMergeService.analyzeMergeReceiptSuggestions(this.accountSetsId, receipts);
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("AI分析银证归并建议失败", e);
            return JsonResult.failure("AI分析失败: " + e.getMessage());
        }
    }

    @PostMapping("/receipts/execute")
    @ApiOperation("AI执行银证归并")
    public JsonResult executeReceiptMerge(@RequestBody List<Integer> receiptIds) {
        try {
            log.info("AI执行银证归并，账套ID: {}, 银证数量: {}", this.accountSetsId, receiptIds.size());

            // 获取银证列表
            List<BankReceipts> receipts = aiMergeService.getUnmergedReceiptsForAi(this.accountSetsId, null);

            // 如果指定了银证ID，则过滤
            if (receiptIds != null && !receiptIds.isEmpty()) {
                receipts = receipts.stream()
                    .filter(receipt -> receiptIds.contains(receipt.getId()))
                    .collect(java.util.stream.Collectors.toList());
            }

            Map<String, Object> result = aiMergeService.aiMergeReceipts(this.accountSetsId, receipts, this.currentUser.getId());
            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("AI执行银证归并失败", e);
            return JsonResult.failure("AI归并失败: " + e.getMessage());
        }
    }

    @PostMapping("/receipts/execute-selected")
    @ApiOperation("AI执行选中的银证归并")
    public JsonResult executeSelectedReceiptMerge(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> selectedGroups = (List<Map<String, Object>>) request.get("selectedGroups");
            @SuppressWarnings("unchecked")
            List<Integer> receiptIds = (List<Integer>) request.get("receiptIds");

            log.info("AI执行选中银证归并，账套ID: {}, 选中组数: {}, 银证数量: {}",
                    this.accountSetsId, selectedGroups.size(), receiptIds.size());

            Map<String, Object> result = aiMergeService.executeSelectedReceiptMerge(
                    this.accountSetsId, selectedGroups, this.currentUser.getId());
            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("AI执行选中银证归并失败", e);
            return JsonResult.failure("选中归并失败: " + e.getMessage());
        }
    }

    @GetMapping("/documents/unmerged")
    @ApiOperation("获取未归并的票据列表")
    public JsonResult getUnmergedDocuments(@ApiParam("限制数量") @RequestParam(required = false) Integer limit) {
        try {
            log.info("获取未归并的票据列表，账套ID: {}, 限制数量: {}", this.accountSetsId, limit);
            
            List<Bill> bills = aiMergeService.getUnmergedDocumentsForAi(this.accountSetsId, limit);
            return JsonResult.successful(bills);
            
        } catch (Exception e) {
            log.error("获取未归并票据列表失败", e);
            return JsonResult.failure("获取失败: " + e.getMessage());
        }
    }

    @GetMapping("/receipts/unmerged")
    @ApiOperation("获取未归并的银证列表")
    public JsonResult getUnmergedReceipts(@ApiParam("限制数量") @RequestParam(required = false) Integer limit) {
        try {
            log.info("获取未归并的银证列表，账套ID: {}, 限制数量: {}", this.accountSetsId, limit);
            
            List<BankReceipts> receipts = aiMergeService.getUnmergedReceiptsForAi(this.accountSetsId, limit);
            return JsonResult.successful(receipts);
            
        } catch (Exception e) {
            log.error("获取未归并银证列表失败", e);
            return JsonResult.failure("获取失败: " + e.getMessage());
        }
    }

    @PostMapping("/accept-suggestion")
    @ApiOperation("接受单个AI归并建议")
    public JsonResult acceptSuggestion(@RequestBody Map<String, Object> suggestion) {
        try {
            log.info("接受AI归并建议，账套ID: {}, 请求数据: {}", this.accountSetsId, suggestion);

            // 解析建议数据
            String type = (String) suggestion.get("type");
            Object itemsObj = suggestion.get("items");
            String groupName = (String) suggestion.get("groupName");
            String reason = (String) suggestion.get("reasoning");

            // 处理items数据，可能是对象数组或ID数组
            List<Integer> itemIds = new ArrayList<>();
            if (itemsObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> items = (List<Object>) itemsObj;
                for (Object item : items) {
                    if (item instanceof Map) {
                        // 如果是对象，提取id字段
                        @SuppressWarnings("unchecked")
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        Object idObj = itemMap.get("id");
                        if (idObj instanceof Number) {
                            itemIds.add(((Number) idObj).intValue());
                        }
                    } else if (item instanceof Number) {
                        // 如果直接是数字ID
                        itemIds.add(((Number) item).intValue());
                    }
                }
            }

            log.info("解析后的ID列表: {}", itemIds);

            if (itemIds.isEmpty() || itemIds.size() < 2) {
                return JsonResult.failure("归并建议数据无效：至少需要2个有效的项目ID");
            }

            Map<String, Object> result;

            if ("bill".equals(type) || "document".equals(type)) {
                // 执行票据归并
                result = aiMergeService.executeSingleDocumentMerge(this.accountSetsId, itemIds, groupName, reason, this.currentUser.getId());
            } else if ("receipt".equals(type)) {
                // 执行银证归并
                result = aiMergeService.executeSingleReceiptMerge(this.accountSetsId, itemIds, groupName, reason, this.currentUser.getId());
            } else {
                return JsonResult.failure("不支持的归并类型: " + type);
            }

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("接受AI归并建议失败", e);
            return JsonResult.failure("接受归并建议失败: " + e.getMessage());
        }
    }

    @PostMapping("/auto-merge-all")
    @ApiOperation("AI自动归并所有未归并数据")
    public JsonResult autoMergeAll(@ApiParam("限制数量") @RequestParam(required = false, defaultValue = "50") Integer limit) {
        try {
            log.info("AI自动归并所有未归并数据，账套ID: {}, 限制数量: {}", this.accountSetsId, limit);
            
            // 获取未归并的票据和银证
            List<Bill> bills = aiMergeService.getUnmergedDocumentsForAi(this.accountSetsId, limit);
            List<BankReceipts> receipts = aiMergeService.getUnmergedReceiptsForAi(this.accountSetsId, limit);
            
            Map<String, Object> documentResult = null;
            Map<String, Object> receiptResult = null;
            
            // 执行票据归并
            if (!bills.isEmpty()) {
                documentResult = aiMergeService.aiMergeDocuments(this.accountSetsId, bills, this.currentUser.getId());
            }
            
            // 执行银证归并
            if (!receipts.isEmpty()) {
                receiptResult = aiMergeService.aiMergeReceipts(this.accountSetsId, receipts, this.currentUser.getId());
            }
            
            // 汇总结果
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("documentResult", documentResult);
            result.put("receiptResult", receiptResult);
            
            int totalGroups = 0;
            int totalItems = 0;
            
            if (documentResult != null && (Boolean) documentResult.get("success")) {
                totalGroups += (Integer) documentResult.get("groupCount");
                totalItems += (Integer) documentResult.get("totalMergedItems");
            }
            
            if (receiptResult != null && (Boolean) receiptResult.get("success")) {
                totalGroups += (Integer) receiptResult.get("groupCount");
                totalItems += (Integer) receiptResult.get("totalMergedItems");
            }
            
            result.put("success", true);
            result.put("message", String.format("AI自动归并完成，共创建%d个归并组，归并%d个项目", totalGroups, totalItems));
            result.put("totalGroups", totalGroups);
            result.put("totalItems", totalItems);
            
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("AI自动归并失败", e);
            return JsonResult.failure("自动归并失败: " + e.getMessage());
        }
    }
}
