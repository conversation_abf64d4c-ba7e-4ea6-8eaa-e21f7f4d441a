package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.model.dto.RelationCreateDto;
import cn.gson.financial.kernel.model.entity.EntityRelation;
import cn.gson.financial.kernel.service.RelationManagerService;
import cn.gson.financial.kernel.controller.JsonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 关联管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/relations")
@Api(tags = "关联关系管理")
public class RelationManagerController extends BaseController {

    @Autowired
    private RelationManagerService relationManagerService;

    @PostMapping
    @ApiOperation("创建关联关系")
    public JsonResult createRelation(@RequestBody RelationCreateDto createDto) {
        try {
            log.info("创建关联关系，用户: {}, 账套ID: {}", currentUser.getRealName(), accountSetsId);

            String relationId = relationManagerService.createRelation(accountSetsId, createDto, currentUser.getId());
            return JsonResult.successful(relationId);
        } catch (Exception e) {
            log.error("创建关联关系失败", e);
            return JsonResult.failure("创建关联关系失败: " + e.getMessage());
        }
    }

    @PostMapping("/batch")
    @ApiOperation("批量创建关联关系")
    public JsonResult createBatchRelations(
            @RequestBody RelationCreateDto.BatchRelationCreateDto batchCreateDto) {
        try {
            log.info("批量创建关联关系，用户: {}, 账套ID: {}", currentUser.getRealName(), accountSetsId);

            List<String> relationIds = relationManagerService.createBatchRelations(accountSetsId, batchCreateDto, currentUser.getId());
            return JsonResult.successful(relationIds);
        } catch (Exception e) {
            log.error("批量创建关联关系失败", e);
            return JsonResult.failure("批量创建关联关系失败: " + e.getMessage());
        }
    }

    @GetMapping("/documents/{entityId}")
    @ApiOperation("查询票据相关关联")
    public JsonResult queryDocumentRelations(
            @ApiParam("实体ID") @PathVariable String entityId,
            @ApiParam("实体类型") @RequestParam String entityType) {
        try {
            log.info("查询票据相关关联，用户: {}, 账套ID: {}, 实体ID: {}, 类型: {}",
                    currentUser.getRealName(), accountSetsId, entityId, entityType);

            List<EntityRelation> relations = relationManagerService.queryDocumentRelations(accountSetsId, entityId, entityType);
            return JsonResult.successful(relations);
        } catch (Exception e) {
            log.error("查询票据相关关联失败", e);
            return JsonResult.failure("查询票据相关关联失败: " + e.getMessage());
        }
    }

    @GetMapping("/receipts/{entityId}")
    @ApiOperation("查询银证相关关联")
    public JsonResult queryReceiptRelations(
            @ApiParam("实体ID") @PathVariable String entityId,
            @ApiParam("实体类型") @RequestParam String entityType) {
        try {
            log.info("查询银证相关关联，用户: {}, 账套ID: {}, 实体ID: {}, 类型: {}",
                    currentUser.getRealName(), accountSetsId, entityId, entityType);

            List<EntityRelation> relations = relationManagerService.queryReceiptRelations(accountSetsId, entityId, entityType);
            return JsonResult.successful(relations);
        } catch (Exception e) {
            log.error("查询银证相关关联失败", e);
            return JsonResult.failure("查询银证相关关联失败: " + e.getMessage());
        }
    }

    @GetMapping("/cross-type/{entityId}")
    @ApiOperation("查询跨类型关联")
    public JsonResult queryCrossTypeRelations(
            @ApiParam("实体ID") @PathVariable String entityId,
            @ApiParam("实体类型") @RequestParam String entityType) {
        try {
            log.info("查询跨类型关联，用户: {}, 账套ID: {}, 实体ID: {}, 类型: {}",
                    currentUser.getRealName(), accountSetsId, entityId, entityType);

            List<EntityRelation> relations = relationManagerService.queryCrossTypeRelations(accountSetsId, entityId, entityType);
            return JsonResult.successful(relations);
        } catch (Exception e) {
            log.error("查询跨类型关联失败", e);
            return JsonResult.failure("查询跨类型关联失败: " + e.getMessage());
        }
    }

    @GetMapping("/all/{entityId}")
    @ApiOperation("查询所有关联关系")
    public JsonResult queryAllRelations(
            @ApiParam("实体ID") @PathVariable String entityId,
            @ApiParam("实体类型") @RequestParam String entityType) {
        try {
            log.info("查询所有关联关系，用户: {}, 账套ID: {}, 实体ID: {}, 类型: {}",
                    currentUser.getRealName(), accountSetsId, entityId, entityType);

            List<EntityRelation> relations = relationManagerService.queryAllRelations(accountSetsId, entityId, entityType);
            return JsonResult.successful(relations);
        } catch (Exception e) {
            log.error("查询所有关联关系失败", e);
            return JsonResult.failure("查询所有关联关系失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{relationId}")
    @ApiOperation("删除关联关系")
    public JsonResult deleteRelation(@ApiParam("关联ID") @PathVariable String relationId) {
        try {
            log.info("删除关联关系，用户: {}, 账套ID: {}, 关联ID: {}",
                    currentUser.getRealName(), accountSetsId, relationId);

            boolean result = relationManagerService.deleteRelation(accountSetsId, relationId);
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("删除关联关系失败", e);
            return JsonResult.failure("删除关联关系失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/entity/{entityId}")
    @ApiOperation("删除实体的所有关联关系")
    public JsonResult deleteAllRelationsByEntity(
            @ApiParam("实体ID") @PathVariable String entityId,
            @ApiParam("实体类型") @RequestParam String entityType) {
        try {
            log.info("删除实体的所有关联关系，用户: {}, 账套ID: {}, 实体ID: {}, 类型: {}",
                    currentUser.getRealName(), accountSetsId, entityId, entityType);

            int count = relationManagerService.deleteAllRelationsByEntity(accountSetsId, entityId, entityType);
            return JsonResult.successful(count);
        } catch (Exception e) {
            log.error("删除实体的所有关联关系失败", e);
            return JsonResult.failure("删除实体的所有关联关系失败: " + e.getMessage());
        }
    }

    @GetMapping("/validate")
    @ApiOperation("验证关联关系有效性")
    public JsonResult validateRelation(
            @ApiParam("源类型") @RequestParam String sourceType,
            @ApiParam("源ID") @RequestParam String sourceId,
            @ApiParam("目标类型") @RequestParam String targetType,
            @ApiParam("目标ID") @RequestParam String targetId) {
        try {
            log.info("验证关联关系有效性，用户: {}, 账套ID: {}, 源: {}:{}, 目标: {}:{}",
                    currentUser.getRealName(), accountSetsId, sourceType, sourceId, targetType, targetId);

            boolean valid = relationManagerService.validateRelation(accountSetsId, sourceType, sourceId, targetType, targetId);
            return JsonResult.successful(valid);
        } catch (Exception e) {
            log.error("验证关联关系失败", e);
            return JsonResult.failure("验证关联关系失败: " + e.getMessage());
        }
    }
}
