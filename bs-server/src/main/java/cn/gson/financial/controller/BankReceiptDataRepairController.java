package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.service.BankReceiptDataRepairService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 银行回单数据修复控制器
 */
@RestController
@RequestMapping("/bank-receipt-repair")
@Slf4j
public class BankReceiptDataRepairController extends BaseController {

    @Autowired
    private BankReceiptDataRepairService repairService;

    /**
     * 修复当前账套的"未知收款人"数据
     */
    @PostMapping("/repair-unknown-payee")
    public JsonResult repairUnknownPayee() {
        try {
            if (accountSetsId == null) {
                return JsonResult.failure("请先选择账套");
            }
            
            log.info("开始修复账套 {} 的未知收款人数据", accountSetsId);
            int repairedCount = repairService.repairUnknownPayeeData(accountSetsId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("repairedCount", repairedCount);
            result.put("message", "修复完成，成功修复 " + repairedCount + " 条记录");
            
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("修复未知收款人数据失败", e);
            return JsonResult.failure("修复失败: " + e.getMessage());
        }
    }

    /**
     * 修复指定记录的数据
     */
    @PostMapping("/repair-single/{receiptId}")
    public JsonResult repairSingleReceipt(@PathVariable Integer receiptId) {
        try {
            boolean success = repairService.repairSingleReceiptById(receiptId);
            
            if (success) {
                return JsonResult.successful("记录修复成功");
            } else {
                return JsonResult.failure("记录修复失败，可能没有找到可修复的数据");
            }
        } catch (Exception e) {
            log.error("修复单条记录失败，ID: {}", receiptId, e);
            return JsonResult.failure("修复失败: " + e.getMessage());
        }
    }

    /**
     * 批量修复指定记录的数据
     */
    @PostMapping("/repair-batch")
    public JsonResult repairBatch(@RequestBody List<Long> receiptIds) {
        try {
            if (accountSetsId == null) {
                return JsonResult.failure("请先选择账套");
            }

            if (receiptIds == null || receiptIds.isEmpty()) {
                return JsonResult.failure("请提供要修复的记录ID列表");
            }

            log.info("开始批量修复账套 {} 的指定记录，共 {} 条", accountSetsId, receiptIds.size());
            int repairedCount = repairService.repairSpecificRecords(accountSetsId, receiptIds);

            Map<String, Object> result = new HashMap<>();
            result.put("repairedCount", repairedCount);
            result.put("totalRequested", receiptIds.size());
            result.put("message", String.format("批量修复完成，成功修复 %d 条记录（共请求修复 %d 条）",
                repairedCount, receiptIds.size()));

            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("批量修复指定记录失败", e);
            return JsonResult.failure("批量修复失败: " + e.getMessage());
        }
    }

    /**
     * 获取修复状态统计
     */
    @GetMapping("/repair-status")
    public JsonResult getRepairStatus() {
        try {
            if (accountSetsId == null) {
                return JsonResult.failure("请先选择账套");
            }

            // 统计需要修复的记录数量
            int unknownCount = repairService.countUnknownPayeeRecords(accountSetsId);

            Map<String, Object> status = new HashMap<>();
            status.put("accountSetsId", accountSetsId);
            status.put("unknownCount", unknownCount);
            status.put("message", unknownCount > 0 ?
                String.format("发现 %d 条需要修复的记录", unknownCount) :
                "暂无需要修复的记录");

            return JsonResult.successful(status);
        } catch (Exception e) {
            log.error("获取修复状态失败", e);
            return JsonResult.failure("获取状态失败: " + e.getMessage());
        }
    }


}
