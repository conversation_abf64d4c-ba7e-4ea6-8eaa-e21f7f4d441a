package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.model.entity.DocumentGroup;
import cn.gson.financial.kernel.model.entity.ReceiptGroup;
import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.mapper.DocumentGroupMapper;
import cn.gson.financial.kernel.model.mapper.ReceiptGroupMapper;
import cn.gson.financial.kernel.model.mapper.BillMapper;
import cn.gson.financial.kernel.model.mapper.BankReceiptsMapper;
import cn.gson.financial.kernel.controller.JsonResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 归并组管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/merge-groups")
@Api(tags = "归并组管理")
public class MergeGroupController extends BaseController {

    @Autowired
    private DocumentGroupMapper documentGroupMapper;

    @Autowired
    private ReceiptGroupMapper receiptGroupMapper;

    @Autowired
    private BillMapper billMapper;

    @Autowired
    private BankReceiptsMapper bankReceiptsMapper;

    @GetMapping("/documents")
    @ApiOperation("获取票据归并组列表")
    public JsonResult getDocumentGroups() {
        try {
            log.info("获取票据归并组列表，账套ID: {}", this.accountSetsId);
            List<DocumentGroup> groups = documentGroupMapper.findActiveGroupsByAccountSets(this.accountSetsId);
            log.info("查询到票据归并组数量: {}", groups.size());
            return JsonResult.successful(groups);
        } catch (Exception e) {
            log.error("获取票据归并组列表失败", e);
            return JsonResult.failure("获取票据归并组列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/receipts")
    @ApiOperation("获取银证归并组列表")
    public JsonResult getReceiptGroups() {
        try {
            log.info("获取银证归并组列表，账套ID: {}", this.accountSetsId);
            List<ReceiptGroup> groups = receiptGroupMapper.findActiveGroupsByAccountSets(this.accountSetsId);
            log.info("查询到银证归并组数量: {}", groups.size());
            return JsonResult.successful(groups);
        } catch (Exception e) {
            log.error("获取银证归并组列表失败", e);
            return JsonResult.failure("获取银证归并组列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/documents/{groupId}")
    @ApiOperation("获取票据归并组详情")
    public JsonResult getDocumentGroup(
            @ApiParam("组ID") @PathVariable String groupId) {
        try {
            
            DocumentGroup group = documentGroupMapper.selectById(groupId);
            if (group == null || !group.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("票据归并组不存在或不属于当前账套");
            }
            
            // 查询组内票据
            QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("doc_group_id", groupId);
            List<Bill> documents = billMapper.selectList(queryWrapper);
            group.setDocuments(documents);
            
            return JsonResult.successful(group);
        } catch (Exception e) {
            log.error("获取票据归并组详情失败", e);
            return JsonResult.failure("获取票据归并组详情失败: " + e.getMessage());
        }
    }

    @GetMapping("/receipts/{groupId}")
    @ApiOperation("获取银证归并组详情")
    public JsonResult getReceiptGroup(@ApiParam("组ID") @PathVariable String groupId) {
        try {
            ReceiptGroup group = receiptGroupMapper.selectById(groupId);
            if (group == null || !group.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("银证归并组不存在或不属于当前账套");
            }

            // 查询组内银证
            QueryWrapper<BankReceipts> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("receipt_group_id", groupId);
            List<BankReceipts> receipts = bankReceiptsMapper.selectList(queryWrapper);
            group.setReceipts(receipts);

            return JsonResult.successful(group);
        } catch (Exception e) {
            log.error("获取银证归并组详情失败", e);
            return JsonResult.failure("获取银证归并组详情失败: " + e.getMessage());
        }
    }

    @GetMapping("/documents/{groupId}/items")
    @ApiOperation("获取票据归并组内的票据列表")
    public JsonResult getDocumentGroupItems(@ApiParam("组ID") @PathVariable String groupId) {
        try {
            // 验证组存在且属于当前账套
            DocumentGroup group = documentGroupMapper.selectById(groupId);
            if (group == null || !group.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("票据归并组不存在或不属于当前账套");
            }

            QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("doc_group_id", groupId)
                       .eq("account_sets_id", this.accountSetsId);
            List<Bill> documents = billMapper.selectList(queryWrapper);
            
            return JsonResult.successful(documents);
        } catch (Exception e) {
            log.error("获取票据归并组内票据列表失败", e);
            return JsonResult.failure("获取票据归并组内票据列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/receipts/{groupId}/items")
    @ApiOperation("获取银证归并组内的银证列表")
    public JsonResult getReceiptGroupItems(@ApiParam("组ID") @PathVariable String groupId) {
        try {
            // 验证组存在且属于当前账套
            ReceiptGroup group = receiptGroupMapper.selectById(groupId);
            if (group == null || !group.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("银证归并组不存在或不属于当前账套");
            }

            QueryWrapper<BankReceipts> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("receipt_group_id", groupId)
                       .eq("account_sets_id", this.accountSetsId);
            List<BankReceipts> receipts = bankReceiptsMapper.selectList(queryWrapper);
            
            return JsonResult.successful(receipts);
        } catch (Exception e) {
            log.error("获取银证归并组内银证列表失败", e);
            return JsonResult.failure("获取银证归并组内银证列表失败: " + e.getMessage());
        }
    }

    @PutMapping("/documents/{groupId}")
    @ApiOperation("更新票据归并组信息")
    public JsonResult updateDocumentGroup(
            @ApiParam("组ID") @PathVariable String groupId,
            @RequestBody DocumentGroup group) {
        try {
            // 验证组存在且属于当前账套
            DocumentGroup existingGroup = documentGroupMapper.selectById(groupId);
            if (existingGroup == null || !existingGroup.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("票据归并组不存在或不属于当前账套");
            }

            group.setGroupId(groupId);
            group.setAccountSetsId(this.accountSetsId);
            group.setCreatedBy(existingGroup.getCreatedBy());
            group.setCreatedAt(existingGroup.getCreatedAt());
            
            int result = documentGroupMapper.updateById(group);
            return JsonResult.successful(result > 0);
        } catch (Exception e) {
            log.error("更新票据归并组失败", e);
            return JsonResult.failure("更新票据归并组失败: " + e.getMessage());
        }
    }

    @PutMapping("/receipts/{groupId}")
    @ApiOperation("更新银证归并组信息")
    public JsonResult updateReceiptGroup(
            @ApiParam("组ID") @PathVariable String groupId,
            @RequestBody ReceiptGroup group) {
        try {
            // 验证组存在且属于当前账套
            ReceiptGroup existingGroup = receiptGroupMapper.selectById(groupId);
            if (existingGroup == null || !existingGroup.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("银证归并组不存在或不属于当前账套");
            }

            group.setGroupId(groupId);
            group.setAccountSetsId(this.accountSetsId);
            group.setCreatedBy(existingGroup.getCreatedBy());
            group.setCreatedAt(existingGroup.getCreatedAt());
            
            int result = receiptGroupMapper.updateById(group);
            return JsonResult.successful(result > 0);
        } catch (Exception e) {
            log.error("更新银证归并组失败", e);
            return JsonResult.failure("更新银证归并组失败: " + e.getMessage());
        }
    }
}
