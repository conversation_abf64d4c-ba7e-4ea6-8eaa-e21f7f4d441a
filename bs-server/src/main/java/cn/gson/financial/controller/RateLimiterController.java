package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.kernel.service.RateLimiterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 限流器监控控制器
 */
@RestController
@RequestMapping("/rate-limiter")
@Slf4j
public class RateLimiterController extends BaseController {

    @Autowired
    private RateLimiterService rateLimiterService;

    /**
     * 获取腾讯云OCR限流器状态
     */
    @GetMapping("/tencent-ocr/status")
    public JsonResult getTencentOcrStatus() {
        try {
            RateLimiterService.ServiceRateLimiter rateLimiter = rateLimiterService.getTencentOcrRateLimiter();

            Map<String, Object> status = new HashMap<>();
            status.put("serviceName", "TENCENT_OCR");
            status.put("qpsLimit", 8);
            status.put("availablePermits", rateLimiter.getAvailablePermits());
            status.put("currentSecondUsage", rateLimiter.getCurrentSecondUsage());
            status.put("statusMessage", rateLimiter.getStatus());

            return JsonResult.successful(status);
        } catch (Exception e) {
            log.error("获取限流器状态失败", e);
            return JsonResult.failure("获取限流器状态失败: " + e.getMessage());
        }
    }

    /**
     * 测试限流器
     */
    @GetMapping("/tencent-ocr/test")
    public JsonResult testTencentOcrRateLimiter() {
        try {
            RateLimiterService.ServiceRateLimiter rateLimiter = rateLimiterService.getTencentOcrRateLimiter();

            Map<String, Object> result = new HashMap<>();
            result.put("beforeTest", rateLimiter.getStatus());

            // 尝试获取许可
            boolean acquired = rateLimiter.tryAcquire();
            result.put("acquired", acquired);
            result.put("afterTest", rateLimiter.getStatus());

            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("测试限流器失败", e);
            return JsonResult.failure("测试限流器失败: " + e.getMessage());
        }
    }
}
