package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BillAiResult;
import cn.gson.financial.kernel.model.vo.AiMatchingResult;
import cn.gson.financial.kernel.service.AiAccountingService;
import cn.gson.financial.kernel.service.BankReceiptsService;
import cn.gson.financial.kernel.service.BillService;

import cn.gson.financial.kernel.service.BillAiResultService;
import cn.gson.financial.kernel.service.RelationManagerService;
import cn.gson.financial.kernel.model.entity.EntityRelation;
import java.util.ArrayList;
import cn.gson.financial.model.vo.JsonResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI智能会计控制器
 * 提供基于大模型的自动科目匹配和凭证生成功能
 *
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/ai-accounting")
@AllArgsConstructor
public class AiAccountingController extends BaseController {
    
    private final AiAccountingService aiAccountingService;
    private final BankReceiptsService bankReceiptsService;
    private final BillService billService;
    private final BillAiResultService billAiResultService;
    private final RelationManagerService relationManagerService;

    /**
     * 智能匹配银行回单对应的会计科目
     *
     * @param bankReceiptsId 银行回单ID
     * @return 匹配结果
     */
    @PostMapping("/match-subjects/bank-receipts/{bankReceiptsId}")
    public JsonResult matchSubjectsForBankReceipts(@PathVariable Integer bankReceiptsId) {
        try {
            log.info("银行回单科目匹配，用户: {}, 账套ID: {}, 回单ID: {}",
                    currentUser.getRealName(), accountSetsId, bankReceiptsId);

            // 获取银行回单信息
            BankReceipts bankReceipts = bankReceiptsService.getById(bankReceiptsId);
            if (bankReceipts == null) {
                return JsonResult.error("银行回单不存在");
            }

            // 获取关联票据
            List<Bill> relatedBills = getRelatedBillsForReceipt(accountSetsId, bankReceipts.getId());

            // 执行AI匹配
            AiMatchingResult result = aiAccountingService.matchSubjectsForBankReceipts(
                    bankReceipts, relatedBills, accountSetsId);

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("银行回单科目匹配失败", e);
            return JsonResult.error("匹配失败: " + e.getMessage());
        }
    }
    
    /**
     * 智能匹配票据对应的会计科目
     *
     * @param billId 票据ID
     * @return 匹配结果
     */
    @PostMapping("/match-subjects/bill/{billId}")
    public JsonResult matchSubjectsForBill(@PathVariable Integer billId) {
        try {
            log.info("票据科目匹配，用户: {}, 账套ID: {}, 票据ID: {}",
                    currentUser.getRealName(), accountSetsId, billId);

            // 获取票据信息
            Bill bill = billService.getById(billId);
            if (bill == null) {
                return JsonResult.error("票据不存在");
            }

            // 执行AI匹配
            AiMatchingResult result = aiAccountingService.matchSubjectsForBill(bill, accountSetsId);

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("票据科目匹配失败", e);
            return JsonResult.error("匹配失败: " + e.getMessage());
        }
    }
    
    /**
     * 自动生成会计凭证
     *
     * @param bankReceiptsId 银行回单ID
     * @return 生成的凭证ID
     */
    @PostMapping("/generate-voucher/{bankReceiptsId}")
    public JsonResult generateVoucherAutomatically(@PathVariable Integer bankReceiptsId) {
        try {
            log.info("自动生成凭证，用户: {}, 账套ID: {}, 回单ID: {}",
                    currentUser.getRealName(), accountSetsId, bankReceiptsId);

            // 获取银行回单信息
            BankReceipts bankReceipts = bankReceiptsService.getById(bankReceiptsId);
            if (bankReceipts == null) {
                return JsonResult.error("银行回单不存在");
            }

            // 注意：related_voucher_id字段已删除，如需要可以通过entity_relations表检查关联状态
            // 暂时移除重复生成检查，或者通过entity_relations表实现

            // 获取关联票据
            List<Bill> relatedBills = getRelatedBillsForReceipt(accountSetsId, bankReceipts.getId());

            // 自动生成凭证
            Integer voucherId = aiAccountingService.generateVoucherAutomatically(
                    bankReceipts, relatedBills, accountSetsId, currentUser.getId());

            Map<String, Object> result = new HashMap<>();
            result.put("voucherId", voucherId);
            result.put("message", "凭证生成成功");

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("自动生成凭证失败", e);
            return JsonResult.error("生成失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量自动生成凭证
     *
     * @param request 批量请求参数
     * @return 处理结果
     */
    @PostMapping("/batch-generate-vouchers")
    public JsonResult batchGenerateVouchers(@RequestBody BatchGenerateRequest request) {
        try {
            if (request.getBankReceiptsIds() == null || request.getBankReceiptsIds().isEmpty()) {
                return JsonResult.error("请选择要处理的银行回单");
            }

            log.info("批量生成凭证，用户: {}, 账套ID: {}, 回单数量: {}",
                    currentUser.getRealName(), accountSetsId, request.getBankReceiptsIds().size());

            // 批量生成凭证
            Integer successCount = aiAccountingService.batchGenerateVouchers(
                    request.getBankReceiptsIds(), accountSetsId, currentUser.getId());

            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", request.getBankReceiptsIds().size());
            result.put("successCount", successCount);
            result.put("failureCount", request.getBankReceiptsIds().size() - successCount);
            result.put("message", String.format("批量处理完成，成功%d个，失败%d个",
                    successCount, request.getBankReceiptsIds().size() - successCount));

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("批量生成凭证失败", e);
            return JsonResult.error("批量处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新科目AI描述
     *
     * @param request 更新请求
     * @return 更新结果
     */
    @PostMapping("/update-subject-ai-description")
    public JsonResult updateSubjectAiDescription(@RequestBody UpdateSubjectAiRequest request) {
        try {
            // 这个方法不需要验证账套，因为科目更新是全局的
            if (currentUser == null) {
                log.error("用户未登录，无法更新科目AI描述");
                return JsonResult.error("用户未登录，请先登录");
            }

            if (request.getSubjectId() == null) {
                return JsonResult.error("科目ID不能为空");
            }

            log.info("更新科目AI描述，用户: {}, 科目ID: {}",
                    currentUser.getRealName(), request.getSubjectId());

            Boolean success = aiAccountingService.updateSubjectAiDescription(
                    request.getSubjectId(),
                    request.getAiDescription(),
                    request.getKeywords());

            if (success) {
                return JsonResult.successful("更新成功");
            } else {
                return JsonResult.error("更新失败");
            }

        } catch (Exception e) {
            log.error("更新科目AI描述失败", e);
            return JsonResult.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取AI匹配统计信息
     *
     * @param days 统计天数
     * @return 统计结果
     */
    @GetMapping("/matching-statistics")
    public JsonResult getMatchingStatistics(@RequestParam(defaultValue = "30") Integer days) {
        try {
            log.info("获取AI匹配统计，用户: {}, 账套ID: {}, 天数: {}",
                    currentUser.getRealName(), accountSetsId, days);

            List<Object> statistics = aiAccountingService.getMatchingStatistics(accountSetsId, days);

            return JsonResult.successful(statistics);

        } catch (Exception e) {
            log.error("获取匹配统计失败", e);
            return JsonResult.error("获取统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 票据AI识别处理
     *
     * @param request 票据识别请求
     * @return 识别结果
     */
    @PostMapping("/recognize-bill")
    public JsonResult recognizeBill(@RequestBody BillRecognizeRequest request) {
        try {
            log.info("票据AI识别，用户: {}, 账套ID: {}", currentUser.getRealName(), accountSetsId);
            
            Bill bill = null;
            
            // 如果提供了billData，优先使用billData中的信息
            if (request.getBillData() != null) {
                bill = new Bill();
                BillRecognizeRequest.BillData billData = request.getBillData();
                bill.setBillNo(billData.getBillNo());
                bill.setAmount(billData.getAmount());
                bill.setIssuer(billData.getPayerName());
                bill.setRecipient(billData.getPayeeName());
                bill.setType(billData.getBillType());
                bill.setAccountSetsId(accountSetsId);
                log.info("使用提供的billData进行AI识别: {}", billData.getBillNo());
            } else if (request.getBillId() != null) {
                // 如果没有billData，则从数据库获取票据信息
                bill = billService.getById(request.getBillId());
                if (bill == null) {
                    return JsonResult.error("票据不存在");
                }
                log.info("从数据库获取票据信息进行AI识别: billId={}", request.getBillId());
            } else {
                return JsonResult.error("请提供billId或billData参数");
            }

            // 执行AI匹配
            AiMatchingResult result = aiAccountingService.matchSubjectsForBill(bill, accountSetsId);
            
            // 保存AI处理结果到数据库
            if (request.getBillId() != null) {
                try {
                    BillAiResult aiResult = new BillAiResult();
                    aiResult.setBillId(request.getBillId());
                    aiResult.setAiProcessed(true);
                    aiResult.setConfidence(result.getConfidence());
                    aiResult.setAiAnalysis(result.getReason());
                    aiResult.setSuggestedSubjects(result.getSuggestedSummary());
                    aiResult.setDebitSubjects(result.getDebitSubjects() != null ? result.getDebitSubjects().toString() : null);
                    aiResult.setCreditSubjects(result.getCreditSubjects() != null ? result.getCreditSubjects().toString() : null);
                    aiResult.setMatchingReason(result.getReason());
                    aiResult.setAiProcessTime(new Date());
                    
                    billAiResultService.saveOrUpdateByBillId(aiResult);
                    log.info("AI处理结果已保存到数据库，billId: {}", request.getBillId());
                } catch (Exception e) {
                    log.error("保存AI处理结果失败，billId: {}", request.getBillId(), e);
                    // 不影响主流程，继续返回结果
                }
            }
            
            // 构建返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("confidence", result.getConfidence());
            response.put("debitSubjects", result.getDebitSubjects());
            response.put("creditSubjects", result.getCreditSubjects());
            response.put("reason", result.getReason());
            response.put("suggestedSummary", result.getSuggestedSummary());
            response.put("success", result.getSuccess());
            
            return JsonResult.successful(response);
            
        } catch (Exception e) {
            log.error("票据AI识别失败", e);
            return JsonResult.error("AI处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量处理票据
     *
     * @param request 批量处理请求
     * @return 处理结果
     */
    @PostMapping("/batch-process-bills")
    public JsonResult batchProcessBills(@RequestBody BatchProcessBillsRequest request) {
        try {
            if (request.getBillIds() == null || request.getBillIds().isEmpty()) {
                return JsonResult.error("请选择要处理的票据");
            }

            log.info("批量处理票据，用户: {}, 账套ID: {}, 票据数量: {}",
                    currentUser.getRealName(), accountSetsId, request.getBillIds().size());

            int successCount = 0;
            int failureCount = 0;

            for (Integer billId : request.getBillIds()) {
                try {
                    Bill bill = billService.getById(billId);
                    if (bill != null) {
                        AiMatchingResult result = aiAccountingService.matchSubjectsForBill(bill, accountSetsId);
                        if (result.getSuccess()) {
                            successCount++;
                        } else {
                            failureCount++;
                        }
                    } else {
                        failureCount++;
                    }
                } catch (Exception e) {
                    log.error("处理票据{}失败", billId, e);
                    failureCount++;
                }
            }

            Map<String, Object> response = new HashMap<>();
            response.put("successCount", successCount);
            response.put("failureCount", failureCount);
            response.put("totalCount", request.getBillIds().size());

            return JsonResult.successful(response);

        } catch (Exception e) {
            log.error("批量处理票据失败", e);
            return JsonResult.error("批量处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 预览匹配结果（不实际生成凭证）
     *
     * @param bankReceiptsId 银行回单ID
     * @return 预览结果
     */
    @PostMapping("/preview-voucher/{bankReceiptsId}")
    public JsonResult previewVoucher(@PathVariable Integer bankReceiptsId) {
        try {
            log.info("预览凭证，用户: {}, 账套ID: {}, 回单ID: {}",
                    currentUser.getRealName(), accountSetsId, bankReceiptsId);

            // 获取银行回单信息
            BankReceipts bankReceipts = bankReceiptsService.getById(bankReceiptsId);
            if (bankReceipts == null) {
                return JsonResult.error("银行回单不存在");
            }

            // 获取关联票据
            List<Bill> relatedBills = getRelatedBillsForReceipt(accountSetsId, bankReceipts.getId());

            // 执行AI匹配（仅预览，不生成实际凭证）
            AiMatchingResult result = aiAccountingService.matchSubjectsForBankReceipts(
                    bankReceipts, relatedBills, accountSetsId);

            // 构建预览数据
            Map<String, Object> preview = new HashMap<>();
            preview.put("bankReceipts", bankReceipts);
            preview.put("relatedBills", relatedBills);
            preview.put("matchingResult", result);
            preview.put("canGenerate", result.getSuccess() && result.getConfidence() >= 0.7);

            return JsonResult.successful(preview);

        } catch (Exception e) {
            log.error("预览凭证失败", e);
            return JsonResult.error("预览失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量生成请求参数
     */
    public static class BatchGenerateRequest {
        private List<Integer> bankReceiptsIds;

        public List<Integer> getBankReceiptsIds() {
            return bankReceiptsIds;
        }

        public void setBankReceiptsIds(List<Integer> bankReceiptsIds) {
            this.bankReceiptsIds = bankReceiptsIds;
        }
    }
    
    /**
     * 更新科目AI描述请求参数
     */
    public static class UpdateSubjectAiRequest {
        private Integer subjectId;
        private String aiDescription;
        private List<String> keywords;
        
        public Integer getSubjectId() {
            return subjectId;
        }
        
        public void setSubjectId(Integer subjectId) {
            this.subjectId = subjectId;
        }
        
        public String getAiDescription() {
            return aiDescription;
        }
        
        public void setAiDescription(String aiDescription) {
            this.aiDescription = aiDescription;
        }
        
        public List<String> getKeywords() {
            return keywords;
        }
        
        public void setKeywords(List<String> keywords) {
            this.keywords = keywords;
        }
    }
    
    /**
     * 票据识别请求参数
     */
    public static class BillRecognizeRequest {
        private Integer billId;
        private BillData billData;
        
        public Integer getBillId() {
            return billId;
        }
        
        public void setBillId(Integer billId) {
            this.billId = billId;
        }
        
        public BillData getBillData() {
            return billData;
        }
        
        public void setBillData(BillData billData) {
            this.billData = billData;
        }
        
        public static class BillData {
            private String billNo;
            private Double amount;
            private String payerName;
            private String payeeName;
            private String billType;
            
            public String getBillNo() {
                return billNo;
            }
            
            public void setBillNo(String billNo) {
                this.billNo = billNo;
            }
            
            public Double getAmount() {
                return amount;
            }
            
            public void setAmount(Double amount) {
                this.amount = amount;
            }
            
            public String getPayerName() {
                return payerName;
            }
            
            public void setPayerName(String payerName) {
                this.payerName = payerName;
            }
            
            public String getPayeeName() {
                return payeeName;
            }
            
            public void setPayeeName(String payeeName) {
                this.payeeName = payeeName;
            }
            
            public String getBillType() {
                return billType;
            }
            
            public void setBillType(String billType) {
                this.billType = billType;
            }
        }
    }
    
    /**
     * 批量处理票据请求参数
     */
    public static class BatchProcessBillsRequest {
        private List<Integer> billIds;
        
        public List<Integer> getBillIds() {
            return billIds;
        }
        
        public void setBillIds(List<Integer> billIds) {
            this.billIds = billIds;
        }
    }
    
    /**
     * 获取票据AI建议
     *
     * @param billId 票据ID
     * @return AI建议结果
     */
    @GetMapping("/bill-suggestion/{billId}")
    public JsonResult getBillSuggestion(@PathVariable Integer billId) {
        try {
            log.info("获取票据AI建议，用户: {}, 账套ID: {}, 票据ID: {}",
                    currentUser.getRealName(), accountSetsId, billId);

            // 从数据库获取票据信息
            Bill bill = billService.getById(billId);
            if (bill == null) {
                return JsonResult.error("票据不存在");
            }

            // 执行AI匹配
            AiMatchingResult result = aiAccountingService.matchSubjectsForBill(bill, accountSetsId);
            
            // 构建返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("confidence", result.getConfidence());
            response.put("debitSubjects", result.getDebitSubjects());
            response.put("creditSubjects", result.getCreditSubjects());
            response.put("reason", result.getReason());
            response.put("suggestedSummary", result.getSuggestedSummary());
            response.put("success", result.getSuccess());
            
            return JsonResult.successful(response);
            
        } catch (Exception e) {
            log.error("获取票据AI建议失败", e);
            return JsonResult.error("获取AI建议失败: " + e.getMessage());
        }
    }

    /**
     * 获取银行回单关联的票据列表
     *
     * @param accountSetsId 账套ID
     * @param receiptId 银行回单ID
     * @return 关联的票据列表
     */
    private List<Bill> getRelatedBillsForReceipt(Integer accountSetsId, Integer receiptId) {
        try {
            // 查询银行回单相关的关联关系
            List<EntityRelation> relations = relationManagerService.queryReceiptRelations(
                accountSetsId, receiptId.toString(), "RECEIPT");

            List<Bill> relatedBills = new ArrayList<>();
            for (EntityRelation relation : relations) {
                // 如果目标是票据，则获取票据信息
                if ("DOCUMENT".equals(relation.getTargetType())) {
                    Bill bill = billService.getById(Integer.valueOf(relation.getTargetId()));
                    if (bill != null) {
                        relatedBills.add(bill);
                    }
                }
                // 如果源是票据，则获取票据信息
                else if ("DOCUMENT".equals(relation.getSourceType())) {
                    Bill bill = billService.getById(Integer.valueOf(relation.getSourceId()));
                    if (bill != null) {
                        relatedBills.add(bill);
                    }
                }
            }

            return relatedBills;
        } catch (Exception e) {
            log.warn("获取银行回单关联票据失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }
}