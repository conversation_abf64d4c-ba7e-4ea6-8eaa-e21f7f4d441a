package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.kernel.service.AiVoucherService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI智能凭证生成控制器
 */
@Api(tags = "AI智能凭证生成")
@RestController
@RequestMapping("/ai-voucher")
@Slf4j
public class AiVoucherController extends BaseController {

    @Autowired
    private AiVoucherService aiVoucherService;

    @ApiOperation("生成AI凭证")
    @PostMapping("/generate")
    public JsonResult generateVouchers(@RequestBody Map<String, Object> request) {
        try {
            log.info("🚀 收到AI凭证生成请求");
            log.info("请求参数: {}", request);
            log.info("当前用户: {}", this.currentUser != null ? this.currentUser.getRealName() : "未登录");
            log.info("账套ID: {}", this.accountSetsId);
            
            @SuppressWarnings("unchecked")
            List<Object> rawSourceItems = (List<Object>) request.get("sourceItems");

            if (rawSourceItems == null || rawSourceItems.isEmpty()) {
                return JsonResult.failure("数据源不能为空");
            }

            // 转换为Map格式
            List<Map<String, Object>> sourceItems = new ArrayList<>();
            for (Object item : rawSourceItems) {
                if (item instanceof Integer) {
                    // 票据ID，构建基本信息
                    Map<String, Object> itemMap = new HashMap<>();
                    itemMap.put("id", item);
                    itemMap.put("type", "bill");
                    sourceItems.add(itemMap);
                } else if (item instanceof String) {
                    // 组ID，构建基本信息
                    Map<String, Object> itemMap = new HashMap<>();
                    itemMap.put("groupId", item);
                    itemMap.put("type", "group");
                    sourceItems.add(itemMap);
                } else if (item instanceof Map) {
                    // 已经是Map格式
                    @SuppressWarnings("unchecked")
                    Map<String, Object> mapItem = (Map<String, Object>) item;
                    sourceItems.add(mapItem);
                }
            }

            // 获取凭证年月参数
            Integer voucherYear = (Integer) request.get("voucherYear");
            Integer voucherMonth = (Integer) request.get("voucherMonth");

            Map<String, Object> result = aiVoucherService.generateVouchers(
                this.accountSetsId,
                sourceItems,
                this.currentUser.getId(),
                voucherYear,
                voucherMonth
            );
            
            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("AI凭证生成失败", e);
            return JsonResult.failure("AI凭证生成失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取AI凭证生成统计")
    @GetMapping("/statistics")
    public JsonResult getStatistics() {
        try {
            Map<String, Object> statistics = aiVoucherService.getGenerationStatistics(
                this.accountSetsId, 
                this.currentUser.getId()
            );
            
            return JsonResult.successful(statistics);
            
        } catch (Exception e) {
            log.error("获取AI凭证统计失败", e);
            return JsonResult.failure("获取统计数据失败: " + e.getMessage());
        }
    }

    @ApiOperation("确认AI生成的凭证")
    @PostMapping("/confirm")
    public JsonResult confirmVoucher(@RequestBody Map<String, Object> request) {
        try {
            log.info("🔍 收到AI凭证确认请求");
            log.info("请求参数: {}", request);

            @SuppressWarnings("unchecked")
            Map<String, Object> voucherData = (Map<String, Object>) request.get("voucher");

            if (voucherData == null) {
                return JsonResult.failure("凭证数据不能为空");
            }

            boolean success = aiVoucherService.confirmGeneratedVoucher(
                this.accountSetsId,
                voucherData,
                this.currentUser.getId()
            );
            
            if (success) {
                return JsonResult.successful();
            } else {
                return JsonResult.failure("凭证确认失败");
            }

        } catch (Exception e) {
            log.error("确认AI凭证失败", e);
            return JsonResult.failure("确认凭证失败: " + e.getMessage());
        }
    }

    @ApiOperation("批量确认AI生成的凭证")
    @PostMapping("/batch-confirm")
    public JsonResult batchConfirmVouchers(
            @ApiParam("凭证列表") @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> vouchers = (List<Map<String, Object>>) request.get("vouchers");
            
            if (vouchers == null || vouchers.isEmpty()) {
                return JsonResult.failure("凭证列表不能为空");
            }
            
            int confirmedCount = aiVoucherService.batchConfirmVouchers(
                this.accountSetsId,
                vouchers,
                this.currentUser.getId()
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("confirmedCount", confirmedCount);
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("批量确认AI凭证失败", e);
            return JsonResult.failure("批量确认失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取AI凭证生成历史")
    @GetMapping("/history")
    public JsonResult getGenerationHistory(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(defaultValue = "20") Integer pageSize) {
        try {
            Map<String, Object> result = aiVoucherService.getGenerationHistory(
                this.accountSetsId,
                this.currentUser.getId(),
                page,
                pageSize
            );
            
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("获取AI凭证生成历史失败", e);
            return JsonResult.failure("获取历史记录失败: " + e.getMessage());
        }
    }

    @ApiOperation("重新生成凭证")
    @PostMapping("/regenerate")
    public JsonResult regenerateVoucher(
            @ApiParam("原凭证ID") @RequestParam Integer originalVoucherId,
            @ApiParam("生成参数") @RequestBody Map<String, Object> parameters) {
        try {
            Map<String, Object> result = aiVoucherService.regenerateVoucher(
                this.accountSetsId,
                originalVoucherId,
                parameters,
                this.currentUser.getId()
            );
            
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("重新生成凭证失败", e);
            return JsonResult.failure("重新生成失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取AI凭证模板")
    @GetMapping("/templates")
    public JsonResult getVoucherTemplates() {
        try {
            List<Map<String, Object>> templates = aiVoucherService.getVoucherTemplates(this.accountSetsId);
            return JsonResult.successful(templates);
            
        } catch (Exception e) {
            log.error("获取AI凭证模板失败", e);
            return JsonResult.failure("获取模板失败: " + e.getMessage());
        }
    }

    @ApiOperation("保存AI凭证模板")
    @PostMapping("/templates")
    public JsonResult saveVoucherTemplate(@RequestBody Map<String, Object> template) {
        try {
            boolean success = aiVoucherService.saveVoucherTemplate(
                this.accountSetsId,
                template,
                this.currentUser.getId()
            );
            
            if (success) {
                return JsonResult.successful();
            } else {
                return JsonResult.failure("保存模板失败");
            }

        } catch (Exception e) {
            log.error("保存AI凭证模板失败", e);
            return JsonResult.failure("保存模板失败: " + e.getMessage());
        }
    }

    @ApiOperation("分析数据源并预览凭证")
    @PostMapping("/preview")
    public JsonResult previewVoucher(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> sourceItems = (List<Map<String, Object>>) request.get("sourceItems");
            
            if (sourceItems == null || sourceItems.isEmpty()) {
                return JsonResult.failure("数据源不能为空");
            }
            
            Map<String, Object> preview = aiVoucherService.previewVoucher(
                this.accountSetsId,
                sourceItems,
                this.currentUser.getId()
            );
            
            return JsonResult.successful(preview);
            
        } catch (Exception e) {
            log.error("预览AI凭证失败", e);
            return JsonResult.failure("预览失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取AI分析建议")
    @PostMapping("/analyze")
    public JsonResult analyzeSourceData(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> sourceData = (Map<String, Object>) request.get("sourceData");
            
            if (sourceData == null) {
                return JsonResult.failure("源数据不能为空");
            }
            
            Map<String, Object> analysis = aiVoucherService.analyzeSourceData(
                this.accountSetsId,
                sourceData,
                this.currentUser.getId()
            );
            
            return JsonResult.successful(analysis);
            
        } catch (Exception e) {
            log.error("AI数据分析失败", e);
            return JsonResult.failure("数据分析失败: " + e.getMessage());
        }
    }
}
