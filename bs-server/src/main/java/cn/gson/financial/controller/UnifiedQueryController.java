package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.model.dto.UnifiedQueryDto;
import cn.gson.financial.kernel.service.UnifiedQueryService;
import cn.gson.financial.kernel.controller.JsonResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 统一查询控制器
 */
@Slf4j
@RestController
@RequestMapping("/unified-query")
@Api(tags = "统一查询管理")
public class UnifiedQueryController extends BaseController {

    @Autowired
    private UnifiedQueryService unifiedQueryService;

    @PostMapping("/documents")
    @ApiOperation("票据统一查询")
    public JsonResult queryDocuments(@RequestBody UnifiedQueryDto queryDto) {
        try {
            log.info("票据统一查询，用户: {}, 账套ID: {}", currentUser.getRealName(), accountSetsId);

            Page<Map<String, Object>> result = unifiedQueryService.queryDocuments(accountSetsId, queryDto);
            log.info("票据统一查询结果，总数: {}, 当前页数据: {}", result.getTotal(), result.getRecords().size());
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("票据统一查询失败", e);
            return JsonResult.failure("票据统一查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/receipts")
    @ApiOperation("银证统一查询")
    public JsonResult queryReceipts(@RequestBody UnifiedQueryDto queryDto) {
        try {
            log.info("银证统一查询，用户: {}, 账套ID: {}", currentUser.getRealName(), accountSetsId);

            Page<Map<String, Object>> result = unifiedQueryService.queryReceipts(accountSetsId, queryDto);
            log.info("银证统一查询结果，总数: {}, 当前页数据: {}", result.getTotal(), result.getRecords().size());
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("银证统一查询失败", e);
            return JsonResult.failure("银证统一查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/cross-type-relations")
    @ApiOperation("跨类型关联查询")
    public JsonResult queryCrossTypeRelations(@RequestBody UnifiedQueryDto queryDto) {
        try {
            log.info("跨类型关联查询，用户: {}, 账套ID: {}", currentUser.getRealName(), accountSetsId);

            Page<Map<String, Object>> result = unifiedQueryService.queryCrossTypeRelations(accountSetsId, queryDto);
            log.info("跨类型关联查询结果，总数: {}, 当前页数据: {}", result.getTotal(), result.getRecords().size());
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("跨类型关联查询失败", e);
            return JsonResult.failure("跨类型关联查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/relations")
    @ApiOperation("跨类型关联查询（新接口）")
    public JsonResult queryRelations(@RequestBody UnifiedQueryDto queryDto) {
        return queryCrossTypeRelations(queryDto);
    }
}
