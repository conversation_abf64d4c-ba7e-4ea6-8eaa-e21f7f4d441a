package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.model.entity.MergeRule;
import cn.gson.financial.kernel.model.mapper.MergeRuleMapper;
import cn.gson.financial.kernel.controller.JsonResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.List;

/**
 * 归并规则控制器
 */
@Slf4j
@RestController
@RequestMapping("/merge-rules")
@Api(tags = "归并规则管理")
public class MergeRuleController extends BaseController {

    @Autowired
    private MergeRuleMapper mergeRuleMapper;

    @GetMapping
    @ApiOperation("获取归并规则列表")
    public JsonResult getMergeRules(
            @ApiParam("适用实体类型") @RequestParam(required = false) String applicableEntity) {
        try {
            log.info("获取归并规则列表，账套ID: {}, 适用实体: {}", this.accountSetsId, applicableEntity);

            List<MergeRule> rules;
            if (applicableEntity != null && !applicableEntity.isEmpty()) {
                rules = mergeRuleMapper.findActiveRulesByAccountSetsAndEntity(this.accountSetsId, applicableEntity);
            } else {
                rules = mergeRuleMapper.findActiveRulesByAccountSets(this.accountSetsId);
            }

            log.info("查询到归并规则数量: {}", rules.size());
            return JsonResult.successful(rules);
        } catch (Exception e) {
            log.error("获取归并规则列表失败", e);
            return JsonResult.failure("获取归并规则列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/list")
    @ApiOperation("获取归并规则列表（新接口）")
    public JsonResult getRulesList(
            @ApiParam("适用实体类型") @RequestParam(required = false) String applicableEntity) {
        return getMergeRules(applicableEntity);
    }

    @GetMapping("/{ruleId}")
    @ApiOperation("获取归并规则详情")
    public JsonResult getMergeRule(
            @ApiParam("规则ID") @PathVariable String ruleId) {
        try {
            MergeRule rule = mergeRuleMapper.selectById(ruleId);
            if (rule == null || !rule.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("归并规则不存在或不属于当前账套");
            }

            return JsonResult.successful(rule);
        } catch (Exception e) {
            log.error("获取归并规则详情失败", e);
            return JsonResult.failure("获取归并规则详情失败: " + e.getMessage());
        }
    }

    @PostMapping
    @ApiOperation("创建归并规则")
    public JsonResult createMergeRule(@RequestBody MergeRule mergeRule) {
        try {
            mergeRule.setAccountSetsId(this.accountSetsId);
            mergeRule.setCreatedBy(this.currentUser.getId());

            mergeRuleMapper.insert(mergeRule);

            return JsonResult.successful(mergeRule.getRuleId());
        } catch (Exception e) {
            log.error("创建归并规则失败", e);
            return JsonResult.failure("创建归并规则失败: " + e.getMessage());
        }
    }

    @PostMapping("/create")
    @ApiOperation("创建归并规则（新接口）")
    public JsonResult createRule(@RequestBody MergeRule mergeRule) {
        return createMergeRule(mergeRule);
    }

    @PostMapping("/update")
    @ApiOperation("更新归并规则（新接口）")
    public JsonResult updateRule(@RequestBody MergeRule mergeRule) {
        return updateMergeRule(mergeRule.getRuleId(), mergeRule);
    }

    @PutMapping("/{ruleId}")
    @ApiOperation("更新归并规则")
    public JsonResult updateMergeRule(
            @ApiParam("规则ID") @PathVariable String ruleId,
            @RequestBody MergeRule mergeRule) {
        try {
            // 验证规则存在且属于当前账套
            MergeRule existingRule = mergeRuleMapper.selectById(ruleId);
            if (existingRule == null || !existingRule.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("归并规则不存在或不属于当前账套");
            }

            mergeRule.setRuleId(ruleId);
            mergeRule.setAccountSetsId(this.accountSetsId);
            mergeRule.setCreatedBy(existingRule.getCreatedBy());
            mergeRule.setCreatedAt(existingRule.getCreatedAt());

            int result = mergeRuleMapper.updateById(mergeRule);
            return JsonResult.successful(result > 0);
        } catch (Exception e) {
            log.error("更新归并规则失败", e);
            return JsonResult.failure("更新归并规则失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{ruleId}")
    @ApiOperation("删除归并规则")
    public JsonResult deleteMergeRule(@ApiParam("规则ID") @PathVariable String ruleId) {
        try {
            // 验证规则存在且属于当前账套
            MergeRule existingRule = mergeRuleMapper.selectById(ruleId);
            if (existingRule == null || !existingRule.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("归并规则不存在或不属于当前账套");
            }

            int result = mergeRuleMapper.deleteById(ruleId);
            return JsonResult.successful(result > 0);
        } catch (Exception e) {
            log.error("删除归并规则失败", e);
            return JsonResult.failure("删除归并规则失败: " + e.getMessage());
        }
    }

    @PutMapping("/{ruleId}/toggle")
    @ApiOperation("启用/禁用归并规则")
    public JsonResult toggleMergeRule(
            @ApiParam("规则ID") @PathVariable String ruleId,
            @ApiParam("是否启用") @RequestParam Boolean isActive) {
        try {
            // 验证规则存在且属于当前账套
            MergeRule existingRule = mergeRuleMapper.selectById(ruleId);
            if (existingRule == null || !existingRule.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("归并规则不存在或不属于当前账套");
            }

            existingRule.setIsActive(isActive);
            int result = mergeRuleMapper.updateById(existingRule);

            return JsonResult.successful(result > 0);
        } catch (Exception e) {
            log.error("切换归并规则状态失败", e);
            return JsonResult.failure("切换归并规则状态失败: " + e.getMessage());
        }
    }

    @PostMapping("/{ruleId}/toggle-status")
    @ApiOperation("切换规则状态（新接口）")
    public JsonResult toggleRuleStatus(@ApiParam("规则ID") @PathVariable String ruleId) {
        try {
            // 验证规则存在且属于当前账套
            MergeRule existingRule = mergeRuleMapper.selectById(ruleId);
            if (existingRule == null || !existingRule.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("归并规则不存在或不属于当前账套");
            }

            // 切换状态
            existingRule.setIsActive(!existingRule.getIsActive());
            int result = mergeRuleMapper.updateById(existingRule);

            return JsonResult.successful(result > 0);
        } catch (Exception e) {
            log.error("切换归并规则状态失败", e);
            return JsonResult.failure("切换归并规则状态失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{ruleId}/delete")
    @ApiOperation("删除归并规则（新接口）")
    public JsonResult deleteRule(@ApiParam("规则ID") @PathVariable String ruleId) {
        return deleteMergeRule(ruleId);
    }
}
