package cn.gson.financial.controller;

import cn.gson.financial.base.BaseCrudController;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.service.BankReceiptsService;

// import cn.gson.financial.kernel.model.entity.Bill; // 已移除，关联功能已移至关联管理模块
// import cn.gson.financial.kernel.service.BillService; // 已移除，关联功能已移至关联管理模块

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired; // 已移除，关联功能已移至关联管理模块
import org.springframework.web.bind.annotation.*;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/bank-receipts")
public class BankReceiptsController extends BaseCrudController<BankReceiptsService, BankReceipts> {

    // @Autowired
    // private BillService billService; // 已移除，关联功能已移至关联管理模块
    


    @GetMapping("receipts-no")
    public JsonResult generateReceiptsNo(@RequestParam(required = false) Integer year,
                                       @RequestParam(required = false) Integer month) {
        // 构造日期
        Date receiptsDate = null;
        if (year != null && month != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(year, month - 1, 1); // month-1 因为Calendar月份从0开始
            receiptsDate = calendar.getTime();
        }
        
        String receiptsNo = this.service.generateReceiptsNo(this.accountSetsId, receiptsDate);
        return JsonResult.successful(receiptsNo);
    }

    @GetMapping("/list")
    public JsonResult list(Integer receipts_year, Integer receipts_month, Integer page, Integer size) {
        LambdaQueryWrapper<BankReceipts> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BankReceipts::getAccountSetsId, this.accountSetsId);

        if (receipts_year != null) {
            queryWrapper.eq(BankReceipts::getReceiptsYear, receipts_year);
        }

        if (receipts_month != null) {
            queryWrapper.eq(BankReceipts::getReceiptsMonth, receipts_month);
        }

        // 优先按转账日期倒序，如果转账日期为空则按单据日期倒序
        queryWrapper.orderByDesc(BankReceipts::getTransferDate)
                   .orderByDesc(BankReceipts::getReceiptsDate);

        if (page != null && page > 0) {
            // 设置默认分页大小为10，支持10、20、50
            int pageSize = (size != null && (size == 10 || size == 20 || size == 50)) ? size : 10;
            IPage<BankReceipts> pageResult = this.service.page(new Page<>(page, pageSize), queryWrapper);
            return JsonResult.successful(pageResult);
        } else {
            List<BankReceipts> list = this.service.list(queryWrapper);
            return JsonResult.successful(list);
        }
    }

    @Override
    @PostMapping
    public JsonResult save(@RequestBody BankReceipts entity) {
        try {
            // 设置创建时间
            if (entity.getCreateDate() == null) {
                entity.setCreateDate(new Date());
            }

            // 设置创建人
            if (entity.getCreateMember() == null) {
                entity.setCreateMember(this.currentUser.getId());
            }

            // 调用service的自定义save方法，会自动设置年月等字段
            BankReceipts result = this.service.save(this.accountSetsId, entity, this.currentUser);
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("保存银行回单失败", e);
            return JsonResult.failure("保存失败: " + e.getMessage());
        }
    }

    @Override
    @PutMapping
    public JsonResult update(@RequestBody BankReceipts entity) {
        try {
            // 调用service的自定义update方法，会自动设置年月等字段
            BankReceipts result = this.service.update(this.accountSetsId, entity);
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("更新银行回单失败", e);
            return JsonResult.failure("更新失败: " + e.getMessage());
        }
    }

    @Override
    @DeleteMapping("/{id:\\d+}")
    public JsonResult delete(@PathVariable Long id) {
        try {
            this.service.delete(this.accountSetsId, id.intValue());
            return JsonResult.successful();
        } catch (Exception e) {
            log.error("删除银行回单失败", e);
            return JsonResult.failure("删除失败: " + e.getMessage());
        }
    }

    @PostMapping("batchDelete")
    public JsonResult batchDelete(@RequestBody Integer[] checked) {
        try {
            if (checked != null && checked.length > 0) {
                for (Integer id : checked) {
                    this.service.delete(this.accountSetsId, id);
                }
            }
            return JsonResult.successful();
        } catch (Exception e) {
            log.error("批量删除银行回单失败", e);
            return JsonResult.failure("批量删除失败: " + e.getMessage());
        }
    }

    // getRecommendedBills 接口已移除，关联功能已移至关联管理模块

    // getUnrelatedBills 接口已移除，关联功能已移至关联管理模块
}
