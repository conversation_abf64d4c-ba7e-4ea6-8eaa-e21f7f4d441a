package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.model.entity.FieldMappingTemplate;
import cn.gson.financial.kernel.model.mapper.FieldMappingTemplateMapper;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.service.FieldMappingTemplateService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 字段映射模板管理控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */
@RestController
@RequestMapping("/field-mapping-template")
@Slf4j
public class FieldMappingTemplateController extends BaseController {

    @Autowired
    private FieldMappingTemplateMapper templateMapper;

    @Autowired
    private FieldMappingTemplateService templateService;

    @Autowired
    private cn.gson.financial.kernel.service.CacheService cacheService;

    /**
     * 分页查询字段映射模板
     */
    @GetMapping("/page")
    public JsonResult getTemplatePage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam Integer accountSetsId,
            @RequestParam(required = false) String documentType,
            @RequestParam(required = false) String bankIdentifier) {
        
        try {
            Page<FieldMappingTemplate> page = new Page<>(current, size);
            
            LambdaQueryWrapper<FieldMappingTemplate> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FieldMappingTemplate::getAccountSetsId, accountSetsId)
                   .eq(FieldMappingTemplate::getIsActive, true);
            
            if (documentType != null && !documentType.trim().isEmpty()) {
                wrapper.eq(FieldMappingTemplate::getDocumentType, documentType);
            }
            
            if (bankIdentifier != null && !bankIdentifier.trim().isEmpty()) {
                wrapper.like(FieldMappingTemplate::getBankIdentifier, bankIdentifier);
            }
            
            wrapper.orderByDesc(FieldMappingTemplate::getUsageCount)
                   .orderByDesc(FieldMappingTemplate::getSuccessRate)
                   .orderByDesc(FieldMappingTemplate::getCreatedTime);
            
            templateMapper.selectPage(page, wrapper);

            return JsonResult.successful(page);

        } catch (Exception e) {
            log.error("查询字段映射模板失败", e);
            return JsonResult.failure("查询字段映射模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取模版列表（支持分页和搜索）
     */
    @GetMapping("/list")
    public JsonResult getList(@RequestParam(required = false) String templateName,
                             @RequestParam(required = false) String documentType,
                             @RequestParam(required = false) String bankIdentifier,
                             @RequestParam(required = false) Boolean isActive,
                             @RequestParam(defaultValue = "1") Integer page,
                             @RequestParam(defaultValue = "20") Integer size) {
        try {
            Page<FieldMappingTemplate> pageObj = new Page<>(page, size);

            LambdaQueryWrapper<FieldMappingTemplate> wrapper = new LambdaQueryWrapper<>();
            // 单据模版是系统级别功能，不限制账套

            if (templateName != null && !templateName.trim().isEmpty()) {
                wrapper.like(FieldMappingTemplate::getTemplateName, templateName.trim());
            }
            if (documentType != null && !documentType.trim().isEmpty()) {
                wrapper.eq(FieldMappingTemplate::getDocumentType, documentType);
            }
            if (bankIdentifier != null && !bankIdentifier.trim().isEmpty()) {
                wrapper.like(FieldMappingTemplate::getBankIdentifier, bankIdentifier.trim());
            }
            if (isActive != null) {
                wrapper.eq(FieldMappingTemplate::getIsActive, isActive);
            }

            wrapper.orderByDesc(FieldMappingTemplate::getCreatedTime);

            templateMapper.selectPage(pageObj, wrapper);

            return JsonResult.successful(pageObj);

        } catch (Exception e) {
            log.error("获取模版列表失败", e);
            return JsonResult.failure("获取模版列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板详情
     */
    @GetMapping("/{id}")
    public JsonResult getTemplateDetail(@PathVariable Integer id) {
        try {
            FieldMappingTemplate template = templateMapper.selectById(id);
            if (template == null) {
                return JsonResult.failure("模板不存在");
            }
            return JsonResult.successful(template);
        } catch (Exception e) {
            log.error("获取模板详情失败", e);
            return JsonResult.failure("获取模板详情失败: " + e.getMessage());
        }
    }

    /**
     * 启用模板
     */
    @PostMapping("/{id}/enable")
    public JsonResult enableTemplate(@PathVariable Integer id) {
        try {
            int result = templateMapper.enableTemplate(id);
            if (result > 0) {
                return JsonResult.successful("模板已启用");
            } else {
                return JsonResult.failure("启用模板失败");
            }
        } catch (Exception e) {
            log.error("启用模板失败", e);
            return JsonResult.failure("启用模板失败: " + e.getMessage());
        }
    }

    /**
     * 禁用模板
     */
    @PostMapping("/{id}/disable")
    public JsonResult disableTemplate(@PathVariable Integer id) {
        try {
            int result = templateMapper.disableTemplate(id);
            if (result > 0) {
                return JsonResult.successful("模板已禁用");
            } else {
                return JsonResult.failure("禁用模板失败");
            }
        } catch (Exception e) {
            log.error("禁用模板失败", e);
            return JsonResult.failure("禁用模板失败: " + e.getMessage());
        }
    }

    /**
     * 删除模板
     */
    @DeleteMapping("/{id}")
    public JsonResult deleteTemplate(@PathVariable Integer id) {
        try {
            int result = templateMapper.deleteById(id);
            if (result > 0) {
                return JsonResult.successful("模板已删除");
            } else {
                return JsonResult.failure("删除模板失败");
            }
        } catch (Exception e) {
            log.error("删除模板失败", e);
            return JsonResult.failure("删除模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板统计信息
     */
    @GetMapping("/statistics")
    public JsonResult getTemplateStatistics(@RequestParam Integer accountSetsId) {
        try {
            LambdaQueryWrapper<FieldMappingTemplate> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FieldMappingTemplate::getAccountSetsId, accountSetsId)
                   .eq(FieldMappingTemplate::getIsActive, true);
            
            List<FieldMappingTemplate> templates = templateMapper.selectList(wrapper);
            
            // 统计信息
            long totalTemplatesCount = templates.size();
            long bankReceiptTemplatesCount = templates.stream()
                    .filter(t -> "BANK_RECEIPT".equals(t.getDocumentType()))
                    .count();
            long invoiceTemplatesCount = templates.stream()
                    .filter(t -> "INVOICE".equals(t.getDocumentType()))
                    .count();

            int totalUsageCount = templates.stream()
                    .mapToInt(t -> t.getUsageCount() != null ? t.getUsageCount() : 0)
                    .sum();

            double avgSuccessRateValue = templates.stream()
                    .filter(t -> t.getSuccessRate() != null)
                    .mapToDouble(t -> t.getSuccessRate().doubleValue())
                    .average()
                    .orElse(0.0);

            return JsonResult.successful(new Object() {
                public final long totalTemplates = totalTemplatesCount;
                public final long bankReceiptTemplates = bankReceiptTemplatesCount;
                public final long invoiceTemplates = invoiceTemplatesCount;
                public final int totalUsage = totalUsageCount;
                public final double avgSuccessRate = Math.round(avgSuccessRateValue * 100.0) / 100.0;
            });

        } catch (Exception e) {
            log.error("获取模板统计信息失败", e);
            return JsonResult.failure("获取模板统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 创建模版
     * @param template 模版数据
     * @return 创建结果
     */
    @PostMapping
    public JsonResult create(@RequestBody FieldMappingTemplate template) {
        try {
            template.setAccountSetsId(this.accountSetsId);
            template.setCreateUser(this.currentUser.getId());
            template.setCreatedTime(java.time.LocalDateTime.now());
            template.setUpdatedTime(java.time.LocalDateTime.now());

            int result = templateMapper.insert(template);
            if (result > 0) {
                return JsonResult.successful(template);
            } else {
                return JsonResult.failure("创建模版失败");
            }
        } catch (Exception e) {
            log.error("创建模版失败", e);
            return JsonResult.failure("创建模版失败: " + e.getMessage());
        }
    }

    /**
     * 更新模版
     * @param id 模版ID
     * @param template 模版数据
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public JsonResult update(@PathVariable Integer id, @RequestBody FieldMappingTemplate template) {
        try {
            FieldMappingTemplate existingTemplate = templateMapper.selectById(id);
            if (existingTemplate == null || !existingTemplate.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("模版不存在");
            }

            template.setId(id);
            template.setAccountSetsId(this.accountSetsId);
            template.setUpdatedTime(java.time.LocalDateTime.now());

            int result = templateMapper.updateById(template);
            if (result > 0) {
                return JsonResult.successful(template);
            } else {
                return JsonResult.failure("更新模版失败");
            }
        } catch (Exception e) {
            log.error("更新模版失败", e);
            return JsonResult.failure("更新模版失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除模版
     * @param request 删除请求
     * @return 删除结果
     */
    @PostMapping("/batch-delete")
    public JsonResult batchDelete(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> ids = (List<Integer>) request.get("ids");
            if (ids == null || ids.isEmpty()) {
                return JsonResult.failure("请选择要删除的模版");
            }

            // 验证权限
            LambdaQueryWrapper<FieldMappingTemplate> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(FieldMappingTemplate::getId, ids);
            queryWrapper.eq(FieldMappingTemplate::getAccountSetsId, this.accountSetsId);

            List<FieldMappingTemplate> templates = templateMapper.selectList(queryWrapper);
            if (templates.size() != ids.size()) {
                return JsonResult.failure("部分模版不存在或无权限删除");
            }

            int result = templateMapper.deleteBatchIds(ids);
            if (result > 0) {
                return JsonResult.successful();
            } else {
                return JsonResult.failure("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除模版失败", e);
            return JsonResult.failure("批量删除模版失败: " + e.getMessage());
        }
    }

    /**
     * 切换模版状态
     * @param id 模版ID
     * @return 切换结果
     */
    @PutMapping("/{id}/toggle-status")
    public JsonResult toggleStatus(@PathVariable Integer id) {
        try {
            FieldMappingTemplate template = templateMapper.selectById(id);
            if (template == null || !template.getAccountSetsId().equals(this.accountSetsId)) {
                return JsonResult.failure("模版不存在");
            }

            template.setIsActive(!template.getIsActive());
            template.setUpdatedTime(java.time.LocalDateTime.now());
            int result = templateMapper.updateById(template);

            if (result > 0) {
                return JsonResult.successful();
            } else {
                return JsonResult.failure("状态切换失败");
            }
        } catch (Exception e) {
            log.error("切换模版状态失败", e);
            return JsonResult.failure("切换模版状态失败: " + e.getMessage());
        }
    }

    /**
     * 清除字段映射模板缓存
     * @param documentType 文档类型 (可选，如果不提供则清除所有类型)
     * @return 清除结果
     */
    @PostMapping("/clear-cache")
    public JsonResult clearCache(@RequestParam(required = false) String documentType) {
        try {
            if (documentType != null) {
                // 清除指定类型的缓存
                cacheService.clearFieldMappingTemplateCache(this.accountSetsId, documentType);
                log.info("清除账套 {} 的 {} 类型字段映射模板缓存", this.accountSetsId, documentType);
                return JsonResult.successful("已清除 " + documentType + " 类型的模板缓存");
            } else {
                // 清除所有类型的缓存
                cacheService.clearFieldMappingTemplateCache(this.accountSetsId, "INVOICE");
                cacheService.clearFieldMappingTemplateCache(this.accountSetsId, "BANK_RECEIPT");
                log.info("清除账套 {} 的所有字段映射模板缓存", this.accountSetsId);
                return JsonResult.successful("已清除所有模板缓存");
            }
        } catch (Exception e) {
            log.error("清除模板缓存失败", e);
            return JsonResult.failure("清除模板缓存失败: " + e.getMessage());
        }
    }
}
