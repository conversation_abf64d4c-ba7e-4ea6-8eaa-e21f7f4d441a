package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.kernel.service.EntityRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 实体关联控制器
 * 支持复杂的关联模式：
 * 1. 一张对一张 - 单个银行回单 ↔ 单个票据
 * 2. 一张对一组 - 单个银行回单 ↔ 票据组
 * 3. 一组对多组 - 银行回单组 ↔ 多个票据组
 * 4. 多组对一组 - 多个银行回单组 ↔ 单个票据组
 * 5. 多组对一张 - 多个银行回单组 ↔ 单个票据
 */
@Api(tags = "实体关联管理")
@RestController
@RequestMapping("/entity-relations")
@Slf4j
public class EntityRelationController extends BaseController {

    @Autowired
    private EntityRelationService entityRelationService;

    @PostMapping("/create-complex")
    @ApiOperation("创建复杂关联关系")
    public JsonResult createComplexRelation(@RequestBody Map<String, Object> relationData) {
        try {
            log.info("创建复杂关联关系，账套ID: {}, 数据: {}", this.accountSetsId, relationData);
            
            Map<String, Object> result = entityRelationService.createComplexRelation(
                this.accountSetsId, relationData, this.currentUser.getId());
            
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("创建复杂关联关系失败", e);
            return JsonResult.failure("创建关联失败: " + e.getMessage());
        }
    }

    @GetMapping("/list")
    @ApiOperation("获取关联关系列表")
    public JsonResult getRelations(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Map<String, Object> result = entityRelationService.getRelations(
                this.accountSetsId, page, size);
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("获取关联关系列表失败", e);
            return JsonResult.failure("获取关联列表失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{relationId}")
    @ApiOperation("删除关联关系")
    public JsonResult deleteRelation(@PathVariable String relationId) {
        try {
            entityRelationService.deleteRelation(relationId, this.accountSetsId);
            return JsonResult.successful("关联关系删除成功");
        } catch (Exception e) {
            log.error("删除关联关系失败", e);
            return JsonResult.failure("删除关联失败: " + e.getMessage());
        }
    }
}
