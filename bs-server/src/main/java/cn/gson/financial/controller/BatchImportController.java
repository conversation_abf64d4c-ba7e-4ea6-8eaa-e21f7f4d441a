package cn.gson.financial.controller;

import lombok.extern.slf4j.Slf4j;
import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.kernel.model.entity.BatchImportDetail;
import cn.gson.financial.kernel.model.entity.BatchImportTask;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.mapper.BatchImportDetailMapper;
import cn.gson.financial.kernel.model.mapper.BatchImportTaskMapper;
import cn.gson.financial.kernel.service.BankReceiptsService;
import cn.gson.financial.kernel.service.BillService;
import cn.gson.financial.kernel.service.AiService;
import cn.gson.financial.service.BatchOcrService;
import cn.gson.financial.service.PdfProcessService;
import cn.gson.financial.service.AsyncBatchProcessService;
import cn.gson.financial.service.OcrService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.io.IOException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 批量导入控制器
 * 处理批量上传、识别和导入功能
 */
@RestController
@RequestMapping("/batch")
@Slf4j
public class BatchImportController extends BaseController {
    
    @Resource
    private PdfProcessService pdfProcessService;
    
    @Resource
    private BatchOcrService batchOcrService;
    
    @Resource
    private BatchImportTaskMapper batchImportTaskMapper;
    
    @Resource
    private BatchImportDetailMapper batchImportDetailMapper;

    @Resource
    private BankReceiptsService bankReceiptsService;

    @Resource
    private BillService billService;

    @Autowired(required = false)
    private AiService aiService;

    @Resource
    private AsyncBatchProcessService asyncBatchProcessService;

    @Resource
    private OcrService ocrService;
    
    /**
     * 批量上传文件
     * @param files 文件数组
     * @param type 导入类型：BANK_RECEIPT-银行回单，INVOICE-发票
     * @param taskName 任务名称
     * @return 上传结果
     */
    @PostMapping("/upload")
    public JsonResult batchUpload(@RequestParam("files") MultipartFile[] files,
                                 @RequestParam("type") String type,
                                 @RequestParam(value = "taskName", required = false) String taskName,
                                 @RequestParam(value = "receiptsPerPage", required = false) Integer receiptsPerPage) {
        try {
            log.info("开始批量上传，文件数量: {}, 类型: {}", files.length, type);
            
            // 验证参数
            if (files == null || files.length == 0) {
                return JsonResult.failure("请选择要上传的文件");
            }
            
            if (!BatchImportTask.TYPE_BANK_RECEIPT.equals(type) && !BatchImportTask.TYPE_INVOICE.equals(type)) {
                return JsonResult.failure("不支持的导入类型");
            }
            
            // 创建任务
            String taskId = UUID.randomUUID().toString();
            if (taskName == null || taskName.trim().isEmpty()) {
                taskName = type.equals(BatchImportTask.TYPE_BANK_RECEIPT) ? "银行回单批量导入" : "发票批量导入";
                taskName += "_" + System.currentTimeMillis();
            }
            
            BatchImportTask task = new BatchImportTask();
            task.setTaskId(taskId);
            task.setTaskName(taskName);
            task.setImportType(type);
            task.setTotalFiles(files.length);
            task.setTotalImages(0); // 稍后更新
            task.setProcessedFiles(0);
            task.setProcessedImages(0);
            task.setSuccessCount(0);
            task.setFailedCount(0);
            task.setStatus(BatchImportTask.STATUS_UPLOADING);
            task.setProgressPercentage(BigDecimal.ZERO);
            task.setStartTime(LocalDateTime.now());
            task.setCreatedTime(LocalDateTime.now());
            task.setAccountSetsId(this.accountSetsId);
            task.setCreateUser(this.currentUser.getId());
            
            batchImportTaskMapper.insert(task);
            
            // 先将文件保存到临时位置，避免异步处理时MultipartFile失效
            List<File> tempFiles = new ArrayList<>();
            try {
                for (MultipartFile file : files) {
                    // 创建临时文件
                    String originalFilename = file.getOriginalFilename();
                    if (originalFilename == null || originalFilename.trim().isEmpty()) {
                        throw new RuntimeException("文件名不能为空");
                    }

                    String extension = "";
                    int lastDotIndex = originalFilename.lastIndexOf(".");
                    if (lastDotIndex > 0) {
                        extension = originalFilename.substring(lastDotIndex);
                    }

                    File tempFile = File.createTempFile("batch_upload_", extension);
                    file.transferTo(tempFile);
                    tempFiles.add(tempFile);
                }

                // 异步处理文件上传和识别
                // 如果没有指定单页回单数，使用智能识别（默认为null，让程序自动判断）
                Integer finalReceiptsPerPage = receiptsPerPage != null ? receiptsPerPage : null;
                asyncBatchProcessService.processUploadedFiles(taskId, tempFiles, type, finalReceiptsPerPage, String.valueOf(this.accountSetsId), this.currentUser.getId());
            } catch (IOException e) {
                log.error("保存临时文件失败", e);
                // 清理已创建的临时文件
                for (File tempFile : tempFiles) {
                    if (tempFile.exists()) {
                        tempFile.delete();
                    }
                }
                return JsonResult.failure("文件处理失败: " + e.getMessage());
            }

            // 立即返回任务信息，不等待处理完成
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("taskName", taskName);
            result.put("totalFiles", files.length);
            result.put("status", BatchImportTask.STATUS_UPLOADING);
            result.put("message", "文件上传成功，正在后台处理中，请稍后查看进度");

            log.info("批量上传任务已创建，任务ID: {}, 总文件数: {}", taskId, files.length);

            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("批量上传失败", e);
            return JsonResult.failure("批量上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务进度
     * @param taskId 任务ID
     * @return 任务进度信息
     */
    @GetMapping("/progress/{taskId}")
    public JsonResult getTaskProgress(@PathVariable("taskId") String taskId) {
        try {
            // 获取任务信息
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, taskId)
                    .eq(BatchImportTask::getAccountSetsId, this.accountSetsId)
            );

            if (task == null) {
                return JsonResult.failure("任务不存在");
            }

            // 获取明细统计信息
            List<BatchImportDetail> details = batchImportDetailMapper.selectByTaskId(taskId);

            // 根据明细实际状态计算统计信息
            long pendingCount = details.stream().filter(d -> BatchImportDetail.STATUS_PENDING.equals(d.getStatus())).count();
            long processingCount = details.stream().filter(d -> BatchImportDetail.STATUS_PROCESSING.equals(d.getStatus())).count();
            long completedCount = details.stream().filter(d -> "SUCCESS".equals(d.getStatus())).count(); // 修复：使用SUCCESS而不是COMPLETED
            long failedCount = details.stream().filter(d -> BatchImportDetail.STATUS_FAILED.equals(d.getStatus())).count();

            // 获取失败记录的详细信息
            List<Map<String, Object>> failedDetails = details.stream()
                .filter(d -> BatchImportDetail.STATUS_FAILED.equals(d.getStatus()))
                .map(d -> {
                    Map<String, Object> failedItem = new HashMap<>();
                    failedItem.put("id", d.getId());
                    failedItem.put("fileName", d.getFileName());
                    failedItem.put("imageUrl", d.getImageUrl());
                    failedItem.put("errorMessage", d.getErrorMessage());
                    failedItem.put("pageNumber", d.getPageNumber());
                    return failedItem;
                })
                .collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("taskName", task.getTaskName());
            result.put("importType", task.getImportType());
            result.put("status", task.getStatus());
            result.put("statusMessage", getStatusMessage(task.getStatus()));
            result.put("totalFiles", task.getTotalFiles() != null ? task.getTotalFiles() : 0);
            result.put("totalImages", task.getTotalImages() != null ? task.getTotalImages() : details.size());
            result.put("processedFiles", task.getProcessedFiles() != null ? task.getProcessedFiles() : 0);
            result.put("processedImages", task.getProcessedImages() != null ? task.getProcessedImages() : (int)(completedCount + failedCount));
            result.put("successCount", (int)completedCount); // 使用实际统计的成功数量
            result.put("failedCount", (int)failedCount); // 使用实际统计的失败数量
            result.put("totalCount", details.size()); // 总数量使用明细记录数
            result.put("progressPercentage", task.getProgressPercentage());
            result.put("startTime", task.getStartTime());
            result.put("endTime", task.getEndTime());
            result.put("createdTime", task.getCreatedTime());
            result.put("updatedTime", task.getUpdatedTime());

            // 明细状态统计
            Map<String, Object> detailStats = new HashMap<>();
            detailStats.put("pending", (int)pendingCount);
            detailStats.put("processing", (int)processingCount);
            detailStats.put("completed", (int)completedCount);
            detailStats.put("failed", (int)failedCount);
            detailStats.put("total", details.size());
            result.put("detailStats", detailStats);

            // 失败记录详情
            result.put("failedDetails", failedDetails);

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("查询任务进度失败", e);
            return JsonResult.failure("查询任务进度失败: " + e.getMessage());
        }
    }

    /**
     * 获取状态的友好显示文本
     */
    private String getStatusMessage(String status) {
        if (status == null) return "未知状态";

        switch (status) {
            case BatchImportTask.STATUS_UPLOADING:
                return "正在上传";
            case BatchImportTask.STATUS_PROCESSING:
                return "正在处理";
            case BatchImportTask.STATUS_RECOGNIZING:
                return "正在识别";
            case BatchImportTask.STATUS_PREVIEWING:
                return "等待预览";
            case BatchImportTask.STATUS_SAVING:
                return "正在保存";
            case BatchImportTask.STATUS_COMPLETED:
                return "已完成";
            case BatchImportTask.STATUS_PARTIAL_SUCCESS:
                return "部分成功";
            case BatchImportTask.STATUS_FAILED:
                return "处理失败";
            case BatchImportTask.STATUS_CANCELLED:
                return "已取消";
            default:
                return status;
        }
    }

    /**
     * 开始批量识别
     * @param taskId 任务ID
     * @return 识别结果
     */
    @PostMapping("/recognize")
    public JsonResult startBatchRecognize(@RequestParam("taskId") String taskId) {
        try {
            log.info("开始批量识别，任务ID: {}", taskId);
            
            // 获取任务信息
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, taskId)
                    .eq(BatchImportTask::getAccountSetsId, this.accountSetsId)
            );
            
            if (task == null) {
                return JsonResult.failure("任务不存在");
            }
            
            // 获取待识别的明细
            List<BatchImportDetail> details = batchImportDetailMapper.selectByTaskIdAndStatus(taskId, BatchImportDetail.STATUS_PENDING);
            
            if (details.isEmpty()) {
                return JsonResult.failure("没有待识别的图片");
            }
            
            // 更新任务状态
            batchImportTaskMapper.updateStatus(taskId, BatchImportTask.STATUS_RECOGNIZING, null);
            
            // 获取明细ID列表
            List<Integer> detailIds = details.stream()
                .map(BatchImportDetail::getId)
                .collect(Collectors.toList());
            
            // 异步开始批量识别
            CompletableFuture<Map<String, Object>> future;
            if (BatchImportTask.TYPE_BANK_RECEIPT.equals(task.getImportType())) {
                future = batchOcrService.batchRecognizeBankReceipts(taskId, detailIds, this.currentUser.getId());
            } else {
                future = batchOcrService.batchRecognizeInvoices(taskId, detailIds, this.currentUser.getId());
            }
            
            // 设置异步回调，更新任务状态
            future.thenAccept(result -> {
                try {
                    String status = (String) result.get("status");
                    if ("COMPLETED".equals(status)) {
                        // 识别完成，设置为预览状态，进度90%
                        batchImportTaskMapper.updateStatus(taskId, BatchImportTask.STATUS_PREVIEWING, "识别完成，等待预览和保存");
                        batchImportTaskMapper.updateProgressPercentage(taskId, new BigDecimal("90"));
                    } else {
                        batchImportTaskMapper.updateStatus(taskId, BatchImportTask.STATUS_FAILED, (String) result.get("error"));
                    }
                } catch (Exception e) {
                    log.error("更新任务状态失败", e);
                }
            });
            
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("message", "批量识别已开始，请稍后查询进度");
            result.put("totalCount", detailIds.size());
            
            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("开始批量识别失败", e);
            return JsonResult.failure("开始批量识别失败: " + e.getMessage());
        }
    }

    /**
     * 重新识别选中的记录（基于模版）
     * @param request 重新识别请求
     * @return 识别结果
     */
    @PostMapping("/re-recognize-with-template")
    public JsonResult reRecognizeWithTemplate(@RequestBody Map<String, Object> request) {
        try {
            String taskId = (String) request.get("taskId");
            List<Integer> itemIds = (List<Integer>) request.get("itemIds");

            if (taskId == null || itemIds == null || itemIds.isEmpty()) {
                return JsonResult.failure("参数错误");
            }

            log.info("开始重新识别，任务ID: {}, 记录数量: {}", taskId, itemIds.size());

            // 验证任务权限
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, taskId)
                    .eq(BatchImportTask::getAccountSetsId, this.accountSetsId)
            );

            if (task == null) {
                return JsonResult.failure("任务不存在");
            }

            // 获取选中的明细记录
            List<BatchImportDetail> details = batchImportDetailMapper.selectBatchIds(itemIds);

            if (details.isEmpty()) {
                return JsonResult.failure("没有找到要重新识别的记录");
            }

            // 验证明细是否属于该任务
            boolean allBelongToTask = details.stream()
                .allMatch(detail -> taskId.equals(detail.getTaskId()));

            if (!allBelongToTask) {
                return JsonResult.failure("部分记录不属于该任务");
            }

            // 重置选中记录的状态为待处理，清空识别结果
            for (BatchImportDetail detail : details) {
                detail.setStatus(BatchImportDetail.STATUS_PENDING);
                detail.setRecognitionResult(null);
                detail.setErrorMessage(null);
                batchImportDetailMapper.updateById(detail);
            }

            // 异步开始重新识别（使用模版优化的识别）
            CompletableFuture<Map<String, Object>> future;
            if (BatchImportTask.TYPE_BANK_RECEIPT.equals(task.getImportType())) {
                future = batchOcrService.batchRecognizeBankReceipts(taskId, itemIds, this.currentUser.getId());
            } else {
                future = batchOcrService.batchRecognizeInvoices(taskId, itemIds, this.currentUser.getId());
            }

            // 设置异步回调
            future.thenAccept(result -> {
                try {
                    log.info("重新识别完成，任务ID: {}, 结果: {}", taskId, result);
                } catch (Exception e) {
                    log.error("重新识别回调处理失败", e);
                }
            });

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("message", "重新识别已开始，请稍后查询结果");
            result.put("totalCount", itemIds.size());

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("重新识别失败", e);
            return JsonResult.failure("重新识别失败: " + e.getMessage());
        }
    }

    /**
     * 更改单个记录的识别类型并重新识别
     * @param itemId 明细ID
     * @param newType 新的识别类型：BANK_RECEIPT-银行回单，INVOICE-发票
     * @return 重新识别结果
     */
    @PostMapping("/change-type-and-recognize")
    public JsonResult changeTypeAndReRecognize(@RequestParam("itemId") Integer itemId,
                                              @RequestParam("newType") String newType) {
        try {
            log.info("开始更改类型并重新识别，明细ID: {}, 新类型: {}", itemId, newType);

            // 验证新类型
            if (!BatchImportTask.TYPE_BANK_RECEIPT.equals(newType) && !BatchImportTask.TYPE_INVOICE.equals(newType)) {
                return JsonResult.failure("不支持的识别类型");
            }

            // 获取明细记录
            BatchImportDetail detail = batchImportDetailMapper.selectById(itemId);
            if (detail == null) {
                return JsonResult.failure("记录不存在");
            }

            // 获取任务信息
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, detail.getTaskId())
                    .eq(BatchImportTask::getAccountSetsId, this.accountSetsId)
            );

            if (task == null) {
                return JsonResult.failure("任务不存在");
            }

            // 重置记录状态，清空识别结果，并更新实际识别类型
            detail.setStatus(BatchImportDetail.STATUS_PENDING);  // 修复：设置为PENDING状态，这样批量识别接口才能找到
            detail.setRecognitionResult(null);
            detail.setErrorMessage(null);
            // 在parsedData字段中记录实际的识别类型，用于后续保存时判断
            detail.setParsedData("{\"actualType\":\"" + newType + "\"}");
            batchImportDetailMapper.updateById(detail);

            // 异步开始单个记录的重新识别
            CompletableFuture<Map<String, Object>> future;
            List<Integer> singleItemList = Arrays.asList(itemId);

            if (BatchImportTask.TYPE_BANK_RECEIPT.equals(newType)) {
                future = batchOcrService.batchRecognizeBankReceipts(detail.getTaskId(), singleItemList, this.currentUser.getId());
            } else {
                future = batchOcrService.batchRecognizeInvoices(detail.getTaskId(), singleItemList, this.currentUser.getId());
            }

            // 设置异步回调
            future.thenAccept(result -> {
                try {
                    log.info("单个记录重新识别完成，明细ID: {}, 结果: {}", itemId, result);
                } catch (Exception e) {
                    log.error("单个记录重新识别回调处理失败", e);
                }
            });

            Map<String, Object> result = new HashMap<>();
            result.put("itemId", itemId);
            result.put("newType", newType);
            result.put("message", "重新识别已开始，请稍后查询结果");

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("更改类型并重新识别失败", e);
            return JsonResult.failure("更改类型并重新识别失败: " + e.getMessage());
        }
    }

    /**
     * 批量更改记录的识别类型并重新识别
     * @param request 批量更改请求
     * @return 重新识别结果
     */




    /**
     * 获取任务列表
     * @param type 导入类型
     * @param status 任务状态
     * @param pageSize 页面大小
     * @return 任务列表
     */
    @GetMapping("/tasks")
    public JsonResult getTasks(@RequestParam(value = "type", required = false) String type,
                              @RequestParam(value = "status", required = false) String status,
                              @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            List<BatchImportTask> tasks;

            if (type != null || status != null) {
                // 根据条件查询
                com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask> wrapper =
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
                wrapper.eq(BatchImportTask::getAccountSetsId, this.accountSetsId);

                if (type != null && !type.isEmpty()) {
                    wrapper.eq(BatchImportTask::getImportType, type);
                }
                if (status != null && !status.isEmpty()) {
                    wrapper.eq(BatchImportTask::getStatus, status);
                }

                wrapper.orderByDesc(BatchImportTask::getCreatedTime);
                wrapper.last("LIMIT " + pageSize);

                tasks = batchImportTaskMapper.selectList(wrapper);
            } else {
                // 查询用户的所有任务
                tasks = batchImportTaskMapper.selectByAccountSetsAndUser(this.accountSetsId, this.currentUser.getId());
                if (tasks.size() > pageSize) {
                    tasks = tasks.subList(0, pageSize);
                }
            }

            return JsonResult.successful(tasks);

        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            return JsonResult.failure("获取任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务列表（分页）
     * @param page 页码
     * @param size 页面大小
     * @param importType 导入类型
     * @param status 任务状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页任务列表
     */
    @GetMapping("/task-list")
    public JsonResult getTaskList(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                                  @RequestParam(value = "importType", required = false) String importType,
                                  @RequestParam(value = "status", required = false) String status,
                                  @RequestParam(value = "startDate", required = false) String startDate,
                                  @RequestParam(value = "endDate", required = false) String endDate) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<BatchImportTask> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(BatchImportTask::getAccountSetsId, this.accountSetsId);
            wrapper.eq(BatchImportTask::getCreateUser, this.currentUser.getId());

            if (importType != null && !importType.trim().isEmpty()) {
                wrapper.eq(BatchImportTask::getImportType, importType);
            }

            if (status != null && !status.trim().isEmpty()) {
                wrapper.eq(BatchImportTask::getStatus, status);
            }

            if (startDate != null && !startDate.trim().isEmpty()) {
                wrapper.ge(BatchImportTask::getCreatedTime, startDate);
            }

            if (endDate != null && !endDate.trim().isEmpty()) {
                wrapper.le(BatchImportTask::getCreatedTime, endDate);
            }

            wrapper.orderByDesc(BatchImportTask::getCreatedTime);

            // 分页查询
            Page<BatchImportTask> pageParam = new Page<>(page, size);
            IPage<BatchImportTask> result = batchImportTaskMapper.selectPage(pageParam, wrapper);

            // 为每个任务计算实时统计信息
            List<Map<String, Object>> enrichedRecords = new ArrayList<>();
            for (BatchImportTask task : result.getRecords()) {
                Map<String, Object> taskData = new HashMap<>();
                taskData.put("id", task.getId());
                taskData.put("taskId", task.getTaskId());
                taskData.put("taskName", task.getTaskName());
                taskData.put("importType", task.getImportType());
                taskData.put("status", task.getStatus());
                taskData.put("totalFiles", task.getTotalFiles());
                taskData.put("totalImages", task.getTotalImages());
                taskData.put("processedFiles", task.getProcessedFiles());
                taskData.put("processedImages", task.getProcessedImages());
                taskData.put("progressPercentage", task.getProgressPercentage());
                taskData.put("errorMessage", task.getErrorMessage());
                taskData.put("startTime", task.getStartTime());
                taskData.put("endTime", task.getEndTime());
                taskData.put("createdTime", task.getCreatedTime());
                taskData.put("updatedTime", task.getUpdatedTime());
                taskData.put("accountSetsId", task.getAccountSetsId());
                taskData.put("createUser", task.getCreateUser());

                // 计算实时统计信息
                List<BatchImportDetail> details = batchImportDetailMapper.selectByTaskId(task.getTaskId());
                long successCount = details.stream().filter(d -> "SUCCESS".equals(d.getStatus())).count();
                long failedCount = details.stream().filter(d -> BatchImportDetail.STATUS_FAILED.equals(d.getStatus())).count();

                taskData.put("successCount", (int)successCount);
                taskData.put("failedCount", (int)failedCount);

                enrichedRecords.add(taskData);
            }

            // 构建返回结果
            Map<String, Object> data = new HashMap<>();
            data.put("records", enrichedRecords);
            data.put("total", result.getTotal());
            data.put("current", result.getCurrent());
            data.put("size", result.getSize());
            data.put("pages", result.getPages());

            return JsonResult.successful(data);
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            return JsonResult.failure("获取任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务的识别结果列表
     * @param taskId 任务ID
     * @return 识别结果列表
     */
    @GetMapping("/results/{taskId}")
    public JsonResult getRecognitionResults(@PathVariable("taskId") String taskId) {
        try {
            // 验证任务权限
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, taskId)
                    .eq(BatchImportTask::getAccountSetsId, this.accountSetsId)
            );

            if (task == null) {
                return JsonResult.failure("任务不存在");
            }

            // 获取明细列表
            List<BatchImportDetail> details = batchImportDetailMapper.selectByTaskId(taskId);

            return JsonResult.successful(details);

        } catch (Exception e) {
            log.error("获取识别结果失败", e);
            return JsonResult.failure("获取识别结果失败: " + e.getMessage());
        }
    }

    /**
     * 重新识别单个图片
     * @param detailId 明细ID
     * @return 识别结果
     */
    @PostMapping("/retry/{detailId}")
    public JsonResult retryRecognition(@PathVariable("detailId") Integer detailId) {
        try {
            // 获取明细信息
            BatchImportDetail detail = batchImportDetailMapper.selectById(detailId);
            if (detail == null) {
                return JsonResult.failure("明细记录不存在");
            }

            // 验证任务权限
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, detail.getTaskId())
                    .eq(BatchImportTask::getAccountSetsId, this.accountSetsId)
            );

            if (task == null) {
                return JsonResult.failure("无权限操作此任务");
            }

            // 异步重新识别
            CompletableFuture<Map<String, Object>> future = batchOcrService.retryRecognition(
                detailId,
                task.getImportType()
            );

            // 等待结果（这里简化处理，实际可以异步）
            Map<String, Object> result = future.get();

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("重新识别失败", e);
            return JsonResult.failure("重新识别失败: " + e.getMessage());
        }
    }

    /**
     * 批量保存到业务表
     * @param request 保存请求
     * @return 保存结果
     */
    @PostMapping("/save")
    public JsonResult batchSave(@RequestBody Map<String, Object> request) {
        try {
            String taskId = (String) request.get("taskId");
            List<Integer> selectedIds = (List<Integer>) request.get("selectedIds");

            if (taskId == null || selectedIds == null || selectedIds.isEmpty()) {
                return JsonResult.failure("参数错误");
            }

            // 验证任务权限
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, taskId)
                    .eq(BatchImportTask::getAccountSetsId, this.accountSetsId)
            );

            if (task == null) {
                return JsonResult.failure("任务不存在");
            }

            // 更新任务状态为保存中
            batchImportTaskMapper.updateStatus(taskId, BatchImportTask.STATUS_SAVING, null);

            // 异步执行批量保存，立即返回
            asyncBatchProcessService.batchSaveToBusinessTables(taskId, selectedIds, task.getImportType(), this.accountSetsId);

            Map<String, Object> result = new HashMap<>();
            result.put("message", "批量保存已开始，请稍后查看进度");
            result.put("totalCount", selectedIds.size());
            result.put("taskId", taskId);

            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("批量保存失败", e);
            return JsonResult.failure("批量保存失败: " + e.getMessage());
        }
    }

    /**
     * 删除任务
     * @param taskId 任务ID
     * @return 删除结果
     */
    @DeleteMapping("/task/{taskId}")
    public JsonResult deleteTask(@PathVariable("taskId") String taskId) {
        try {
            // 验证任务权限
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, taskId)
                    .eq(BatchImportTask::getAccountSetsId, this.accountSetsId)
            );

            if (task == null) {
                return JsonResult.failure("任务不存在");
            }

            // 删除任务（级联删除明细）
            batchImportTaskMapper.deleteById(task.getId());

            return JsonResult.successful();

        } catch (Exception e) {
            log.error("删除任务失败", e);
            return JsonResult.failure("删除任务失败: " + e.getMessage());
        }
    }

    /**
     * 更新单个识别结果
     * @param detailId 明细ID
     * @param request 更新数据
     * @return 更新结果
     */
    @PutMapping("/result/{detailId}")
    public JsonResult updateRecognitionResult(@PathVariable("detailId") Integer detailId,
                                            @RequestBody Map<String, Object> request) {
        try {
            // 获取明细信息并验证权限
            BatchImportDetail detail = batchImportDetailMapper.selectById(detailId);
            if (detail == null) {
                return JsonResult.failure("明细记录不存在");
            }

            // 验证任务权限
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, detail.getTaskId())
                    .eq(BatchImportTask::getAccountSetsId, this.accountSetsId)
            );

            if (task == null) {
                return JsonResult.failure("无权限操作此任务");
            }

            // 更新最终数据
            String finalData = JSON.toJSONString(request.get("data"));
            batchImportDetailMapper.updateFinalData(detailId, finalData);

            return JsonResult.successful();

        } catch (Exception e) {
            log.error("更新识别结果失败", e);
            return JsonResult.failure("更新识别结果失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新识别结果
     * @param taskId 任务ID
     * @param request 更新数据
     * @return 更新结果
     */
    @PutMapping("/results/{taskId}")
    public JsonResult batchUpdateResults(@PathVariable("taskId") String taskId,
                                       @RequestBody Map<String, Object> request) {
        try {
            // 验证任务权限
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, taskId)
                    .eq(BatchImportTask::getAccountSetsId, this.accountSetsId)
            );

            if (task == null) {
                return JsonResult.failure("任务不存在");
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> results = (List<Map<String, Object>>) request.get("results");
            if (results == null || results.isEmpty()) {
                return JsonResult.failure("没有要更新的数据");
            }

            int successCount = 0;
            int failedCount = 0;

            for (Map<String, Object> result : results) {
                try {
                    Integer detailId = (Integer) result.get("detailId");
                    Object data = result.get("data");

                    // 验证明细权限
                    BatchImportDetail detail = batchImportDetailMapper.selectById(detailId);
                    if (detail != null && detail.getAccountSetsId().equals(this.accountSetsId)) {
                        String finalData = JSON.toJSONString(data);
                        batchImportDetailMapper.updateFinalData(detailId, finalData);
                        successCount++;
                    } else {
                        failedCount++;
                    }
                } catch (Exception e) {
                    log.error("更新明细失败，ID: {}", result.get("detailId"), e);
                    failedCount++;
                }
            }

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("successCount", successCount);
            resultMap.put("failedCount", failedCount);
            resultMap.put("totalCount", results.size());

            return JsonResult.successful(resultMap);

        } catch (Exception e) {
            log.error("批量更新识别结果失败", e);
            return JsonResult.failure("批量更新识别结果失败: " + e.getMessage());
        }
    }

    /**
     * 手动拆分图片（暂时返回未实现）
     * @param detailId 明细ID
     * @return 拆分结果
     */
    @PostMapping("/split-image")
    public JsonResult splitImage(@RequestParam("detailId") Integer detailId) {
        return JsonResult.failure("拆分功能暂未实现");
    }

    /**
     * 保存银行回单
     * @param detail 批量导入明细
     */
    private void saveBankReceipt(BatchImportDetail detail) {
        try {
            // 解析数据，优先使用final_data，如果没有则使用parsed_data
            String dataStr = detail.getFinalData() != null ? detail.getFinalData() : detail.getParsedData();
            log.info("批量导入银行回单，明细ID: {}, 数据来源: {}, 数据长度: {}",
                detail.getId(),
                detail.getFinalData() != null ? "final_data" : "parsed_data",
                dataStr != null ? dataStr.length() : 0);

            if (dataStr == null || dataStr.trim().isEmpty()) {
                log.error("明细ID: {} 没有可用的解析数据，final_data: {}, parsed_data: {}",
                    detail.getId(), detail.getFinalData(), detail.getParsedData());
                throw new RuntimeException("没有可用的解析数据");
            }

            JSONObject finalData = JSON.parseObject(dataStr);
            if (finalData == null) {
                log.error("解析数据失败，数据格式不正确，原始数据: {}", dataStr);
                throw new RuntimeException("解析数据失败，数据格式不正确");
            }

            // 注意：数据已经在OCR识别阶段通过LLM映射为标准字段，这里直接使用
            log.info("使用已映射的标准字段数据，字段数: {}", finalData.size());

            // 创建银行回单对象
            BankReceipts bankReceipt = new BankReceipts();

            // 设置基本信息（使用OCR识别阶段已映射的标准字段名）
            bankReceipt.setReceiptTitle(finalData.getString("receipt_title"));
            bankReceipt.setAmount(finalData.getDouble("amount"));
            bankReceipt.setPayeeName(finalData.getString("payee_name"));
            bankReceipt.setPayeeAccount(finalData.getString("payee_account"));
            bankReceipt.setPayeeBank(finalData.getString("payee_bank"));
            bankReceipt.setPayerName(finalData.getString("payer_name"));
            bankReceipt.setPayerAccount(finalData.getString("payer_account"));
            bankReceipt.setPayerBank(finalData.getString("payer_bank"));
            bankReceipt.setSerialNumber(finalData.getString("serial_number"));
            bankReceipt.setTransactionInstitution(finalData.getString("transaction_institution"));
            bankReceipt.setAmountInWords(finalData.getString("amount_in_words"));

            // 处理必填字段：摘要
            String summary = finalData.getString("summary");
            if (summary == null || summary.trim().isEmpty()) {
                // 如果没有摘要，根据回单标题生成默认摘要
                String receiptTitle = finalData.getString("receipt_title");
                if (receiptTitle != null && !receiptTitle.trim().isEmpty()) {
                    summary = receiptTitle;
                } else {
                    summary = "银行回单";
                }
            }
            bankReceipt.setSummary(summary);

            // 处理必填字段：张数（默认1）
            Integer quantity = finalData.getInteger("quantity");
            if (quantity == null || quantity <= 0) {
                quantity = 1;
            }
            bankReceipt.setReceiptNum(quantity);

            // 处理必填字段：类型
            String type = finalData.getString("type");
            if (type == null || type.trim().isEmpty()) {
                // 如果AI没有判断出类型，根据回单标题进行简单判断
                String receiptTitle = finalData.getString("receipt_title");
                if (receiptTitle != null) {
                    if (receiptTitle.contains("收入") || receiptTitle.contains("转入") || receiptTitle.contains("存入") || receiptTitle.contains("贷方")) {
                        type = "收入";
                    } else if (receiptTitle.contains("支出") || receiptTitle.contains("转出") || receiptTitle.contains("支付") || receiptTitle.contains("借方")) {
                        type = "支出";
                    } else {
                        type = "支出"; // 默认为支出
                    }
                } else {
                    type = "支出"; // 默认为支出
                }
            }
            bankReceipt.setType(type);

            // 处理日期字段
            String receiptsDateStr = finalData.getString("receipts_date");
            if (receiptsDateStr != null && !receiptsDateStr.isEmpty()) {
                try {
                    // 尝试多种日期格式
                    java.text.SimpleDateFormat[] formats = {
                        new java.text.SimpleDateFormat("yyyy-MM-dd"),
                        new java.text.SimpleDateFormat("yyyy/MM/dd"),
                        new java.text.SimpleDateFormat("yyyy年MM月dd日")
                    };

                    for (java.text.SimpleDateFormat format : formats) {
                        try {
                            bankReceipt.setReceiptsDate(format.parse(receiptsDateStr));
                            break;
                        } catch (java.text.ParseException ignored) {
                            // 继续尝试下一种格式
                        }
                    }

                    if (bankReceipt.getReceiptsDate() == null) {
                        log.warn("无法解析日期格式: {}, 使用当前日期", receiptsDateStr);
                        bankReceipt.setReceiptsDate(new Date());
                    }
                } catch (Exception e) {
                    log.warn("解析日期失败: {}, 使用当前日期", receiptsDateStr, e);
                    bankReceipt.setReceiptsDate(new Date());
                }
            } else {
                bankReceipt.setReceiptsDate(new Date());
            }

            // 处理转账日期（必填字段）
            String transferDateStr = finalData.getString("transfer_date");
            if (transferDateStr != null && !transferDateStr.isEmpty()) {
                try {
                    java.text.SimpleDateFormat[] formats = {
                        new java.text.SimpleDateFormat("yyyy-MM-dd"),
                        new java.text.SimpleDateFormat("yyyy/MM/dd"),
                        new java.text.SimpleDateFormat("yyyy年MM月dd日")
                    };

                    for (java.text.SimpleDateFormat format : formats) {
                        try {
                            bankReceipt.setTransferDate(format.parse(transferDateStr));
                            break;
                        } catch (java.text.ParseException ignored) {
                            // 继续尝试下一种格式
                        }
                    }

                    if (bankReceipt.getTransferDate() == null) {
                        log.warn("无法解析转账日期格式: {}, 使用回单日期", transferDateStr);
                        bankReceipt.setTransferDate(bankReceipt.getReceiptsDate());
                    }
                } catch (Exception e) {
                    log.warn("解析转账日期失败: {}, 使用回单日期", transferDateStr, e);
                    bankReceipt.setTransferDate(bankReceipt.getReceiptsDate());
                }
            } else {
                // 如果没有转账日期，使用回单日期作为默认值
                log.info("转账日期为空，使用回单日期作为默认值");
                bankReceipt.setTransferDate(bankReceipt.getReceiptsDate());
            }

            // 设置图片路径
            bankReceipt.setFilePath(detail.getImageUrl());

            // 设置OCR识别信息（使用易读文本格式保存）
            String ocrRecognitionInfo = finalData.getString("ocrRecognitionInfo");
            if (ocrRecognitionInfo == null || ocrRecognitionInfo.trim().isEmpty()) {
                // 如果没有格式化的OCR信息，尝试从原始数据生成
                try {
                    JSONObject originalData = JSON.parseObject(detail.getRecognitionResult());
                    @SuppressWarnings("unchecked")
                    Map<String, String> rawOcrData = (Map<String, String>) originalData.get("rawOcrData");
                    if (rawOcrData != null) {
                        ocrRecognitionInfo = ocrService.formatOcrDataToReadableText(rawOcrData, "BANK_RECEIPT");
                    } else {
                        ocrRecognitionInfo = detail.getRecognitionResult();
                    }
                } catch (Exception e) {
                    log.warn("解析OCR原始数据失败，使用原始结果", e);
                    ocrRecognitionInfo = detail.getRecognitionResult();
                }
            }
            bankReceipt.setOcrRecognitionInfo(ocrRecognitionInfo);

            // 设置备注信息（保持用户可编辑的备注功能）
            String userRemark = finalData.getString("remark");
            if (userRemark != null && !userRemark.trim().isEmpty()) {
                bankReceipt.setRemark(userRemark);
            }

            // 设置创建信息
            bankReceipt.setCreateDate(new Date());
            bankReceipt.setCreateMember(this.currentUser.getId());

            // 调用服务保存
            bankReceiptsService.save(this.accountSetsId, bankReceipt, this.currentUser);

            log.info("银行回单保存成功，明细ID: {}, 回单编号: {}", detail.getId(), bankReceipt.getReceiptsNo());

        } catch (Exception e) {
            log.error("保存银行回单失败，明细ID: {}", detail.getId(), e);
            throw new RuntimeException("保存银行回单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存票据
     * @param detail 批量导入明细
     */
    private void saveBill(BatchImportDetail detail) {
        try {
            // 解析数据，优先使用final_data，如果没有则使用parsed_data
            String dataStr = detail.getFinalData() != null ? detail.getFinalData() : detail.getParsedData();
            log.info("批量导入票据，明细ID: {}, 数据来源: {}, 数据长度: {}",
                detail.getId(),
                detail.getFinalData() != null ? "final_data" : "parsed_data",
                dataStr != null ? dataStr.length() : 0);

            if (dataStr == null || dataStr.trim().isEmpty()) {
                log.error("明细ID: {} 没有可用的解析数据，final_data: {}, parsed_data: {}",
                    detail.getId(), detail.getFinalData(), detail.getParsedData());
                throw new RuntimeException("没有可用的解析数据");
            }

            JSONObject ocrData = JSON.parseObject(dataStr);
            if (ocrData == null) {
                log.error("解析票据OCR数据失败，数据格式不正确，原始数据: {}", dataStr);
                throw new RuntimeException("解析票据OCR数据失败，数据格式不正确");
            }

            // 创建票据对象
            Bill bill = new Bill();

            // 处理发票号码（重要：需要检查重复）
            String invoiceNumber = ocrData.getString("invoice_number");
            if (invoiceNumber != null && !invoiceNumber.trim().isEmpty()) {
                // 检查发票号码是否重复
                if (billService.isInvoiceNumberDuplicate(this.accountSetsId, invoiceNumber.trim(), null)) {
                    throw new RuntimeException("发票号码 '" + invoiceNumber + "' 已存在，请检查后重新输入");
                }
                bill.setInvoiceNumber(invoiceNumber.trim());
            }

            // 设置基本信息
            bill.setType(ocrData.getString("type") != null ? ocrData.getString("type") : "发票");

            // 处理金额（使用实际的字段名）
            if (ocrData.getDouble("amount") != null) {
                bill.setAmount(ocrData.getDouble("amount"));
            } else {
                log.warn("发票金额字段为空，明细ID: {}", detail.getId());
                bill.setAmount(0.0);
            }

            // 处理税率
            if (ocrData.getString("tax_rate") != null) {
                try {
                    String taxRateStr = ocrData.getString("tax_rate").replace("%", "");
                    double taxRateValue = Double.parseDouble(taxRateStr) / 100.0; // 转换为小数
                    bill.setTaxRate(java.math.BigDecimal.valueOf(taxRateValue));
                } catch (Exception e) {
                    log.warn("解析税率失败: {}, 明细ID: {}", ocrData.getString("tax_rate"), detail.getId());
                    bill.setTaxRate(java.math.BigDecimal.ZERO);
                }
            } else {
                bill.setTaxRate(java.math.BigDecimal.ZERO);
            }

            // 处理税额
            String totalTaxAmountStr = ocrData.getString("total_tax_amount");
            if (totalTaxAmountStr != null && !totalTaxAmountStr.trim().isEmpty()) {
                try {
                    // 移除货币符号
                    String cleanAmount = totalTaxAmountStr.replace("￥", "").replace("¥", "").replace(",", "").trim();
                    double taxAmount = Double.parseDouble(cleanAmount);
                    bill.setTotalTaxAmount(java.math.BigDecimal.valueOf(taxAmount));
                } catch (Exception e) {
                    log.warn("解析税额失败: {}, 明细ID: {}", totalTaxAmountStr, detail.getId());
                    bill.setTotalTaxAmount(java.math.BigDecimal.ZERO);
                }
            } else {
                bill.setTotalTaxAmount(java.math.BigDecimal.ZERO);
            }

            // 处理小写金额
            bill.setAmountInWords(ocrData.getString("amount_in_words"));

            // 处理开票方和收票方
            bill.setIssuer(ocrData.getString("issuer"));
            bill.setRecipient(ocrData.getString("recipient"));

            // 处理摘要
            String summary = ocrData.getString("summary");
            if (summary == null || summary.trim().isEmpty()) {
                summary = "发票";
            }
            bill.setSummary(summary);

            // 处理日期
            String invoiceDateStr = ocrData.getString("bill_date");
            if (invoiceDateStr != null && !invoiceDateStr.isEmpty()) {
                try {
                    java.text.SimpleDateFormat[] formats = {
                        new java.text.SimpleDateFormat("yyyy-MM-dd"),
                        new java.text.SimpleDateFormat("yyyy/MM/dd"),
                        new java.text.SimpleDateFormat("yyyy年MM月dd日")
                    };

                    for (java.text.SimpleDateFormat format : formats) {
                        try {
                            bill.setBillDate(format.parse(invoiceDateStr));
                            break;
                        } catch (java.text.ParseException ignored) {
                            // 继续尝试下一种格式
                        }
                    }

                    if (bill.getBillDate() == null) {
                        log.warn("无法解析日期格式: {}, 使用当前日期", invoiceDateStr);
                        bill.setBillDate(new Date());
                    }
                } catch (Exception e) {
                    log.warn("解析日期失败: {}, 使用当前日期", invoiceDateStr, e);
                    bill.setBillDate(new Date());
                }
            } else {
                bill.setBillDate(new Date());
            }

            // 设置图片路径
            bill.setAttachmentPath(detail.getImageUrl());

            // 设置OCR识别信息（使用易读文本格式保存）
            String ocrRecognitionInfo = ocrData.getString("ocrRecognitionInfo");
            if (ocrRecognitionInfo == null || ocrRecognitionInfo.trim().isEmpty()) {
                // 如果没有格式化的OCR信息，尝试从原始数据生成
                try {
                    JSONObject originalData = JSON.parseObject(detail.getRecognitionResult());
                    @SuppressWarnings("unchecked")
                    Map<String, String> rawOcrData = (Map<String, String>) originalData.get("rawOcrData");
                    if (rawOcrData != null) {
                        ocrRecognitionInfo = ocrService.formatOcrDataToReadableText(rawOcrData, "INVOICE");
                    } else {
                        ocrRecognitionInfo = detail.getRecognitionResult();
                    }
                } catch (Exception e) {
                    log.warn("解析OCR原始数据失败，使用原始结果", e);
                    ocrRecognitionInfo = detail.getRecognitionResult();
                }
            }
            bill.setOcrRecognitionInfo(ocrRecognitionInfo);

            // 设置备注信息（保持用户可编辑的备注功能）
            String userRemark = ocrData.getString("remark");
            if (userRemark != null && !userRemark.trim().isEmpty()) {
                bill.setRemark(userRemark);
            }

            // 设置默认状态
            bill.setStatus("未使用");

            // 调用服务保存
            billService.save(this.accountSetsId, bill, this.currentUser);

            log.info("票据保存成功，明细ID: {}, 票据编号: {}, 发票号码: {}",
                detail.getId(), bill.getBillNo(), bill.getInvoiceNumber());

        } catch (Exception e) {
            log.error("保存票据失败，明细ID: {}", detail.getId(), e);
            throw new RuntimeException("保存票据失败: " + e.getMessage(), e);
        }
    }








}
