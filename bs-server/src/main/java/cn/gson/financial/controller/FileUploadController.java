package cn.gson.financial.controller;

import cn.gson.financial.kernel.aliyuncs.OssService;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.service.OcrService;
import cn.gson.financial.service.SmartFieldMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/upload")
@Slf4j
public class FileUploadController {
    
    @Resource
    private OssService ossService;

    @Resource
    private OcrService ocrService;

    @Autowired(required = false)
    private SmartFieldMappingService smartFieldMappingService;
    
    /**
     * 上传文件到OSS
     */
    @PostMapping("/file")
    public JsonResult uploadFile(@RequestParam("file") MultipartFile file,
                                @RequestParam(value = "folder", defaultValue = "bank-voucher") String folder) {
        try {
            String fileUrl = ossService.uploadFile(file, folder);
            
            Map<String, Object> result = new HashMap<>();
            result.put("filePath", fileUrl);
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            
            return JsonResult.successful(result);
        } catch (Exception e) {
            //log.error("文件上传失败", e);
            return JsonResult.failure("文件上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除OSS文件
     */
    @DeleteMapping("/file")
    public JsonResult deleteFile(@RequestParam("fileUrl") String fileUrl) {
        try {
            boolean success = ossService.deleteFile(fileUrl);
            if (success) {
                return JsonResult.successful("文件删除成功", 200);
            } else {
                return JsonResult.failure("文件删除失败");
            }
        } catch (Exception e) {
            //log.error("文件删除失败", e);
            return JsonResult.failure("文件删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 识别银行回单
     */
    @PostMapping("/recognize-bank-receipt")
    public JsonResult recognizeFile(@RequestBody Map<String, String> request) {
        try {
            String fileUrl = request.get("fileUrl");
            if (fileUrl == null || fileUrl.trim().isEmpty()) {
                return JsonResult.failure("文件URL不能为空");
            }
            
            // 调用OCR服务识别
            Map<String, Object> result = ocrService.recognizeBankSlip(fileUrl);
            
            return JsonResult.successful(result);
        } catch (Exception e) {
            //log.error("银行回单识别失败", e);
            return JsonResult.failure("银行回单识别失败: " + e.getMessage());
        }
    }

    /**
     * 识别增值税发票
     */
    @PostMapping("/recognize-vat-invoice") // 新增的API路径
    public JsonResult recognizeVatInvoice(@RequestBody Map<String, String> request) {
        try {
            String fileUrl = request.get("fileUrl");
            if (fileUrl == null || fileUrl.trim().isEmpty()) {
                return JsonResult.failure("文件URL不能为空");
            }

            // 调用OCR服务识别增值税发票
            Map<String, Object> result = ocrService.recognizeVatInvoice(fileUrl); //确保OcrService中有这个方法

            return JsonResult.successful(result);
        } catch (Exception e) {
            //log.error("增值税发票识别失败", e);
            return JsonResult.failure("增值税发票识别失败: " + e.getMessage());
        }
    }

    /**
     * 智能识别银行回单（包含字段映射）
     */
    @PostMapping("/smart-recognize-bank-receipt")
    public JsonResult smartRecognizeBankReceipt(@RequestBody Map<String, Object> request) {
        try {
            String fileUrl = (String) request.get("fileUrl");
            Integer accountSetsId = (Integer) request.get("accountSetsId");
            Integer userId = (Integer) request.get("userId");

            if (fileUrl == null || fileUrl.trim().isEmpty()) {
                return JsonResult.failure("文件URL不能为空");
            }

            // 1. 先调用OCR服务获取原始识别结果
            Map<String, Object> ocrResult = ocrService.recognizeBankSlip(fileUrl);

            // 2. 如果有智能映射服务且提供了必要参数，进行智能字段映射
            if (smartFieldMappingService != null && accountSetsId != null && userId != null) {
                Map<String, Object> mappedResult = smartFieldMappingService.mapBankReceiptFields(
                        ocrResult, accountSetsId, userId);

                if (mappedResult != null && !mappedResult.isEmpty()) {
                    log.info("智能银行回单识别成功，映射字段数: {}", mappedResult.size());
                    return JsonResult.successful(mappedResult);
                }
            }

            // 3. 如果智能映射失败或不可用，返回原始OCR结果
            log.info("使用原始OCR识别结果");
            return JsonResult.successful(ocrResult);

        } catch (Exception e) {
            log.error("智能银行回单识别失败", e);
            return JsonResult.failure("智能银行回单识别失败: " + e.getMessage());
        }
    }

    /**
     * 智能识别发票（包含字段映射）
     */
    @PostMapping("/smart-recognize-invoice")
    public JsonResult smartRecognizeInvoice(@RequestBody Map<String, Object> request) {
        try {
            String fileUrl = (String) request.get("fileUrl");
            Integer accountSetsId = (Integer) request.get("accountSetsId");
            Integer userId = (Integer) request.get("userId");

            if (fileUrl == null || fileUrl.trim().isEmpty()) {
                return JsonResult.failure("文件URL不能为空");
            }

            // 1. 先调用OCR服务获取原始识别结果
            Map<String, Object> ocrResult = ocrService.recognizeVatInvoice(fileUrl);

            // 2. 如果有智能映射服务且提供了必要参数，进行智能字段映射
            if (smartFieldMappingService != null && accountSetsId != null && userId != null) {
                Map<String, Object> mappedResult = smartFieldMappingService.mapInvoiceFields(
                        ocrResult, accountSetsId, userId);

                if (mappedResult != null && !mappedResult.isEmpty()) {
                    log.info("智能发票识别成功，映射字段数: {}", mappedResult.size());
                    return JsonResult.successful(mappedResult);
                }
            }

            // 3. 如果智能映射失败或不可用，返回原始OCR结果
            log.info("使用原始OCR识别结果");
            return JsonResult.successful(ocrResult);

        } catch (Exception e) {
            log.error("智能发票识别失败", e);
            return JsonResult.failure("智能发票识别失败: " + e.getMessage());
        }
    }
}