package cn.gson.financial.controller;

import cn.gson.financial.base.BaseCrudController;
import cn.gson.financial.common.InitialBalanceExcelUtils;
import cn.gson.financial.kernel.common.DoubleValueUtil;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.kernel.exception.ServiceException;
import cn.gson.financial.kernel.model.entity.Subject;
import cn.gson.financial.kernel.model.entity.VoucherDetails;
import cn.gson.financial.kernel.model.vo.InitialBalanceVo;
import cn.gson.financial.kernel.service.SubjectService;
import cn.gson.financial.kernel.service.VoucherDetailsService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.controller</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月05日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@RestController
@RequestMapping("/initial-balance")
@AllArgsConstructor
@Slf4j
public class InitialBalanceController extends BaseCrudController<VoucherDetailsService, VoucherDetails> {

    private SubjectService subjectService;
    private InitialBalanceExcelUtils excelUtils;

    @Override
    public JsonResult list(@RequestParam Map<String, String> params) {
        List<VoucherDetails> balanceList = this.service.balanceList(this.accountSetsId, params.get("type"));
        List<VoucherDetails> auxiliaryList = this.service.auxiliaryList(this.accountSetsId, params.get("type"));
        Map<String, VoucherDetails> detailsMap = balanceList.stream().collect(Collectors.toMap(v -> v.getSubjectId() + v.getSubjectCode(), voucherDetails -> voucherDetails));
        LambdaQueryWrapper<Subject> qw = Wrappers.lambdaQuery();
        qw.orderByAsc(Subject::getCode);
        qw.eq(Subject::getAccountSetsId, this.accountSetsId);
        qw.eq(Subject::getType, params.get("type"));
        List<Integer> leafs = subjectService.leafList(this.accountSetsId);
        ArrayList<InitialBalanceVo> result = subjectService.list(qw).stream().collect(ArrayList::new, (list, subject) -> {
            VoucherDetails details = detailsMap.get(subject.getId() + subject.getCode());
            InitialBalanceVo vo = new InitialBalanceVo();

            // 先设置科目基本信息
            vo.setSubjectId(subject.getId());
            vo.setType(subject.getType());
            vo.setCode(subject.getCode());
            vo.setName(subject.getName());
            vo.setMnemonicCode(subject.getMnemonicCode());
            vo.setBalanceDirection(subject.getBalanceDirection());
            vo.setStatus(subject.getStatus());
            vo.setParentId(subject.getParentId());
            vo.setLevel(subject.getLevel());
            vo.setSystemDefault(subject.getSystemDefault());
            vo.setAccountSetsId(subject.getAccountSetsId());
            vo.setUnit(subject.getUnit());
            vo.setAuxiliaryAccounting(subject.getAuxiliaryAccounting());
            vo.setLeaf(leafs.contains(subject.getId()));

            // 然后设置期初余额数据（需要用到余额方向）
            setData(vo, details);

            list.add(vo);
        }, List::addAll);

        auxiliaryList.forEach(voucherDetails -> {
            InitialBalanceVo vo = new InitialBalanceVo();
            Subject subject = voucherDetails.getSubject();
            VoucherDetails details = detailsMap.get(subject.getId() + voucherDetails.getSubjectCode());

            setData(vo, details);

            vo.setSubjectId(voucherDetails.getSubjectId());
            vo.setCode(voucherDetails.getSubjectCode());
            vo.setName(subject.getName() + voucherDetails.getAuxiliaryTitle());
            vo.setBalanceDirection(subject.getBalanceDirection());
            vo.setParentId(subject.getParentId());
            vo.setLevel((short) (subject.getLevel() + 1));
            vo.setSystemDefault(subject.getSystemDefault());
            vo.setAccountSetsId(subject.getAccountSetsId());
            vo.setUnit(subject.getUnit());
            vo.setLeaf(true);

            result.add(vo);
        });

        return JsonResult.successful(result.stream().sorted(Comparator.comparing(Subject::getCode)).collect(Collectors.toList()));
    }

    private void setData(InitialBalanceVo vo, VoucherDetails details) {
        if (details != null) {
            // 设置原始的借贷金额，供前端使用
            vo.setDebitAmount(details.getDebitAmount());
            vo.setCreditAmount(details.getCreditAmount());

            // 正确处理期初余额，考虑借贷方向和负数转换
            Double beginBalance = null;
            String balanceDirection = vo.getBalanceDirection() != null ? vo.getBalanceDirection().toString() : null;

            if (details.getDebitAmount() != null && details.getCreditAmount() != null) {
                // 如果借贷方都有值，按科目方向计算
                if ("借".equals(balanceDirection)) {
                    beginBalance = details.getDebitAmount() - details.getCreditAmount();
                } else {
                    beginBalance = details.getCreditAmount() - details.getDebitAmount();
                }
            } else if (details.getDebitAmount() != null) {
                // 只有借方有值
                if ("借".equals(balanceDirection)) {
                    beginBalance = details.getDebitAmount();
                } else {
                    // 贷方科目但借方有值，说明是负数余额
                    beginBalance = -details.getDebitAmount();
                }
            } else if (details.getCreditAmount() != null) {
                // 只有贷方有值
                if ("贷".equals(balanceDirection)) {
                    beginBalance = details.getCreditAmount();
                } else {
                    // 借方科目但贷方有值，说明是负数余额
                    beginBalance = -details.getCreditAmount();
                }
            }

            vo.setBeginBalance(beginBalance);
            vo.setNum(details.getNum());
            vo.setCumulativeCredit(details.getCumulativeCredit());
            vo.setCumulativeDebit(details.getCumulativeDebit());
            vo.setCumulativeCreditNum(details.getCumulativeCreditNum());
            vo.setCumulativeDebitNum(details.getCumulativeDebitNum());
        }
    }

    @Override
    public JsonResult save(@RequestBody VoucherDetails entity) {
        // 处理期初余额的负数转换
        if ("期初".equals(entity.getSummary())) {
            // 验证期初数据的完整性和准确性
            String validationResult = validateInitialBalanceData(entity);
            if (validationResult != null) {
                return JsonResult.failure(validationResult);
            }

            // 获取科目信息以确定借贷方向
            Subject subject = subjectService.getById(entity.getSubjectId());
            if (subject != null) {
                // 验证只有末级科目可以录入期初余额
                if (!isLeafSubject(subject.getId(), this.accountSetsId)) {
                    return JsonResult.failure("只有末级科目可以录入期初余额，科目 " + subject.getCode() + "-" + subject.getName() + " 不是末级科目");
                }

                // 处理负数转换和余额方向验证
                String conversionResult = handleAmountConversionAndValidation(entity, subject);
                if (conversionResult != null) {
                    return JsonResult.failure(conversionResult);
                }
            } else {
                return JsonResult.failure("科目不存在");
            }
        }

        JsonResult result = super.save(entity);
        result.setData(entity);
        return result;
    }

    /**
     * 检查是否为末级科目
     * @param subjectId 科目ID
     * @param accountSetsId 账套ID
     * @return 是否为末级科目
     */
    private boolean isLeafSubject(Integer subjectId, Integer accountSetsId) {
        // 查询是否有子科目
        QueryWrapper<Subject> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", subjectId)
                   .eq("account_sets_id", accountSetsId)
                   .eq("status", true);
        long childCount = subjectService.count(queryWrapper);
        return childCount == 0;
    }

    /**
     * 验证期初余额数据的完整性和准确性
     * @param entity 期初余额数据
     * @return 验证错误信息，null表示验证通过
     */
    private String validateInitialBalanceData(VoucherDetails entity) {
        // 验证科目ID不能为空
        if (entity.getSubjectId() == null) {
            return "科目ID不能为空";
        }

        // 验证借贷金额不能同时为空
        if ((entity.getDebitAmount() == null || entity.getDebitAmount() == 0) &&
            (entity.getCreditAmount() == null || entity.getCreditAmount() == 0)) {
            return "借方金额和贷方金额不能同时为空";
        }

        // 验证借贷金额不能同时有值
        if (entity.getDebitAmount() != null && entity.getDebitAmount() != 0 &&
            entity.getCreditAmount() != null && entity.getCreditAmount() != 0) {
            return "借方金额和贷方金额不能同时有值";
        }

        // 验证金额不能为负数（在转换之前）
        if ((entity.getDebitAmount() != null && entity.getDebitAmount() < 0) ||
            (entity.getCreditAmount() != null && entity.getCreditAmount() < 0)) {
            // 负数将在后续处理中转换，这里只是记录
        }

        return null;
    }

    /**
     * 处理金额转换和余额方向验证
     * @param entity 期初余额数据
     * @param subject 科目信息
     * @return 验证错误信息，null表示处理成功
     */
    private String handleAmountConversionAndValidation(VoucherDetails entity, Subject subject) {
        // 如果借方金额为负数，转换为贷方金额
        if (entity.getDebitAmount() != null && entity.getDebitAmount() < 0) {
            entity.setCreditAmount(Math.abs(entity.getDebitAmount()));
            entity.setDebitAmount(null);
        }
        // 如果贷方金额为负数，转换为借方金额
        else if (entity.getCreditAmount() != null && entity.getCreditAmount() < 0) {
            entity.setDebitAmount(Math.abs(entity.getCreditAmount()));
            entity.setCreditAmount(null);
        }

        // 验证余额方向是否合理（可选的业务规则验证）
        String balanceDirection = subject.getBalanceDirection().toString();
        if ("借".equals(balanceDirection) && entity.getCreditAmount() != null && entity.getCreditAmount() > 0) {
            // 对于借方余额科目，如果有贷方余额，给出警告但不阻止保存
            // 这在实际业务中可能是合理的（如应收账款可能有贷方余额）
        } else if ("贷".equals(balanceDirection) && entity.getDebitAmount() != null && entity.getDebitAmount() > 0) {
            // 对于贷方余额科目，如果有借方余额，给出警告但不阻止保存
        }

        return null;
    }

    @PostMapping("auxiliary")
    public JsonResult auxiliary(@RequestBody HashMap<String, Object> entity) {
        this.service.saveAuxiliary(this.accountSetsId, entity);
        return JsonResult.successful();
    }

    @GetMapping("trialBalance")
    public JsonResult trialBalance() {
        Map<String, Map<String, Double>> data = this.service.trialBalance(this.accountSetsId);
        return JsonResult.successful(data);
    }

    /**
     * 下载期初余额模板
     *
     * @param response
     * @throws IOException
     */
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        try {
            // 检查用户认证
            if (this.currentUser == null) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("用户未登录");
                return;
            }

            if (this.accountSetsId == null) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("账套信息不存在");
                return;
            }

            log.info("开始生成期初余额导入模板，账套ID: {}", this.accountSetsId);

            excelUtils.generateTemplate(this.accountSetsId, response);

            log.info("期初余额模板生成成功");
        } catch (ServiceException e) {
            log.warn("期初余额模板生成业务异常: {}", e.getMessage());
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write(e.getMessage());
        } catch (Exception e) {
            log.error("期初余额模板生成系统异常", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("模板生成失败: " + e.getMessage());
        }
    }

    /**
     * 导入期初余额
     *
     * @param multipartFile
     * @return
     */
    @PostMapping("/import")
    public JsonResult importInitialBalance(@RequestParam("file") MultipartFile multipartFile) {
        try {
            // 验证文件
            if (multipartFile == null || multipartFile.isEmpty()) {
                return JsonResult.failure("请选择要导入的文件");
            }

            String fileName = multipartFile.getOriginalFilename();
            if (fileName == null || (!fileName.toLowerCase().endsWith(".xls") && !fileName.toLowerCase().endsWith(".xlsx"))) {
                return JsonResult.failure("请上传Excel文件（.xls或.xlsx格式）");
            }

            log.info("开始导入期初余额文件: {}", fileName);

            List<VoucherDetails> initialBalanceList = excelUtils.readExcel(fileName, multipartFile.getInputStream(), this.currentUser);
            this.service.importInitialBalance(initialBalanceList, this.currentUser.getAccountSets());

            log.info("期初余额导入成功，共导入 {} 条记录", initialBalanceList.size());
            return JsonResult.successful();
        } catch (ServiceException e) {
            log.warn("期初余额导入业务异常: {}", e.getMessage());
            return JsonResult.failure(e.getMessage());
        } catch (Exception e) {
            log.error("期初余额导入系统异常", e);
            return JsonResult.failure("导入失败: " + e.getMessage());
        }
    }
}
