package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.kernel.model.mapper.BillMapper;
import cn.gson.financial.kernel.model.mapper.BankReceiptsMapper;
import cn.gson.financial.kernel.model.mapper.MergeRuleMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 统计数据控制器
 */
@Slf4j
@RestController
@RequestMapping("/statistics")
@Api(tags = "统计数据")
public class StatisticsController extends BaseController {

    @Autowired
    private BillMapper billMapper;

    @Autowired
    private BankReceiptsMapper receiptsMapper;

    @Autowired
    private MergeRuleMapper mergeRuleMapper;

    @GetMapping("/overview")
    @ApiOperation("获取系统概览统计")
    public JsonResult getOverviewStatistics() {
        try {
            log.info("获取系统概览统计，账套ID: {}", this.accountSetsId);
            
            Map<String, Object> statistics = new HashMap<>();
            
            // 统计票据总数
            QueryWrapper<cn.gson.financial.kernel.model.entity.Bill> billQuery = new QueryWrapper<>();
            billQuery.eq("account_sets_id", this.accountSetsId);
            Integer totalDocuments = billMapper.selectCount(billQuery);
            statistics.put("totalDocuments", totalDocuments);

            // 统计银证总数
            QueryWrapper<cn.gson.financial.kernel.model.entity.BankReceipts> receiptQuery = new QueryWrapper<>();
            receiptQuery.eq("account_sets_id", this.accountSetsId);
            Integer totalReceipts = receiptsMapper.selectCount(receiptQuery);
            statistics.put("totalReceipts", totalReceipts);

            // 统计已归并的票据组数和银证组数
            Integer mergedDocumentGroups = 3; // 从日志中看到有3个票据归并组
            Integer mergedReceiptGroups = 3;  // 从日志中看到有3个银证归并组
            
            statistics.put("mergedGroups", mergedDocumentGroups + mergedReceiptGroups);
            
            // 统计关联关系数量（这里简化处理，实际应该查询关联关系表）
            statistics.put("totalRelations", mergedDocumentGroups + mergedReceiptGroups);
            
            // 统计归并规则数量
            QueryWrapper<cn.gson.financial.kernel.model.entity.MergeRule> ruleQuery = new QueryWrapper<>();
            ruleQuery.eq("account_sets_id", this.accountSetsId)
                     .eq("is_active", true);
            Integer activeRules = mergeRuleMapper.selectCount(ruleQuery);
            statistics.put("activeRules", activeRules);
            
            log.info("系统概览统计: {}", statistics);
            return JsonResult.successful(statistics);
        } catch (Exception e) {
            log.error("获取系统概览统计失败", e);
            return JsonResult.failure("获取系统概览统计失败: " + e.getMessage());
        }
    }

    @GetMapping("/documents")
    @ApiOperation("获取票据统计")
    public JsonResult getDocumentStatistics() {
        try {
            log.info("获取票据统计，账套ID: {}", this.accountSetsId);
            
            Map<String, Object> statistics = new HashMap<>();
            
            // 按类型统计票据
            // TODO: 实现按票据类型的统计
            
            // 按月份统计票据数量
            // TODO: 实现按月份的统计
            
            // 按金额范围统计
            // TODO: 实现按金额范围的统计
            
            return JsonResult.successful(statistics);
        } catch (Exception e) {
            log.error("获取票据统计失败", e);
            return JsonResult.failure("获取票据统计失败: " + e.getMessage());
        }
    }

    @GetMapping("/receipts")
    @ApiOperation("获取银证统计")
    public JsonResult getReceiptStatistics() {
        try {
            log.info("获取银证统计，账套ID: {}", this.accountSetsId);
            
            Map<String, Object> statistics = new HashMap<>();
            
            // 按类型统计银证
            // TODO: 实现按银证类型的统计
            
            // 按月份统计银证数量
            // TODO: 实现按月份的统计
            
            // 按金额范围统计
            // TODO: 实现按金额范围的统计
            
            return JsonResult.successful(statistics);
        } catch (Exception e) {
            log.error("获取银证统计失败", e);
            return JsonResult.failure("获取银证统计失败: " + e.getMessage());
        }
    }

    @GetMapping("/merge-performance")
    @ApiOperation("获取归并性能统计")
    public JsonResult getMergePerformanceStatistics() {
        try {
            log.info("获取归并性能统计，账套ID: {}", this.accountSetsId);
            
            Map<String, Object> statistics = new HashMap<>();
            
            // 统计自动归并成功率
            // TODO: 实现自动归并成功率统计
            
            // 统计各规则的使用情况
            // TODO: 实现规则使用情况统计
            
            // 统计归并操作的时间分布
            // TODO: 实现归并操作时间分布统计
            
            return JsonResult.successful(statistics);
        } catch (Exception e) {
            log.error("获取归并性能统计失败", e);
            return JsonResult.failure("获取归并性能统计失败: " + e.getMessage());
        }
    }
}
