package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.controller.JsonResult;
import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.service.AiRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * AI智能关联控制器
 */
@Slf4j
@RestController
@RequestMapping("/ai-relation")
@Api(tags = "AI智能关联管理")
public class AiRelationController extends BaseController {

    @Autowired
    private AiRelationService aiRelationService;

    @PostMapping("/analyze")
    @ApiOperation("AI分析关联关系")
    public JsonResult analyzeRelations(@RequestBody Map<String, Object> requestData) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> billIds = (List<Integer>) requestData.get("billIds");
            @SuppressWarnings("unchecked")
            List<Integer> receiptIds = (List<Integer>) requestData.get("receiptIds");
            
            log.info("AI分析关联关系，账套ID: {}, 票据数量: {}, 银证数量: {}", 
                this.accountSetsId, billIds != null ? billIds.size() : 0, receiptIds != null ? receiptIds.size() : 0);
            
            // 获取票据和银证列表
            List<Bill> bills = aiRelationService.getUnrelatedBills(this.accountSetsId, null);
            List<BankReceipts> receipts = aiRelationService.getUnrelatedReceipts(this.accountSetsId, null);
            
            // 如果指定了ID，则过滤
            if (billIds != null && !billIds.isEmpty()) {
                bills = bills.stream()
                    .filter(bill -> billIds.contains(bill.getId()))
                    .collect(java.util.stream.Collectors.toList());
            }
            
            if (receiptIds != null && !receiptIds.isEmpty()) {
                receipts = receipts.stream()
                    .filter(receipt -> receiptIds.contains(receipt.getId()))
                    .collect(java.util.stream.Collectors.toList());
            }
            
            Map<String, Object> result = aiRelationService.analyzeRelations(this.accountSetsId, bills, receipts, this.currentUser.getId());
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("AI分析关联关系失败", e);
            return JsonResult.failure("AI分析失败: " + e.getMessage());
        }
    }

    @PostMapping("/execute")
    @ApiOperation("AI执行智能关联")
    public JsonResult executeRelations(@RequestBody Map<String, Object> requestData) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> billIds = (List<Integer>) requestData.get("billIds");
            @SuppressWarnings("unchecked")
            List<Integer> receiptIds = (List<Integer>) requestData.get("receiptIds");

            log.info("AI执行智能关联，账套ID: {}, 票据数量: {}, 银证数量: {}",
                this.accountSetsId, billIds != null ? billIds.size() : 0, receiptIds != null ? receiptIds.size() : 0);

            // 获取票据和银证列表
            List<Bill> bills = aiRelationService.getUnrelatedBills(this.accountSetsId, null);
            List<BankReceipts> receipts = aiRelationService.getUnrelatedReceipts(this.accountSetsId, null);

            // 如果指定了ID，则过滤
            if (billIds != null && !billIds.isEmpty()) {
                bills = bills.stream()
                    .filter(bill -> billIds.contains(bill.getId()))
                    .collect(java.util.stream.Collectors.toList());
            }

            if (receiptIds != null && !receiptIds.isEmpty()) {
                receipts = receipts.stream()
                    .filter(receipt -> receiptIds.contains(receipt.getId()))
                    .collect(java.util.stream.Collectors.toList());
            }

            Map<String, Object> result = aiRelationService.executeAiRelations(this.accountSetsId, bills, receipts, this.currentUser.getId());
            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("AI执行智能关联失败", e);
            return JsonResult.failure("AI关联失败: " + e.getMessage());
        }
    }

    @PostMapping("/execute-selected")
    @ApiOperation("AI执行选中的智能关联")
    public JsonResult executeSelectedRelations(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> selectedRelations = (List<Map<String, Object>>) request.get("selectedRelations");

            log.info("AI执行选中智能关联，账套ID: {}, 选中关联数: {}",
                    this.accountSetsId, selectedRelations.size());

            Map<String, Object> result = aiRelationService.executeSelectedRelations(
                    this.accountSetsId, selectedRelations, this.currentUser.getId());
            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("AI执行选中智能关联失败", e);
            return JsonResult.failure("选中关联失败: " + e.getMessage());
        }
    }

    @PostMapping("/analyze-complex")
    @ApiOperation("AI复杂智能关联分析")
    public JsonResult analyzeComplexRelations(@RequestBody Map<String, Object> analysisData) {
        try {
            log.info("AI复杂智能关联分析，账套ID: {}", this.accountSetsId);

            Map<String, Object> result = aiRelationService.analyzeComplexRelations(
                    this.accountSetsId, analysisData, this.currentUser.getId());
            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("AI复杂智能关联分析失败", e);
            return JsonResult.failure("复杂分析失败: " + e.getMessage());
        }
    }

    @PostMapping("/execute-selected-complex")
    @ApiOperation("AI执行选中的复杂智能关联")
    public JsonResult executeSelectedComplexRelations(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> selectedRelations = (List<Map<String, Object>>) request.get("selectedRelations");

            log.info("AI执行选中复杂智能关联，账套ID: {}, 选中关联数: {}",
                    this.accountSetsId, selectedRelations.size());

            Map<String, Object> result = aiRelationService.executeSelectedComplexRelations(
                    this.accountSetsId, selectedRelations, this.currentUser.getId());
            return JsonResult.successful(result);

        } catch (Exception e) {
            log.error("AI执行选中复杂智能关联失败", e);
            return JsonResult.failure("选中复杂关联失败: " + e.getMessage());
        }
    }

    @GetMapping("/bill/{billId}/suggestions")
    @ApiOperation("AI分析票据关联建议")
    public JsonResult analyzeBillSuggestions(@ApiParam("票据ID") @PathVariable Integer billId) {
        try {
            log.info("AI分析票据关联建议，账套ID: {}, 票据ID: {}", this.accountSetsId, billId);
            
            // 这里需要根据billId获取Bill对象，简化处理
            Bill bill = new Bill();
            bill.setId(billId);
            
            Map<String, Object> result = aiRelationService.analyzeBillRelationSuggestions(this.accountSetsId, bill);
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("AI分析票据关联建议失败", e);
            return JsonResult.failure("AI分析失败: " + e.getMessage());
        }
    }

    @GetMapping("/receipt/{receiptId}/suggestions")
    @ApiOperation("AI分析银证关联建议")
    public JsonResult analyzeReceiptSuggestions(@ApiParam("银证ID") @PathVariable Integer receiptId) {
        try {
            log.info("AI分析银证关联建议，账套ID: {}, 银证ID: {}", this.accountSetsId, receiptId);
            
            // 这里需要根据receiptId获取BankReceipts对象，简化处理
            BankReceipts receipt = new BankReceipts();
            receipt.setId(receiptId);
            
            Map<String, Object> result = aiRelationService.analyzeReceiptRelationSuggestions(this.accountSetsId, receipt);
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("AI分析银证关联建议失败", e);
            return JsonResult.failure("AI分析失败: " + e.getMessage());
        }
    }

    @GetMapping("/unrelated/bills")
    @ApiOperation("获取未关联的票据列表")
    public JsonResult getUnrelatedBills(@ApiParam("限制数量") @RequestParam(required = false) Integer limit) {
        try {
            log.info("获取未关联的票据列表，账套ID: {}, 限制数量: {}", this.accountSetsId, limit);
            
            List<Bill> bills = aiRelationService.getUnrelatedBills(this.accountSetsId, limit);
            return JsonResult.successful(bills);
            
        } catch (Exception e) {
            log.error("获取未关联票据列表失败", e);
            return JsonResult.failure("获取失败: " + e.getMessage());
        }
    }

    @GetMapping("/unrelated/receipts")
    @ApiOperation("获取未关联的银证列表")
    public JsonResult getUnrelatedReceipts(@ApiParam("限制数量") @RequestParam(required = false) Integer limit) {
        try {
            log.info("获取未关联的银证列表，账套ID: {}, 限制数量: {}", this.accountSetsId, limit);
            
            List<BankReceipts> receipts = aiRelationService.getUnrelatedReceipts(this.accountSetsId, limit);
            return JsonResult.successful(receipts);
            
        } catch (Exception e) {
            log.error("获取未关联银证列表失败", e);
            return JsonResult.failure("获取失败: " + e.getMessage());
        }
    }

    @PostMapping("/auto-relate-all")
    @ApiOperation("AI自动关联所有未关联数据")
    public JsonResult autoRelateAll(@ApiParam("限制数量") @RequestParam(required = false, defaultValue = "50") Integer limit) {
        try {
            log.info("AI自动关联所有未关联数据，账套ID: {}, 限制数量: {}", this.accountSetsId, limit);
            
            Map<String, Object> result = aiRelationService.autoRelateAll(this.accountSetsId, this.currentUser.getId(), limit);
            return JsonResult.successful(result);
            
        } catch (Exception e) {
            log.error("AI自动关联失败", e);
            return JsonResult.failure("自动关联失败: " + e.getMessage());
        }
    }
}
