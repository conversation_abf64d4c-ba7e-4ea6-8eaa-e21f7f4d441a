package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.model.dto.ManualMergeDto;
import cn.gson.financial.kernel.model.dto.MergeExecuteDto;
import cn.gson.financial.kernel.model.dto.MergePreviewDto;
import cn.gson.financial.kernel.model.entity.MergeTask;
import cn.gson.financial.kernel.service.MergeEngineService;
import cn.gson.financial.kernel.controller.JsonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 归并引擎控制器
 */
@Slf4j
@RestController
@RequestMapping("/merge-engine")
@Api(tags = "归并引擎管理")
public class MergeEngineController extends BaseController {

    @Autowired
    private MergeEngineService mergeEngineService;

    @GetMapping("/documents/preview")
    @ApiOperation("预览票据归并结果")
    public JsonResult previewDocumentMerge(@ApiParam("规则ID") @RequestParam String ruleId) {
        try {
            log.info("预览票据归并，账套ID: {}, 规则ID: {}", this.accountSetsId, ruleId);
            MergePreviewDto preview = mergeEngineService.previewDocumentMerge(this.accountSetsId, ruleId);
            return JsonResult.successful(preview);
        } catch (Exception e) {
            log.error("预览票据归并失败", e);
            return JsonResult.failure("预览票据归并失败: " + e.getMessage());
        }
    }

    @PostMapping("/documents/execute")
    @ApiOperation("执行票据归并")
    public JsonResult executeDocumentMerge(@RequestBody MergeExecuteDto executeDto) {
        try {
            log.info("执行票据归并，账套ID: {}, 规则ID: {}", this.accountSetsId, executeDto.getRuleId());
            Object result = mergeEngineService.executeDocumentMerge(this.accountSetsId, executeDto, this.currentUser.getId());
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("执行票据归并失败", e);
            return JsonResult.failure("执行票据归并失败: " + e.getMessage());
        }
    }

    @GetMapping("/receipts/preview")
    @ApiOperation("预览银证归并结果")
    public JsonResult previewReceiptMerge(@ApiParam("规则ID") @RequestParam String ruleId) {
        try {
            log.info("预览银证归并，账套ID: {}, 规则ID: {}", this.accountSetsId, ruleId);
            MergePreviewDto preview = mergeEngineService.previewReceiptMerge(this.accountSetsId, ruleId);
            return JsonResult.successful(preview);
        } catch (Exception e) {
            log.error("预览银证归并失败", e);
            return JsonResult.failure("预览银证归并失败: " + e.getMessage());
        }
    }

    @PostMapping("/receipts/execute")
    @ApiOperation("执行银证归并")
    public JsonResult executeReceiptMerge(@RequestBody MergeExecuteDto executeDto) {
        try {
            log.info("执行银证归并，账套ID: {}, 规则ID: {}", this.accountSetsId, executeDto.getRuleId());
            Object result = mergeEngineService.executeReceiptMerge(this.accountSetsId, executeDto, this.currentUser.getId());
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("执行银证归并失败", e);
            return JsonResult.failure("执行银证归并失败: " + e.getMessage());
        }
    }

    @GetMapping("/documents/unmerged")
    @ApiOperation("获取未归并的票据")
    public JsonResult getUnmergedDocuments() {
        try {
            log.info("获取未归并票据，账套ID: {}", this.accountSetsId);
            Object result = mergeEngineService.getUnmergedDocuments(this.accountSetsId);
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("获取未归并票据失败", e);
            return JsonResult.failure("获取未归并票据失败: " + e.getMessage());
        }
    }

    @GetMapping("/receipts/unmerged")
    @ApiOperation("获取未归并的银证")
    public JsonResult getUnmergedReceipts() {
        try {
            log.info("获取未归并银证，账套ID: {}", this.accountSetsId);
            Object result = mergeEngineService.getUnmergedReceipts(this.accountSetsId);
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("获取未归并银证失败", e);
            return JsonResult.failure("获取未归并银证失败: " + e.getMessage());
        }
    }

    @PostMapping("/documents/manual-merge")
    @ApiOperation("手动票据归并")
    public JsonResult manualDocumentMerge(@RequestBody ManualMergeDto mergeDto) {
        try {
            log.info("手动票据归并，账套ID: {}, 票据数量: {}, 组名称: {}",
                this.accountSetsId, mergeDto.getDocumentIds().size(), mergeDto.getGroupName());
            Object result = mergeEngineService.manualMergeDocuments(this.accountSetsId, mergeDto, this.currentUser.getId());
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("手动票据归并失败", e);
            return JsonResult.failure("手动票据归并失败: " + e.getMessage());
        }
    }

    @PostMapping("/receipts/manual-merge")
    @ApiOperation("手动银证归并")
    public JsonResult manualReceiptMerge(@RequestBody ManualMergeDto mergeDto) {
        try {
            log.info("手动银证归并，账套ID: {}, 银证数量: {}, 组名称: {}",
                this.accountSetsId, mergeDto.getReceiptIds().size(), mergeDto.getGroupName());
            Object result = mergeEngineService.manualMergeReceipts(this.accountSetsId, mergeDto, this.currentUser.getId());
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("手动银证归并失败", e);
            return JsonResult.failure("手动银证归并失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/documents/groups/{groupId}")
    @ApiOperation("解散票据归并组")
    public JsonResult unmergeDocumentGroup(@ApiParam("组ID") @PathVariable String groupId) {
        try {
            log.info("解散票据归并组，账套ID: {}, 组ID: {}", this.accountSetsId, groupId);
            boolean result = mergeEngineService.unmergeDocumentGroup(this.accountSetsId, groupId);
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("解散票据归并组失败", e);
            return JsonResult.failure("解散票据归并组失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/receipts/groups/{groupId}")
    @ApiOperation("解散银证归并组")
    public JsonResult unmergeReceiptGroup(@ApiParam("组ID") @PathVariable String groupId) {
        try {
            log.info("解散银证归并组，账套ID: {}, 组ID: {}", this.accountSetsId, groupId);
            boolean result = mergeEngineService.unmergeReceiptGroup(this.accountSetsId, groupId);
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("解散银证归并组失败", e);
            return JsonResult.failure("解散银证归并组失败: " + e.getMessage());
        }
    }

    @PostMapping("/incremental-merge")
    @ApiOperation("增量归并新记录")
    public JsonResult incrementalMerge(
            @ApiParam("实体ID") @RequestParam String entityId,
            @ApiParam("实体类型") @RequestParam String entityType) {
        try {
            boolean result = mergeEngineService.incrementalMerge(this.accountSetsId, entityId, entityType, this.currentUser.getId());
            return JsonResult.successful(result);
        } catch (Exception e) {
            log.error("增量归并失败", e);
            return JsonResult.failure("增量归并失败: " + e.getMessage());
        }
    }

    @GetMapping("/tasks/{taskId}")
    @ApiOperation("查询归并任务状态")
    public JsonResult getTaskStatus(
            @ApiParam("任务ID") @PathVariable String taskId) {
        try {
            MergeTask task = mergeEngineService.getTaskStatus(taskId);
            return JsonResult.successful(task);
        } catch (Exception e) {
            log.error("查询任务状态失败", e);
            return JsonResult.failure("查询任务状态失败: " + e.getMessage());
        }
    }
}
