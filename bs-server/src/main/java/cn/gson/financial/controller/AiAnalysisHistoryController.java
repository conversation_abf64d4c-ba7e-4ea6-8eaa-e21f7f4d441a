package cn.gson.financial.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.model.entity.AiMatchingHistory;
import cn.gson.financial.kernel.model.vo.AiStatisticsOverview;
import cn.gson.financial.kernel.model.vo.ErrorAnalysis;
import cn.gson.financial.kernel.service.AiAnalysisHistoryService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/ai-analysis-history")
public class AiAnalysisHistoryController extends BaseController {

    @Resource
    private AiAnalysisHistoryService aiAnalysisHistoryService;

    @PostMapping("/history")
    public IPage<AiMatchingHistory> getHistory(@RequestBody Map<String, Object> params) {
        Integer page = (Integer) params.get("page");
        Integer size = (Integer) params.get("size");
        String businessType = (String) params.get("businessType");
        Date startDate = params.get("startDate") != null ? new Date((Long) params.get("startDate")) : null;
        Date endDate = params.get("endDate") != null ? new Date((Long) params.get("endDate")) : null;

        return aiAnalysisHistoryService.findHistoryByPage(
                new Page<>(page, size),
                this.accountSetsId,
                businessType,
                startDate,
                endDate
        );
    }

    @GetMapping("/overview")
    public AiStatisticsOverview getStatisticsOverview(@RequestParam(required = false) Long startDate, @RequestParam(required = false) Long endDate) {
        Date start = startDate != null ? new Date(startDate) : null;
        Date end = endDate != null ? new Date(endDate) : null;
        return aiAnalysisHistoryService.getStatisticsOverview(this.accountSetsId, start, end);
    }

    @GetMapping("/error-analysis")
    public List<ErrorAnalysis> getErrorAnalysis(@RequestParam(required = false) Long startDate, @RequestParam(required = false) Long endDate) {
        Date start = startDate != null ? new Date(startDate) : null;
        Date end = endDate != null ? new Date(endDate) : null;
        return aiAnalysisHistoryService.getErrorAnalysis(this.accountSetsId, start, end);
    }
}
