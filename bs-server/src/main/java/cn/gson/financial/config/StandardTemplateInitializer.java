package cn.gson.financial.config;

import cn.gson.financial.service.StandardInvoiceTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 标准模板初始化器
 * 在应用启动时初始化标准发票模板
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-13
 */
@Component
@Slf4j
public class StandardTemplateInitializer implements ApplicationRunner {

    @Autowired
    private StandardInvoiceTemplateService standardInvoiceTemplateService;

    @Autowired
    private cn.gson.financial.kernel.service.CacheService cacheService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            log.info("开始初始化标准发票模板...");

            // 清除所有字段映射模板缓存，确保使用最新模板
            log.info("清除所有字段映射模板缓存...");
            cacheService.clearAllFieldMappingTemplateCache();

            standardInvoiceTemplateService.initializeStandardTemplates();
            log.info("标准发票模板初始化完成");
        } catch (Exception e) {
            log.error("标准发票模板初始化失败", e);
        }
    }
}
