package cn.gson.financial.service;

import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.entity.FieldMappingTemplate;
import cn.gson.financial.kernel.model.mapper.BankReceiptsMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 银行回单数据修复服务
 * 用于修复"未知收款人"等数据质量问题
 */
@Service
@Slf4j
public class BankReceiptDataRepairService {

    @Autowired
    private BankReceiptsMapper bankReceiptsMapper;

    @Autowired
    private FieldMappingTemplateService fieldMappingTemplateService;

    /**
     * 修复指定账套的"未知收款人"数据
     * @param accountSetsId 账套ID
     * @return 修复的记录数
     */
    public int repairUnknownPayeeData(Integer accountSetsId) {
        log.info("开始修复账套 {} 的未知收款人数据", accountSetsId);
        
        // 查询所有"未知收款人"的记录
        LambdaQueryWrapper<BankReceipts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BankReceipts::getAccountSetsId, accountSetsId);

        // 构建OR条件：支出类型的收款人为空 OR 收入类型的付款人为空
        queryWrapper.and(wrapper -> wrapper
            // 支出类型：收款人为空需要修复
            .eq(BankReceipts::getType, "支出")
            .isNull(BankReceipts::getPayeeName)
            .or()
            // 收入类型：付款人为空需要修复
            .eq(BankReceipts::getType, "收入")
            .isNull(BankReceipts::getPayerName)
        );
        
        List<BankReceipts> unknownReceipts = bankReceiptsMapper.selectList(queryWrapper);
        log.info("找到 {} 条未知收款人记录", unknownReceipts.size());
        
        int repairedCount = 0;
        for (BankReceipts receipt : unknownReceipts) {
            try {
                if (repairSingleReceipt(receipt)) {
                    repairedCount++;
                }
            } catch (Exception e) {
                log.error("修复记录失败，ID: {}, 错误: {}", receipt.getId(), e.getMessage(), e);
            }
        }
        
        log.info("修复完成，成功修复 {} 条记录", repairedCount);
        return repairedCount;
    }

    /**
     * 修复指定的记录列表
     * @param accountSetsId 账套ID
     * @param receiptIds 要修复的记录ID列表
     * @return 修复的记录数
     */
    public int repairSpecificRecords(Integer accountSetsId, List<Long> receiptIds) {
        log.info("开始修复账套 {} 的指定记录，共 {} 条", accountSetsId, receiptIds.size());

        int repairedCount = 0;
        for (Long receiptId : receiptIds) {
            try {
                // 查询记录
                BankReceipts receipt = bankReceiptsMapper.selectById(receiptId);
                if (receipt == null) {
                    log.warn("记录不存在，ID: {}", receiptId);
                    continue;
                }

                // 验证账套ID
                if (!accountSetsId.equals(receipt.getAccountSetsId())) {
                    log.warn("记录 {} 不属于当前账套 {}，跳过修复", receiptId, accountSetsId);
                    continue;
                }

                // 检查是否需要修复
                boolean needsRepair = false;
                if ("支出".equals(receipt.getType()) && !StringUtils.hasText(receipt.getPayeeName())) {
                    needsRepair = true;
                } else if ("收入".equals(receipt.getType()) && !StringUtils.hasText(receipt.getPayerName())) {
                    needsRepair = true;
                }

                if (!needsRepair) {
                    log.info("记录 {} 不需要修复，跳过", receiptId);
                    continue;
                }

                // 执行修复
                if (repairSingleReceipt(receipt)) {
                    repairedCount++;
                }
            } catch (Exception e) {
                log.error("修复记录失败，ID: {}, 错误: {}", receiptId, e.getMessage(), e);
            }
        }

        log.info("批量修复完成，成功修复 {} 条记录（共请求修复 {} 条）", repairedCount, receiptIds.size());
        return repairedCount;
    }

    /**
     * 修复单条记录
     * @param receipt 银行回单记录
     * @return 是否修复成功
     */
    private boolean repairSingleReceipt(BankReceipts receipt) {
        String ocrInfo = receipt.getOcrRecognitionInfo();
        if (!StringUtils.hasText(ocrInfo)) {
            log.warn("记录 {} 没有OCR识别信息，跳过修复", receipt.getId());
            return false;
        }

        try {
            // 解析OCR识别信息
            JSONObject ocrData = JSON.parseObject(ocrInfo);

            // 使用字段映射模板重新映射
            FieldMappingTemplate template = fieldMappingTemplateService.findMatchingTemplate(
                ocrData, receipt.getAccountSetsId(), "BANK_RECEIPT"
            );

            JSONObject mappedData;
            if (template != null) {
                mappedData = fieldMappingTemplateService.applyTemplate(ocrData, template);
                log.info("使用模板重新映射字段，模板: {}", template.getTemplateName());
            } else {
                mappedData = ocrData;
                log.info("未找到匹配模板，使用原始OCR数据");
            }

            boolean updated = false;

            // 根据交易类型修复对应字段（使用直接从OCR数据提取的方法）
            if ("支出".equals(receipt.getType())) {
                // 支出类型：修复收款人 - 直接从OCR数据中提取
                String payeeName = extractPayeeNameDirectly(ocrData);
                if (StringUtils.hasText(payeeName)) {
                    String oldPayeeName = receipt.getPayeeName();
                    receipt.setPayeeName(payeeName);
                    updated = true;
                    log.info("修复收款人：{} -> {}", oldPayeeName, payeeName);
                }
            } else if ("收入".equals(receipt.getType())) {
                // 收入类型：修复付款人 - 直接从OCR数据中提取
                String payerName = extractPayerNameDirectly(ocrData);
                if (StringUtils.hasText(payerName)) {
                    String oldPayerName = receipt.getPayerName();
                    receipt.setPayerName(payerName);
                    updated = true;
                    log.info("修复付款人：{} -> {}", oldPayerName, payerName);
                }
            }

            // 同时修复其他可能缺失的字段
            updateOtherFields(receipt, mappedData);

            // 只要能提取到数据就认为修复成功，即使字段原本不为空
            if (updated) {
                bankReceiptsMapper.updateById(receipt);
                log.info("成功修复记录 ID: {}, 回单编号: {}", receipt.getId(), receipt.getReceiptsNo());
                return true;
            } else {
                log.info("未能从OCR数据中提取到有效信息，记录ID: {}", receipt.getId());
                return false;
            }

        } catch (Exception e) {
            log.error("解析OCR数据失败，记录ID: {}, 错误: {}", receipt.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 直接从OCR数据中提取收款人姓名（绕过映射模板）
     */
    private String extractPayeeNameDirectly(JSONObject ocrData) {
        // 1. 首先从映射数据中获取（优先级最高）
        String payeeName = ocrData.getString("payeeName");
        if (StringUtils.hasText(payeeName) && !"未知收款人".equals(payeeName)) {
            log.info("从映射数据中提取收款人: {}", payeeName);
            return payeeName;
        }

        // 2. 直接从OCR原始数据中提取收款人信息
        payeeName = extractFromOcrData(ocrData, new String[]{
            "收款人", "收款方", "入账户名", "收款单位", "收款账户名称", "收款国库(银行)名称"
        });
        log.info("直接从OCR数据中提取收款人: {}", payeeName);

        // 3. 检查特殊业务类型
        if (!StringUtils.hasText(payeeName)) {
            payeeName = extractSpecialBusinessPayee(ocrData);
            if (StringUtils.hasText(payeeName)) {
                log.info("特殊业务类型，提取收款人: {}", payeeName);
            }
        }

        // 4. 如果还没有找到收款人，检查是否为银行收费类回单
        if (!StringUtils.hasText(payeeName)) {
            payeeName = extractBankNameAsPayee(ocrData);
            if (StringUtils.hasText(payeeName)) {
                log.info("银行收费类回单，使用银行名称作为收款人: {}", payeeName);
            }
        }

        return payeeName;
    }

    /**
     * 提取特殊业务类型的收款人
     */
    private String extractSpecialBusinessPayee(JSONObject ocrData) {
        String businessType = extractFromOcrData(ocrData, new String[]{"业务类型"});
        String summary = extractFromOcrData(ocrData, new String[]{"交易摘要", "摘要"});

        // 缴税业务
        if (StringUtils.hasText(businessType) && businessType.contains("缴税")) {
            String treasury = extractFromOcrData(ocrData, new String[]{"收款国库(银行)名称", "征收机关名称"});
            if (StringUtils.hasText(treasury)) {
                return treasury;
            }
            return "税务局";
        }

        // 代发工资/奖金业务
        if (StringUtils.hasText(businessType) && (businessType.contains("代发") || businessType.contains("工资"))) {
            if (StringUtils.hasText(summary) && (summary.contains("工资") || summary.contains("奖金"))) {
                return "员工";
            }
        }

        // 代发付费（手续费）
        if (StringUtils.hasText(businessType) && businessType.contains("代发付费")) {
            return "招商银行";
        }

        return null;
    }

    /**
     * 对于银行收费类回单，提取银行名称作为收款人
     */
    private String extractBankNameAsPayee(JSONObject ocrData) {
        // 检查是否为银行收费类回单
        String businessType = extractFromOcrData(ocrData, new String[]{"业务类型"});
        String summary = extractFromOcrData(ocrData, new String[]{"交易摘要", "摘要", "收费项目"});

        boolean isBankFee = false;
        if (StringUtils.hasText(businessType) && businessType.contains("收费")) {
            isBankFee = true;
        }
        if (StringUtils.hasText(summary) && (summary.contains("手续费") || summary.contains("服务费") || summary.contains("收费"))) {
            isBankFee = true;
        }

        if (isBankFee) {
            // 提取银行名称
            String bankName = extractFromOcrData(ocrData, new String[]{"机构", "银行", "company"});
            if (StringUtils.hasText(bankName)) {
                // 标准化银行名称
                if (bankName.contains("CHINA MERCHANTS BANK") || bankName.contains("招商银行")) {
                    return "招商银行";
                } else if (bankName.contains("ICBC") || bankName.contains("工商银行")) {
                    return "中国工商银行";
                } else if (bankName.contains("CCB") || bankName.contains("建设银行")) {
                    return "中国建设银行";
                } else if (bankName.contains("ABC") || bankName.contains("农业银行")) {
                    return "中国农业银行";
                } else if (bankName.contains("BOC") || bankName.contains("中国银行")) {
                    return "中国银行";
                } else {
                    return bankName;
                }
            }
        }

        return null;
    }

    /**
     * 直接从OCR数据中提取付款人姓名（绕过映射模板）
     */
    private String extractPayerNameDirectly(JSONObject ocrData) {
        // 直接从OCR原始数据中提取付款人信息
        String payerName = extractFromOcrData(ocrData, new String[]{
            "付款人", "付款方", "出账户名", "付款单位", "付款账户名称"
        });
        log.info("直接从OCR数据中提取付款人: {}", payerName);
        return payerName;
    }

    /**
     * 从映射数据中提取收款人姓名
     */
    private String extractPayeeName(JSONObject mappedData, JSONObject ocrData) {
        // 优先从映射数据中获取
        String payeeName = getStringValue(mappedData, "payeeName");
        log.info("从映射数据中提取收款人: {}", payeeName);
        if (StringUtils.hasText(payeeName) && !"未知收款人".equals(payeeName)) {
            return payeeName;
        }

        // 从原始OCR数据中提取
        String ocrPayeeName = extractFromOcrData(ocrData, new String[]{
            "收款人", "收款方", "入账户名", "收款单位", "收款账户名称"
        });
        log.info("从OCR数据中提取收款人: {}", ocrPayeeName);
        return ocrPayeeName;
    }

    /**
     * 从映射数据中提取付款人姓名
     */
    private String extractPayerName(JSONObject mappedData, JSONObject ocrData) {
        // 优先从映射数据中获取
        String payerName = getStringValue(mappedData, "payerName");
        if (StringUtils.hasText(payerName) && !"未知收款人".equals(payerName)) {
            return payerName;
        }

        // 从原始OCR数据中提取
        return extractFromOcrData(ocrData, new String[]{
            "付款人", "付款方", "出账户名", "付款单位", "付款账户名称"
        });
    }

    /**
     * 从OCR数据中提取指定字段
     */
    private String extractFromOcrData(JSONObject ocrData, String[] fieldNames) {
        if (ocrData == null) return null;
        
        // 遍历所有可能的字段名
        for (String fieldName : fieldNames) {
            String value = findValueInOcrData(ocrData, fieldName);
            if (StringUtils.hasText(value) && !"未知收款人".equals(value)) {
                return value;
            }
        }
        
        return null;
    }

    /**
     * 在OCR数据中查找指定字段的值
     */
    private String findValueInOcrData(JSONObject ocrData, String fieldName) {
        if (ocrData == null) return null;

        // 直接查找
        if (ocrData.containsKey(fieldName)) {
            String value = ocrData.getString(fieldName);
            if (StringUtils.hasText(value)) {
                return value;
            }
        }

        // 在ocrRecognitionInfo中查找（需要解析嵌套的JSON字符串）
        String ocrRecognitionInfo = ocrData.getString("ocrRecognitionInfo");
        if (StringUtils.hasText(ocrRecognitionInfo)) {
            try {
                JSONObject innerOcrData = JSON.parseObject(ocrRecognitionInfo);
                JSONObject recognitionData = innerOcrData.getJSONObject("recognitionData");
                if (recognitionData != null) {
                    // 遍历所有分类
                    for (String category : recognitionData.keySet()) {
                        JSONObject categoryData = recognitionData.getJSONObject(category);
                        if (categoryData != null && categoryData.containsKey(fieldName)) {
                            String value = categoryData.getString(fieldName);
                            if (StringUtils.hasText(value)) {
                                return value;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("解析ocrRecognitionInfo失败: {}", e.getMessage());
            }
        }

        // 在recognitionData中查找（外层）
        JSONObject recognitionData = ocrData.getJSONObject("recognitionData");
        if (recognitionData != null) {
            // 遍历所有分类
            for (String category : recognitionData.keySet()) {
                JSONObject categoryData = recognitionData.getJSONObject(category);
                if (categoryData != null && categoryData.containsKey(fieldName)) {
                    String value = categoryData.getString(fieldName);
                    if (StringUtils.hasText(value)) {
                        return value;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 更新其他字段
     */
    private void updateOtherFields(BankReceipts receipt, JSONObject mappedData) {
        // 更新收款账号
        String payeeAccount = getStringValue(mappedData, "payeeAccount");
        if (StringUtils.hasText(payeeAccount) && !StringUtils.hasText(receipt.getPayeeAccount())) {
            receipt.setPayeeAccount(payeeAccount);
        }

        // 更新付款账号
        String payerAccount = getStringValue(mappedData, "payerAccount");
        if (StringUtils.hasText(payerAccount) && !StringUtils.hasText(receipt.getPayerAccount())) {
            receipt.setPayerAccount(payerAccount);
        }

        // 更新收款银行
        String payeeBank = getStringValue(mappedData, "payeeBank");
        if (StringUtils.hasText(payeeBank) && !StringUtils.hasText(receipt.getPayeeBank())) {
            receipt.setPayeeBank(payeeBank);
        }

        // 更新付款银行
        String payerBank = getStringValue(mappedData, "payerBank");
        if (StringUtils.hasText(payerBank) && !StringUtils.hasText(receipt.getPayerBank())) {
            receipt.setPayerBank(payerBank);
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(JSONObject jsonObject, String key) {
        return jsonObject != null ? jsonObject.getString(key) : null;
    }

    /**
     * 修复指定记录ID的数据
     * @param receiptId 记录ID
     * @return 是否修复成功
     */
    public boolean repairSingleReceiptById(Integer receiptId) {
        BankReceipts receipt = bankReceiptsMapper.selectById(receiptId);
        if (receipt == null) {
            log.warn("记录不存在，ID: {}", receiptId);
            return false;
        }
        
        return repairSingleReceipt(receipt);
    }

    /**
     * 统计需要修复的记录数量
     */
    public int countUnknownPayeeRecords(Integer accountSetsId) {
        if (accountSetsId == null) {
            return 0;
        }

        LambdaQueryWrapper<BankReceipts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BankReceipts::getAccountSetsId, accountSetsId);

        // 查询字段为空的记录：支出类型的收款人为空 OR 收入类型的付款人为空
        queryWrapper.and(wrapper -> wrapper
            .nested(subWrapper -> subWrapper
                .eq(BankReceipts::getType, "支出")
                .isNull(BankReceipts::getPayeeName)
            )
            .or()
            .nested(subWrapper -> subWrapper
                .eq(BankReceipts::getType, "收入")
                .isNull(BankReceipts::getPayerName)
            )
        );

        return Math.toIntExact(bankReceiptsMapper.selectCount(queryWrapper));
    }


}
