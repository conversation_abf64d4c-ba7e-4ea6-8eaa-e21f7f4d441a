package cn.gson.financial.service;

import cn.gson.financial.kernel.service.AiService;
import cn.gson.financial.kernel.config.AiConfig;
import cn.gson.financial.kernel.model.entity.FieldMappingTemplate;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 智能字段映射服务
 * 负责OCR识别结果的智能字段映射，包括模板匹配和LLM映射
 */
@Service
@Slf4j
public class SmartFieldMappingService {

    @Autowired(required = false)
    private AiService aiService;

    @Autowired
    private AiConfig aiConfig;

    @Autowired(required = false)
    private FieldMappingTemplateService templateService;

    @Autowired
    private OcrService ocrService;

    /**
     * 智能映射银行回单字段
     * @param ocrData OCR识别的原始数据
     * @param accountSetsId 账套ID
     * @param userId 用户ID
     * @return 映射后的标准字段数据
     */
    public Map<String, Object> mapBankReceiptFields(Map<String, Object> ocrData, Integer accountSetsId, Integer userId) {
        if (ocrData == null || ocrData.isEmpty()) {
            log.warn("OCR数据为空，无法进行银行回单字段映射");
            return ocrData;
        }

        // 加载指定用户的AI配置
        if (userId != null && aiConfig != null) {
            aiConfig.loadFromDatabaseByUser(userId);
            log.info("已加载用户 {} 的AI配置", userId);
        }

        try {
            // 1. 首先尝试使用字段映射模板（包括系统级模板）
            if (templateService != null) {
                // 提取原始OCR数据用于模板匹配
                JSONObject ocrDataForTemplate = extractRawOcrData(ocrData);
                log.info("提取用于模板匹配的OCR数据，字段数: {}", ocrDataForTemplate.size());

                FieldMappingTemplate template = templateService.findMatchingTemplate(
                        ocrDataForTemplate, accountSetsId, "BANK_RECEIPT");

                if (template != null) {
                    log.info("找到匹配的银行回单字段映射模板: {}", template.getTemplateName());
                    JSONObject templateResult = templateService.applyTemplate(ocrDataForTemplate, template);
                    if (templateResult != null && !templateResult.isEmpty()) {
                        log.info("使用模板映射成功，映射字段数: {}", templateResult.size());
                        // 转换为Map<String, Object>
                        Map<String, Object> result = new HashMap<>();
                        for (Map.Entry<String, Object> entry : templateResult.entrySet()) {
                            result.put(entry.getKey(), entry.getValue());
                        }

                        // 添加OCR原始信息到模板映射结果中
                        @SuppressWarnings("unchecked")
                        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
                        if (rawData != null && ocrService != null) {
                            String readableOcrInfo = ocrService.formatOcrDataToReadableText(rawData, "BANK_RECEIPT");
                            result.put("ocrRecognitionInfo", readableOcrInfo);

                            String jsonOcrInfo = ocrService.formatOcrDataToJson(rawData);
                            result.put("ocrRecognitionInfoJson", jsonOcrInfo);
                        }

                        return result;
                    }
                }
            }

            // 2. 如果没有找到模板或模板应用失败，使用LLM映射
            log.info("未找到匹配模板，使用LLM进行银行回单字段映射");

            // 如果AI服务不可用，使用原始数据
            if (aiService == null || !aiService.isAvailable()) {
                log.warn("AI服务不可用，使用原始数据");
                return enhanceOcrDataWithRawInfo(ocrData);
            }

            // 构建LLM提示词
            String prompt = buildBankReceiptFieldMappingPrompt(ocrData);

            // 调用LLM进行字段映射
            Map<String, Object> aiParameters = new HashMap<>();
            aiParameters.put("temperature", 0.1); // 使用较低的温度以获得更稳定的结果
            aiParameters.put("max_tokens", 1000);

            String aiResponse = aiService.chat(prompt, aiParameters);

            if (aiResponse != null && !aiResponse.trim().isEmpty()) {
                // 解析AI响应
                Map<String, Object> mappedData = parseAiMappingResponse(aiResponse);
                if (mappedData != null && !mappedData.isEmpty()) {
                    log.info("AI银行回单字段映射成功，映射字段数: {}", mappedData.size());

                    // 添加OCR原始信息到映射结果中
                    @SuppressWarnings("unchecked")
                    Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
                    if (rawData != null && ocrService != null) {
                        String readableOcrInfo = ocrService.formatOcrDataToReadableText(rawData, "BANK_RECEIPT");
                        mappedData.put("ocrRecognitionInfo", readableOcrInfo);

                        String jsonOcrInfo = ocrService.formatOcrDataToJson(rawData);
                        mappedData.put("ocrRecognitionInfoJson", jsonOcrInfo);
                    }

                    // 3. 保存成功的映射结果为模板（如果有accountSetsId和templateService）
                    if (templateService != null && accountSetsId != null && userId != null) {
                        try {
                            JSONObject ocrDataJson = new JSONObject(ocrData);
                            JSONObject mappedDataJson = new JSONObject(mappedData);
                            templateService.createTemplate(ocrDataJson, mappedDataJson, accountSetsId, "BANK_RECEIPT", userId);
                            log.info("已保存银行回单字段映射模板，账套ID: {}", accountSetsId);
                        } catch (Exception e) {
                            log.warn("保存银行回单字段映射模板失败", e);
                        }
                    }

                    return mappedData;
                }
            }

            log.warn("AI银行回单字段映射失败，使用原始数据");
            return enhanceOcrDataWithRawInfo(ocrData);

        } catch (Exception e) {
            log.error("AI银行回单字段映射异常，使用原始数据", e);
            return enhanceOcrDataWithRawInfo(ocrData);
        }
    }

    /**
     * 智能映射发票字段
     * @param ocrData OCR识别的原始数据
     * @param accountSetsId 账套ID
     * @param userId 用户ID
     * @return 映射后的标准字段数据
     */
    public Map<String, Object> mapInvoiceFields(Map<String, Object> ocrData, Integer accountSetsId, Integer userId) {
        if (ocrData == null || ocrData.isEmpty()) {
            log.warn("OCR数据为空，无法进行发票字段映射");
            return ocrData;
        }

        // 加载指定用户的AI配置
        if (userId != null && aiConfig != null) {
            aiConfig.loadFromDatabaseByUser(userId);
            log.info("已加载用户 {} 的AI配置", userId);
        }

        try {
            // 1. 首先尝试使用字段映射模板（包括系统级模板）
            if (templateService != null) {
                // 提取原始OCR数据用于模板匹配
                JSONObject ocrDataForTemplate = extractRawOcrData(ocrData);
                log.info("提取用于模板匹配的OCR数据，字段数: {}", ocrDataForTemplate.size());

                FieldMappingTemplate template = templateService.findMatchingTemplate(
                        ocrDataForTemplate, accountSetsId, "INVOICE");

                if (template != null) {
                    log.info("找到匹配的发票字段映射模板: {}", template.getTemplateName());
                    JSONObject templateResult = templateService.applyTemplate(ocrDataForTemplate, template);
                    if (templateResult != null && !templateResult.isEmpty()) {
                        log.info("使用模板映射成功，映射字段数: {}", templateResult.size());
                        // 转换为Map<String, Object>
                        Map<String, Object> result = new HashMap<>();
                        for (Map.Entry<String, Object> entry : templateResult.entrySet()) {
                            result.put(entry.getKey(), entry.getValue());
                        }

                        // 添加OCR原始信息到发票模板映射结果中
                        @SuppressWarnings("unchecked")
                        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
                        if (rawData != null && ocrService != null) {
                            String readableOcrInfo = ocrService.formatOcrDataToReadableText(rawData, "INVOICE");
                            result.put("ocrRecognitionInfo", readableOcrInfo);

                            String jsonOcrInfo = ocrService.formatOcrDataToJson(rawData);
                            result.put("ocrRecognitionInfoJson", jsonOcrInfo);
                        }

                        return result;
                    }
                }
            }

            // 2. 如果没有找到模板或模板应用失败，使用LLM映射
            log.info("未找到匹配模板，使用LLM进行发票字段映射");

            // 如果AI服务不可用，返回增强的原始数据
            if (aiService == null || !aiService.isAvailable()) {
                log.warn("AI服务不可用，使用原始数据");
                return enhanceOcrDataWithRawInfo(ocrData, "INVOICE");
            }

            // 构建LLM提示词
            String prompt = buildInvoiceFieldMappingPrompt(ocrData);

            // 调用LLM进行字段映射
            Map<String, Object> aiParameters = new HashMap<>();
            aiParameters.put("temperature", 0.1); // 使用较低的温度以获得更稳定的结果
            aiParameters.put("max_tokens", 1000);

            String aiResponse = aiService.chat(prompt, aiParameters);

            if (aiResponse != null && !aiResponse.trim().isEmpty()) {
                // 解析AI响应
                Map<String, Object> mappedData = parseAiMappingResponse(aiResponse);
                if (mappedData != null && !mappedData.isEmpty()) {
                    log.info("AI发票字段映射成功，映射字段数: {}", mappedData.size());

                    // 添加OCR原始信息到发票AI映射结果中
                    @SuppressWarnings("unchecked")
                    Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
                    if (rawData != null && ocrService != null) {
                        String readableOcrInfo = ocrService.formatOcrDataToReadableText(rawData, "INVOICE");
                        mappedData.put("ocrRecognitionInfo", readableOcrInfo);

                        String jsonOcrInfo = ocrService.formatOcrDataToJson(rawData);
                        mappedData.put("ocrRecognitionInfoJson", jsonOcrInfo);
                    }

                    // 3. 保存成功的映射结果为模板（如果有accountSetsId和templateService）
                    if (templateService != null && accountSetsId != null && userId != null) {
                        try {
                            JSONObject ocrDataJson = new JSONObject(ocrData);
                            JSONObject mappedDataJson = new JSONObject(mappedData);
                            templateService.createTemplate(ocrDataJson, mappedDataJson, accountSetsId, "INVOICE", userId);
                            log.info("已保存发票字段映射模板，账套ID: {}", accountSetsId);
                        } catch (Exception e) {
                            log.warn("保存发票字段映射模板失败", e);
                        }
                    }

                    return mappedData;
                }
            }

            log.warn("AI发票字段映射失败，使用原始数据");
            return enhanceOcrDataWithRawInfo(ocrData, "INVOICE");

        } catch (Exception e) {
            log.error("AI发票字段映射异常，使用原始数据", e);
            return enhanceOcrDataWithRawInfo(ocrData, "INVOICE");
        }
    }

    /**
     * 增强OCR数据，添加原始信息
     */
    private Map<String, Object> enhanceOcrDataWithRawInfo(Map<String, Object> ocrData) {
        return enhanceOcrDataWithRawInfo(ocrData, "BANK_RECEIPT");
    }

    /**
     * 增强OCR数据，添加原始信息（支持指定文档类型）
     */
    private Map<String, Object> enhanceOcrDataWithRawInfo(Map<String, Object> ocrData, String documentType) {
        @SuppressWarnings("unchecked")
        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
        if (rawData != null && ocrService != null) {
            // 使用统一的OCR格式化方法生成易读格式
            String readableOcrInfo = ocrService.formatOcrDataToReadableText(rawData, documentType);
            ocrData.put("ocrRecognitionInfo", readableOcrInfo);

            // 同时保存JSON格式（用于程序处理）
            String jsonOcrInfo = ocrService.formatOcrDataToJson(rawData);
            ocrData.put("ocrRecognitionInfoJson", jsonOcrInfo);

            // 如果没有备注信息，生成简化的备注
            if (!ocrData.containsKey("remark") || ocrData.get("remark") == null) {
                String formattedRemark = formatOcrDataForRemark(rawData);
                ocrData.put("remark", formattedRemark);
            }
        }
        return ocrData;
    }

    /**
     * 格式化OCR数据为简化备注信息
     */
    private String formatOcrDataForRemark(Map<String, String> rawData) {
        StringBuilder remark = new StringBuilder();
        remark.append("OCR识别信息：\n");
        for (Map.Entry<String, String> entry : rawData.entrySet()) {
            remark.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }
        return remark.toString();
    }

    /**
     * 构建银行回单字段映射的LLM提示词
     */
    private String buildBankReceiptFieldMappingPrompt(Map<String, Object> ocrData) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个银行回单字段映射专家。请将OCR识别的银行回单字段映射到标准的数据库字段。\n\n");

        prompt.append("标准字段定义：\n");
        prompt.append("- receipt_title: 回单标题/类型（如：支付转账、代发工资、汇总收费扣款等）\n");
        prompt.append("- amount: 金额（数字，如：1000.50）\n");
        prompt.append("- payer_name: 付款人姓名\n");
        prompt.append("- payer_account: 付款人账号\n");
        prompt.append("- payer_bank: 付款人开户行\n");
        prompt.append("- payee_name: 收款人姓名\n");
        prompt.append("- payee_account: 收款人账号\n");
        prompt.append("- payee_bank: 收款人开户行\n");
        prompt.append("- serial_number: 流水号/交易序号\n");
        prompt.append("- transaction_institution: 交易机构（如：交通银行、工商银行等）\n");
        prompt.append("- amount_in_words: 金额大写\n");
        prompt.append("- summary: 摘要/用途（必填字段）\n");
        prompt.append("- receipts_date: 回单日期（格式：yyyy-MM-dd）\n");
        prompt.append("- transfer_date: 转账日期（格式：yyyy-MM-dd，必填字段）\n");
        prompt.append("- quantity: 张数（默认为1）\n");
        prompt.append("- type: 类型（收入/支出，必填字段，根据单据内容智能判断）\n\n");

        // 获取原始OCR数据用于显示
        @SuppressWarnings("unchecked")
        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
        if (rawData != null && !rawData.isEmpty()) {
            prompt.append("OCR识别的原始数据：\n");
            for (Map.Entry<String, String> entry : rawData.entrySet()) {
                prompt.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }
        } else {
            prompt.append("OCR识别的原始数据：\n");
            try {
                prompt.append(JSON.toJSONString(ocrData, true)).append("\n");
            } catch (Exception e) {
                prompt.append(ocrData.toString()).append("\n");
            }
        }

        prompt.append("\n请分析上述OCR数据，将其映射到标准字段。注意：\n");
        prompt.append("1. 不同银行的字段名称可能不同，请根据语义进行映射\n");
        prompt.append("2. 金额字段请转换为数字格式，去除逗号等符号\n");
        prompt.append("3. 日期字段请转换为yyyy-MM-dd格式\n");
        prompt.append("4. transfer_date（转账日期）是必填字段，如果OCR数据中没有明确的转账日期，可以使用交易日期、业务日期等相关日期\n");
        prompt.append("5. summary（摘要）是必填字段，如果OCR数据中没有明确的摘要，可以根据业务类型生成合适的摘要\n");
        prompt.append("6. quantity（张数）默认为1\n");
        prompt.append("7. type（类型）是必填字段，请根据单据内容智能判断：\n");
        prompt.append("   - 如果是转入、收款、存款、贷方发生额等，判断为\"收入\"\n");
        prompt.append("   - 如果是转出、付款、支付、借方发生额等，判断为\"支出\"\n");
        prompt.append("   - 根据回单标题、业务类型、摘要等综合判断收入支出性质\n");
        prompt.append("8. 对于\"对方户名\"字段，请根据交易类型智能判断：\n");
        prompt.append("   - 如果是出账/支出类型，对方户名通常是收款人(payee_name)\n");
        prompt.append("   - 如果是入账/收入类型，对方户名通常是付款人(payer_name)\n");
        prompt.append("   - 请结合回单类型、业务类型等信息综合判断\n");
        prompt.append("9. 如果某个非必填字段在OCR数据中找不到对应值，请设为null\n");
        prompt.append("10. 请只返回JSON格式的映射结果，不要包含其他文字说明\n\n");

        prompt.append("返回格式示例：\n");
        prompt.append("{\n");
        prompt.append("  \"receipt_title\": \"支付转账\",\n");
        prompt.append("  \"amount\": 1000.50,\n");
        prompt.append("  \"payer_name\": \"张三\",\n");
        prompt.append("  \"payer_account\": \"****************\",\n");
        prompt.append("  \"payee_name\": \"李四\",\n");
        prompt.append("  \"payee_account\": \"****************\",\n");
        prompt.append("  \"summary\": \"转账备注\",\n");
        prompt.append("  \"receipts_date\": \"2025-06-29\",\n");
        prompt.append("  \"transfer_date\": \"2025-06-29\",\n");
        prompt.append("  \"quantity\": 1,\n");
        prompt.append("  \"type\": \"支出\"\n");
        prompt.append("}\n");

        return prompt.toString();
    }

    /**
     * 构建发票字段映射的LLM提示词
     */
    private String buildInvoiceFieldMappingPrompt(Map<String, Object> ocrData) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请将以下增值税发票OCR识别结果映射到标准字段格式。\n\n");

        prompt.append("标准字段格式（JSON）：\n");
        prompt.append("- type: 票据类型（如：发票、收据等）\n");
        prompt.append("- amount: 金额（数字，必填字段）\n");
        prompt.append("- issuer: 开票方/销售方名称\n");
        prompt.append("- recipient: 收票方/购买方名称\n");
        prompt.append("- invoice_number: 发票号码\n");
        prompt.append("- bill_date: 开票日期（格式：yyyy-MM-dd）\n");
        prompt.append("- summary: 摘要/用途（必填字段）\n");
        prompt.append("- tax_rate: 税率（数字）\n");
        prompt.append("- total_tax_amount: 合计税额（数字）\n");
        prompt.append("- amount_in_words: 大写金额\n");
        prompt.append("- remark: 备注信息\n\n");

        // 获取原始OCR数据用于显示
        @SuppressWarnings("unchecked")
        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
        if (rawData != null && !rawData.isEmpty()) {
            prompt.append("OCR识别的原始数据：\n");
            for (Map.Entry<String, String> entry : rawData.entrySet()) {
                prompt.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }
        } else {
            prompt.append("OCR识别的原始数据：\n");
            try {
                prompt.append(JSON.toJSONString(ocrData, true)).append("\n");
            } catch (Exception e) {
                prompt.append(ocrData.toString()).append("\n");
            }
        }

        prompt.append("\n请分析上述OCR数据，将其映射到标准字段。注意：\n");
        prompt.append("1. amount（金额）是必填字段，请转换为数字格式，去除逗号等符号\n");
        prompt.append("2. bill_date（开票日期）请转换为yyyy-MM-dd格式\n");
        prompt.append("3. summary（摘要）是必填字段，如果OCR数据中没有明确的摘要，可以根据商品名称或发票类型生成合适的摘要\n");
        prompt.append("4. type（票据类型）请设置为\"发票\"\n");
        prompt.append("5. issuer（开票方）通常是销售方名称\n");
        prompt.append("6. recipient（收票方）通常是购买方名称\n");
        prompt.append("7. tax_rate（税率）和total_tax_amount（税额）请转换为数字格式\n");
        prompt.append("8. 如果某个非必填字段在OCR数据中找不到对应值，请设为null\n");
        prompt.append("9. 请只返回JSON格式的映射结果，不要包含其他文字说明\n\n");

        prompt.append("返回格式示例：\n");
        prompt.append("{\n");
        prompt.append("  \"type\": \"发票\",\n");
        prompt.append("  \"amount\": 1130.00,\n");
        prompt.append("  \"issuer\": \"北京京东金禾贸易有限公司\",\n");
        prompt.append("  \"recipient\": \"北京征马科技有限公司\",\n");
        prompt.append("  \"invoice_number\": \"25117000000046260969\",\n");
        prompt.append("  \"bill_date\": \"2025-01-15\",\n");
        prompt.append("  \"summary\": \"商品销售\",\n");
        prompt.append("  \"tax_rate\": 0.13,\n");
        prompt.append("  \"total_tax_amount\": 130.00,\n");
        prompt.append("  \"amount_in_words\": \"壹仟壹佰叁拾元整\"\n");
        prompt.append("}\n");

        return prompt.toString();
    }

    /**
     * 解析AI映射响应
     */
    private Map<String, Object> parseAiMappingResponse(String aiResponse) {
        try {
            // 清理响应文本，提取JSON部分
            String jsonStr = extractJsonFromResponse(aiResponse);
            if (jsonStr != null) {
                return JSON.parseObject(jsonStr, Map.class);
            }
            return null;
        } catch (Exception e) {
            log.error("解析AI映射响应失败: {}", aiResponse, e);
            return null;
        }
    }

    /**
     * 从AI响应中提取JSON字符串
     */
    private String extractJsonFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return null;
        }

        // 查找JSON开始和结束位置
        int startIndex = response.indexOf('{');
        int endIndex = response.lastIndexOf('}');

        if (startIndex >= 0 && endIndex > startIndex) {
            return response.substring(startIndex, endIndex + 1);
        }

        return null;
    }

    /**
     * 提取原始OCR数据用于模板匹配
     * @param ocrData OCR识别结果数据
     * @return 原始OCR字段数据
     */
    private JSONObject extractRawOcrData(Map<String, Object> ocrData) {
        // 如果数据中包含rawOcrData字段，使用它
        if (ocrData.containsKey("rawOcrData")) {
            Object rawData = ocrData.get("rawOcrData");
            if (rawData instanceof Map) {
                return new JSONObject((Map<String, Object>) rawData);
            }
        }

        // 否则直接使用传入的数据
        return new JSONObject(ocrData);
    }
}
