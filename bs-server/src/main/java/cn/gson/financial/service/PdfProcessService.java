package cn.gson.financial.service;

import cn.gson.financial.kernel.aliyuncs.OssService;
import cn.gson.financial.kernel.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.concurrent.Executor;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * PDF处理服务
 * 负责PDF文件的解析、页面提取和转换为图片
 */
@Service
@Slf4j
public class PdfProcessService {
    
    @Resource
    private OssService ossService;

    @Resource
    private ImageSplitService imageSplitService;

    // 创建线程池用于并行处理文件拆分和转换
    private final ExecutorService fileProcessExecutor = Executors.newFixedThreadPool(8);

    @Resource
    @Qualifier("imageSplitExecutor")
    private Executor imageSplitExecutor;
    
    /**
     * 处理上传的文件，支持PDF和图片（原有的顺序处理方法）
     * @param files 上传的文件数组
     * @param folder 存储文件夹
     * @param receiptsPerPage 单页回单数量（仅对银行回单有效）
     * @return 处理结果列表
     */
    public List<Map<String, Object>> processUploadedFiles(MultipartFile[] files, String folder, Integer receiptsPerPage) {
        // 使用并行处理方法
        return processUploadedFilesParallel(files, folder, receiptsPerPage);
    }

    /**
     * 并行处理上传的文件，支持PDF和图片
     * @param files 上传的文件数组
     * @param folder 存储文件夹
     * @param receiptsPerPage 单页回单数量（仅对银行回单有效）
     * @return 处理结果列表
     */
    public List<Map<String, Object>> processUploadedFilesParallel(MultipartFile[] files, String folder, Integer receiptsPerPage) {
        log.info("开始并行处理 {} 个文件", files.length);

        // 创建并行任务
        List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();

        for (MultipartFile file : files) {
            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    log.info("开始处理文件: {}", file.getOriginalFilename());
                    Map<String, Object> result = processSingleFile(file, folder, receiptsPerPage);
                    log.info("文件处理完成: {}", file.getOriginalFilename());
                    return result;
                } catch (Exception e) {
                    log.error("处理文件失败: {}", file.getOriginalFilename(), e);
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("fileName", file.getOriginalFilename());
                    errorResult.put("fileType", "UNKNOWN");
                    errorResult.put("status", "FAILED");
                    errorResult.put("error", e.getMessage());
                    errorResult.put("images", new ArrayList<>());
                    return errorResult;
                }
            }, fileProcessExecutor);

            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        List<Map<String, Object>> results = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());

        log.info("并行处理完成，共处理 {} 个文件", results.size());
        return results;
    }

    /**
     * 批量处理临时文件（并行处理）
     * @param tempFiles 临时文件列表
     * @param folder 存储文件夹
     * @param receiptsPerPage 单页回单数量
     * @return 处理结果列表
     */
    public List<Map<String, Object>> processUploadedTempFiles(List<java.io.File> tempFiles, String folder, Integer receiptsPerPage) {
        log.info("开始并行处理 {} 个临时文件", tempFiles.size());

        // 创建并行任务
        List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();

        for (java.io.File tempFile : tempFiles) {
            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    log.info("开始处理临时文件: {}", tempFile.getName());
                    Map<String, Object> result = processSingleTempFile(tempFile, folder, receiptsPerPage);
                    log.info("临时文件处理完成: {}", tempFile.getName());
                    return result;
                } catch (Exception e) {
                    log.error("处理临时文件失败: {}", tempFile.getName(), e);
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("fileName", tempFile.getName());
                    errorResult.put("error", e.getMessage());
                    errorResult.put("success", false);
                    return errorResult;
                }
            }, fileProcessExecutor);

            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        List<Map<String, Object>> results = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());

        log.info("并行处理临时文件完成，共处理 {} 个文件", results.size());
        return results;
    }

    /**
     * 处理单个临时文件
     * @param tempFile 临时文件
     * @param folder 存储文件夹
     * @param receiptsPerPage 单页回单数量
     * @return 处理结果
     */
    private Map<String, Object> processSingleTempFile(java.io.File tempFile, String folder, Integer receiptsPerPage) throws IOException {
        Map<String, Object> result = new HashMap<>();
        String fileName = tempFile.getName();
        String fileExtension = getFileExtension(fileName).toLowerCase();

        result.put("fileName", fileName);
        result.put("fileSize", tempFile.length());

        // 先上传原始文件
        String originalFileUrl = ossService.uploadFile(tempFile, folder + "/originals");
        result.put("originalFileUrl", originalFileUrl);

        List<Map<String, Object>> images = new ArrayList<>();

        if ("pdf".equals(fileExtension)) {
            // 处理PDF文件
            images = processPdfFile(tempFile, folder, receiptsPerPage);
            result.put("fileType", "PDF");
        } else if (isImageFile(fileExtension)) {
            // 处理图片文件
            images = processImageFile(tempFile, folder, receiptsPerPage);
            result.put("fileType", "IMAGE");
        } else {
            throw new ServiceException("不支持的文件类型: " + fileExtension);
        }

        result.put("images", images);
        result.put("imageCount", images.size());
        result.put("success", true);

        return result;
    }

    /**
     * 处理PDF文件（File版本）
     * @param pdfFile PDF文件
     * @param folder 存储文件夹
     * @param receiptsPerPage 单页回单数量
     * @return 图片信息列表
     */
    private List<Map<String, Object>> processPdfFile(java.io.File pdfFile, String folder, Integer receiptsPerPage) throws IOException {
        List<Map<String, Object>> images = new ArrayList<>();

        try (PDDocument document = PDDocument.load(pdfFile)) {
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            int pageCount = document.getNumberOfPages();

            for (int page = 0; page < pageCount; page++) {
                // 渲染PDF页面为图片
                BufferedImage bufferedImage = pdfRenderer.renderImageWithDPI(page, 300, ImageType.RGB);

                // 将BufferedImage转换为字节数组
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(bufferedImage, "PNG", baos);
                byte[] imageBytes = baos.toByteArray();

                // 创建临时文件用于上传
                String imageName = getFileNameWithoutExtension(pdfFile.getName()) + "_page_" + (page + 1) + ".png";
                java.io.File tempImageFile = java.io.File.createTempFile("pdf_page_", ".png");
                java.io.FileOutputStream fos = new java.io.FileOutputStream(tempImageFile);
                fos.write(imageBytes);
                fos.close();

                // 上传图片到OSS
                String imageUrl = ossService.uploadFile(tempImageFile, folder + "/images");

                // 删除临时文件
                tempImageFile.delete();

                // 检查是否需要拆分图片（针对银行回单）
                if (folder.contains("bank-receipts")) {
                    // 尝试拆分银行回单图片
                    List<Map<String, Object>> splitImages = imageSplitService.splitBankReceiptImage(imageUrl, folder, receiptsPerPage);
                    if (!splitImages.isEmpty()) {
                        images.addAll(splitImages);
                        continue;
                    }
                }

                // 如果没有拆分，添加原始图片信息
                Map<String, Object> imageInfo = new HashMap<>();
                imageInfo.put("pageNumber", page + 1);
                imageInfo.put("imageUrl", imageUrl);
                imageInfo.put("imageName", imageName);
                imageInfo.put("width", bufferedImage.getWidth());
                imageInfo.put("height", bufferedImage.getHeight());
                images.add(imageInfo);
            }
        }

        return images;
    }

    /**
     * 处理图片文件（File版本）
     * @param imageFile 图片文件
     * @param folder 存储文件夹
     * @param receiptsPerPage 单页回单数量
     * @return 图片信息列表
     */
    private List<Map<String, Object>> processImageFile(java.io.File imageFile, String folder, Integer receiptsPerPage) throws IOException {
        List<Map<String, Object>> images = new ArrayList<>();

        // 上传图片文件
        String imageUrl = ossService.uploadFile(imageFile, folder + "/images");

        // 获取图片尺寸
        BufferedImage bufferedImage = ImageIO.read(imageFile);

        // 检查是否需要拆分图片（针对银行回单）
        if (folder.contains("bank-receipts")) {
            // 尝试拆分银行回单图片
            List<Map<String, Object>> splitImages = imageSplitService.splitBankReceiptImage(imageUrl, folder, receiptsPerPage);
            if (!splitImages.isEmpty()) {
                return splitImages;
            }
        }

        // 如果没有拆分，添加原始图片信息
        Map<String, Object> imageInfo = new HashMap<>();
        imageInfo.put("pageNumber", 1);
        imageInfo.put("imageUrl", imageUrl);
        imageInfo.put("imageName", imageFile.getName());
        imageInfo.put("width", bufferedImage.getWidth());
        imageInfo.put("height", bufferedImage.getHeight());
        images.add(imageInfo);

        return images;
    }
    
    /**
     * 处理单个文件
     * @param file 文件
     * @param folder 存储文件夹
     * @param receiptsPerPage 单页回单数量
     * @return 处理结果
     */
    private Map<String, Object> processSingleFile(MultipartFile file, String folder, Integer receiptsPerPage) throws IOException {
        Map<String, Object> result = new HashMap<>();
        String fileName = file.getOriginalFilename();
        String fileExtension = getFileExtension(fileName).toLowerCase();
        
        result.put("fileName", fileName);
        result.put("fileSize", file.getSize());
        
        // 先上传原始文件
        String originalFileUrl = ossService.uploadFile(file, folder + "/originals");
        result.put("originalFileUrl", originalFileUrl);
        
        List<Map<String, Object>> images = new ArrayList<>();
        
        if ("pdf".equals(fileExtension)) {
            // 处理PDF文件
            result.put("fileType", "PDF");
            images = convertPdfToImages(file, folder, receiptsPerPage);
        } else if (isImageFile(fileExtension)) {
            // 处理图片文件
            result.put("fileType", "IMAGE");
            List<Map<String, Object>> imageInfos = processImageFile(file, folder, receiptsPerPage);
            images.addAll(imageInfos);
        } else {
            throw new ServiceException("不支持的文件类型: " + fileExtension);
        }
        
        result.put("images", images);
        result.put("totalPages", images.size());
        result.put("status", "SUCCESS");
        
        return result;
    }
    
    /**
     * 将PDF转换为图片列表（并行处理版本）
     * @param pdfFile PDF文件
     * @param folder 存储文件夹
     * @return 图片信息列表
     */
    public List<Map<String, Object>> convertPdfToImages(MultipartFile pdfFile, String folder, Integer receiptsPerPage) throws IOException {
        try (PDDocument document = PDDocument.load(pdfFile.getInputStream())) {
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            int pageCount = document.getNumberOfPages();

            log.info("PDF文件 {} 共有 {} 页，开始并行转换", pdfFile.getOriginalFilename(), pageCount);

            // 创建并行任务列表
            List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>();

            for (int page = 0; page < pageCount; page++) {
                final int currentPage = page;
                CompletableFuture<List<Map<String, Object>>> future = CompletableFuture.supplyAsync(() -> {
                    return processPdfPage(pdfRenderer, currentPage, pdfFile.getOriginalFilename(), folder, receiptsPerPage);
                }, imageSplitExecutor);

                futures.add(future);
            }

            // 等待所有页面处理完成并收集结果
            List<Map<String, Object>> allImages = new ArrayList<>();
            for (CompletableFuture<List<Map<String, Object>>> future : futures) {
                try {
                    List<Map<String, Object>> pageImages = future.get();
                    allImages.addAll(pageImages);
                } catch (Exception e) {
                    log.error("获取PDF页面处理结果失败", e);
                }
            }

            log.info("PDF文件 {} 并行转换完成，共生成 {} 个图片", pdfFile.getOriginalFilename(), allImages.size());
            return allImages;
        }
    }

    /**
     * 处理PDF的单个页面
     * @param pdfRenderer PDF渲染器
     * @param page 页面索引
     * @param fileName 文件名
     * @param folder 存储文件夹
     * @param receiptsPerPage 单页回单数量
     * @return 该页面生成的图片信息列表
     */
    private List<Map<String, Object>> processPdfPage(PDFRenderer pdfRenderer, int page, String fileName, String folder, Integer receiptsPerPage) {
        List<Map<String, Object>> images = new ArrayList<>();

        try {
            // 渲染PDF页面为图片，使用300 DPI确保清晰度
            BufferedImage bufferedImage = pdfRenderer.renderImageWithDPI(page, 300, ImageType.RGB);

            // 将BufferedImage转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "PNG", baos);
            byte[] imageBytes = baos.toByteArray();

            // 创建MultipartFile用于上传
            String imageName = getFileNameWithoutExtension(fileName) + "_page_" + (page + 1) + ".png";
            MultipartFile imageFile = new ByteArrayMultipartFile(imageBytes, imageName, "image/png");

            // 上传图片到OSS
            String imageUrl = ossService.uploadFile(imageFile, folder + "/images");

            // 检查是否需要拆分图片（针对银行回单）
            if (folder.contains("bank-receipts")) {
                // 尝试拆分银行回单图片
                List<Map<String, Object>> splitImages = imageSplitService.splitBankReceiptImage(imageUrl, folder, receiptsPerPage);

                if (splitImages.size() > 1) {
                    // 如果拆分出多个图片，添加所有拆分后的图片
                    for (Map<String, Object> splitImage : splitImages) {
                        Map<String, Object> imageInfo = new HashMap<>();
                        imageInfo.put("pageNumber", page + 1);
                        imageInfo.put("subImageIndex", splitImage.get("subImageIndex"));
                        imageInfo.put("imageUrl", (String) splitImage.get("imageUrl"));
                        imageInfo.put("imageName", (String) splitImage.get("imageName"));
                        imageInfo.put("width", splitImage.get("width"));
                        imageInfo.put("height", splitImage.get("height"));
                        imageInfo.put("originalImageUrl", imageUrl);
                        imageInfo.put("isSplit", true);
                        imageInfo.put("splitArea", splitImage.get("area"));
                        imageInfo.put("size", imageBytes.length / splitImages.size()); // 估算大小

                        images.add(imageInfo);
                    }
                } else {
                    // 如果没有拆分，添加原图
                    Map<String, Object> imageInfo = new HashMap<>();
                    imageInfo.put("pageNumber", page + 1);
                    imageInfo.put("imageUrl", imageUrl);
                    imageInfo.put("imageName", imageName);
                    imageInfo.put("width", bufferedImage.getWidth());
                    imageInfo.put("height", bufferedImage.getHeight());
                    imageInfo.put("size", imageBytes.length);
                    imageInfo.put("isSplit", false);

                    images.add(imageInfo);
                }
            } else {
                // 非银行回单，直接添加原图
                Map<String, Object> imageInfo = new HashMap<>();
                imageInfo.put("pageNumber", page + 1);
                imageInfo.put("imageUrl", imageUrl);
                imageInfo.put("imageName", imageName);
                imageInfo.put("width", bufferedImage.getWidth());
                imageInfo.put("height", bufferedImage.getHeight());
                imageInfo.put("size", imageBytes.length);
                imageInfo.put("isSplit", false);

                images.add(imageInfo);
            }

            log.info("PDF第{}页转换完成: {}", page + 1, imageUrl);
        } catch (Exception e) {
            log.error("处理PDF页面失败", e);
        }

        return images;
    }
    
    /**
     * 从URL下载PDF并转换为图片
     * @param pdfUrl PDF文件URL
     * @param folder 存储文件夹
     * @return 图片信息列表
     */
    public List<Map<String, Object>> convertPdfFromUrl(String pdfUrl, String folder) throws IOException {
        List<Map<String, Object>> images = new ArrayList<>();
        
        try (InputStream inputStream = new URL(pdfUrl).openStream();
             PDDocument document = PDDocument.load(inputStream)) {
            
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            int pageCount = document.getNumberOfPages();
            
            log.info("PDF文件 {} 共有 {} 页", pdfUrl, pageCount);
            
            for (int page = 0; page < pageCount; page++) {
                try {
                    BufferedImage bufferedImage = pdfRenderer.renderImageWithDPI(page, 300, ImageType.RGB);
                    
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    ImageIO.write(bufferedImage, "PNG", baos);
                    byte[] imageBytes = baos.toByteArray();
                    
                    String imageName = "pdf_page_" + (page + 1) + "_" + System.currentTimeMillis() + ".png";
                    MultipartFile imageFile = new ByteArrayMultipartFile(imageBytes, imageName, "image/png");
                    
                    String imageUrl = ossService.uploadFile(imageFile, folder + "/images");
                    
                    Map<String, Object> imageInfo = new HashMap<>();
                    imageInfo.put("pageNumber", page + 1);
                    imageInfo.put("imageUrl", imageUrl);
                    imageInfo.put("imageName", imageName);
                    imageInfo.put("width", bufferedImage.getWidth());
                    imageInfo.put("height", bufferedImage.getHeight());
                    imageInfo.put("size", imageBytes.length);
                    
                    images.add(imageInfo);
                    
                } catch (Exception e) {
                    log.error("PDF第{}页转换失败", page + 1, e);
                }
            }
        }
        
        return images;
    }
    
    /**
     * 处理图片文件
     * @param imageFile 图片文件
     * @param folder 存储文件夹
     * @return 图片信息
     */
    private List<Map<String, Object>> processImageFile(MultipartFile imageFile, String folder, Integer receiptsPerPage) throws IOException {
        List<Map<String, Object>> images = new ArrayList<>();

        // 上传图片文件
        String imageUrl = ossService.uploadFile(imageFile, folder + "/images");

        // 获取图片尺寸
        BufferedImage bufferedImage = ImageIO.read(imageFile.getInputStream());

        // 检查是否需要拆分图片（针对银行回单）
        if (folder.contains("bank-receipts")) {
            // 尝试拆分银行回单图片
            List<Map<String, Object>> splitImages = imageSplitService.splitBankReceiptImage(imageUrl, folder, receiptsPerPage);

            if (splitImages.size() > 1) {
                // 如果拆分出多个图片，添加所有拆分后的图片
                for (Map<String, Object> splitImage : splitImages) {
                    Map<String, Object> imageInfo = new HashMap<>();
                    imageInfo.put("pageNumber", 1);
                    imageInfo.put("subImageIndex", splitImage.get("subImageIndex"));
                    imageInfo.put("imageUrl", (String) splitImage.get("imageUrl"));
                    imageInfo.put("imageName", (String) splitImage.get("imageName"));
                    imageInfo.put("width", splitImage.get("width"));
                    imageInfo.put("height", splitImage.get("height"));
                    imageInfo.put("originalImageUrl", imageUrl);
                    imageInfo.put("isSplit", true);
                    imageInfo.put("splitArea", splitImage.get("area"));
                    imageInfo.put("size", imageFile.getSize() / splitImages.size()); // 估算大小

                    images.add(imageInfo);
                }
            } else {
                // 如果没有拆分，添加原图
                Map<String, Object> imageInfo = new HashMap<>();
                imageInfo.put("pageNumber", 1);
                imageInfo.put("imageUrl", imageUrl);
                imageInfo.put("imageName", imageFile.getOriginalFilename());
                imageInfo.put("width", bufferedImage != null ? bufferedImage.getWidth() : 0);
                imageInfo.put("height", bufferedImage != null ? bufferedImage.getHeight() : 0);
                imageInfo.put("size", imageFile.getSize());
                imageInfo.put("isSplit", false);

                images.add(imageInfo);
            }
        } else {
            // 非银行回单，直接添加原图
            Map<String, Object> imageInfo = new HashMap<>();
            imageInfo.put("pageNumber", 1);
            imageInfo.put("imageUrl", imageUrl);
            imageInfo.put("imageName", imageFile.getOriginalFilename());
            imageInfo.put("width", bufferedImage != null ? bufferedImage.getWidth() : 0);
            imageInfo.put("height", bufferedImage != null ? bufferedImage.getHeight() : 0);
            imageInfo.put("size", imageFile.getSize());
            imageInfo.put("isSplit", false);

            images.add(imageInfo);
        }

        return images;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
    
    /**
     * 获取不带扩展名的文件名
     */
    private String getFileNameWithoutExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return fileName;
        }
        return fileName.substring(0, fileName.lastIndexOf("."));
    }
    
    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String extension) {
        return "jpg".equals(extension) || "jpeg".equals(extension) || 
               "png".equals(extension) || "bmp".equals(extension) || 
               "gif".equals(extension);
    }
}
