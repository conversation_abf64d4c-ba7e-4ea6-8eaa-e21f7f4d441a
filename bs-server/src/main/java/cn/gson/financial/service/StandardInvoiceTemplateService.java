package cn.gson.financial.service;

import cn.gson.financial.kernel.model.entity.FieldMappingTemplate;
import cn.gson.financial.kernel.model.mapper.FieldMappingTemplateMapper;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 标准增值税发票模板服务
 * 为增值税专用发票和普通发票提供标准的字段映射模板
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-13
 */
@Service
@Slf4j
public class StandardInvoiceTemplateService {

    @Autowired
    private FieldMappingTemplateMapper templateMapper;

    /**
     * 初始化标准发票模板
     */
    @PostConstruct
    public void initializeStandardTemplates() {
        try {
            // 创建增值税普通发票模板
            createVatGeneralInvoiceTemplate();
            
            // 创建增值税专用发票模板
            createVatSpecialInvoiceTemplate();
            
            log.info("标准增值税发票模板初始化完成");
        } catch (Exception e) {
            log.error("初始化标准发票模板失败", e);
        }
    }

    /**
     * 创建增值税普通发票标准模板
     */
    private void createVatGeneralInvoiceTemplate() {
        // 检查是否已存在
        FieldMappingTemplate existing = templateMapper.findByTemplateName("增值税普通发票标准模板");
        if (existing != null) {
            log.info("增值税普通发票标准模板已存在，跳过创建");
            return;
        }

        FieldMappingTemplate template = new FieldMappingTemplate();
        template.setTemplateName("增值税普通发票标准模板");
        template.setBankIdentifier(null); // 通用模板
        template.setDocumentType("INVOICE");
        template.setReceiptType("增值税普通发票");
        template.setFieldCount(15); // 标准字段数量
        template.setFieldSignature("vat_general_invoice_standard");
        
        // 标准字段映射规则
        JSONObject mappingRules = new JSONObject();
        mappingRules.put("发票号码", "invoice_number");
        mappingRules.put("发票代码", "invoice_code");
        mappingRules.put("开票日期", "bill_date");
        mappingRules.put("销售方名称", "issuer");
        mappingRules.put("购买方名称", "recipient");
        mappingRules.put("销售方纳税人识别号", "seller_tax_id");
        mappingRules.put("购买方纳税人识别号", "buyer_tax_id");
        mappingRules.put("价税合计(小写)", "amount");
        mappingRules.put("价税合计（小写）", "amount");
        mappingRules.put("价税合计(大写)", "amount_in_words");
        mappingRules.put("价税合计（大写）", "amount_in_words");
        mappingRules.put("合计金额", "amount_without_tax");
        mappingRules.put("不含税金额", "amount_without_tax");
        mappingRules.put("合计税额", "total_tax_amount");
        mappingRules.put("税额", "total_tax_amount");
        mappingRules.put("备注", "remark");
        mappingRules.put("发票名称", "type");
        mappingRules.put("发票类型", "type");
        
        template.setMappingRules(mappingRules.toJSONString());
        template.setSampleOcrData("{}");
        template.setUsageCount(0);
        template.setSuccessRate(new BigDecimal("100.00"));
        template.setLastUsedTime(LocalDateTime.now());
        template.setAccountSetsId(null); // 系统级模板
        template.setCreateUser(0); // 系统创建
        template.setCreatedTime(LocalDateTime.now());
        template.setUpdatedTime(LocalDateTime.now());
        template.setIsActive(true);

        templateMapper.insert(template);
        log.info("创建增值税普通发票标准模板成功");
    }

    /**
     * 创建增值税专用发票标准模板
     */
    private void createVatSpecialInvoiceTemplate() {
        // 检查是否已存在
        FieldMappingTemplate existing = templateMapper.findByTemplateName("增值税专用发票标准模板");
        if (existing != null) {
            log.info("增值税专用发票标准模板已存在，跳过创建");
            return;
        }

        FieldMappingTemplate template = new FieldMappingTemplate();
        template.setTemplateName("增值税专用发票标准模板");
        template.setBankIdentifier(null); // 通用模板
        template.setDocumentType("INVOICE");
        template.setReceiptType("增值税专用发票");
        template.setFieldCount(20); // 专用发票字段更多
        template.setFieldSignature("vat_special_invoice_standard");
        
        // 标准字段映射规则（专用发票包含更多字段）
        JSONObject mappingRules = new JSONObject();
        mappingRules.put("发票号码", "invoice_number");
        mappingRules.put("发票代码", "invoice_code");
        mappingRules.put("开票日期", "bill_date");
        mappingRules.put("销售方名称", "issuer");
        mappingRules.put("购买方名称", "recipient");
        mappingRules.put("销售方识别号", "seller_tax_id");
        mappingRules.put("销售方纳税人识别号", "seller_tax_id");
        mappingRules.put("购买方识别号", "buyer_tax_id");
        mappingRules.put("购买方纳税人识别号", "buyer_tax_id");
        mappingRules.put("价税合计(小写)", "amount");
        mappingRules.put("价税合计（小写）", "amount");
        mappingRules.put("小写金额", "amount");
        mappingRules.put("价税合计(大写)", "amount_in_words");
        mappingRules.put("价税合计（大写）", "amount_in_words");
        mappingRules.put("合计金额", "amount_without_tax");
        mappingRules.put("不含税金额", "amount_without_tax");
        mappingRules.put("金额", "amount_without_tax");
        mappingRules.put("合计税额", "total_tax_amount");
        mappingRules.put("税额", "total_tax_amount");
        mappingRules.put("备注", "remark");
        mappingRules.put("发票名称", "type");
        mappingRules.put("发票类型", "type");
        mappingRules.put("货物或应税劳务、服务名称", "summary");
        mappingRules.put("项目名称", "summary");
        mappingRules.put("税率", "tax_rate");
        mappingRules.put("税率/征收率", "tax_rate");
        
        template.setMappingRules(mappingRules.toJSONString());
        template.setSampleOcrData("{}");
        template.setUsageCount(0);
        template.setSuccessRate(new BigDecimal("100.00"));
        template.setLastUsedTime(LocalDateTime.now());
        template.setAccountSetsId(null); // 系统级模板
        template.setCreateUser(0); // 系统创建
        template.setCreatedTime(LocalDateTime.now());
        template.setUpdatedTime(LocalDateTime.now());
        template.setIsActive(true);

        templateMapper.insert(template);
        log.info("创建增值税专用发票标准模板成功");
    }

    /**
     * 应用标准发票模板进行字段映射
     * 
     * @param ocrData OCR识别的原始数据
     * @return 映射后的标准字段数据
     */
    public JSONObject applyStandardTemplate(JSONObject ocrData) {
        if (ocrData == null) {
            return new JSONObject();
        }

        try {
            // 从OCR数据中提取原始字段
            @SuppressWarnings("unchecked")
            Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
            if (rawData == null || rawData.isEmpty()) {
                log.warn("OCR原始数据为空");
                return new JSONObject();
            }

            // 判断发票类型
            String invoiceType = determineInvoiceType(rawData);
            log.info("识别发票类型: {}", invoiceType);

            // 选择对应的标准模板
            FieldMappingTemplate template = getStandardTemplate(invoiceType);
            if (template == null) {
                log.warn("未找到对应的标准模板: {}", invoiceType);
                return new JSONObject();
            }

            // 应用模板映射
            return applyTemplateMapping(rawData, template);

        } catch (Exception e) {
            log.error("应用标准发票模板失败", e);
            return new JSONObject();
        }
    }

    /**
     * 判断发票类型
     */
    private String determineInvoiceType(Map<String, String> rawData) {
        String invoiceName = rawData.get("发票名称");
        String invoiceType = rawData.get("发票类型");
        
        if (invoiceName != null) {
            if (invoiceName.contains("专用发票")) {
                return "增值税专用发票";
            } else if (invoiceName.contains("普通发票")) {
                return "增值税普通发票";
            }
        }
        
        if (invoiceType != null) {
            if (invoiceType.contains("专用发票")) {
                return "增值税专用发票";
            } else if (invoiceType.contains("普通发票")) {
                return "增值税普通发票";
            }
        }
        
        // 默认为普通发票
        return "增值税普通发票";
    }

    /**
     * 获取标准模板
     */
    private FieldMappingTemplate getStandardTemplate(String invoiceType) {
        String templateName;
        if ("增值税专用发票".equals(invoiceType)) {
            templateName = "增值税专用发票标准模板";
        } else {
            templateName = "增值税普通发票标准模板";
        }
        
        return templateMapper.findByTemplateName(templateName);
    }

    /**
     * 应用模板映射
     */
    private JSONObject applyTemplateMapping(Map<String, String> rawData, FieldMappingTemplate template) {
        JSONObject result = new JSONObject();
        JSONObject mappingRules = JSONObject.parseObject(template.getMappingRules());

        log.info("开始应用模板映射，模板: {}, OCR字段数: {}, 映射规则数: {}",
                template.getTemplateName(), rawData.size(), mappingRules.size());

        int mappedCount = 0;
        for (Map.Entry<String, String> entry : rawData.entrySet()) {
            String ocrField = entry.getKey();
            String ocrValue = entry.getValue();

            log.debug("处理OCR字段: {} = {}", ocrField, ocrValue);

            if (mappingRules.containsKey(ocrField)) {
                String standardField = mappingRules.getString(ocrField);
                log.debug("找到映射规则: {} -> {}", ocrField, standardField);

                // 处理特殊字段
                if ("amount".equals(standardField) || "amount_without_tax".equals(standardField) || "total_tax_amount".equals(standardField)) {
                    // 去除货币符号，转换为数字
                    String numericValue = ocrValue.replaceAll("[￥¥$,，]", "").trim();
                    try {
                        Double amount = Double.parseDouble(numericValue);
                        result.put(standardField, amount);
                        mappedCount++;
                        log.debug("成功映射金额字段: {} = {} -> {} = {}", ocrField, ocrValue, standardField, amount);
                    } catch (NumberFormatException e) {
                        log.warn("无法解析金额字段: {} = {}", ocrField, ocrValue);
                    }
                } else if ("bill_date".equals(standardField)) {
                    // 处理日期格式
                    String dateValue = formatDate(ocrValue);
                    result.put(standardField, dateValue);
                    mappedCount++;
                    log.debug("成功映射日期字段: {} = {} -> {} = {}", ocrField, ocrValue, standardField, dateValue);
                } else {
                    // 普通字段直接映射
                    result.put(standardField, ocrValue);
                    mappedCount++;
                    log.debug("成功映射普通字段: {} = {} -> {} = {}", ocrField, ocrValue, standardField, ocrValue);
                }
            } else {
                log.debug("未找到映射规则: {}", ocrField);
            }
        }
        
        // 设置默认值
        if (!result.containsKey("type")) {
            result.put("type", "发票");
        }
        if (!result.containsKey("summary")) {
            result.put("summary", "发票");
        }
        
        log.info("标准模板映射完成，映射字段数: {}", mappedCount);
        return result;
    }

    /**
     * 格式化日期
     */
    private String formatDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        
        // 处理常见的日期格式
        dateStr = dateStr.replaceAll("[年月]", "-").replaceAll("日", "");
        
        // 如果是yyyy-MM-dd格式，直接返回
        if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
            return dateStr;
        }
        
        // 如果是yyyy-M-d格式，补零
        if (dateStr.matches("\\d{4}-\\d{1,2}-\\d{1,2}")) {
            String[] parts = dateStr.split("-");
            if (parts.length == 3) {
                return String.format("%s-%02d-%02d", 
                    parts[0], 
                    Integer.parseInt(parts[1]), 
                    Integer.parseInt(parts[2]));
            }
        }
        
        return dateStr;
    }
}
