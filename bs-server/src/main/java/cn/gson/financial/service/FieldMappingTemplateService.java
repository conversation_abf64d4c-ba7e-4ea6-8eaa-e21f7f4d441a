package cn.gson.financial.service;

import cn.gson.financial.kernel.model.entity.FieldMappingTemplate;
import cn.gson.financial.kernel.model.mapper.FieldMappingTemplateMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字段映射模板服务
 * 用于管理OCR字段到标准字段的映射模板，减少LLM调用次数
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */
@Service
@Slf4j
public class FieldMappingTemplateService {

    @Autowired
    private FieldMappingTemplateMapper templateMapper;

    @Autowired
    private cn.gson.financial.kernel.service.CacheService cacheService;

    /**
     * 查找匹配的字段映射模板
     *
     * @param ocrData OCR识别数据
     * @param accountSetsId 账套ID（可以为null，查找系统级模板）
     * @param documentType 票据类型
     * @return 匹配的模板，如果没有找到返回null
     */
    public FieldMappingTemplate findMatchingTemplate(JSONObject ocrData, Integer accountSetsId, String documentType) {
        if (ocrData == null || documentType == null) {
            log.warn("查找模板参数不完整: ocrData={}, accountSetsId={}, documentType={}",
                    ocrData != null, accountSetsId, documentType);
            return null;
        }

        try {
            // 提取OCR数据特征
            TemplateFeature feature = extractFeatures(ocrData);
            log.info("提取OCR特征: 银行={}, 回单类型={}, 字段数={}, 签名={}",
                    feature.bankIdentifier, feature.receiptType, feature.fieldCount, feature.fieldSignature);

            // 1. 暂时禁用缓存，直接从数据库查找
            // FieldMappingTemplate cachedTemplate = null;
            // if (accountSetsId != null) {
            //     cachedTemplate = cacheService.getCachedFieldMappingTemplate(
            //             accountSetsId, documentType, feature.fieldSignature);
            //     if (cachedTemplate != null) {
            //         log.info("从缓存找到精确匹配模板: {}", cachedTemplate.getTemplateName());
            //         return cachedTemplate;
            //     }
            // }
            log.info("缓存已禁用，直接从数据库查找模板");

            // 2. 从数据库按字段特征签名精确匹配
            FieldMappingTemplate exactMatch = templateMapper.findByFieldSignature(
                    accountSetsId, documentType, feature.fieldSignature);
            if (exactMatch != null) {
                log.info("找到精确匹配模板: {}", exactMatch.getTemplateName());
                // 暂时禁用缓存
                // if (accountSetsId != null) {
                //     cacheService.cacheFieldMappingTemplate(accountSetsId, documentType, feature.fieldSignature, exactMatch);
                // }
                return exactMatch;
            }

            // 3. 按银行和回单类型匹配
            List<FieldMappingTemplate> bankTypeMatches = templateMapper.findByBankAndType(
                    accountSetsId, feature.bankIdentifier, documentType, feature.receiptType);
            if (!bankTypeMatches.isEmpty()) {
                FieldMappingTemplate bestMatch = findBestMatch(bankTypeMatches, feature);
                if (bestMatch != null) {
                    log.info("找到银行类型匹配模板: {}", bestMatch.getTemplateName());
                    // 暂时禁用缓存
                    // if (accountSetsId != null) {
                    //     cacheService.cacheFieldMappingTemplate(accountSetsId, documentType, feature.fieldSignature, bestMatch);
                    // }
                    return bestMatch;
                }
            }

            // 4. 按相似度匹配（字段数量相近）
            List<FieldMappingTemplate> similarMatches = templateMapper.findSimilarTemplates(
                    accountSetsId, feature.bankIdentifier, documentType, feature.fieldCount);
            if (!similarMatches.isEmpty()) {
                FieldMappingTemplate bestMatch = findBestMatch(similarMatches, feature);
                if (bestMatch != null) {
                    log.info("找到相似匹配模板: {}", bestMatch.getTemplateName());
                    // 暂时禁用缓存
                    // if (accountSetsId != null) {
                    //     cacheService.cacheFieldMappingTemplate(accountSetsId, documentType, feature.fieldSignature, bestMatch);
                    // }
                    return bestMatch;
                }
            }

            log.info("未找到匹配的字段映射模板");
            return null;

        } catch (Exception e) {
            log.error("查找字段映射模板失败", e);
            return null;
        }
    }

    /**
     * 应用字段映射模板
     *
     * @param ocrData OCR识别数据
     * @param template 映射模板
     * @return 映射后的标准字段数据
     */
    public JSONObject applyTemplate(JSONObject ocrData, FieldMappingTemplate template) {
        if (ocrData == null || template == null) {
            log.warn("应用模板参数不完整");
            return ocrData;
        }

        try {
            JSONObject mappingRules = JSON.parseObject(template.getMappingRules());
            JSONObject result = new JSONObject();

            // 获取原始OCR数据，优先使用rawOcrData，然后是recognitionData，最后是整个ocrData
            JSONObject recognitionData = ocrData.getJSONObject("rawOcrData");
            if (recognitionData == null) {
                recognitionData = ocrData.getJSONObject("recognitionData");
            }
            if (recognitionData == null) {
                recognitionData = ocrData;
            }

            log.info("应用模板 {} 映射字段，数据源字段数: {}", template.getTemplateName(), recognitionData.size());

            // 应用映射规则
            for (Map.Entry<String, Object> entry : mappingRules.entrySet()) {
                String standardFieldName = entry.getKey();
                Object mappingRule = entry.getValue();

                Object fieldValue = null;

                // 处理不同类型的映射规则
                if (mappingRule instanceof String) {
                    String ruleStr = (String) mappingRule;

                    // 检查是否为动态占位符
                    if (ruleStr.startsWith("{{") && ruleStr.endsWith("}}")) {
                        String placeholder = ruleStr.substring(2, ruleStr.length() - 2);
                        fieldValue = resolvePlaceholder(recognitionData, placeholder);
                        log.info("解析动态占位符: {} -> {}", ruleStr, fieldValue);
                    } else {
                        // 固定值
                        fieldValue = ruleStr;
                        log.info("使用固定值: {} = {}", standardFieldName, fieldValue);
                    }
                } else if (mappingRule instanceof JSONArray) {
                    // 数组形式的字段名列表，按优先级查找
                    JSONArray fieldNames = (JSONArray) mappingRule;
                    for (Object fieldNameObj : fieldNames) {
                        String fieldName = fieldNameObj.toString();
                        fieldValue = findFieldValue(recognitionData, fieldName);
                        if (fieldValue != null) {
                            log.info("字段映射成功: {} -> {} = {}", fieldName, standardFieldName, fieldValue);
                            break;
                        }
                    }
                    if (fieldValue == null) {
                        log.warn("未找到字段: {} 在数据源中", fieldNames);
                    }
                } else {
                    // 其他类型直接作为值使用
                    fieldValue = mappingRule;
                    log.info("使用直接值: {} = {}", standardFieldName, fieldValue);
                }

                if (fieldValue != null) {
                    // 应用字段转换
                    Object transformedValue = transformFieldValue(fieldValue, standardFieldName);
                    result.put(standardFieldName, transformedValue);
                }
            }

            // 设置默认值
            setDefaultValues(result);

            // 更新模板使用统计
            updateTemplateUsage(template.getId(), true);

            log.info("成功应用模板 {} 映射字段，映射了 {} 个字段", template.getTemplateName(), result.size());
            return result;

        } catch (Exception e) {
            log.error("应用字段映射模板失败", e);
            updateTemplateUsage(template.getId(), false);
            return ocrData;
        }
    }

    /**
     * 创建新的字段映射模板
     *
     * @param ocrData OCR识别数据
     * @param mappedData LLM映射后的数据
     * @param accountSetsId 账套ID（可以为null，创建系统级模板）
     * @param documentType 票据类型
     * @param createUser 创建用户ID（可以为null，系统创建）
     * @return 创建的模板
     */
    public FieldMappingTemplate createTemplate(JSONObject ocrData, JSONObject mappedData,
                                               Integer accountSetsId, String documentType, Integer createUser) {
        try {
            TemplateFeature feature = extractFeatures(ocrData);
            
            // 生成映射规则
            JSONObject mappingRules = generateMappingRules(ocrData, mappedData);
            
            // 生成模板名称
            String templateName = generateTemplateName(feature);

            FieldMappingTemplate template = new FieldMappingTemplate();
            template.setTemplateName(templateName);
            template.setBankIdentifier(feature.bankIdentifier);
            template.setDocumentType(documentType);
            template.setReceiptType(feature.receiptType);
            template.setFieldCount(feature.fieldCount);
            template.setFieldSignature(feature.fieldSignature);
            template.setMappingRules(mappingRules.toJSONString());
            template.setSampleOcrData(ocrData.toJSONString());
            template.setUsageCount(1);
            template.setSuccessRate(new BigDecimal("100.00"));
            template.setLastUsedTime(LocalDateTime.now());
            template.setAccountSetsId(accountSetsId);
            template.setCreateUser(createUser);
            template.setIsActive(true);

            templateMapper.insert(template);
            log.info("创建新的字段映射模板: {}", templateName);

            // 暂时禁用缓存
            // if (accountSetsId != null) {
            //     cacheService.clearFieldMappingTemplateCache(accountSetsId, documentType);
            // }

            return template;

        } catch (Exception e) {
            log.error("创建字段映射模板失败", e);
            return null;
        }
    }

    /**
     * 提取OCR数据特征
     */
    private TemplateFeature extractFeatures(JSONObject ocrData) {
        TemplateFeature feature = new TemplateFeature();

        // 提取银行标识
        feature.bankIdentifier = extractBankIdentifier(ocrData);
        
        // 提取回单类型
        feature.receiptType = extractReceiptType(ocrData);
        
        // 计算字段数量和签名
        Set<String> fieldNames = extractFieldNames(ocrData);
        feature.fieldCount = fieldNames.size();
        feature.fieldSignature = generateFieldSignature(fieldNames);

        return feature;
    }

    /**
     * 提取银行标识或发票开票方标识
     */
    private String extractBankIdentifier(JSONObject ocrData) {
        // 从多个可能的字段中提取银行标识或发票开票方标识
        String[] identifierFields = {
            // 银行回单字段
            "机构", "transaction_institution", "company", "银行", "开户行",
            // 发票字段
            "销售方名称", "sellerName", "开票方", "issuer", "销售方识别号", "sellerTaxId"
        };

        for (String field : identifierFields) {
            String value = getNestedValue(ocrData, field);
            if (value != null && !value.trim().isEmpty()) {
                // 标准化名称
                return normalizeIdentifierName(value);
            }
        }

        return null;
    }

    /**
     * 提取回单类型或发票类型
     */
    private String extractReceiptType(JSONObject ocrData) {
        String[] typeFields = {
            // 银行回单字段
            "回单类型", "业务类型", "交易类型", "标题", "receipt_title",
            // 发票字段
            "发票类型", "票据类型", "type", "invoiceType"
        };

        for (String field : typeFields) {
            String value = getNestedValue(ocrData, field);
            if (value != null && !value.trim().isEmpty()) {
                return value.trim();
            }
        }

        return null;
    }

    /**
     * 提取所有字段名称
     */
    private Set<String> extractFieldNames(JSONObject ocrData) {
        Set<String> fieldNames = new HashSet<>();
        extractFieldNamesRecursive(ocrData, fieldNames);
        return fieldNames;
    }

    /**
     * 递归提取字段名称
     */
    private void extractFieldNamesRecursive(JSONObject obj, Set<String> fieldNames) {
        if (obj == null) return;
        
        for (String key : obj.keySet()) {
            fieldNames.add(key);
            Object value = obj.get(key);
            if (value instanceof JSONObject) {
                extractFieldNamesRecursive((JSONObject) value, fieldNames);
            }
        }
    }

    /**
     * 生成字段特征签名
     */
    private String generateFieldSignature(Set<String> fieldNames) {
        try {
            // 对字段名称排序后生成hash
            String sortedFields = fieldNames.stream()
                    .sorted()
                    .collect(Collectors.joining(","));
            
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(sortedFields.getBytes("UTF-8"));
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
            
        } catch (Exception e) {
            log.error("生成字段签名失败", e);
            return String.valueOf(fieldNames.hashCode());
        }
    }

    /**
     * 模板特征类
     */
    private static class TemplateFeature {
        String bankIdentifier;
        String receiptType;
        Integer fieldCount;
        String fieldSignature;
    }

    /**
     * 找到最佳匹配模板
     */
    private FieldMappingTemplate findBestMatch(List<FieldMappingTemplate> templates, TemplateFeature feature) {
        if (templates.isEmpty()) return null;

        // 按匹配度排序：银行匹配 > 类型匹配 > 使用次数 > 成功率
        return templates.stream()
                .max(Comparator
                        .comparing((FieldMappingTemplate t) ->
                                Objects.equals(t.getBankIdentifier(), feature.bankIdentifier) ? 1 : 0)
                        .thenComparing(t ->
                                Objects.equals(t.getReceiptType(), feature.receiptType) ? 1 : 0)
                        .thenComparing(FieldMappingTemplate::getUsageCount)
                        .thenComparing(FieldMappingTemplate::getSuccessRate))
                .orElse(templates.get(0));
    }

    /**
     * 解析动态占位符
     */
    private Object resolvePlaceholder(JSONObject ocrData, String placeholder) {
        switch (placeholder) {
            case "机构":
                // 查找机构名称，用作收款人
                Object institution = findFieldValue(ocrData, "机构");
                if (institution != null) return institution;

                institution = findFieldValue(ocrData, "transaction_institution");
                if (institution != null) return institution;

                institution = findFieldValue(ocrData, "company");
                if (institution != null) return institution;

                institution = findFieldValue(ocrData, "银行");
                if (institution != null) return institution;

                institution = findFieldValue(ocrData, "开户行名称");
                if (institution != null) return institution;

                return null;
            default:
                log.warn("未知的占位符: {}", placeholder);
                return null;
        }
    }

    /**
     * 在OCR数据中查找字段值
     */
    private Object findFieldValue(JSONObject ocrData, String fieldName) {
        // 直接查找
        if (ocrData.containsKey(fieldName)) {
            return ocrData.get(fieldName);
        }

        // 在嵌套对象中查找
        for (String key : ocrData.keySet()) {
            Object value = ocrData.get(key);
            if (value instanceof JSONObject) {
                Object nestedValue = findFieldValue((JSONObject) value, fieldName);
                if (nestedValue != null) {
                    return nestedValue;
                }
            }
        }

        return null;
    }

    /**
     * 转换字段值
     */
    private Object transformFieldValue(Object value, String standardFieldName) {
        if (value == null) return null;

        String strValue = value.toString().trim();

        switch (standardFieldName) {
            case "amount":
                // 金额字段：去除符号，转换为数字
                return parseAmount(strValue);
            case "transfer_date":
            case "receipts_date":
                // 日期字段：转换为标准格式
                return parseDate(strValue);
            case "type":
                // 类型字段：判断收入/支出
                return determineTransactionType(strValue);
            case "quantity":
                // 数量字段：转换为整数
                return parseInteger(strValue, 1);
            default:
                return strValue;
        }
    }

    /**
     * 解析金额
     */
    private Double parseAmount(String amountStr) {
        if (amountStr == null || amountStr.trim().isEmpty()) return null;

        try {
            // 去除货币符号和逗号
            String cleanAmount = amountStr.replaceAll("[^0-9.]", "");
            return cleanAmount.isEmpty() ? null : Double.parseDouble(cleanAmount);
        } catch (NumberFormatException e) {
            log.warn("金额解析失败: {}", amountStr);
            return null;
        }
    }

    /**
     * 解析日期
     */
    private String parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) return null;

        // 简化的日期解析，实际应该更完善
        if (dateStr.matches("\\d{4}年\\d{1,2}月\\d{1,2}日")) {
            return dateStr.replaceAll("年", "-")
                          .replaceAll("月", "-")
                          .replaceAll("日", "");
        }

        return dateStr;
    }

    /**
     * 判断交易类型
     */
    private String determineTransactionType(String typeStr) {
        if (typeStr == null) return "支出"; // 默认支出

        String lowerType = typeStr.toLowerCase();
        if (lowerType.contains("收入") || lowerType.contains("转入") ||
            lowerType.contains("存入") || lowerType.contains("贷方")) {
            return "收入";
        } else {
            return "支出";
        }
    }

    /**
     * 解析整数
     */
    private Integer parseInteger(String intStr, Integer defaultValue) {
        if (intStr == null || intStr.trim().isEmpty()) return defaultValue;

        try {
            return Integer.parseInt(intStr.replaceAll("[^0-9]", ""));
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(JSONObject result) {
        // 设置必填字段的默认值
        if (!result.containsKey("quantity") || result.get("quantity") == null) {
            result.put("quantity", 1);
        }
        if (!result.containsKey("type") || result.get("type") == null) {
            result.put("type", "支出");
        }
    }

    /**
     * 更新模板使用统计
     */
    private void updateTemplateUsage(Integer templateId, boolean success) {
        try {
            templateMapper.updateUsageStats(templateId, success, LocalDateTime.now());
        } catch (Exception e) {
            log.error("更新模板使用统计失败", e);
        }
    }

    /**
     * 生成映射规则
     */
    private JSONObject generateMappingRules(JSONObject ocrData, JSONObject mappedData) {
        JSONObject rules = new JSONObject();

        // 基于映射结果反推规则
        for (String standardField : mappedData.keySet()) {
            Object value = mappedData.get(standardField);
            if (value != null) {
                String ocrField = findOcrFieldForValue(ocrData, value);
                if (ocrField != null) {
                    rules.put(ocrField, standardField);
                }
            }
        }

        return rules;
    }

    /**
     * 查找OCR字段对应的值
     */
    private String findOcrFieldForValue(JSONObject ocrData, Object targetValue) {
        return findOcrFieldForValueRecursive(ocrData, targetValue, "");
    }

    /**
     * 递归查找OCR字段
     */
    private String findOcrFieldForValueRecursive(JSONObject obj, Object targetValue, String prefix) {
        if (obj == null || targetValue == null) return null;

        for (String key : obj.keySet()) {
            Object value = obj.get(key);
            if (value != null && value.toString().equals(targetValue.toString())) {
                return prefix.isEmpty() ? key : prefix + "." + key;
            }
            if (value instanceof JSONObject) {
                String result = findOcrFieldForValueRecursive((JSONObject) value, targetValue,
                        prefix.isEmpty() ? key : prefix + "." + key);
                if (result != null) return result;
            }
        }

        return null;
    }

    /**
     * 生成模板名称
     */
    private String generateTemplateName(TemplateFeature feature) {
        StringBuilder name = new StringBuilder();

        if (feature.bankIdentifier != null) {
            name.append(feature.bankIdentifier);
        } else {
            name.append("通用");
        }

        if (feature.receiptType != null) {
            name.append("-").append(feature.receiptType);
        }

        name.append("模板");
        return name.toString();
    }

    /**
     * 标准化标识符名称（银行名称或发票开票方名称）
     */
    private String normalizeIdentifierName(String identifierName) {
        if (identifierName == null) return null;

        // 移除常见的后缀和前缀
        String normalized = identifierName.trim()
                .replaceAll("(?i)(银行|BANK|有限公司|有限责任公司|股份有限公司|集团|GROUP|CO\\.?LTD\\.?|LIMITED|INC\\.?)$", "")
                .replaceAll("(?i)^(中国|CHINA|BANK OF)", "")
                .trim();

        // 如果标准化后为空，返回原始值
        return normalized.isEmpty() ? identifierName.trim() : normalized;
    }

    /**
     * 标准化银行名称（保留向后兼容性）
     */
    private String normalizeBankName(String bankName) {
        if (bankName == null) return null;

        // 移除常见的后缀
        bankName = bankName.replaceAll("(银行|BANK).*", "");

        // 标准化常见银行名称
        if (bankName.contains("交通") || bankName.contains("COMMUNICATIONS")) {
            return "交通银行";
        } else if (bankName.contains("工商") || bankName.contains("ICBC")) {
            return "工商银行";
        } else if (bankName.contains("建设") || bankName.contains("CCB")) {
            return "建设银行";
        } else if (bankName.contains("农业") || bankName.contains("ABC")) {
            return "农业银行";
        }

        return bankName.trim();
    }

    /**
     * 获取嵌套值
     */
    private String getNestedValue(JSONObject obj, String fieldName) {
        if (obj == null) return null;

        // 直接查找
        Object value = obj.get(fieldName);
        if (value != null) {
            return value.toString().trim();
        }

        // 在嵌套对象中查找
        for (String key : obj.keySet()) {
            Object nestedObj = obj.get(key);
            if (nestedObj instanceof JSONObject) {
                String nestedValue = getNestedValue((JSONObject) nestedObj, fieldName);
                if (nestedValue != null) {
                    return nestedValue;
                }
            }
        }

        return null;
    }
}
