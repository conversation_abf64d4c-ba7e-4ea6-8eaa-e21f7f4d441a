package cn.gson.financial.service;

import lombok.extern.slf4j.Slf4j;
import cn.gson.financial.kernel.aliyuncs.OssService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.concurrent.Executor;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 图像拆分服务
 * 负责将包含多个银行回单的图片拆分为单个回单
 */
@Service
@Slf4j
public class ImageSplitService {
    
    @Resource
    private OssService ossService;

    @Resource
    @Qualifier("imageSplitExecutor")
    private Executor imageSplitExecutor;
    
    /**
     * 拆分包含多个回单的图片
     * @param imageUrl 原始图片URL
     * @param folder 存储文件夹
     * @param receiptsPerPage 单页回单数量
     * @return 拆分后的图片信息列表
     */
    public List<Map<String, Object>> splitBankReceiptImage(String imageUrl, String folder, Integer receiptsPerPage) {
        List<Map<String, Object>> splitImages = new ArrayList<>();

        try {
            // 下载原始图片
            BufferedImage originalImage = ImageIO.read(new URL(imageUrl));
            if (originalImage == null) {
                log.error("无法读取图片: {}", imageUrl);
                return splitImages;
            }

            log.info("开始拆分图片: {}, 尺寸: {}x{}, 单页回单数: {}", imageUrl, originalImage.getWidth(), originalImage.getHeight(), receiptsPerPage);

            // 如果单页回单数为1，直接返回原图
            if (receiptsPerPage != null && receiptsPerPage <= 1) {
                log.info("单页回单数为1，返回原图");
                Map<String, Object> imageInfo = new HashMap<>();
                imageInfo.put("subImageIndex", 1);
                imageInfo.put("imageUrl", imageUrl);
                imageInfo.put("imageName", "original.png");
                imageInfo.put("width", originalImage.getWidth());
                imageInfo.put("height", originalImage.getHeight());
                imageInfo.put("isSplit", false);
                imageInfo.put("originalImageUrl", null);
                splitImages.add(imageInfo);
                return splitImages;
            }

            // 智能检测银行回单区域
            List<Rectangle> receiptAreas = smartDetectReceiptAreas(originalImage, receiptsPerPage);

            if (receiptAreas.isEmpty()) {
                log.warn("创建拆分区域失败，返回原图");
                Map<String, Object> imageInfo = new HashMap<>();
                imageInfo.put("subImageIndex", 1);
                imageInfo.put("imageUrl", imageUrl);
                imageInfo.put("imageName", "original.png");
                imageInfo.put("width", originalImage.getWidth());
                imageInfo.put("height", originalImage.getHeight());
                imageInfo.put("isSplit", false);
                imageInfo.put("originalImageUrl", null);
                splitImages.add(imageInfo);
                return splitImages;
            }

            // 并行拆分图片
            log.info("开始并行拆分 {} 个区域", receiptAreas.size());

            List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();

            for (int i = 0; i < receiptAreas.size(); i++) {
                final int index = i;
                final Rectangle area = receiptAreas.get(i);

                CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                    return processSplitArea(originalImage, area, index + 1, imageUrl, folder);
                }, imageSplitExecutor);

                futures.add(future);
            }

            // 等待所有拆分任务完成并收集结果
            List<Map<String, Object>> results = futures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.error("获取图片拆分结果失败", e);
                        return null;
                    }
                })
                .filter(result -> result != null)
                .collect(Collectors.toList());

            splitImages.addAll(results);
            log.info("并行拆分完成，共生成 {} 个图片", results.size());

        } catch (Exception e) {
            log.error("拆分图片失败: {}", imageUrl, e);
        }

        return splitImages;
    }

    /**
     * 处理单个拆分区域
     * @param originalImage 原始图片
     * @param area 拆分区域
     * @param index 区域索引
     * @param originalImageUrl 原始图片URL
     * @param folder 存储文件夹
     * @return 拆分后的图片信息
     */
    private Map<String, Object> processSplitArea(BufferedImage originalImage, Rectangle area, int index, String originalImageUrl, String folder) {
        try {
            // 裁剪图片
            BufferedImage subImage = originalImage.getSubimage(
                area.x, area.y, area.width, area.height
            );

            // 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(subImage, "PNG", baos);
            byte[] imageBytes = baos.toByteArray();

            // 生成文件名
            String fileName = String.format("split_%d_%d.png", System.currentTimeMillis(), index);
            MultipartFile imageFile = new ByteArrayMultipartFile(imageBytes, fileName, "image/png");

            // 上传到OSS
            String splitImageUrl = ossService.uploadFile(imageFile, folder + "/split");

            // 创建图片信息
            Map<String, Object> imageInfo = new HashMap<>();
            imageInfo.put("subImageIndex", index);
            imageInfo.put("imageUrl", splitImageUrl);
            imageInfo.put("imageName", fileName);
            imageInfo.put("width", subImage.getWidth());
            imageInfo.put("height", subImage.getHeight());
            imageInfo.put("isSplit", true);
            imageInfo.put("originalImageUrl", originalImageUrl);
            imageInfo.put("splitArea", area);

            log.info("拆分图片 {} 完成: {}", index, splitImageUrl);
            return imageInfo;

        } catch (Exception e) {
            log.error("拆分图片第{}个区域失败", index, e);
            return null;
        }
    }

    /**
     * 智能检测银行回单区域
     * 结合内容检测和用户指定的回单数量进行智能分割
     * @param image 原始图片
     * @param receiptsPerPage 单页回单数量
     * @return 拆分区域列表
     */
    private List<Rectangle> smartDetectReceiptAreas(BufferedImage image, Integer receiptsPerPage) {
        List<Rectangle> areas = new ArrayList<>();

        try {
            log.info("开始智能检测银行回单区域，图片尺寸: {}x{}, 期望回单数: {}",
                image.getWidth(), image.getHeight(), receiptsPerPage);

            // 方法1: 首先尝试基于内容的自动检测
            areas = detectReceiptAreas(image);

            // 如果没有指定期望数量（智能模式），直接使用自动检测结果
            if (receiptsPerPage == null) {
                if (!areas.isEmpty()) {
                    log.info("智能模式：自动检测到 {} 个回单区域", areas.size());
                    return areas;
                } else {
                    // 如果自动检测失败，返回原图作为单个回单
                    log.info("智能模式：自动检测失败，返回原图作为单个回单");
                    areas.add(new Rectangle(0, 0, image.getWidth(), image.getHeight()));
                    return areas;
                }
            }

            // 如果自动检测的结果与期望数量匹配，直接使用
            if (areas.size() == receiptsPerPage) {
                log.info("自动检测结果与期望数量匹配，检测到 {} 个回单区域", areas.size());
                return areas;
            }

            // 方法2: 如果自动检测失败或数量不匹配，使用智能分割
            areas = intelligentSplit(image, receiptsPerPage);

            if (!areas.isEmpty()) {
                log.info("智能分割成功，生成 {} 个回单区域", areas.size());
                return areas;
            }

            // 方法3: 最后的后备方案，使用传统的等分分割
            log.warn("智能检测失败，使用传统等分分割");
            areas = createVerticalSplitAreas(image, receiptsPerPage);

        } catch (Exception e) {
            log.error("智能检测银行回单区域失败，使用后备方案", e);
            areas = createVerticalSplitAreas(image, receiptsPerPage);
        }

        return areas;
    }

    /**
     * 智能分割算法
     * 基于图像内容分析和用户指定数量进行智能分割
     */
    private List<Rectangle> intelligentSplit(BufferedImage image, Integer receiptsPerPage) {
        List<Rectangle> areas = new ArrayList<>();

        try {
            int width = image.getWidth();
            int height = image.getHeight();

            // 检测有效内容区域（非空白区域）
            Rectangle contentBounds = detectContentBounds(image);

            if (contentBounds == null) {
                log.warn("无法检测到有效内容区域");
                return areas;
            }

            log.info("检测到内容区域: x={}, y={}, width={}, height={}",
                contentBounds.x, contentBounds.y, contentBounds.width, contentBounds.height);

            // 根据内容区域和期望数量进行分割
            if (receiptsPerPage == 1) {
                // 单个回单，使用整个内容区域
                areas.add(contentBounds);
            } else {
                // 多个回单，在内容区域内进行分割
                areas = splitContentArea(contentBounds, receiptsPerPage, width, height);
            }

        } catch (Exception e) {
            log.error("智能分割失败", e);
        }

        return areas;
    }

    /**
     * 检测图像中的有效内容边界
     * 通过分析像素密度和颜色变化来确定内容区域
     */
    private Rectangle detectContentBounds(BufferedImage image) {
        try {
            int width = image.getWidth();
            int height = image.getHeight();

            // 检测顶部边界
            int topBound = detectTopBound(image);

            // 检测底部边界
            int bottomBound = detectBottomBound(image);

            // 检测左侧边界
            int leftBound = detectLeftBound(image);

            // 检测右侧边界
            int rightBound = detectRightBound(image);

            // 验证边界的有效性
            if (topBound >= bottomBound || leftBound >= rightBound) {
                log.warn("检测到的内容边界无效: top={}, bottom={}, left={}, right={}",
                    topBound, bottomBound, leftBound, rightBound);
                return null;
            }

            // 添加一些边距以确保内容完整
            int margin = 10;
            topBound = Math.max(0, topBound - margin);
            bottomBound = Math.min(height, bottomBound + margin);
            leftBound = Math.max(0, leftBound - margin);
            rightBound = Math.min(width, rightBound + margin);

            return new Rectangle(leftBound, topBound, rightBound - leftBound, bottomBound - topBound);

        } catch (Exception e) {
            log.error("检测内容边界失败", e);
            return null;
        }
    }

    /**
     * 创建垂直拆分区域
     * @param image 原始图片
     * @param receiptsPerPage 单页回单数量
     * @return 拆分区域列表
     */
    private List<Rectangle> createVerticalSplitAreas(BufferedImage image, Integer receiptsPerPage) {
        List<Rectangle> areas = new ArrayList<>();

        int width = image.getWidth();
        int height = image.getHeight();

        // 计算每个回单的高度
        int receiptHeight = height / receiptsPerPage;

        log.info("创建垂直拆分区域: 图片尺寸={}x{}, 回单数={}, 每个回单高度={}",
                width, height, receiptsPerPage, receiptHeight);

        // 创建拆分区域
        for (int i = 0; i < receiptsPerPage; i++) {
            int y = i * receiptHeight;
            int actualHeight = receiptHeight;

            // 最后一个区域包含剩余的像素
            if (i == receiptsPerPage - 1) {
                actualHeight = height - y;
            }

            Rectangle area = new Rectangle(0, y, width, actualHeight);
            areas.add(area);

            log.info("创建拆分区域 {}: x={}, y={}, width={}, height={}",
                    i + 1, area.x, area.y, area.width, area.height);
        }

        return areas;
    }
    
    /**
     * 检测银行回单区域
     * 基于银行回单的特征进行检测
     */
    private List<Rectangle> detectReceiptAreas(BufferedImage image) {
        List<Rectangle> areas = new ArrayList<>();
        
        try {
            int width = image.getWidth();
            int height = image.getHeight();
            
            // 方法1: 基于固定分割 - 适用于规整排列的回单
            areas.addAll(detectByFixedGrid(image));
            
            // 方法2: 基于空白区域检测 - 适用于有明显分隔的回单
            if (areas.isEmpty()) {
                areas.addAll(detectByWhitespace(image));
            }
            
            // 方法3: 基于文字密度检测 - 适用于文字分布不均的情况
            if (areas.isEmpty()) {
                areas.addAll(detectByTextDensity(image));
            }
            
            // 过滤太小的区域
            areas.removeIf(area -> area.width < 200 || area.height < 150);
            
            log.info("检测到 {} 个银行回单区域", areas.size());
            
        } catch (Exception e) {
            log.error("检测银行回单区域失败", e);
        }
        
        return areas;
    }
    
    /**
     * 基于固定网格检测
     * 假设银行回单按照固定的网格排列
     */
    private List<Rectangle> detectByFixedGrid(BufferedImage image) {
        List<Rectangle> areas = new ArrayList<>();
        
        int width = image.getWidth();
        int height = image.getHeight();
        
        // 常见的银行回单排列方式
        // 1. 垂直排列（一列多行）
        if (height > width * 1.5) {
            // 检测可能的行数
            int estimatedRows = Math.max(2, height / 400); // 假设每个回单高度约400像素
            int rowHeight = height / estimatedRows;
            
            for (int i = 0; i < estimatedRows; i++) {
                int y = i * rowHeight;
                int actualHeight = (i == estimatedRows - 1) ? height - y : rowHeight;
                
                if (actualHeight > 100) { // 最小高度过滤
                    areas.add(new Rectangle(0, y, width, actualHeight));
                }
            }
        }
        // 2. 水平排列（一行多列）
        else if (width > height * 1.5) {
            int estimatedCols = Math.max(2, width / 600); // 假设每个回单宽度约600像素
            int colWidth = width / estimatedCols;
            
            for (int i = 0; i < estimatedCols; i++) {
                int x = i * colWidth;
                int actualWidth = (i == estimatedCols - 1) ? width - x : colWidth;
                
                if (actualWidth > 200) { // 最小宽度过滤
                    areas.add(new Rectangle(x, 0, actualWidth, height));
                }
            }
        }
        // 3. 网格排列（多行多列）
        else {
            // 2x2 网格是最常见的
            int rows = 2;
            int cols = 2;
            int rowHeight = height / rows;
            int colWidth = width / cols;
            
            for (int row = 0; row < rows; row++) {
                for (int col = 0; col < cols; col++) {
                    int x = col * colWidth;
                    int y = row * rowHeight;
                    int actualWidth = (col == cols - 1) ? width - x : colWidth;
                    int actualHeight = (row == rows - 1) ? height - y : rowHeight;
                    
                    if (actualWidth > 200 && actualHeight > 150) {
                        areas.add(new Rectangle(x, y, actualWidth, actualHeight));
                    }
                }
            }
        }
        
        return areas;
    }
    
    /**
     * 基于空白区域检测
     * 检测图片中的大片空白区域作为分隔线
     */
    private List<Rectangle> detectByWhitespace(BufferedImage image) {
        List<Rectangle> areas = new ArrayList<>();
        
        try {
            int width = image.getWidth();
            int height = image.getHeight();
            
            // 检测水平分隔线
            List<Integer> horizontalSeparators = new ArrayList<>();
            horizontalSeparators.add(0); // 起始位置
            
            for (int y = height / 10; y < height * 9 / 10; y += 10) {
                if (isHorizontalWhiteLine(image, y, 0.8)) {
                    horizontalSeparators.add(y);
                }
            }
            horizontalSeparators.add(height); // 结束位置
            
            // 检测垂直分隔线
            List<Integer> verticalSeparators = new ArrayList<>();
            verticalSeparators.add(0); // 起始位置
            
            for (int x = width / 10; x < width * 9 / 10; x += 10) {
                if (isVerticalWhiteLine(image, x, 0.8)) {
                    verticalSeparators.add(x);
                }
            }
            verticalSeparators.add(width); // 结束位置
            
            // 生成区域
            for (int i = 0; i < horizontalSeparators.size() - 1; i++) {
                for (int j = 0; j < verticalSeparators.size() - 1; j++) {
                    int x = verticalSeparators.get(j);
                    int y = horizontalSeparators.get(i);
                    int w = verticalSeparators.get(j + 1) - x;
                    int h = horizontalSeparators.get(i + 1) - y;
                    
                    if (w > 200 && h > 150) {
                        areas.add(new Rectangle(x, y, w, h));
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("基于空白区域检测失败", e);
        }
        
        return areas;
    }
    
    /**
     * 基于文字密度检测
     * 检测图片中文字密集的区域
     */
    private List<Rectangle> detectByTextDensity(BufferedImage image) {
        List<Rectangle> areas = new ArrayList<>();
        
        // 这里可以实现更复杂的文字密度检测算法
        // 暂时使用简单的网格分割作为后备方案
        int width = image.getWidth();
        int height = image.getHeight();
        
        // 简单的2x1分割
        if (height > width) {
            areas.add(new Rectangle(0, 0, width, height / 2));
            areas.add(new Rectangle(0, height / 2, width, height / 2));
        } else {
            areas.add(new Rectangle(0, 0, width / 2, height));
            areas.add(new Rectangle(width / 2, 0, width / 2, height));
        }
        
        return areas;
    }
    
    /**
     * 检测是否为水平空白线
     */
    private boolean isHorizontalWhiteLine(BufferedImage image, int y, double threshold) {
        int width = image.getWidth();
        int whitePixels = 0;
        
        for (int x = 0; x < width; x++) {
            int rgb = image.getRGB(x, y);
            if (isWhitePixel(rgb)) {
                whitePixels++;
            }
        }
        
        return (double) whitePixels / width >= threshold;
    }
    
    /**
     * 检测是否为垂直空白线
     */
    private boolean isVerticalWhiteLine(BufferedImage image, int x, double threshold) {
        int height = image.getHeight();
        int whitePixels = 0;
        
        for (int y = 0; y < height; y++) {
            int rgb = image.getRGB(x, y);
            if (isWhitePixel(rgb)) {
                whitePixels++;
            }
        }
        
        return (double) whitePixels / height >= threshold;
    }
    
    /**
     * 判断是否为白色像素
     */
    private boolean isWhitePixel(int rgb) {
        int r = (rgb >> 16) & 0xFF;
        int g = (rgb >> 8) & 0xFF;
        int b = rgb & 0xFF;

        // 判断是否接近白色
        return r > 240 && g > 240 && b > 240;
    }

    /**
     * 在内容区域内进行分割
     */
    private List<Rectangle> splitContentArea(Rectangle contentBounds, Integer receiptsPerPage, int imageWidth, int imageHeight) {
        List<Rectangle> areas = new ArrayList<>();

        try {
            if (receiptsPerPage <= 1) {
                areas.add(contentBounds);
                return areas;
            }

            // 根据内容区域的形状决定分割方向
            if (contentBounds.height > contentBounds.width) {
                // 垂直分割（适用于纵向排列的回单）
                int areaHeight = contentBounds.height / receiptsPerPage;

                for (int i = 0; i < receiptsPerPage; i++) {
                    int y = contentBounds.y + i * areaHeight;
                    int height = (i == receiptsPerPage - 1) ?
                        contentBounds.y + contentBounds.height - y : areaHeight;

                    if (height > 50) { // 最小高度过滤
                        areas.add(new Rectangle(contentBounds.x, y, contentBounds.width, height));
                    }
                }
            } else {
                // 水平分割（适用于横向排列的回单）
                int areaWidth = contentBounds.width / receiptsPerPage;

                for (int i = 0; i < receiptsPerPage; i++) {
                    int x = contentBounds.x + i * areaWidth;
                    int width = (i == receiptsPerPage - 1) ?
                        contentBounds.x + contentBounds.width - x : areaWidth;

                    if (width > 100) { // 最小宽度过滤
                        areas.add(new Rectangle(x, contentBounds.y, width, contentBounds.height));
                    }
                }
            }

            log.info("在内容区域内分割完成，生成 {} 个区域", areas.size());

        } catch (Exception e) {
            log.error("在内容区域内分割失败", e);
        }

        return areas;
    }

    /**
     * 检测顶部边界
     */
    private int detectTopBound(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 从顶部开始扫描，寻找第一行有内容的位置
        for (int y = 0; y < height; y++) {
            int contentPixels = 0;

            // 检查这一行的内容密度
            for (int x = 0; x < width; x += 5) { // 每5个像素采样一次以提高性能
                int rgb = image.getRGB(x, y);
                if (!isWhitePixel(rgb)) {
                    contentPixels++;
                }
            }

            // 如果这一行有足够的内容像素，认为找到了顶部边界
            if (contentPixels > width / 50) { // 至少2%的像素有内容
                return y;
            }
        }

        return 0; // 如果没有找到，返回顶部
    }

    /**
     * 检测底部边界
     */
    private int detectBottomBound(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 从底部开始扫描，寻找最后一行有内容的位置
        for (int y = height - 1; y >= 0; y--) {
            int contentPixels = 0;

            // 检查这一行的内容密度
            for (int x = 0; x < width; x += 5) { // 每5个像素采样一次以提高性能
                int rgb = image.getRGB(x, y);
                if (!isWhitePixel(rgb)) {
                    contentPixels++;
                }
            }

            // 如果这一行有足够的内容像素，认为找到了底部边界
            if (contentPixels > width / 50) { // 至少2%的像素有内容
                return y + 1; // 返回下一行作为边界
            }
        }

        return height; // 如果没有找到，返回底部
    }

    /**
     * 检测左侧边界
     */
    private int detectLeftBound(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 从左侧开始扫描，寻找第一列有内容的位置
        for (int x = 0; x < width; x++) {
            int contentPixels = 0;

            // 检查这一列的内容密度
            for (int y = 0; y < height; y += 5) { // 每5个像素采样一次以提高性能
                int rgb = image.getRGB(x, y);
                if (!isWhitePixel(rgb)) {
                    contentPixels++;
                }
            }

            // 如果这一列有足够的内容像素，认为找到了左侧边界
            if (contentPixels > height / 50) { // 至少2%的像素有内容
                return x;
            }
        }

        return 0; // 如果没有找到，返回左侧
    }

    /**
     * 检测右侧边界
     */
    private int detectRightBound(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 从右侧开始扫描，寻找最后一列有内容的位置
        for (int x = width - 1; x >= 0; x--) {
            int contentPixels = 0;

            // 检查这一列的内容密度
            for (int y = 0; y < height; y += 5) { // 每5个像素采样一次以提高性能
                int rgb = image.getRGB(x, y);
                if (!isWhitePixel(rgb)) {
                    contentPixels++;
                }
            }

            // 如果这一列有足够的内容像素，认为找到了右侧边界
            if (contentPixels > height / 50) { // 至少2%的像素有内容
                return x + 1; // 返回下一列作为边界
            }
        }

        return width; // 如果没有找到，返回右侧
    }

    /**
     * 字节数组MultipartFile实现
     */
    private static class ByteArrayMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String contentType;

        public ByteArrayMultipartFile(byte[] content, String name, String contentType) {
            this.content = content;
            this.name = name;
            this.contentType = contentType;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return name;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() {
            return content;
        }

        @Override
        public java.io.InputStream getInputStream() {
            return new java.io.ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }
}
