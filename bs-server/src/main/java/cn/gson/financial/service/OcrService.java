package cn.gson.financial.service;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;

/**
 * OCR识别服务
 * 纯粹的OCR调用服务，只负责调用腾讯云OCR接口并返回原始识别结果
 */
@Service
@Slf4j
public class OcrService {

    @Value("${tencent.cloud.secret-id}")
    private String secretId;

    @Value("${tencent.cloud.secret-key}")
    private String secretKey;

    @Value("${tencent.cloud.region:ap-beijing}")
    private String region;

    @Autowired(required = false)
    private cn.gson.financial.kernel.service.CacheService cacheService;

    @Autowired
    private cn.gson.financial.kernel.service.RateLimiterService rateLimiterService;

    /**
     * 识别银行回单
     * @param imageUrl 图片URL
     * @return 识别结果
     */
    public Map<String, Object> recognizeBankSlip(String imageUrl) {
        return recognizeBankSlip(imageUrl, null);
    }

    /**
     * 识别银行回单
     * @param imageUrl 图片URL
     * @param userId 用户ID（保留参数兼容性，但不使用）
     * @return 识别结果
     */
    public Map<String, Object> recognizeBankSlip(String imageUrl, Integer userId) {
        try {
            // 先尝试从缓存获取OCR结果
            if (cacheService != null && cacheService.isRedisAvailable()) {
                Map<String, Object> cachedResult = cacheService.getCachedOcrResult(imageUrl);
                if (cachedResult != null) {
                    log.debug("使用缓存的OCR识别结果: {}", imageUrl);
                    return cachedResult;
                }
            }

            // 限流控制：等待获取腾讯云OCR调用许可
            cn.gson.financial.kernel.service.RateLimiterService.ServiceRateLimiter rateLimiter =
                    rateLimiterService.getTencentOcrRateLimiter();

            log.debug("准备调用腾讯云OCR，当前限流器状态: {}", rateLimiter.getStatus());
            rateLimiter.acquireWithSmartWait();

            try {
                // 获取OCR客户端
                OcrClient client = getOcrClient();

                // 实例化一个请求对象
                BankSlipOCRRequest req = new BankSlipOCRRequest();
                req.setImageUrl(imageUrl);

                log.info("调用腾讯云银行回单OCR识别，URL: {}", imageUrl);
                // 返回的resp是一个BankSlipOCRResponse的实例
                BankSlipOCRResponse resp = client.BankSlipOCR(req);

                // 解析识别结果
                Map<String, Object> result = parseBankSlipResult(resp);

                // 缓存OCR识别结果
                if (cacheService != null && cacheService.isRedisAvailable() && result != null && !result.isEmpty()) {
                    cacheService.cacheOcrResult(imageUrl, result);
                    log.debug("缓存OCR识别结果: {}", imageUrl);
                }

                return result;

            } finally {
                // 注意：这里不需要手动释放许可，因为我们使用的是基于时间窗口的限流
                log.debug("腾讯云OCR调用完成，当前限流器状态: {}", rateLimiter.getStatus());
            }

        } catch (TencentCloudSDKException e) {
            log.error("银行回单识别失败", e);
            throw new RuntimeException("银行回单识别失败: " + e.getMessage());
        } catch (InterruptedException e) {
            log.error("等待OCR限流许可被中断", e);
            Thread.currentThread().interrupt();
            throw new RuntimeException("OCR识别被中断");
        }
    }

    /**
     * 解析银行回单识别结果
     * @param response OCR响应
     */
    private Map<String, Object> parseBankSlipResult(BankSlipOCRResponse response) {
        Map<String, Object> ocrResult = new HashMap<>();
        Map<String, String> rawOcrData = new HashMap<>(); // 保存所有原始OCR数据
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");

        log.info("开始解析OCR识别结果");

        if (response.getBankSlipInfos() != null) {
            log.info("识别到 {} 个字段", response.getBankSlipInfos().length);

            for (BankSlipInfo info : response.getBankSlipInfos()) {
                String name = info.getName();
                String value = info.getValue();

                // 保存所有原始数据
                rawOcrData.put(name, value);
                
                log.info("识别字段: {} = {}", name, value);

                // 基本的字段解析（不进行智能映射，只做基础的数据提取）
                switch (name) {
                    case "机构":
                        ocrResult.put("institution", value);
                        break;
                    case "标题":
                        ocrResult.put("title", value);
                        break;
                    case "交易日期":
                    case "业务日期":
                        ocrResult.put("transactionDate", value);
                        break;
                    case "交易金额(小写)":
                    case "金额":
                        ocrResult.put("amount", value);
                        break;
                    case "交易金额(大写)":
                        ocrResult.put("amountInWords", value);
                        break;
                    case "付款人":
                    case "付款账户名":
                        ocrResult.put("payerName", value);
                        break;
                    case "付款账号":
                        ocrResult.put("payerAccount", value);
                        break;
                    case "收款人":
                    case "收款账户名":
                        ocrResult.put("payeeName", value);
                        break;
                    case "收款账号":
                        ocrResult.put("payeeAccount", value);
                        break;
                    case "交易流水":
                    case "业务编号":
                    case "流水号":
                        ocrResult.put("serialNumber", value);
                        break;
                    case "交易摘要":
                    case "摘要":
                    case "用途":
                        ocrResult.put("summary", value);
                        break;
                    default:
                        log.info("未处理的字段: {} = {}", name, value);
                        break;
                }
            }
        } else {
            log.warn("OCR识别结果为空");
        }

        // 保存原始OCR数据到结果中
        ocrResult.put("rawOcrData", rawOcrData);

        // 生成格式化的备注信息
        String formattedRemark = formatOcrDataForRemark(rawOcrData);
        ocrResult.put("remark", formattedRemark);

        // 生成格式化的OCR识别信息（易读文本格式）
        String readableOcrInfo = formatOcrDataToReadableText(rawOcrData, "BANK_RECEIPT");
        ocrResult.put("ocrRecognitionInfo", readableOcrInfo);

        // 同时保存JSON格式的OCR信息（用于程序处理）
        String jsonOcrInfo = formatOcrDataToJson(rawOcrData);
        ocrResult.put("ocrRecognitionInfoJson", jsonOcrInfo);

        log.info("OCR原始解析结果: {}", ocrResult);
        log.info("银行回单OCR识别完成，返回原始解析结果");
        return ocrResult;
    }

    /**
     * 识别增值税发票
     * @param imageUrl 图片URL
     * @return 识别结果
     */
    public Map<String, Object> recognizeVatInvoice(String imageUrl) {
        try {
            // 先尝试从缓存获取OCR结果
            if (cacheService != null && cacheService.isRedisAvailable()) {
                Map<String, Object> cachedResult = cacheService.getCachedOcrResult(imageUrl);
                if (cachedResult != null) {
                    log.debug("使用缓存的增值税发票OCR识别结果: {}", imageUrl);
                    return cachedResult;
                }
            }

            // 限流控制：等待获取腾讯云OCR调用许可
            cn.gson.financial.kernel.service.RateLimiterService.ServiceRateLimiter rateLimiter =
                    rateLimiterService.getTencentOcrRateLimiter();

            log.debug("准备调用腾讯云OCR，当前限流器状态: {}", rateLimiter.getStatus());
            rateLimiter.acquireWithSmartWait();

            try {
                // 获取OCR客户端
                OcrClient client = getOcrClient();

                // 实例化一个请求对象
                VatInvoiceOCRRequest req = new VatInvoiceOCRRequest();
                req.setImageUrl(imageUrl);

                log.info("调用腾讯云增值税发票OCR识别，URL: {}", imageUrl);
                // 返回的resp是一个VatInvoiceOCRResponse的实例
                VatInvoiceOCRResponse resp = client.VatInvoiceOCR(req);

                // 解析识别结果
                Map<String, Object> result = parseVatInvoiceResult(resp);

                // 缓存OCR识别结果
                if (cacheService != null && cacheService.isRedisAvailable() && result != null && !result.isEmpty()) {
                    cacheService.cacheOcrResult(imageUrl, result);
                    log.debug("缓存增值税发票OCR识别结果: {}", imageUrl);
                }

                return result;

            } finally {
                log.debug("腾讯云OCR调用完成，当前限流器状态: {}", rateLimiter.getStatus());
            }

        } catch (TencentCloudSDKException e) {
            log.error("增值税发票识别失败", e);
            throw new RuntimeException("增值税发票识别失败: " + e.getMessage());
        } catch (InterruptedException e) {
            log.error("等待OCR限流许可被中断", e);
            Thread.currentThread().interrupt();
            throw new RuntimeException("OCR识别被中断");
        }
    }

    /**
     * 解析增值税发票识别结果
     */
    private Map<String, Object> parseVatInvoiceResult(VatInvoiceOCRResponse response) {
        Map<String, Object> result = new HashMap<>();
        Map<String, String> rawOcrData = new HashMap<>();

        log.info("开始解析增值税发票OCR识别结果");

        try {
            // 使用VatInvoiceInfos数组获取所有字段
            if (response.getVatInvoiceInfos() != null && response.getVatInvoiceInfos().length > 0) {
                for (TextVatInvoice vatInfo : response.getVatInvoiceInfos()) {
                    if (vatInfo.getName() != null && vatInfo.getValue() != null) {
                        String name = vatInfo.getName();
                        String value = vatInfo.getValue();
                        rawOcrData.put(name, value);

                        // OCR Service只负责提取原始数据，不做字段映射
                        log.debug("提取OCR字段: {} = {}", name, value);
                    }
                }
            } else {
                log.warn("发票OCR识别结果为空");
            }
        } catch (Exception e) {
            log.error("解析发票OCR结果异常", e);
        }

        // 保存原始OCR数据到结果中
        result.put("rawOcrData", rawOcrData);

        // 生成格式化的OCR识别信息（易读文本格式）
        String readableOcrInfo = formatOcrDataToReadableText(rawOcrData, "INVOICE");
        result.put("ocrRecognitionInfo", readableOcrInfo);

        // 同时保存JSON格式的OCR信息（用于程序处理）
        String jsonOcrInfo = formatOcrDataToJson(rawOcrData);
        result.put("ocrRecognitionInfoJson", jsonOcrInfo);

        log.info("OCR原始解析结果: {}", result);
        log.info("发票OCR识别完成，返回原始解析结果");
        return result;
    }

    /**
     * 获取OCR客户端
     */
    private OcrClient getOcrClient() {
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey
            Credential cred = new Credential(secretId, secretKey);

            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            // 实例化要请求产品的client对象，clientProfile是可选的
            return new OcrClient(cred, region, clientProfile);
        } catch (Exception e) {
            log.error("创建OCR客户端失败", e);
            throw new RuntimeException("创建OCR客户端失败: " + e.getMessage());
        }
    }

    /**
     * 格式化OCR数据为备注信息
     */
    private String formatOcrDataForRemark(Map<String, String> rawOcrData) {
        if (rawOcrData == null || rawOcrData.isEmpty()) {
            return "";
        }

        StringBuilder remark = new StringBuilder();
        remark.append("OCR识别信息：\n");
        for (Map.Entry<String, String> entry : rawOcrData.entrySet()) {
            remark.append(entry.getKey()).append("：").append(entry.getValue()).append("\n");
        }
        return remark.toString().trim();
    }

    /**
     * 格式化OCR数据为JSON格式的识别信息
     */
    public String formatOcrDataToJson(Map<String, String> rawOcrData) {
        if (rawOcrData == null || rawOcrData.isEmpty()) {
            return "{}";
        }

        try {
            Map<String, Object> formattedData = new HashMap<>();
            formattedData.put("timestamp", new Date());
            formattedData.put("source", "腾讯云OCR");
            formattedData.put("fieldCount", rawOcrData.size());
            formattedData.put("recognitionData", rawOcrData);

            return JSON.toJSONString(formattedData, true);
        } catch (Exception e) {
            log.error("格式化OCR数据为JSON失败", e);
            return JSON.toJSONString(rawOcrData);
        }
    }

    /**
     * 格式化OCR数据为易读的文本格式
     * 按业务逻辑分类显示，便于用户查看
     */
    public String formatOcrDataToReadableText(Map<String, String> rawOcrData, String documentType) {
        if (rawOcrData == null || rawOcrData.isEmpty()) {
            return "暂无OCR识别信息";
        }

        StringBuilder formatted = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 添加头部信息
        formatted.append("=== OCR识别信息 ===\n");
        formatted.append("识别时间: ").append(dateFormat.format(new Date())).append("\n");
        formatted.append("识别来源: 腾讯云OCR\n");
        formatted.append("文档类型: ").append(getDocumentTypeName(documentType)).append("\n");
        formatted.append("识别字段数: ").append(rawOcrData.size()).append("\n\n");

        if ("BANK_RECEIPT".equals(documentType)) {
            formatBankReceiptFields(formatted, rawOcrData);
        } else if ("INVOICE".equals(documentType)) {
            formatInvoiceFields(formatted, rawOcrData);
        } else {
            formatGenericFields(formatted, rawOcrData);
        }

        return formatted.toString();
    }

    /**
     * 格式化银行回单字段
     */
    private void formatBankReceiptFields(StringBuilder formatted, Map<String, String> rawOcrData) {
        // 基本信息
        addSectionHeader(formatted, "基本信息");
        addFieldIfExists(formatted, rawOcrData, "回单名称", "回单名称");
        addFieldIfExists(formatted, rawOcrData, "流水号", "流水号");
        addFieldIfExists(formatted, rawOcrData, "交易机构", "交易机构");

        // 日期信息
        addSectionHeader(formatted, "日期信息");
        addFieldIfExists(formatted, rawOcrData, "回单日期", "回单日期");
        addFieldIfExists(formatted, rawOcrData, "转账日期", "转账日期");

        // 金额信息
        addSectionHeader(formatted, "金额信息");
        addFieldIfExists(formatted, rawOcrData, "金额", "金额");
        addFieldIfExists(formatted, rawOcrData, "金额大写", "金额大写");
        addFieldIfExists(formatted, rawOcrData, "收支", "收支类型");

        // 账户信息
        addSectionHeader(formatted, "账户信息");
        addFieldIfExists(formatted, rawOcrData, "付款人姓名", "付款人");
        addFieldIfExists(formatted, rawOcrData, "付款人账号", "付款账号");
        addFieldIfExists(formatted, rawOcrData, "付款人开户行", "付款开户行");
        addFieldIfExists(formatted, rawOcrData, "收款人姓名", "收款人");
        addFieldIfExists(formatted, rawOcrData, "收款人账号", "收款账号");
        addFieldIfExists(formatted, rawOcrData, "收款人开户行", "收款开户行");

        // 其他信息
        addSectionHeader(formatted, "其他信息");
        addFieldIfExists(formatted, rawOcrData, "摘要", "摘要");
        addFieldIfExists(formatted, rawOcrData, "用途", "用途");
        addFieldIfExists(formatted, rawOcrData, "附言", "附言");

        // 未分类字段
        addUnclassifiedFields(formatted, rawOcrData, getBankReceiptKnownFields());
    }

    /**
     * 格式化发票字段
     */
    private void formatInvoiceFields(StringBuilder formatted, Map<String, String> rawOcrData) {
        // 基本信息
        addSectionHeader(formatted, "基本信息");
        addFieldIfExists(formatted, rawOcrData, "发票名称", "发票类型");
        addFieldIfExists(formatted, rawOcrData, "发票代码", "发票代码");
        addFieldIfExists(formatted, rawOcrData, "发票号码", "发票号码");
        addFieldIfExists(formatted, rawOcrData, "开票日期", "开票日期");

        // 开票方信息
        addSectionHeader(formatted, "开票方信息");
        addFieldIfExists(formatted, rawOcrData, "销售方名称", "销售方名称");
        addFieldIfExists(formatted, rawOcrData, "销售方纳税人识别号", "销售方税号");
        addFieldIfExists(formatted, rawOcrData, "销售方识别号", "销售方税号");
        addFieldIfExists(formatted, rawOcrData, "销售方地址电话", "销售方地址电话");
        addFieldIfExists(formatted, rawOcrData, "销售方开户行及账号", "销售方开户行账号");

        // 购买方信息
        addSectionHeader(formatted, "购买方信息");
        addFieldIfExists(formatted, rawOcrData, "购买方名称", "购买方名称");
        addFieldIfExists(formatted, rawOcrData, "购买方纳税人识别号", "购买方税号");
        addFieldIfExists(formatted, rawOcrData, "购买方识别号", "购买方税号");
        addFieldIfExists(formatted, rawOcrData, "购买方地址电话", "购买方地址电话");
        addFieldIfExists(formatted, rawOcrData, "购买方开户行及账号", "购买方开户行账号");

        // 金额信息
        addSectionHeader(formatted, "金额信息");
        addFieldIfExists(formatted, rawOcrData, "价税合计(小写)", "价税合计");
        addFieldIfExists(formatted, rawOcrData, "价税合计（小写）", "价税合计");
        addFieldIfExists(formatted, rawOcrData, "小写金额", "价税合计");
        addFieldIfExists(formatted, rawOcrData, "价税合计(大写)", "价税合计大写");
        addFieldIfExists(formatted, rawOcrData, "价税合计（大写）", "价税合计大写");
        addFieldIfExists(formatted, rawOcrData, "合计金额", "不含税金额");
        addFieldIfExists(formatted, rawOcrData, "不含税金额", "不含税金额");
        addFieldIfExists(formatted, rawOcrData, "合计税额", "税额");
        addFieldIfExists(formatted, rawOcrData, "税额", "税额");
        addFieldIfExists(formatted, rawOcrData, "税率", "税率");

        // 其他信息
        addSectionHeader(formatted, "其他信息");
        addFieldIfExists(formatted, rawOcrData, "备注", "备注");
        addFieldIfExists(formatted, rawOcrData, "收款人", "收款人");
        addFieldIfExists(formatted, rawOcrData, "复核", "复核");
        addFieldIfExists(formatted, rawOcrData, "开票人", "开票人");

        // 未分类字段
        addUnclassifiedFields(formatted, rawOcrData, getInvoiceKnownFields());
    }

    /**
     * 格式化通用字段
     */
    private void formatGenericFields(StringBuilder formatted, Map<String, String> rawOcrData) {
        addSectionHeader(formatted, "识别字段");
        for (Map.Entry<String, String> entry : rawOcrData.entrySet()) {
            addField(formatted, entry.getKey(), entry.getValue());
        }
    }

    /**
     * 添加章节标题
     */
    private void addSectionHeader(StringBuilder formatted, String title) {
        formatted.append("\n--- ").append(title).append(" ---\n");
    }

    /**
     * 添加字段（如果存在）
     */
    private void addFieldIfExists(StringBuilder formatted, Map<String, String> data, String key, String displayName) {
        if (data.containsKey(key) && data.get(key) != null && !data.get(key).trim().isEmpty()) {
            addField(formatted, displayName, data.get(key));
        }
    }

    /**
     * 添加字段
     */
    private void addField(StringBuilder formatted, String name, String value) {
        formatted.append(String.format("%-12s: %s\n", name, value != null ? value.trim() : ""));
    }

    /**
     * 添加未分类字段
     */
    private void addUnclassifiedFields(StringBuilder formatted, Map<String, String> rawOcrData, Set<String> knownFields) {
        Map<String, String> unclassified = new HashMap<>();
        for (Map.Entry<String, String> entry : rawOcrData.entrySet()) {
            if (!knownFields.contains(entry.getKey()) && entry.getValue() != null && !entry.getValue().trim().isEmpty()) {
                unclassified.put(entry.getKey(), entry.getValue());
            }
        }

        if (!unclassified.isEmpty()) {
            addSectionHeader(formatted, "其他识别字段");
            for (Map.Entry<String, String> entry : unclassified.entrySet()) {
                addField(formatted, entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * 获取银行回单已知字段集合
     */
    private Set<String> getBankReceiptKnownFields() {
        Set<String> fields = new HashSet<>();
        fields.add("回单名称");
        fields.add("流水号");
        fields.add("交易机构");
        fields.add("回单日期");
        fields.add("转账日期");
        fields.add("金额");
        fields.add("金额大写");
        fields.add("收支");
        fields.add("付款人姓名");
        fields.add("付款人账号");
        fields.add("付款人开户行");
        fields.add("收款人姓名");
        fields.add("收款人账号");
        fields.add("收款人开户行");
        fields.add("摘要");
        fields.add("用途");
        fields.add("附言");
        return fields;
    }

    /**
     * 获取发票已知字段集合
     */
    private Set<String> getInvoiceKnownFields() {
        Set<String> fields = new HashSet<>();
        fields.add("发票名称");
        fields.add("发票代码");
        fields.add("发票号码");
        fields.add("开票日期");
        fields.add("销售方名称");
        fields.add("销售方纳税人识别号");
        fields.add("销售方识别号");
        fields.add("销售方地址电话");
        fields.add("销售方开户行及账号");
        fields.add("购买方名称");
        fields.add("购买方纳税人识别号");
        fields.add("购买方识别号");
        fields.add("购买方地址电话");
        fields.add("购买方开户行及账号");
        fields.add("价税合计(小写)");
        fields.add("价税合计（小写）");
        fields.add("小写金额");
        fields.add("价税合计(大写)");
        fields.add("价税合计（大写）");
        fields.add("合计金额");
        fields.add("不含税金额");
        fields.add("合计税额");
        fields.add("税额");
        fields.add("税率");
        fields.add("备注");
        fields.add("收款人");
        fields.add("复核");
        fields.add("开票人");
        return fields;
    }

    /**
     * 获取文档类型名称
     */
    private String getDocumentTypeName(String documentType) {
        switch (documentType) {
            case "BANK_RECEIPT":
                return "银行回单";
            case "INVOICE":
                return "增值税发票";
            default:
                return "未知类型";
        }
    }
}
