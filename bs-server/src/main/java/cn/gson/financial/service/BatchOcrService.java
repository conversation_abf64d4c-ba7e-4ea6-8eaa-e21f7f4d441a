package cn.gson.financial.service;

import cn.gson.financial.kernel.model.entity.BatchImportDetail;
import cn.gson.financial.kernel.model.mapper.BatchImportDetailMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 批量OCR识别服务
 * 负责批量处理图片的OCR识别
 */
@Service
@Slf4j
public class BatchOcrService {
    
    @Resource
    private OcrService ocrService;

    @Resource
    private BatchImportDetailMapper batchImportDetailMapper;

    @Resource
    private cn.gson.financial.kernel.service.RateLimiterService rateLimiterService;

    @Resource
    private SmartFieldMappingService smartFieldMappingService;

    // 创建线程池用于并发处理OCR识别（8个线程配合腾讯云OCR 8QPS限流）
    private final ExecutorService executorService = Executors.newFixedThreadPool(8);
    
    /**
     * 批量识别银行回单
     * @param taskId 任务ID
     * @param detailIds 明细ID列表
     * @return 识别结果
     */
    @Async
    public CompletableFuture<Map<String, Object>> batchRecognizeBankReceipts(String taskId, List<Integer> detailIds) {
        return batchRecognizeBankReceipts(taskId, detailIds, null);
    }

    /**
     * 批量识别银行回单
     * @param taskId 任务ID
     * @param detailIds 明细ID列表
     * @param userId 用户ID
     * @return 识别结果
     */
    @Async
    public CompletableFuture<Map<String, Object>> batchRecognizeBankReceipts(String taskId, List<Integer> detailIds, Integer userId) {
        log.info("开始批量识别银行回单，任务ID: {}, 明细数量: {}", taskId, detailIds.size());
        
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> results = new ArrayList<>();
        int successCount = 0;
        int failedCount = 0;
        
        try {
            // 获取明细信息
            List<BatchImportDetail> details = detailIds.stream()
                .map(id -> batchImportDetailMapper.selectById(id))
                .collect(Collectors.toList());
            
            // 并发处理每个图片
            List<CompletableFuture<Map<String, Object>>> futures = details.stream()
                .map(detail -> recognizeSingleImage(detail, "BANK_RECEIPT", userId))
                .collect(Collectors.toList());
            
            // 等待所有任务完成
            for (CompletableFuture<Map<String, Object>> future : futures) {
                Map<String, Object> singleResult = future.get();
                results.add(singleResult);
                
                if ("SUCCESS".equals(singleResult.get("status"))) {
                    successCount++;
                } else {
                    failedCount++;
                }
            }
            
            result.put("taskId", taskId);
            result.put("totalCount", detailIds.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("results", results);
            result.put("status", "COMPLETED");
            
        } catch (Exception e) {
            log.error("批量识别银行回单失败，任务ID: {}", taskId, e);
            result.put("status", "FAILED");
            result.put("error", e.getMessage());
        }
        
        log.info("批量识别银行回单完成，任务ID: {}, 成功: {}, 失败: {}", taskId, successCount, failedCount);
        return CompletableFuture.completedFuture(result);
    }
    
    /**
     * 批量识别发票
     * @param taskId 任务ID
     * @param detailIds 明细ID列表
     * @return 识别结果
     */
    @Async
    public CompletableFuture<Map<String, Object>> batchRecognizeInvoices(String taskId, List<Integer> detailIds) {
        return batchRecognizeInvoices(taskId, detailIds, null);
    }

    /**
     * 批量识别发票（带用户ID）
     * @param taskId 任务ID
     * @param detailIds 明细ID列表
     * @param userId 用户ID
     * @return 识别结果
     */
    @Async
    public CompletableFuture<Map<String, Object>> batchRecognizeInvoices(String taskId, List<Integer> detailIds, Integer userId) {
        log.info("开始批量识别发票，任务ID: {}, 明细数量: {}, 用户ID: {}", taskId, detailIds.size(), userId);

        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> results = new ArrayList<>();
        int successCount = 0;
        int failedCount = 0;

        try {
            // 获取明细信息
            List<BatchImportDetail> details = detailIds.stream()
                .map(id -> batchImportDetailMapper.selectById(id))
                .collect(Collectors.toList());

            // 并发处理每个图片，传递userId参数
            List<CompletableFuture<Map<String, Object>>> futures = details.stream()
                .map(detail -> recognizeSingleImage(detail, "INVOICE", userId))
                .collect(Collectors.toList());
            
            // 等待所有任务完成
            for (CompletableFuture<Map<String, Object>> future : futures) {
                Map<String, Object> singleResult = future.get();
                results.add(singleResult);
                
                if ("SUCCESS".equals(singleResult.get("status"))) {
                    successCount++;
                } else {
                    failedCount++;
                }
            }
            
            result.put("taskId", taskId);
            result.put("totalCount", detailIds.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("results", results);
            result.put("status", "COMPLETED");
            
        } catch (Exception e) {
            log.error("批量识别发票失败，任务ID: {}", taskId, e);
            result.put("status", "FAILED");
            result.put("error", e.getMessage());
        }
        
        log.info("批量识别发票完成，任务ID: {}, 成功: {}, 失败: {}", taskId, successCount, failedCount);
        return CompletableFuture.completedFuture(result);
    }
    
    /**
     * 识别单个图片
     * @param detail 明细信息
     * @param type 识别类型
     * @return 识别结果
     */
    private CompletableFuture<Map<String, Object>> recognizeSingleImage(BatchImportDetail detail, String type) {
        return recognizeSingleImage(detail, type, null);
    }

    /**
     * 识别单个图片
     * @param detail 明细信息
     * @param type 识别类型
     * @param userId 用户ID
     * @return 识别结果
     */
    private CompletableFuture<Map<String, Object>> recognizeSingleImage(BatchImportDetail detail, String type, Integer userId) {
        return CompletableFuture.supplyAsync(() -> {
            Map<String, Object> result = new HashMap<>();
            long startTime = System.currentTimeMillis();

            try {
                log.debug("开始识别图片，明细ID: {}, 类型: {}, URL: {}", detail.getId(), type, detail.getImageUrl());

                // 更新状态为识别中
                batchImportDetailMapper.updateStatus(detail.getId(), BatchImportDetail.STATUS_RECOGNIZING, null);

                long ocrStartTime = System.currentTimeMillis();
                Map<String, Object> rawOcrResult;
                Map<String, Object> finalResult;

                if ("BANK_RECEIPT".equals(type)) {
                    finalResult = ocrService.recognizeBankSlip(detail.getImageUrl(), userId);
                    rawOcrResult = finalResult; // 银行回单已经包含了智能映射
                } else if ("INVOICE".equals(type)) {
                    // 1. 先调用OCR服务获取原始识别结果
                    rawOcrResult = ocrService.recognizeVatInvoice(detail.getImageUrl());

                    // 2. 如果有智能映射服务且提供了必要参数，进行智能字段映射
                    if (smartFieldMappingService != null && userId != null) {
                        // 需要获取accountSetsId，从detail中获取
                        Integer accountSetsId = detail.getAccountSetsId();
                        if (accountSetsId != null) {
                            Map<String, Object> mappedResult = smartFieldMappingService.mapInvoiceFields(
                                    rawOcrResult, accountSetsId, userId);

                            if (mappedResult != null && !mappedResult.isEmpty()) {
                                log.info("批量发票智能映射成功，明细ID: {}, 映射字段数: {}", detail.getId(), mappedResult.size());
                                finalResult = mappedResult;
                            } else {
                                log.warn("批量发票智能映射失败，使用原始OCR结果，明细ID: {}", detail.getId());
                                finalResult = rawOcrResult;
                            }
                        } else {
                            log.warn("账套ID为空，无法进行智能映射，明细ID: {}", detail.getId());
                            finalResult = rawOcrResult;
                        }
                    } else {
                        log.warn("智能映射服务不可用或用户ID为空，使用原始OCR结果，明细ID: {}", detail.getId());
                        finalResult = rawOcrResult;
                    }
                } else {
                    throw new RuntimeException("不支持的识别类型: " + type);
                }
                long ocrTime = System.currentTimeMillis() - ocrStartTime;

                // 计算处理时间
                int processingTime = (int) (System.currentTimeMillis() - startTime);

                // 计算置信度（这里简化处理，实际可以根据OCR结果计算）
                BigDecimal confidence = calculateConfidence(finalResult);

                log.debug("OCR识别完成，明细ID: {}, OCR耗时: {}ms, 总耗时: {}ms", detail.getId(), ocrTime, processingTime);

                // 添加调试日志：输出识别结果的关键字段
                if ("INVOICE".equals(type)) {
                    log.info("发票识别结果调试 - 明细ID: {}, 关键字段: amount={}, invoice_number={}, issuer={}, type={}",
                        detail.getId(),
                        finalResult.get("amount"),
                        finalResult.get("invoice_number"),
                        finalResult.get("issuer"),
                        finalResult.get("type"));
                }

                // 更新识别结果
                // recognitionResult存储原始OCR结果，parsedData存储映射后的结构化数据
                String recognitionResult = JSON.toJSONString(rawOcrResult);

                // 检查是否有实际类型信息需要保留
                String existingParsedData = detail.getParsedData();
                String actualType = null;
                if (existingParsedData != null && !existingParsedData.trim().isEmpty()) {
                    try {
                        JSONObject existingJson = JSON.parseObject(existingParsedData);
                        if (existingJson != null && existingJson.containsKey("actualType")) {
                            actualType = existingJson.getString("actualType");
                        }
                    } catch (Exception e) {
                        log.warn("解析现有parsedData失败，明细ID: {}", detail.getId(), e);
                    }
                }

                // 如果有实际类型信息，将其添加到最终结果中
                if (actualType != null) {
                    finalResult.put("actualType", actualType);
                    log.info("保留实际识别类型: {}, 明细ID: {}", actualType, detail.getId());
                }

                String parsedData = JSON.toJSONString(finalResult);

                log.info("保存识别结果 - 明细ID: {}, 原始字段数: {}, 最终字段数: {}",
                    detail.getId(), rawOcrResult.size(), finalResult.size());

                // 更新识别结果（包含原始OCR结果和字段映射结果）
                batchImportDetailMapper.updateRecognitionResult(
                    detail.getId(),
                    recognitionResult,
                    parsedData,
                    confidence,
                    BatchImportDetail.STATUS_SUCCESS,
                    processingTime
                );

                // 同时将字段映射结果保存到final_data字段，作为默认的最终数据
                batchImportDetailMapper.updateFinalData(detail.getId(), parsedData);
                
                result.put("detailId", detail.getId());
                result.put("status", "SUCCESS");
                result.put("data", finalResult);
                result.put("confidence", confidence);
                result.put("processingTime", processingTime);
                
                log.info("图片识别成功，明细ID: {}, 耗时: {}ms", detail.getId(), processingTime);
                
            } catch (Exception e) {
                log.error("图片识别失败，明细ID: {}, 图片URL: {}", detail.getId(), detail.getImageUrl(), e);
                
                // 更新失败状态
                batchImportDetailMapper.updateStatus(detail.getId(), BatchImportDetail.STATUS_FAILED, e.getMessage());
                
                result.put("detailId", detail.getId());
                result.put("status", "FAILED");
                result.put("error", e.getMessage());
            }
            
            return result;
        }, executorService);
    }
    
    /**
     * 计算识别置信度
     * @param ocrResult OCR识别结果
     * @return 置信度
     */
    private BigDecimal calculateConfidence(Map<String, Object> ocrResult) {
        // 这里简化处理，实际应该根据OCR结果中的置信度信息计算
        // 可以根据识别到的字段数量、字段完整性等因素计算
        if (ocrResult == null || ocrResult.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        // 简单的置信度计算：根据识别到的字段数量
        int fieldCount = 0;
        for (Map.Entry<String, Object> entry : ocrResult.entrySet()) {
            if (entry.getValue() != null && !entry.getValue().toString().trim().isEmpty()) {
                fieldCount++;
            }
        }
        
        // 假设最多有10个重要字段，根据识别到的字段数量计算置信度
        double confidence = Math.min(fieldCount / 10.0, 1.0);
        return BigDecimal.valueOf(confidence).setScale(4, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 重新识别失败的图片
     * @param detailId 明细ID
     * @param type 识别类型
     * @return 识别结果
     */
    public CompletableFuture<Map<String, Object>> retryRecognition(Integer detailId, String type) {
        BatchImportDetail detail = batchImportDetailMapper.selectById(detailId);
        if (detail == null) {
            Map<String, Object> result = new HashMap<>();
            result.put("status", "FAILED");
            result.put("error", "明细记录不存在");
            return CompletableFuture.completedFuture(result);
        }
        
        // 增加重试次数
        detail.setRetryCount(detail.getRetryCount() + 1);
        batchImportDetailMapper.updateById(detail);
        
        return recognizeSingleImage(detail, type);
    }
}
