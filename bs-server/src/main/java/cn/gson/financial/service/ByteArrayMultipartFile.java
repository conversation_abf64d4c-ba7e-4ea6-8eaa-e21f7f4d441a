package cn.gson.financial.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * 字节数组MultipartFile实现
 * 用于将字节数组包装为MultipartFile接口
 */
public class ByteArrayMultipartFile implements MultipartFile {
    
    private final byte[] content;
    private final String name;
    private final String originalFilename;
    private final String contentType;
    
    public ByteArrayMultipartFile(byte[] content, String name, String contentType) {
        this.content = content;
        this.name = name;
        this.originalFilename = name;
        this.contentType = contentType;
    }
    
    public ByteArrayMultipartFile(byte[] content, String name, String originalFilename, String contentType) {
        this.content = content;
        this.name = name;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public String getOriginalFilename() {
        return originalFilename;
    }
    
    @Override
    public String getContentType() {
        return contentType;
    }
    
    @Override
    public boolean isEmpty() {
        return content == null || content.length == 0;
    }
    
    @Override
    public long getSize() {
        return content != null ? content.length : 0;
    }
    
    @Override
    public byte[] getBytes() throws IOException {
        return content;
    }
    
    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(content);
    }
    
    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        throw new UnsupportedOperationException("transferTo not supported for ByteArrayMultipartFile");
    }
}
