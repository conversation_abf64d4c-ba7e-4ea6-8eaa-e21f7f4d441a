package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BankReceipts;

import java.util.List;
import java.util.Map;

/**
 * AI智能归并服务接口
 */
public interface AiMergeService {

    /**
     * AI智能票据归并
     * 
     * @param accountSetsId 账套ID
     * @param bills 待归并的票据列表
     * @param currentUserId 当前用户ID
     * @return 归并结果
     */
    Map<String, Object> aiMergeDocuments(Integer accountSetsId, List<Bill> bills, Integer currentUserId);

    /**
     * AI智能银证归并
     * 
     * @param accountSetsId 账套ID
     * @param receipts 待归并的银证列表
     * @param currentUserId 当前用户ID
     * @return 归并结果
     */
    Map<String, Object> aiMergeReceipts(Integer accountSetsId, List<BankReceipts> receipts, Integer currentUserId);

    /**
     * AI分析票据归并建议
     *
     * @param accountSetsId 账套ID
     * @param bills 票据列表
     * @return 归并建议
     */
    Map<String, Object> analyzeMergeDocumentSuggestions(Integer accountSetsId, List<Bill> bills);

    /**
     * AI分析票据归并建议（指定用户）
     *
     * @param accountSetsId 账套ID
     * @param bills 票据列表
     * @param userId 用户ID
     * @return 归并建议
     */
    Map<String, Object> analyzeMergeDocumentSuggestions(Integer accountSetsId, List<Bill> bills, Integer userId);

    /**
     * AI分析银证归并建议
     *
     * @param accountSetsId 账套ID
     * @param receipts 银证列表
     * @return 归并建议
     */
    Map<String, Object> analyzeMergeReceiptSuggestions(Integer accountSetsId, List<BankReceipts> receipts);

    /**
     * AI分析银证归并建议（指定用户）
     *
     * @param accountSetsId 账套ID
     * @param receipts 银证列表
     * @param userId 用户ID
     * @return 归并建议
     */
    Map<String, Object> analyzeMergeReceiptSuggestions(Integer accountSetsId, List<BankReceipts> receipts, Integer userId);

    /**
     * 获取未归并的票据（用于AI分析）
     * 
     * @param accountSetsId 账套ID
     * @param limit 限制数量
     * @return 未归并的票据列表
     */
    List<Bill> getUnmergedDocumentsForAi(Integer accountSetsId, Integer limit);

    /**
     * 获取未归并的银证（用于AI分析）
     *
     * @param accountSetsId 账套ID
     * @param limit 限制数量
     * @return 未归并的银证列表
     */
    List<BankReceipts> getUnmergedReceiptsForAi(Integer accountSetsId, Integer limit);

    /**
     * 执行选中的票据归并
     *
     * @param accountSetsId 账套ID
     * @param selectedGroups 选中的归并组
     * @param currentUserId 当前用户ID
     * @return 归并结果
     */
    Map<String, Object> executeSelectedDocumentMerge(Integer accountSetsId, List<Map<String, Object>> selectedGroups, Integer currentUserId);

    /**
     * 执行选中的银证归并
     *
     * @param accountSetsId 账套ID
     * @param selectedGroups 选中的归并组
     * @param currentUserId 当前用户ID
     * @return 归并结果
     */
    Map<String, Object> executeSelectedReceiptMerge(Integer accountSetsId, List<Map<String, Object>> selectedGroups, Integer currentUserId);

    /**
     * 执行单个票据归并建议
     *
     * @param accountSetsId 账套ID
     * @param billIds 票据ID列表
     * @param groupName 归并组名称
     * @param reason 归并原因
     * @param currentUserId 当前用户ID
     * @return 归并结果
     */
    Map<String, Object> executeSingleDocumentMerge(Integer accountSetsId, List<Integer> billIds, String groupName, String reason, Integer currentUserId);

    /**
     * 执行单个银证归并建议
     *
     * @param accountSetsId 账套ID
     * @param receiptIds 银证ID列表
     * @param groupName 归并组名称
     * @param reason 归并原因
     * @param currentUserId 当前用户ID
     * @return 归并结果
     */
    Map<String, Object> executeSingleReceiptMerge(Integer accountSetsId, List<Integer> receiptIds, String groupName, String reason, Integer currentUserId);
}
