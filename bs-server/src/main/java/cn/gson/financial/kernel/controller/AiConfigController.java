package cn.gson.financial.kernel.controller;

import cn.gson.financial.base.BaseController;
import cn.gson.financial.kernel.common.Result;
import cn.gson.financial.kernel.config.AiConfig;
import cn.gson.financial.kernel.service.AiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.Arrays;

/**
 * AI配置管理控制器
 */
@Api(tags = "AI配置管理")
@RestController
@RequestMapping("/ai-config")
@CrossOrigin
public class AiConfigController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AiConfigController.class);

    @Autowired
    private AiConfig aiConfig;

    @Autowired(required = false)
    private AiService aiService;

    @Autowired(required = false)
    private cn.gson.financial.kernel.service.FinancialAiConfigService financialAiConfigService;
    
    @ApiOperation("获取AI配置")
    @GetMapping("/get")
    public Result<?> getConfig() {
        try {
            Map<String, Object> config = new HashMap<>();

            // 使用用户关联的配置
            if (financialAiConfigService != null && currentUser != null) {
                Map<String, String> userConfig = financialAiConfigService.getAiConfigByUser(currentUser.getId());
                config.put("enabled", "true".equalsIgnoreCase(userConfig.getOrDefault("enabled", "false")));
                config.put("baseUrl", userConfig.getOrDefault("base_url", "https://api.openai.com/v1"));
                config.put("apiKey", maskApiKey(userConfig.getOrDefault("api_key", "")));
                config.put("defaultModel", userConfig.getOrDefault("default_model", "gpt-3.5-turbo"));
                config.put("userId", currentUser.getId());
            } else {
                // 回退到全局配置
                config.put("enabled", aiConfig.isEnabled());
                config.put("baseUrl", aiConfig.getBaseUrl());
                config.put("apiKey", maskApiKey(aiConfig.getApiKey()));
                config.put("defaultModel", aiConfig.getDefaultModel());
                config.put("userId", 0);
            }

            return Result.success(config);

        } catch (Exception e) {
            return Result.error("获取AI配置失败: " + e.getMessage());
        }
    }
    
    @ApiOperation("保存AI配置")
    @PostMapping("/save")
    public Result<?> saveConfig(@RequestBody Map<String, Object> configData) {
        try {
            // 使用当前用户ID
            Integer targetUserId = currentUser != null ? currentUser.getId() : 0;

            // 保存到数据库（用户关联的配置）
            if (financialAiConfigService != null) {
                Map<String, String> configMap = new HashMap<>();

                if (configData.containsKey("enabled")) {
                    configMap.put("enabled", String.valueOf(configData.get("enabled")));
                }

                if (configData.containsKey("baseUrl")) {
                    configMap.put("base_url", (String) configData.get("baseUrl"));
                }

                if (configData.containsKey("apiKey")) {
                    String apiKey = (String) configData.get("apiKey");
                    if (apiKey != null && !apiKey.startsWith("***")) {
                        configMap.put("api_key", apiKey);
                    }
                }

                if (configData.containsKey("defaultModel")) {
                    configMap.put("default_model", (String) configData.get("defaultModel"));
                }

                boolean saved = financialAiConfigService.saveAiConfigByUser(targetUserId, configMap);
                if (saved) {
                    return Result.success("AI配置保存成功（用户ID: " + targetUserId + "）");
                } else {
                    return Result.error("AI配置保存失败");
                }
            } else {
                return Result.error("AI配置服务未初始化");
            }

        } catch (Exception e) {
            return Result.error("保存AI配置失败: " + e.getMessage());
        }
    }
    
    @ApiOperation("测试AI连接")
    @PostMapping("/test")
    public Result<?> testConnection(@RequestBody Map<String, Object> configData) {
        try {
            // 使用传入的配置进行测试，不修改全局配置
            String testApiKey = null;
            String testBaseUrl = "https://api.openai.com/v1";
            String testModel = "gpt-3.5-turbo";

            if (configData.containsKey("apiKey")) {
                String apiKey = (String) configData.get("apiKey");
                if (apiKey != null && !apiKey.startsWith("***")) {
                    testApiKey = apiKey;
                }
            }

            if (configData.containsKey("baseUrl")) {
                testBaseUrl = (String) configData.get("baseUrl");
            }

            if (configData.containsKey("defaultModel")) {
                testModel = (String) configData.get("defaultModel");
            }

            // 如果没有提供API密钥，尝试从当前用户配置中获取
            if (testApiKey == null && financialAiConfigService != null && currentUser != null) {
                testApiKey = financialAiConfigService.getConfigValueByUser(currentUser.getId(), "api_key");
            }

            if (testApiKey == null || testApiKey.trim().isEmpty()) {
                return Result.error("API密钥未配置");
            }

            // 直接测试API连接
            String testResult = testApiConnectionWithConfig(testBaseUrl, testApiKey, testModel);
            if (testResult.startsWith("SUCCESS")) {
                return Result.success("AI服务连接测试成功: " + testResult.substring(8));
            } else {
                return Result.error("AI服务连接失败: " + testResult);
            }

        } catch (Exception e) {
            return Result.error("连接测试失败: " + e.getMessage());
        }
    }
    
    @ApiOperation("测试AI功能")
    @PostMapping("/test-function")
    public Result<?> testFunction(@RequestBody Map<String, Object> configData) {
        try {
            // 临时更新配置进行测试
            String originalApiKey = aiConfig.getApiKey();
            String originalBaseUrl = aiConfig.getBaseUrl();
            String originalModel = aiConfig.getDefaultModel();
            boolean originalEnabled = aiConfig.isEnabled();

            try {
                // 应用测试配置
                if (configData.containsKey("apiKey")) {
                    String apiKey = (String) configData.get("apiKey");
                    if (apiKey != null && !apiKey.startsWith("***")) {
                        aiConfig.setApiKey(apiKey);
                    }
                }

                if (configData.containsKey("baseUrl")) {
                    aiConfig.setBaseUrl((String) configData.get("baseUrl"));
                }

                if (configData.containsKey("defaultModel")) {
                    aiConfig.setDefaultModel((String) configData.get("defaultModel"));
                }

                aiConfig.setEnabled(true);
                
                // 测试AI功能
                if (aiService != null) {
                    String response;
                    if (currentUser != null && aiService.isAvailable(currentUser.getId())) {
                        response = aiService.chat("你好，请简单介绍一下你的功能。", currentUser.getId());
                    } else if (aiService.isAvailable()) {
                        response = aiService.chat("你好，请简单介绍一下你的功能。");
                    } else {
                        return Result.error("AI服务不可用");
                    }

                    if (response != null && !response.trim().isEmpty()) {
                        return Result.success("AI功能测试成功，响应: " + response.substring(0, Math.min(100, response.length())));
                    } else {
                        return Result.error("AI功能测试失败，未收到有效响应");
                    }
                } else {
                    return Result.error("AI服务不可用");
                }
                
            } finally {
                // 恢复原始配置
                aiConfig.setApiKey(originalApiKey);
                aiConfig.setBaseUrl(originalBaseUrl);
                aiConfig.setDefaultModel(originalModel);
                aiConfig.setEnabled(originalEnabled);
            }
            
        } catch (Exception e) {
            return Result.error("功能测试失败: " + e.getMessage());
        }
    }
    
    @ApiOperation("获取AI服务状态")
    @GetMapping("/status")
    public Result<?> getStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 优先检查用户配置
            if (financialAiConfigService != null && currentUser != null) {
                Map<String, String> userConfig = financialAiConfigService.getAiConfigByUser(currentUser.getId());
                boolean userEnabled = "true".equalsIgnoreCase(userConfig.getOrDefault("enabled", "false"));
                boolean hasValidUserConfig = userEnabled &&
                    userConfig.get("base_url") != null && !userConfig.get("base_url").trim().isEmpty() &&
                    userConfig.get("api_key") != null && !userConfig.get("api_key").trim().isEmpty() &&
                    userConfig.get("default_model") != null && !userConfig.get("default_model").trim().isEmpty();

                status.put("enabled", userEnabled);
                status.put("hasValidConfig", hasValidUserConfig);
                status.put("available", aiService != null && aiService.isAvailable(currentUser.getId()));
                status.put("model", userConfig.getOrDefault("default_model", ""));
                status.put("baseUrl", userConfig.getOrDefault("base_url", ""));
                status.put("configSource", "user");
                status.put("userId", currentUser.getId());
            } else {
                // 回退到全局配置
                boolean hasValidConfig = aiConfig.getBaseUrl() != null && !aiConfig.getBaseUrl().trim().isEmpty() &&
                                       aiConfig.getApiKey() != null && !aiConfig.getApiKey().trim().isEmpty() &&
                                       aiConfig.getDefaultModel() != null && !aiConfig.getDefaultModel().trim().isEmpty();

                status.put("enabled", aiConfig.isEnabled());
                status.put("hasValidConfig", hasValidConfig);
                status.put("available", aiService != null && aiService.isAvailable());
                status.put("model", aiConfig.getDefaultModel());
                status.put("baseUrl", aiConfig.getBaseUrl());
                status.put("configSource", "global");
                status.put("userId", 0);
            }

            return Result.success(status);

        } catch (Exception e) {
            return Result.error("获取AI状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用模型列表
     */
    @PostMapping("/models")
    public Result<Object> getModels(@RequestBody Map<String, Object> configData) {
        try {
            // 临时更新配置进行模型列表获取
            String originalApiKey = aiConfig.getApiKey();
            String originalBaseUrl = aiConfig.getBaseUrl();
            boolean originalEnabled = aiConfig.isEnabled();

            try {
                // 应用临时配置
                if (configData.containsKey("apiKey")) {
                    String apiKey = (String) configData.get("apiKey");
                    if (apiKey != null && !apiKey.startsWith("***")) {
                        aiConfig.setApiKey(apiKey);
                    }
                }

                if (configData.containsKey("baseUrl")) {
                    aiConfig.setBaseUrl((String) configData.get("baseUrl"));
                }

                aiConfig.setEnabled(true);

                // 获取模型列表
                if (aiService != null) {
                    if (currentUser != null) {
                        return Result.success(aiService.getAvailableModels(currentUser.getId()));
                    } else {
                        return Result.success(aiService.getAvailableModels());
                    }
                } else {
                    // AI服务未初始化时，直接调用API获取模型列表
                    return Result.success(getModelsDirectly());
                }

            } finally {
                // 恢复原始配置
                aiConfig.setApiKey(originalApiKey);
                aiConfig.setBaseUrl(originalBaseUrl);
                aiConfig.setEnabled(originalEnabled);
            }

        } catch (Exception e) {
            return Result.error("获取模型列表失败: " + e.getMessage());
        }
    }

    /**
     * 直接调用API获取模型列表（当AI服务未初始化时使用）
     */
    private List<Map<String, Object>> getModelsDirectly() {
        try {
            RestTemplate restTemplate = createRestTemplateWithTimeout();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(aiConfig.getApiKey());

            HttpEntity<String> entity = new HttpEntity<>(headers);
            String url = aiConfig.getBaseUrl() + "/models";

            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseBody = (Map<String, Object>) response.getBody();

                if (responseBody != null && responseBody.containsKey("data")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> models = (List<Map<String, Object>>) responseBody.get("data");

                    // 格式化模型列表
                    List<Map<String, Object>> formattedModels = new ArrayList<>();
                    for (Map<String, Object> model : models) {
                        Map<String, Object> formattedModel = new HashMap<>();
                        formattedModel.put("id", model.get("id"));
                        formattedModel.put("name", model.get("id"));
                        formattedModel.put("owned_by", model.get("owned_by"));
                        formattedModel.put("created", model.get("created"));
                        formattedModels.add(formattedModel);
                    }

                    return formattedModels;
                }
            }

            // API调用失败，返回默认模型列表
            return getDefaultModels();

        } catch (Exception e) {
            logger.error("直接获取模型列表失败", e);
            return getDefaultModels();
        }
    }

    /**
     * 获取默认模型列表
     */
    private List<Map<String, Object>> getDefaultModels() {
        List<Map<String, Object>> defaultModels = new ArrayList<>();

        String baseUrl = aiConfig.getBaseUrl().toLowerCase();

        if (baseUrl.contains("deepseek")) {
            defaultModels.add(createModelInfo("deepseek-chat", "DeepSeek Chat", "deepseek"));
            defaultModels.add(createModelInfo("deepseek-coder", "DeepSeek Coder", "deepseek"));
        } else if (baseUrl.contains("dashscope")) {
            defaultModels.add(createModelInfo("qwen-turbo", "Qwen Turbo", "alibaba"));
            defaultModels.add(createModelInfo("qwen-plus", "Qwen Plus", "alibaba"));
            defaultModels.add(createModelInfo("qwen-max", "Qwen Max", "alibaba"));
        } else {
            // OpenAI 或其他兼容服务
            defaultModels.add(createModelInfo("gpt-3.5-turbo", "GPT-3.5 Turbo", "openai"));
            defaultModels.add(createModelInfo("gpt-4", "GPT-4", "openai"));
            defaultModels.add(createModelInfo("gpt-4-turbo", "GPT-4 Turbo", "openai"));
        }

        return defaultModels;
    }

    /**
     * 创建模型信息对象
     */
    private Map<String, Object> createModelInfo(String id, String name, String ownedBy) {
        Map<String, Object> model = new HashMap<>();
        model.put("id", id);
        model.put("name", name);
        model.put("owned_by", ownedBy);
        model.put("created", System.currentTimeMillis() / 1000);
        return model;
    }

    /**
     * 创建带超时配置的RestTemplate
     */
    private RestTemplate createRestTemplateWithTimeout() {
        RestTemplate restTemplate = new RestTemplate();

        // 设置连接超时和读取超时
        restTemplate.getRequestFactory();
        if (restTemplate.getRequestFactory() instanceof org.springframework.http.client.SimpleClientHttpRequestFactory) {
            org.springframework.http.client.SimpleClientHttpRequestFactory factory =
                (org.springframework.http.client.SimpleClientHttpRequestFactory) restTemplate.getRequestFactory();
            factory.setConnectTimeout(10000); // 10秒连接超时
            factory.setReadTimeout(30000);    // 30秒读取超时
        }

        return restTemplate;
    }

    /**
     * 使用指定配置测试API连接
     */
    private String testApiConnectionWithConfig(String baseUrl, String apiKey, String model) {
        try {
            // 创建带超时配置的RestTemplate
            RestTemplate restTemplate = createRestTemplateWithTimeout();

            // 首先测试模型列表接口
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            String modelsUrl = baseUrl + "/models";

            logger.info("测试API连接: {}", modelsUrl);
            logger.info("使用API密钥: {}***", apiKey.substring(0, Math.min(8, apiKey.length())));

            try {
                @SuppressWarnings("rawtypes")
                ResponseEntity<Map> modelsResponse = restTemplate.exchange(modelsUrl, HttpMethod.GET, entity, Map.class);

                if (modelsResponse.getStatusCode().is2xxSuccessful()) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> responseBody = (Map<String, Object>) modelsResponse.getBody();

                    if (responseBody != null && responseBody.containsKey("data")) {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> models = (List<Map<String, Object>>) responseBody.get("data");
                        return "SUCCESS: 模型列表获取成功，共 " + models.size() + " 个模型";
                    } else {
                        return "SUCCESS: API连接成功，但响应格式异常";
                    }
                } else {
                    return "ERROR: HTTP状态码 " + modelsResponse.getStatusCode();
                }
            } catch (Exception e) {
                logger.warn("模型列表接口测试失败，尝试聊天接口: {}", e.getMessage());
                return testChatApiWithConfig(restTemplate, baseUrl, apiKey, model);
            }

        } catch (Exception e) {
            logger.error("API连接测试失败", e);
            return "ERROR: " + e.getMessage();
        }
    }

    /**
     * 直接测试API连接
     */
    private String testApiConnection() {
        try {
            // 创建带超时配置的RestTemplate
            RestTemplate restTemplate = createRestTemplateWithTimeout();

            // 首先测试模型列表接口
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(aiConfig.getApiKey());

            HttpEntity<String> entity = new HttpEntity<>(headers);
            String modelsUrl = aiConfig.getBaseUrl() + "/models";

            logger.info("测试API连接: {}", modelsUrl);
            logger.info("使用API密钥: {}***", aiConfig.getApiKey().substring(0, Math.min(8, aiConfig.getApiKey().length())));

            try {
                @SuppressWarnings("rawtypes")
                ResponseEntity<Map> modelsResponse = restTemplate.exchange(modelsUrl, HttpMethod.GET, entity, Map.class);

                if (modelsResponse.getStatusCode().is2xxSuccessful()) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> responseBody = (Map<String, Object>) modelsResponse.getBody();

                    if (responseBody != null && responseBody.containsKey("data")) {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> models = (List<Map<String, Object>>) responseBody.get("data");
                        return "SUCCESS: 模型列表获取成功，共 " + models.size() + " 个模型";
                    } else {
                        return "SUCCESS: API连接成功，但响应格式异常";
                    }
                } else {
                    return "ERROR: 模型列表API返回错误状态: " + modelsResponse.getStatusCode();
                }
            } catch (Exception modelsEx) {
                logger.warn("模型列表API测试失败，尝试聊天API: {}", modelsEx.getMessage());

                // 如果模型列表失败，尝试聊天API
                return testChatApi(restTemplate, headers);
            }

        } catch (Exception e) {
            logger.error("API连接测试失败", e);
            return "ERROR: " + e.getMessage();
        }
    }

    /**
     * 使用指定配置测试聊天API
     */
    private String testChatApiWithConfig(RestTemplate restTemplate, String baseUrl, String apiKey, String model) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);

            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", "Hello");
            requestBody.put("messages", Arrays.asList(message));
            requestBody.put("max_tokens", 10);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            String chatUrl = baseUrl + "/chat/completions";

            logger.info("测试聊天API: {}", chatUrl);
            logger.info("使用模型: {}", model);

            ResponseEntity<String> response = restTemplate.exchange(chatUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                return "SUCCESS: 聊天API连接成功，模型: " + model;
            } else {
                return "ERROR: 聊天API返回错误状态: " + response.getStatusCode() + ", 响应: " + response.getBody();
            }

        } catch (Exception e) {
            logger.error("聊天API测试失败", e);
            return "ERROR: 聊天API测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试聊天API
     */
    private String testChatApi(RestTemplate restTemplate, HttpHeaders headers) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", aiConfig.getDefaultModel());

            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", "Hello");
            requestBody.put("messages", Arrays.asList(message));
            requestBody.put("max_tokens", 10);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            String chatUrl = aiConfig.getBaseUrl() + "/chat/completions";

            logger.info("测试聊天API: {}", chatUrl);
            logger.info("使用模型: {}", aiConfig.getDefaultModel());

            ResponseEntity<String> response = restTemplate.exchange(chatUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                return "SUCCESS: 聊天API连接成功，模型: " + aiConfig.getDefaultModel();
            } else {
                return "ERROR: 聊天API返回错误状态: " + response.getStatusCode() + ", 响应: " + response.getBody();
            }

        } catch (Exception e) {
            logger.error("聊天API测试失败", e);
            return "ERROR: 聊天API测试失败: " + e.getMessage();
        }
    }

    /**
     * 掩码API密钥
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return "";
        }

        if (apiKey.length() <= 8) {
            return "***";
        }

        return apiKey.substring(0, 4) + "***" + apiKey.substring(apiKey.length() - 4);
    }


}
