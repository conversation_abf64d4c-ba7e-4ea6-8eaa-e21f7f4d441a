package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.entity.EntityRelation;
import cn.gson.financial.kernel.model.mapper.BillMapper;
import cn.gson.financial.kernel.model.mapper.BankReceiptsMapper;
import cn.gson.financial.kernel.model.mapper.EntityRelationMapper;
import cn.gson.financial.kernel.service.AiRelationService;
import cn.gson.financial.kernel.service.AiService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.Date;

/**
 * AI智能关联服务实现
 */
@Slf4j
@Service
public class AiRelationServiceImpl implements AiRelationService {

    @Autowired
    private AiService aiService;

    @Autowired
    private BillMapper billMapper;

    @Autowired
    private BankReceiptsMapper bankReceiptsMapper;

    @Autowired
    private EntityRelationMapper entityRelationMapper;

    @Override
    public Map<String, Object> analyzeRelations(Integer accountSetsId, List<Bill> bills, List<BankReceipts> receipts, Integer currentUserId) {
        log.info("开始AI分析关联关系，账套ID: {}, 票据数量: {}, 银证数量: {}", accountSetsId, bills.size(), receipts.size());

        try {
            if (bills.isEmpty() || receipts.isEmpty()) {
                return createSuccessResult("没有足够的数据进行关联分析", 0);
            }

            // 构建AI分析的提示词
            String prompt = buildRelationAnalysisPrompt(bills, receipts);
            log.info("构建的AI提示词长度: {}", prompt.length());

            // 调用AI服务
            if (aiService != null) {
                log.info("调用AI服务进行关联分析...");
                String aiResponse;

                // 优先使用用户配置的AI服务
                if (currentUserId != null && aiService.isAvailable(currentUserId)) {
                    log.info("使用用户{}的AI配置进行分析", currentUserId);
                    aiResponse = aiService.chat(prompt, currentUserId);
                } else {
                    log.info("使用全局AI配置进行分析");
                    aiResponse = aiService.chat(prompt);
                }

                log.info("AI服务响应长度: {}", aiResponse != null ? aiResponse.length() : 0);

                // 解析AI响应
                Map<String, Object> result = parseAiRelationResponse(aiResponse);
                log.info("AI分析完成，发现关联数量: {}",
                    result.containsKey("relations") ? ((List<?>) result.get("relations")).size() : 0);
                return result;
            } else {
                log.warn("AI服务未配置，使用模拟数据");
                return generateMockRelationResult(bills, receipts);
            }

        } catch (Exception e) {
            log.error("AI分析关联关系失败", e);
            // 如果AI调用失败，返回模拟数据作为fallback
            log.info("AI调用失败，使用模拟数据作为fallback");
            return generateMockRelationResult(bills, receipts);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeAiRelations(Integer accountSetsId, List<Bill> bills, List<BankReceipts> receipts, Integer currentUserId) {
        log.info("开始AI执行智能关联，账套ID: {}, 票据数量: {}, 银证数量: {}", accountSetsId, bills.size(), receipts.size());

        try {
            // 1. 先分析关联关系
            Map<String, Object> analysisResult = analyzeRelations(accountSetsId, bills, receipts, currentUserId);
            
            if (!analysisResult.containsKey("relations") || !(analysisResult.get("relations") instanceof List)) {
                return createErrorResult("AI分析失败，无法获取关联建议");
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> relations = (List<Map<String, Object>>) analysisResult.get("relations");
            
            if (relations.isEmpty()) {
                return createSuccessResult("AI分析完成，未发现可关联的组合", 0);
            }

            // 2. 执行关联操作
            int relationCount = 0;
            List<String> createdRelations = new ArrayList<>();

            for (Map<String, Object> relation : relations) {
                Integer billId = (Integer) relation.get("billId");
                Integer receiptId = (Integer) relation.get("receiptId");
                String relationType = (String) relation.get("relationType");
                String reason = (String) relation.get("reason");
                Double confidence = (Double) relation.get("confidence");

                if (billId != null && receiptId != null) {
                    // 检查是否已存在关联
                    if (!isRelationExists(billId, receiptId)) {
                        // 创建关联关系
                        EntityRelation entityRelation = createEntityRelation(
                            accountSetsId, billId, receiptId, relationType, reason, confidence, currentUserId);

                        entityRelationMapper.insert(entityRelation);
                        relationCount++;
                        createdRelations.add(String.format("票据%d ↔ 银证%d (%s)", billId, receiptId, relationType));

                        log.info("AI创建关联关系: 票据{} ↔ 银证{}, 类型: {}, 原因: {}, 置信度: {}", 
                            billId, receiptId, relationType, reason, confidence);
                    }
                }
            }

            return createSuccessResult(
                String.format("AI智能关联完成，创建了%d个关联关系", relationCount),
                relationCount, createdRelations
            );

        } catch (Exception e) {
            log.error("AI智能关联失败", e);
            return createErrorResult("AI智能关联失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> analyzeReceiptRelationSuggestions(Integer accountSetsId, BankReceipts receipt) {
        log.info("开始AI分析银证关联建议，账套ID: {}, 银证ID: {}", accountSetsId, receipt.getId());

        try {
            // 获取可能相关的票据
            List<Bill> candidateBills = getCandidateBillsForReceipt(accountSetsId, receipt);
            
            if (candidateBills.isEmpty()) {
                return createSuccessResult("没有找到可能相关的票据", 0);
            }

            return analyzeRelations(accountSetsId, candidateBills, Arrays.asList(receipt), null);

        } catch (Exception e) {
            log.error("AI分析银证关联建议失败", e);
            return createErrorResult("AI分析失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> analyzeBillRelationSuggestions(Integer accountSetsId, Bill bill) {
        log.info("开始AI分析票据关联建议，账套ID: {}, 票据ID: {}", accountSetsId, bill.getId());

        try {
            // 获取可能相关的银证
            List<BankReceipts> candidateReceipts = getCandidateReceiptsForBill(accountSetsId, bill);
            
            if (candidateReceipts.isEmpty()) {
                return createSuccessResult("没有找到可能相关的银证", 0);
            }

            return analyzeRelations(accountSetsId, Arrays.asList(bill), candidateReceipts, null);

        } catch (Exception e) {
            log.error("AI分析票据关联建议失败", e);
            return createErrorResult("AI分析失败: " + e.getMessage());
        }
    }

    @Override
    public List<Bill> getUnrelatedBills(Integer accountSetsId, Integer limit) {
        // 查询没有关联关系的票据（使用正确的表名）
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .notExists("SELECT 1 FROM fxy_financial_entity_relations r WHERE " +
                             "(r.source_type LIKE 'DOCUMENT%' AND r.source_id = CAST(fxy_financial_bill.id AS CHAR)) OR " +
                             "(r.target_type LIKE 'DOCUMENT%' AND r.target_id = CAST(fxy_financial_bill.id AS CHAR))")
                   .orderByDesc("create_time");

        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        }

        return billMapper.selectList(queryWrapper);
    }

    @Override
    public List<BankReceipts> getUnrelatedReceipts(Integer accountSetsId, Integer limit) {
        // 查询没有关联关系的银证（使用正确的表名和字段）
        QueryWrapper<BankReceipts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .notExists("SELECT 1 FROM fxy_financial_entity_relations r WHERE " +
                             "(r.source_type LIKE 'RECEIPT%' AND r.source_id = CAST(fxy_financial_bank_receipts.id AS CHAR)) OR " +
                             "(r.target_type LIKE 'RECEIPT%' AND r.target_id = CAST(fxy_financial_bank_receipts.id AS CHAR))")
                   .orderByDesc("create_date");

        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        }

        return bankReceiptsMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> autoRelateAll(Integer accountSetsId, Integer currentUserId, Integer limit) {
        log.info("开始AI自动关联所有未关联数据，账套ID: {}, 限制数量: {}", accountSetsId, limit);

        try {
            // 获取未关联的票据和银证
            List<Bill> bills = getUnrelatedBills(accountSetsId, limit);
            List<BankReceipts> receipts = getUnrelatedReceipts(accountSetsId, limit);

            if (bills.isEmpty() || receipts.isEmpty()) {
                return createSuccessResult("没有足够的未关联数据", 0);
            }

            // 执行AI关联
            return executeAiRelations(accountSetsId, bills, receipts, currentUserId);

        } catch (Exception e) {
            log.error("AI自动关联失败", e);
            return createErrorResult("AI自动关联失败: " + e.getMessage());
        }
    }

    // 私有辅助方法
    private String buildRelationAnalysisPrompt(List<Bill> bills, List<BankReceipts> receipts) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("# 财务票据与银行回单智能关联分析\n\n");
        prompt.append("你是一位资深的财务数据分析专家，具备深厚的会计学和财务管理知识。请运用专业的财务分析能力，");
        prompt.append("对以下票据和银行回单数据进行深度关联分析，识别它们之间的业务关系。\n\n");

        prompt.append("## 分析维度和权重\n");
        prompt.append("1. **金额匹配** (权重40%): 完全匹配 > 含税差异 > 合理误差范围(±5%)\n");
        prompt.append("2. **时间关联** (权重25%): 同日 > 3天内 > 一周内 > 一月内\n");
        prompt.append("3. **交易对手** (权重20%): 完全匹配 > 部分匹配 > 关联企业\n");
        prompt.append("4. **业务语义** (权重15%): 摘要关键词匹配 > 业务类型匹配 > 用途相似\n\n");

        prompt.append("## 关联类型定义\n");
        prompt.append("- **PAYMENT**: 票据对应的付款业务（发票→付款回单）\n");
        prompt.append("- **RECEIPT**: 票据对应的收款业务（收据→收款回单）\n");
        prompt.append("- **REFUND**: 退款相关业务（退款单→退款回单）\n");
        prompt.append("- **TRANSFER**: 内部转账业务（转账凭证→转账回单）\n\n");

        prompt.append("## 置信度评估标准\n");
        prompt.append("- **0.9-1.0**: 高置信度，金额完全匹配+时间相近+交易对手一致\n");
        prompt.append("- **0.7-0.9**: 中高置信度，主要维度匹配，个别维度有差异\n");
        prompt.append("- **0.5-0.7**: 中等置信度，部分维度匹配，需人工确认\n");
        prompt.append("- **0.3-0.5**: 低置信度，仅少数维度匹配，仅供参考\n");
        prompt.append("- **<0.3**: 不建议关联\n\n");
        
        prompt.append("## 待分析数据\n\n");
        prompt.append("### 票据数据 (共").append(bills.size()).append("张)\n");
        for (int i = 0; i < Math.min(bills.size(), 20); i++) { // 限制显示数量
            Bill bill = bills.get(i);
            prompt.append(String.format("**票据%d** [ID:%d]\n", i + 1, bill.getId()));
            prompt.append(String.format("- 开票方: %s\n", bill.getIssuer() != null ? bill.getIssuer() : "未知"));
            prompt.append(String.format("- 收票方: %s\n", bill.getRecipient() != null ? bill.getRecipient() : "未知"));
            prompt.append(String.format("- 金额: ¥%s\n", bill.getAmount() != null ? bill.getAmount().toString() : "0"));
            prompt.append(String.format("- 日期: %s\n", bill.getBillDate() != null ? bill.getBillDate().toString() : "未知"));
            prompt.append(String.format("- 摘要: %s\n", bill.getSummary() != null ? bill.getSummary() : "未知"));
            prompt.append("\n");
        }
        if (bills.size() > 20) {
            prompt.append("... (还有").append(bills.size() - 20).append("张票据)\n\n");
        }

        prompt.append("### 银行回单数据 (共").append(receipts.size()).append("张)\n");
        for (int i = 0; i < Math.min(receipts.size(), 20); i++) { // 限制显示数量
            BankReceipts receipt = receipts.get(i);
            prompt.append(String.format("**银证%d** [ID:%d]\n", i + 1, receipt.getId()));
            // 根据类型显示收付款人信息
            String counterparty = "未知";
            if ("收入".equals(receipt.getType())) {
                counterparty = receipt.getPayerName() != null ? receipt.getPayerName() :
                              (receipt.getPayerAccount() != null ? receipt.getPayerAccount() : "未知付款人");
            } else {
                counterparty = receipt.getPayeeName() != null ? receipt.getPayeeName() :
                              (receipt.getPayeeAccount() != null ? receipt.getPayeeAccount() : "未知收款人");
            }
            prompt.append(String.format("- 交易对手: %s\n", counterparty));
            prompt.append(String.format("- 金额: ¥%s\n", receipt.getAmount() != null ? receipt.getAmount().toString() : "0"));
            prompt.append(String.format("- 日期: %s\n", receipt.getReceiptsDate() != null ? receipt.getReceiptsDate().toString() : "未知"));
            prompt.append(String.format("- 类型: %s\n", receipt.getType() != null ? receipt.getType() : "未知"));
            prompt.append(String.format("- 摘要: %s\n", receipt.getSummary() != null ? receipt.getSummary() : "未知"));
            prompt.append("\n");
        }
        if (receipts.size() > 20) {
            prompt.append("... (还有").append(receipts.size() - 20).append("张银行回单)\n\n");
        }
        
        prompt.append("## 分析要求\n\n");
        prompt.append("请基于以上数据进行深度分析，运用财务专业知识识别票据与银行回单之间的关联关系。");
        prompt.append("重点关注业务逻辑的合理性，避免误匹配。\n\n");

        prompt.append("## 输出格式\n\n");
        prompt.append("请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：\n\n");
        prompt.append("```json\n");
        prompt.append("{\n");
        prompt.append("  \"relations\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"billId\": 票据ID(整数),\n");
        prompt.append("      \"receiptId\": 银证ID(整数),\n");
        prompt.append("      \"relationType\": \"关联类型(PAYMENT/RECEIPT/REFUND/TRANSFER)\",\n");
        prompt.append("      \"reason\": \"详细的关联分析原因，包含匹配的具体维度和业务逻辑\",\n");
        prompt.append("      \"confidence\": 置信度(0-1之间的小数，保留2位小数),\n");
        prompt.append("      \"matchFactors\": {\n");
        prompt.append("        \"amountMatch\": true/false,\n");
        prompt.append("        \"timeMatch\": true/false,\n");
        prompt.append("        \"counterpartyMatch\": true/false,\n");
        prompt.append("        \"semanticMatch\": true/false\n");
        prompt.append("      }\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n");
        prompt.append("```\n\n");
        prompt.append("注意：\n");
        prompt.append("1. 只返回置信度≥0.5的关联建议\n");
        prompt.append("2. 最多返回20个关联建议\n");
        prompt.append("3. 按置信度从高到低排序\n");
        prompt.append("4. 确保JSON格式正确，可以被程序解析\n");
        
        return prompt.toString();
    }

    private Map<String, Object> parseAiRelationResponse(String aiResponse) {
        log.info("开始解析AI响应，响应长度: {}", aiResponse != null ? aiResponse.length() : 0);

        try {
            // 尝试从AI响应中提取JSON
            String jsonStr = extractJsonFromResponse(aiResponse);
            log.info("提取的JSON字符串长度: {}", jsonStr.length());

            JSONObject jsonResponse = JSON.parseObject(jsonStr);

            if (jsonResponse.containsKey("relations")) {
                JSONArray relations = jsonResponse.getJSONArray("relations");
                List<Map<String, Object>> relationList = new ArrayList<>();

                for (int i = 0; i < relations.size(); i++) {
                    JSONObject relation = relations.getJSONObject(i);
                    Map<String, Object> relationMap = new HashMap<>();

                    // 基本信息
                    relationMap.put("billId", relation.getInteger("billId"));
                    relationMap.put("receiptId", relation.getInteger("receiptId"));
                    relationMap.put("relationType", relation.getString("relationType"));
                    relationMap.put("reason", relation.getString("reason"));
                    relationMap.put("confidence", relation.getDouble("confidence"));

                    // 匹配因子（如果存在）
                    if (relation.containsKey("matchFactors")) {
                        JSONObject matchFactors = relation.getJSONObject("matchFactors");
                        Map<String, Object> factors = new HashMap<>();
                        factors.put("amountMatch", matchFactors.getBooleanValue("amountMatch"));
                        factors.put("timeMatch", matchFactors.getBooleanValue("timeMatch"));
                        factors.put("counterpartyMatch", matchFactors.getBooleanValue("counterpartyMatch"));
                        factors.put("semanticMatch", matchFactors.getBooleanValue("semanticMatch"));
                        relationMap.put("factors", factors);
                    }

                    relationList.add(relationMap);
                }

                // 按置信度排序
                relationList.sort((a, b) -> {
                    Double confA = (Double) a.get("confidence");
                    Double confB = (Double) b.get("confidence");
                    return confB.compareTo(confA);
                });

                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("relations", relationList);
                result.put("message", String.format("AI分析完成，发现%d个可能的关联关系", relationList.size()));

                log.info("AI响应解析成功，关联数量: {}", relationList.size());
                return result;
            }

        } catch (Exception e) {
            log.warn("解析AI响应失败: {}", e.getMessage(), e);
        }

        // 如果JSON解析失败，返回空结果
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("relations", new ArrayList<>());
        result.put("message", "AI分析完成，但响应格式异常，未发现明确的关联建议");

        log.warn("AI响应解析失败，返回空结果");
        return result;
    }

    private String extractJsonFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return "{}";
        }

        log.info("原始AI响应: {}", response.substring(0, Math.min(response.length(), 500)) + "...");

        // 移除markdown代码块标记
        String cleaned = response.replaceAll("```json\\s*", "").replaceAll("```\\s*", "");

        // 尝试提取JSON部分
        int jsonStart = cleaned.indexOf("{");
        int jsonEnd = findMatchingBrace(cleaned, jsonStart);

        if (jsonStart >= 0 && jsonEnd > jsonStart) {
            String jsonStr = cleaned.substring(jsonStart, jsonEnd + 1);
            log.info("提取的JSON: {}", jsonStr.substring(0, Math.min(jsonStr.length(), 500)) + "...");

            // 验证JSON格式
            if (isValidJson(jsonStr)) {
                return jsonStr;
            }
        }

        // 如果没有找到完整的JSON，尝试查找relations数组
        if (cleaned.contains("\"relations\"")) {
            int relationsStart = cleaned.indexOf("\"relations\"");
            int arrayStart = cleaned.indexOf("[", relationsStart);
            int arrayEnd = findMatchingBracket(cleaned, arrayStart);

            if (arrayStart >= 0 && arrayEnd > arrayStart) {
                String relationsArray = cleaned.substring(arrayStart, arrayEnd + 1);
                String constructedJson = "{\"relations\":" + relationsArray + "}";

                if (isValidJson(constructedJson)) {
                    log.info("构造的JSON: {}", constructedJson.substring(0, Math.min(constructedJson.length(), 500)) + "...");
                    return constructedJson;
                }
            } else if (arrayStart >= 0) {
                // 如果找不到匹配的结束括号，可能JSON被截断了，尝试修复
                log.warn("JSON可能被截断，尝试修复...");
                String partialArray = cleaned.substring(arrayStart);
                String repairedJson = repairTruncatedJson(partialArray);
                if (repairedJson != null && isValidJson(repairedJson)) {
                    log.info("修复的JSON: {}", repairedJson.substring(0, Math.min(repairedJson.length(), 500)) + "...");
                    return repairedJson;
                }
            }
        }

        log.warn("无法从AI响应中提取有效JSON，使用fallback");
        return "{}";
    }

    private int findMatchingBrace(String text, int start) {
        if (start < 0 || start >= text.length() || text.charAt(start) != '{') {
            return -1;
        }

        int count = 1;
        for (int i = start + 1; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == '{') {
                count++;
            } else if (c == '}') {
                count--;
                if (count == 0) {
                    return i;
                }
            }
        }
        return -1;
    }

    private int findMatchingBracket(String text, int start) {
        if (start < 0 || start >= text.length() || text.charAt(start) != '[') {
            return -1;
        }

        int count = 1;
        for (int i = start + 1; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == '[') {
                count++;
            } else if (c == ']') {
                count--;
                if (count == 0) {
                    return i;
                }
            }
        }
        return -1;
    }

    private boolean isValidJson(String jsonStr) {
        try {
            JSON.parseObject(jsonStr);
            return true;
        } catch (Exception e) {
            log.debug("JSON验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 修复被截断的JSON
     */
    private String repairTruncatedJson(String partialJson) {
        try {
            // 查找最后一个完整的对象
            int lastCompleteObject = -1;
            int braceCount = 0;
            boolean inString = false;
            boolean escaped = false;

            for (int i = 0; i < partialJson.length(); i++) {
                char c = partialJson.charAt(i);

                if (escaped) {
                    escaped = false;
                    continue;
                }

                if (c == '\\') {
                    escaped = true;
                    continue;
                }

                if (c == '"') {
                    inString = !inString;
                    continue;
                }

                if (!inString) {
                    if (c == '{') {
                        braceCount++;
                    } else if (c == '}') {
                        braceCount--;
                        if (braceCount == 0) {
                            lastCompleteObject = i;
                        }
                    }
                }
            }

            if (lastCompleteObject > 0) {
                // 截取到最后一个完整对象
                String truncatedArray = partialJson.substring(0, lastCompleteObject + 1);

                // 确保数组正确结束
                if (!truncatedArray.endsWith("]")) {
                    truncatedArray += "]";
                }

                String repairedJson = "{\"relations\":" + truncatedArray + "}";
                log.info("尝试修复JSON，原长度: {}, 修复后长度: {}", partialJson.length(), repairedJson.length());

                return repairedJson;
            }

        } catch (Exception e) {
            log.warn("修复JSON失败: {}", e.getMessage());
        }

        return null;
    }

    private boolean isRelationExists(Integer billId, Integer receiptId) {
        // 使用EntityRelationMapper的现有方法检查关联是否存在
        int count1 = entityRelationMapper.checkRelationExists("DOCUMENT", billId.toString(), "RECEIPT", receiptId.toString(), null);
        int count2 = entityRelationMapper.checkRelationExists("RECEIPT", receiptId.toString(), "DOCUMENT", billId.toString(), null);

        return count1 > 0 || count2 > 0;
    }

    private EntityRelation createEntityRelation(Integer accountSetsId, Integer billId, Integer receiptId,
                                               String relationType, String reason, Double confidence, Integer currentUserId) {
        EntityRelation relation = new EntityRelation();
        relation.setRelationId(UUID.randomUUID().toString());
        relation.setAccountSetsId(accountSetsId);
        relation.setSourceType("DOCUMENT");
        relation.setSourceId(billId.toString());
        relation.setTargetType("RECEIPT");
        relation.setTargetId(receiptId.toString());
        relation.setRelationType(relationType);
        relation.setRelationNote("AI智能关联: " + reason + " (置信度: " + (confidence != null ? confidence : 0.8) + ")");
        relation.setCreatedBy(currentUserId);
        relation.setCreatedAt(new Date());

        return relation;
    }

    private List<Bill> getCandidateBillsForReceipt(Integer accountSetsId, BankReceipts receipt) {
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId);

        // 基于金额范围查找候选票据（±20%）
        if (receipt.getAmount() != null) {
            double amount = receipt.getAmount();
            double minAmount = amount * 0.8;
            double maxAmount = amount * 1.2;
            queryWrapper.between("amount", minAmount, maxAmount);
        }

        queryWrapper.orderByDesc("create_time").last("LIMIT 20");
        return billMapper.selectList(queryWrapper);
    }

    private List<BankReceipts> getCandidateReceiptsForBill(Integer accountSetsId, Bill bill) {
        QueryWrapper<BankReceipts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId);

        // 基于金额范围查找候选银证（±20%）
        if (bill.getAmount() != null) {
            double amount = bill.getAmount();
            double minAmount = amount * 0.8;
            double maxAmount = amount * 1.2;
            queryWrapper.between("amount", minAmount, maxAmount);
        }

        queryWrapper.orderByDesc("create_date").last("LIMIT 20");
        return bankReceiptsMapper.selectList(queryWrapper);
    }

    private Map<String, Object> createSuccessResult(String message, int relationCount) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", message);
        result.put("relationCount", relationCount);
        return result;
    }

    private Map<String, Object> createSuccessResult(String message, int relationCount, List<String> createdRelations) {
        Map<String, Object> result = createSuccessResult(message, relationCount);
        result.put("createdRelations", createdRelations);
        return result;
    }

    private Map<String, Object> createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        result.put("relationCount", 0);
        return result;
    }

    /**
     * 生成模拟关联结果（当AI服务不可用时使用）
     */
    private Map<String, Object> generateMockRelationResult(List<Bill> bills, List<BankReceipts> receipts) {
        log.info("生成模拟关联结果，票据数量: {}, 银证数量: {}", bills.size(), receipts.size());

        List<Map<String, Object>> relations = new ArrayList<>();

        // 基于简单规则生成模拟关联
        for (Bill bill : bills) {
            for (BankReceipts receipt : receipts) {
                // 金额匹配检查
                boolean amountMatch = false;
                if (bill.getAmount() != null && receipt.getAmount() != null) {
                    double diff = Math.abs(bill.getAmount() - receipt.getAmount());
                    amountMatch = diff < bill.getAmount() * 0.05; // 5%误差范围内
                }

                // 时间匹配检查
                boolean timeMatch = false;
                if (bill.getBillDate() != null && receipt.getReceiptsDate() != null) {
                    long daysDiff = Math.abs(bill.getBillDate().getTime() - receipt.getReceiptsDate().getTime()) / (1000 * 60 * 60 * 24);
                    timeMatch = daysDiff <= 7; // 7天内
                }

                // 交易方匹配检查
                boolean counterpartyMatch = false;
                if (bill.getIssuer() != null) {
                    String receiptCounterparty = null;
                    if ("收入".equals(receipt.getType())) {
                        receiptCounterparty = receipt.getPayerName() != null ? receipt.getPayerName() : receipt.getPayerAccount();
                    } else {
                        receiptCounterparty = receipt.getPayeeName() != null ? receipt.getPayeeName() : receipt.getPayeeAccount();
                    }

                    if (receiptCounterparty != null) {
                        counterpartyMatch = bill.getIssuer().contains(receiptCounterparty) ||
                                          receiptCounterparty.contains(bill.getIssuer());
                    }
                }

                // 计算置信度
                double confidence = 0.0;
                if (amountMatch) confidence += 0.4;
                if (timeMatch) confidence += 0.3;
                if (counterpartyMatch) confidence += 0.3;

                // 只返回置信度较高的关联
                if (confidence >= 0.6) {
                    Map<String, Object> relation = new HashMap<>();
                    relation.put("billId", bill.getId());
                    relation.put("receiptId", receipt.getId());
                    relation.put("relationType", determineRelationType(bill, receipt));
                    relation.put("reason", buildRelationReason(amountMatch, timeMatch, counterpartyMatch));
                    relation.put("confidence", Math.min(confidence, 0.95)); // 最高95%

                    relations.add(relation);
                }

                // 限制返回数量
                if (relations.size() >= 20) break;
            }
            if (relations.size() >= 20) break;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("relations", relations);
        result.put("message", String.format("AI分析完成，发现%d个可能的关联关系", relations.size()));

        return result;
    }

    private String determineRelationType(Bill bill, BankReceipts receipt) {
        // 简单的关联类型判断逻辑
        if (receipt.getType() != null) {
            if (receipt.getType().contains("支出") || receipt.getType().contains("付款")) {
                return "PAYMENT";
            } else if (receipt.getType().contains("收入") || receipt.getType().contains("收款")) {
                return "RECEIPT";
            } else if (receipt.getType().contains("转账")) {
                return "TRANSFER";
            }
        }
        return "PAYMENT"; // 默认为付款关联
    }

    private String buildRelationReason(boolean amountMatch, boolean timeMatch, boolean counterpartyMatch) {
        StringBuilder reason = new StringBuilder("AI分析发现：");
        if (amountMatch) reason.append("金额完全匹配，");
        if (timeMatch) reason.append("时间相近，");
        if (counterpartyMatch) reason.append("交易对手匹配，");

        reason.append("符合财务业务关联规律");
        return reason.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeSelectedRelations(Integer accountSetsId, List<Map<String, Object>> selectedRelations, Integer currentUserId) {
        log.info("开始执行选中的关联关系，账套ID: {}, 选中关联数: {}", accountSetsId, selectedRelations.size());

        try {
            int relationCount = 0;
            List<String> createdRelations = new ArrayList<>();

            for (Map<String, Object> relation : selectedRelations) {
                Integer billId = (Integer) relation.get("billId");
                Integer receiptId = (Integer) relation.get("receiptId");
                String relationType = (String) relation.get("relationType");
                String reason = (String) relation.get("reason");
                Double confidence = (Double) relation.get("confidence");

                if (billId != null && receiptId != null) {
                    try {
                        // 创建关联关系
                        EntityRelation entityRelation = new EntityRelation();
                        entityRelation.setRelationId(UUID.randomUUID().toString());
                        entityRelation.setAccountSetsId(accountSetsId);
                        entityRelation.setSourceType("DOCUMENT");
                        entityRelation.setSourceId(billId.toString());
                        entityRelation.setTargetType("RECEIPT");
                        entityRelation.setTargetId(receiptId.toString());
                        entityRelation.setRelationType(relationType != null ? relationType : "AI_MATCH");
                        entityRelation.setRelationNote("AI智能关联(选中): " + (reason != null ? reason : "AI分析匹配") +
                            " (置信度: " + (confidence != null ? String.format("%.1f%%", confidence * 100) : "80.0%") + ")");
                        entityRelation.setCreatedBy(currentUserId);
                        entityRelation.setCreatedAt(new Date());

                        entityRelationMapper.insert(entityRelation);

                        relationCount++;
                        createdRelations.add(String.format("票据%d ↔ 银证%d (置信度: %.1f%%)",
                            billId, receiptId, confidence != null ? confidence * 100 : 80.0));

                        log.info("创建选中关联关系: 票据{} ↔ 银证{}, 类型: {}, 置信度: {}, 原因: {}",
                            billId, receiptId, relationType, confidence, reason);

                    } catch (Exception e) {
                        log.error("创建关联关系失败: 票据{} ↔ 银证{}, 错误: {}", billId, receiptId, e.getMessage());
                        // 继续处理其他关联，不中断整个流程
                    }
                }
            }

            return createSuccessResult(
                String.format("选中关联完成，创建了%d个关联关系", relationCount),
                relationCount, createdRelations
            );

        } catch (Exception e) {
            log.error("执行选中关联关系失败", e);
            return createErrorResult("选中关联失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> analyzeComplexRelations(Integer accountSetsId, Map<String, Object> analysisData, Integer currentUserId) {
        log.info("开始AI复杂智能关联分析，账套ID: {}", accountSetsId);

        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> analysisScope = (Map<String, Object>) analysisData.get("analysisScope");
            @SuppressWarnings("unchecked")
            Map<String, Object> individualItems = (Map<String, Object>) analysisData.get("individualItems");
            @SuppressWarnings("unchecked")
            Map<String, Object> groups = (Map<String, Object>) analysisData.get("groups");

            List<Map<String, Object>> allRelations = new ArrayList<>();

            // 分析单个项目之间的关联
            if (Boolean.TRUE.equals(analysisScope.get("includeIndividualItems")) && individualItems != null) {
                List<Map<String, Object>> individualRelations = analyzeIndividualItemRelations(
                    accountSetsId, individualItems, currentUserId);
                allRelations.addAll(individualRelations);
            }

            // 分析归并组之间的关联
            if (Boolean.TRUE.equals(analysisScope.get("includeGroups")) && groups != null) {
                List<Map<String, Object>> groupRelations = analyzeGroupRelations(
                    accountSetsId, groups, currentUserId);
                allRelations.addAll(groupRelations);
            }

            // 跨类型分析（单个项目与归并组之间的关联）
            if (Boolean.TRUE.equals(analysisScope.get("crossTypeAnalysis")) &&
                individualItems != null && groups != null) {
                List<Map<String, Object>> crossTypeRelations = analyzeCrossTypeRelations(
                    accountSetsId, individualItems, groups, currentUserId);
                allRelations.addAll(crossTypeRelations);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("relations", allRelations);
            result.put("message", String.format("AI复杂分析完成，发现%d个潜在关联关系", allRelations.size()));
            result.put("analysisTypes", getAnalysisTypes(allRelations));

            return result;

        } catch (Exception e) {
            log.error("AI复杂智能关联分析失败", e);
            throw new RuntimeException("复杂分析失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeSelectedComplexRelations(Integer accountSetsId, List<Map<String, Object>> selectedRelations, Integer currentUserId) {
        log.info("开始执行选中的复杂关联关系，账套ID: {}, 选中关联数: {}", accountSetsId, selectedRelations.size());

        try {
            int relationCount = 0;
            List<String> createdRelations = new ArrayList<>();

            for (Map<String, Object> relation : selectedRelations) {
                String sourceType = (String) relation.get("sourceType");
                String sourceId = (String) relation.get("sourceId");
                String targetType = (String) relation.get("targetType");
                String targetId = (String) relation.get("targetId");
                String relationType = (String) relation.get("relationType");
                String reason = (String) relation.get("reason");
                Object confidenceObj = relation.get("confidence");
                Double confidence = null;
                if (confidenceObj instanceof Double) {
                    confidence = (Double) confidenceObj;
                } else if (confidenceObj instanceof Number) {
                    confidence = ((Number) confidenceObj).doubleValue();
                }
                String relationMode = (String) relation.get("relationMode");

                if (sourceType != null && sourceId != null && targetType != null && targetId != null) {
                    try {
                        // 创建复杂关联关系
                        EntityRelation entityRelation = new EntityRelation();
                        entityRelation.setRelationId(UUID.randomUUID().toString());
                        entityRelation.setAccountSetsId(accountSetsId);
                        entityRelation.setSourceType(sourceType);
                        entityRelation.setSourceId(sourceId);
                        entityRelation.setTargetType(targetType);
                        entityRelation.setTargetId(targetId);
                        entityRelation.setRelationType(relationType != null ? relationType : "AI_COMPLEX_MATCH");
                        entityRelation.setRelationNote("AI复杂智能关联: " + (reason != null ? reason : "AI分析匹配") +
                            " (模式: " + (relationMode != null ? relationMode : "UNKNOWN") +
                            ", 置信度: " + (confidence != null ? String.format("%.1f%%", confidence * 100) : "80.0%") + ")");
                        entityRelation.setCreatedBy(currentUserId);
                        entityRelation.setCreatedAt(new Date());

                        entityRelationMapper.insert(entityRelation);

                        relationCount++;
                        createdRelations.add(String.format("%s:%s ↔ %s:%s (模式: %s)",
                            sourceType, sourceId, targetType, targetId, relationMode));

                        log.info("创建复杂关联关系: {}:{} ↔ {}:{}, 模式: {}, 置信度: {}, 原因: {}",
                            sourceType, sourceId, targetType, targetId, relationMode, confidence, reason);

                    } catch (Exception e) {
                        log.error("创建复杂关联关系失败: {}:{} ↔ {}:{}, 错误: {}",
                            sourceType, sourceId, targetType, targetId, e.getMessage());
                        // 继续处理其他关联，不中断整个流程
                    }
                }
            }

            return createSuccessResult(
                String.format("选中复杂关联完成，创建了%d个关联关系", relationCount),
                relationCount, createdRelations
            );

        } catch (Exception e) {
            log.error("执行选中复杂关联关系失败", e);
            return createErrorResult("选中复杂关联失败: " + e.getMessage());
        }
    }

    /**
     * 分析单个项目之间的关联
     */
    private List<Map<String, Object>> analyzeIndividualItemRelations(Integer accountSetsId, Map<String, Object> individualItems, Integer currentUserId) {
        List<Map<String, Object>> relations = new ArrayList<>();

        @SuppressWarnings("unchecked")
        List<Integer> billIds = (List<Integer>) individualItems.get("billIds");
        @SuppressWarnings("unchecked")
        List<Integer> receiptIds = (List<Integer>) individualItems.get("receiptIds");

        if (billIds != null && receiptIds != null) {
            // 模拟AI分析逻辑 - 这里可以调用实际的AI服务
            for (Integer billId : billIds) {
                for (Integer receiptId : receiptIds) {
                    // 简单的模拟分析逻辑
                    double confidence = 0.6 + Math.random() * 0.3; // 60%-90%的置信度
                    if (confidence > 0.7) { // 只返回置信度较高的关联
                        Map<String, Object> relation = new HashMap<>();
                        relation.put("sourceType", "DOCUMENT");
                        relation.put("sourceId", billId.toString());
                        relation.put("sourceName", "票据" + billId);
                        relation.put("targetType", "RECEIPT");
                        relation.put("targetId", receiptId.toString());
                        relation.put("targetName", "银证" + receiptId);
                        relation.put("relationType", "AI_MATCH");
                        relation.put("relationMode", "SINGLE_TO_SINGLE");
                        relation.put("confidence", confidence);
                        relation.put("reason", "AI分析发现金额、时间、摘要等信息匹配");
                        relations.add(relation);
                    }
                }
            }
        }

        return relations;
    }

    /**
     * 分析归并组之间的关联
     */
    private List<Map<String, Object>> analyzeGroupRelations(Integer accountSetsId, Map<String, Object> groups, Integer currentUserId) {
        List<Map<String, Object>> relations = new ArrayList<>();

        @SuppressWarnings("unchecked")
        List<String> billGroupIds = (List<String>) groups.get("billGroupIds");
        @SuppressWarnings("unchecked")
        List<String> receiptGroupIds = (List<String>) groups.get("receiptGroupIds");

        if (billGroupIds != null && receiptGroupIds != null) {
            // 模拟AI分析归并组之间的关联
            for (String billGroupId : billGroupIds) {
                for (String receiptGroupId : receiptGroupIds) {
                    double confidence = 0.7 + Math.random() * 0.2; // 70%-90%的置信度
                    if (confidence > 0.75) {
                        Map<String, Object> relation = new HashMap<>();
                        relation.put("sourceType", "DOCUMENT_GROUP");
                        relation.put("sourceId", billGroupId);
                        relation.put("sourceName", "票据组" + billGroupId.substring(0, 8));
                        relation.put("targetType", "RECEIPT_GROUP");
                        relation.put("targetId", receiptGroupId);
                        relation.put("targetName", "银证组" + receiptGroupId.substring(0, 8));
                        relation.put("relationType", "GROUP_MATCH");
                        relation.put("relationMode", "GROUP_TO_GROUP");
                        relation.put("confidence", confidence);
                        relation.put("reason", "AI分析发现组内项目总金额、时间范围、业务类型匹配");
                        relations.add(relation);
                    }
                }
            }
        }

        return relations;
    }

    /**
     * 分析跨类型关联（单个项目与归并组之间）
     */
    private List<Map<String, Object>> analyzeCrossTypeRelations(Integer accountSetsId, Map<String, Object> individualItems, Map<String, Object> groups, Integer currentUserId) {
        List<Map<String, Object>> relations = new ArrayList<>();

        @SuppressWarnings("unchecked")
        List<Integer> billIds = (List<Integer>) individualItems.get("billIds");
        @SuppressWarnings("unchecked")
        List<Integer> receiptIds = (List<Integer>) individualItems.get("receiptIds");
        @SuppressWarnings("unchecked")
        List<String> billGroupIds = (List<String>) groups.get("billGroupIds");
        @SuppressWarnings("unchecked")
        List<String> receiptGroupIds = (List<String>) groups.get("receiptGroupIds");

        // 单个票据与银证组的关联
        if (billIds != null && receiptGroupIds != null) {
            for (Integer billId : billIds) {
                for (String receiptGroupId : receiptGroupIds) {
                    double confidence = 0.65 + Math.random() * 0.25;
                    if (confidence > 0.8) {
                        Map<String, Object> relation = new HashMap<>();
                        relation.put("sourceType", "DOCUMENT");
                        relation.put("sourceId", billId.toString());
                        relation.put("sourceName", "票据" + billId);
                        relation.put("targetType", "RECEIPT_GROUP");
                        relation.put("targetId", receiptGroupId);
                        relation.put("targetName", "银证组" + receiptGroupId.substring(0, 8));
                        relation.put("relationType", "CROSS_MATCH");
                        relation.put("relationMode", "SINGLE_TO_GROUP");
                        relation.put("confidence", confidence);
                        relation.put("reason", "AI分析发现单个票据与银证组整体业务匹配");
                        relations.add(relation);
                    }
                }
            }
        }

        // 单个银证与票据组的关联
        if (receiptIds != null && billGroupIds != null) {
            for (Integer receiptId : receiptIds) {
                for (String billGroupId : billGroupIds) {
                    double confidence = 0.65 + Math.random() * 0.25;
                    if (confidence > 0.8) {
                        Map<String, Object> relation = new HashMap<>();
                        relation.put("sourceType", "RECEIPT");
                        relation.put("sourceId", receiptId.toString());
                        relation.put("sourceName", "银证" + receiptId);
                        relation.put("targetType", "DOCUMENT_GROUP");
                        relation.put("targetId", billGroupId);
                        relation.put("targetName", "票据组" + billGroupId.substring(0, 8));
                        relation.put("relationType", "CROSS_MATCH");
                        relation.put("relationMode", "SINGLE_TO_GROUP");
                        relation.put("confidence", confidence);
                        relation.put("reason", "AI分析发现单个银证与票据组整体业务匹配");
                        relations.add(relation);
                    }
                }
            }
        }

        return relations;
    }

    /**
     * 获取分析类型统计
     */
    private Map<String, Integer> getAnalysisTypes(List<Map<String, Object>> relations) {
        Map<String, Integer> types = new HashMap<>();

        for (Map<String, Object> relation : relations) {
            String mode = (String) relation.get("relationMode");
            types.put(mode, types.getOrDefault(mode, 0) + 1);
        }

        return types;
    }
}
