package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.mapper.EntityRelationMapper;
import cn.gson.financial.kernel.model.entity.EntityRelation;
import cn.gson.financial.kernel.service.EntityRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 实体关联服务实现类
 */
@Service
@Slf4j
public class EntityRelationServiceImpl implements EntityRelationService {

    @Autowired
    private EntityRelationMapper entityRelationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> createComplexRelation(Integer accountSetsId, Map<String, Object> relationData, Integer currentUserId) {
        log.info("开始创建复杂关联关系，账套ID: {}", accountSetsId);

        try {
            String receiptSelectionMode = (String) relationData.get("receiptSelectionMode");
            String billSelectionMode = (String) relationData.get("billSelectionMode");
            String relationType = (String) relationData.get("relationType");
            String relationNote = (String) relationData.get("relationNote");

            List<String> createdRelations = new ArrayList<>();
            int relationCount = 0;

            // 获取银行回单相关数据
            List<String> receiptSources = getReceiptSources(accountSetsId, receiptSelectionMode, relationData);
            
            // 获取票据相关数据
            List<String> billSources = getBillSources(accountSetsId, billSelectionMode, relationData);

            // 创建关联关系
            for (String receiptSource : receiptSources) {
                for (String billSource : billSources) {
                    EntityRelation relation = new EntityRelation();
                    relation.setRelationId(UUID.randomUUID().toString());
                    relation.setAccountSetsId(accountSetsId);
                    relation.setSourceType(getSourceType(receiptSelectionMode));
                    relation.setSourceId(receiptSource);
                    relation.setTargetType(getTargetType(billSelectionMode));
                    relation.setTargetId(billSource);
                    relation.setRelationType(relationType != null ? relationType : "MANUAL");
                    relation.setRelationNote(relationNote != null ? relationNote : "手动创建关联");
                    relation.setCreatedBy(currentUserId);
                    relation.setCreatedAt(new Date());

                    entityRelationMapper.insert(relation);
                    relationCount++;
                    
                    createdRelations.add(String.format("%s:%s ↔ %s:%s", 
                        getSourceType(receiptSelectionMode), receiptSource,
                        getTargetType(billSelectionMode), billSource));
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", String.format("成功创建%d个关联关系", relationCount));
            result.put("relationCount", relationCount);
            result.put("createdRelations", createdRelations);
            
            return result;

        } catch (Exception e) {
            log.error("创建复杂关联关系失败", e);
            throw new RuntimeException("创建关联失败: " + e.getMessage());
        }
    }

    /**
     * 获取银行回单数据源
     */
    private List<String> getReceiptSources(Integer accountSetsId, String selectionMode, Map<String, Object> relationData) {
        List<String> sources = new ArrayList<>();
        
        switch (selectionMode) {
            case "single":
                Integer receiptId = (Integer) relationData.get("selectedReceiptId");
                if (receiptId != null) {
                    sources.add(receiptId.toString());
                }
                break;
                
            case "group":
                @SuppressWarnings("unchecked")
                List<Integer> receiptIds = (List<Integer>) relationData.get("selectedReceiptIds");
                if (receiptIds != null) {
                    receiptIds.forEach(id -> sources.add(id.toString()));
                }
                break;
                
            case "existing-group":
                String receiptGroupId = (String) relationData.get("selectedReceiptGroupId");
                if (receiptGroupId != null) {
                    sources.add(receiptGroupId);
                }
                break;
                
            case "multiple-groups":
                @SuppressWarnings("unchecked")
                List<String> receiptGroupIds = (List<String>) relationData.get("selectedReceiptGroupIds");
                if (receiptGroupIds != null) {
                    sources.addAll(receiptGroupIds);
                }
                break;
        }
        
        return sources;
    }

    /**
     * 获取票据数据源
     */
    private List<String> getBillSources(Integer accountSetsId, String selectionMode, Map<String, Object> relationData) {
        List<String> sources = new ArrayList<>();
        
        switch (selectionMode) {
            case "single":
                Integer billId = (Integer) relationData.get("selectedBillId");
                if (billId != null) {
                    sources.add(billId.toString());
                }
                break;
                
            case "group":
                @SuppressWarnings("unchecked")
                List<Integer> billIds = (List<Integer>) relationData.get("selectedBillIds");
                if (billIds != null) {
                    billIds.forEach(id -> sources.add(id.toString()));
                }
                break;
                
            case "existing-group":
                String billGroupId = (String) relationData.get("selectedBillGroupId");
                if (billGroupId != null) {
                    sources.add(billGroupId);
                }
                break;
                
            case "multiple-groups":
                @SuppressWarnings("unchecked")
                List<String> billGroupIds = (List<String>) relationData.get("selectedBillGroupIds");
                if (billGroupIds != null) {
                    sources.addAll(billGroupIds);
                }
                break;
        }
        
        return sources;
    }

    /**
     * 获取源类型
     */
    private String getSourceType(String selectionMode) {
        switch (selectionMode) {
            case "single":
            case "group":
                return "RECEIPT";
            case "existing-group":
            case "multiple-groups":
                return "RECEIPT_GROUP";
            default:
                return "RECEIPT";
        }
    }

    /**
     * 获取目标类型
     */
    private String getTargetType(String selectionMode) {
        switch (selectionMode) {
            case "single":
            case "group":
                return "DOCUMENT";
            case "existing-group":
            case "multiple-groups":
                return "DOCUMENT_GROUP";
            default:
                return "DOCUMENT";
        }
    }

    @Override
    public Map<String, Object> getRelations(Integer accountSetsId, int page, int size) {
        log.info("获取关联关系列表，账套ID: {}, 页码: {}, 页大小: {}", accountSetsId, page, size);

        try {
            // 计算偏移量
            int offset = (page - 1) * size;

            // 查询关联关系列表
            List<EntityRelation> relations = entityRelationMapper.findByAccountSetsIdWithPaging(accountSetsId, offset, size);

            // 查询总数
            int total = entityRelationMapper.countByAccountSetsId(accountSetsId);

            Map<String, Object> result = new HashMap<>();
            result.put("records", relations);
            result.put("total", total);
            result.put("current", page);
            result.put("size", size);
            result.put("pages", (total + size - 1) / size);

            log.info("获取关联关系列表成功，总数: {}, 当前页数据: {}", total, relations.size());
            return result;

        } catch (Exception e) {
            log.error("获取关联关系列表失败", e);
            throw new RuntimeException("获取关联关系列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRelation(String relationId, Integer accountSetsId) {
        entityRelationMapper.deleteByRelationId(relationId);
    }
}
