package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BankReceipts;

import java.util.List;
import java.util.Map;

/**
 * AI智能关联服务接口
 */
public interface AiRelationService {

    /**
     * AI智能分析票据和银证的关联关系
     * 
     * @param accountSetsId 账套ID
     * @param bills 票据列表
     * @param receipts 银证列表
     * @param currentUserId 当前用户ID
     * @return 关联分析结果
     */
    Map<String, Object> analyzeRelations(Integer accountSetsId, List<Bill> bills, List<BankReceipts> receipts, Integer currentUserId);

    /**
     * AI执行智能关联
     * 
     * @param accountSetsId 账套ID
     * @param bills 票据列表
     * @param receipts 银证列表
     * @param currentUserId 当前用户ID
     * @return 关联执行结果
     */
    Map<String, Object> executeAiRelations(Integer accountSetsId, List<Bill> bills, List<BankReceipts> receipts, Integer currentUserId);

    /**
     * AI分析单个银证的关联建议
     * 
     * @param accountSetsId 账套ID
     * @param receipt 银证
     * @return 关联建议
     */
    Map<String, Object> analyzeReceiptRelationSuggestions(Integer accountSetsId, BankReceipts receipt);

    /**
     * AI分析单个票据的关联建议
     * 
     * @param accountSetsId 账套ID
     * @param bill 票据
     * @return 关联建议
     */
    Map<String, Object> analyzeBillRelationSuggestions(Integer accountSetsId, Bill bill);

    /**
     * 获取未关联的票据列表
     * 
     * @param accountSetsId 账套ID
     * @param limit 限制数量
     * @return 未关联的票据列表
     */
    List<Bill> getUnrelatedBills(Integer accountSetsId, Integer limit);

    /**
     * 获取未关联的银证列表
     * 
     * @param accountSetsId 账套ID
     * @param limit 限制数量
     * @return 未关联的银证列表
     */
    List<BankReceipts> getUnrelatedReceipts(Integer accountSetsId, Integer limit);

    /**
     * AI自动关联所有未关联数据
     *
     * @param accountSetsId 账套ID
     * @param currentUserId 当前用户ID
     * @param limit 限制数量
     * @return 自动关联结果
     */
    Map<String, Object> autoRelateAll(Integer accountSetsId, Integer currentUserId, Integer limit);

    /**
     * 执行选中的关联关系
     *
     * @param accountSetsId 账套ID
     * @param selectedRelations 选中的关联关系
     * @param currentUserId 当前用户ID
     * @return 关联结果
     */
    Map<String, Object> executeSelectedRelations(Integer accountSetsId, List<Map<String, Object>> selectedRelations, Integer currentUserId);

    /**
     * AI复杂智能关联分析
     * 支持单个项目、归并组、跨类型等复杂分析
     *
     * @param accountSetsId 账套ID
     * @param analysisData 分析数据
     * @param currentUserId 当前用户ID
     * @return 分析结果
     */
    Map<String, Object> analyzeComplexRelations(Integer accountSetsId, Map<String, Object> analysisData, Integer currentUserId);

    /**
     * 执行选中的复杂关联关系
     *
     * @param accountSetsId 账套ID
     * @param selectedRelations 选中的复杂关联关系
     * @param currentUserId 当前用户ID
     * @return 关联结果
     */
    Map<String, Object> executeSelectedComplexRelations(Integer accountSetsId, List<Map<String, Object>> selectedRelations, Integer currentUserId);
}
