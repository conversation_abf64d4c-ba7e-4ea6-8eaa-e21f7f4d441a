package cn.gson.financial.kernel.service;

import java.util.Map;

/**
 * 实体关联服务接口
 * 支持复杂的关联模式
 */
public interface EntityRelationService {

    /**
     * 创建复杂关联关系
     * 支持以下关联模式：
     * 1. single -> single: 单个银行回单 ↔ 单个票据
     * 2. single -> group: 单个银行回单 ↔ 票据组合
     * 3. single -> existing-group: 单个银行回单 ↔ 已归并票据组
     * 4. group -> single: 银行回单组合 ↔ 单个票据
     * 5. group -> group: 银行回单组合 ↔ 票据组合
     * 6. group -> existing-group: 银行回单组合 ↔ 已归并票据组
     * 7. existing-group -> single: 已归并银行回单组 ↔ 单个票据
     * 8. existing-group -> group: 已归并银行回单组 ↔ 票据组合
     * 9. existing-group -> existing-group: 已归并银行回单组 ↔ 已归并票据组
     * 10. existing-group -> multiple-groups: 已归并银行回单组 ↔ 多个已归并票据组
     * 11. multiple-groups -> single: 多个已归并银行回单组 ↔ 单个票据
     * 12. multiple-groups -> group: 多个已归并银行回单组 ↔ 票据组合
     * 13. multiple-groups -> existing-group: 多个已归并银行回单组 ↔ 已归并票据组
     * 14. multiple-groups -> multiple-groups: 多个已归并银行回单组 ↔ 多个已归并票据组
     * 
     * @param accountSetsId 账套ID
     * @param relationData 关联数据
     * @param currentUserId 当前用户ID
     * @return 创建结果
     */
    Map<String, Object> createComplexRelation(Integer accountSetsId, Map<String, Object> relationData, Integer currentUserId);

    /**
     * 获取关联关系列表
     * 
     * @param accountSetsId 账套ID
     * @param page 页码
     * @param size 页大小
     * @return 关联关系列表
     */
    Map<String, Object> getRelations(Integer accountSetsId, int page, int size);

    /**
     * 删除关联关系
     * 
     * @param relationId 关联ID
     * @param accountSetsId 账套ID
     */
    void deleteRelation(String relationId, Integer accountSetsId);
}
