package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.config.AiConfig;
import cn.gson.financial.kernel.service.AiService;
import cn.gson.financial.kernel.service.FinancialAiConfigService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * OpenAI兼容的AI服务实现
 */
@Service
@ConditionalOnProperty(name = "ai.enabled", havingValue = "true")
public class OpenAiServiceImpl implements AiService {

    private static final Logger logger = LoggerFactory.getLogger(OpenAiServiceImpl.class);

    @Autowired
    private AiConfig aiConfig;

    @Autowired(required = false)
    private FinancialAiConfigService financialAiConfigService;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    public OpenAiServiceImpl() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 获取用户AI配置
     */
    private Map<String, String> getUserAiConfig(Integer userId) {
        if (financialAiConfigService != null && userId != null) {
            return financialAiConfigService.getAiConfigByUser(userId);
        }
        return new HashMap<>();
    }

    /**
     * 检查用户AI配置是否有效
     */
    private boolean isUserConfigValid(Integer userId) {
        Map<String, String> userConfig = getUserAiConfig(userId);
        if (userConfig.isEmpty()) {
            return false;
        }

        String enabled = userConfig.get("enabled");
        String apiKey = userConfig.get("api_key");
        String baseUrl = userConfig.get("base_url");

        return "true".equalsIgnoreCase(enabled) &&
               apiKey != null && !apiKey.trim().isEmpty() &&
               baseUrl != null && !baseUrl.trim().isEmpty();
    }

    @Override
    public boolean isAvailable() {
        if (!aiConfig.isEnabled() || aiConfig.getApiKey() == null || aiConfig.getApiKey().trim().isEmpty()) {
            return false;
        }

        try {
            // 发送简单的测试请求
            String response = chat("Hello");
            return response != null && !response.trim().isEmpty();
        } catch (Exception e) {
            logger.warn("AI服务不可用: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean isAvailable(Integer userId) {
        if (!isUserConfigValid(userId)) {
            return false;
        }

        try {
            // 发送简单的测试请求
            String response = chat("Hello", userId);
            return response != null && !response.trim().isEmpty();
        } catch (Exception e) {
            logger.warn("用户{}的AI服务不可用: {}", userId, e.getMessage());
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getAvailableModels() {
        if (!aiConfig.isEnabled() || aiConfig.getApiKey() == null) {
            throw new RuntimeException("AI服务未启用或API密钥未配置");
        }

        try {
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            String url = aiConfig.getBaseUrl() + "/models";
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseBody = (Map<String, Object>) response.getBody();

                if (responseBody != null && responseBody.containsKey("data")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> models = (List<Map<String, Object>>) responseBody.get("data");

                    // 过滤和格式化模型列表
                    List<Map<String, Object>> formattedModels = new ArrayList<>();
                    for (Map<String, Object> model : models) {
                        Map<String, Object> formattedModel = new HashMap<>();
                        formattedModel.put("id", model.get("id"));
                        formattedModel.put("name", model.get("id")); // 使用id作为显示名称
                        formattedModel.put("owned_by", model.get("owned_by"));
                        formattedModel.put("created", model.get("created"));
                        formattedModels.add(formattedModel);
                    }

                    return formattedModels;
                } else {
                    throw new RuntimeException("API响应格式不正确");
                }
            } else {
                throw new RuntimeException("获取模型列表失败: HTTP " + response.getStatusCode());
            }

        } catch (Exception e) {
            logger.error("获取模型列表失败", e);

            // 如果API调用失败，返回默认模型列表
            return getDefaultModels();
        }
    }

    @Override
    public List<Map<String, Object>> getAvailableModels(Integer userId) {
        if (!isUserConfigValid(userId)) {
            throw new RuntimeException("用户AI服务未启用或配置无效");
        }

        Map<String, String> userConfig = getUserAiConfig(userId);
        String apiKey = userConfig.get("api_key");
        String baseUrl = userConfig.get("base_url");

        try {
            HttpHeaders headers = createHeaders(apiKey);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            String url = baseUrl + "/models";
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseBody = (Map<String, Object>) response.getBody();

                if (responseBody != null && responseBody.containsKey("data")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> models = (List<Map<String, Object>>) responseBody.get("data");

                    // 过滤和格式化模型列表
                    List<Map<String, Object>> formattedModels = new ArrayList<>();
                    for (Map<String, Object> model : models) {
                        Map<String, Object> formattedModel = new HashMap<>();
                        formattedModel.put("id", model.get("id"));
                        formattedModel.put("name", model.get("id")); // 使用id作为显示名称
                        formattedModel.put("owned_by", model.get("owned_by"));
                        formattedModel.put("created", model.get("created"));
                        formattedModels.add(formattedModel);
                    }

                    return formattedModels;
                } else {
                    throw new RuntimeException("API响应格式不正确");
                }
            } else {
                throw new RuntimeException("获取模型列表失败: HTTP " + response.getStatusCode());
            }

        } catch (Exception e) {
            logger.error("获取用户{}的模型列表失败", userId, e);

            // 如果API调用失败，返回默认模型列表
            return getDefaultModels(baseUrl);
        }
    }

    /**
     * 获取默认模型列表（当API调用失败时使用）
     */
    private List<Map<String, Object>> getDefaultModels() {
        return getDefaultModels(aiConfig.getBaseUrl());
    }

    /**
     * 获取默认模型列表（指定baseUrl）
     */
    private List<Map<String, Object>> getDefaultModels(String baseUrl) {
        List<Map<String, Object>> defaultModels = new ArrayList<>();

        String lowerBaseUrl = baseUrl != null ? baseUrl.toLowerCase() : "";

        if (lowerBaseUrl.contains("deepseek")) {
            defaultModels.add(createModelInfo("deepseek-chat", "DeepSeek Chat", "deepseek"));
            defaultModels.add(createModelInfo("deepseek-coder", "DeepSeek Coder", "deepseek"));
        } else if (lowerBaseUrl.contains("dashscope")) {
            defaultModels.add(createModelInfo("qwen-turbo", "Qwen Turbo", "alibaba"));
            defaultModels.add(createModelInfo("qwen-plus", "Qwen Plus", "alibaba"));
            defaultModels.add(createModelInfo("qwen-max", "Qwen Max", "alibaba"));
        } else {
            // OpenAI 或其他兼容服务
            defaultModels.add(createModelInfo("gpt-3.5-turbo", "GPT-3.5 Turbo", "openai"));
            defaultModels.add(createModelInfo("gpt-4", "GPT-4", "openai"));
            defaultModels.add(createModelInfo("gpt-4-turbo", "GPT-4 Turbo", "openai"));
        }

        return defaultModels;
    }

    /**
     * 创建模型信息对象
     */
    private Map<String, Object> createModelInfo(String id, String name, String ownedBy) {
        Map<String, Object> model = new HashMap<>();
        model.put("id", id);
        model.put("name", name);
        model.put("owned_by", ownedBy);
        model.put("created", System.currentTimeMillis() / 1000);
        return model;
    }
    
    @Override
    public String chat(String prompt) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("temperature", aiConfig.getTemperature());
        parameters.put("max_tokens", aiConfig.getMaxTokens());
        return chat(prompt, parameters);
    }

    @Override
    public String chat(String prompt, Map<String, Object> parameters) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", aiConfig.getDefaultModel());

            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", prompt);
            requestBody.put("messages", Arrays.asList(userMessage));

            // 添加参数
            if (parameters != null) {
                requestBody.putAll(parameters);
            }

            // 发送请求
            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                aiConfig.getBaseUrl() + "/chat/completions",
                HttpMethod.POST,
                entity,
                String.class
            );

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                JsonNode choices = jsonNode.get("choices");
                if (choices != null && choices.size() > 0) {
                    JsonNode message = choices.get(0).get("message");
                    if (message != null) {
                        return message.get("content").asText();
                    }
                }
            }

            logger.error("AI请求失败: {}", response.getBody());
            return null;

        } catch (Exception e) {
            logger.error("AI请求异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String chat(String prompt, Integer userId) {
        Map<String, String> userConfig = getUserAiConfig(userId);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("temperature", Double.parseDouble(userConfig.getOrDefault("temperature", "0.7")));
        parameters.put("max_tokens", Integer.parseInt(userConfig.getOrDefault("max_tokens", "2000")));
        return chat(prompt, parameters, userId);
    }

    @Override
    public String chat(String prompt, Map<String, Object> parameters, Integer userId) {
        if (!isUserConfigValid(userId)) {
            logger.error("用户{}的AI配置无效", userId);
            return null;
        }

        Map<String, String> userConfig = getUserAiConfig(userId);
        String apiKey = userConfig.get("api_key");
        String baseUrl = userConfig.get("base_url");
        String defaultModel = userConfig.getOrDefault("default_model", "gpt-3.5-turbo");

        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", defaultModel);

            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", prompt);
            requestBody.put("messages", Arrays.asList(userMessage));

            // 添加参数
            if (parameters != null) {
                requestBody.putAll(parameters);
            }

            // 发送请求
            HttpHeaders headers = createHeaders(apiKey);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                baseUrl + "/chat/completions",
                HttpMethod.POST,
                entity,
                String.class
            );

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                JsonNode choices = jsonNode.get("choices");
                if (choices != null && choices.size() > 0) {
                    JsonNode message = choices.get(0).get("message");
                    if (message != null) {
                        return message.get("content").asText();
                    }
                }
            }

            logger.error("用户{}的AI请求失败: {}", userId, response.getBody());
            return null;

        } catch (Exception e) {
            logger.error("用户{}的AI请求异常: {}", userId, e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public List<Map<String, Object>> analyzeBillMerge(List<Map<String, Object>> bills, Map<String, Object> config) {
        if (bills == null || bills.isEmpty()) {
            return new ArrayList<>();
        }
        
        String prompt = buildBillMergePrompt(bills, config);
        String response = chat(prompt);
        
        if (response != null) {
            return parseAnalysisResponse(response, "bill-merge");
        }
        
        return new ArrayList<>();
    }
    
    @Override
    public List<Map<String, Object>> analyzeReceiptMerge(List<Map<String, Object>> receipts, Map<String, Object> config) {
        if (receipts == null || receipts.isEmpty()) {
            return new ArrayList<>();
        }
        
        String prompt = buildReceiptMergePrompt(receipts, config);
        String response = chat(prompt);
        
        if (response != null) {
            return parseAnalysisResponse(response, "receipt-merge");
        }
        
        return new ArrayList<>();
    }
    
    @Override
    public List<Map<String, Object>> analyzeMixedMerge(List<Map<String, Object>> bills, List<Map<String, Object>> receipts, Map<String, Object> config) {
        if ((bills == null || bills.isEmpty()) && (receipts == null || receipts.isEmpty())) {
            return new ArrayList<>();
        }
        
        String prompt = buildMixedMergePrompt(bills, receipts, config);
        String response = chat(prompt);
        
        if (response != null) {
            return parseAnalysisResponse(response, "mixed-merge");
        }
        
        return new ArrayList<>();
    }
    
    @Override
    public List<Map<String, Object>> analyzeRelations(List<Map<String, Object>> bills, List<Map<String, Object>> receipts, String mode, Map<String, Object> config) {
        if ((bills == null || bills.isEmpty()) || (receipts == null || receipts.isEmpty())) {
            return new ArrayList<>();
        }
        
        String prompt = buildRelationAnalysisPrompt(bills, receipts, mode, config);
        String response = chat(prompt);
        
        if (response != null) {
            return parseRelationResponse(response);
        }
        
        return new ArrayList<>();
    }
    
    @Override
    public List<Double> generateEmbedding(String text) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "text-embedding-ada-002");
            requestBody.put("input", text);
            
            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                aiConfig.getBaseUrl() + "/embeddings",
                HttpMethod.POST,
                entity,
                String.class
            );
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                JsonNode data = jsonNode.get("data");
                if (data != null && data.size() > 0) {
                    JsonNode embedding = data.get(0).get("embedding");
                    List<Double> result = new ArrayList<>();
                    for (JsonNode value : embedding) {
                        result.add(value.asDouble());
                    }
                    return result;
                }
            }
            
        } catch (Exception e) {
            logger.error("生成嵌入向量失败: {}", e.getMessage(), e);
        }
        
        return new ArrayList<>();
    }
    
    @Override
    public double calculateSimilarity(String text1, String text2) {
        List<Double> embedding1 = generateEmbedding(text1);
        List<Double> embedding2 = generateEmbedding(text2);
        
        if (embedding1.isEmpty() || embedding2.isEmpty() || embedding1.size() != embedding2.size()) {
            return 0.0;
        }
        
        // 计算余弦相似度
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;
        
        for (int i = 0; i < embedding1.size(); i++) {
            dotProduct += embedding1.get(i) * embedding2.get(i);
            norm1 += Math.pow(embedding1.get(i), 2);
            norm2 += Math.pow(embedding2.get(i), 2);
        }
        
        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }
    
    /**
     * 创建HTTP请求头
     */
    private HttpHeaders createHeaders() {
        return createHeaders(aiConfig.getApiKey());
    }

    /**
     * 创建HTTP请求头（指定API密钥）
     */
    private HttpHeaders createHeaders(String apiKey) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);

        return headers;
    }
    
    /**
     * 构建票据归并分析提示词
     */
    private String buildBillMergePrompt(List<Map<String, Object>> bills, Map<String, Object> config) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("作为一个专业的财务AI助手，请分析以下票据数据，识别可以归并的票据组合。\n\n");
        prompt.append("分析要求：\n");
        prompt.append("1. 基于票据的内容、金额、日期、交易方等信息进行相似性分析\n");
        prompt.append("2. 识别属于同一业务流程的票据\n");
        prompt.append("3. 为每个归并建议提供置信度评分(0-100)\n");
        prompt.append("4. 提供详细的分析理由\n\n");
        
        prompt.append("票据数据：\n");
        for (int i = 0; i < bills.size(); i++) {
            Map<String, Object> bill = bills.get(i);
            prompt.append(String.format("票据%d: 编号=%s, 金额=%s, 摘要=%s, 日期=%s\n", 
                i + 1, 
                bill.get("billNo"), 
                bill.get("amount"), 
                bill.get("summary"),
                bill.get("date")
            ));
        }
        
        prompt.append("\n请以JSON格式返回分析结果，包含归并建议列表。");
        return prompt.toString();
    }
    
    /**
     * 构建银行回单归并分析提示词
     */
    private String buildReceiptMergePrompt(List<Map<String, Object>> receipts, Map<String, Object> config) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("作为一个专业的财务AI助手，请分析以下银行回单数据，识别可以归并的回单组合。\n\n");
        
        prompt.append("银行回单数据：\n");
        for (int i = 0; i < receipts.size(); i++) {
            Map<String, Object> receipt = receipts.get(i);
            prompt.append(String.format("回单%d: 编号=%s, 金额=%s, 摘要=%s, 交易方=%s\n", 
                i + 1, 
                receipt.get("receiptsNo"), 
                receipt.get("amount"), 
                receipt.get("summary"),
                receipt.get("counterparty")
            ));
        }
        
        prompt.append("\n请以JSON格式返回分析结果。");
        return prompt.toString();
    }
    
    /**
     * 构建混合归并分析提示词
     */
    private String buildMixedMergePrompt(List<Map<String, Object>> bills, List<Map<String, Object>> receipts, Map<String, Object> config) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("作为一个专业的财务AI助手，请分析票据和银行回单数据，识别跨类型的业务关联。\n\n");
        
        if (bills != null && !bills.isEmpty()) {
            prompt.append("票据数据：\n");
            for (int i = 0; i < bills.size(); i++) {
                Map<String, Object> bill = bills.get(i);
                prompt.append(String.format("票据%d: %s\n", i + 1, bill.toString()));
            }
        }
        
        if (receipts != null && !receipts.isEmpty()) {
            prompt.append("\n银行回单数据：\n");
            for (int i = 0; i < receipts.size(); i++) {
                Map<String, Object> receipt = receipts.get(i);
                prompt.append(String.format("回单%d: %s\n", i + 1, receipt.toString()));
            }
        }
        
        prompt.append("\n请以JSON格式返回分析结果。");
        return prompt.toString();
    }
    
    /**
     * 构建关联分析提示词
     */
    private String buildRelationAnalysisPrompt(List<Map<String, Object>> bills, List<Map<String, Object>> receipts, String mode, Map<String, Object> config) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("作为一个专业的财务AI助手，请分析票据和银行回单之间的关联关系。\n\n");
        prompt.append("分析模式: ").append(mode).append("\n\n");
        
        prompt.append("票据数据：\n");
        for (Map<String, Object> bill : bills) {
            prompt.append(bill.toString()).append("\n");
        }
        
        prompt.append("\n银行回单数据：\n");
        for (Map<String, Object> receipt : receipts) {
            prompt.append(receipt.toString()).append("\n");
        }
        
        prompt.append("\n请识别票据与银行回单之间的关联关系，以JSON格式返回结果。");
        return prompt.toString();
    }
    
    /**
     * 解析分析响应
     */
    private List<Map<String, Object>> parseAnalysisResponse(String response, String type) {
        // 这里应该解析AI返回的JSON响应
        // 为了演示，返回模拟数据
        List<Map<String, Object>> results = new ArrayList<>();
        
        Map<String, Object> result = new HashMap<>();
        result.put("confidence", 85);
        result.put("type", type);
        result.put("reasoning", "AI分析: " + response.substring(0, Math.min(100, response.length())));
        
        results.add(result);
        return results;
    }
    
    /**
     * 解析关联响应
     */
    private List<Map<String, Object>> parseRelationResponse(String response) {
        // 这里应该解析AI返回的关联分析结果
        // 为了演示，返回模拟数据
        List<Map<String, Object>> results = new ArrayList<>();
        
        Map<String, Object> result = new HashMap<>();
        result.put("confidence", 78);
        result.put("relationType", "PAYMENT");
        result.put("reasoning", "AI关联分析: " + response.substring(0, Math.min(100, response.length())));
        
        results.add(result);
        return results;
    }
}
