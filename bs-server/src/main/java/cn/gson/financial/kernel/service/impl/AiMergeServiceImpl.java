package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.entity.DocumentGroup;
import cn.gson.financial.kernel.model.entity.ReceiptGroup;
import cn.gson.financial.kernel.model.mapper.BillMapper;
import cn.gson.financial.kernel.model.mapper.BankReceiptsMapper;
import cn.gson.financial.kernel.model.mapper.DocumentGroupMapper;
import cn.gson.financial.kernel.model.mapper.ReceiptGroupMapper;
import cn.gson.financial.kernel.service.AiMergeService;
import cn.gson.financial.kernel.service.AiService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AI智能归并服务实现
 */
@Slf4j
@Service
public class AiMergeServiceImpl implements AiMergeService {

    @Autowired
    private AiService aiService;

    @Autowired
    private BillMapper billMapper;

    @Autowired
    private BankReceiptsMapper bankReceiptsMapper;

    @Autowired
    private DocumentGroupMapper documentGroupMapper;

    @Autowired
    private ReceiptGroupMapper receiptGroupMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> aiMergeDocuments(Integer accountSetsId, List<Bill> bills, Integer currentUserId) {
        log.info("开始AI智能票据归并，账套ID: {}, 票据数量: {}", accountSetsId, bills.size());

        try {
            // 1. 使用AI分析归并建议
            Map<String, Object> suggestions = analyzeMergeDocumentSuggestions(accountSetsId, bills, currentUserId);

            if (!suggestions.containsKey("groups") || !(suggestions.get("groups") instanceof List)) {
                return createErrorResult("AI分析失败，无法获取归并建议");
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> groups = (List<Map<String, Object>>) suggestions.get("groups");
            
            if (groups.isEmpty()) {
                return createSuccessResult("AI分析完成，未发现可归并的票据组合", 0, 0);
            }

            // 2. 执行归并操作
            int groupCount = 0;
            int totalMergedItems = 0;
            List<String> createdGroups = new ArrayList<>();

            for (Map<String, Object> group : groups) {
                @SuppressWarnings("unchecked")
                List<Integer> billIds = (List<Integer>) group.get("billIds");
                String groupName = (String) group.get("groupName");
                String reason = (String) group.get("reason");

                if (billIds != null && billIds.size() > 1) {
                    // 获取要归并的票据
                    List<Bill> groupBills = bills.stream()
                        .filter(bill -> billIds.contains(bill.getId()))
                        .collect(Collectors.toList());

                    if (groupBills.size() > 1) {
                        // 创建归并组
                        DocumentGroup docGroup = createDocumentGroup(accountSetsId, groupName, groupBills, currentUserId);
                        docGroup.setGroupSummary("AI智能归并: " + reason);
                        documentGroupMapper.updateById(docGroup);

                        // 更新票据的归并组ID
                        updateBillsGroupId(groupBills, docGroup.getGroupId());

                        groupCount++;
                        totalMergedItems += groupBills.size();
                        createdGroups.add(String.format("%s (%d个票据)", groupName, groupBills.size()));

                        log.info("AI创建票据归并组: {}, 包含票据数量: {}, 原因: {}", 
                            groupName, groupBills.size(), reason);
                    }
                }
            }

            return createSuccessResult(
                String.format("AI智能归并完成，创建了%d个归并组，共归并%d个票据", groupCount, totalMergedItems),
                groupCount, totalMergedItems, createdGroups
            );

        } catch (Exception e) {
            log.error("AI智能票据归并失败", e);
            return createErrorResult("AI智能归并失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> aiMergeReceipts(Integer accountSetsId, List<BankReceipts> receipts, Integer currentUserId) {
        log.info("开始AI智能银证归并，账套ID: {}, 银证数量: {}", accountSetsId, receipts.size());

        try {
            // 1. 使用AI分析归并建议
            Map<String, Object> suggestions = analyzeMergeReceiptSuggestions(accountSetsId, receipts, currentUserId);

            if (!suggestions.containsKey("groups") || !(suggestions.get("groups") instanceof List)) {
                return createErrorResult("AI分析失败，无法获取归并建议");
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> groups = (List<Map<String, Object>>) suggestions.get("groups");
            
            if (groups.isEmpty()) {
                return createSuccessResult("AI分析完成，未发现可归并的银证组合", 0, 0);
            }

            // 2. 执行归并操作
            int groupCount = 0;
            int totalMergedItems = 0;
            List<String> createdGroups = new ArrayList<>();

            for (Map<String, Object> group : groups) {
                @SuppressWarnings("unchecked")
                List<Integer> receiptIds = (List<Integer>) group.get("receiptIds");
                String groupName = (String) group.get("groupName");
                String reason = (String) group.get("reason");

                if (receiptIds != null && receiptIds.size() > 1) {
                    // 获取要归并的银证
                    List<BankReceipts> groupReceipts = receipts.stream()
                        .filter(receipt -> receiptIds.contains(receipt.getId()))
                        .collect(Collectors.toList());

                    if (groupReceipts.size() > 1) {
                        // 创建归并组
                        ReceiptGroup receiptGroup = createReceiptGroup(accountSetsId, groupName, groupReceipts, currentUserId);
                        receiptGroup.setGroupSummary("AI智能归并: " + reason);
                        receiptGroupMapper.updateById(receiptGroup);

                        // 更新银证的归并组ID
                        updateReceiptsGroupId(groupReceipts, receiptGroup.getGroupId());

                        groupCount++;
                        totalMergedItems += groupReceipts.size();
                        createdGroups.add(String.format("%s (%d个银证)", groupName, groupReceipts.size()));

                        log.info("AI创建银证归并组: {}, 包含银证数量: {}, 原因: {}", 
                            groupName, groupReceipts.size(), reason);
                    }
                }
            }

            return createSuccessResult(
                String.format("AI智能归并完成，创建了%d个归并组，共归并%d个银证", groupCount, totalMergedItems),
                groupCount, totalMergedItems, createdGroups
            );

        } catch (Exception e) {
            log.error("AI智能银证归并失败", e);
            return createErrorResult("AI智能归并失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> analyzeMergeDocumentSuggestions(Integer accountSetsId, List<Bill> bills) {
        return analyzeMergeDocumentSuggestions(accountSetsId, bills, null);
    }

    @Override
    public Map<String, Object> analyzeMergeDocumentSuggestions(Integer accountSetsId, List<Bill> bills, Integer userId) {
        log.info("开始AI分析票据归并建议，账套ID: {}, 票据数量: {}, 用户ID: {}", accountSetsId, bills.size(), userId);

        try {
            if (bills.isEmpty()) {
                return createSuccessResult("没有票据需要分析", 0, 0);
            }

            // 构建AI分析的提示词
            String prompt = buildDocumentMergePrompt(bills);

            // 调用AI服务
            String aiResponse;
            if (userId != null && aiService.isAvailable(userId)) {
                log.info("使用用户{}的AI配置进行票据归并分析", userId);
                aiResponse = aiService.chat(prompt, userId);
            } else {
                log.info("使用全局AI配置进行票据归并分析");
                aiResponse = aiService.chat(prompt);
            }

            // 解析AI响应
            return parseAiMergeResponse(aiResponse, "documents");

        } catch (Exception e) {
            log.error("AI分析票据归并建议失败", e);
            return createErrorResult("AI分析失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> analyzeMergeReceiptSuggestions(Integer accountSetsId, List<BankReceipts> receipts) {
        return analyzeMergeReceiptSuggestions(accountSetsId, receipts, null);
    }

    @Override
    public Map<String, Object> analyzeMergeReceiptSuggestions(Integer accountSetsId, List<BankReceipts> receipts, Integer userId) {
        log.info("开始AI分析银证归并建议，账套ID: {}, 银证数量: {}, 用户ID: {}", accountSetsId, receipts.size(), userId);

        try {
            if (receipts.isEmpty()) {
                return createSuccessResult("没有银证需要分析", 0, 0);
            }

            // 构建AI分析的提示词
            String prompt = buildReceiptMergePrompt(receipts);

            // 调用AI服务
            String aiResponse;
            if (userId != null && aiService.isAvailable(userId)) {
                log.info("使用用户{}的AI配置进行银证归并分析", userId);
                aiResponse = aiService.chat(prompt, userId);
            } else {
                log.info("使用全局AI配置进行银证归并分析");
                aiResponse = aiService.chat(prompt);
            }

            // 解析AI响应
            return parseAiMergeResponse(aiResponse, "receipts");

        } catch (Exception e) {
            log.error("AI分析银证归并建议失败", e);
            return createErrorResult("AI分析失败: " + e.getMessage());
        }
    }

    @Override
    public List<Bill> getUnmergedDocumentsForAi(Integer accountSetsId, Integer limit) {
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .isNull("doc_group_id")
                   .orderByDesc("create_time");
        
        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        }
        
        return billMapper.selectList(queryWrapper);
    }

    @Override
    public List<BankReceipts> getUnmergedReceiptsForAi(Integer accountSetsId, Integer limit) {
        QueryWrapper<BankReceipts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .isNull("receipt_group_id")
                   .orderByDesc("create_date");
        
        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        }
        
        return bankReceiptsMapper.selectList(queryWrapper);
    }

    // 私有辅助方法
    private String buildDocumentMergePrompt(List<Bill> bills) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个专业的财务数据分析师，请分析以下票据数据，识别可以归并的票据组合。\n\n");
        prompt.append("归并规则：\n");
        prompt.append("1. 相同开票方、相近日期的票据可以归并\n");
        prompt.append("2. 相同用途、相近金额的票据可以归并\n");
        prompt.append("3. 相同类型、相近时间的票据可以归并\n");
        prompt.append("4. 每个归并组至少包含2个票据\n\n");
        
        prompt.append("票据数据：\n");
        for (int i = 0; i < bills.size(); i++) {
            Bill bill = bills.get(i);
            prompt.append(String.format("票据%d: ID=%d, 开票方=%s, 金额=%s, 日期=%s, 类型=%s, 摘要=%s\n",
                i + 1, bill.getId(),
                bill.getIssuer() != null ? bill.getIssuer() : "未知",
                bill.getAmount() != null ? bill.getAmount().toString() : "0",
                bill.getBillDate() != null ? bill.getBillDate().toString() : "未知",
                bill.getType() != null ? bill.getType() : "未知",
                bill.getSummary() != null ? bill.getSummary() : "未知"));
        }
        
        prompt.append("\n请以JSON格式返回归并建议，格式如下：\n");
        prompt.append("{\n");
        prompt.append("  \"groups\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"groupName\": \"归并组名称\",\n");
        prompt.append("      \"billIds\": [票据ID列表],\n");
        prompt.append("      \"reason\": \"归并原因\"\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n");
        
        return prompt.toString();
    }

    private String buildReceiptMergePrompt(List<BankReceipts> receipts) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个专业的财务数据分析师，请分析以下银行回单数据，识别可以归并的银证组合。\n\n");
        prompt.append("归并规则：\n");
        prompt.append("1. 相同交易对手、相近日期的银证可以归并\n");
        prompt.append("2. 相同用途、相近金额的银证可以归并\n");
        prompt.append("3. 相同类型、相近时间的银证可以归并\n");
        prompt.append("4. 每个归并组至少包含2个银证\n\n");

        prompt.append("银证数据：\n");
        for (int i = 0; i < receipts.size(); i++) {
            BankReceipts receipt = receipts.get(i);
            // 根据类型获取交易对手信息
            String counterparty = "未知";
            if ("收入".equals(receipt.getType())) {
                counterparty = receipt.getPayerName() != null ? receipt.getPayerName() :
                              (receipt.getPayerAccount() != null ? receipt.getPayerAccount() : "未知付款人");
            } else {
                counterparty = receipt.getPayeeName() != null ? receipt.getPayeeName() :
                              (receipt.getPayeeAccount() != null ? receipt.getPayeeAccount() : "未知收款人");
            }

            prompt.append(String.format("银证%d: ID=%d, 交易对手=%s, 金额=%s, 日期=%s, 类型=%s, 摘要=%s\n",
                i + 1, receipt.getId(),
                counterparty,
                receipt.getAmount() != null ? receipt.getAmount().toString() : "0",
                receipt.getReceiptsDate() != null ? receipt.getReceiptsDate().toString() : "未知",
                receipt.getType() != null ? receipt.getType() : "未知",
                receipt.getSummary() != null ? receipt.getSummary() : "未知"));
        }

        prompt.append("\n请以JSON格式返回归并建议，格式如下：\n");
        prompt.append("{\n");
        prompt.append("  \"groups\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"groupName\": \"归并组名称\",\n");
        prompt.append("      \"receiptIds\": [银证ID列表],\n");
        prompt.append("      \"reason\": \"归并原因\"\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n");

        return prompt.toString();
    }

    private Map<String, Object> parseAiMergeResponse(String aiResponse, String type) {
        try {
            // 尝试从AI响应中提取JSON
            String jsonStr = extractJsonFromResponse(aiResponse);
            JSONObject jsonResponse = JSON.parseObject(jsonStr);

            if (jsonResponse.containsKey("groups")) {
                JSONArray groups = jsonResponse.getJSONArray("groups");
                List<Map<String, Object>> groupList = new ArrayList<>();

                for (int i = 0; i < groups.size(); i++) {
                    JSONObject group = groups.getJSONObject(i);
                    Map<String, Object> groupMap = new HashMap<>();
                    groupMap.put("groupName", group.getString("groupName"));
                    groupMap.put("reason", group.getString("reason"));

                    if ("documents".equals(type)) {
                        groupMap.put("billIds", group.getJSONArray("billIds").toJavaList(Integer.class));
                    } else {
                        groupMap.put("receiptIds", group.getJSONArray("receiptIds").toJavaList(Integer.class));
                    }

                    groupList.add(groupMap);
                }

                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("groups", groupList);
                result.put("message", String.format("AI分析完成，发现%d个可归并的组合", groupList.size()));
                return result;
            }

        } catch (Exception e) {
            log.warn("解析AI响应失败，尝试使用备用解析方法", e);
        }

        // 如果JSON解析失败，返回空结果
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("groups", new ArrayList<>());
        result.put("message", "AI分析完成，但未发现明确的归并建议");
        return result;
    }

    private String extractJsonFromResponse(String response) {
        // 尝试提取JSON部分
        int jsonStart = response.indexOf("{");
        int jsonEnd = response.lastIndexOf("}");

        if (jsonStart >= 0 && jsonEnd > jsonStart) {
            return response.substring(jsonStart, jsonEnd + 1);
        }

        return response;
    }

    private DocumentGroup createDocumentGroup(Integer accountSetsId, String groupName, List<Bill> bills, Integer currentUserId) {
        DocumentGroup group = new DocumentGroup();
        group.setGroupId(UUID.randomUUID().toString());
        group.setAccountSetsId(accountSetsId);
        group.setGroupName(groupName);
        group.setMergeRuleId("AI_MERGE"); // AI归并使用特殊的规则ID
        group.setStatus(DocumentGroup.Status.ACTIVE.getCode());
        group.setCreatedBy(currentUserId);
        group.setCreatedAt(new Date());
        group.setUpdatedAt(new Date());

        // 计算总金额
        BigDecimal totalAmount = bills.stream()
            .map(bill -> bill.getAmount() != null ? BigDecimal.valueOf(bill.getAmount()) : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        group.setTotalAmount(totalAmount);
        group.setItemCount(bills.size());

        documentGroupMapper.insert(group);
        return group;
    }

    private ReceiptGroup createReceiptGroup(Integer accountSetsId, String groupName, List<BankReceipts> receipts, Integer currentUserId) {
        ReceiptGroup group = new ReceiptGroup();
        group.setGroupId(UUID.randomUUID().toString());
        group.setAccountSetsId(accountSetsId);
        group.setGroupName(groupName);
        group.setMergeRuleId("AI_MERGE"); // AI归并使用特殊的规则ID
        group.setStatus(ReceiptGroup.Status.ACTIVE.getCode());
        group.setCreatedBy(currentUserId);
        group.setCreatedAt(new Date());
        group.setUpdatedAt(new Date());

        // 计算总金额
        BigDecimal totalAmount = receipts.stream()
            .map(receipt -> receipt.getAmount() != null ? BigDecimal.valueOf(receipt.getAmount()) : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        group.setTotalAmount(totalAmount);
        group.setItemCount(receipts.size());

        receiptGroupMapper.insert(group);
        return group;
    }

    private void updateBillsGroupId(List<Bill> bills, String groupId) {
        for (Bill bill : bills) {
            bill.setDocGroupId(groupId);
            billMapper.updateById(bill);
        }
    }

    private void updateReceiptsGroupId(List<BankReceipts> receipts, String groupId) {
        for (BankReceipts receipt : receipts) {
            receipt.setReceiptGroupId(groupId);
            bankReceiptsMapper.updateById(receipt);
        }
    }

    private Map<String, Object> createSuccessResult(String message, int groupCount, int totalItems) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", message);
        result.put("groupCount", groupCount);
        result.put("totalMergedItems", totalItems);
        return result;
    }

    private Map<String, Object> createSuccessResult(String message, int groupCount, int totalItems, List<String> createdGroups) {
        Map<String, Object> result = createSuccessResult(message, groupCount, totalItems);
        result.put("createdGroups", createdGroups);
        return result;
    }

    private Map<String, Object> createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        result.put("groupCount", 0);
        result.put("totalMergedItems", 0);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeSelectedDocumentMerge(Integer accountSetsId, List<Map<String, Object>> selectedGroups, Integer currentUserId) {
        log.info("开始执行选中的票据归并，账套ID: {}, 选中组数: {}", accountSetsId, selectedGroups.size());

        try {
            int groupCount = 0;
            int totalMergedItems = 0;
            List<String> createdGroups = new ArrayList<>();

            // 获取所有未归并的票据
            List<Bill> allBills = getUnmergedDocumentsForAi(accountSetsId, null);

            for (Map<String, Object> group : selectedGroups) {
                String groupName = (String) group.get("groupName");
                String reason = (String) group.get("reason");
                @SuppressWarnings("unchecked")
                List<Integer> billIds = (List<Integer>) group.get("billIds");

                if (billIds != null && billIds.size() > 1) {
                    // 获取要归并的票据
                    List<Bill> groupBills = allBills.stream()
                        .filter(bill -> billIds.contains(bill.getId()))
                        .collect(Collectors.toList());

                    if (groupBills.size() > 1) {
                        // 创建归并组
                        DocumentGroup docGroup = createDocumentGroup(accountSetsId, groupName, groupBills, currentUserId);
                        docGroup.setGroupSummary("AI智能归并(选中): " + reason);
                        documentGroupMapper.updateById(docGroup);

                        // 更新票据的归并组ID
                        updateBillsGroupId(groupBills, docGroup.getGroupId());

                        groupCount++;
                        totalMergedItems += groupBills.size();
                        createdGroups.add(String.format("%s (%d个票据)", groupName, groupBills.size()));

                        log.info("创建选中票据归并组: {}, 包含票据数量: {}, 原因: {}",
                            groupName, groupBills.size(), reason);
                    }
                }
            }

            return createSuccessResult(
                String.format("选中归并完成，创建了%d个归并组，共归并%d个票据", groupCount, totalMergedItems),
                groupCount, totalMergedItems, createdGroups
            );

        } catch (Exception e) {
            log.error("执行选中票据归并失败", e);
            return createErrorResult("选中归并失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeSelectedReceiptMerge(Integer accountSetsId, List<Map<String, Object>> selectedGroups, Integer currentUserId) {
        log.info("开始执行选中的银证归并，账套ID: {}, 选中组数: {}", accountSetsId, selectedGroups.size());

        try {
            int groupCount = 0;
            int totalMergedItems = 0;
            List<String> createdGroups = new ArrayList<>();

            // 获取所有未归并的银证
            List<BankReceipts> allReceipts = getUnmergedReceiptsForAi(accountSetsId, null);

            for (Map<String, Object> group : selectedGroups) {
                String groupName = (String) group.get("groupName");
                String reason = (String) group.get("reason");
                @SuppressWarnings("unchecked")
                List<Integer> receiptIds = (List<Integer>) group.get("receiptIds");

                if (receiptIds != null && receiptIds.size() > 1) {
                    // 获取要归并的银证
                    List<BankReceipts> groupReceipts = allReceipts.stream()
                        .filter(receipt -> receiptIds.contains(receipt.getId()))
                        .collect(Collectors.toList());

                    if (groupReceipts.size() > 1) {
                        // 创建归并组
                        ReceiptGroup receiptGroup = createReceiptGroup(accountSetsId, groupName, groupReceipts, currentUserId);
                        receiptGroup.setGroupSummary("AI智能归并(选中): " + reason);
                        receiptGroupMapper.updateById(receiptGroup);

                        // 更新银证的归并组ID
                        updateReceiptsGroupId(groupReceipts, receiptGroup.getGroupId());

                        groupCount++;
                        totalMergedItems += groupReceipts.size();
                        createdGroups.add(String.format("%s (%d个银证)", groupName, groupReceipts.size()));

                        log.info("创建选中银证归并组: {}, 包含银证数量: {}, 原因: {}",
                            groupName, groupReceipts.size(), reason);
                    }
                }
            }

            return createSuccessResult(
                String.format("选中归并完成，创建了%d个归并组，共归并%d个银证", groupCount, totalMergedItems),
                groupCount, totalMergedItems, createdGroups
            );

        } catch (Exception e) {
            log.error("执行选中银证归并失败", e);
            return createErrorResult("选中归并失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeSingleDocumentMerge(Integer accountSetsId, List<Integer> billIds, String groupName, String reason, Integer currentUserId) {
        log.info("执行单个票据归并建议，账套ID: {}, 票据数量: {}", accountSetsId, billIds.size());

        try {
            if (billIds == null || billIds.size() < 2) {
                return createErrorResult("至少需要2个票据才能进行归并");
            }

            // 确保billIds都是Integer类型
            List<Integer> validBillIds = billIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (validBillIds.size() < 2) {
                return createErrorResult("有效的票据ID数量不足");
            }

            // 获取票据列表
            List<Bill> bills = billMapper.selectBatchIds(validBillIds);
            bills = bills.stream()
                .filter(bill -> bill.getAccountSetsId().equals(accountSetsId))
                .filter(bill -> bill.getDocGroupId() == null) // 只处理未归并的票据
                .collect(Collectors.toList());

            if (bills.size() < 2) {
                return createErrorResult("可归并的票据数量不足");
            }

            // 创建归并组
            DocumentGroup documentGroup = createDocumentGroup(accountSetsId, groupName, bills, currentUserId);
            documentGroup.setGroupSummary("AI建议归并: " + reason);
            documentGroupMapper.updateById(documentGroup);

            // 更新票据的归并组ID
            updateBillsGroupId(bills, documentGroup.getGroupId());

            log.info("成功执行单个票据归并建议: {}, 包含票据数量: {}", groupName, bills.size());

            return createSuccessResult(
                String.format("归并建议已执行，创建归并组: %s，包含%d个票据", groupName, bills.size()),
                1, bills.size()
            );

        } catch (Exception e) {
            log.error("执行单个票据归并建议失败", e);
            return createErrorResult("执行归并建议失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeSingleReceiptMerge(Integer accountSetsId, List<Integer> receiptIds, String groupName, String reason, Integer currentUserId) {
        log.info("执行单个银证归并建议，账套ID: {}, 银证数量: {}", accountSetsId, receiptIds.size());

        try {
            if (receiptIds == null || receiptIds.size() < 2) {
                return createErrorResult("至少需要2个银证才能进行归并");
            }

            // 确保receiptIds都是Integer类型
            List<Integer> validReceiptIds = receiptIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (validReceiptIds.size() < 2) {
                return createErrorResult("有效的银证ID数量不足");
            }

            // 获取银证列表
            List<BankReceipts> receipts = bankReceiptsMapper.selectBatchIds(validReceiptIds);
            receipts = receipts.stream()
                .filter(receipt -> receipt.getAccountSetsId().equals(accountSetsId))
                .filter(receipt -> receipt.getReceiptGroupId() == null) // 只处理未归并的银证
                .collect(Collectors.toList());

            if (receipts.size() < 2) {
                return createErrorResult("可归并的银证数量不足");
            }

            // 创建归并组
            ReceiptGroup receiptGroup = createReceiptGroup(accountSetsId, groupName, receipts, currentUserId);
            receiptGroup.setGroupSummary("AI建议归并: " + reason);
            receiptGroupMapper.updateById(receiptGroup);

            // 更新银证的归并组ID
            updateReceiptsGroupId(receipts, receiptGroup.getGroupId());

            log.info("成功执行单个银证归并建议: {}, 包含银证数量: {}", groupName, receipts.size());

            return createSuccessResult(
                String.format("归并建议已执行，创建归并组: %s，包含%d个银证", groupName, receipts.size()),
                1, receipts.size()
            );

        } catch (Exception e) {
            log.error("执行单个银证归并建议失败", e);
            return createErrorResult("执行归并建议失败: " + e.getMessage());
        }
    }
}
