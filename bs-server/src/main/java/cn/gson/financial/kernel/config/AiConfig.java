package cn.gson.financial.kernel.config;

import cn.gson.financial.kernel.model.entity.FinancialAiConfig;
import cn.gson.financial.kernel.service.FinancialAiConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * AI配置类
 * 支持OpenAI兼容的API配置，支持从数据库加载配置
 */
@Configuration
@ConfigurationProperties(prefix = "ai")
public class AiConfig {

    @Autowired(required = false)
    private FinancialAiConfigService financialAiConfigService;

    /**
     * 当前账套ID，默认为0（全局配置）
     */
    private Integer currentAccountSetsId = 0;

    /**
     * 是否启用AI功能
     */
    private boolean enabled = false;

    /**
     * API基础URL
     */
    private String baseUrl = "https://api.openai.com/v1";

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * 默认模型
     */
    private String defaultModel = "gpt-3.5-turbo";

    /**
     * 请求超时时间(秒)
     */
    private int timeout = 60;

    /**
     * 最大重试次数
     */
    private int maxRetries = 3;

    /**
     * 温度参数
     */
    private double temperature = 0.7;

    /**
     * 最大token数
     */
    private int maxTokens = 2000;
    
    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getDefaultModel() {
        return defaultModel;
    }

    public void setDefaultModel(String defaultModel) {
        this.defaultModel = defaultModel;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    public double getTemperature() {
        return temperature;
    }

    public void setTemperature(double temperature) {
        this.temperature = temperature;
    }

    public int getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(int maxTokens) {
        this.maxTokens = maxTokens;
    }

    /**
     * 从数据库加载配置
     */
    public void loadFromDatabase(Integer accountSetsId) {
        if (financialAiConfigService == null) {
            return; // 服务未注入，使用默认配置
        }

        this.currentAccountSetsId = accountSetsId;
        Map<String, String> configMap = financialAiConfigService.getAiConfig(accountSetsId);

        if (!configMap.isEmpty()) {
            // 从数据库加载配置
            this.enabled = Boolean.parseBoolean(configMap.getOrDefault(FinancialAiConfig.KEY_ENABLED, "false"));
            this.baseUrl = configMap.getOrDefault(FinancialAiConfig.KEY_BASE_URL, this.baseUrl);
            this.apiKey = configMap.getOrDefault(FinancialAiConfig.KEY_API_KEY, this.apiKey);
            this.defaultModel = configMap.getOrDefault(FinancialAiConfig.KEY_DEFAULT_MODEL, this.defaultModel);
            this.timeout = Integer.parseInt(configMap.getOrDefault(FinancialAiConfig.KEY_TIMEOUT, String.valueOf(this.timeout)));
            this.maxRetries = Integer.parseInt(configMap.getOrDefault(FinancialAiConfig.KEY_MAX_RETRIES, String.valueOf(this.maxRetries)));
            this.temperature = Double.parseDouble(configMap.getOrDefault(FinancialAiConfig.KEY_TEMPERATURE, String.valueOf(this.temperature)));
            this.maxTokens = Integer.parseInt(configMap.getOrDefault(FinancialAiConfig.KEY_MAX_TOKENS, String.valueOf(this.maxTokens)));
        }
    }

    /**
     * 保存配置到数据库
     */
    public boolean saveToDatabase(Integer accountSetsId) {
        if (financialAiConfigService == null) {
            return false;
        }

        Map<String, String> configMap = new HashMap<>();
        configMap.put(FinancialAiConfig.KEY_ENABLED, String.valueOf(this.enabled));
        configMap.put(FinancialAiConfig.KEY_BASE_URL, this.baseUrl);
        configMap.put(FinancialAiConfig.KEY_API_KEY, this.apiKey);
        configMap.put(FinancialAiConfig.KEY_DEFAULT_MODEL, this.defaultModel);
        configMap.put(FinancialAiConfig.KEY_TIMEOUT, String.valueOf(this.timeout));
        configMap.put(FinancialAiConfig.KEY_MAX_RETRIES, String.valueOf(this.maxRetries));
        configMap.put(FinancialAiConfig.KEY_TEMPERATURE, String.valueOf(this.temperature));
        configMap.put(FinancialAiConfig.KEY_MAX_TOKENS, String.valueOf(this.maxTokens));

        return financialAiConfigService.saveAiConfig(accountSetsId, configMap);
    }

    /**
     * 初始化配置（从数据库加载或创建默认配置）
     */
    @PostConstruct
    public void initConfig() {
        if (financialAiConfigService != null) {
            // 初始化默认配置
            financialAiConfigService.initDefaultConfig(currentAccountSetsId);
            // 加载配置
            loadFromDatabase(currentAccountSetsId);
        }
    }

    public Integer getCurrentAccountSetsId() {
        return currentAccountSetsId;
    }

    public void setCurrentAccountSetsId(Integer currentAccountSetsId) {
        this.currentAccountSetsId = currentAccountSetsId;
        loadFromDatabase(currentAccountSetsId);
    }

    /**
     * 从数据库加载用户配置
     */
    public void loadFromDatabaseByUser(Integer userId) {
        if (financialAiConfigService == null) {
            return; // 服务未注入，使用默认配置
        }

        Map<String, String> configMap = financialAiConfigService.getAiConfigByUser(userId);

        if (!configMap.isEmpty()) {
            // 从数据库加载用户配置
            this.enabled = Boolean.parseBoolean(configMap.getOrDefault("enabled", "false"));
            this.baseUrl = configMap.getOrDefault("base_url", this.baseUrl);
            this.apiKey = configMap.getOrDefault("api_key", this.apiKey);
            this.defaultModel = configMap.getOrDefault("default_model", this.defaultModel);
            this.timeout = Integer.parseInt(configMap.getOrDefault("timeout", String.valueOf(this.timeout)));
            this.maxRetries = Integer.parseInt(configMap.getOrDefault("max_retries", String.valueOf(this.maxRetries)));
            this.temperature = Double.parseDouble(configMap.getOrDefault("temperature", String.valueOf(this.temperature)));
            this.maxTokens = Integer.parseInt(configMap.getOrDefault("max_tokens", String.valueOf(this.maxTokens)));
        }
    }
}
