package cn.gson.financial.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户视图对象
 * 用于前端展示用户信息
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
@Data
public class UserVo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    private Integer id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 用户状态（0-禁用，1-启用）
     */
    private Integer status;
    
    /**
     * 角色ID
     */
    private Integer roleId;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 账套ID
     */
    private Integer accountSetsId;
}