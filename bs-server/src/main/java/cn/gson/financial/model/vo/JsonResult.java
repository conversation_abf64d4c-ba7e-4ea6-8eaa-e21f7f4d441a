package cn.gson.financial.model.vo;

import lombok.Data;

/**
 * JSON响应结果类
 * 用于统一API响应格式
 * 
 * @param <T> 响应数据类型
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
@Data
public class JsonResult<T> {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 成功状态码
     */
    public static final Integer SUCCESS_CODE = 200;
    
    /**
     * 失败状态码
     */
    public static final Integer ERROR_CODE = 500;
    
    /**
     * 私有构造函数
     */
    private JsonResult() {}
    
    /**
     * 私有构造函数
     */
    private JsonResult(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 成功响应（无数据）
     */
    public static <T> JsonResult<T> success() {
        return new JsonResult<>(SUCCESS_CODE, "操作成功", null);
    }
    
    /**
     * 成功响应（带数据）
     */
    public static <T> JsonResult<T> success(T data) {
        return new JsonResult<>(SUCCESS_CODE, "操作成功", data);
    }
    
    /**
     * 成功响应（自定义消息和数据）
     */
    public static <T> JsonResult<T> success(String message, T data) {
        return new JsonResult<>(SUCCESS_CODE, message, data);
    }
    
    /**
     * 成功响应（别名方法）
     */
    public static <T> JsonResult<T> successful() {
        return success();
    }
    
    /**
     * 成功响应（别名方法，带数据）
     */
    public static <T> JsonResult<T> successful(T data) {
        return success(data);
    }
    
    /**
     * 成功响应（别名方法，自定义消息和数据）
     */
    public static <T> JsonResult<T> successful(String message, T data) {
        return success(message, data);
    }
    
    /**
     * 失败响应（默认消息）
     */
    public static <T> JsonResult<T> error() {
        return new JsonResult<>(ERROR_CODE, "操作失败", null);
    }
    
    /**
     * 失败响应（自定义消息）
     */
    public static <T> JsonResult<T> error(String message) {
        return new JsonResult<>(ERROR_CODE, message, null);
    }
    
    /**
     * 失败响应（自定义状态码和消息）
     */
    public static <T> JsonResult<T> error(Integer code, String message) {
        return new JsonResult<>(code, message, null);
    }
    
    /**
     * 自定义响应
     */
    public static <T> JsonResult<T> build(Integer code, String message, T data) {
        return new JsonResult<>(code, message, data);
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return SUCCESS_CODE.equals(this.code);
    }
    
    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
}