package cn.gson.financial.common;

import cn.gson.financial.kernel.exception.ServiceException;
import cn.gson.financial.kernel.model.entity.AccountingCategory;
import cn.gson.financial.kernel.model.entity.Subject;
import cn.gson.financial.kernel.model.vo.SubjectVo;
import cn.gson.financial.kernel.model.vo.UserVo;
import cn.gson.financial.kernel.service.AccountingCategoryService;
import cn.gson.financial.kernel.service.SubjectService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.common</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年10月18日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Slf4j
@Component
public final class SubjectExcelUtils {
    private final String[] COLS = {"科目编码", "科目名称", "类别", "余额方向", "数量核算", "辅助核算", "外币核算", "期末调汇"};

    @Autowired
    private SubjectService subjectService;

    @Autowired
    private AccountingCategoryService categoryService;

    private List<Integer> encoding;

    /**
     * 读取excel数据并转化
     *
     * @param fileName
     * @param is
     * @param userVo
     * @return
     * @throws IOException
     */
    public List<SubjectVo> readExcel(String fileName, InputStream is, UserVo userVo) throws IOException {
        Workbook workbook = null;
        try {
            // 初始化编码配置
            String encodingStr = userVo.getAccountSets().getEncoding();
            if (StringUtils.isEmpty(encodingStr)) {
                throw new ServiceException("账套编码配置为空，请先设置科目编码规则");
            }

            try {
                encoding = Arrays.stream(encodingStr.split("-"))
                    .map(s -> Integer.parseInt(s.trim()))
                    .collect(Collectors.toList());
                log.info("科目编码配置: {}", encoding);
            } catch (Exception e) {
                throw new ServiceException("账套编码配置格式错误: %s，正确格式如：4-2-2-2", encodingStr);
            }
            List<SubjectVo> list = new ArrayList<>();
            Map<String, SubjectVo> subjectMap = new HashMap<>();

            log.info("开始解析Excel文件: {}", fileName);
            try {
                if ("xls".equals(FilenameUtils.getExtension(fileName))) {
                    workbook = new HSSFWorkbook(is);
                } else {
                    workbook = new XSSFWorkbook(is);
                }
            } catch (Exception e) {
                throw new ServiceException("Excel文件格式错误，请确保文件是有效的Excel文件(.xls或.xlsx)");
            }

            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new ServiceException("Excel文件中没有找到工作表");
            }

            int totalRows = sheet.getLastRowNum();
            if (totalRows < 1) {
                throw new ServiceException("Excel文件中没有数据行，请确保除表头外至少有一行数据");
            }

            log.info("Excel文件包含 {} 行数据（不含表头）", totalRows);

            //检查excel格式
            this.checkHeader(sheet);

            for (int i = 1; i <= totalRows; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    log.warn("第{}行为空，跳过", i + 1);
                    continue;
                }

                try {
                SubjectVo subject = new SubjectVo();
                subject.setLevel((short) 0);
                subject.setChildren(new ArrayList<>());

                // 处理类别
                Cell typeCell = row.getCell(2);
                if (typeCell == null) {
                    throw new ServiceException("第%d行类别列为空", i + 1);
                }
                String type = typeCell.getStringCellValue();
                if (type != null) {
                    type = type.replace("类", "").trim();
                }
                subject.setType(type);

                // 处理科目编码
                Cell codeCell = row.getCell(0);
                if (codeCell == null) {
                    throw new ServiceException("第%d行科目编码列为空", i + 1);
                }
                String code;
                if (codeCell.getCellType().equals(CellType.NUMERIC)) {
                    Double value = codeCell.getNumericCellValue();
                    code = value.intValue() + "";
                } else {
                    code = StringUtils.trim(codeCell.getStringCellValue());
                }
                if (StringUtils.isEmpty(code)) {
                    throw new ServiceException("第%d行科目编码为空", i + 1);
                }
                subject.setCode(code);

                // 处理科目名称
                Cell nameCell = row.getCell(1);
                if (nameCell == null) {
                    throw new ServiceException("第%d行科目名称列为空", i + 1);
                }
                String name = StringUtils.trim(nameCell.getStringCellValue());
                if (StringUtils.isEmpty(name)) {
                    throw new ServiceException("第%d行科目名称为空", i + 1);
                }
                subject.setName(name);
                subject.setBalanceDirection(StringUtils.trim(row.getCell(3).getStringCellValue()));
                subject.setStatus(true);
                subject.setAccountSetsId(userVo.getAccountSetsId());
                subject.setUnit(StringUtils.trim(row.getCell(4).getStringCellValue()));
                subject.setAuxiliaryAccounting(this.getCategory(userVo.getAccountSetsId(), row.getCell(5).getStringCellValue()));
                subject.setMnemonicCode(PinYinUtil.getFirstLettersLo(subject.getName()));

                if (userVo.getAccountSets().getAccountingStandards() == (short) 0 && subject.getType().equals("共同")) {
                    throw new ServiceException("当前企业会计准则为[小企业会计准则],不允许导入[企业会计准则]科目！");
                }

                if (!"借".equals(subject.getBalanceDirection()) && !"贷".equals(subject.getBalanceDirection())) {
                    throw new ServiceException("编码[" + subject.getCode() + "]的余额方向错误。正确值为：借、贷");
                }

                //编码格式检查
                this.checkCode(code);
                //顶级科目和子集科目分开存储
                int len = code.length();

                if (len == getLen(1)) {
                    subject.setLevel((short) 1);
                } else if (len == getLen(2)) {
                    subject.setLevel((short) 2);
                    subject.setParentCode(code.substring(0, getLen(1)));
                } else if (len == getLen(3)) {
                    subject.setLevel((short) 3);
                    subject.setParentCode(code.substring(0, getLen(2)));
                } else if (len == getLen(4)) {
                    subject.setLevel((short) 4);
                    subject.setParentCode(code.substring(0, getLen(3)));
                }
                list.add(subject);
                subjectMap.put(code, subject);
                } catch (Exception e) {
                    throw new ServiceException("第%d行数据处理失败: %s", i + 1, e.getMessage());
                }
            }

            Map<String, Subject> orgSubject = getByCodeList(userVo.getAccountSetsId(), subjectMap.keySet());

            //构建树形数据
            for (SubjectVo vo : list) {
                //如果编码已经存在，这设置初始属性
                if (orgSubject.containsKey(vo.getCode())) {
                    Subject subject = orgSubject.get(vo.getCode());
                    vo.setId(subject.getId());
                    vo.setSystemDefault(subject.getSystemDefault());
                    vo.setStatus(subject.getStatus());
                    vo.setParentId(subject.getParentId());
                }

                //上级判断
                if (StringUtils.isNotEmpty(vo.getParentCode())) {
                    SubjectVo parent = subjectMap.get(vo.getParentCode());
                    if (parent != null) {
                        vo.setMnemonicCode(parent.getMnemonicCode() + "_" + PinYinUtil.getFirstLettersLo(vo.getName()));
                        parent.getChildren().add(vo);
                    } else {
                        log.warn("科目编码[{}]的父科目[{}]不存在，请检查数据顺序", vo.getCode(), vo.getParentCode());
                        vo.setMnemonicCode(PinYinUtil.getFirstLettersLo(vo.getName()));
                    }
                }
            }

            List<SubjectVo> subjectVos = list.stream().filter(subjectVo -> new Short("1").equals(subjectVo.getLevel())).collect(Collectors.toList());

            log.info("科目导入解析完成，共解析出 {} 个一级科目", subjectVos.size());

            return subjectVos;
        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("Excel解析过程中发生异常", e);
            throw new ServiceException("Excel文件解析失败: " + e.getMessage());
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.warn("关闭Excel文件失败", e);
                }
            }
        }
    }

    /**
     * 获取长度
     *
     * @param lv
     * @return
     */
    private int getLen(int lv) {
        if (encoding == null || encoding.isEmpty()) {
            throw new ServiceException("科目编码配置未初始化");
        }

        if (lv <= 0 || lv > encoding.size()) {
            throw new ServiceException("科目级别[%d]超出配置范围，当前配置支持%d级科目", lv, encoding.size());
        }

        int len = 0;
        for (int i = 0; i < lv; i++) {
            Integer levelLen = encoding.get(i);
            if (levelLen == null || levelLen <= 0) {
                throw new ServiceException("科目编码配置第%d级长度无效: %s", i + 1, levelLen);
            }
            len += levelLen;
        }
        return len;
    }

    /**
     * 编码格式检查
     *
     * @param code
     */
    private void checkCode(@NonNull String code) {
        String reg = "\\d{4}(\\d{" + encoding.get(1) + "})?(\\d{" + encoding.get(2) + "})?(\\d{" + encoding.get(3) + "})?";
        if (!code.matches(reg)) {
            throw new ServiceException("编码：" + code + ",不符合格式：" + StringUtils.join(encoding, "-"));
        }
    }

    private Map<String, Subject> getByCodeList(Integer accountSetsId, Set<String> subjectCodeSet) {
        LambdaQueryWrapper<Subject> qw = Wrappers.lambdaQuery();
        qw.eq(Subject::getAccountSetsId, accountSetsId);
        qw.in(Subject::getCode, subjectCodeSet);

        List<Subject> list = this.subjectService.list(qw);
        return list.stream().collect(Collectors.toMap(Subject::getCode, subject -> subject));
    }

    private String getCategory(Integer accountSetsId, String ca) {
        if (StringUtils.isEmpty(ca)) {
            return null;
        }

        QueryWrapper<AccountingCategory> qw = Wrappers.query();
        qw.eq(AccountingCategory.COL_ACCOUNT_SETS_ID, accountSetsId);
        qw.in(AccountingCategory.COL_NAME, Arrays.asList(StringUtils.split(ca, "/")));
        List<AccountingCategory> list = categoryService.list(qw);
        if (list.isEmpty()) {
            throw new ServiceException("辅助项：[" + ca + "]在账套中不存在！");
        }
        List<Map<String, Object>> collect = list.stream().map(sss -> {
            Map<String, Object> item = new HashMap<>();
            item.put("id", sss.getId());
            item.put("name", sss.getName());
            return item;
        }).collect(Collectors.toList());
        return JSON.toJSONString(collect);
    }

    /**
     * 表头检查，不符合表头，不允许导入
     *
     * @param sheet
     */
    private void checkHeader(Sheet sheet) {
        Row row = sheet.getRow(0);
        if (row == null) {
            throw new ServiceException("Excel文件第一行为空，请确保第一行包含表头信息");
        }

        for (int i = 0; i < COLS.length; i++) {
            String expectedCol = COLS[i];
            Cell cell = row.getCell(i);

            if (cell == null) {
                throw new ServiceException("表头第%d列为空，期望的列名是：%s。完整表头顺序：%s",
                    i + 1, expectedCol, StringUtils.join(COLS, ","));
            }

            String actualCol = "";
            try {
                actualCol = cell.getStringCellValue();
            } catch (Exception e) {
                throw new ServiceException("表头第%d列不是文本格式，期望的列名是：%s。完整表头顺序：%s",
                    i + 1, expectedCol, StringUtils.join(COLS, ","));
            }

            if (!expectedCol.equalsIgnoreCase(actualCol.trim())) {
                throw new ServiceException("表头第%d列错误，实际是：%s，期望是：%s。完整表头顺序：%s",
                    i + 1, actualCol, expectedCol, StringUtils.join(COLS, ","));
            }
        }

        log.info("Excel表头验证通过");
    }
}
