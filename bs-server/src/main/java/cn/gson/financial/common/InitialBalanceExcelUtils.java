package cn.gson.financial.common;

import cn.gson.financial.kernel.exception.ServiceException;
import cn.gson.financial.kernel.model.entity.Subject;
import cn.gson.financial.kernel.model.entity.VoucherDetails;
import cn.gson.financial.kernel.model.vo.UserVo;
import cn.gson.financial.kernel.service.SubjectService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 期初余额Excel导入工具类
 */
@Component
@AllArgsConstructor
@Slf4j
public class InitialBalanceExcelUtils {

    private SubjectService subjectService;

    /**
     * 生成期初余额导入模板
     *
     * @param accountSetsId
     * @param response
     * @throws IOException
     */
    public void generateTemplate(Integer accountSetsId, HttpServletResponse response) throws IOException {
        log.info("开始生成账套{}的期初余额导入模板", accountSetsId);

        // 获取该账套的所有科目
        LambdaQueryWrapper<Subject> qw = Wrappers.lambdaQuery();
        qw.eq(Subject::getAccountSetsId, accountSetsId);
        qw.eq(Subject::getStatus, true);
        qw.orderByAsc(Subject::getCode);
        List<Subject> subjects = subjectService.list(qw);

        if (subjects.isEmpty()) {
            throw new ServiceException("该账套没有配置科目，请先配置科目");
        }

        // 获取叶子节点科目ID列表
        List<Integer> leafSubjectIds = subjectService.leafList(accountSetsId);

        // 创建Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("期初余额导入模板");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"科目编码", "科目名称", "余额方向", "期初余额", "数量", "借方累计", "贷方累计"};

        // 设置表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
            // 设置列宽
            sheet.setColumnWidth(i, 4000);
        }

        // 创建数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        // 填充科目数据
        int rowIndex = 1;
        for (Subject subject : subjects) {
            // 只为叶子节点科目创建行（可以录入期初余额的科目）
            if (leafSubjectIds.contains(subject.getId())) {
                Row dataRow = sheet.createRow(rowIndex++);

                // 科目编码
                Cell codeCell = dataRow.createCell(0);
                codeCell.setCellValue(subject.getCode());
                codeCell.setCellStyle(dataStyle);

                // 科目名称
                Cell nameCell = dataRow.createCell(1);
                nameCell.setCellValue(subject.getName());
                nameCell.setCellStyle(dataStyle);

                // 余额方向
                Cell directionCell = dataRow.createCell(2);
                directionCell.setCellValue(String.valueOf(subject.getBalanceDirection()));
                directionCell.setCellStyle(dataStyle);

                // 期初余额（空白，用户填写）
                Cell balanceCell = dataRow.createCell(3);
                balanceCell.setCellStyle(dataStyle);

                // 数量（空白，用户填写）
                Cell numCell = dataRow.createCell(4);
                numCell.setCellStyle(dataStyle);

                // 借方累计（空白，用户填写）
                Cell debitCell = dataRow.createCell(5);
                debitCell.setCellStyle(dataStyle);

                // 贷方累计（空白，用户填写）
                Cell creditCell = dataRow.createCell(6);
                creditCell.setCellStyle(dataStyle);
            }
        }

        // 设置响应头
        String fileName = "期初余额导入模板.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

        // 输出到响应流
        try (OutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
            outputStream.flush();
        } finally {
            workbook.close();
        }

        log.info("期初余额导入模板生成完成，包含{}个科目", rowIndex - 1);
    }

    /**
     * 读取excel数据并转化为期初余额数据
     *
     * @param fileName
     * @param is
     * @param userVo
     * @return
     * @throws IOException
     */
    public List<VoucherDetails> readExcel(String fileName, InputStream is, UserVo userVo) throws IOException {
        Workbook workbook = null;
        try {
            List<VoucherDetails> list = new ArrayList<>();
            Map<String, Subject> subjectMap = new HashMap<>();

            log.info("开始解析期初余额Excel文件: {}", fileName);
            try {
                if ("xls".equals(FilenameUtils.getExtension(fileName))) {
                    workbook = new HSSFWorkbook(is);
                } else {
                    workbook = new XSSFWorkbook(is);
                }
            } catch (Exception e) {
                throw new ServiceException("Excel文件格式错误，请确保文件是有效的Excel文件(.xls或.xlsx)");
            }

            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new ServiceException("Excel文件中没有找到工作表");
            }

            int totalRows = sheet.getLastRowNum();
            if (totalRows < 1) {
                throw new ServiceException("Excel文件中没有数据行，请确保除表头外至少有一行数据");
            }

            log.info("Excel文件包含 {} 行数据（不含表头）", totalRows);

            // 检查excel格式
            this.checkHeader(sheet);

            // 获取所有科目信息
            LambdaQueryWrapper<Subject> qw = Wrappers.lambdaQuery();
            qw.eq(Subject::getAccountSetsId, userVo.getAccountSetsId());
            List<Subject> subjects = subjectService.list(qw);
            for (Subject subject : subjects) {
                subjectMap.put(subject.getCode(), subject);
            }

            for (int i = 1; i <= totalRows; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    log.warn("第{}行为空，跳过", i + 1);
                    continue;
                }

                try {
                    VoucherDetails voucherDetails = new VoucherDetails();

                    // 处理科目编码
                    Cell codeCell = row.getCell(0);
                    if (codeCell == null) {
                        throw new ServiceException("第%d行科目编码列为空", i + 1);
                    }
                    String code = getCellStringValue(codeCell);
                    if (StringUtils.isBlank(code)) {
                        throw new ServiceException("第%d行科目编码不能为空", i + 1);
                    }

                    // 查找科目
                    Subject subject = subjectMap.get(code);
                    if (subject == null) {
                        throw new ServiceException("第%d行科目编码[%s]不存在", i + 1, code);
                    }

                    // 处理科目名称（验证）
                    Cell nameCell = row.getCell(1);
                    if (nameCell != null) {
                        String name = getCellStringValue(nameCell);
                        if (StringUtils.isNotBlank(name) && !name.equals(subject.getName())) {
                            log.warn("第{}行科目名称[{}]与系统中的科目名称[{}]不匹配", i + 1, name, subject.getName());
                        }
                    }

                    // 处理余额方向（验证）
                    Cell directionCell = row.getCell(2);
                    if (directionCell != null) {
                        String direction = getCellStringValue(directionCell);
                        if (StringUtils.isNotBlank(direction) && !direction.equals(subject.getBalanceDirection())) {
                            log.warn("第{}行余额方向[{}]与系统中的余额方向[{}]不匹配", i + 1, direction, subject.getBalanceDirection());
                        }
                    }

                    // 处理期初余额
                    Cell balanceCell = row.getCell(3);
                    Double balance = getCellNumericValue(balanceCell);

                    // 处理数量
                    Cell numCell = row.getCell(4);
                    Double num = getCellNumericValue(numCell);

                    // 处理借方累计
                    Cell debitCell = row.getCell(5);
                    Double debit = getCellNumericValue(debitCell);

                    // 处理贷方累计
                    Cell creditCell = row.getCell(6);
                    Double credit = getCellNumericValue(creditCell);

                    // 检查是否有任何数据需要导入
                    boolean hasData = (balance != null && balance != 0) ||
                                     (num != null && num != 0) ||
                                     (debit != null && debit != 0) ||
                                     (credit != null && credit != 0);

                    // 如果没有任何数据，跳过这一行
                    if (!hasData) {
                        continue;
                    }

                    // 设置期初余额
                    if (balance != null && balance != 0) {
                        if ("借".equals(subject.getBalanceDirection())) {
                            voucherDetails.setDebitAmount(balance);
                        } else {
                            voucherDetails.setCreditAmount(balance);
                        }
                    }

                    // 设置数量
                    if (num != null && num != 0) {
                        voucherDetails.setNum(num);
                    }

                    // 设置借方累计
                    if (debit != null && debit != 0) {
                        voucherDetails.setCumulativeDebit(debit);
                    }

                    // 设置贷方累计
                    if (credit != null && credit != 0) {
                        voucherDetails.setCumulativeCredit(credit);
                    }

                    // 设置基本信息
                    voucherDetails.setSummary("期初");
                    voucherDetails.setSubjectId(subject.getId());
                    voucherDetails.setSubjectName(subject.getCode() + "-" + subject.getName());
                    voucherDetails.setSubjectCode(subject.getCode());
                    voucherDetails.setAccountSetsId(userVo.getAccountSetsId());

                    list.add(voucherDetails);

                } catch (Exception e) {
                    throw new ServiceException("第%d行数据处理失败: %s", i + 1, e.getMessage());
                }
            }

            log.info("期初余额导入解析完成，共解析出 {} 条记录", list.size());
            return list;

        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("Excel解析过程中发生异常", e);
            throw new ServiceException("Excel文件解析失败: " + e.getMessage());
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.warn("关闭Excel文件失败", e);
                }
            }
        }
    }

    /**
     * 检查Excel表头格式
     *
     * @param sheet
     */
    private void checkHeader(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            throw new ServiceException("Excel文件缺少表头行");
        }

        String[] expectedHeaders = {"科目编码", "科目名称", "余额方向", "期初余额", "数量", "借方累计", "贷方累计"};
        
        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            if (cell == null) {
                throw new ServiceException("Excel表头第%d列为空，期望为：%s", i + 1, expectedHeaders[i]);
            }
            
            String headerValue = getCellStringValue(cell);
            if (!expectedHeaders[i].equals(headerValue)) {
                throw new ServiceException("Excel表头第%d列格式错误，期望为：%s，实际为：%s", 
                    i + 1, expectedHeaders[i], headerValue);
            }
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return StringUtils.trim(cell.getStringCellValue());
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return StringUtils.trim(cell.getCellFormula());
            default:
                return "";
        }
    }

    /**
     * 获取单元格数值
     */
    private Double getCellNumericValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case STRING:
                String strValue = StringUtils.trim(cell.getStringCellValue());
                if (StringUtils.isBlank(strValue)) {
                    return null;
                }
                try {
                    return Double.parseDouble(strValue);
                } catch (NumberFormatException e) {
                    throw new ServiceException("数值格式错误: %s", strValue);
                }
            default:
                return null;
        }
    }
}
