package cn.gson.financial.admin.service;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 管理员数据导入导出服务接口
 */
public interface AdminDataImportExportService {
    
    /**
     * 导出用户数据
     */
    void exportUsers(HttpServletResponse response, Map<String, Object> params);
    
    /**
     * 导入用户数据
     */
    Map<String, Object> importUsers(MultipartFile file, Integer adminUserId);
    
    /**
     * 导出账套数据
     */
    void exportAccountSets(HttpServletResponse response, Map<String, Object> params);
    
    /**
     * 导入账套数据
     */
    Map<String, Object> importAccountSets(MultipartFile file, Integer adminUserId);
    
    /**
     * 导出AI配置数据
     */
    void exportAiConfigs(HttpServletResponse response, Map<String, Object> params);
    
    /**
     * 导入AI配置数据
     */
    Map<String, Object> importAiConfigs(MultipartFile file, Integer adminUserId);
    
    /**
     * 获取导入导出记录
     */
    List<Map<String, Object>> getImportExportLogs(Integer page, Integer size, Integer adminUserId, String operationType);
    
    /**
     * 批量修复数据
     */
    Map<String, Object> batchRepairData(String dataType, List<Integer> ids);
}
