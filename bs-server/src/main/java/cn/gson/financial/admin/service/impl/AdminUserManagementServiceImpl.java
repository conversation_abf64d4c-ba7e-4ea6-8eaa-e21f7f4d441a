package cn.gson.financial.admin.service.impl;

import cn.gson.financial.admin.service.AdminUserManagementService;
import cn.gson.financial.kernel.model.entity.User;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.mapper.UserMapper;
import cn.gson.financial.kernel.model.mapper.AccountSetsMapper;
import cn.gson.financial.kernel.model.vo.UserVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 管理员用户管理服务实现类
 */
@Service
public class AdminUserManagementServiceImpl implements AdminUserManagementService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AccountSetsMapper accountSetsMapper;
    
    @Override
    public List<UserVo> getUserList(Integer page, Integer size, String keyword, String role, Integer accountSetsId) {
        // 构建查询条件
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        // 关键词搜索（用户名、手机号、邮箱）
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like("mobile", keyword)
                .or().like("nickname", keyword)
                .or().like("real_name", keyword)
                .or().like("email", keyword)
            );
        }

        // 账套筛选
        if (accountSetsId != null) {
            queryWrapper.eq("account_sets_id", accountSetsId);
        }

        // 分页查询
        Page<User> pageParam = new Page<>(page, size);
        userMapper.selectPage(pageParam, queryWrapper);

        // 转换为VO
        List<UserVo> userVoList = new ArrayList<>();
        for (User user : pageParam.getRecords()) {
            UserVo userVo = new UserVo();
            BeanUtils.copyProperties(user, userVo);

            // 获取账套信息
            if (user.getAccountSetsId() != null) {
                AccountSets accountSets = accountSetsMapper.selectById(user.getAccountSetsId());
                if (accountSets != null) {
                    userVo.setAccountSetsName(accountSets.getCompanyName());
                }
            }

            userVoList.add(userVo);
        }

        return userVoList;
    }
    
    @Override
    public UserVo getUserDetail(Integer userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        UserVo userVo = new UserVo();
        BeanUtils.copyProperties(user, userVo);

        // 获取账套信息
        if (user.getAccountSetsId() != null) {
            AccountSets accountSets = accountSetsMapper.selectById(user.getAccountSetsId());
            if (accountSets != null) {
                userVo.setAccountSetsName(accountSets.getCompanyName());
            }
        }

        return userVo;
    }
    
    @Override
    public User createUser(Map<String, Object> userData) {
        User user = new User();

        // 设置用户基本信息
        user.setMobile((String) userData.get("mobile"));
        user.setNickname((String) userData.get("nickname"));
        user.setRealName((String) userData.get("realName"));
        user.setEmail((String) userData.get("email"));
        user.setAccountSetsId((Integer) userData.get("accountSetsId"));

        // 设置默认密码
        user.setInitPassword("123456");
        user.setCreateDate(new Date());

        // 保存用户
        userMapper.insert(user);

        return user;
    }
    
    @Override
    public void updateUser(Integer userId, Map<String, Object> userData) {
        // 临时实现
    }
    
    @Override
    public void deleteUser(Integer userId) {
        // 临时实现
    }
    
    @Override
    public String resetPassword(Integer userId) {
        // 临时实现，返回默认密码
        return "123456";
    }
    
    @Override
    public void toggleUserStatus(Integer userId) {
        // 临时实现
    }
    
    @Override
    public List<Map<String, Object>> getUserAccountSets(Integer userId) {
        // 临时实现，返回空列表
        return new ArrayList<>();
    }
    
    @Override
    public void addUserToAccountSet(Integer userId, Integer accountSetsId, String role) {
        // 临时实现
    }
    
    @Override
    public void removeUserFromAccountSet(Integer userId, Integer accountSetsId) {
        // 临时实现
    }
    
    @Override
    public void batchOperation(String operation, List<Integer> userIds, Map<String, Object> data) {
        // 临时实现
    }
}
