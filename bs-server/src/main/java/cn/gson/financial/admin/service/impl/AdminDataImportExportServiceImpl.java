package cn.gson.financial.admin.service.impl;

import cn.gson.financial.admin.service.AdminDataImportExportService;
import cn.gson.financial.kernel.model.entity.User;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.entity.FinancialAiConfig;
import cn.gson.financial.kernel.model.mapper.UserMapper;
import cn.gson.financial.kernel.model.mapper.AccountSetsMapper;
import cn.gson.financial.kernel.model.mapper.FinancialAiConfigMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员数据导入导出服务实现类
 */
@Service
public class AdminDataImportExportServiceImpl implements AdminDataImportExportService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private AccountSetsMapper accountSetsMapper;
    
    @Autowired
    private FinancialAiConfigMapper financialAiConfigMapper;
    
    @Override
    public void exportUsers(HttpServletResponse response, Map<String, Object> params) {
        try {
            // 查询用户数据
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            List<User> users = userMapper.selectList(queryWrapper);
            
            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("用户数据");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"ID", "手机号", "昵称", "真实姓名", "邮箱", "账套ID", "创建时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 填充数据
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (int i = 0; i < users.size(); i++) {
                User user = users.get(i);
                Row row = sheet.createRow(i + 1);
                
                row.createCell(0).setCellValue(user.getId());
                row.createCell(1).setCellValue(user.getMobile());
                row.createCell(2).setCellValue(user.getNickname());
                row.createCell(3).setCellValue(user.getRealName());
                row.createCell(4).setCellValue(user.getEmail());
                row.createCell(5).setCellValue(user.getAccountSetsId());
                row.createCell(6).setCellValue(user.getCreateDate() != null ?
                    LocalDateTime.ofInstant(user.getCreateDate().toInstant(), java.time.ZoneId.systemDefault()).format(formatter) : "");
            }
            
            // 设置响应头
            String fileName = "用户数据_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            
            // 输出文件
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();
            
        } catch (IOException e) {
            throw new RuntimeException("导出用户数据失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> importUsers(MultipartFile file, Integer adminUserId) {
        Map<String, Object> result = new HashMap<>();
        int totalRecords = 0;
        int successRecords = 0;
        int failedRecords = 0;
        List<String> errorMessages = new ArrayList<>();
        
        try {
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            
            // 跳过标题行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                totalRecords++;
                
                try {
                    User user = new User();
                    user.setMobile(getCellValue(row.getCell(1)));
                    user.setNickname(getCellValue(row.getCell(2)));
                    user.setRealName(getCellValue(row.getCell(3)));
                    user.setEmail(getCellValue(row.getCell(4)));
                    
                    String accountSetsIdStr = getCellValue(row.getCell(5));
                    if (accountSetsIdStr != null && !accountSetsIdStr.isEmpty()) {
                        user.setAccountSetsId(Integer.parseInt(accountSetsIdStr));
                    }
                    
                    user.setCreateDate(new Date());
                    user.setInitPassword("123456");
                    
                    userMapper.insert(user);
                    successRecords++;
                    
                } catch (Exception e) {
                    failedRecords++;
                    errorMessages.add("第" + (i + 1) + "行: " + e.getMessage());
                }
            }
            
            workbook.close();
            
        } catch (IOException e) {
            throw new RuntimeException("导入用户数据失败: " + e.getMessage());
        }
        
        result.put("totalRecords", totalRecords);
        result.put("successRecords", successRecords);
        result.put("failedRecords", failedRecords);
        result.put("errorMessages", errorMessages);
        
        return result;
    }
    
    @Override
    public void exportAccountSets(HttpServletResponse response, Map<String, Object> params) {
        // 类似用户导出的实现
        try {
            QueryWrapper<AccountSets> queryWrapper = new QueryWrapper<>();
            List<AccountSets> accountSets = accountSetsMapper.selectList(queryWrapper);
            
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("账套数据");
            
            Row headerRow = sheet.createRow(0);
            String[] headers = {"ID", "公司名称", "启用日期", "统一社会信用代码", "会计准则", "地址", "创建时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (int i = 0; i < accountSets.size(); i++) {
                AccountSets accountSet = accountSets.get(i);
                Row row = sheet.createRow(i + 1);
                
                row.createCell(0).setCellValue(accountSet.getId());
                row.createCell(1).setCellValue(accountSet.getCompanyName());
                row.createCell(2).setCellValue(accountSet.getEnableDate() != null ?
                    LocalDateTime.ofInstant(accountSet.getEnableDate().toInstant(), java.time.ZoneId.systemDefault()).format(formatter) : "");
                row.createCell(3).setCellValue(accountSet.getCreditCode());
                row.createCell(4).setCellValue(accountSet.getAccountingStandards());
                row.createCell(5).setCellValue(accountSet.getAddress());
                row.createCell(6).setCellValue(accountSet.getCreateDate() != null ?
                    LocalDateTime.ofInstant(accountSet.getCreateDate().toInstant(), java.time.ZoneId.systemDefault()).format(formatter) : "");
            }
            
            String fileName = "账套数据_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();
            
        } catch (IOException e) {
            throw new RuntimeException("导出账套数据失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> importAccountSets(MultipartFile file, Integer adminUserId) {
        // 临时实现
        Map<String, Object> result = new HashMap<>();
        result.put("totalRecords", 0);
        result.put("successRecords", 0);
        result.put("failedRecords", 0);
        result.put("errorMessages", new ArrayList<>());
        return result;
    }
    
    @Override
    public void exportAiConfigs(HttpServletResponse response, Map<String, Object> params) {
        // 临时实现
    }
    
    @Override
    public Map<String, Object> importAiConfigs(MultipartFile file, Integer adminUserId) {
        // 临时实现
        Map<String, Object> result = new HashMap<>();
        result.put("totalRecords", 0);
        result.put("successRecords", 0);
        result.put("failedRecords", 0);
        result.put("errorMessages", new ArrayList<>());
        return result;
    }
    
    @Override
    public List<Map<String, Object>> getImportExportLogs(Integer page, Integer size, Integer adminUserId, String operationType) {
        // 临时实现
        return new ArrayList<>();
    }
    
    @Override
    public Map<String, Object> batchRepairData(String dataType, List<Integer> ids) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failedCount = 0;
        List<String> errorMessages = new ArrayList<>();
        
        try {
            if ("users".equals(dataType)) {
                // 修复用户数据
                for (Integer id : ids) {
                    try {
                        User user = userMapper.selectById(id);
                        if (user != null) {
                            // 执行数据修复逻辑
                            if (user.getCreateDate() == null) {
                                user.setCreateDate(new Date());
                            }
                            if (user.getInitPassword() == null || user.getInitPassword().isEmpty()) {
                                user.setInitPassword("123456");
                            }
                            userMapper.updateById(user);
                            successCount++;
                        }
                    } catch (Exception e) {
                        failedCount++;
                        errorMessages.add("用户ID " + id + ": " + e.getMessage());
                    }
                }
            }
            // 可以添加其他数据类型的修复逻辑
            
        } catch (Exception e) {
            throw new RuntimeException("批量修复数据失败: " + e.getMessage());
        }
        
        result.put("successCount", successCount);
        result.put("failedCount", failedCount);
        result.put("errorMessages", errorMessages);
        
        return result;
    }
    
    /**
     * 获取单元格值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return null;
        }
    }
}
