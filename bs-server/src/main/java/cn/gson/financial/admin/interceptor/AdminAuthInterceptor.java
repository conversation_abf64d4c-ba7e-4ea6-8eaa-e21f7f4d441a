package cn.gson.financial.admin.interceptor;

import cn.gson.financial.admin.annotation.AdminLogin;
import cn.gson.financial.admin.annotation.AdminPermission;
import cn.gson.financial.admin.model.vo.AdminUserVo;
import cn.gson.financial.admin.service.AdminPermissionService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统管理员认证和权限拦截器
 */
@Component
public class AdminAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private AdminPermissionService adminPermissionService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果不是HandlerMethod类型，直接放行
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        
        // 检查是否需要管理员登录
        AdminLogin adminLogin = getAdminLoginAnnotation(handlerMethod);
        if (adminLogin == null) {
            return true; // 不需要管理员登录验证
        }

        // 验证管理员登录状态
        HttpSession session = request.getSession(false);
        if (session == null) {
            return handleUnauthorized(response, "未登录");
        }

        AdminUserVo adminUser = (AdminUserVo) session.getAttribute("adminUser");
        if (adminUser == null) {
            return handleUnauthorized(response, "会话已过期");
        }

        // 检查用户状态
        if (!adminUser.getStatus()) {
            return handleUnauthorized(response, "账号已被禁用");
        }

        // 检查是否需要超级管理员权限
        if (adminLogin.requireSuperAdmin() && !adminUser.getIsSuperAdmin()) {
            return handleForbidden(response, "需要超级管理员权限");
        }

        // 检查具体权限
        AdminPermission adminPermission = getAdminPermissionAnnotation(handlerMethod);
        if (adminPermission != null) {
            String requiredPermission = adminPermission.value();
            if (!adminPermissionService.hasPermission(adminUser.getId(), requiredPermission)) {
                return handleForbidden(response, "权限不足: " + adminPermission.description());
            }
        }

        // 更新最后访问时间
        adminPermissionService.updateLastAccessTime(adminUser.getId(), getClientIp(request));

        return true;
    }

    /**
     * 获取AdminLogin注解
     */
    private AdminLogin getAdminLoginAnnotation(HandlerMethod handlerMethod) {
        AdminLogin adminLogin = handlerMethod.getMethodAnnotation(AdminLogin.class);
        if (adminLogin == null) {
            adminLogin = handlerMethod.getBeanType().getAnnotation(AdminLogin.class);
        }
        return adminLogin;
    }

    /**
     * 获取AdminPermission注解
     */
    private AdminPermission getAdminPermissionAnnotation(HandlerMethod handlerMethod) {
        AdminPermission adminPermission = handlerMethod.getMethodAnnotation(AdminPermission.class);
        if (adminPermission == null) {
            adminPermission = handlerMethod.getBeanType().getAnnotation(AdminPermission.class);
        }
        return adminPermission;
    }

    /**
     * 处理未授权响应
     */
    private boolean handleUnauthorized(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("code", HttpStatus.UNAUTHORIZED.value());
        result.put("message", message);

        response.getWriter().write(objectMapper.writeValueAsString(result));
        return false;
    }

    /**
     * 处理禁止访问响应
     */
    private boolean handleForbidden(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpStatus.FORBIDDEN.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("code", HttpStatus.FORBIDDEN.value());
        result.put("message", message);

        response.getWriter().write(objectMapper.writeValueAsString(result));
        return false;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
