package cn.gson.financial.admin.service;

import java.util.Map;

/**
 * 管理员批量任务管理服务接口
 */
public interface AdminBatchTaskService {

    /**
     * 获取批量任务列表
     * @param page 页码
     * @param size 每页大小
     * @param status 任务状态
     * @param importType 导入类型
     * @param accountSetsId 账套ID
     * @param keyword 关键词搜索
     * @return 任务列表和分页信息
     */
    Map<String, Object> getTaskList(Integer page, Integer size, String status, String importType, Integer accountSetsId, String keyword);

    /**
     * 获取任务详情
     * @param taskId 任务ID
     * @return 任务详情和明细信息
     */
    Map<String, Object> getTaskDetail(String taskId);

    /**
     * 继续卡住的任务
     * @param taskId 任务ID
     */
    void continueTask(String taskId);

    /**
     * 删除任务
     * @param taskId 任务ID
     */
    void deleteTask(String taskId);

    /**
     * 重新执行任务
     * @param taskId 任务ID
     */
    void retryTask(String taskId);

    /**
     * 清理长期卡住的任务
     * @param minutesThreshold 时间阈值（分钟）
     * @return 清理结果
     */
    Map<String, Object> cleanupStuckTasks(Integer minutesThreshold);

    /**
     * 获取任务统计信息
     * @return 统计信息
     */
    Map<String, Object> getTaskStatistics();

    /**
     * 强制更新任务状态
     * @param taskId 任务ID
     * @param status 新状态
     * @param errorMessage 错误信息
     */
    void updateTaskStatus(String taskId, String status, String errorMessage);
}
