package cn.gson.financial.admin.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 系统管理员登录验证注解
 * 用于标记需要管理员登录才能访问的接口
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AdminLogin {
    
    /**
     * 是否需要超级管理员权限
     */
    boolean requireSuperAdmin() default false;
}
