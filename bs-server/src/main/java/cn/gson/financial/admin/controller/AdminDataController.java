package cn.gson.financial.admin.controller;

import cn.gson.financial.admin.annotation.AdminPermission;
import cn.gson.financial.admin.annotation.AdminLogin;
import cn.gson.financial.admin.service.AdminDataImportExportService;
import cn.gson.financial.kernel.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 系统管理员 - 数据导入导出控制器
 */
@Api(tags = "系统管理员-数据导入导出")
@RestController
@RequestMapping("/admin/data")
@CrossOrigin
@AdminLogin
public class AdminDataController {

    @Autowired
    private AdminDataImportExportService adminDataImportExportService;

    @ApiOperation("导出用户数据")
    @GetMapping("/export/users")
    @AdminPermission("export:users")
    public void exportUsers(HttpServletResponse response, @RequestParam Map<String, Object> params) {
        adminDataImportExportService.exportUsers(response, params);
    }

    @ApiOperation("导入用户数据")
    @PostMapping("/import/users")
    @AdminPermission("import:users")
    public Result<Map<String, Object>> importUsers(@RequestParam("file") MultipartFile file) {
        try {
            // TODO: 从session获取当前管理员ID
            Integer adminUserId = 1;
            Map<String, Object> result = adminDataImportExportService.importUsers(file, adminUserId);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("导入用户数据失败: " + e.getMessage());
        }
    }

    @ApiOperation("导出账套数据")
    @GetMapping("/export/account-sets")
    @AdminPermission("export:account_sets")
    public void exportAccountSets(HttpServletResponse response, @RequestParam Map<String, Object> params) {
        adminDataImportExportService.exportAccountSets(response, params);
    }

    @ApiOperation("导入账套数据")
    @PostMapping("/import/account-sets")
    @AdminPermission("import:account_sets")
    public Result<Map<String, Object>> importAccountSets(@RequestParam("file") MultipartFile file) {
        try {
            Integer adminUserId = 1;
            Map<String, Object> result = adminDataImportExportService.importAccountSets(file, adminUserId);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("导入账套数据失败: " + e.getMessage());
        }
    }

    @ApiOperation("导出AI配置数据")
    @GetMapping("/export/ai-configs")
    @AdminPermission("export:ai_configs")
    public void exportAiConfigs(HttpServletResponse response, @RequestParam Map<String, Object> params) {
        adminDataImportExportService.exportAiConfigs(response, params);
    }

    @ApiOperation("导入AI配置数据")
    @PostMapping("/import/ai-configs")
    @AdminPermission("import:ai_configs")
    public Result<Map<String, Object>> importAiConfigs(@RequestParam("file") MultipartFile file) {
        try {
            Integer adminUserId = 1;
            Map<String, Object> result = adminDataImportExportService.importAiConfigs(file, adminUserId);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("导入AI配置数据失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取导入导出记录")
    @GetMapping("/logs")
    @AdminPermission("log:view")
    public Result<List<Map<String, Object>>> getImportExportLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Integer adminUserId,
            @RequestParam(required = false) String operationType) {
        try {
            List<Map<String, Object>> logs = adminDataImportExportService.getImportExportLogs(page, size, adminUserId, operationType);
            return Result.success(logs);
        } catch (Exception e) {
            return Result.error("获取导入导出记录失败: " + e.getMessage());
        }
    }

    @ApiOperation("批量修复数据")
    @PostMapping("/repair")
    @AdminPermission("system:repair")
    public Result<Map<String, Object>> batchRepairData(@RequestBody Map<String, Object> data) {
        try {
            String dataType = (String) data.get("dataType");
            @SuppressWarnings("unchecked")
            List<Integer> ids = (List<Integer>) data.get("ids");
            
            Map<String, Object> result = adminDataImportExportService.batchRepairData(dataType, ids);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("批量修复数据失败: " + e.getMessage());
        }
    }
}
