package cn.gson.financial.admin.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 系统管理员权限验证注解
 * 用于标记需要特定权限才能访问的接口
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AdminPermission {
    
    /**
     * 需要的权限码
     * 格式：模块:操作，如 user:view, user:create, user:edit, user:delete
     * 支持通配符：user:* 表示用户模块的所有权限
     */
    String value();
    
    /**
     * 权限描述
     */
    String description() default "";
}
