package cn.gson.financial.admin.controller;

import cn.gson.financial.admin.annotation.AdminLogin;
import cn.gson.financial.kernel.common.Result;
import cn.gson.financial.kernel.model.entity.User;
import cn.gson.financial.kernel.model.mapper.UserMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员端 - 用户管理控制器
 */
@Api(tags = "管理员端-用户管理")
@RestController
@RequestMapping("/admin/user")
@CrossOrigin
@AdminLogin
@Slf4j
public class AdminUserController {

    @Autowired
    private UserMapper userMapper;

    @ApiOperation("获取用户列表")
    @GetMapping("/list")
    public Result<Map<String, Object>> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer accountSetsId) {
        try {
            Page<User> pageObj = new Page<>(page, size);
            
            LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
            
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.and(w -> w.like(User::getRealName, keyword.trim())
                    .or().like(User::getNickname, keyword.trim())
                    .or().like(User::getEmail, keyword.trim())
                    .or().like(User::getMobile, keyword.trim()));
            }
            
            if (accountSetsId != null) {
                wrapper.eq(User::getAccountSetsId, accountSetsId);
            }
            
            wrapper.orderByDesc(User::getCreateDate);
            
            Page<User> result = (Page<User>) userMapper.selectPage(pageObj, wrapper);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", result.getRecords());
            data.put("total", result.getTotal());
            data.put("current", result.getCurrent());
            data.put("size", result.getSize());
            data.put("pages", result.getPages());
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error("获取用户列表失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取用户详情")
    @GetMapping("/{id}")
    public Result<User> getUserDetail(@PathVariable Integer id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }
            // 清除敏感信息
            user.setPassword(null);
            user.setInitPassword(null);
            return Result.success(user);
        } catch (Exception e) {
            log.error("获取用户详情失败", e);
            return Result.error("获取用户详情失败: " + e.getMessage());
        }
    }

    @ApiOperation("更新用户信息")
    @PutMapping("/{id}")
    public Result<?> updateUser(@PathVariable Integer id, @RequestBody User user) {
        try {
            User existingUser = userMapper.selectById(id);
            if (existingUser == null) {
                return Result.error("用户不存在");
            }
            
            user.setId(id);
            // 不允许通过此接口修改密码
            user.setPassword(null);
            user.setInitPassword(null);
            
            int result = userMapper.updateById(user);
            if (result > 0) {
                return Result.success("用户信息更新成功");
            } else {
                return Result.error("用户信息更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return Result.error("更新用户信息失败: " + e.getMessage());
        }
    }

    @ApiOperation("删除用户")
    @DeleteMapping("/{id}")
    public Result<?> deleteUser(@PathVariable Integer id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            int result = userMapper.deleteById(id);
            if (result > 0) {
                return Result.success("用户删除成功");
            } else {
                return Result.error("用户删除失败");
            }
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error("删除用户失败: " + e.getMessage());
        }
    }

    @ApiOperation("重置用户密码")
    @PostMapping("/{id}/reset-password")
    public Result<?> resetPassword(@PathVariable Integer id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 重置为默认密码
            String defaultPassword = "123456";
            user.setPassword(defaultPassword); // 这里应该加密，但为了简化暂时不加密
            user.setInitPassword(defaultPassword);
            
            int result = userMapper.updateById(user);
            if (result > 0) {
                return Result.success("密码重置成功，新密码为：123456");
            } else {
                return Result.error("密码重置失败");
            }
        } catch (Exception e) {
            log.error("重置用户密码失败", e);
            return Result.error("重置用户密码失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取用户统计信息")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getUserStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 总用户数
            Integer totalUsers = userMapper.selectCount(null);
            statistics.put("totalUsers", totalUsers);

            // 今日新增用户数（简化实现）
            statistics.put("todayNewUsers", 0);

            // 活跃用户数（简化实现）
            statistics.put("activeUsers", totalUsers);
            
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取用户统计信息失败", e);
            return Result.error("获取用户统计信息失败: " + e.getMessage());
        }
    }
}
