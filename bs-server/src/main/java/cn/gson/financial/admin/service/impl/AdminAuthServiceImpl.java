package cn.gson.financial.admin.service.impl;

import cn.gson.financial.admin.model.vo.AdminUserVo;
import cn.gson.financial.admin.service.AdminAuthService;
import cn.gson.financial.kernel.model.entity.AdminUser;
import cn.gson.financial.kernel.model.mapper.AdminUserMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * 管理员认证服务实现类
 */
@Service
public class AdminAuthServiceImpl implements AdminAuthService {
    
    @Autowired
    private AdminUserMapper adminUserMapper;
    
    @Override
    public AdminUserVo login(String username, String password, String clientIp) {
        // 查询管理员用户
        QueryWrapper<AdminUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        queryWrapper.eq("status", true);
        
        AdminUser adminUser = adminUserMapper.selectOne(queryWrapper);
        if (adminUser == null) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 验证密码
        String encryptedPassword = sha256(password);
        if (!encryptedPassword.equals(adminUser.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 更新登录信息
        adminUser.setLastLoginTime(LocalDateTime.now());
        adminUser.setLastLoginIp(clientIp);
        adminUser.setLoginCount(adminUser.getLoginCount() + 1);
        adminUserMapper.updateById(adminUser);
        
        // 转换为VO
        AdminUserVo adminUserVo = new AdminUserVo();
        BeanUtils.copyProperties(adminUser, adminUserVo);
        
        // 设置权限（临时实现，后续需要从角色表获取）
        if (adminUser.getIsSuperAdmin()) {
            adminUserVo.setPermissions(Arrays.asList("*"));
        } else {
            adminUserVo.setPermissions(new ArrayList<>());
        }
        
        return adminUserVo;
    }
    
    @Override
    public void logout() {
        // 登出逻辑（清除session等）
    }
    
    @Override
    public AdminUserVo getCurrentUser() {
        // 从session或token获取当前用户信息
        // 临时实现，返回null
        return null;
    }
    
    @Override
    public void changePassword(Integer adminUserId, String oldPassword, String newPassword) {
        // 查询用户
        AdminUser adminUser = adminUserMapper.selectById(adminUserId);
        if (adminUser == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证原密码
        String encryptedOldPassword = sha256(oldPassword);
        if (!encryptedOldPassword.equals(adminUser.getPassword())) {
            throw new RuntimeException("原密码错误");
        }

        // 更新密码
        String encryptedNewPassword = sha256(newPassword);
        adminUser.setPassword(encryptedNewPassword);
        adminUserMapper.updateById(adminUser);
    }

    @Override
    public void updateProfile(Integer adminUserId, String realName, String email, String mobile) {
        // 获取管理员用户
        AdminUser adminUser = adminUserMapper.selectById(adminUserId);
        if (adminUser == null) {
            throw new RuntimeException("管理员用户不存在");
        }

        // 更新个人资料
        adminUser.setRealName(realName);
        adminUser.setEmail(email);
        adminUser.setMobile(mobile);
        adminUserMapper.updateById(adminUser);
    }

    /**
     * SHA256加密
     */
    private String sha256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not found", e);
        }
    }
}
