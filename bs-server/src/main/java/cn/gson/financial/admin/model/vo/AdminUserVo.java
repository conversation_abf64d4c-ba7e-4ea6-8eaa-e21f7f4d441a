package cn.gson.financial.admin.model.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理员用户VO
 */
@Data
public class AdminUserVo {
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 管理员用户名
     */
    private String username;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 头像URL
     */
    private String avatarUrl;
    
    /**
     * 状态：1启用，0禁用
     */
    private Boolean status;
    
    /**
     * 是否超级管理员
     */
    private Boolean isSuperAdmin;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 最后登录IP
     */
    private String lastLoginIp;
    
    /**
     * 登录次数
     */
    private Integer loginCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 权限列表
     */
    private List<String> permissions;
}
