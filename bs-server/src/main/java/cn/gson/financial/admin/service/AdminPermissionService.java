package cn.gson.financial.admin.service;

/**
 * 管理员权限服务接口
 */
public interface AdminPermissionService {
    
    /**
     * 检查管理员是否有指定权限
     * @param adminUserId 管理员用户ID
     * @param permission 权限码
     * @return 是否有权限
     */
    boolean hasPermission(Integer adminUserId, String permission);
    
    /**
     * 更新管理员最后访问时间
     * @param adminUserId 管理员用户ID
     * @param clientIp 客户端IP
     */
    void updateLastAccessTime(Integer adminUserId, String clientIp);
}
