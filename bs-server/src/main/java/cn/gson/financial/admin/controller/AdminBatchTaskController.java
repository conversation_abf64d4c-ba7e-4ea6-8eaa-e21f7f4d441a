package cn.gson.financial.admin.controller;

import cn.gson.financial.admin.annotation.AdminLogin;
import cn.gson.financial.admin.annotation.AdminPermission;
import cn.gson.financial.admin.service.AdminBatchTaskService;
import cn.gson.financial.kernel.common.Result;
import cn.gson.financial.kernel.model.entity.BatchImportTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 系统管理员 - 批量任务管理控制器
 */
@Api(tags = "系统管理员-批量任务管理")
@RestController
@RequestMapping("/admin/batch-tasks")
@CrossOrigin
@AdminLogin
@Slf4j
public class AdminBatchTaskController {

    @Autowired
    private AdminBatchTaskService adminBatchTaskService;

    @ApiOperation("获取所有批量任务列表")
    @GetMapping("/list")
    @AdminPermission("batch_task:view")
    public Result<Map<String, Object>> getTaskList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String importType,
            @RequestParam(required = false) Integer accountSetsId,
            @RequestParam(required = false) String keyword) {
        try {
            Map<String, Object> result = adminBatchTaskService.getTaskList(page, size, status, importType, accountSetsId, keyword);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取批量任务列表失败", e);
            return Result.error("获取任务列表失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取任务详情")
    @GetMapping("/{taskId}")
    @AdminPermission("batch_task:view")
    public Result<Map<String, Object>> getTaskDetail(@PathVariable String taskId) {
        try {
            Map<String, Object> result = adminBatchTaskService.getTaskDetail(taskId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取任务详情失败，任务ID: {}", taskId, e);
            return Result.error("获取任务详情失败: " + e.getMessage());
        }
    }

    @ApiOperation("继续卡住的任务")
    @PostMapping("/{taskId}/continue")
    @AdminPermission("batch_task:manage")
    public Result<?> continueTask(@PathVariable String taskId) {
        try {
            adminBatchTaskService.continueTask(taskId);
            return Result.success("任务已重新启动");
        } catch (Exception e) {
            log.error("继续任务失败，任务ID: {}", taskId, e);
            return Result.error("继续任务失败: " + e.getMessage());
        }
    }

    @ApiOperation("删除任务")
    @DeleteMapping("/{taskId}")
    @AdminPermission("batch_task:delete")
    public Result<?> deleteTask(@PathVariable String taskId) {
        try {
            adminBatchTaskService.deleteTask(taskId);
            return Result.success("任务已删除");
        } catch (Exception e) {
            log.error("删除任务失败，任务ID: {}", taskId, e);
            return Result.error("删除任务失败: " + e.getMessage());
        }
    }

    @ApiOperation("重新执行任务")
    @PostMapping("/{taskId}/retry")
    @AdminPermission("batch_task:manage")
    public Result<?> retryTask(@PathVariable String taskId) {
        try {
            adminBatchTaskService.retryTask(taskId);
            return Result.success("任务已重新执行");
        } catch (Exception e) {
            log.error("重新执行任务失败，任务ID: {}", taskId, e);
            return Result.error("重新执行任务失败: " + e.getMessage());
        }
    }

    @ApiOperation("批量删除长期卡住的任务")
    @PostMapping("/cleanup-stuck")
    @AdminPermission("batch_task:delete")
    public Result<Map<String, Object>> cleanupStuckTasks(
            @RequestParam(defaultValue = "1440") Integer minutesThreshold) {
        try {
            Map<String, Object> result = adminBatchTaskService.cleanupStuckTasks(minutesThreshold);
            return Result.success(result);
        } catch (Exception e) {
            log.error("清理卡住任务失败", e);
            return Result.error("清理卡住任务失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取任务统计信息")
    @GetMapping("/statistics")
    @AdminPermission("batch_task:view")
    public Result<Map<String, Object>> getTaskStatistics() {
        try {
            Map<String, Object> result = adminBatchTaskService.getTaskStatistics();
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    @ApiOperation("强制更新任务状态")
    @PutMapping("/{taskId}/status")
    @AdminPermission("batch_task:manage")
    public Result<?> updateTaskStatus(
            @PathVariable String taskId,
            @RequestParam String status,
            @RequestParam(required = false) String errorMessage) {
        try {
            adminBatchTaskService.updateTaskStatus(taskId, status, errorMessage);
            return Result.success("任务状态已更新");
        } catch (Exception e) {
            log.error("更新任务状态失败，任务ID: {}", taskId, e);
            return Result.error("更新任务状态失败: " + e.getMessage());
        }
    }
}
