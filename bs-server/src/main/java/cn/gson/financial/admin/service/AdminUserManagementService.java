package cn.gson.financial.admin.service;

import cn.gson.financial.kernel.model.entity.User;
import cn.gson.financial.kernel.model.vo.UserVo;

import java.util.List;
import java.util.Map;

/**
 * 管理员用户管理服务接口
 */
public interface AdminUserManagementService {
    
    /**
     * 获取用户列表
     */
    List<UserVo> getUserList(Integer page, Integer size, String keyword, String role, Integer accountSetsId);
    
    /**
     * 获取用户详情
     */
    UserVo getUserDetail(Integer userId);
    
    /**
     * 创建用户
     */
    User createUser(Map<String, Object> userData);
    
    /**
     * 更新用户信息
     */
    void updateUser(Integer userId, Map<String, Object> userData);
    
    /**
     * 删除用户
     */
    void deleteUser(Integer userId);
    
    /**
     * 重置用户密码
     */
    String resetPassword(Integer userId);
    
    /**
     * 切换用户状态
     */
    void toggleUserStatus(Integer userId);
    
    /**
     * 获取用户的账套关联
     */
    List<Map<String, Object>> getUserAccountSets(Integer userId);
    
    /**
     * 为用户添加账套权限
     */
    void addUserToAccountSet(Integer userId, Integer accountSetsId, String role);
    
    /**
     * 移除用户的账套权限
     */
    void removeUserFromAccountSet(Integer userId, Integer accountSetsId);
    
    /**
     * 批量操作用户
     */
    void batchOperation(String operation, List<Integer> userIds, Map<String, Object> data);
}
