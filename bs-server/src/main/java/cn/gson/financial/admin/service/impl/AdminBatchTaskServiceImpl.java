package cn.gson.financial.admin.service.impl;

import cn.gson.financial.admin.service.AdminBatchTaskService;
import cn.gson.financial.kernel.model.entity.BatchImportDetail;
import cn.gson.financial.kernel.model.entity.BatchImportTask;
import cn.gson.financial.kernel.model.mapper.BatchImportDetailMapper;
import cn.gson.financial.kernel.model.mapper.BatchImportTaskMapper;
import cn.gson.financial.service.AsyncBatchProcessService;
import cn.gson.financial.service.BatchOcrService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理员批量任务管理服务实现类
 */
@Service
@Slf4j
public class AdminBatchTaskServiceImpl implements AdminBatchTaskService {

    @Autowired
    private BatchImportTaskMapper batchImportTaskMapper;

    @Autowired
    private BatchImportDetailMapper batchImportDetailMapper;

    @Autowired
    private AsyncBatchProcessService asyncBatchProcessService;

    @Autowired
    private BatchOcrService batchOcrService;

    @Override
    public Map<String, Object> getTaskList(Integer page, Integer size, String status, String importType, Integer accountSetsId, String keyword) {
        // 构建查询条件
        LambdaQueryWrapper<BatchImportTask> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(status)) {
            queryWrapper.eq(BatchImportTask::getStatus, status);
        }
        
        if (StringUtils.hasText(importType)) {
            queryWrapper.eq(BatchImportTask::getImportType, importType);
        }
        
        if (accountSetsId != null) {
            queryWrapper.eq(BatchImportTask::getAccountSetsId, accountSetsId);
        }
        
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like(BatchImportTask::getTaskName, keyword)
                .or()
                .like(BatchImportTask::getTaskId, keyword)
            );
        }
        
        queryWrapper.orderByDesc(BatchImportTask::getCreatedTime);
        
        // 分页查询
        Page<BatchImportTask> pageParam = new Page<>(page, size);
        IPage<BatchImportTask> pageResult = batchImportTaskMapper.selectPage(pageParam, queryWrapper);
        
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("list", pageResult.getRecords());
        result.put("total", pageResult.getTotal());
        result.put("current", pageResult.getCurrent());
        result.put("size", pageResult.getSize());
        result.put("pages", pageResult.getPages());
        
        return result;
    }

    @Override
    public Map<String, Object> getTaskDetail(String taskId) {
        // 获取任务信息
        BatchImportTask task = batchImportTaskMapper.selectOne(
            new LambdaQueryWrapper<BatchImportTask>().eq(BatchImportTask::getTaskId, taskId)
        );
        
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        
        // 获取明细信息
        List<BatchImportDetail> details = batchImportDetailMapper.selectByTaskId(taskId);
        
        // 统计明细状态
        Map<String, Long> detailStatusCount = details.stream()
            .collect(Collectors.groupingBy(BatchImportDetail::getStatus, Collectors.counting()));
        
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("task", task);
        result.put("details", details);
        result.put("detailStatusCount", detailStatusCount);
        result.put("totalDetails", details.size());
        
        return result;
    }

    @Override
    @Transactional
    public void continueTask(String taskId) {
        BatchImportTask task = batchImportTaskMapper.selectOne(
            new LambdaQueryWrapper<BatchImportTask>().eq(BatchImportTask::getTaskId, taskId)
        );
        
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        
        log.info("管理员继续任务: {}, 当前状态: {}", taskId, task.getStatus());
        
        // 根据任务状态决定继续的方式
        switch (task.getStatus()) {
            case BatchImportTask.STATUS_PROCESSING:
                // 如果是处理中状态，重新启动OCR识别
                asyncBatchProcessService.startOcrRecognition(taskId, task.getAccountSetsId().toString(), task.getCreateUser());
                break;
                
            case BatchImportTask.STATUS_RECOGNIZING:
                // 如果是识别中状态，重新启动批量识别
                List<BatchImportDetail> pendingDetails = batchImportDetailMapper.selectList(
                    new LambdaQueryWrapper<BatchImportDetail>()
                        .eq(BatchImportDetail::getTaskId, taskId)
                        .eq(BatchImportDetail::getStatus, BatchImportDetail.STATUS_PENDING)
                );
                
                if (!pendingDetails.isEmpty()) {
                    List<Integer> detailIds = pendingDetails.stream()
                        .map(BatchImportDetail::getId)
                        .collect(Collectors.toList());
                    
                    if (BatchImportTask.TYPE_BANK_RECEIPT.equals(task.getImportType())) {
                        batchOcrService.batchRecognizeBankReceipts(taskId, detailIds, task.getCreateUser());
                    } else {
                        batchOcrService.batchRecognizeInvoices(taskId, detailIds, task.getCreateUser());
                    }
                }
                break;
                
            case BatchImportTask.STATUS_FAILED:
            case BatchImportTask.STATUS_CANCELLED:
                // 失败或取消的任务，重置状态后重新开始
                batchImportTaskMapper.updateStatus(taskId, BatchImportTask.STATUS_PROCESSING, null);
                asyncBatchProcessService.startOcrRecognition(taskId, task.getAccountSetsId().toString(), task.getCreateUser());
                break;
                
            default:
                throw new RuntimeException("任务状态不支持继续操作: " + task.getStatus());
        }
    }

    @Override
    @Transactional
    public void deleteTask(String taskId) {
        // 删除明细记录
        batchImportDetailMapper.delete(
            new LambdaQueryWrapper<BatchImportDetail>().eq(BatchImportDetail::getTaskId, taskId)
        );
        
        // 删除任务记录
        batchImportTaskMapper.delete(
            new LambdaQueryWrapper<BatchImportTask>().eq(BatchImportTask::getTaskId, taskId)
        );
        
        log.info("管理员删除任务: {}", taskId);
    }

    @Override
    @Transactional
    public void retryTask(String taskId) {
        BatchImportTask task = batchImportTaskMapper.selectOne(
            new LambdaQueryWrapper<BatchImportTask>().eq(BatchImportTask::getTaskId, taskId)
        );
        
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        
        // 重置任务状态
        batchImportTaskMapper.updateStatus(taskId, BatchImportTask.STATUS_PROCESSING, null);
        
        // 重置所有明细状态
        List<BatchImportDetail> details = batchImportDetailMapper.selectByTaskId(taskId);
        for (BatchImportDetail detail : details) {
            detail.setStatus(BatchImportDetail.STATUS_PENDING);
            detail.setRecognitionResult(null);
            detail.setParsedData(null);
            detail.setFinalData(null);
            detail.setErrorMessage(null);
            detail.setRetryCount(0);
            batchImportDetailMapper.updateById(detail);
        }
        
        // 重新开始处理
        asyncBatchProcessService.startOcrRecognition(taskId, task.getAccountSetsId().toString(), task.getCreateUser());
        
        log.info("管理员重新执行任务: {}", taskId);
    }

    @Override
    @Transactional
    public Map<String, Object> cleanupStuckTasks(Integer minutesThreshold) {
        // 查找长期卡住的任务
        LocalDateTime thresholdTime = LocalDateTime.now().minusMinutes(minutesThreshold);
        
        List<BatchImportTask> stuckTasks = batchImportTaskMapper.selectList(
            new LambdaQueryWrapper<BatchImportTask>()
                .in(BatchImportTask::getStatus, 
                    BatchImportTask.STATUS_PROCESSING, 
                    BatchImportTask.STATUS_RECOGNIZING, 
                    BatchImportTask.STATUS_SAVING)
                .lt(BatchImportTask::getUpdatedTime, thresholdTime)
        );
        
        int deletedCount = 0;
        List<String> deletedTaskIds = new ArrayList<>();
        
        for (BatchImportTask task : stuckTasks) {
            try {
                deleteTask(task.getTaskId());
                deletedCount++;
                deletedTaskIds.add(task.getTaskId());
            } catch (Exception e) {
                log.error("删除卡住任务失败: {}", task.getTaskId(), e);
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("foundCount", stuckTasks.size());
        result.put("deletedCount", deletedCount);
        result.put("deletedTaskIds", deletedTaskIds);
        result.put("minutesThreshold", minutesThreshold);
        
        log.info("清理卡住任务完成，发现: {}, 删除: {}", stuckTasks.size(), deletedCount);
        
        return result;
    }

    @Override
    public Map<String, Object> getTaskStatistics() {
        // 统计各状态任务数量
        List<BatchImportTask> allTasks = batchImportTaskMapper.selectList(null);
        
        Map<String, Long> statusCount = allTasks.stream()
            .collect(Collectors.groupingBy(BatchImportTask::getStatus, Collectors.counting()));
        
        Map<String, Long> typeCount = allTasks.stream()
            .collect(Collectors.groupingBy(BatchImportTask::getImportType, Collectors.counting()));
        
        // 统计今日任务
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        long todayCount = allTasks.stream()
            .filter(task -> task.getCreatedTime().isAfter(todayStart))
            .count();
        
        // 统计卡住的任务（超过1小时未更新）
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        long stuckCount = allTasks.stream()
            .filter(task -> Arrays.asList(
                BatchImportTask.STATUS_PROCESSING,
                BatchImportTask.STATUS_RECOGNIZING,
                BatchImportTask.STATUS_SAVING
            ).contains(task.getStatus()))
            .filter(task -> task.getUpdatedTime().isBefore(oneHourAgo))
            .count();
        
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", allTasks.size());
        result.put("statusCount", statusCount);
        result.put("typeCount", typeCount);
        result.put("todayCount", todayCount);
        result.put("stuckCount", stuckCount);
        
        return result;
    }

    @Override
    public void updateTaskStatus(String taskId, String status, String errorMessage) {
        batchImportTaskMapper.updateStatus(taskId, status, errorMessage);
        log.info("管理员更新任务状态: {} -> {}", taskId, status);
    }
}
