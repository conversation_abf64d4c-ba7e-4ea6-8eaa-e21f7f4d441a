package cn.gson.financial.admin.aspect;

import cn.gson.financial.admin.model.vo.AdminUserVo;
import cn.gson.financial.kernel.model.entity.AdminOperationLog;
import cn.gson.financial.kernel.model.mapper.AdminOperationLogMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;

/**
 * 管理员操作日志切面
 */
@Aspect
@Component
public class AdminOperationLogAspect {
    
    @Autowired
    private AdminOperationLogMapper adminOperationLogMapper;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 定义切点：所有管理员Controller的方法
     */
    @Pointcut("execution(* cn.gson.financial.admin.controller..*(..))")
    public void adminControllerMethods() {}
    
    /**
     * 环绕通知：记录操作日志
     */
    @Around("adminControllerMethods()")
    public Object logOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }
        
        HttpServletRequest request = attributes.getRequest();
        HttpSession session = request.getSession(false);
        
        // 获取当前管理员信息
        AdminUserVo adminUser = null;
        if (session != null) {
            adminUser = (AdminUserVo) session.getAttribute("adminUser");
        }
        
        if (adminUser == null) {
            return joinPoint.proceed();
        }
        
        // 创建操作日志对象
        AdminOperationLog operationLog = new AdminOperationLog();
        operationLog.setAdminUserId(adminUser.getId());
        operationLog.setAdminUsername(adminUser.getUsername());
        operationLog.setRequestMethod(request.getMethod());
        operationLog.setRequestUrl(request.getRequestURI());
        operationLog.setIpAddress(getClientIp(request));
        operationLog.setUserAgent(request.getHeader("User-Agent"));
        
        // 解析操作类型和模块
        parseOperationInfo(joinPoint, operationLog);
        
        // 记录请求参数
        try {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                // 过滤敏感参数（如HttpServletRequest等）
                Object[] filteredArgs = filterSensitiveArgs(args);
                operationLog.setRequestParams(objectMapper.writeValueAsString(filteredArgs));
            }
        } catch (Exception e) {
            operationLog.setRequestParams("参数序列化失败: " + e.getMessage());
        }
        
        Object result = null;
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            
            // 记录成功响应
            operationLog.setStatus(true);
            try {
                operationLog.setResponseData(objectMapper.writeValueAsString(result));
            } catch (Exception e) {
                operationLog.setResponseData("响应序列化失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            // 记录异常信息
            operationLog.setStatus(false);
            operationLog.setErrorMessage(e.getMessage());
            throw e;
        } finally {
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            operationLog.setExecutionTime((int) executionTime);
            operationLog.setCreateTime(LocalDateTime.now());
            
            // 保存操作日志
            try {
                adminOperationLogMapper.insert(operationLog);
            } catch (Exception e) {
                // 日志记录失败不应该影响业务逻辑
                e.printStackTrace();
            }
        }
        
        return result;
    }
    
    /**
     * 异常通知：记录异常操作
     */
    @AfterThrowing(pointcut = "adminControllerMethods()", throwing = "ex")
    public void logException(JoinPoint joinPoint, Exception ex) {
        // 异常已在Around通知中处理
    }
    
    /**
     * 解析操作类型和模块
     */
    private void parseOperationInfo(ProceedingJoinPoint joinPoint, AdminOperationLog operationLog) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        // 根据Controller类名解析模块
        if (className.contains("User")) {
            operationLog.setOperationModule("用户管理");
        } else if (className.contains("Account")) {
            operationLog.setOperationModule("账套管理");
        } else if (className.contains("AiConfig")) {
            operationLog.setOperationModule("AI配置管理");
        } else if (className.contains("Template")) {
            operationLog.setOperationModule("模板管理");
        } else if (className.contains("System")) {
            operationLog.setOperationModule("系统管理");
        } else if (className.contains("Auth")) {
            operationLog.setOperationModule("认证管理");
        } else {
            operationLog.setOperationModule("其他");
        }
        
        // 根据方法名解析操作类型
        if (methodName.contains("create") || methodName.contains("add")) {
            operationLog.setOperationType("创建");
        } else if (methodName.contains("update") || methodName.contains("edit") || methodName.contains("modify")) {
            operationLog.setOperationType("编辑");
        } else if (methodName.contains("delete") || methodName.contains("remove")) {
            operationLog.setOperationType("删除");
        } else if (methodName.contains("get") || methodName.contains("list") || methodName.contains("view")) {
            operationLog.setOperationType("查看");
        } else if (methodName.contains("login")) {
            operationLog.setOperationType("登录");
        } else if (methodName.contains("logout")) {
            operationLog.setOperationType("登出");
        } else if (methodName.contains("reset")) {
            operationLog.setOperationType("重置");
        } else if (methodName.contains("test")) {
            operationLog.setOperationType("测试");
        } else if (methodName.contains("export")) {
            operationLog.setOperationType("导出");
        } else if (methodName.contains("import")) {
            operationLog.setOperationType("导入");
        } else {
            operationLog.setOperationType("其他");
        }
        
        // 生成操作描述
        operationLog.setOperationDesc(String.format("%s - %s", operationLog.getOperationType(), methodName));
    }
    
    /**
     * 过滤敏感参数
     */
    private Object[] filterSensitiveArgs(Object[] args) {
        Object[] filtered = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg instanceof HttpServletRequest || 
                arg instanceof HttpSession ||
                arg.getClass().getName().contains("servlet")) {
                filtered[i] = "[FILTERED]";
            } else {
                filtered[i] = arg;
            }
        }
        return filtered;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
