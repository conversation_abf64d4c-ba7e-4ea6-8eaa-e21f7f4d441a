package cn.gson.financial.admin.controller;

import cn.gson.financial.admin.annotation.AdminLogin;
import cn.gson.financial.kernel.common.Result;
import cn.gson.financial.kernel.model.entity.User;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.mapper.UserMapper;
import cn.gson.financial.kernel.model.mapper.AccountSetsMapper;
import cn.gson.financial.kernel.service.FinancialAiConfigService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 管理员端 - AI配置管理控制器
 */
@Api(tags = "管理员端-AI配置管理")
@RestController
@RequestMapping("/admin/ai-config")
@CrossOrigin
@AdminLogin
@Slf4j
public class AdminAiConfigController {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private AccountSetsMapper accountSetsMapper;
    
    @Autowired
    private FinancialAiConfigService financialAiConfigService;

    @Autowired(required = false)
    private cn.gson.financial.kernel.service.AiService aiService;

    @ApiOperation("获取所有用户的AI配置")
    @GetMapping("/list")
    public Result<Map<String, Object>> getAiConfigList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer accountSetsId,
            @RequestParam(required = false) Boolean enabled) {
        try {
            Page<User> pageObj = new Page<>(page, size);
            
            LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
            
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.and(w -> w.like(User::getRealName, keyword.trim())
                    .or().like(User::getNickname, keyword.trim())
                    .or().like(User::getEmail, keyword.trim()));
            }
            
            if (accountSetsId != null) {
                wrapper.eq(User::getAccountSetsId, accountSetsId);
            }
            
            wrapper.orderByDesc(User::getCreateDate);
            
            Page<User> result = (Page<User>) userMapper.selectPage(pageObj, wrapper);
            
            // 为每个用户添加AI配置信息
            List<Map<String, Object>> configList = new ArrayList<>();
            for (User user : result.getRecords()) {
                Map<String, Object> userConfig = new HashMap<>();
                userConfig.put("userId", user.getId());
                userConfig.put("realName", user.getRealName());
                userConfig.put("nickname", user.getNickname());
                userConfig.put("email", user.getEmail());
                userConfig.put("mobile", user.getMobile());
                userConfig.put("accountSetsId", user.getAccountSetsId());
                
                // 获取账套信息
                if (user.getAccountSetsId() != null) {
                    AccountSets accountSets = accountSetsMapper.selectById(user.getAccountSetsId());
                    if (accountSets != null) {
                        userConfig.put("accountSetsName", accountSets.getCompanyName());
                    }
                }
                
                // 获取AI配置信息
                Map<String, String> aiConfig = financialAiConfigService.getAiConfigByUser(user.getId());
                boolean aiEnabled = financialAiConfigService.isAiEnabledByUser(user.getId());
                
                userConfig.put("enabled", aiEnabled);
                userConfig.put("baseUrl", aiConfig.get("base_url"));
                userConfig.put("apiKey", maskApiKey(aiConfig.get("api_key")));
                userConfig.put("defaultModel", aiConfig.get("default_model"));
                
                // 计算配置完整性
                int completeness = calculateCompleteness(aiConfig);
                userConfig.put("configCompleteness", completeness);
                
                // 如果有启用状态过滤，应用过滤
                if (enabled == null || enabled.equals(aiEnabled)) {
                    configList.add(userConfig);
                }
            }
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", configList);
            data.put("total", result.getTotal());
            data.put("current", result.getCurrent());
            data.put("size", result.getSize());
            data.put("pages", result.getPages());
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取AI配置列表失败", e);
            return Result.error("获取AI配置列表失败: " + e.getMessage());
        }
    }

    @ApiOperation("为用户设置AI配置")
    @PostMapping("/user/{userId}")
    public Result<?> saveUserAiConfig(@PathVariable Integer userId, @RequestBody Map<String, Object> configData) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            Map<String, String> configMap = new HashMap<>();
            
            if (configData.containsKey("baseUrl")) {
                configMap.put("base_url", (String) configData.get("baseUrl"));
            }
            
            if (configData.containsKey("apiKey")) {
                String apiKey = (String) configData.get("apiKey");
                // 如果API密钥不是掩码，则保存
                if (apiKey != null && !apiKey.startsWith("***")) {
                    configMap.put("api_key", apiKey);
                }
            }
            
            if (configData.containsKey("defaultModel")) {
                configMap.put("default_model", (String) configData.get("defaultModel"));
            }
            
            if (configData.containsKey("enabled")) {
                configMap.put("enabled", configData.get("enabled").toString());
            }
            
            boolean result = financialAiConfigService.saveAiConfigByUser(userId, configMap);
            if (result) {
                return Result.success("AI配置保存成功");
            } else {
                return Result.error("AI配置保存失败");
            }
        } catch (Exception e) {
            log.error("保存用户AI配置失败", e);
            return Result.error("保存用户AI配置失败: " + e.getMessage());
        }
    }

    @ApiOperation("切换用户AI状态")
    @PostMapping("/user/{userId}/toggle-status")
    public Result<?> toggleUserAiStatus(@PathVariable Integer userId, @RequestBody Map<String, Object> data) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            Boolean enabled = (Boolean) data.get("enabled");
            boolean result = financialAiConfigService.setConfigValueByUser(userId, "enabled", enabled.toString());
            
            if (result) {
                return Result.success("AI状态切换成功");
            } else {
                return Result.error("AI状态切换失败");
            }
        } catch (Exception e) {
            log.error("切换用户AI状态失败", e);
            return Result.error("切换用户AI状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("测试用户AI配置")
    @PostMapping("/user/{userId}/test")
    public Result<?> testUserAiConfig(@PathVariable Integer userId, @RequestBody Map<String, Object> testData) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            String baseUrl = (String) testData.get("base_url");
            String apiKey = (String) testData.get("api_key");
            String model = (String) testData.get("default_model");
            
            if (baseUrl == null || baseUrl.trim().isEmpty()) {
                return Result.error("API地址不能为空");
            }
            
            if (apiKey == null || apiKey.trim().isEmpty()) {
                return Result.error("API密钥不能为空");
            }
            
            // 调用AI服务进行实际测试
            if (aiService != null) {
                // 临时设置用户配置进行测试
                financialAiConfigService.setConfigValueByUser(userId, "base_url", baseUrl);
                financialAiConfigService.setConfigValueByUser(userId, "api_key", apiKey);
                if (model != null) {
                    financialAiConfigService.setConfigValueByUser(userId, "default_model", model);
                }
                financialAiConfigService.setConfigValueByUser(userId, "enabled", "true");

                try {
                    String response = aiService.chat("Hello, this is a test message.", userId);
                    if (response != null && !response.trim().isEmpty()) {
                        return Result.success("AI配置测试成功，响应: " + response.substring(0, Math.min(100, response.length())));
                    } else {
                        return Result.error("AI服务无响应");
                    }
                } catch (Exception e) {
                    return Result.error("AI服务调用失败: " + e.getMessage());
                }
            } else {
                return Result.error("AI服务未初始化");
            }
        } catch (Exception e) {
            log.error("测试用户AI配置失败", e);
            return Result.error("测试用户AI配置失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取用户可用模型列表")
    @PostMapping("/user/{userId}/models")
    public Result<?> getUserAvailableModels(@PathVariable Integer userId, @RequestBody Map<String, Object> configData) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            String baseUrl = (String) configData.get("baseUrl");
            String apiKey = (String) configData.get("apiKey");

            if (baseUrl == null || apiKey == null) {
                return Result.error("API地址和密钥不能为空");
            }

            // 临时设置用户配置
            financialAiConfigService.setConfigValueByUser(userId, "base_url", baseUrl);
            financialAiConfigService.setConfigValueByUser(userId, "api_key", apiKey);
            financialAiConfigService.setConfigValueByUser(userId, "enabled", "true");

            if (aiService != null) {
                try {
                    List<Map<String, Object>> models = aiService.getAvailableModels(userId);
                    return Result.success(models);
                } catch (Exception e) {
                    log.error("获取用户模型列表失败", e);
                    return Result.error("获取模型列表失败: " + e.getMessage());
                }
            } else {
                return Result.error("AI服务未初始化");
            }
        } catch (Exception e) {
            log.error("获取用户可用模型失败", e);
            return Result.error("获取用户可用模型失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取预设配置模板")
    @GetMapping("/templates")
    public Result<?> getConfigTemplates() {
        try {
            List<Map<String, Object>> templates = new ArrayList<>();

            // DeepSeek 配置模板
            Map<String, Object> deepseekTemplate = new HashMap<>();
            deepseekTemplate.put("id", "deepseek");
            deepseekTemplate.put("name", "DeepSeek");
            deepseekTemplate.put("description", "DeepSeek AI 服务配置");
            deepseekTemplate.put("baseUrl", "https://api.deepseek.com/v1");
            deepseekTemplate.put("defaultModel", "deepseek-chat");
            deepseekTemplate.put("icon", "🧠");
            templates.add(deepseekTemplate);

            // 行业智能体配置模板
            Map<String, Object> industryTemplate = new HashMap<>();
            industryTemplate.put("id", "industry");
            industryTemplate.put("name", "行业智能体");
            industryTemplate.put("description", "阿里云行业智能体服务配置");
            industryTemplate.put("baseUrl", "https://dashscope.aliyuncs.com/compatible-mode/v1");
            industryTemplate.put("defaultModel", "qwen-turbo");
            industryTemplate.put("icon", "🏭");
            templates.add(industryTemplate);

            // OpenAI 配置模板
            Map<String, Object> openaiTemplate = new HashMap<>();
            openaiTemplate.put("id", "openai");
            openaiTemplate.put("name", "公司专属智能体");
            openaiTemplate.put("description", "OpenAI 兼容服务配置");
            openaiTemplate.put("baseUrl", "https://api.openai.com/v1");
            openaiTemplate.put("defaultModel", "gpt-3.5-turbo");
            openaiTemplate.put("icon", "🤖");
            templates.add(openaiTemplate);

            return Result.success(templates);
        } catch (Exception e) {
            log.error("获取配置模板失败", e);
            return Result.error("获取配置模板失败: " + e.getMessage());
        }
    }

    /**
     * 掩码API密钥
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "***";
        }
        return apiKey.substring(0, 4) + "***" + apiKey.substring(apiKey.length() - 4);
    }

    /**
     * 计算配置完整性
     */
    private int calculateCompleteness(Map<String, String> config) {
        int total = 3; // base_url, api_key, default_model
        int completed = 0;
        
        if (config.get("base_url") != null && !config.get("base_url").trim().isEmpty()) {
            completed++;
        }
        if (config.get("api_key") != null && !config.get("api_key").trim().isEmpty()) {
            completed++;
        }
        if (config.get("default_model") != null && !config.get("default_model").trim().isEmpty()) {
            completed++;
        }
        
        return (int) Math.round((double) completed / total * 100);
    }
}
