package cn.gson.financial.admin.controller;

import cn.gson.financial.admin.service.AdminAuthService;
import cn.gson.financial.kernel.common.Result;
import cn.gson.financial.admin.model.dto.AdminLoginDto;
import cn.gson.financial.admin.model.vo.AdminUserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统管理员认证控制器
 */
@Api(tags = "系统管理员认证")
@RestController
@RequestMapping("/admin/auth")
@CrossOrigin
public class AdminAuthController {

    @Autowired
    private AdminAuthService adminAuthService;

    @ApiOperation("管理员登录")
    @PostMapping("/login")
    public Result<AdminUserVo> login(@RequestBody AdminLoginDto loginDto, HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            AdminUserVo adminUser = adminAuthService.login(loginDto.getUsername(), loginDto.getPassword(), clientIp);
            
            // 将管理员信息存储到session
            HttpSession session = request.getSession();
            session.setAttribute("adminUser", adminUser);
            session.setMaxInactiveInterval(7200); // 2小时超时
            
            return Result.success(adminUser);
        } catch (Exception e) {
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    @ApiOperation("管理员登出")
    @PostMapping("/logout")
    public Result<?> logout(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                session.invalidate();
            }
            return Result.success("登出成功");
        } catch (Exception e) {
            return Result.error("登出失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取当前管理员信息")
    @GetMapping("/current")
    public Result<AdminUserVo> getCurrentAdmin(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession(false);
            if (session == null) {
                return Result.error("未登录");
            }
            
            AdminUserVo adminUser = (AdminUserVo) session.getAttribute("adminUser");
            if (adminUser == null) {
                return Result.error("会话已过期");
            }
            
            return Result.success(adminUser);
        } catch (Exception e) {
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }

    @ApiOperation("修改密码")
    @PostMapping("/change-password")
    public Result<?> changePassword(@RequestBody Map<String, String> params, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession(false);
            if (session == null) {
                return Result.error("未登录");
            }

            AdminUserVo adminUser = (AdminUserVo) session.getAttribute("adminUser");
            if (adminUser == null) {
                return Result.error("会话已过期");
            }

            String oldPassword = params.get("oldPassword");
            String newPassword = params.get("newPassword");

            adminAuthService.changePassword(adminUser.getId(), oldPassword, newPassword);
            return Result.success("密码修改成功");
        } catch (Exception e) {
            return Result.error("密码修改失败: " + e.getMessage());
        }
    }

    @ApiOperation("更新个人资料")
    @PostMapping("/update-profile")
    public Result<?> updateProfile(@RequestBody Map<String, String> params, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession(false);
            if (session == null) {
                return Result.error("未登录");
            }

            AdminUserVo adminUser = (AdminUserVo) session.getAttribute("adminUser");
            if (adminUser == null) {
                return Result.error("会话已过期");
            }

            String realName = params.get("realName");
            String email = params.get("email");
            String mobile = params.get("mobile");

            adminAuthService.updateProfile(adminUser.getId(), realName, email, mobile);

            // 更新session中的用户信息
            adminUser.setRealName(realName);
            adminUser.setEmail(email);
            adminUser.setMobile(mobile);
            session.setAttribute("adminUser", adminUser);

            return Result.success("个人资料更新成功");
        } catch (Exception e) {
            return Result.error("个人资料更新失败: " + e.getMessage());
        }
    }

    @ApiOperation("检查会话状态")
    @GetMapping("/check-session")
    public Result<Map<String, Object>> checkSession(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession(false);
            boolean isValid = session != null && session.getAttribute("adminUser") != null;
            
            Map<String, Object> result = new HashMap<>();
            result.put("isValid", isValid);
            result.put("sessionId", session != null ? session.getId() : null);
            result.put("maxInactiveInterval", session != null ? session.getMaxInactiveInterval() : 0);
            
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("检查会话失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
