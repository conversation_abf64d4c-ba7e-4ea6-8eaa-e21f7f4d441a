package cn.gson.financial.admin.controller;

import cn.gson.financial.admin.annotation.AdminLogin;
import cn.gson.financial.kernel.common.Result;
import cn.gson.financial.kernel.model.entity.FieldMappingTemplate;
import cn.gson.financial.kernel.model.mapper.FieldMappingTemplateMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员端 - 字段映射模板管理控制器
 */
@Api(tags = "管理员端-字段映射模板管理")
@RestController
@RequestMapping("/admin/field-mapping-template")
@CrossOrigin
@AdminLogin
@Slf4j
public class AdminFieldMappingTemplateController {

    @Autowired
    private FieldMappingTemplateMapper templateMapper;

    @ApiOperation("获取字段映射模板列表")
    @GetMapping("/list")
    public Result<Map<String, Object>> getTemplateList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String templateType,
            @RequestParam(required = false) Boolean isActive) {
        try {
            Page<FieldMappingTemplate> pageObj = new Page<>(page, size);
            
            LambdaQueryWrapper<FieldMappingTemplate> wrapper = new LambdaQueryWrapper<>();
            
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.and(w -> w.like(FieldMappingTemplate::getTemplateName, keyword.trim())
                    .or().like(FieldMappingTemplate::getBankIdentifier, keyword.trim()));
            }

            if (templateType != null && !templateType.trim().isEmpty()) {
                wrapper.eq(FieldMappingTemplate::getDocumentType, templateType);
            }
            
            if (isActive != null) {
                wrapper.eq(FieldMappingTemplate::getIsActive, isActive);
            }
            
            wrapper.orderByDesc(FieldMappingTemplate::getCreatedTime);
            
            Page<FieldMappingTemplate> result = (Page<FieldMappingTemplate>) templateMapper.selectPage(pageObj, wrapper);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", result.getRecords());
            data.put("total", result.getTotal());
            data.put("current", result.getCurrent());
            data.put("size", result.getSize());
            data.put("pages", result.getPages());
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取字段映射模板列表失败", e);
            return Result.error("获取字段映射模板列表失败: " + e.getMessage());
        }
    }

    @ApiOperation("创建字段映射模板")
    @PostMapping
    public Result<?> createTemplate(@RequestBody FieldMappingTemplate template) {
        try {
            template.setCreatedTime(LocalDateTime.now());
            template.setUpdatedTime(LocalDateTime.now());
            template.setIsActive(true);
            
            int result = templateMapper.insert(template);
            if (result > 0) {
                return Result.success("字段映射模板创建成功");
            } else {
                return Result.error("字段映射模板创建失败");
            }
        } catch (Exception e) {
            log.error("创建字段映射模板失败", e);
            return Result.error("创建字段映射模板失败: " + e.getMessage());
        }
    }

    @ApiOperation("更新字段映射模板")
    @PutMapping("/{id}")
    public Result<?> updateTemplate(@PathVariable Integer id, @RequestBody FieldMappingTemplate template) {
        try {
            FieldMappingTemplate existingTemplate = templateMapper.selectById(id);
            if (existingTemplate == null) {
                return Result.error("字段映射模板不存在");
            }
            
            template.setId(id);
            template.setUpdatedTime(LocalDateTime.now());
            
            int result = templateMapper.updateById(template);
            if (result > 0) {
                return Result.success("字段映射模板更新成功");
            } else {
                return Result.error("字段映射模板更新失败");
            }
        } catch (Exception e) {
            log.error("更新字段映射模板失败", e);
            return Result.error("更新字段映射模板失败: " + e.getMessage());
        }
    }

    @ApiOperation("删除字段映射模板")
    @DeleteMapping("/{id}")
    public Result<?> deleteTemplate(@PathVariable Integer id) {
        try {
            FieldMappingTemplate template = templateMapper.selectById(id);
            if (template == null) {
                return Result.error("字段映射模板不存在");
            }
            
            int result = templateMapper.deleteById(id);
            if (result > 0) {
                return Result.success("字段映射模板删除成功");
            } else {
                return Result.error("字段映射模板删除失败");
            }
        } catch (Exception e) {
            log.error("删除字段映射模板失败", e);
            return Result.error("删除字段映射模板失败: " + e.getMessage());
        }
    }

    @ApiOperation("切换模板状态")
    @PutMapping("/{id}/toggle-status")
    public Result<?> toggleTemplateStatus(@PathVariable Integer id) {
        try {
            FieldMappingTemplate template = templateMapper.selectById(id);
            if (template == null) {
                return Result.error("字段映射模板不存在");
            }
            
            template.setIsActive(!template.getIsActive());
            template.setUpdatedTime(LocalDateTime.now());
            
            int result = templateMapper.updateById(template);
            if (result > 0) {
                String action = template.getIsActive() ? "启用" : "禁用";
                return Result.success("字段映射模板" + action + "成功");
            } else {
                return Result.error("字段映射模板状态切换失败");
            }
        } catch (Exception e) {
            log.error("切换字段映射模板状态失败", e);
            return Result.error("切换字段映射模板状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取模板详情")
    @GetMapping("/{id}")
    public Result<FieldMappingTemplate> getTemplateDetail(@PathVariable Integer id) {
        try {
            FieldMappingTemplate template = templateMapper.selectById(id);
            if (template == null) {
                return Result.error("字段映射模板不存在");
            }
            return Result.success(template);
        } catch (Exception e) {
            log.error("获取字段映射模板详情失败", e);
            return Result.error("获取字段映射模板详情失败: " + e.getMessage());
        }
    }

    @ApiOperation("测试模板")
    @PostMapping("/{id}/test")
    public Result<?> testTemplate(@PathVariable Integer id, @RequestBody Map<String, Object> testData) {
        try {
            FieldMappingTemplate template = templateMapper.selectById(id);
            if (template == null) {
                return Result.error("字段映射模板不存在");
            }
            
            // 这里可以添加实际的模板测试逻辑
            // 暂时返回成功
            return Result.success("字段映射模板测试成功");
        } catch (Exception e) {
            log.error("测试字段映射模板失败", e);
            return Result.error("测试字段映射模板失败: " + e.getMessage());
        }
    }
}
