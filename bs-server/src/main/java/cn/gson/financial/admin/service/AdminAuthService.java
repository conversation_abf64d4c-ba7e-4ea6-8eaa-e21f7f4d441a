package cn.gson.financial.admin.service;

import cn.gson.financial.admin.model.vo.AdminUserVo;

/**
 * 管理员认证服务接口
 */
public interface AdminAuthService {
    
    /**
     * 管理员登录
     * @param username 用户名
     * @param password 密码
     * @param clientIp 客户端IP
     * @return 管理员用户信息
     */
    AdminUserVo login(String username, String password, String clientIp);
    
    /**
     * 管理员登出
     */
    void logout();
    
    /**
     * 获取当前管理员信息
     * @return 管理员用户信息
     */
    AdminUserVo getCurrentUser();
    
    /**
     * 修改密码
     * @param adminUserId 管理员用户ID
     * @param oldPassword 原密码
     * @param newPassword 新密码
     */
    void changePassword(Integer adminUserId, String oldPassword, String newPassword);

    /**
     * 更新个人资料
     * @param adminUserId 管理员用户ID
     * @param realName 真实姓名
     * @param email 邮箱
     * @param mobile 手机号
     */
    void updateProfile(Integer adminUserId, String realName, String email, String mobile);
}
