package cn.gson.financial.admin.controller;

import cn.gson.financial.admin.annotation.AdminPermission;
import cn.gson.financial.admin.annotation.AdminLogin;
import cn.gson.financial.admin.service.AdminUserManagementService;
import cn.gson.financial.kernel.common.Result;
import cn.gson.financial.kernel.model.entity.User;
import cn.gson.financial.kernel.model.vo.UserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map;

/**
 * 系统管理员 - 用户管理控制器
 * 管理现有系统的用户账号
 */
@Api(tags = "系统管理员-用户管理")
@RestController
@RequestMapping("/admin/users")
@CrossOrigin
@AdminLogin
public class AdminUserManagementController {

    @Autowired
    private AdminUserManagementService adminUserManagementService;

    @ApiOperation("获取用户列表")
    @GetMapping("/list")
    @AdminPermission("user:view")
    public Result<Map<String, Object>> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Integer accountSetsId) {
        try {
            List<UserVo> users = adminUserManagementService.getUserList(page, size, keyword, role, accountSetsId);

            // 构建分页响应格式
            Map<String, Object> response = new HashMap<>();
            response.put("list", users);
            response.put("total", users.size()); // 注意：这里需要实际的总数，暂时用list大小
            response.put("current", page);
            response.put("size", size);
            response.put("pages", (int) Math.ceil((double) users.size() / size));

            return Result.success(response);
        } catch (Exception e) {
            return Result.error("获取用户列表失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取用户详情")
    @GetMapping("/{userId}")
    @AdminPermission("user:view")
    public Result<UserVo> getUserDetail(@PathVariable Integer userId) {
        try {
            UserVo user = adminUserManagementService.getUserDetail(userId);
            return Result.success(user);
        } catch (Exception e) {
            return Result.error("获取用户详情失败: " + e.getMessage());
        }
    }

    @ApiOperation("创建用户")
    @PostMapping("/create")
    @AdminPermission("user:create")
    public Result<User> createUser(@RequestBody Map<String, Object> userData) {
        try {
            User user = adminUserManagementService.createUser(userData);
            return Result.success(user);
        } catch (Exception e) {
            return Result.error("创建用户失败: " + e.getMessage());
        }
    }

    @ApiOperation("更新用户信息")
    @PutMapping("/{userId}")
    @AdminPermission("user:edit")
    public Result<?> updateUser(@PathVariable Integer userId, @RequestBody Map<String, Object> userData) {
        try {
            adminUserManagementService.updateUser(userId, userData);
            return Result.success("用户信息更新成功");
        } catch (Exception e) {
            return Result.error("更新用户信息失败: " + e.getMessage());
        }
    }

    @ApiOperation("删除用户")
    @DeleteMapping("/{userId}")
    @AdminPermission("user:delete")
    public Result<?> deleteUser(@PathVariable Integer userId) {
        try {
            adminUserManagementService.deleteUser(userId);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            return Result.error("删除用户失败: " + e.getMessage());
        }
    }

    @ApiOperation("重置用户密码")
    @PostMapping("/{userId}/reset-password")
    @AdminPermission("user:reset_password")
    public Result<String> resetPassword(@PathVariable Integer userId) {
        try {
            String newPassword = adminUserManagementService.resetPassword(userId);
            return Result.success(newPassword);
        } catch (Exception e) {
            return Result.error("重置密码失败: " + e.getMessage());
        }
    }

    @ApiOperation("启用/禁用用户")
    @PostMapping("/{userId}/toggle-status")
    @AdminPermission("user:edit")
    public Result<?> toggleUserStatus(@PathVariable Integer userId) {
        try {
            adminUserManagementService.toggleUserStatus(userId);
            return Result.success("用户状态更新成功");
        } catch (Exception e) {
            return Result.error("更新用户状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取用户的账套关联")
    @GetMapping("/{userId}/account-sets")
    @AdminPermission("user:view")
    public Result<List<Map<String, Object>>> getUserAccountSets(@PathVariable Integer userId) {
        try {
            List<Map<String, Object>> accountSets = adminUserManagementService.getUserAccountSets(userId);
            return Result.success(accountSets);
        } catch (Exception e) {
            return Result.error("获取用户账套关联失败: " + e.getMessage());
        }
    }

    @ApiOperation("为用户添加账套权限")
    @PostMapping("/{userId}/account-sets")
    @AdminPermission("user:edit")
    public Result<?> addUserToAccountSet(@PathVariable Integer userId, @RequestBody Map<String, Object> data) {
        try {
            Integer accountSetsId = (Integer) data.get("accountSetsId");
            String role = (String) data.get("role");
            adminUserManagementService.addUserToAccountSet(userId, accountSetsId, role);
            return Result.success("账套权限添加成功");
        } catch (Exception e) {
            return Result.error("添加账套权限失败: " + e.getMessage());
        }
    }

    @ApiOperation("移除用户的账套权限")
    @DeleteMapping("/{userId}/account-sets/{accountSetsId}")
    @AdminPermission("user:edit")
    public Result<?> removeUserFromAccountSet(@PathVariable Integer userId, @PathVariable Integer accountSetsId) {
        try {
            adminUserManagementService.removeUserFromAccountSet(userId, accountSetsId);
            return Result.success("账套权限移除成功");
        } catch (Exception e) {
            return Result.error("移除账套权限失败: " + e.getMessage());
        }
    }

    @ApiOperation("批量操作用户")
    @PostMapping("/batch")
    @AdminPermission("user:edit")
    public Result<?> batchOperation(@RequestBody Map<String, Object> data) {
        try {
            String operation = (String) data.get("operation");
            @SuppressWarnings("unchecked")
            List<Integer> userIds = (List<Integer>) data.get("userIds");
            
            adminUserManagementService.batchOperation(operation, userIds, data);
            return Result.success("批量操作成功");
        } catch (Exception e) {
            return Result.error("批量操作失败: " + e.getMessage());
        }
    }
}
