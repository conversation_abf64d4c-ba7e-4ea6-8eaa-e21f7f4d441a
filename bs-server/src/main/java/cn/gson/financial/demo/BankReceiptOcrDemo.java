package cn.gson.financial.demo;

import java.util.HashMap;
import java.util.Map;

/**
 * 银行回单OCR格式化演示
 * 展示银行回单使用SmartFieldMappingService的统一OCR格式化功能
 */
public class BankReceiptOcrDemo {

    public static void main(String[] args) {
        String separator = "================================================================================";
        System.out.println(separator);
        System.out.println("银行回单OCR格式化统一处理演示");
        System.out.println(separator);
        
        // 演示银行回单OCR数据处理
        demonstrateBankReceiptOcrProcessing();
    }

    /**
     * 演示银行回单OCR数据处理
     */
    private static void demonstrateBankReceiptOcrProcessing() {
        System.out.println("【银行回单OCR数据处理演示】");
        System.out.println();
        
        // 模拟从OcrService获取的银行回单OCR数据
        Map<String, Object> ocrData = new HashMap<>();
        
        // 原始OCR数据
        Map<String, String> rawOcrData = new HashMap<>();
        rawOcrData.put("回单名称", "电子银行转账回单");
        rawOcrData.put("流水号", "*****************");
        rawOcrData.put("回单日期", "2025年07月13日");
        rawOcrData.put("转账日期", "2025年07月13日");
        rawOcrData.put("金额", "￥1,000.00");
        rawOcrData.put("金额大写", "壹仟元整");
        rawOcrData.put("收支", "支出");
        rawOcrData.put("付款人姓名", "张三");
        rawOcrData.put("付款人账号", "6222021234567890123");
        rawOcrData.put("付款人开户行", "中国工商银行北京分行");
        rawOcrData.put("收款人姓名", "李四");
        rawOcrData.put("收款人账号", "6228481234567890123");
        rawOcrData.put("收款人开户行", "中国建设银行上海分行");
        rawOcrData.put("摘要", "货款");
        rawOcrData.put("交易机构", "网上银行");
        rawOcrData.put("附言", "请查收");
        
        ocrData.put("rawOcrData", rawOcrData);
        
        // 模拟一些已映射的字段
        ocrData.put("amount", 1000.0);
        ocrData.put("serial_number", "*****************");
        ocrData.put("payer_name", "张三");
        ocrData.put("payee_name", "李四");
        ocrData.put("summary", "货款");
        ocrData.put("type", "支出");
        
        System.out.println("原始OCR数据包含字段数: " + rawOcrData.size());
        System.out.println("已映射字段数: " + (ocrData.size() - 1)); // 减去rawOcrData本身
        System.out.println();
        
        // 模拟SmartFieldMappingService的处理
        Map<String, Object> enhancedData = enhanceOcrDataWithRawInfo(ocrData);

        System.out.println("经过SmartFieldMappingService处理后的数据:");
        System.out.println("总字段数: " + enhancedData.size());
        System.out.println();

        // 显示OCR原始信息（易读格式）
        if (enhancedData.containsKey("ocrRecognitionInfo")) {
            System.out.println("OCR原始信息（易读格式）:");
            System.out.println(enhancedData.get("ocrRecognitionInfo"));
        }

        String separator = "================================================================================";
        System.out.println("\n" + separator);
        System.out.println("演示完成！");
        System.out.println("银行回单现在使用与发票相同的统一OCR格式化方法。");
        System.out.println(separator);
    }

    /**
     * 模拟SmartFieldMappingService的enhanceOcrDataWithRawInfo方法
     */
    private static Map<String, Object> enhanceOcrDataWithRawInfo(Map<String, Object> ocrData) {
        @SuppressWarnings("unchecked")
        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
        if (rawData != null) {
            // 使用统一的OCR格式化方法生成易读格式
            String readableOcrInfo = OcrFormatDemoStandalone.formatOcrDataToReadableText(rawData, "BANK_RECEIPT");
            ocrData.put("ocrRecognitionInfo", readableOcrInfo);

            // 如果没有备注信息，生成简化的备注
            if (!ocrData.containsKey("remark") || ocrData.get("remark") == null) {
                String formattedRemark = formatOcrDataForRemark(rawData);
                ocrData.put("remark", formattedRemark);
            }
        }
        return ocrData;
    }

    /**
     * 格式化OCR数据为简化备注信息
     */
    private static String formatOcrDataForRemark(Map<String, String> rawData) {
        StringBuilder remark = new StringBuilder();
        remark.append("OCR识别信息：\n");
        for (Map.Entry<String, String> entry : rawData.entrySet()) {
            remark.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }
        return remark.toString();
    }
}
