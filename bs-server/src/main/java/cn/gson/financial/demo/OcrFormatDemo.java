package cn.gson.financial.demo;

import cn.gson.financial.service.OcrService;

import java.util.HashMap;
import java.util.Map;

/**
 * OCR格式化功能演示
 * 展示新的易读格式OCR信息功能
 */
public class OcrFormatDemo {

    public static void main(String[] args) {
        OcrService ocrService = new OcrService();

        String separator = "================================================================================";
        System.out.println(separator);
        System.out.println("OCR原始信息格式化功能演示");
        System.out.println(separator);

        // 演示银行回单OCR格式化
        demonstrateBankReceiptFormatting(ocrService);

        System.out.println("\n" + separator);

        // 演示发票OCR格式化
        demonstrateInvoiceFormatting(ocrService);
    }

    /**
     * 演示银行回单OCR格式化
     */
    private static void demonstrateBankReceiptFormatting(OcrService ocrService) {
        System.out.println("【银行回单OCR格式化演示】");
        System.out.println();
        
        // 模拟银行回单OCR数据
        Map<String, String> bankReceiptData = new HashMap<>();
        bankReceiptData.put("回单名称", "电子银行转账回单");
        bankReceiptData.put("流水号", "*****************");
        bankReceiptData.put("回单日期", "2025年07月13日");
        bankReceiptData.put("转账日期", "2025年07月13日");
        bankReceiptData.put("金额", "￥1,000.00");
        bankReceiptData.put("金额大写", "壹仟元整");
        bankReceiptData.put("收支", "支出");
        bankReceiptData.put("付款人姓名", "张三");
        bankReceiptData.put("付款人账号", "622202********90123");
        bankReceiptData.put("付款人开户行", "中国工商银行北京分行");
        bankReceiptData.put("收款人姓名", "李四");
        bankReceiptData.put("收款人账号", "622848********90123");
        bankReceiptData.put("收款人开户行", "中国建设银行上海分行");
        bankReceiptData.put("摘要", "货款");
        bankReceiptData.put("交易机构", "网上银行");
        bankReceiptData.put("附言", "请查收");
        
        // 生成易读格式
        String readableText = ocrService.formatOcrDataToReadableText(bankReceiptData, "BANK_RECEIPT");
        System.out.println("易读格式输出：");
        System.out.println(readableText);
        
        // 生成JSON格式
        String jsonText = ocrService.formatOcrDataToJson(bankReceiptData);
        System.out.println("\nJSON格式输出：");
        System.out.println(jsonText);
    }

    /**
     * 演示发票OCR格式化
     */
    private static void demonstrateInvoiceFormatting(OcrService ocrService) {
        System.out.println("【增值税发票OCR格式化演示】");
        System.out.println();
        
        // 模拟增值税发票OCR数据
        Map<String, String> invoiceData = new HashMap<>();
        invoiceData.put("发票名称", "增值税专用发票");
        invoiceData.put("发票代码", "**********");
        invoiceData.put("发票号码", "********");
        invoiceData.put("开票日期", "2025年07月13日");
        invoiceData.put("销售方名称", "北京科技有限公司");
        invoiceData.put("销售方纳税人识别号", "91110000********9X");
        invoiceData.put("销售方地址电话", "北京市朝阳区xxx路xxx号 010-********");
        invoiceData.put("销售方开户行及账号", "中国银行北京分行 ********90********9");
        invoiceData.put("购买方名称", "上海贸易有限公司");
        invoiceData.put("购买方纳税人识别号", "91310000987654321Y");
        invoiceData.put("购买方地址电话", "上海市浦东新区xxx路xxx号 021-87654321");
        invoiceData.put("购买方开户行及账号", "中国工商银行上海分行 9876543210987654321");
        invoiceData.put("价税合计（小写）", "￥11,300.00");
        invoiceData.put("价税合计（大写）", "壹万壹仟叁佰元整");
        invoiceData.put("合计金额", "￥10,000.00");
        invoiceData.put("合计税额", "￥1,300.00");
        invoiceData.put("税率", "13%");
        invoiceData.put("备注", "技术服务费");
        invoiceData.put("开票人", "张三");
        invoiceData.put("复核", "李四");
        invoiceData.put("收款人", "王五");
        
        // 生成易读格式
        String readableText = ocrService.formatOcrDataToReadableText(invoiceData, "INVOICE");
        System.out.println("易读格式输出：");
        System.out.println(readableText);
        
        // 生成JSON格式
        String jsonText = ocrService.formatOcrDataToJson(invoiceData);
        System.out.println("\nJSON格式输出：");
        System.out.println(jsonText);
    }
}
