package cn.gson.financial.demo;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * OCR格式化功能独立演示
 * 展示新的易读格式OCR信息功能（不依赖外部库）
 */
public class OcrFormatDemoStandalone {

    public static void main(String[] args) {
        String separator = "================================================================================";
        System.out.println(separator);
        System.out.println("OCR原始信息格式化功能演示");
        System.out.println(separator);
        
        // 演示银行回单OCR格式化
        demonstrateBankReceiptFormatting();
        
        System.out.println("\n" + separator);
        
        // 演示发票OCR格式化
        demonstrateInvoiceFormatting();
    }

    /**
     * 演示银行回单OCR格式化
     */
    private static void demonstrateBankReceiptFormatting() {
        System.out.println("【银行回单OCR格式化演示】");
        System.out.println();
        
        // 模拟银行回单OCR数据
        Map<String, String> bankReceiptData = new HashMap<>();
        bankReceiptData.put("回单名称", "电子银行转账回单");
        bankReceiptData.put("流水号", "*****************");
        bankReceiptData.put("回单日期", "2025年07月13日");
        bankReceiptData.put("转账日期", "2025年07月13日");
        bankReceiptData.put("金额", "￥1,000.00");
        bankReceiptData.put("金额大写", "壹仟元整");
        bankReceiptData.put("收支", "支出");
        bankReceiptData.put("付款人姓名", "张三");
        bankReceiptData.put("付款人账号", "622202********90123");
        bankReceiptData.put("付款人开户行", "中国工商银行北京分行");
        bankReceiptData.put("收款人姓名", "李四");
        bankReceiptData.put("收款人账号", "622848********90123");
        bankReceiptData.put("收款人开户行", "中国建设银行上海分行");
        bankReceiptData.put("摘要", "货款");
        bankReceiptData.put("交易机构", "网上银行");
        bankReceiptData.put("附言", "请查收");
        
        // 生成易读格式
        String readableText = formatOcrDataToReadableText(bankReceiptData, "BANK_RECEIPT");
        System.out.println("易读格式输出：");
        System.out.println(readableText);
    }

    /**
     * 演示发票OCR格式化
     */
    private static void demonstrateInvoiceFormatting() {
        System.out.println("【增值税发票OCR格式化演示】");
        System.out.println();
        
        // 模拟增值税发票OCR数据
        Map<String, String> invoiceData = new HashMap<>();
        invoiceData.put("发票名称", "增值税专用发票");
        invoiceData.put("发票代码", "**********");
        invoiceData.put("发票号码", "********");
        invoiceData.put("开票日期", "2025年07月13日");
        invoiceData.put("销售方名称", "北京科技有限公司");
        invoiceData.put("销售方纳税人识别号", "91110000********9X");
        invoiceData.put("销售方地址电话", "北京市朝阳区xxx路xxx号 010-********");
        invoiceData.put("销售方开户行及账号", "中国银行北京分行 ********90********9");
        invoiceData.put("购买方名称", "上海贸易有限公司");
        invoiceData.put("购买方纳税人识别号", "91310000987654321Y");
        invoiceData.put("购买方地址电话", "上海市浦东新区xxx路xxx号 021-87654321");
        invoiceData.put("购买方开户行及账号", "中国工商银行上海分行 9876543210987654321");
        invoiceData.put("价税合计（小写）", "￥11,300.00");
        invoiceData.put("价税合计（大写）", "壹万壹仟叁佰元整");
        invoiceData.put("合计金额", "￥10,000.00");
        invoiceData.put("合计税额", "￥1,300.00");
        invoiceData.put("税率", "13%");
        invoiceData.put("备注", "技术服务费");
        invoiceData.put("开票人", "张三");
        invoiceData.put("复核", "李四");
        invoiceData.put("收款人", "王五");
        
        // 生成易读格式
        String readableText = formatOcrDataToReadableText(invoiceData, "INVOICE");
        System.out.println("易读格式输出：");
        System.out.println(readableText);
    }

    /**
     * 格式化OCR数据为易读的文本格式
     */
    public static String formatOcrDataToReadableText(Map<String, String> rawOcrData, String documentType) {
        if (rawOcrData == null || rawOcrData.isEmpty()) {
            return "暂无OCR识别信息";
        }

        StringBuilder formatted = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        // 添加头部信息
        formatted.append("=== OCR识别信息 ===\n");
        formatted.append("识别时间: ").append(dateFormat.format(new Date())).append("\n");
        formatted.append("识别来源: 腾讯云OCR\n");
        formatted.append("文档类型: ").append(getDocumentTypeName(documentType)).append("\n");
        formatted.append("识别字段数: ").append(rawOcrData.size()).append("\n\n");

        if ("BANK_RECEIPT".equals(documentType)) {
            formatBankReceiptFields(formatted, rawOcrData);
        } else if ("INVOICE".equals(documentType)) {
            formatInvoiceFields(formatted, rawOcrData);
        } else {
            formatGenericFields(formatted, rawOcrData);
        }

        return formatted.toString();
    }

    /**
     * 格式化银行回单字段
     */
    private static void formatBankReceiptFields(StringBuilder formatted, Map<String, String> rawOcrData) {
        // 基本信息
        addSectionHeader(formatted, "基本信息");
        addFieldIfExists(formatted, rawOcrData, "回单名称", "回单名称");
        addFieldIfExists(formatted, rawOcrData, "流水号", "流水号");
        addFieldIfExists(formatted, rawOcrData, "交易机构", "交易机构");
        
        // 日期信息
        addSectionHeader(formatted, "日期信息");
        addFieldIfExists(formatted, rawOcrData, "回单日期", "回单日期");
        addFieldIfExists(formatted, rawOcrData, "转账日期", "转账日期");
        
        // 金额信息
        addSectionHeader(formatted, "金额信息");
        addFieldIfExists(formatted, rawOcrData, "金额", "金额");
        addFieldIfExists(formatted, rawOcrData, "金额大写", "金额大写");
        addFieldIfExists(formatted, rawOcrData, "收支", "收支类型");
        
        // 账户信息
        addSectionHeader(formatted, "账户信息");
        addFieldIfExists(formatted, rawOcrData, "付款人姓名", "付款人");
        addFieldIfExists(formatted, rawOcrData, "付款人账号", "付款账号");
        addFieldIfExists(formatted, rawOcrData, "付款人开户行", "付款开户行");
        addFieldIfExists(formatted, rawOcrData, "收款人姓名", "收款人");
        addFieldIfExists(formatted, rawOcrData, "收款人账号", "收款账号");
        addFieldIfExists(formatted, rawOcrData, "收款人开户行", "收款开户行");
        
        // 其他信息
        addSectionHeader(formatted, "其他信息");
        addFieldIfExists(formatted, rawOcrData, "摘要", "摘要");
        addFieldIfExists(formatted, rawOcrData, "用途", "用途");
        addFieldIfExists(formatted, rawOcrData, "附言", "附言");
    }

    /**
     * 格式化发票字段
     */
    private static void formatInvoiceFields(StringBuilder formatted, Map<String, String> rawOcrData) {
        // 基本信息
        addSectionHeader(formatted, "基本信息");
        addFieldIfExists(formatted, rawOcrData, "发票名称", "发票类型");
        addFieldIfExists(formatted, rawOcrData, "发票代码", "发票代码");
        addFieldIfExists(formatted, rawOcrData, "发票号码", "发票号码");
        addFieldIfExists(formatted, rawOcrData, "开票日期", "开票日期");
        
        // 开票方信息
        addSectionHeader(formatted, "开票方信息");
        addFieldIfExists(formatted, rawOcrData, "销售方名称", "销售方名称");
        addFieldIfExists(formatted, rawOcrData, "销售方纳税人识别号", "销售方税号");
        addFieldIfExists(formatted, rawOcrData, "销售方识别号", "销售方税号");
        addFieldIfExists(formatted, rawOcrData, "销售方地址电话", "销售方地址电话");
        addFieldIfExists(formatted, rawOcrData, "销售方开户行及账号", "销售方开户行账号");
        
        // 购买方信息
        addSectionHeader(formatted, "购买方信息");
        addFieldIfExists(formatted, rawOcrData, "购买方名称", "购买方名称");
        addFieldIfExists(formatted, rawOcrData, "购买方纳税人识别号", "购买方税号");
        addFieldIfExists(formatted, rawOcrData, "购买方识别号", "购买方税号");
        addFieldIfExists(formatted, rawOcrData, "购买方地址电话", "购买方地址电话");
        addFieldIfExists(formatted, rawOcrData, "购买方开户行及账号", "购买方开户行账号");
        
        // 金额信息
        addSectionHeader(formatted, "金额信息");
        addFieldIfExists(formatted, rawOcrData, "价税合计(小写)", "价税合计");
        addFieldIfExists(formatted, rawOcrData, "价税合计（小写）", "价税合计");
        addFieldIfExists(formatted, rawOcrData, "小写金额", "价税合计");
        addFieldIfExists(formatted, rawOcrData, "价税合计(大写)", "价税合计大写");
        addFieldIfExists(formatted, rawOcrData, "价税合计（大写）", "价税合计大写");
        addFieldIfExists(formatted, rawOcrData, "合计金额", "不含税金额");
        addFieldIfExists(formatted, rawOcrData, "不含税金额", "不含税金额");
        addFieldIfExists(formatted, rawOcrData, "合计税额", "税额");
        addFieldIfExists(formatted, rawOcrData, "税额", "税额");
        addFieldIfExists(formatted, rawOcrData, "税率", "税率");
        
        // 其他信息
        addSectionHeader(formatted, "其他信息");
        addFieldIfExists(formatted, rawOcrData, "备注", "备注");
        addFieldIfExists(formatted, rawOcrData, "收款人", "收款人");
        addFieldIfExists(formatted, rawOcrData, "复核", "复核");
        addFieldIfExists(formatted, rawOcrData, "开票人", "开票人");
    }

    /**
     * 格式化通用字段
     */
    private static void formatGenericFields(StringBuilder formatted, Map<String, String> rawOcrData) {
        addSectionHeader(formatted, "识别字段");
        for (Map.Entry<String, String> entry : rawOcrData.entrySet()) {
            addField(formatted, entry.getKey(), entry.getValue());
        }
    }

    /**
     * 添加章节标题
     */
    private static void addSectionHeader(StringBuilder formatted, String title) {
        formatted.append("\n--- ").append(title).append(" ---\n");
    }

    /**
     * 添加字段（如果存在）
     */
    private static void addFieldIfExists(StringBuilder formatted, Map<String, String> data, String key, String displayName) {
        if (data.containsKey(key) && data.get(key) != null && !data.get(key).trim().isEmpty()) {
            addField(formatted, displayName, data.get(key));
        }
    }

    /**
     * 添加字段
     */
    private static void addField(StringBuilder formatted, String name, String value) {
        formatted.append(String.format("%-12s: %s\n", name, value != null ? value.trim() : ""));
    }

    /**
     * 获取文档类型名称
     */
    private static String getDocumentTypeName(String documentType) {
        switch (documentType) {
            case "BANK_RECEIPT":
                return "银行回单";
            case "INVOICE":
                return "增值税发票";
            default:
                return "未知类型";
        }
    }
}
