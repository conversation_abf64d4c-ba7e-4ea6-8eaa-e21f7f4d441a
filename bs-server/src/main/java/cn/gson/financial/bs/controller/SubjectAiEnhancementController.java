package cn.gson.financial.bs.controller;

import cn.gson.financial.kernel.model.entity.SubjectAiEnhancement;
import cn.gson.financial.kernel.service.SubjectAiEnhancementService;
import cn.gson.financial.kernel.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 科目AI增强控制器
 * 提供科目智能匹配、学习优化等AI增强功能
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
@Slf4j
@Api(tags = "科目AI增强管理")
@RestController
@RequestMapping("/api/subject-ai")
public class SubjectAiEnhancementController {

    @Autowired
    private SubjectAiEnhancementService subjectAiEnhancementService;

    @ApiOperation("智能匹配科目")
    @PostMapping("/intelligent-match")
    public Result<List<SubjectAiEnhancement>> intelligentMatch(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId,
            @ApiParam("业务描述") @RequestParam String description,
            @ApiParam("金额") @RequestParam(required = false) BigDecimal amount,
            @ApiParam("业务类型") @RequestParam(required = false) String businessType) {
        
        try {
            List<SubjectAiEnhancement> matches = subjectAiEnhancementService.intelligentMatchSubjects(
                    accountSetsId, description, amount, businessType);
            
            return Result.success(matches);
            
        } catch (Exception e) {
            log.error("智能匹配科目失败", e);
            return Result.error("智能匹配科目失败: " + e.getMessage());
        }
    }

    @ApiOperation("根据关键词搜索科目")
    @GetMapping("/search")
    public Result<List<SubjectAiEnhancement>> searchByKeyword(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId,
            @ApiParam("关键词") @RequestParam String keyword) {
        
        try {
            List<SubjectAiEnhancement> results = subjectAiEnhancementService.searchSubjectsByKeyword(
                    accountSetsId, keyword);
            
            return Result.success(results);
            
        } catch (Exception e) {
            log.error("搜索科目失败", e);
            return Result.error("搜索科目失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取使用频率最高的科目")
    @GetMapping("/top-used")
    public Result<List<SubjectAiEnhancement>> getTopUsedSubjects(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId,
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            List<SubjectAiEnhancement> results = subjectAiEnhancementService.getTopUsedSubjects(
                    accountSetsId, limit);
            
            return Result.success(results);
            
        } catch (Exception e) {
            log.error("获取热门科目失败", e);
            return Result.error("获取热门科目失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取推荐科目")
    @GetMapping("/recommended")
    public Result<List<SubjectAiEnhancement>> getRecommendedSubjects(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId,
            @ApiParam("最小置信度") @RequestParam(required = false) BigDecimal minConfidence) {
        
        try {
            List<SubjectAiEnhancement> results = subjectAiEnhancementService.getRecommendedSubjects(
                    accountSetsId, minConfidence);
            
            return Result.success(results);
            
        } catch (Exception e) {
            log.error("获取推荐科目失败", e);
            return Result.error("获取推荐科目失败: " + e.getMessage());
        }
    }

    @ApiOperation("记录科目使用情况")
    @PostMapping("/record-usage")
    public Result<Void> recordUsage(
            @ApiParam("科目ID") @RequestParam Integer subjectId,
            @ApiParam("账套ID") @RequestParam Integer accountSetsId,
            @ApiParam("匹配上下文") @RequestBody Map<String, Object> matchingContext,
            @ApiParam("是否正确") @RequestParam(required = false) Boolean isCorrect) {
        
        try {
            subjectAiEnhancementService.recordSubjectUsage(
                    subjectId, accountSetsId, matchingContext, isCorrect);
            
            return Result.success();
            
        } catch (Exception e) {
            log.error("记录科目使用情况失败", e);
            return Result.error("记录科目使用情况失败: " + e.getMessage());
        }
    }

    @ApiOperation("更新科目AI信息")
    @PutMapping("/update-ai-info")
    public Result<Void> updateAiInfo(
            @ApiParam("科目ID") @RequestParam Integer subjectId,
            @ApiParam("账套ID") @RequestParam Integer accountSetsId,
            @ApiParam("AI描述") @RequestParam String aiDescription,
            @ApiParam("AI关键词") @RequestParam String aiKeywords) {
        
        try {
            boolean success = subjectAiEnhancementService.updateSubjectAiInfo(
                    subjectId, accountSetsId, aiDescription, aiKeywords);
            
            if (success) {
                return Result.success();
            } else {
                return Result.error("更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新科目AI信息失败", e);
            return Result.error("更新科目AI信息失败: " + e.getMessage());
        }
    }

    @ApiOperation("初始化科目AI增强信息")
    @PostMapping("/initialize")
    public Result<Integer> initializeAiEnhancement(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId) {
        
        try {
            int count = subjectAiEnhancementService.initializeSubjectAiEnhancement(accountSetsId);
            return Result.success(count);
            
        } catch (Exception e) {
            log.error("初始化科目AI增强信息失败", e);
            return Result.error("初始化失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取科目AI增强信息")
    @GetMapping("/enhancement")
    public Result<SubjectAiEnhancement> getEnhancement(
            @ApiParam("科目ID") @RequestParam Integer subjectId,
            @ApiParam("账套ID") @RequestParam Integer accountSetsId) {
        
        try {
            SubjectAiEnhancement enhancement = subjectAiEnhancementService.getSubjectAiEnhancement(
                    subjectId, accountSetsId);
            
            return Result.success(enhancement);
            
        } catch (Exception e) {
            log.error("获取科目AI增强信息失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    @ApiOperation("保存或更新AI增强信息")
    @PostMapping("/save-enhancement")
    public Result<Void> saveEnhancement(@RequestBody SubjectAiEnhancement enhancement) {
        try {
            boolean success = subjectAiEnhancementService.saveOrUpdateEnhancement(enhancement);
            
            if (success) {
                return Result.success();
            } else {
                return Result.error("保存失败");
            }
            
        } catch (Exception e) {
            log.error("保存AI增强信息失败", e);
            return Result.error("保存失败: " + e.getMessage());
        }
    }

    @ApiOperation("启用或禁用AI增强")
    @PutMapping("/status")
    public Result<Void> updateStatus(
            @ApiParam("主键ID") @RequestParam Integer id,
            @ApiParam("状态") @RequestParam Integer status) {
        
        try {
            boolean success = subjectAiEnhancementService.updateStatus(id, status);
            
            if (success) {
                return Result.success();
            } else {
                return Result.error("更新状态失败");
            }
            
        } catch (Exception e) {
            log.error("更新状态失败", e);
            return Result.error("更新状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取所有启用的AI增强科目")
    @GetMapping("/enabled")
    public Result<List<SubjectAiEnhancement>> getAllEnabled(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId) {
        
        try {
            List<SubjectAiEnhancement> results = subjectAiEnhancementService.getAllEnabledSubjects(
                    accountSetsId);
            
            return Result.success(results);
            
        } catch (Exception e) {
            log.error("获取启用科目失败", e);
            return Result.error("获取启用科目失败: " + e.getMessage());
        }
    }

    @ApiOperation("分析科目使用统计")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> analyzeStatistics(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId,
            @ApiParam("开始日期") @RequestParam(required = false) String startDate,
            @ApiParam("结束日期") @RequestParam(required = false) String endDate) {
        
        try {
            Map<String, Object> statistics = subjectAiEnhancementService.analyzeSubjectUsageStatistics(
                    accountSetsId, startDate, endDate);
            
            return Result.success(statistics);
            
        } catch (Exception e) {
            log.error("分析统计失败", e);
            return Result.error("分析统计失败: " + e.getMessage());
        }
    }

    @ApiOperation("导出AI配置")
    @GetMapping("/export")
    public Result<List<Map<String, Object>>> exportConfiguration(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId) {
        
        try {
            List<Map<String, Object>> config = subjectAiEnhancementService.exportAiConfiguration(
                    accountSetsId);
            
            return Result.success(config);
            
        } catch (Exception e) {
            log.error("导出配置失败", e);
            return Result.error("导出配置失败: " + e.getMessage());
        }
    }

    @ApiOperation("导入AI配置")
    @PostMapping("/import")
    public Result<Integer> importConfiguration(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId,
            @ApiParam("配置数据") @RequestBody List<Map<String, Object>> configData) {
        
        try {
            int count = subjectAiEnhancementService.importAiConfiguration(
                    accountSetsId, configData);
            
            return Result.success(count);
            
        } catch (Exception e) {
            log.error("导入配置失败", e);
            return Result.error("导入配置失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取匹配建议")
    @PostMapping("/suggestions")
    public Result<List<Map<String, Object>>> getMatchingSuggestions(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId,
            @ApiParam("输入文本") @RequestParam String inputText,
            @ApiParam("上下文信息") @RequestBody(required = false) Map<String, Object> contextInfo) {
        
        try {
            List<Map<String, Object>> suggestions = subjectAiEnhancementService.getMatchingSuggestions(
                    accountSetsId, inputText, contextInfo);
            
            return Result.success(suggestions);
            
        } catch (Exception e) {
            log.error("获取匹配建议失败", e);
            return Result.error("获取匹配建议失败: " + e.getMessage());
        }
    }

    @ApiOperation("学习用户偏好")
    @PostMapping("/learn-preferences")
    public Result<Integer> learnUserPreferences(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId,
            @ApiParam("用户ID") @RequestParam Integer userId) {
        
        try {
            int count = subjectAiEnhancementService.learnUserPreferences(accountSetsId, userId);
            return Result.success(count);
            
        } catch (Exception e) {
            log.error("学习用户偏好失败", e);
            return Result.error("学习用户偏好失败: " + e.getMessage());
        }
    }

    @ApiOperation("优化匹配规则")
    @PostMapping("/optimize-rules")
    public Result<Integer> optimizeMatchingRules(
            @ApiParam("账套ID") @RequestParam Integer accountSetsId) {
        
        try {
            int count = subjectAiEnhancementService.optimizeMatchingRules(accountSetsId);
            return Result.success(count);
            
        } catch (Exception e) {
            log.error("优化匹配规则失败", e);
            return Result.error("优化匹配规则失败: " + e.getMessage());
        }
    }
}