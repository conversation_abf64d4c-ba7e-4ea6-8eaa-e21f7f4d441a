package cn.gson.financial.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OCR服务测试类
 * 测试OCR原始信息格式化功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class OcrServiceTest {

    @Test
    public void testFormatBankReceiptOcrData() {
        OcrService ocrService = new OcrService();
        
        // 模拟银行回单OCR数据
        Map<String, String> bankReceiptData = new HashMap<>();
        bankReceiptData.put("回单名称", "电子银行转账回单");
        bankReceiptData.put("流水号", "*****************");
        bankReceiptData.put("回单日期", "2025年07月13日");
        bankReceiptData.put("转账日期", "2025年07月13日");
        bankReceiptData.put("金额", "￥1,000.00");
        bankReceiptData.put("金额大写", "壹仟元整");
        bankReceiptData.put("收支", "支出");
        bankReceiptData.put("付款人姓名", "张三");
        bankReceiptData.put("付款人账号", "6222021234567890123");
        bankReceiptData.put("付款人开户行", "中国工商银行北京分行");
        bankReceiptData.put("收款人姓名", "李四");
        bankReceiptData.put("收款人账号", "6228481234567890123");
        bankReceiptData.put("收款人开户行", "中国建设银行上海分行");
        bankReceiptData.put("摘要", "货款");
        bankReceiptData.put("交易机构", "网上银行");
        
        // 测试易读格式化
        String readableText = ocrService.formatOcrDataToReadableText(bankReceiptData, "BANK_RECEIPT");
        
        System.out.println("银行回单易读格式:");
        System.out.println(readableText);
        
        // 验证格式化结果
        assertNotNull(readableText);
        assertTrue(readableText.contains("=== OCR识别信息 ==="));
        assertTrue(readableText.contains("--- 基本信息 ---"));
        assertTrue(readableText.contains("--- 金额信息 ---"));
        assertTrue(readableText.contains("--- 账户信息 ---"));
        assertTrue(readableText.contains("回单名称"));
        assertTrue(readableText.contains("电子银行转账回单"));
        assertTrue(readableText.contains("流水号"));
        assertTrue(readableText.contains("*****************"));
    }

    @Test
    public void testFormatInvoiceOcrData() {
        OcrService ocrService = new OcrService();
        
        // 模拟增值税发票OCR数据
        Map<String, String> invoiceData = new HashMap<>();
        invoiceData.put("发票名称", "增值税专用发票");
        invoiceData.put("发票代码", "1100231130");
        invoiceData.put("发票号码", "12345678");
        invoiceData.put("开票日期", "2025年07月13日");
        invoiceData.put("销售方名称", "北京科技有限公司");
        invoiceData.put("销售方纳税人识别号", "91110000123456789X");
        invoiceData.put("购买方名称", "上海贸易有限公司");
        invoiceData.put("购买方纳税人识别号", "91310000987654321Y");
        invoiceData.put("价税合计（小写）", "￥11,300.00");
        invoiceData.put("价税合计（大写）", "壹万壹仟叁佰元整");
        invoiceData.put("合计金额", "￥10,000.00");
        invoiceData.put("合计税额", "￥1,300.00");
        invoiceData.put("税率", "13%");
        invoiceData.put("备注", "技术服务费");
        invoiceData.put("开票人", "张三");
        invoiceData.put("复核", "李四");
        invoiceData.put("收款人", "王五");
        
        // 测试易读格式化
        String readableText = ocrService.formatOcrDataToReadableText(invoiceData, "INVOICE");
        
        System.out.println("增值税发票易读格式:");
        System.out.println(readableText);
        
        // 验证格式化结果
        assertNotNull(readableText);
        assertTrue(readableText.contains("=== OCR识别信息 ==="));
        assertTrue(readableText.contains("--- 基本信息 ---"));
        assertTrue(readableText.contains("--- 开票方信息 ---"));
        assertTrue(readableText.contains("--- 购买方信息 ---"));
        assertTrue(readableText.contains("--- 金额信息 ---"));
        assertTrue(readableText.contains("发票号码"));
        assertTrue(readableText.contains("12345678"));
        assertTrue(readableText.contains("销售方名称"));
        assertTrue(readableText.contains("北京科技有限公司"));
    }

    @Test
    public void testFormatJsonOcrData() {
        OcrService ocrService = new OcrService();
        
        // 模拟OCR数据
        Map<String, String> ocrData = new HashMap<>();
        ocrData.put("字段1", "值1");
        ocrData.put("字段2", "值2");
        
        // 测试JSON格式化
        String jsonText = ocrService.formatOcrDataToJson(ocrData);
        
        System.out.println("JSON格式:");
        System.out.println(jsonText);
        
        // 验证JSON格式化结果
        assertNotNull(jsonText);
        assertTrue(jsonText.contains("timestamp"));
        assertTrue(jsonText.contains("source"));
        assertTrue(jsonText.contains("腾讯云OCR"));
        assertTrue(jsonText.contains("fieldCount"));
        assertTrue(jsonText.contains("recognitionData"));
    }

    @Test
    public void testFormatEmptyOcrData() {
        OcrService ocrService = new OcrService();
        
        // 测试空数据
        Map<String, String> emptyData = new HashMap<>();
        
        String readableText = ocrService.formatOcrDataToReadableText(emptyData, "BANK_RECEIPT");
        String jsonText = ocrService.formatOcrDataToJson(emptyData);
        
        assertEquals("暂无OCR识别信息", readableText);
        assertEquals("{}", jsonText);
    }

    @Test
    public void testFormatNullOcrData() {
        OcrService ocrService = new OcrService();
        
        // 测试null数据
        String readableText = ocrService.formatOcrDataToReadableText(null, "BANK_RECEIPT");
        String jsonText = ocrService.formatOcrDataToJson(null);
        
        assertEquals("暂无OCR识别信息", readableText);
        assertEquals("{}", jsonText);
    }
}
