package cn.gson.financial.kernel.mapper;

import cn.gson.financial.kernel.model.entity.SubjectAiEnhancement;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 科目AI增强 Mapper 接口
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
@Mapper
public interface SubjectAiEnhancementMapper extends BaseMapper<SubjectAiEnhancement> {

    /**
     * 根据账套ID和关键词搜索匹配的科目AI增强信息
     * 
     * @param accountSetsId 账套ID
     * @param keyword 关键词
     * @return 匹配的科目AI增强信息列表
     */
    @Select("SELECT sae.*, s.code as subject_code, s.name as subject_name, s.type as subject_type " +
            "FROM fxy_financial_subject_ai_enhancement sae " +
            "INNER JOIN fxy_financial_subject s ON sae.subject_id = s.id " +
            "WHERE sae.account_sets_id = #{accountSetsId} " +
            "AND sae.status = 1 " +
            "AND (sae.ai_keywords LIKE CONCAT('%', #{keyword}, '%') " +
            "     OR sae.ai_description LIKE CONCAT('%', #{keyword}, '%') " +
            "     OR s.name LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY sae.confidence_score DESC, sae.usage_frequency DESC")
    List<SubjectAiEnhancement> searchByKeyword(@Param("accountSetsId") Integer accountSetsId, 
                                               @Param("keyword") String keyword);

    /**
     * 根据账套ID获取使用频率最高的科目AI增强信息
     * 
     * @param accountSetsId 账套ID
     * @param limit 限制数量
     * @return 使用频率最高的科目AI增强信息列表
     */
    @Select("SELECT sae.*, s.code as subject_code, s.name as subject_name, s.type as subject_type " +
            "FROM fxy_financial_subject_ai_enhancement sae " +
            "INNER JOIN fxy_financial_subject s ON sae.subject_id = s.id " +
            "WHERE sae.account_sets_id = #{accountSetsId} " +
            "AND sae.status = 1 " +
            "ORDER BY sae.usage_frequency DESC, sae.confidence_score DESC " +
            "LIMIT #{limit}")
    List<SubjectAiEnhancement> getTopUsedSubjects(@Param("accountSetsId") Integer accountSetsId, 
                                                  @Param("limit") Integer limit);

    /**
     * 根据置信度获取推荐科目
     * 
     * @param accountSetsId 账套ID
     * @param minConfidence 最小置信度
     * @return 推荐科目列表
     */
    @Select("SELECT sae.*, s.code as subject_code, s.name as subject_name, s.type as subject_type " +
            "FROM fxy_financial_subject_ai_enhancement sae " +
            "INNER JOIN fxy_financial_subject s ON sae.subject_id = s.id " +
            "WHERE sae.account_sets_id = #{accountSetsId} " +
            "AND sae.status = 1 " +
            "AND sae.confidence_score >= #{minConfidence} " +
            "ORDER BY sae.confidence_score DESC, sae.usage_frequency DESC")
    List<SubjectAiEnhancement> getRecommendedSubjects(@Param("accountSetsId") Integer accountSetsId, 
                                                      @Param("minConfidence") BigDecimal minConfidence);

    /**
     * 更新使用频率和最后匹配时间
     * 
     * @param id 主键ID
     * @return 更新行数
     */
    @Update("UPDATE fxy_financial_subject_ai_enhancement " +
            "SET usage_frequency = usage_frequency + 1, " +
            "    last_matched_date = NOW(), " +
            "    update_time = NOW() " +
            "WHERE id = #{id}")
    int incrementUsageFrequency(@Param("id") Integer id);

    /**
     * 批量更新置信度评分
     * 
     * @param subjectId 科目ID
     * @param accountSetsId 账套ID
     * @param confidenceScore 置信度评分
     * @return 更新行数
     */
    @Update("UPDATE fxy_financial_subject_ai_enhancement " +
            "SET confidence_score = #{confidenceScore}, " +
            "    update_time = NOW() " +
            "WHERE subject_id = #{subjectId} AND account_sets_id = #{accountSetsId}")
    int updateConfidenceScore(@Param("subjectId") Integer subjectId, 
                             @Param("accountSetsId") Integer accountSetsId, 
                             @Param("confidenceScore") BigDecimal confidenceScore);

    /**
     * 根据科目ID和账套ID查询AI增强信息
     * 
     * @param subjectId 科目ID
     * @param accountSetsId 账套ID
     * @return AI增强信息
     */
    @Select("SELECT * FROM fxy_financial_subject_ai_enhancement " +
            "WHERE subject_id = #{subjectId} AND account_sets_id = #{accountSetsId}")
    SubjectAiEnhancement getBySubjectAndAccount(@Param("subjectId") Integer subjectId, 
                                               @Param("accountSetsId") Integer accountSetsId);

    /**
     * 获取账套下所有启用的AI增强科目
     * 
     * @param accountSetsId 账套ID
     * @return AI增强科目列表
     */
    @Select("SELECT sae.*, s.code as subject_code, s.name as subject_name, s.type as subject_type " +
            "FROM fxy_financial_subject_ai_enhancement sae " +
            "INNER JOIN fxy_financial_subject s ON sae.subject_id = s.id " +
            "WHERE sae.account_sets_id = #{accountSetsId} " +
            "AND sae.status = 1 " +
            "ORDER BY s.code")
    List<SubjectAiEnhancement> getAllEnabledByAccount(@Param("accountSetsId") Integer accountSetsId);

    /**
     * 智能匹配科目 - 综合关键词、金额范围、业务类型等因素
     * 
     * @param accountSetsId 账套ID
     * @param keywords 关键词（多个用空格分隔）
     * @param amount 金额
     * @param businessType 业务类型
     * @return 匹配的科目列表
     */
    @Select("<script>" +
            "SELECT sae.*, s.code as subject_code, s.name as subject_name, s.type as subject_type, " +
            "       (CASE " +
            "         WHEN sae.ai_keywords LIKE CONCAT('%', #{keywords}, '%') THEN 0.8 " +
            "         WHEN sae.ai_description LIKE CONCAT('%', #{keywords}, '%') THEN 0.6 " +
            "         WHEN s.name LIKE CONCAT('%', #{keywords}, '%') THEN 0.4 " +
            "         ELSE 0.2 " +
            "       END) * sae.confidence_score as match_score " +
            "FROM fxy_financial_subject_ai_enhancement sae " +
            "INNER JOIN fxy_financial_subject s ON sae.subject_id = s.id " +
            "WHERE sae.account_sets_id = #{accountSetsId} " +
            "AND sae.status = 1 " +
            "<if test='keywords != null and keywords != \"\"'>" +
            "AND (sae.ai_keywords LIKE CONCAT('%', #{keywords}, '%') " +
            "     OR sae.ai_description LIKE CONCAT('%', #{keywords}, '%') " +
            "     OR s.name LIKE CONCAT('%', #{keywords}, '%')) " +
            "</if>" +
            "<if test='businessType != null and businessType != \"\"'>" +
            "AND (sae.matching_rules LIKE CONCAT('%', #{businessType}, '%') " +
            "     OR sae.ai_keywords LIKE CONCAT('%', #{businessType}, '%')) " +
            "</if>" +
            "ORDER BY match_score DESC, sae.usage_frequency DESC " +
            "LIMIT 10" +
            "</script>")
    List<SubjectAiEnhancement> intelligentMatch(@Param("accountSetsId") Integer accountSetsId,
                                               @Param("keywords") String keywords,
                                               @Param("amount") BigDecimal amount,
                                               @Param("businessType") String businessType);
}