package cn.gson.financial.kernel.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 归并预览DTO
 */
@Data
public class MergePreviewDto {
    
    /**
     * 预览组列表
     */
    private List<PreviewGroup> groups;
    
    /**
     * 总组数
     */
    private Integer totalGroups;
    
    /**
     * 总项目数
     */
    private Integer totalItems;
    
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 预览组
     */
    @Data
    public static class PreviewGroup {
        /**
         * 组名称
         */
        private String groupName;
        
        /**
         * 组内项目列表
         */
        private List<PreviewItem> items;
        
        /**
         * 组内项目数量
         */
        private Integer itemCount;
        
        /**
         * 组内总金额
         */
        private BigDecimal totalAmount;
        
        /**
         * 归并原因
         */
        private String mergeReason;
    }
    
    /**
     * 预览项目
     */
    @Data
    public static class PreviewItem {
        /**
         * 项目ID
         */
        private String itemId;
        
        /**
         * 项目编号
         */
        private String itemNo;
        
        /**
         * 项目类型
         */
        private String itemType;
        
        /**
         * 项目日期
         */
        private String itemDate;
        
        /**
         * 项目金额
         */
        private BigDecimal amount;
        
        /**
         * 项目摘要
         */
        private String summary;
        
        /**
         * 交易对手方
         */
        private String counterparty;
    }
}
