package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 归并规则实体类
 */
@Data
@TableName(value = "fxy_financial_merge_rules")
public class MergeRule implements Serializable {
    
    /**
     * 规则ID，使用UUID
     */
    @TableId(value = "rule_id", type = IdType.ID_WORKER_STR)
    private String ruleId;

    /**
     * 规则名称
     */
    @TableField(value = "rule_name")
    private String ruleName;

    /**
     * 规则描述
     */
    @TableField(value = "rule_description")
    private String ruleDescription;

    /**
     * 适用实体：DOCUMENT-票据，RECEIPT-银证，BOTH-两者
     */
    @TableField(value = "applicable_entity")
    private String applicableEntity;

    /**
     * 规则逻辑配置（JSON格式）
     */
    @TableField(value = "rule_logic")
    private String ruleLogic;

    /**
     * 是否启用
     */
    @TableField(value = "is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    private Date updatedAt;

    /**
     * 创建人ID
     */
    @TableField(value = "created_by")
    private Integer createdBy;

    /**
     * 账套ID
     */
    @TableField(value = "account_sets_id")
    private Integer accountSetsId;

    private static final long serialVersionUID = 1L;

    /**
     * 适用实体枚举
     */
    public enum ApplicableEntity {
        DOCUMENT("DOCUMENT", "票据"),
        RECEIPT("RECEIPT", "银证"),
        BOTH("BOTH", "两者");

        private final String code;
        private final String description;

        ApplicableEntity(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
