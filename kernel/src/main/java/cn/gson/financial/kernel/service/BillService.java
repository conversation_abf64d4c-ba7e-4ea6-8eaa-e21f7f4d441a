package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.vo.UserVo;
import com.baomidou.mybatisplus.extension.service.IService;

// import java.math.BigDecimal; // 已移除，关联功能已移至关联管理模块
import java.util.Date;
import java.util.List;

public interface BillService extends IService<Bill> {

    int batchInsert(List<Bill> list);

    String generateBillNo(Integer accountSetsId, Date billDate);

    /**
     * 保存票据
     * @param accountSetsId 账套ID
     * @param bill 票据对象
     * @param currentUser 当前用户
     * @return 保存后的票据对象
     */
    Bill save(Integer accountSetsId, Bill bill, UserVo currentUser);

    /**
     * 更新票据
     * @param accountSetsId 账套ID
     * @param bill 票据对象
     * @return 更新后的票据对象
     */
    Bill update(Integer accountSetsId, Bill bill);

    // getRecommendedBills 方法已移除，关联功能已移至关联管理模块

    // getUnrelatedBills 方法已移除，关联功能已移至关联管理模块
    
    /**
     * 根据票据编号查询票据
     * @param billNo 票据编号
     * @return 票据对象
     */
    Bill getByBillNo(String billNo);
    
    /**
     * 根据票据编号列表批量查询票据
     * @param billNos 票据编号列表
     * @return 票据列表
     */
    List<Bill> getByBillNos(List<String> billNos);

    /**
     * 检查发票号码是否重复
     * @param accountSetsId 账套ID
     * @param invoiceNumber 发票号码
     * @param excludeBillId 排除的票据ID（用于编辑时排除自身）
     * @return 是否重复
     */
    boolean isInvoiceNumberDuplicate(Integer accountSetsId, String invoiceNumber, Integer excludeBillId);

    /**
     * 检查票据是否重复（综合检查）
     * @param accountSetsId 账套ID
     * @param invoiceNumber 发票号码
     * @param amount 金额
     * @param billDate 票据日期
     * @param issuer 开票方
     * @param excludeBillId 排除的票据ID（用于编辑时排除自身）
     * @return 重复信息，如果不重复返回null
     */
    String checkBillDuplicate(Integer accountSetsId, String invoiceNumber, Double amount,
                            Date billDate, String issuer, Integer excludeBillId);
}