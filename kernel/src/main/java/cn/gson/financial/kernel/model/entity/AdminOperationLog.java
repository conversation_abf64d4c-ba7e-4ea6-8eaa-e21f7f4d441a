package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 管理员操作日志实体类
 * 对应表：fxy_financial_admin_operation_logs
 */
@Data
@TableName("fxy_financial_admin_operation_logs")
public class AdminOperationLog implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 操作人ID
     */
    @TableField("admin_user_id")
    private Integer adminUserId;
    
    /**
     * 操作人用户名
     */
    @TableField("admin_username")
    private String adminUsername;
    
    /**
     * 操作类型
     */
    @TableField("operation_type")
    private String operationType;
    
    /**
     * 操作模块
     */
    @TableField("operation_module")
    private String operationModule;
    
    /**
     * 操作描述
     */
    @TableField("operation_desc")
    private String operationDesc;
    
    /**
     * 请求方法
     */
    @TableField("request_method")
    private String requestMethod;
    
    /**
     * 请求URL
     */
    @TableField("request_url")
    private String requestUrl;
    
    /**
     * 请求参数
     */
    @TableField("request_params")
    private String requestParams;
    
    /**
     * 响应数据
     */
    @TableField("response_data")
    private String responseData;
    
    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;
    
    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;
    
    /**
     * 执行时间(毫秒)
     */
    @TableField("execution_time")
    private Integer executionTime;
    
    /**
     * 操作状态：1成功，0失败
     */
    @TableField("status")
    private Boolean status;
    
    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
