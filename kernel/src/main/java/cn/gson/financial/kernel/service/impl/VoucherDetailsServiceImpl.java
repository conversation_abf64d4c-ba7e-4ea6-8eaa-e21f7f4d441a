package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.common.DoubleComparer;
import cn.gson.financial.kernel.common.DoubleValueUtil;
import cn.gson.financial.kernel.exception.ServiceException;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.entity.AccountingCategoryDetails;
import cn.gson.financial.kernel.model.entity.Subject;
import cn.gson.financial.kernel.model.entity.VoucherDetails;
import cn.gson.financial.kernel.model.entity.VoucherDetailsAuxiliary;
import cn.gson.financial.kernel.model.mapper.AccountingCategoryDetailsMapper;
import cn.gson.financial.kernel.model.mapper.SubjectMapper;
import cn.gson.financial.kernel.model.mapper.VoucherDetailsAuxiliaryMapper;
import cn.gson.financial.kernel.model.mapper.VoucherDetailsMapper;
import cn.gson.financial.kernel.service.SubjectService;
import cn.gson.financial.kernel.service.VoucherDetailsService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Service
@Slf4j
public class VoucherDetailsServiceImpl extends ServiceImpl<VoucherDetailsMapper, VoucherDetails> implements VoucherDetailsService {

    @Autowired
    private SubjectMapper subjectMapper;

    @Autowired
    private AccountingCategoryDetailsMapper categoryDetailsMapper;

    @Autowired
    private VoucherDetailsAuxiliaryMapper detailsAuxiliaryMapper;

    @Autowired
    private SubjectService subjectService;

    @Override
    public int batchInsert(List<VoucherDetails> list) {
        return baseMapper.batchInsert(list);
    }

    @Override
    public boolean save(VoucherDetails entity) {
        LambdaQueryWrapper<VoucherDetails> qw = Wrappers.lambdaQuery();
        qw.eq(VoucherDetails::getAccountSetsId, entity.getAccountSetsId());
        qw.eq(VoucherDetails::getSubjectId, entity.getSubjectId());
        qw.eq(VoucherDetails::getSubjectCode, entity.getSubjectCode());
        qw.isNull(VoucherDetails::getVoucherId);

        boolean result;
        if (this.baseMapper.selectCount(qw) > 0) {
            result = super.update(entity, qw);
        } else {
            result = super.save(entity);
        }

        // 暂时禁用父级科目汇总功能，避免死循环
        // TODO: 重新实现父级科目汇总逻辑
        /*
        if (result && "期初".equals(entity.getSummary()) && entity.getAccountSetsId() != null && entity.getSubjectId() != null) {
            // 只有末级科目的期初余额变更才触发父级汇总，避免死循环
            if (isLeafSubject(entity.getSubjectId(), entity.getAccountSetsId())) {
                refreshParentSubjectBalances(entity.getAccountSetsId());
            }
        }
        */

        return result;
    }

    /**
     * 期初数据
     *
     * @param accountSetsId
     * @param type
     * @return
     */
    @Override
    public List<VoucherDetails> balanceList(Integer accountSetsId, String type) {
        return this.baseMapper.selectBalanceList(accountSetsId, type);
    }

    /**
     * 期初试算平衡
     *
     * @param accountSetsId
     * @return
     */
    @Override
    public Map<String, Map<String, Double>> trialBalance(Integer accountSetsId) {
        try {
            log.info("开始生成期初试算平衡表，账套ID: {}", accountSetsId);

            Map<String, Double> beginningBalance = this.baseMapper.selectListInitialCheckData(accountSetsId);
            List<Map> liabilities = this.baseMapper.selectBassetsAndLiabilities(accountSetsId);

            // 期初余额汇总
            Map<String, Double> bb = new HashMap<>();
            Double totalDebit = beginningBalance != null ? beginningBalance.get("debit_amount") : 0d;
            Double totalCredit = beginningBalance != null ? beginningBalance.get("credit_amount") : 0d;
            bb.put("借", totalDebit);
            bb.put("贷", totalCredit);
            bb.put("差额", Math.abs(totalDebit - totalCredit));
            bb.put("是否平衡", DoubleComparer.considerEqual(totalDebit, totalCredit) ? 1.0 : 0.0);

            // 资产负债分类处理
            Map<String, Double> bl = new HashMap<>();
            Map<String, List<Map>> collect = liabilities.stream().collect(Collectors.groupingBy(map -> (String) map.get("type")));
            collect.forEach((type, maps) -> {
                Optional<Map> borrow = maps.stream().filter(map -> "借".equals(map.get("balance_direction"))).findFirst();
                Optional<Map> credit = maps.stream().filter(map -> "贷".equals(map.get("balance_direction"))).findFirst();

                if (borrow.isPresent() && credit.isPresent()) {
                    Double balanceBorrow = DoubleValueUtil.getNotNullVal((Double) borrow.get().get("debit_amount"), (Double) borrow.get().get("credit_amount"));
                    Double balanceCredit = DoubleValueUtil.getNotNullVal((Double) credit.get().get("debit_amount"), (Double) credit.get().get("credit_amount"));
                    bl.put(type, balanceBorrow - balanceCredit);
                } else if (borrow.isPresent()) {
                    Double balanceBorrow = DoubleValueUtil.getNotNullVal((Double) borrow.get().get("debit_amount"), (Double) borrow.get().get("credit_amount"));
                    bl.put(type, balanceBorrow);
                } else if (credit.isPresent()) {
                    Double balanceCredit = DoubleValueUtil.getNotNullVal((Double) credit.get().get("debit_amount"), (Double) credit.get().get("credit_amount"));
                    bl.put(type, balanceCredit);
                }
            });

            // 获取详细统计信息
            Map<String, Double> detailedStats = getDetailedTrialBalanceStats(accountSetsId);

            Map<String, Map<String, Double>> data = new HashMap<>();
            data.put("beginningBalance", bb);
            data.put("liabilities", bl);
            data.put("statistics", detailedStats);

            log.info("期初试算平衡表生成完成 - 借方: {}, 贷方: {}, 是否平衡: {}",
                    totalDebit, totalCredit, DoubleComparer.considerEqual(totalDebit, totalCredit));

            return data;

        } catch (Exception e) {
            log.error("生成期初试算平衡表时发生异常", e);
            // 返回基本的错误信息
            Map<String, Map<String, Double>> errorResult = new HashMap<>();
            Map<String, Double> error = new HashMap<>();
            error.put("错误", 1.0);
            errorResult.put("错误", error);
            return errorResult;
        }
    }

    /**
     * 获取详细的试算平衡表统计信息
     */
    private Map<String, Double> getDetailedTrialBalanceStats(Integer accountSetsId) {
        Map<String, Double> stats = new HashMap<>();

        try {
            // 获取所有有期初余额的科目
            LambdaQueryWrapper<VoucherDetails> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(VoucherDetails::getAccountSetsId, accountSetsId)
                   .eq(VoucherDetails::getSummary, "期初")
                   .isNull(VoucherDetails::getVoucherId);

            List<VoucherDetails> initialBalances = this.list(wrapper);

            long debitCount = initialBalances.stream()
                    .mapToLong(b -> (b.getDebitAmount() != null && b.getDebitAmount() > 0) ? 1 : 0)
                    .sum();
            long creditCount = initialBalances.stream()
                    .mapToLong(b -> (b.getCreditAmount() != null && b.getCreditAmount() > 0) ? 1 : 0)
                    .sum();

            stats.put("科目总数", (double) initialBalances.size());
            stats.put("借方科目数", (double) debitCount);
            stats.put("贷方科目数", (double) creditCount);

        } catch (Exception e) {
            log.error("获取详细试算平衡表统计信息时发生异常", e);
        }

        return stats;
    }

    @Override
    public void saveAuxiliary(Integer accountSetsId, HashMap<String, Object> entity) {
        Integer subjectId = (Integer) entity.get("subjectId");
        JSONObject auxiliary = (JSONObject) entity.get("auxiliary");
        Subject subject = subjectMapper.selectById(subjectId);
        List<AccountingCategoryDetails> categoryDetails = categoryDetailsMapper.selectBatchIds(auxiliary.values().stream().mapToInt(o -> (Integer) o).boxed().collect(Collectors.toList()));

        StringBuilder auxiliaryTitle = new StringBuilder();
        StringBuilder subjectCode = new StringBuilder(subject.getCode());

        for (AccountingCategoryDetails cd : categoryDetails) {
            auxiliaryTitle.append("_").append(cd.getName());
            subjectCode.append("_").append(cd.getId());
        }

        LambdaQueryWrapper<VoucherDetails> qw = Wrappers.lambdaQuery();
        qw.eq(VoucherDetails::getAccountSetsId, accountSetsId);
        qw.eq(VoucherDetails::getSubjectId, subjectId);
        qw.eq(VoucherDetails::getSubjectCode, subjectCode.toString());
        qw.isNull(VoucherDetails::getVoucherId);

        VoucherDetails details = this.baseMapper.selectOne(qw);
        if (details == null) {
            details = new VoucherDetails();
            details.setSummary("期初");
            details.setSubjectId(subjectId);
            details.setSubjectName(subjectCode.toString() + "-" + subject.getName());
            details.setSubjectCode(subjectCode.toString());
            details.setAuxiliaryTitle(auxiliaryTitle.toString());
            details.setAccountSetsId(accountSetsId);
            this.baseMapper.insert(details);
        }

        List<VoucherDetailsAuxiliary> voucherDetailsAuxiliaries = new ArrayList<>();

        for (AccountingCategoryDetails cd : categoryDetails) {
            LambdaQueryWrapper<VoucherDetailsAuxiliary> aqw = Wrappers.lambdaQuery();
            aqw.eq(VoucherDetailsAuxiliary::getVoucherDetailsId, details.getId());
            aqw.eq(VoucherDetailsAuxiliary::getAccountingCategoryId, cd.getAccountingCategoryId());
            aqw.eq(VoucherDetailsAuxiliary::getAccountingCategoryDetailsId, cd.getId());

            if (detailsAuxiliaryMapper.selectCount(aqw) == 0) {
                VoucherDetailsAuxiliary vda = new VoucherDetailsAuxiliary();
                vda.setVoucherDetailsId(details.getId());
                vda.setAccountingCategoryId(cd.getAccountingCategoryId());
                vda.setAccountingCategoryDetailsId(cd.getId());
                voucherDetailsAuxiliaries.add(vda);
            }
        }

        if (!voucherDetailsAuxiliaries.isEmpty()) {
            detailsAuxiliaryMapper.batchInsert(voucherDetailsAuxiliaries);
        }
    }

    @Override
    public List<VoucherDetails> auxiliaryList(Integer accountSetsId, String type) {
        return this.baseMapper.selectAuxiliaryList(accountSetsId, type);
    }

    /**
     * 科目会计期间的累计金额
     *
     * @param accountSetsId
     * @param codeList
     * @param currentAccountDate
     * @return
     */
    @Override
    public Map<String, VoucherDetails> getAggregateAmount(Integer accountSetsId, Set<String> codeList, Date currentAccountDate) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(currentAccountDate);
        List<VoucherDetails> details = this.baseMapper.selectAggregateAmount(accountSetsId, codeList, instance.get(Calendar.YEAR), instance.get(Calendar.MONTH) + 1);
        return details.stream().collect(Collectors.toMap(VoucherDetails::getSubjectCode, voucherDetails -> voucherDetails));
    }

    /**
     * 批量导入期初余额
     *
     * @param initialBalanceList
     * @param accountSets
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importInitialBalance(List<VoucherDetails> initialBalanceList, AccountSets accountSets) {
        if (initialBalanceList == null || initialBalanceList.isEmpty()) {
            throw new ServiceException("导入数据为空");
        }

        log.info("开始批量导入期初余额，共 {} 条记录", initialBalanceList.size());

        try {
            // 删除现有的期初余额数据
            LambdaQueryWrapper<VoucherDetails> deleteWrapper = Wrappers.lambdaQuery();
            deleteWrapper.eq(VoucherDetails::getAccountSetsId, accountSets.getId());
            deleteWrapper.eq(VoucherDetails::getSummary, "期初");
            deleteWrapper.isNull(VoucherDetails::getVoucherId);
            this.remove(deleteWrapper);

            // 批量插入新的期初余额数据
            for (VoucherDetails voucherDetails : initialBalanceList) {
                // 确保数据完整性
                if (voucherDetails.getAccountSetsId() == null) {
                    voucherDetails.setAccountSetsId(accountSets.getId());
                }
                if (voucherDetails.getSummary() == null) {
                    voucherDetails.setSummary("期初");
                }

                // 验证只有末级科目可以录入期初余额
                if (voucherDetails.getSubjectId() != null) {
                    if (!isLeafSubject(voucherDetails.getSubjectId(), accountSets.getId())) {
                        Subject subject = subjectService.getById(voucherDetails.getSubjectId());
                        String subjectInfo = subject != null ? subject.getCode() + "-" + subject.getName() : "ID:" + voucherDetails.getSubjectId();
                        throw new RuntimeException("只有末级科目可以录入期初余额，科目 " + subjectInfo + " 不是末级科目");
                    }
                }

                // 直接插入期初余额记录，避免触发save方法的递归调用
                this.baseMapper.insert(voucherDetails);
            }

            // 暂时禁用父级科目汇总功能
            // TODO: 重新实现父级科目汇总逻辑
            // refreshParentSubjectBalances(accountSets.getId());

            log.info("期初余额导入完成，成功导入 {} 条记录", initialBalanceList.size());

        } catch (Exception e) {
            log.error("期初余额导入失败", e);
            throw new ServiceException("期初余额导入失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否为末级科目
     * @param subjectId 科目ID
     * @param accountSetsId 账套ID
     * @return 是否为末级科目
     */
    private boolean isLeafSubject(Integer subjectId, Integer accountSetsId) {
        // 查询是否有子科目
        LambdaQueryWrapper<Subject> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Subject::getParentId, subjectId)
                   .eq(Subject::getAccountSetsId, accountSetsId)
                   .eq(Subject::getStatus, true);
        long childCount = subjectService.count(queryWrapper);
        return childCount == 0;
    }

    /**
     * 刷新父级科目余额汇总
     * @param accountSetsId 账套ID
     */
    private void refreshParentSubjectBalances(Integer accountSetsId) {
        try {
            log.info("开始刷新父级科目余额汇总，账套ID: {}", accountSetsId);

            // 获取所有科目
            LambdaQueryWrapper<Subject> subjectWrapper = Wrappers.lambdaQuery();
            subjectWrapper.eq(Subject::getAccountSetsId, accountSetsId)
                         .eq(Subject::getStatus, true)
                         .orderByAsc(Subject::getCode);

            List<Subject> allSubjects = subjectService.list(subjectWrapper);

            // 筛选出父级科目，并按层级从深到浅排序
            List<Subject> parentSubjects = allSubjects.stream()
                    .filter(subject -> hasChildSubjects(subject.getId(), allSubjects))
                    .sorted((a, b) -> Integer.compare(b.getLevel(), a.getLevel()))
                    .collect(Collectors.toList());

            log.info("找到 {} 个父级科目需要更新汇总", parentSubjects.size());

            // 为每个父级科目计算汇总余额
            for (Subject parentSubject : parentSubjects) {
                updateParentSubjectBalance(parentSubject, accountSetsId, allSubjects);
            }

            log.info("父级科目余额汇总刷新完成");

        } catch (Exception e) {
            log.error("刷新父级科目余额汇总时发生异常", e);
            // 异常时不影响主流程
        }
    }

    /**
     * 检查科目是否有子科目
     */
    private boolean hasChildSubjects(Integer subjectId, List<Subject> allSubjects) {
        return allSubjects.stream().anyMatch(subject -> subjectId.equals(subject.getParentId()));
    }

    /**
     * 更新单个父级科目的余额汇总
     * @param parentSubject 父级科目
     * @param accountSetsId 账套ID
     * @param allSubjects 所有科目列表
     */
    private void updateParentSubjectBalance(Subject parentSubject, Integer accountSetsId, List<Subject> allSubjects) {
        try {
            // 获取所有直接子科目的期初余额
            LambdaQueryWrapper<VoucherDetails> childWrapper = Wrappers.lambdaQuery();
            childWrapper.eq(VoucherDetails::getAccountSetsId, accountSetsId)
                       .eq(VoucherDetails::getSummary, "期初")
                       .isNull(VoucherDetails::getVoucherId);

            // 从已有的科目列表中获取直接子科目ID列表
            List<Integer> childSubjectIds = allSubjects.stream()
                    .filter(subject -> parentSubject.getId().equals(subject.getParentId()))
                    .map(Subject::getId)
                    .collect(Collectors.toList());

            if (childSubjectIds.isEmpty()) {
                return;
            }

            childWrapper.in(VoucherDetails::getSubjectId, childSubjectIds);
            List<VoucherDetails> childBalances = this.list(childWrapper);

            // 计算汇总余额
            double totalDebitAmount = 0.0;
            double totalCreditAmount = 0.0;

            for (VoucherDetails childBalance : childBalances) {
                if (childBalance.getDebitAmount() != null) {
                    totalDebitAmount += childBalance.getDebitAmount();
                }
                if (childBalance.getCreditAmount() != null) {
                    totalCreditAmount += childBalance.getCreditAmount();
                }
            }

            // 更新或创建父级科目的期初余额记录
            LambdaQueryWrapper<VoucherDetails> parentWrapper = Wrappers.lambdaQuery();
            parentWrapper.eq(VoucherDetails::getAccountSetsId, accountSetsId)
                        .eq(VoucherDetails::getSubjectId, parentSubject.getId())
                        .eq(VoucherDetails::getSummary, "期初")
                        .isNull(VoucherDetails::getVoucherId);

            VoucherDetails parentBalance = this.getOne(parentWrapper);
            if (parentBalance == null) {
                parentBalance = new VoucherDetails();
                parentBalance.setAccountSetsId(accountSetsId);
                parentBalance.setSubjectId(parentSubject.getId());
                parentBalance.setSubjectCode(parentSubject.getCode());
                parentBalance.setSubjectName(parentSubject.getCode() + "-" + parentSubject.getName());
                parentBalance.setSummary("期初");
            }

            // 根据科目余额方向设置金额
            if ("借".equals(parentSubject.getBalanceDirection().toString())) {
                double netAmount = totalDebitAmount - totalCreditAmount;
                if (netAmount >= 0) {
                    parentBalance.setDebitAmount(netAmount);
                    parentBalance.setCreditAmount(null);
                } else {
                    parentBalance.setDebitAmount(null);
                    parentBalance.setCreditAmount(Math.abs(netAmount));
                }
            } else {
                double netAmount = totalCreditAmount - totalDebitAmount;
                if (netAmount >= 0) {
                    parentBalance.setCreditAmount(netAmount);
                    parentBalance.setDebitAmount(null);
                } else {
                    parentBalance.setCreditAmount(null);
                    parentBalance.setDebitAmount(Math.abs(netAmount));
                }
            }

            // 直接更新父级科目余额，避免触发save方法的递归调用
            if (parentBalance.getId() != null) {
                // 更新现有记录
                this.baseMapper.updateById(parentBalance);
            } else {
                // 插入新记录
                this.baseMapper.insert(parentBalance);
            }

            log.debug("更新父级科目 {} 余额汇总: 借方={}, 贷方={}",
                    parentSubject.getCode(), parentBalance.getDebitAmount(), parentBalance.getCreditAmount());

        } catch (Exception e) {
            log.error("更新父级科目 {} 余额汇总时发生异常", parentSubject.getCode(), e);
        }
    }
}











