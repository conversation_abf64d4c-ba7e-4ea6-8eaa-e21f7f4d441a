package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.AiMatchingHistory;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.gson.financial.kernel.model.vo.AiStatisticsOverview;
import cn.gson.financial.kernel.model.vo.ErrorAnalysis;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Date;
import java.util.List;

public interface AiAnalysisHistoryService {

    /**
     * 分页查询AI匹配历史记录
     *
     * @param page 分页对象
     * @param accountSetsId 账套ID
     * @param businessType 业务类型
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return
     */
    IPage<AiMatchingHistory> findHistoryByPage(
            Page<AiMatchingHistory> page,
            Integer accountSetsId,
            String businessType,
            Date startDate,
            Date endDate
    );

    /**
     * 获取AI统计概览
     *
     * @param accountSetsId 账套ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return
     */
    AiStatisticsOverview getStatisticsOverview(Integer accountSetsId, Date startDate, Date endDate);

    /**
     * 获取错误分析
     *
     * @param accountSetsId 账套ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return
     */
    List<ErrorAnalysis> getErrorAnalysis(Integer accountSetsId, Date startDate, Date endDate);
}
