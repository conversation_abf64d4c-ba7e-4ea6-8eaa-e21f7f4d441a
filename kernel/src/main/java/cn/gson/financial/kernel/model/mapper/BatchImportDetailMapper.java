package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.BatchImportDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 批量导入明细Mapper
 */
@Mapper
public interface BatchImportDetailMapper extends BaseMapper<BatchImportDetail> {
    
    /**
     * 根据任务ID查询明细列表
     */
    @Select("SELECT * FROM fxy_financial_batch_import_detail " +
            "WHERE task_id = #{taskId} " +
            "ORDER BY id ASC")
    List<BatchImportDetail> selectByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据任务ID和状态查询明细列表
     */
    @Select("SELECT * FROM fxy_financial_batch_import_detail " +
            "WHERE task_id = #{taskId} AND status = #{status} " +
            "ORDER BY id ASC")
    List<BatchImportDetail> selectByTaskIdAndStatus(@Param("taskId") String taskId, 
                                                    @Param("status") String status);
    
    /**
     * 统计任务的处理情况
     */
    @Select("SELECT " +
            "COUNT(*) as total, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed, " +
            "SUM(CASE WHEN status IN ('PENDING', 'UPLOADING', 'PROCESSING', 'RECOGNIZING') THEN 1 ELSE 0 END) as processing " +
            "FROM fxy_financial_batch_import_detail " +
            "WHERE task_id = #{taskId}")
    List<java.util.Map<String, Object>> countByTaskId(@Param("taskId") String taskId);
    
    /**
     * 更新明细状态
     */
    @Update("UPDATE fxy_financial_batch_import_detail SET " +
            "status = #{status}, " +
            "error_message = #{errorMessage}, " +
            "updated_time = NOW() " +
            "WHERE id = #{id}")
    int updateStatus(@Param("id") Integer id,
                    @Param("status") String status,
                    @Param("errorMessage") String errorMessage);
    
    /**
     * 更新识别结果
     */
    @Update("UPDATE fxy_financial_batch_import_detail SET " +
            "recognition_result = #{recognitionResult}, " +
            "parsed_data = #{parsedData}, " +
            "confidence_score = #{confidenceScore}, " +
            "status = #{status}, " +
            "processing_time = #{processingTime}, " +
            "updated_time = NOW() " +
            "WHERE id = #{id}")
    int updateRecognitionResult(@Param("id") Integer id,
                               @Param("recognitionResult") String recognitionResult,
                               @Param("parsedData") String parsedData,
                               @Param("confidenceScore") BigDecimal confidenceScore,
                               @Param("status") String status,
                               @Param("processingTime") Integer processingTime);
    
    /**
     * 更新最终数据
     */
    @Update("UPDATE fxy_financial_batch_import_detail SET " +
            "final_data = #{finalData}, " +
            "updated_time = NOW() " +
            "WHERE id = #{id}")
    int updateFinalData(@Param("id") Integer id,
                       @Param("finalData") String finalData);
}
