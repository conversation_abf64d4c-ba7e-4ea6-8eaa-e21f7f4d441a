package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.mapper.BillMapper;
import cn.gson.financial.kernel.model.vo.UserVo;
import cn.gson.financial.kernel.service.BillService;
import cn.gson.financial.kernel.service.AccountSetsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

// import java.math.BigDecimal; // 已移除，关联功能已移至关联管理模块
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class BillServiceImpl extends ServiceImpl<BillMapper, Bill> implements BillService {

    @Autowired
    private AccountSetsService accountSetsService;

    @Override
    public int batchInsert(List<Bill> list) {
        return baseMapper.batchInsert(list);
    }

    @Override
    public String generateBillNo(Integer accountSetsId, Date billDate) {
        // 添加空值检查
        if (billDate == null) {
            billDate = new Date(); // 使用当前日期作为默认值
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(billDate);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int nextNo = 1;
        
        // 添加重试机制防止并发重复
        int maxRetries = 3;
        for (int i = 0; i < maxRetries; i++) {
            // 查询当月最大票据号
            LambdaQueryWrapper<Bill> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(Bill::getAccountSetsId, accountSetsId)
                       .eq(Bill::getBillYear, year)
                       .eq(Bill::getBillMonth, month)
                       .orderByDesc(Bill::getBillNo)
                       .last("LIMIT 1");
            
            Bill lastBill = getOne(queryWrapper);
            
            if (lastBill != null && lastBill.getBillNo() != null) {
                try {
                    String billNo = lastBill.getBillNo();
                    String lastNoStr;

                    // 检查票据号格式，支持多种格式
                    if (billNo.contains("-")) {
                        // 标准格式：PJ202501-0001
                        lastNoStr = billNo.substring(billNo.lastIndexOf("-") + 1);
                    } else if (billNo.matches("^[A-Z]+\\d+$")) {
                        // 旧格式：XS202506003，提取末尾数字部分
                        lastNoStr = billNo.replaceAll("^[A-Z]+", "");
                        // 如果是年月+序号格式，只取最后3位作为序号
                        if (lastNoStr.length() > 3) {
                            lastNoStr = lastNoStr.substring(lastNoStr.length() - 3);
                        }
                    } else {
                        // 其他格式，尝试提取末尾的数字
                        lastNoStr = billNo.replaceAll(".*?(\\d+)$", "$1");
                    }

                    nextNo = Integer.parseInt(lastNoStr) + 1;
                } catch (Exception e) {
                    log.warn("解析票据号失败，票据号格式: {}, 使用默认值1", lastBill.getBillNo(), e);
                }
            }
            
            String billNo = String.format("PJ%04d%02d-%04d", year, month, nextNo);
            
            // 检查是否已存在
            LambdaQueryWrapper<Bill> checkWrapper = Wrappers.lambdaQuery();
            checkWrapper.eq(Bill::getAccountSetsId, accountSetsId)
                       .eq(Bill::getBillNo, billNo);
            
            if (count(checkWrapper) == 0) {
                return billNo;
            }
            
            // 如果存在重复，稍作延迟后重试
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        // 如果重试失败，使用时间戳确保唯一性
        return String.format("PJ%04d%02d-%04d%03d", year, month, nextNo, System.currentTimeMillis() % 1000);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Bill save(Integer accountSetsId, Bill bill, UserVo currentUser) {
        // 设置账套ID
        bill.setAccountSetsId(accountSetsId);

        // 检查票据是否重复（综合检查）
        String duplicateInfo = checkBillDuplicate(accountSetsId,
                bill.getInvoiceNumber(),
                bill.getAmount(),
                bill.getBillDate(),
                bill.getIssuer(),
                null);
        if (duplicateInfo != null) {
            throw new RuntimeException("发票重复：" + duplicateInfo + "，请检查后重新输入");
        }

        // 添加空值检查
        if (bill.getBillDate() == null) {
            bill.setBillDate(new Date());
        }
        
        // 设置年月 - 使用账套当前期间而不是票据日期
        Integer[] currentPeriod = getCurrentAccountPeriod(accountSetsId, currentUser);
        bill.setBillYear(currentPeriod[0]);
        bill.setBillMonth(currentPeriod[1]);
        
        // 生成票据编号
        if (bill.getBillNo() == null || bill.getBillNo().isEmpty()) {
            bill.setBillNo(generateBillNo(accountSetsId, bill.getBillDate()));
        }
        
        // 设置默认状态
        if (bill.getStatus() == null || bill.getStatus().isEmpty()) {
            bill.setStatus("未使用");
        }
        
        // 设置创建和更新时间
        Date now = new Date();
        bill.setCreateTime(now);
        bill.setUpdateTime(now);
        
        // 保存票据
        save(bill);
        
        return bill;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Bill update(Integer accountSetsId, Bill bill) {
        // 检查票据是否重复（综合检查）
        String duplicateInfo = checkBillDuplicate(accountSetsId,
                bill.getInvoiceNumber(),
                bill.getAmount(),
                bill.getBillDate(),
                bill.getIssuer(),
                bill.getId());
        if (duplicateInfo != null) {
            throw new RuntimeException("发票重复：" + duplicateInfo + "，请检查后重新输入");
        }

        // 添加空值检查
        if (bill.getBillDate() == null) {
            bill.setBillDate(new Date());
        }
        
        // 设置年月 - 使用账套当前期间而不是票据日期
        Integer[] currentPeriod = getCurrentAccountPeriod(accountSetsId, null);
        bill.setBillYear(currentPeriod[0]);
        bill.setBillMonth(currentPeriod[1]);
        
        // 设置更新时间
        bill.setUpdateTime(new Date());
        
        // 更新票据
        updateById(bill);
        
        return bill;
    }
    
    // getRecommendedBills 方法已移除，关联功能已移至关联管理模块
    
    // getUnrelatedBills 方法已移除，关联功能已移至关联管理模块
    
    // 在BillServiceImpl类中添加以下方法
    
    @Override
    public Bill getByBillNo(String billNo) {
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_no", billNo);
        return getOne(queryWrapper);
    }
    
    @Override
    public List<Bill> getByBillNos(List<String> billNos) {
        if (CollectionUtils.isEmpty(billNos)) {
            return new ArrayList<>();
        }
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("bill_no", billNos);
        return list(queryWrapper);
    }

    @Override
    public boolean isInvoiceNumberDuplicate(Integer accountSetsId, String invoiceNumber, Integer excludeBillId) {
        // 如果发票号码为空，不检查重复
        if (invoiceNumber == null || invoiceNumber.trim().isEmpty()) {
            return false;
        }

        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId);
        queryWrapper.eq("invoice_number", invoiceNumber.trim());

        // 如果是编辑操作，排除当前票据
        if (excludeBillId != null) {
            queryWrapper.ne("id", excludeBillId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public String checkBillDuplicate(Integer accountSetsId, String invoiceNumber, Double amount,
                                   Date billDate, String issuer, Integer excludeBillId) {
        try {
            // 如果关键字段都缺失，不进行重复检测
            if ((invoiceNumber == null || invoiceNumber.trim().isEmpty()) && amount == null) {
                return null;
            }

            // 构建查询条件
            LambdaQueryWrapper<Bill> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Bill::getAccountSetsId, accountSetsId);

            // 如果是编辑操作，排除当前票据
            if (excludeBillId != null) {
                wrapper.ne(Bill::getId, excludeBillId);
            }

            // 优先使用发票号码进行精确匹配
            if (invoiceNumber != null && !invoiceNumber.trim().isEmpty()) {
                wrapper.eq(Bill::getInvoiceNumber, invoiceNumber.trim());
                long count = count(wrapper);
                log.info("票据重复检测 - 发票号码: {}, 账套ID: {}, 查询结果: {}", invoiceNumber.trim(), accountSetsId, count);
                if (count > 0) {
                    String duplicateInfo = String.format("发票号码 '%s' 已存在", invoiceNumber);
                    log.info("发现重复票据（发票号码匹配）: {}", invoiceNumber);
                    return duplicateInfo;
                }
            }

            // 如果发票号码没有重复，使用组合条件检测
            if (amount != null && billDate != null && issuer != null && !issuer.trim().isEmpty()) {
                LambdaQueryWrapper<Bill> wrapper2 = new LambdaQueryWrapper<>();
                wrapper2.eq(Bill::getAccountSetsId, accountSetsId)
                        .eq(Bill::getAmount, amount)
                        .eq(Bill::getBillDate, billDate)
                        .eq(Bill::getIssuer, issuer.trim());

                // 如果是编辑操作，排除当前票据
                if (excludeBillId != null) {
                    wrapper2.ne(Bill::getId, excludeBillId);
                }

                long count = count(wrapper2);
                if (count > 0) {
                    String duplicateInfo = String.format("相同金额 %.2f 元、日期 %s、开票方 %s 的发票已存在",
                            amount, billDate, issuer);
                    log.info("发现重复票据（组合条件匹配）: 金额={}, 日期={}, 开票方={}",
                            amount, billDate, issuer);
                    return duplicateInfo;
                }
            }

            return null;

        } catch (Exception e) {
            log.error("检查票据重复时发生异常", e);
            // 异常时不阻止保存，但记录日志
            return null;
        }
    }

    /**
     * 获取账套当前期间
     * @param accountSetsId 账套ID
     * @param currentUser 当前用户（可为null）
     * @return [年份, 月份]
     */
    private Integer[] getCurrentAccountPeriod(Integer accountSetsId, UserVo currentUser) {
        try {
            // 优先从currentUser获取账套信息
            if (currentUser != null && currentUser.getAccountSets() != null
                && currentUser.getAccountSets().getCurrentAccountDate() != null) {
                LocalDate currentAccountDate = currentUser.getAccountSets().getCurrentAccountDate().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate();
                return new Integer[]{currentAccountDate.getYear(), currentAccountDate.getMonthValue()};
            }

            // 从数据库获取账套信息
            AccountSets accountSets = accountSetsService.getById(accountSetsId);
            if (accountSets != null && accountSets.getCurrentAccountDate() != null) {
                LocalDate currentAccountDate = accountSets.getCurrentAccountDate().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate();
                log.info("使用账套当前期间: {}-{}", currentAccountDate.getYear(), currentAccountDate.getMonthValue());
                return new Integer[]{currentAccountDate.getYear(), currentAccountDate.getMonthValue()};
            } else {
                // 如果没有当前期间，使用当前日期
                LocalDate now = LocalDate.now();
                log.warn("账套当前期间为空，使用当前日期: {}-{}", now.getYear(), now.getMonthValue());
                return new Integer[]{now.getYear(), now.getMonthValue()};
            }
        } catch (Exception e) {
            log.error("获取账套当前期间失败，使用当前日期", e);
            LocalDate now = LocalDate.now();
            return new Integer[]{now.getYear(), now.getMonthValue()};
        }
    }
}