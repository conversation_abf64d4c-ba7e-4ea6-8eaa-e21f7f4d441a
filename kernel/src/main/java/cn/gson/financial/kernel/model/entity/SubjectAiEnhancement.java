package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 科目AI增强实体类
 * 用于存储科目的AI描述、关键词、匹配规则等信息
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fxy_financial_subject_ai_enhancement")
public class SubjectAiEnhancement implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 科目ID，关联fxy_financial_subject.id
     */
    @TableField("subject_id")
    private Integer subjectId;

    /**
     * 账套ID
     */
    @TableField("account_sets_id")
    private Integer accountSetsId;

    /**
     * AI描述信息，用于大模型理解科目用途
     */
    @TableField("ai_description")
    private String aiDescription;

    /**
     * AI关键词，逗号分隔，用于匹配
     */
    @TableField("ai_keywords")
    private String aiKeywords;

    /**
     * 匹配规则配置，包含条件表达式、权重等
     */
    @TableField("matching_rules")
    private String matchingRules;

    /**
     * 使用频率统计
     */
    @TableField("usage_frequency")
    private Integer usageFrequency;

    /**
     * 最后匹配时间
     */
    @TableField("last_matched_date")
    private LocalDateTime lastMatchedDate;

    /**
     * 匹配置信度评分
     */
    @TableField("confidence_score")
    private BigDecimal confidenceScore;

    /**
     * 学习数据，存储历史匹配结果用于优化
     */
    @TableField("learning_data")
    private String learningData;

    /**
     * 自定义规则，用户可配置的特殊匹配逻辑
     */
    @TableField("custom_rules")
    private String customRules;

    /**
     * 状态：1-启用，0-禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建用户ID
     */
    @TableField("create_user")
    private Integer createUser;

    /**
     * 更新用户ID
     */
    @TableField("update_user")
    private Integer updateUser;

    // 数据库字段常量
    public static final String COL_ID = "id";
    public static final String COL_SUBJECT_ID = "subject_id";
    public static final String COL_ACCOUNT_SETS_ID = "account_sets_id";
    public static final String COL_AI_DESCRIPTION = "ai_description";
    public static final String COL_AI_KEYWORDS = "ai_keywords";
    public static final String COL_MATCHING_RULES = "matching_rules";
    public static final String COL_USAGE_FREQUENCY = "usage_frequency";
    public static final String COL_LAST_MATCHED_DATE = "last_matched_date";
    public static final String COL_CONFIDENCE_SCORE = "confidence_score";
    public static final String COL_LEARNING_DATA = "learning_data";
    public static final String COL_CUSTOM_RULES = "custom_rules";
    public static final String COL_STATUS = "status";
    public static final String COL_CREATE_TIME = "create_time";
    public static final String COL_UPDATE_TIME = "update_time";
    public static final String COL_CREATE_USER = "create_user";
    public static final String COL_UPDATE_USER = "update_user";

    /**
     * 状态枚举
     */
    public static class Status {
        public static final int DISABLED = 0; // 禁用
        public static final int ENABLED = 1;  // 启用
    }

    /**
     * 默认置信度阈值
     */
    public static final BigDecimal DEFAULT_CONFIDENCE_THRESHOLD = new BigDecimal("0.7000");

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return Status.ENABLED == this.status;
    }

    /**
     * 检查置信度是否达到阈值
     */
    public boolean isConfidenceAboveThreshold() {
        return this.confidenceScore != null && 
               this.confidenceScore.compareTo(DEFAULT_CONFIDENCE_THRESHOLD) >= 0;
    }

    /**
     * 增加使用频率
     */
    public void incrementUsageFrequency() {
        if (this.usageFrequency == null) {
            this.usageFrequency = 1;
        } else {
            this.usageFrequency++;
        }
        this.lastMatchedDate = LocalDateTime.now();
    }
}