package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.FieldMappingTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 字段映射模板Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */
@Mapper
public interface FieldMappingTemplateMapper extends BaseMapper<FieldMappingTemplate> {

    /**
     * 根据银行标识和票据类型查找模板（包括系统级模板）
     */
    @Select("SELECT * FROM fxy_financial_field_mapping_template " +
            "WHERE (account_sets_id = #{accountSetsId} OR account_sets_id IS NULL) " +
            "AND document_type = #{documentType} " +
            "AND (bank_identifier = #{bankIdentifier} OR bank_identifier IS NULL) " +
            "AND (receipt_type = #{receiptType} OR receipt_type IS NULL) " +
            "AND is_active = 1 " +
            "ORDER BY " +
            "  CASE WHEN account_sets_id = #{accountSetsId} THEN 1 ELSE 2 END, " +
            "  CASE WHEN bank_identifier = #{bankIdentifier} THEN 1 ELSE 2 END, " +
            "  CASE WHEN receipt_type = #{receiptType} THEN 1 ELSE 2 END, " +
            "  usage_count DESC, success_rate DESC " +
            "LIMIT 5")
    List<FieldMappingTemplate> findByBankAndType(@Param("accountSetsId") Integer accountSetsId,
                                                  @Param("bankIdentifier") String bankIdentifier,
                                                  @Param("documentType") String documentType,
                                                  @Param("receiptType") String receiptType);

    /**
     * 根据字段特征签名查找模板（包括系统级模板）
     */
    @Select("SELECT * FROM fxy_financial_field_mapping_template " +
            "WHERE (account_sets_id = #{accountSetsId} OR account_sets_id IS NULL) " +
            "AND document_type = #{documentType} " +
            "AND field_signature = #{fieldSignature} " +
            "AND is_active = 1 " +
            "ORDER BY " +
            "  CASE WHEN account_sets_id = #{accountSetsId} THEN 1 ELSE 2 END, " +
            "  usage_count DESC, success_rate DESC " +
            "LIMIT 1")
    FieldMappingTemplate findByFieldSignature(@Param("accountSetsId") Integer accountSetsId,
                                               @Param("documentType") String documentType,
                                               @Param("fieldSignature") String fieldSignature);

    /**
     * 根据模板名称查找模板
     */
    @Select("SELECT * FROM fxy_financial_field_mapping_template " +
            "WHERE template_name = #{templateName} " +
            "AND is_active = 1 " +
            "LIMIT 1")
    FieldMappingTemplate findByTemplateName(@Param("templateName") String templateName);

    /**
     * 根据字段数量和相似度查找模板（包括系统级模板）
     */
    @Select("SELECT * FROM fxy_financial_field_mapping_template " +
            "WHERE (account_sets_id = #{accountSetsId} OR account_sets_id IS NULL) " +
            "AND document_type = #{documentType} " +
            "AND ABS(field_count - #{fieldCount}) <= 3 " +
            "AND (bank_identifier = #{bankIdentifier} OR bank_identifier IS NULL) " +
            "AND is_active = 1 " +
            "ORDER BY " +
            "  CASE WHEN account_sets_id = #{accountSetsId} THEN 1 ELSE 2 END, " +
            "  CASE WHEN bank_identifier = #{bankIdentifier} THEN 1 ELSE 2 END, " +
            "  ABS(field_count - #{fieldCount}), " +
            "  usage_count DESC, success_rate DESC " +
            "LIMIT 3")
    List<FieldMappingTemplate> findSimilarTemplates(@Param("accountSetsId") Integer accountSetsId,
                                                     @Param("bankIdentifier") String bankIdentifier,
                                                     @Param("documentType") String documentType,
                                                     @Param("fieldCount") Integer fieldCount);

    /**
     * 更新模板使用统计
     */
    @Update("UPDATE fxy_financial_field_mapping_template SET " +
            "usage_count = usage_count + 1, " +
            "last_used_time = #{lastUsedTime}, " +
            "success_rate = CASE " +
            "  WHEN #{success} = 1 THEN " +
            "    CASE WHEN usage_count = 0 THEN 100.00 " +
            "         ELSE (success_rate * usage_count + 100) / (usage_count + 1) END " +
            "  ELSE " +
            "    CASE WHEN usage_count = 0 THEN 0.00 " +
            "         ELSE (success_rate * usage_count) / (usage_count + 1) END " +
            "END " +
            "WHERE id = #{templateId}")
    int updateUsageStats(@Param("templateId") Integer templateId,
                        @Param("success") Boolean success,
                        @Param("lastUsedTime") LocalDateTime lastUsedTime);

    /**
     * 获取账套下的所有模板
     */
    @Select("SELECT * FROM fxy_financial_field_mapping_template " +
            "WHERE account_sets_id = #{accountSetsId} " +
            "AND is_active = 1 " +
            "ORDER BY document_type, bank_identifier, usage_count DESC")
    List<FieldMappingTemplate> findByAccountSetsId(@Param("accountSetsId") Integer accountSetsId);

    /**
     * 禁用模板
     */
    @Update("UPDATE fxy_financial_field_mapping_template SET is_active = 0 WHERE id = #{templateId}")
    int disableTemplate(@Param("templateId") Integer templateId);

    /**
     * 启用模板
     */
    @Update("UPDATE fxy_financial_field_mapping_template SET is_active = 1 WHERE id = #{templateId}")
    int enableTemplate(@Param("templateId") Integer templateId);
}
