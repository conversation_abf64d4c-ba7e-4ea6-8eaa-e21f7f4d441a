package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 票据AI处理结果实体类
 */
@Data
@TableName(value = "fxy_financial_bill_ai_result")
public class BillAiResult implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的票据ID
     */
    @TableField(value = "bill_id")
    private Integer billId;

    /**
     * AI处理状态：0-未处理，1-已处理
     */
    @TableField(value = "ai_processed")
    private Boolean aiProcessed;

    /**
     * AI置信度(0-1)
     */
    @TableField(value = "confidence")
    private Double confidence;

    /**
     * AI分析结果
     */
    @TableField(value = "ai_analysis")
    private String aiAnalysis;

    /**
     * 推荐科目信息(JSON格式)
     */
    @TableField(value = "suggested_subjects")
    private String suggestedSubjects;

    /**
     * 借方科目推荐(JSON格式)
     */
    @TableField(value = "debit_subjects")
    private String debitSubjects;

    /**
     * 贷方科目推荐(JSON格式)
     */
    @TableField(value = "credit_subjects")
    private String creditSubjects;

    /**
     * AI匹配原因说明
     */
    @TableField(value = "matching_reason")
    private String matchingReason;

    /**
     * AI处理时间
     */
    @TableField(value = "ai_process_time")
    private Date aiProcessTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private Date updatedTime;

    private static final long serialVersionUID = 1L;
}