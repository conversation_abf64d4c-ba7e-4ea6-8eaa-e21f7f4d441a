package cn.gson.financial.kernel.service;

import java.util.List;
import java.util.Map;

/**
 * AI智能凭证生成服务接口
 */
public interface AiVoucherService {

    /**
     * 生成AI凭证
     *
     * @param accountSetsId 账套ID
     * @param sourceItems 数据源列表
     * @param userId 用户ID
     * @param voucherYear 凭证年份
     * @param voucherMonth 凭证月份
     * @return 生成结果
     */
    Map<String, Object> generateVouchers(Integer accountSetsId, List<Map<String, Object>> sourceItems, Integer userId, Integer voucherYear, Integer voucherMonth);

    /**
     * 获取AI凭证生成统计
     * 
     * @param accountSetsId 账套ID
     * @param userId 用户ID
     * @return 统计数据
     */
    Map<String, Object> getGenerationStatistics(Integer accountSetsId, Integer userId);

    /**
     * 确认AI生成的凭证
     * 
     * @param accountSetsId 账套ID
     * @param voucherData 凭证数据
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean confirmGeneratedVoucher(Integer accountSetsId, Map<String, Object> voucherData, Integer userId);

    /**
     * 批量确认AI生成的凭证
     * 
     * @param accountSetsId 账套ID
     * @param vouchers 凭证列表
     * @param userId 用户ID
     * @return 确认数量
     */
    int batchConfirmVouchers(Integer accountSetsId, List<Map<String, Object>> vouchers, Integer userId);

    /**
     * 获取AI凭证生成历史
     * 
     * @param accountSetsId 账套ID
     * @param userId 用户ID
     * @param page 页码
     * @param pageSize 页大小
     * @return 历史记录
     */
    Map<String, Object> getGenerationHistory(Integer accountSetsId, Integer userId, Integer page, Integer pageSize);

    /**
     * 重新生成凭证
     * 
     * @param accountSetsId 账套ID
     * @param originalVoucherId 原凭证ID
     * @param parameters 生成参数
     * @param userId 用户ID
     * @return 生成结果
     */
    Map<String, Object> regenerateVoucher(Integer accountSetsId, Integer originalVoucherId, 
                                         Map<String, Object> parameters, Integer userId);

    /**
     * 获取AI凭证模板
     * 
     * @param accountSetsId 账套ID
     * @return 模板列表
     */
    List<Map<String, Object>> getVoucherTemplates(Integer accountSetsId);

    /**
     * 保存AI凭证模板
     * 
     * @param accountSetsId 账套ID
     * @param template 模板数据
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean saveVoucherTemplate(Integer accountSetsId, Map<String, Object> template, Integer userId);

    /**
     * 预览AI凭证
     * 
     * @param accountSetsId 账套ID
     * @param sourceItems 数据源
     * @param userId 用户ID
     * @return 预览数据
     */
    Map<String, Object> previewVoucher(Integer accountSetsId, List<Map<String, Object>> sourceItems, Integer userId);

    /**
     * 分析源数据
     * 
     * @param accountSetsId 账套ID
     * @param sourceData 源数据
     * @param userId 用户ID
     * @return 分析结果
     */
    Map<String, Object> analyzeSourceData(Integer accountSetsId, Map<String, Object> sourceData, Integer userId);
}
