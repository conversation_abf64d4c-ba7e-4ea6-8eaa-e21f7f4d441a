package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.common.DateUtil;
import cn.gson.financial.kernel.common.DoubleComparer;
import cn.gson.financial.kernel.common.DoubleValueUtil;
import cn.gson.financial.kernel.exception.ServiceException;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.entity.Subject;
import cn.gson.financial.kernel.model.entity.VoucherDetails;
import cn.gson.financial.kernel.model.entity.VoucherTemplateDetails;
import cn.gson.financial.kernel.model.mapper.*;
import cn.gson.financial.kernel.model.vo.BalanceVo;
import cn.gson.financial.kernel.model.vo.SubjectVo;
import cn.gson.financial.kernel.model.vo.VoucherDetailVo;
import cn.gson.financial.kernel.service.SubjectService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Slf4j
@Service
@AllArgsConstructor
public class SubjectServiceImpl extends ServiceImpl<SubjectMapper, Subject> implements SubjectService {

    private AccountSetsMapper accountSetsMapper;
    private VoucherMapper voucherMapper;
    private SubjectMapper subjectMapper;
    private VoucherDetailsMapper voucherDetailsMapper;
    private VoucherTemplateDetailsMapper voucherTemplateDetailsMapper;

    @Override
    public int batchInsert(List<Subject> list) {
        return baseMapper.batchInsert(list);
    }

    @Override
    public List<SubjectVo> selectData(Wrapper<Subject> queryWrapper, boolean showAll) {
        QueryWrapper<Subject> qw = (QueryWrapper<Subject>) queryWrapper;
        qw.eq("status", 1);
        Map<Integer, Subject> subjectMap = baseMapper.selectList(qw).stream().collect(Collectors.toMap(Subject::getId, subject -> subject));
        List<Subject> list = baseMapper.selectNoChildrenSubject(qw);
        list.forEach(subject -> {
            if (subject.getLevel() != 1) {
                recursiveChildren(subjectMap, subject, subject.getParentId());
            }
        });

        if (showAll) {
            list.forEach(subject -> subjectMap.remove(subject.getId()));
            list.addAll(subjectMap.values());
        }

        return list.stream().sorted(Comparator.comparing(Subject::getCode)).map(subject -> {
            SubjectVo vo = new SubjectVo();
            BeanUtils.copyProperties(subject, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 明细科目
     *
     * @param accountDate
     * @param accountSetsId
     * @return
     */
    @Override
    public List<Subject> accountBookList(Date accountDate, Integer accountSetsId, boolean showNumPrice) {
        List<Subject> subjectList = baseMapper.selectAccountBookList(accountDate != null ? DateUtil.getMonthEnd(accountDate) : null, accountSetsId, showNumPrice);

        Map<Integer, Subject> temp = new HashMap<>();
        subjectList.forEach(subject -> this.recursiveParent(temp, subject));
        return temp.values().stream().sorted(Comparator.comparing(Subject::getCode)).distinct().collect(Collectors.toList());
    }

    /**
     * 余额明细科目
     *
     * @param accountDate
     * @param accountSetsId
     * @return
     */
    @Override
    public List<Subject> balanceSubjectList(Date accountDate, Integer accountSetsId, boolean showNumPrice) {
        List<Subject> subjectList = this.accountBookList(DateUtil.getMonthEnd(accountDate), accountSetsId, showNumPrice);

        Map<Integer, Subject> temp = new HashMap<>();
        subjectList.forEach(subject -> this.recursiveParent(temp, subject));
        return temp.values().stream().sorted(Comparator.comparing(Subject::getCode)).distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void importVoucher(List<SubjectVo> voucherList, AccountSets accountSets) {
        AtomicInteger update = new AtomicInteger(0);
        AtomicInteger insert = new AtomicInteger(0);
        this.recursive(voucherList, update, insert, accountSets);
    }

    private void recursive(@NonNull List<SubjectVo> voucherList, AtomicInteger update, AtomicInteger insert, AccountSets accountSets) {
        for (SubjectVo vo : voucherList) {
            if (vo.getId() != null) {
                if (this.checkUse(vo.getId())) {
                    continue;
                }

                this.updateById(vo);
                update.incrementAndGet();
                if (!vo.getChildren().isEmpty()) {
                    vo.getChildren().forEach(s -> s.setParentId(vo.getId()));
                    this.recursive(vo.getChildren(), update, insert, accountSets);
                }
            } else {
                if (accountSets.getAccountingStandards() == (short) 0 && vo.getLevel() == (short) 1) {
                    continue;
                }

                //一般纳税人
                this.save(vo);
                insert.incrementAndGet();
                if (!vo.getChildren().isEmpty()) {
                    vo.getChildren().forEach(s -> s.setParentId(vo.getId()));
                    this.recursive(vo.getChildren(), update, insert, accountSets);
                }
            }
        }
    }

    /**
     * 科目余额
     *
     * @param accountDate
     * @param accountSetsId
     * @return
     */
    @Override
    public List<BalanceVo> subjectBalance(Date accountDate, Integer accountSetsId, boolean showNumPrice) {
        //当前查询账套
        AccountSets accountSets = accountSetsMapper.selectById(accountSetsId);
        List<Subject> subjects = this.accountBookList(null, accountSetsId, showNumPrice);
        //转换为余额对象
        Map<Integer, BalanceVo> sbvMap = subjects.stream().collect(Collectors.toMap(Subject::getId, subject -> {
            BalanceVo sbv = new BalanceVo();
            sbv.setSubjectId(subject.getId());
            sbv.setCode(subject.getCode());
            sbv.setParentId(subject.getParentId());
            sbv.setName(subject.getName());
            sbv.setLevel(subject.getLevel());
            sbv.setUnit(subject.getUnit());
            sbv.setBalanceDirection(subject.getBalanceDirection().toString());
            return sbv;
        }));

        //原始期初余额
        if (sbvMap.isEmpty()) {
            return new ArrayList<>();
        }


        //对比账套时间，判断是否是初始账套时间
        if (!DateFormatUtils.format(accountSets.getEnableDate(), "yyyyMM").equals(DateFormatUtils.format(accountDate, "yyyyMM"))) {
            List<VoucherDetailVo> details = voucherMapper.selectSubjectDetail(sbvMap.keySet(), accountSetsId, DateUtil.getMonthBegin(accountDate), null, showNumPrice);
            details.forEach(vd -> {
                if (sbvMap.containsKey(vd.getSubjectId())) {
                    BalanceVo sbv = sbvMap.get(vd.getSubjectId());
                    Double val = null;
                    switch (sbv.getBalanceDirection()) {
                        case "借":
                            val = DoubleValueUtil.getNotNullVal(vd.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vd.getCreditAmount());
                            break;
                        case "贷":
                            val = DoubleValueUtil.getNotNullVal(vd.getCreditAmount()) - DoubleValueUtil.getNotNullVal(vd.getDebitAmount());
                            break;
                    }
                    sbv.setBeginningActiveBalance(val);
                }
            });
        } else {
            LambdaQueryWrapper<VoucherDetails> qwi = Wrappers.lambdaQuery();
            qwi.eq(VoucherDetails::getAccountSetsId, accountSetsId);
            qwi.in(VoucherDetails::getSubjectId, sbvMap.keySet());
            qwi.isNull(VoucherDetails::getVoucherId);
            qwi.and(wrapper -> {
                wrapper.or(true).isNotNull(VoucherDetails::getCreditAmount);
                wrapper.or(true).isNotNull(VoucherDetails::getDebitAmount);
            });
            this.voucherDetailsMapper.selectList(qwi).forEach(ib -> {
                if (sbvMap.containsKey(ib.getSubjectId())) {
                    BalanceVo balanceVo = sbvMap.get(ib.getSubjectId());
                    // 根据科目借贷方向和实际数据计算期初余额
                    Double beginBalance = null;
                    if (ib.getDebitAmount() != null && ib.getCreditAmount() != null) {
                        // 如果借贷方都有值，按科目方向计算
                        if ("借".equals(balanceVo.getBalanceDirection())) {
                            beginBalance = ib.getDebitAmount() - ib.getCreditAmount();
                        } else {
                            beginBalance = ib.getCreditAmount() - ib.getDebitAmount();
                        }
                    } else if (ib.getDebitAmount() != null) {
                        // 只有借方有值
                        if ("借".equals(balanceVo.getBalanceDirection())) {
                            beginBalance = ib.getDebitAmount();
                        } else {
                            // 贷方科目但借方有值，说明是负数余额
                            beginBalance = -ib.getDebitAmount();
                        }
                    } else if (ib.getCreditAmount() != null) {
                        // 只有贷方有值
                        if ("贷".equals(balanceVo.getBalanceDirection())) {
                            beginBalance = ib.getCreditAmount();
                        } else {
                            // 借方科目但贷方有值，说明是负数余额
                            beginBalance = -ib.getCreditAmount();
                        }
                    }

                    if (beginBalance != null) {
                        // 使用setBeginningActiveBalance处理负数转换
                        balanceVo.setBeginningActiveBalance(beginBalance.doubleValue());
                    }
                }
            });
        }

        //本期发生额
        List<VoucherDetailVo> details = voucherMapper.selectSubjectDetail(sbvMap.keySet(), accountSetsId, DateUtil.getMonthBegin(accountDate), DateUtil.getMonthEnd(accountDate), showNumPrice);
        details.forEach(vd -> {
            if (sbvMap.containsKey(vd.getSubjectId())) {
                BalanceVo sbv = sbvMap.get(vd.getSubjectId());
                sbv.setCurrentCreditAmount(vd.getCreditAmount());
                sbv.setCurrentDebitAmount(vd.getDebitAmount());
                if (showNumPrice && vd.getBalanceDirection() != null) {
                    switch (vd.getBalanceDirection()) {
                        case "借":
                            sbv.setCurrentDebitAmountNum(vd.getNum());
                            break;
                        case "贷":
                            sbv.setCurrentCreditAmountNum(vd.getNum());
                            break;
                    }
                }
            }
        });

        //合计
        BalanceVo aCombined = new BalanceVo();
        aCombined.setName("合计");

        // 初始化合计值
        double totalBeginningDebit = 0.0;
        double totalBeginningCredit = 0.0;
        double totalCurrentDebit = 0.0;
        double totalCurrentCredit = 0.0;
        double totalEndingDebit = 0.0;
        double totalEndingCredit = 0.0;

        //计算期末
        List<BalanceVo> balanceVos = sbvMap.values().stream().sorted(Comparator.comparing(BalanceVo::getCode)).collect(Collectors.toList());
        for (BalanceVo vo : balanceVos) {
            //期初
            Double bb = DoubleValueUtil.getNotNullVal(vo.getBeginningBalance());
            //本期借贷金额
            Double cc = DoubleValueUtil.getNotNullVal(vo.getCurrentCreditAmount());
            Double cd = DoubleValueUtil.getNotNullVal(vo.getCurrentDebitAmount());
            //本期借贷数量
            Double ccNum = DoubleValueUtil.getNotNullVal(vo.getCurrentCreditAmountNum());
            Double cdNum = DoubleValueUtil.getNotNullVal(vo.getCurrentDebitAmountNum());

            //根据方向计算余额
            switch (vo.getBalanceDirection()) {
                case "借":
                    vo.setEndingActiveBalance(bb + cd - cc);
                    break;
                case "贷":
                    vo.setEndingActiveBalance(bb - cd + cc);
                    break;
            }

            if (showNumPrice) {
                switch (vo.getBalanceDirection()) {
                    case "借":
                        vo.setEndingDebitBalanceNum(cdNum - ccNum);
                        break;
                    case "贷":
                        vo.setEndingCreditBalanceNum(ccNum - cdNum);
                        break;
                }
            }

            //计算合计列 - 累加到总计变量
            // 本期发生额合计（所有科目的发生额都要累加）
            totalCurrentDebit += DoubleValueUtil.getNotNullVal(vo.getCurrentDebitAmount());
            totalCurrentCredit += DoubleValueUtil.getNotNullVal(vo.getCurrentCreditAmount());

            // 期初余额合计（按科目借贷方向分别累加）
            if ("借".equals(vo.getBalanceDirection())) {
                // 借方科目的期初余额
                if (vo.getBeginningBalance() != null && vo.getBeginningBalance() > 0) {
                    totalBeginningDebit += vo.getBeginningBalance();
                } else if (vo.getBeginningBalance() != null && vo.getBeginningBalance() < 0) {
                    // 借方科目负余额累加到贷方合计
                    totalBeginningCredit += Math.abs(vo.getBeginningBalance());
                }
            } else {
                // 贷方科目的期初余额
                if (vo.getBeginningBalance() != null && vo.getBeginningBalance() > 0) {
                    totalBeginningCredit += vo.getBeginningBalance();
                } else if (vo.getBeginningBalance() != null && vo.getBeginningBalance() < 0) {
                    // 贷方科目负余额累加到借方合计
                    totalBeginningDebit += Math.abs(vo.getBeginningBalance());
                }
            }

            // 期末余额合计（按科目借贷方向分别累加）
            if ("借".equals(vo.getBalanceDirection())) {
                // 借方科目的期末余额
                if (vo.getEndingBalance() != null && vo.getEndingBalance() > 0) {
                    totalEndingDebit += vo.getEndingBalance();
                } else if (vo.getEndingBalance() != null && vo.getEndingBalance() < 0) {
                    // 借方科目负余额累加到贷方合计
                    totalEndingCredit += Math.abs(vo.getEndingBalance());
                }
            } else {
                // 贷方科目的期末余额
                if (vo.getEndingBalance() != null && vo.getEndingBalance() > 0) {
                    totalEndingCredit += vo.getEndingBalance();
                } else if (vo.getEndingBalance() != null && vo.getEndingBalance() < 0) {
                    // 贷方科目负余额累加到借方合计
                    totalEndingDebit += Math.abs(vo.getEndingBalance());
                }
            }
        }

        // 设置合计行的值
        aCombined.setBeginningDebitBalance(totalBeginningDebit);
        aCombined.setBeginningCreditBalance(totalBeginningCredit);
        aCombined.setCurrentDebitAmount(totalCurrentDebit);
        aCombined.setCurrentCreditAmount(totalCurrentCredit);
        aCombined.setEndingDebitBalance(totalEndingDebit);
        aCombined.setEndingCreditBalance(totalEndingCredit);

        //计算父节点 - 重新实现父子汇总逻辑
        calculateParentSubjectBalances(balanceVos);
        log.info("父子科目汇总计算完成");

        if (balanceVos.size() > 0) {
            //过滤掉空行
            balanceVos = balanceVos.stream().filter(vo ->
                    (vo.getBeginningBalance() != null && vo.getBeginningBalance() != 0) ||
                            (vo.getEndingBalance() != null && vo.getEndingBalance() != 0) ||
                            (vo.getCurrentDebitAmount() != null && vo.getCurrentDebitAmount() != 0) ||
                            (vo.getCurrentCreditAmount() != null && vo.getCurrentCreditAmount() != 0)
            ).collect(Collectors.toList());

            // 借贷平衡校验
            // 检查期初借贷平衡
            boolean beginningBalanced = DoubleComparer.considerEqual(totalBeginningDebit, totalBeginningCredit);
            // 检查期末借贷平衡
            boolean endingBalanced = DoubleComparer.considerEqual(totalEndingDebit, totalEndingCredit);
            // 检查本期发生额借贷平衡
            boolean currentBalanced = DoubleComparer.considerEqual(totalCurrentDebit, totalCurrentCredit);

            // 如果不平衡，在合计行名称中添加警告标识
            if (!beginningBalanced || !endingBalanced || !currentBalanced) {
                StringBuilder warning = new StringBuilder("合计 [");
                if (!beginningBalanced) warning.append("期初不平衡 ");
                if (!currentBalanced) warning.append("本期不平衡 ");
                if (!endingBalanced) warning.append("期末不平衡 ");
                warning.append("]");
                aCombined.setName(warning.toString());
            }

            balanceVos.add(aCombined);
        }

        return balanceVos;
    }

    /**
     * 科目汇总
     *
     * @param accountDate
     * @param accountSetsId
     * @return
     */
    @Override
    public List subjectSummary(Date accountDate, Integer accountSetsId, boolean showNumPrice) {
        List<Subject> subjects = this.accountBookList(accountDate, accountSetsId, showNumPrice);
        //转换为余额对象
        Map<Integer, BalanceVo> sbvMap = subjects.stream().collect(Collectors.toMap(Subject::getId, subject -> {
            BalanceVo sbv = new BalanceVo();
            sbv.setSubjectId(subject.getId());
            sbv.setCode(subject.getCode());
            sbv.setUnit(subject.getUnit());
            sbv.setParentId(subject.getParentId());
            sbv.setName(subject.getName());
            sbv.setLevel(subject.getLevel());
            sbv.setBalanceDirection(subject.getBalanceDirection().toString());
            return sbv;
        }));

        if (sbvMap.isEmpty()) {
            return new ArrayList<>();
        }

        //本期发生额
        List<VoucherDetailVo> details = voucherMapper.selectSubjectDetail(sbvMap.keySet(), accountSetsId, DateUtil.getMonthBegin(accountDate), DateUtil.getMonthEnd(accountDate), showNumPrice);
        details.forEach(vd -> {
            if (sbvMap.containsKey(vd.getSubjectId())) {
                BalanceVo sbv = sbvMap.get(vd.getSubjectId());
                if (showNumPrice) {
                    if (vd.getBalanceDirection() != null) {
                        switch (vd.getBalanceDirection()) {
                            case "借":
                                sbv.setCurrentDebitAmount(vd.getDebitAmount());
                                sbv.setCurrentDebitAmountNum(vd.getNum());
                                break;
                            case "贷":
                                sbv.setCurrentCreditAmount(vd.getCreditAmount());
                                sbv.setCurrentCreditAmountNum(vd.getNum());
                                break;
                        }
                    }
                } else {
                    sbv.setCurrentCreditAmount(vd.getCreditAmount());
                    sbv.setCurrentDebitAmount(vd.getDebitAmount());
                }
            }
        });

        List<BalanceVo> balanceVos = sbvMap.values().stream().sorted(Comparator.comparing(BalanceVo::getCode)).collect(Collectors.toList());

        //合计行 - 简化版本
        BalanceVo aCombined = new BalanceVo();
        aCombined.setName("合计");

        // 使用原有的累加逻辑
        for (BalanceVo vo : balanceVos) {
            aCombined.setCurrentCreditAmount(vo.getCurrentCreditAmount());
            aCombined.setCurrentCreditAmountNum(vo.getCurrentCreditAmountNum());
            aCombined.setCurrentDebitAmount(vo.getCurrentDebitAmount());
            aCombined.setCurrentDebitAmountNum(vo.getCurrentDebitAmountNum());
        }

        if (balanceVos.size() > 0) {
            balanceVos.add(aCombined);
        }

        return balanceVos;
    }

    /**
     * 计算父级科目余额汇总
     * @param balanceVos 余额列表
     */
    private void calculateParentSubjectBalances(List<BalanceVo> balanceVos) {
        try {
            // 按科目层级分组，从最深层级开始计算
            Map<Short, List<BalanceVo>> levelGroups = balanceVos.stream()
                    .collect(Collectors.groupingBy(BalanceVo::getLevel));

            // 获取最大层级，从最深层级开始向上汇总
            short maxLevel = (short) levelGroups.keySet().stream().mapToInt(Short::intValue).max().orElse(1);

            // 从最深层级开始，逐级向上汇总
            for (short level = maxLevel; level > 1; level--) {
                List<BalanceVo> currentLevelSubjects = levelGroups.get(level);
                if (currentLevelSubjects == null) continue;

                // 按父级科目分组
                Map<Integer, List<BalanceVo>> parentGroups = currentLevelSubjects.stream()
                        .filter(vo -> vo.getParentId() != null)
                        .collect(Collectors.groupingBy(BalanceVo::getParentId));

                // 为每个父级科目汇总子科目余额
                for (Map.Entry<Integer, List<BalanceVo>> entry : parentGroups.entrySet()) {
                    Integer parentId = entry.getKey();
                    List<BalanceVo> children = entry.getValue();

                    // 找到父级科目
                    BalanceVo parent = balanceVos.stream()
                            .filter(vo -> vo.getSubjectId().equals(parentId))
                            .findFirst()
                            .orElse(null);

                    if (parent != null) {
                        // 汇总子科目的余额
                        summarizeChildrenBalances(parent, children);
                    }
                }
            }

        } catch (Exception e) {
            log.error("计算父级科目余额汇总时发生异常", e);
            // 异常时不影响主流程，只记录日志
        }
    }

    /**
     * 汇总子科目余额到父科目
     * @param parent 父科目
     * @param children 子科目列表
     */
    private void summarizeChildrenBalances(BalanceVo parent, List<BalanceVo> children) {
        double beginBalance = 0.0;
        double currentDebitAmount = 0.0;
        double currentCreditAmount = 0.0;
        double currentDebitAmountNum = 0.0;
        double currentCreditAmountNum = 0.0;

        for (BalanceVo child : children) {
            // 汇总期初余额
            if (child.getBeginningBalance() != null) {
                beginBalance += child.getBeginningBalance();
            }

            // 汇总本期借方发生额
            if (child.getCurrentDebitAmount() != null) {
                currentDebitAmount += child.getCurrentDebitAmount();
            }

            // 汇总本期贷方发生额
            if (child.getCurrentCreditAmount() != null) {
                currentCreditAmount += child.getCurrentCreditAmount();
            }

            // 汇总本期借方数量
            if (child.getCurrentDebitAmountNum() != null) {
                currentDebitAmountNum += child.getCurrentDebitAmountNum();
            }

            // 汇总本期贷方数量
            if (child.getCurrentCreditAmountNum() != null) {
                currentCreditAmountNum += child.getCurrentCreditAmountNum();
            }
        }

        // 设置父科目的汇总值
        parent.setBeginningBalance(beginBalance);
        parent.setCurrentDebitAmount(currentDebitAmount);
        parent.setCurrentCreditAmount(currentCreditAmount);
        parent.setCurrentDebitAmountNum(currentDebitAmountNum);
        parent.setCurrentCreditAmountNum(currentCreditAmountNum);

        // 计算期末余额
        double endBalance = beginBalance + currentDebitAmount - currentCreditAmount;
        if ("贷".equals(parent.getBalanceDirection())) {
            endBalance = beginBalance + currentCreditAmount - currentDebitAmount;
        }
        parent.setEndingActiveBalance(endBalance);

        log.debug("父科目 {} 汇总完成: 期初={}, 借方={}, 贷方={}, 期末={}",
                parent.getCode(), beginBalance, currentDebitAmount, currentCreditAmount, endBalance);
    }

    private void recursiveParent(Map<Integer, Subject> temp, Subject subject) {
        if (subject == null) {
            return;
        }
        temp.put(subject.getId(), subject);
        if (subject.getLevel() != 1 && subject.getParentId() != null) {
            if (!temp.containsKey(subject.getParentId())) {
                Subject sbj = baseMapper.selectById(subject.getParentId());
                this.recursiveParent(temp, sbj);
            }
        }
    }

    private void recursiveChildren(Map<Integer, Subject> subjectMap, Subject subject, int parentId) {
        Subject parent = subjectMap.get(parentId);
        subject.setName(parent.getName() + "-" + subject.getName());
        if (parent.getLevel() != 1) {
            recursiveChildren(subjectMap, subject, parent.getParentId());
        }
    }

    public List<SubjectVo> listVo(Wrapper<Subject> queryWrapper) {
        QueryWrapper<Subject> qw = (QueryWrapper<Subject>) queryWrapper;
        qw.orderByAsc("code");
        return baseMapper.selectSubjectVo(queryWrapper);
    }

    /**
     * 检查科目是否已经被使用
     *
     * @param id
     * @return
     */
    @Override
    public Boolean checkUse(Integer id) {
        LambdaQueryWrapper<VoucherDetails> vdqw = Wrappers.lambdaQuery();
        vdqw.eq(VoucherDetails::getSubjectId, id);
        boolean vd = voucherDetailsMapper.selectCount(vdqw) == 0;

        LambdaQueryWrapper<VoucherTemplateDetails> vtdqw = Wrappers.lambdaQuery();
        vtdqw.eq(VoucherTemplateDetails::getSubjectId, id);
        boolean vtd = voucherTemplateDetailsMapper.selectCount(vtdqw) == 0;

        return !(vd && vtd);
    }

    /**
     * 科目余额
     *
     * @param accountSetsId
     * @param subjectId
     * @param categoryId
     * @param categoryDetailsId
     * @return
     */
    @Override
    public Double balance(Integer accountSetsId, Integer subjectId, Integer categoryId, Integer categoryDetailsId) {
        LambdaQueryWrapper<VoucherDetails> ibqw = Wrappers.lambdaQuery();
        ibqw.eq(VoucherDetails::getSubjectId, subjectId);
        ibqw.eq(VoucherDetails::getAccountSetsId, accountSetsId);
        ibqw.isNull(VoucherDetails::getVoucherId);

        List<VoucherDetails> ibs = this.voucherDetailsMapper.selectList(ibqw);
        VoucherDetails ib = null;
        if (ibs.size() > 0) {
            ib = ibs.get(0);
            for (int i = 1; i < ibs.size(); i++) {
                double creditAmount = Optional.ofNullable(ib.getCreditAmount()).orElse(0d) + Optional.ofNullable(ibs.get(i).getCreditAmount()).orElse(0d);
                double debitAmount = Optional.ofNullable(ib.getDebitAmount()).orElse(0d) + Optional.ofNullable(ibs.get(i).getDebitAmount()).orElse(0d);
                ib.setCreditAmount(creditAmount);
                ib.setDebitAmount(debitAmount);
            }
        }


        List<VoucherDetailVo> vds = voucherDetailsMapper.selectBalanceData(accountSetsId, subjectId, categoryId, categoryDetailsId);
        double balance = 0d;
        if (!vds.isEmpty()) {
            VoucherDetailVo vo = vds.get(0);
            if (vo != null) {
                if (vo.getBalanceDirection().equals("借")) {
                    balance = DoubleValueUtil.getNotNullVal(vo.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vo.getCreditAmount());
                } else {
                    balance = DoubleValueUtil.getNotNullVal(vo.getCreditAmount()) - DoubleValueUtil.getNotNullVal(vo.getDebitAmount());
                }
            }
        }

        //TODO 期初暂时没有辅助期初，categoryId == null过滤掉辅助
        if (ib != null && categoryId == null) {
            balance += DoubleValueUtil.getNotNullVal(ib.getDebitAmount(), ib.getCreditAmount());
        }

        return balance;
    }

    /**
     * 过滤出所有没有子节点的科目
     *
     * @param accountSetsId
     * @return
     */
    @Override
    public List<Integer> leafList(Integer accountSetsId) {
        return this.baseMapper.selectLeaf(accountSetsId);
    }

    @Override
    public boolean save(Subject entity) {
        LambdaQueryWrapper<Subject> qw = Wrappers.lambdaQuery();
        qw.eq(Subject::getAccountSetsId, entity.getAccountSetsId());
        qw.eq(Subject::getName, entity.getName());
        qw.eq(Subject::getParentId, entity.getParentId());

        if (this.count(qw) > 0) {
            throw new ServiceException("科目名称已经存在！");
        }

        qw = Wrappers.lambdaQuery();
        qw.eq(Subject::getAccountSetsId, entity.getAccountSetsId());
        qw.eq(Subject::getCode, entity.getCode());

        if (this.count(qw) > 0) {
            throw new ServiceException("科目编码已经存在！");
        }

        return super.save(entity);
    }

    @Override
    public boolean remove(Wrapper<Subject> wrapper) {
        if (this.checkUse(getOne(wrapper).getId())) {
            throw new ServiceException("科目已被使用，不能删除！");
        }
        return super.remove(wrapper);
    }
}
