package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName("fxy_financial_ai_matching_history")
public class AiMatchingHistory {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer accountSetsId;

    private String businessType;

    private Integer businessId;

    private String matchedSubjects;

    private BigDecimal confidenceScore;

    private String aiReasoning;

    private Integer userFeedback;

    private Integer processingTime;

    private Date createTime;

    private Integer createUser;
}
