package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 银证归并组实体类
 */
@Data
@TableName(value = "fxy_financial_receipt_groups")
public class ReceiptGroup implements Serializable {
    
    /**
     * 组ID，使用UUID
     */
    @TableId(value = "group_id", type = IdType.ID_WORKER_STR)
    private String groupId;

    /**
     * 组名称
     */
    @TableField(value = "group_name")
    private String groupName;

    /**
     * 使用的归并规则ID
     */
    @TableField(value = "merge_rule_id")
    private String mergeRuleId;

    /**
     * 规则参数（JSON格式）
     */
    @TableField(value = "rule_params")
    private String ruleParams;

    /**
     * 组摘要信息
     */
    @TableField(value = "group_summary")
    private String groupSummary;

    /**
     * 组内总金额
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 组内项目数量
     */
    @TableField(value = "item_count")
    private Integer itemCount;

    /**
     * 组状态：ACTIVE-活跃，DISSOLVED-已解散
     */
    @TableField(value = "status")
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    private Date updatedAt;

    /**
     * 创建人ID
     */
    @TableField(value = "created_by")
    private Integer createdBy;

    /**
     * 账套ID
     */
    @TableField(value = "account_sets_id")
    private Integer accountSetsId;

    /**
     * 关联的归并规则（非数据库字段）
     */
    @TableField(exist = false)
    private MergeRule mergeRule;

    /**
     * 组内银证列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<BankReceipts> receipts;

    private static final long serialVersionUID = 1L;

    /**
     * 组状态枚举
     */
    public enum Status {
        ACTIVE("ACTIVE", "活跃"),
        DISSOLVED("DISSOLVED", "已解散");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
