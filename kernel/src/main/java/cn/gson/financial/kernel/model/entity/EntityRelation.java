package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 统一关联实体类
 */
@Data
@TableName(value = "fxy_financial_entity_relations")
public class EntityRelation implements Serializable {
    
    /**
     * 关联ID，使用UUID
     */
    @TableId(value = "relation_id", type = IdType.ID_WORKER_STR)
    private String relationId;

    /**
     * 源类型：DOCUMENT-票据，DOCUMENT_GROUP-票据组，RECEIPT-银证，RECEIPT_GROUP-银证组
     */
    @TableField(value = "source_type")
    private String sourceType;

    /**
     * 源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 目标类型：DOCUMENT-票据，DOCUMENT_GROUP-票据组，RECEIPT-银证，RECEIPT_GROUP-银证组
     */
    @TableField(value = "target_type")
    private String targetType;

    /**
     * 目标ID
     */
    @TableField(value = "target_id")
    private String targetId;

    /**
     * 关联类型
     */
    @TableField(value = "relation_type")
    private String relationType;

    /**
     * 关联金额（部分关联时使用）
     */
    @TableField(value = "relation_amount")
    private BigDecimal relationAmount;

    /**
     * 关联备注
     */
    @TableField(value = "relation_note")
    private String relationNote;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     * 创建人ID
     */
    @TableField(value = "created_by")
    private Integer createdBy;

    /**
     * 账套ID
     */
    @TableField(value = "account_sets_id")
    private Integer accountSetsId;

    private static final long serialVersionUID = 1L;

    /**
     * 实体类型枚举
     */
    public enum EntityType {
        DOCUMENT("DOCUMENT", "票据"),
        DOCUMENT_GROUP("DOCUMENT_GROUP", "票据组"),
        RECEIPT("RECEIPT", "银证"),
        RECEIPT_GROUP("RECEIPT_GROUP", "银证组");

        private final String code;
        private final String description;

        EntityType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 关联类型枚举
     */
    public enum RelationType {
        ASSOCIATED("ASSOCIATED", "关联"),
        MATCHED("MATCHED", "匹配"),
        PARTIAL("PARTIAL", "部分关联"),
        FULL("FULL", "完全关联");

        private final String code;
        private final String description;

        RelationType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
