package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.dto.UnifiedQueryDto;
import cn.gson.financial.kernel.service.UnifiedQueryService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 统一查询服务实现
 */
@Slf4j
@Service
public class UnifiedQueryServiceImpl implements UnifiedQueryService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public Page<Map<String, Object>> queryDocuments(Integer accountSetsId, UnifiedQueryDto queryDto) {
        log.info("票据统一查询，账套ID: {}", accountSetsId);
        
        List<String> entityTypes = new ArrayList<>();
        entityTypes.add("DOCUMENT");
        entityTypes.add("DOCUMENT_GROUP");
        
        String baseSql = buildUnifiedQuery(accountSetsId, entityTypes);
        
        // 添加查询条件
        baseSql = addQueryConditions(baseSql, queryDto.getCondition());
        
        // 添加关联关系筛选
        if (queryDto.getCondition().getIncludeRelations() != null && queryDto.getCondition().getIncludeRelations()) {
            baseSql = addRelationFilter(baseSql, true, queryDto.getCondition().getOnlyWithRelations());
        }
        
        // 添加归并组筛选
        baseSql = addMergeGroupFilter(baseSql, "DOCUMENT");
        
        // 查询优化
        baseSql = optimizeQuery(baseSql);
        
        return executePaginatedQuery(baseSql, queryDto.getPageParam());
    }

    @Override
    public Page<Map<String, Object>> queryReceipts(Integer accountSetsId, UnifiedQueryDto queryDto) {
        log.info("银证统一查询，账套ID: {}", accountSetsId);
        
        List<String> entityTypes = new ArrayList<>();
        entityTypes.add("RECEIPT");
        entityTypes.add("RECEIPT_GROUP");
        
        String baseSql = buildUnifiedQuery(accountSetsId, entityTypes);
        
        // 添加查询条件
        baseSql = addQueryConditions(baseSql, queryDto.getCondition());
        
        // 添加关联关系筛选
        if (queryDto.getCondition().getIncludeRelations() != null && queryDto.getCondition().getIncludeRelations()) {
            baseSql = addRelationFilter(baseSql, true, queryDto.getCondition().getOnlyWithRelations());
        }
        
        // 添加归并组筛选
        baseSql = addMergeGroupFilter(baseSql, "RECEIPT");
        
        // 查询优化
        baseSql = optimizeQuery(baseSql);
        
        return executePaginatedQuery(baseSql, queryDto.getPageParam());
    }

    @Override
    public Page<Map<String, Object>> queryCrossTypeRelations(Integer accountSetsId, UnifiedQueryDto queryDto) {
        log.info("跨类型关联查询，账套ID: {}", accountSetsId);
        
        String sql = "SELECT " +
                "er.relation_id, er.source_type, er.source_id, er.target_type, er.target_id, " +
                "er.relation_type, er.relation_amount, er.relation_note, er.created_at, " +
                "CASE " +
                "  WHEN er.source_type = 'DOCUMENT' THEN b.bill_no " +
                "  WHEN er.source_type = 'DOCUMENT_GROUP' THEN dg.group_name " +
                "  WHEN er.source_type = 'RECEIPT' THEN br.receipts_no " +
                "  WHEN er.source_type = 'RECEIPT_GROUP' THEN rg.group_name " +
                "END as source_name, " +
                "CASE " +
                "  WHEN er.target_type = 'DOCUMENT' THEN b2.bill_no " +
                "  WHEN er.target_type = 'DOCUMENT_GROUP' THEN dg2.group_name " +
                "  WHEN er.target_type = 'RECEIPT' THEN br2.receipts_no " +
                "  WHEN er.target_type = 'RECEIPT_GROUP' THEN rg2.group_name " +
                "END as target_name " +
                "FROM fxy_financial_entity_relations er " +
                "LEFT JOIN fxy_financial_bill b ON er.source_type = 'DOCUMENT' AND er.source_id = b.id " +
                "LEFT JOIN fxy_financial_document_groups dg ON er.source_type = 'DOCUMENT_GROUP' AND er.source_id = dg.group_id " +
                "LEFT JOIN fxy_financial_bank_receipts br ON er.source_type = 'RECEIPT' AND er.source_id = br.id " +
                "LEFT JOIN fxy_financial_receipt_groups rg ON er.source_type = 'RECEIPT_GROUP' AND er.source_id = rg.group_id " +
                "LEFT JOIN fxy_financial_bill b2 ON er.target_type = 'DOCUMENT' AND er.target_id = b2.id " +
                "LEFT JOIN fxy_financial_document_groups dg2 ON er.target_type = 'DOCUMENT_GROUP' AND er.target_id = dg2.group_id " +
                "LEFT JOIN fxy_financial_bank_receipts br2 ON er.target_type = 'RECEIPT' AND er.target_id = br2.id " +
                "LEFT JOIN fxy_financial_receipt_groups rg2 ON er.target_type = 'RECEIPT_GROUP' AND er.target_id = rg2.group_id " +
                "WHERE er.account_sets_id = " + accountSetsId + " " +
                "AND ((er.source_type LIKE 'DOCUMENT%' AND er.target_type LIKE 'RECEIPT%') " +
                "OR (er.source_type LIKE 'RECEIPT%' AND er.target_type LIKE 'DOCUMENT%'))";
        
        // 添加查询条件
        sql = addCrossTypeQueryConditions(sql, queryDto.getCondition());
        
        return executePaginatedQuery(sql, queryDto.getPageParam());
    }

    @Override
    public String buildUnifiedQuery(Integer accountSetsId, List<String> entityTypes) {
        StringBuilder sql = new StringBuilder();
        
        for (int i = 0; i < entityTypes.size(); i++) {
            if (i > 0) {
                sql.append(" UNION ALL ");
            }
            
            String entityType = entityTypes.get(i);
            switch (entityType) {
                case "DOCUMENT":
                    sql.append(buildDocumentQuery(accountSetsId));
                    break;
                case "DOCUMENT_GROUP":
                    sql.append(buildDocumentGroupQuery(accountSetsId));
                    break;
                case "RECEIPT":
                    sql.append(buildReceiptQuery(accountSetsId));
                    break;
                case "RECEIPT_GROUP":
                    sql.append(buildReceiptGroupQuery(accountSetsId));
                    break;
            }
        }
        
        return "(" + sql.toString() + ")";
    }

    @Override
    public String addRelationFilter(String baseSql, Boolean includeRelations, Boolean onlyWithRelations) {
        if (includeRelations == null || !includeRelations) {
            return baseSql;
        }
        
        String relationSql = "SELECT t.*, " +
                "GROUP_CONCAT(DISTINCT CONCAT(er.relation_type, ':', er.target_type, ':', er.target_id)) as relations " +
                "FROM " + baseSql + " t " +
                "LEFT JOIN fxy_financial_entity_relations er ON " +
                "((er.source_type = t.entity_type AND er.source_id = t.entity_id) OR " +
                "(er.target_type = t.entity_type AND er.target_id = t.entity_id))";
        
        if (onlyWithRelations != null && onlyWithRelations) {
            relationSql += " WHERE er.relation_id IS NOT NULL";
        }
        
        relationSql += " GROUP BY t.entity_type, t.entity_id";
        
        return relationSql;
    }

    @Override
    public String addMergeGroupFilter(String baseSql, String entityType) {
        // 为查询添加归并组相关信息
        return baseSql;
    }

    @Override
    public String optimizeQuery(String sql) {
        // 查询优化逻辑
        return sql;
    }

    @Override
    public Page<Map<String, Object>> executePaginatedQuery(String sql, UnifiedQueryDto.PageParam pageParam) {
        if (pageParam == null) {
            pageParam = new UnifiedQueryDto.PageParam();
        }
        
        // 计算总数
        String countSql = "SELECT COUNT(*) FROM (" + sql + ") t";
        Long total = jdbcTemplate.queryForObject(countSql, Long.class);
        
        // 添加排序和分页
        String orderBy = pageParam.getOrderBy() != null ? pageParam.getOrderBy() : "created_at";
        String orderDirection = pageParam.getOrderDirection() != null ? pageParam.getOrderDirection() : "DESC";
        
        sql += " ORDER BY " + orderBy + " " + orderDirection;
        
        int offset = (pageParam.getPageNum() - 1) * pageParam.getPageSize();
        sql += " LIMIT " + pageParam.getPageSize() + " OFFSET " + offset;
        
        List<Map<String, Object>> records = jdbcTemplate.queryForList(sql);
        
        Page<Map<String, Object>> page = new Page<>(pageParam.getPageNum(), pageParam.getPageSize());
        page.setTotal(total != null ? total : 0);
        page.setRecords(records);
        
        return page;
    }

    // 私有辅助方法
    private String buildDocumentQuery(Integer accountSetsId) {
        return "SELECT " +
                "'DOCUMENT' as entity_type, " +
                "CAST(id AS CHAR) as entity_id, " +
                "bill_no as entity_no, " +
                "bill_date as entity_date, " +
                "type as entity_type_name, " +
                "amount, " +
                "issuer as counterparty, " +
                "summary, " +
                "status, " +
                "doc_group_id as group_id, " +
                "create_time as created_at " +
                "FROM fxy_financial_bill " +
                "WHERE account_sets_id = " + accountSetsId;
    }
    
    private String buildDocumentGroupQuery(Integer accountSetsId) {
        return "SELECT " +
                "'DOCUMENT_GROUP' as entity_type, " +
                "group_id as entity_id, " +
                "group_name as entity_no, " +
                "created_at as entity_date, " +
                "'归并组' as entity_type_name, " +
                "total_amount as amount, " +
                "'' as counterparty, " +
                "group_summary as summary, " +
                "status, " +
                "group_id as group_id, " +
                "created_at " +
                "FROM fxy_financial_document_groups " +
                "WHERE account_sets_id = " + accountSetsId;
    }
    
    private String buildReceiptQuery(Integer accountSetsId) {
        return "SELECT " +
                "'RECEIPT' as entity_type, " +
                "CAST(id AS CHAR) as entity_id, " +
                "receipts_no as entity_no, " +
                "receipts_date as entity_date, " +
                "type as entity_type_name, " +
                "amount, " +
                "counterparty, " +
                "summary, " +
                "'正常' as status, " +
                "receipt_group_id as group_id, " +
                "create_date as created_at " +
                "FROM fxy_financial_bank_receipts " +
                "WHERE account_sets_id = " + accountSetsId;
    }
    
    private String buildReceiptGroupQuery(Integer accountSetsId) {
        return "SELECT " +
                "'RECEIPT_GROUP' as entity_type, " +
                "group_id as entity_id, " +
                "group_name as entity_no, " +
                "created_at as entity_date, " +
                "'归并组' as entity_type_name, " +
                "total_amount as amount, " +
                "'' as counterparty, " +
                "group_summary as summary, " +
                "status, " +
                "group_id as group_id, " +
                "created_at " +
                "FROM fxy_financial_receipt_groups " +
                "WHERE account_sets_id = " + accountSetsId;
    }
    
    private String addQueryConditions(String sql, UnifiedQueryDto.QueryCondition condition) {
        if (condition == null) {
            return sql;
        }
        
        StringBuilder whereClauses = new StringBuilder();
        
        // 关键字搜索
        if (condition.getKeyword() != null && !condition.getKeyword().trim().isEmpty()) {
            whereClauses.append(" AND (entity_no LIKE '%").append(condition.getKeyword()).append("%'")
                       .append(" OR summary LIKE '%").append(condition.getKeyword()).append("%'")
                       .append(" OR counterparty LIKE '%").append(condition.getKeyword()).append("%')");
        }
        
        // 日期范围
        if (condition.getStartDate() != null) {
            whereClauses.append(" AND entity_date >= '").append(condition.getStartDate()).append("'");
        }
        if (condition.getEndDate() != null) {
            whereClauses.append(" AND entity_date <= '").append(condition.getEndDate()).append("'");
        }
        
        // 金额范围
        if (condition.getMinAmount() != null) {
            whereClauses.append(" AND amount >= ").append(condition.getMinAmount());
        }
        if (condition.getMaxAmount() != null) {
            whereClauses.append(" AND amount <= ").append(condition.getMaxAmount());
        }
        
        if (whereClauses.length() > 0) {
            sql = "SELECT * FROM (" + sql + ") t WHERE 1=1" + whereClauses.toString();
        }
        
        return sql;
    }
    
    private String addCrossTypeQueryConditions(String sql, UnifiedQueryDto.QueryCondition condition) {
        // 为跨类型关联查询添加条件
        return sql;
    }
}
