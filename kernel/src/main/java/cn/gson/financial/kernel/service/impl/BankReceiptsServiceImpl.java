package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.mapper.BankReceiptsMapper;
import cn.gson.financial.kernel.model.vo.UserVo;
import cn.gson.financial.kernel.service.BankReceiptsService;
import cn.gson.financial.kernel.service.AccountSetsService;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class BankReceiptsServiceImpl extends ServiceImpl<BankReceiptsMapper, BankReceipts> implements BankReceiptsService {

    @Autowired
    private AccountSetsService accountSetsService;


    
    @Override
    public int batchInsert(List<BankReceipts> list) {
        return baseMapper.batchInsert(list);
    }

    @Override
    public String generateReceiptsNo(Integer accountSetsId, Date receiptsDate) {
        // 添加空值检查
        if (receiptsDate == null) {
            receiptsDate = new Date();
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(receiptsDate);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        
        // 添加重试机制防止并发重复
        int maxRetries = 3;
        for (int i = 0; i < maxRetries; i++) {
            // 查询当月最大银行回单号
            LambdaQueryWrapper<BankReceipts> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(BankReceipts::getAccountSetsId, accountSetsId)
                       .eq(BankReceipts::getReceiptsYear, year)
                       .eq(BankReceipts::getReceiptsMonth, month)
                       .orderByDesc(BankReceipts::getReceiptsNo)
                       .last("LIMIT 1");
            
            BankReceipts lastReceipts = getOne(queryWrapper);
            int nextNo = 1;
            
            if (lastReceipts != null && lastReceipts.getReceiptsNo() != null) {
                try {
                    String receiptsNo = lastReceipts.getReceiptsNo();
                    String lastNoStr;

                    // 检查回单号格式，支持多种格式
                    if (receiptsNo.contains("-")) {
                        // 标准格式：YHD202501-0001
                        lastNoStr = receiptsNo.substring(receiptsNo.lastIndexOf("-") + 1);
                    } else if (receiptsNo.matches("^[A-Z]+\\d+$")) {
                        // 旧格式：类似XS202506003，提取末尾数字部分
                        lastNoStr = receiptsNo.replaceAll("^[A-Z]+", "");
                        // 如果是年月+序号格式，只取最后3位作为序号
                        if (lastNoStr.length() > 3) {
                            lastNoStr = lastNoStr.substring(lastNoStr.length() - 3);
                        }
                    } else {
                        // 其他格式，尝试提取末尾的数字
                        lastNoStr = receiptsNo.replaceAll(".*?(\\d+)$", "$1");
                    }

                    nextNo = Integer.parseInt(lastNoStr) + 1;
                } catch (Exception e) {
                    log.warn("解析银行回单编号失败，回单号格式: {}, 使用默认值1", lastReceipts.getReceiptsNo(), e);
                }
            }
            
            String receiptsNo = String.format("YHD%04d%02d-%04d", year, month, nextNo);
            
            // 检查是否已存在
            LambdaQueryWrapper<BankReceipts> checkWrapper = Wrappers.lambdaQuery();
            checkWrapper.eq(BankReceipts::getAccountSetsId, accountSetsId)
                       .eq(BankReceipts::getReceiptsNo, receiptsNo);
            
            if (count(checkWrapper) == 0) {
                return receiptsNo;
            }
            
            // 如果存在重复，稍作延迟后重试
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        // 如果重试失败，使用时间戳后缀
        long timestamp = System.currentTimeMillis() % 10000;
        return String.format("YHD%04d%02d-%04d", year, month, (int)timestamp);
    }

    @Override
    @Transactional
    public BankReceipts save(Integer accountSetsId, BankReceipts bankReceipts, UserVo currentUser) {
        // 设置账套ID
        bankReceipts.setAccountSetsId(accountSetsId);

        // 添加空值检查
        if (bankReceipts.getReceiptsDate() == null) {
            bankReceipts.setReceiptsDate(new Date());
        }

        // 处理必填字段：转账日期
        if (bankReceipts.getTransferDate() == null) {
            // 如果没有转账日期，使用回单日期作为默认值
            bankReceipts.setTransferDate(bankReceipts.getReceiptsDate());
            log.info("转账日期为空，使用回单日期作为默认值");
        }

        // 检查银行回单是否重复
        String duplicateInfo = checkBankReceiptDuplicate(accountSetsId,
                bankReceipts.getSerialNumber(),
                bankReceipts.getAmount(),
                bankReceipts.getTransferDate(),
                bankReceipts.getPayerAccount(),
                null);
        if (duplicateInfo != null) {
            throw new RuntimeException("银行回单重复：" + duplicateInfo + "，请检查后重新输入");
        }

        // 处理必填字段：摘要
        if (bankReceipts.getSummary() == null || bankReceipts.getSummary().trim().isEmpty()) {
            // 如果没有摘要，根据回单标题生成默认摘要
            if (bankReceipts.getReceiptTitle() != null && !bankReceipts.getReceiptTitle().trim().isEmpty()) {
                bankReceipts.setSummary(bankReceipts.getReceiptTitle());
            } else {
                bankReceipts.setSummary("银行回单");
            }
            log.info("摘要为空，设置默认摘要: {}", bankReceipts.getSummary());
        }

        // 处理必填字段：张数（默认1）
        if (bankReceipts.getReceiptNum() == null || bankReceipts.getReceiptNum() <= 0) {
            bankReceipts.setReceiptNum(1);
            log.info("张数为空或无效，设置默认值: 1");
        }

        // 处理必填字段：类型
        if (bankReceipts.getType() == null || bankReceipts.getType().trim().isEmpty()) {
            // 如果没有类型，根据回单标题进行简单判断
            if (bankReceipts.getReceiptTitle() != null) {
                String receiptTitle = bankReceipts.getReceiptTitle();
                if (receiptTitle.contains("收入") || receiptTitle.contains("转入") || receiptTitle.contains("存入") || receiptTitle.contains("贷方")) {
                    bankReceipts.setType("收入");
                } else if (receiptTitle.contains("支出") || receiptTitle.contains("转出") || receiptTitle.contains("支付") || receiptTitle.contains("借方")) {
                    bankReceipts.setType("支出");
                } else {
                    bankReceipts.setType("支出"); // 默认为支出
                }
            } else {
                bankReceipts.setType("支出"); // 默认为支出
            }
            log.info("类型为空，设置默认类型: {}", bankReceipts.getType());
        }

        // 设置年月 - 使用账套当前期间而不是回单日期
        Integer[] currentPeriod = getCurrentAccountPeriod(accountSetsId, currentUser);
        bankReceipts.setReceiptsYear(currentPeriod[0]);
        bankReceipts.setReceiptsMonth(currentPeriod[1]);

        // 生成单据编号
        if (bankReceipts.getReceiptsNo() == null || bankReceipts.getReceiptsNo().isEmpty()) {
            bankReceipts.setReceiptsNo(generateReceiptsNo(accountSetsId, bankReceipts.getReceiptsDate()));
        }

        // 保存银行回单基本信息
        boolean saved = super.save(bankReceipts);
        if (!saved) {
            throw new RuntimeException("银行回单保存失败");
        }

        return bankReceipts;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BankReceipts update(Integer accountSetsId, BankReceipts bankReceipts) {
        // 添加空值检查
        if (bankReceipts.getReceiptsDate() == null) {
            bankReceipts.setReceiptsDate(new Date());
        }

        // 处理必填字段：转账日期
        if (bankReceipts.getTransferDate() == null) {
            // 如果没有转账日期，使用回单日期作为默认值
            bankReceipts.setTransferDate(bankReceipts.getReceiptsDate());
            log.info("转账日期为空，使用回单日期作为默认值");
        }

        // 检查银行回单是否重复
        String duplicateInfo = checkBankReceiptDuplicate(accountSetsId,
                bankReceipts.getSerialNumber(),
                bankReceipts.getAmount(),
                bankReceipts.getTransferDate(),
                bankReceipts.getPayerAccount(),
                bankReceipts.getId());
        if (duplicateInfo != null) {
            throw new RuntimeException("银行回单重复：" + duplicateInfo + "，请检查后重新输入");
        }

        // 设置年月 - 使用账套当前期间而不是回单日期
        Integer[] currentPeriod = getCurrentAccountPeriod(accountSetsId, null);
        bankReceipts.setReceiptsYear(currentPeriod[0]);
        bankReceipts.setReceiptsMonth(currentPeriod[1]);

        // 更新银行回单
        updateById(bankReceipts);

        return bankReceipts;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer accountSetsId, Integer id) {
        // 检查银行回单是否存在且属于当前账套
        LambdaQueryWrapper<BankReceipts> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BankReceipts::getId, id);
        queryWrapper.eq(BankReceipts::getAccountSetsId, accountSetsId);
        
        BankReceipts bankReceipts = getOne(queryWrapper);
        if (bankReceipts == null) {
            throw new RuntimeException("银行回单不存在或不属于当前账套");
        }
        
        // 删除银行回单
        removeById(id);
    }
    
    @Override
    public BankReceipts getByReceiptsNo(String receiptsNo, Integer accountSetsId) {
        QueryWrapper<BankReceipts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("receipts_no", receiptsNo)
                   .eq("account_sets_id", accountSetsId);
        return getOne(queryWrapper);
    }

    @Override
    public String checkBankReceiptDuplicate(Integer accountSetsId, String serialNumber, Double amount,
                                          Date transferDate, String payerAccount, Integer excludeReceiptId) {
        try {
            // 如果关键字段都缺失，不进行重复检测
            if ((serialNumber == null || serialNumber.trim().isEmpty()) && amount == null) {
                return null;
            }

            // 构建查询条件
            LambdaQueryWrapper<BankReceipts> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BankReceipts::getAccountSetsId, accountSetsId);

            // 如果是编辑操作，排除当前银行回单
            if (excludeReceiptId != null) {
                wrapper.ne(BankReceipts::getId, excludeReceiptId);
            }

            // 优先使用流水号进行精确匹配
            if (serialNumber != null && !serialNumber.trim().isEmpty()) {
                wrapper.eq(BankReceipts::getSerialNumber, serialNumber.trim());
                long count = count(wrapper);
                log.info("银行回单重复检测 - 流水号: {}, 账套ID: {}, 查询结果: {}", serialNumber.trim(), accountSetsId, count);
                if (count > 0) {
                    String duplicateInfo = String.format("流水号 '%s' 已存在", serialNumber);
                    log.info("发现重复银行回单（流水号匹配）: {}", serialNumber);
                    return duplicateInfo;
                }
            }

            // 如果流水号没有重复，使用组合条件检测
            if (amount != null && transferDate != null && payerAccount != null && !payerAccount.trim().isEmpty()) {
                LambdaQueryWrapper<BankReceipts> wrapper2 = new LambdaQueryWrapper<>();
                wrapper2.eq(BankReceipts::getAccountSetsId, accountSetsId)
                        .eq(BankReceipts::getAmount, amount)
                        .eq(BankReceipts::getTransferDate, transferDate)
                        .eq(BankReceipts::getPayerAccount, payerAccount.trim());

                // 如果是编辑操作，排除当前银行回单
                if (excludeReceiptId != null) {
                    wrapper2.ne(BankReceipts::getId, excludeReceiptId);
                }

                long count = count(wrapper2);
                if (count > 0) {
                    String duplicateInfo = String.format("相同金额 %.2f 元、日期 %s、付款账号 %s 的回单已存在",
                            amount, transferDate, payerAccount);
                    log.info("发现重复银行回单（组合条件匹配）: 金额={}, 日期={}, 付款账号={}",
                            amount, transferDate, payerAccount);
                    return duplicateInfo;
                }
            }

            return null;

        } catch (Exception e) {
            log.error("检查银行回单重复时发生异常", e);
            // 异常时不阻止保存，但记录日志
            return null;
        }
    }

    /**
     * 获取账套当前期间
     * @param accountSetsId 账套ID
     * @param currentUser 当前用户（可为null）
     * @return [年份, 月份]
     */
    private Integer[] getCurrentAccountPeriod(Integer accountSetsId, UserVo currentUser) {
        try {
            // 优先从currentUser获取账套信息
            if (currentUser != null && currentUser.getAccountSets() != null
                && currentUser.getAccountSets().getCurrentAccountDate() != null) {
                LocalDate currentAccountDate = currentUser.getAccountSets().getCurrentAccountDate().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate();
                return new Integer[]{currentAccountDate.getYear(), currentAccountDate.getMonthValue()};
            }

            // 从数据库获取账套信息
            AccountSets accountSets = accountSetsService.getById(accountSetsId);
            if (accountSets != null && accountSets.getCurrentAccountDate() != null) {
                LocalDate currentAccountDate = accountSets.getCurrentAccountDate().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate();
                log.info("使用账套当前期间: {}-{}", currentAccountDate.getYear(), currentAccountDate.getMonthValue());
                return new Integer[]{currentAccountDate.getYear(), currentAccountDate.getMonthValue()};
            } else {
                // 如果没有当前期间，使用当前日期
                LocalDate now = LocalDate.now();
                log.warn("账套当前期间为空，使用当前日期: {}-{}", now.getYear(), now.getMonthValue());
                return new Integer[]{now.getYear(), now.getMonthValue()};
            }
        } catch (Exception e) {
            log.error("获取账套当前期间失败，使用当前日期", e);
            LocalDate now = LocalDate.now();
            return new Integer[]{now.getYear(), now.getMonthValue()};
        }
    }
}
