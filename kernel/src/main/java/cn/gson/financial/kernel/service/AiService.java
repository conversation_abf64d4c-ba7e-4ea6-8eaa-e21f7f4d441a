package cn.gson.financial.kernel.service;

import java.util.List;
import java.util.Map;

/**
 * AI服务接口
 * 提供统一的AI调用接口
 */
public interface AiService {

    /**
     * 检查AI服务是否可用
     */
    boolean isAvailable();

    /**
     * 检查指定用户的AI服务是否可用
     */
    boolean isAvailable(Integer userId);

    /**
     * 获取可用模型列表
     */
    List<Map<String, Object>> getAvailableModels();

    /**
     * 获取指定用户的可用模型列表
     */
    List<Map<String, Object>> getAvailableModels(Integer userId);

    /**
     * 发送聊天请求
     */
    String chat(String prompt);

    /**
     * 发送聊天请求（带参数）
     */
    String chat(String prompt, Map<String, Object> parameters);

    /**
     * 发送聊天请求（指定用户）
     */
    String chat(String prompt, Integer userId);

    /**
     * 发送聊天请求（指定用户，带参数）
     */
    String chat(String prompt, Map<String, Object> parameters, Integer userId);

    /**
     * 分析票据归并
     */
    List<Map<String, Object>> analyzeBillMerge(List<Map<String, Object>> bills, Map<String, Object> config);

    /**
     * 分析银行回单归并
     */
    List<Map<String, Object>> analyzeReceiptMerge(List<Map<String, Object>> receipts, Map<String, Object> config);

    /**
     * 分析混合归并
     */
    List<Map<String, Object>> analyzeMixedMerge(List<Map<String, Object>> bills, List<Map<String, Object>> receipts, Map<String, Object> config);

    /**
     * 分析关联关系
     */
    List<Map<String, Object>> analyzeRelations(List<Map<String, Object>> bills, List<Map<String, Object>> receipts, String mode, Map<String, Object> config);

    /**
     * 生成文本嵌入向量
     */
    List<Double> generateEmbedding(String text);

    /**
     * 计算文本相似度
     */
    double calculateSimilarity(String text1, String text2);
}
