package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理员角色实体类
 * 对应表：fxy_financial_admin_roles
 */
@Data
@TableName("fxy_financial_admin_roles")
public class AdminRole implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;
    
    /**
     * 角色编码
     */
    @TableField("role_code")
    private String roleCode;
    
    /**
     * 角色描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 权限列表(JSON格式)
     */
    @TableField("permissions")
    private String permissions;
    
    /**
     * 状态：1启用，0禁用
     */
    @TableField("status")
    private Boolean status;
    
    /**
     * 是否系统内置角色
     */
    @TableField("is_system")
    private Boolean isSystem;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 创建人ID
     */
    @TableField("creator_id")
    private Integer creatorId;
    
    /**
     * 权限列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<String> permissionList;
}
