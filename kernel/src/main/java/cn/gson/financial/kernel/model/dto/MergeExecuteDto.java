package cn.gson.financial.kernel.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 归并执行DTO
 */
@Data
public class MergeExecuteDto {
    
    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 归并组名称
     */
    private String groupName;

    /**
     * 是否异步执行
     */
    private Boolean async = false;
    
    /**
     * 手动归并组列表（可选）
     */
    private List<ManualMergeGroup> manualGroups;
    
    /**
     * 手动归并组
     */
    @Data
    public static class ManualMergeGroup {
        /**
         * 组名称
         */
        private String groupName;
        
        /**
         * 项目ID列表
         */
        private List<String> itemIds;
        
        /**
         * 项目类型：DOCUMENT-票据，RECEIPT-银证
         */
        private String itemType;
    }
}
