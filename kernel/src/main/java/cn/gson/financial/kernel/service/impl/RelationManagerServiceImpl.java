package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.dto.RelationCreateDto;
import cn.gson.financial.kernel.model.entity.EntityRelation;
import cn.gson.financial.kernel.model.mapper.EntityRelationMapper;
import cn.gson.financial.kernel.service.RelationManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 关联管理服务实现
 */
@Slf4j
@Service
public class RelationManagerServiceImpl implements RelationManagerService {

    @Autowired
    private EntityRelationMapper entityRelationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createRelation(Integer accountSetsId, RelationCreateDto createDto, Integer currentUserId) {
        log.info("创建关联关系，账套ID: {}, 源: {}:{}, 目标: {}:{}", 
                accountSetsId, createDto.getSourceType(), createDto.getSourceId(),
                createDto.getTargetType(), createDto.getTargetId());

        // 验证关联关系有效性
        if (!validateRelation(accountSetsId, createDto.getSourceType(), createDto.getSourceId(),
                createDto.getTargetType(), createDto.getTargetId())) {
            throw new RuntimeException("关联关系验证失败");
        }

        // 检查关联关系是否已存在
        int existCount = entityRelationMapper.checkRelationExists(
                createDto.getSourceType(), createDto.getSourceId(),
                createDto.getTargetType(), createDto.getTargetId(),
                accountSetsId);
        
        if (existCount > 0) {
            throw new RuntimeException("关联关系已存在");
        }

        // 创建关联关系
        EntityRelation relation = new EntityRelation();
        relation.setSourceType(createDto.getSourceType());
        relation.setSourceId(createDto.getSourceId());
        relation.setTargetType(createDto.getTargetType());
        relation.setTargetId(createDto.getTargetId());
        relation.setRelationType(createDto.getRelationType());
        relation.setRelationAmount(createDto.getRelationAmount());
        relation.setRelationNote(createDto.getRelationNote());
        relation.setCreatedBy(currentUserId);
        relation.setAccountSetsId(accountSetsId);

        entityRelationMapper.insert(relation);
        
        return relation.getRelationId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> createBatchRelations(Integer accountSetsId, RelationCreateDto.BatchRelationCreateDto batchCreateDto, Integer currentUserId) {
        log.info("批量创建关联关系，账套ID: {}, 源: {}:{}, 目标数量: {}", 
                accountSetsId, batchCreateDto.getSourceType(), batchCreateDto.getSourceId(),
                batchCreateDto.getTargets().size());

        List<String> relationIds = new ArrayList<>();

        for (RelationCreateDto.BatchRelationCreateDto.TargetRelation target : batchCreateDto.getTargets()) {
            try {
                RelationCreateDto createDto = new RelationCreateDto();
                createDto.setSourceType(batchCreateDto.getSourceType());
                createDto.setSourceId(batchCreateDto.getSourceId());
                createDto.setTargetType(target.getTargetType());
                createDto.setTargetId(target.getTargetId());
                createDto.setRelationType(target.getRelationType());
                createDto.setRelationAmount(target.getRelationAmount());
                createDto.setRelationNote(target.getRelationNote());

                String relationId = createRelation(accountSetsId, createDto, currentUserId);
                relationIds.add(relationId);
            } catch (Exception e) {
                log.warn("创建关联关系失败: {}:{} -> {}:{}, 错误: {}", 
                        batchCreateDto.getSourceType(), batchCreateDto.getSourceId(),
                        target.getTargetType(), target.getTargetId(), e.getMessage());
            }
        }

        return relationIds;
    }

    @Override
    public List<EntityRelation> queryDocumentRelations(Integer accountSetsId, String entityId, String entityType) {
        log.info("查询票据相关关联，账套ID: {}, 实体: {}:{}", accountSetsId, entityType, entityId);
        
        if (!entityType.startsWith("DOCUMENT")) {
            throw new RuntimeException("实体类型必须是票据相关类型");
        }

        return entityRelationMapper.findAllRelationsByEntity(entityType, entityId, accountSetsId);
    }

    @Override
    public List<EntityRelation> queryReceiptRelations(Integer accountSetsId, String entityId, String entityType) {
        log.info("查询银证相关关联，账套ID: {}, 实体: {}:{}", accountSetsId, entityType, entityId);
        
        if (!entityType.startsWith("RECEIPT")) {
            throw new RuntimeException("实体类型必须是银证相关类型");
        }

        return entityRelationMapper.findAllRelationsByEntity(entityType, entityId, accountSetsId);
    }

    @Override
    public List<EntityRelation> queryCrossTypeRelations(Integer accountSetsId, String entityId, String entityType) {
        log.info("查询跨类型关联，账套ID: {}, 实体: {}:{}", accountSetsId, entityType, entityId);
        
        List<EntityRelation> allRelations = entityRelationMapper.findAllRelationsByEntity(entityType, entityId, accountSetsId);
        
        // 过滤出跨类型关联
        List<EntityRelation> crossTypeRelations = new ArrayList<>();
        for (EntityRelation relation : allRelations) {
            if (isCrossTypeRelation(relation)) {
                crossTypeRelations.add(relation);
            }
        }
        
        return crossTypeRelations;
    }

    @Override
    public List<EntityRelation> queryAllRelations(Integer accountSetsId, String entityId, String entityType) {
        log.info("查询所有关联关系，账套ID: {}, 实体: {}:{}", accountSetsId, entityType, entityId);
        
        return entityRelationMapper.findAllRelationsByEntity(entityType, entityId, accountSetsId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRelation(Integer accountSetsId, String relationId) {
        log.info("删除关联关系，账套ID: {}, 关联ID: {}", accountSetsId, relationId);
        
        // 验证关联关系存在且属于当前账套
        EntityRelation relation = entityRelationMapper.selectById(relationId);
        if (relation == null || !relation.getAccountSetsId().equals(accountSetsId)) {
            throw new RuntimeException("关联关系不存在或不属于当前账套");
        }
        
        int result = entityRelationMapper.deleteById(relationId);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAllRelationsByEntity(Integer accountSetsId, String entityId, String entityType) {
        log.info("删除实体的所有关联关系，账套ID: {}, 实体: {}:{}", accountSetsId, entityType, entityId);
        
        return entityRelationMapper.deleteAllRelationsByEntity(entityType, entityId, accountSetsId);
    }

    @Override
    public boolean validateRelation(Integer accountSetsId, String sourceType, String sourceId, String targetType, String targetId) {
        log.debug("验证关联关系，账套ID: {}, 源: {}:{}, 目标: {}:{}", 
                accountSetsId, sourceType, sourceId, targetType, targetId);
        
        // 基本验证
        if (sourceType == null || sourceId == null || targetType == null || targetId == null) {
            return false;
        }
        
        // 不能自己关联自己
        if (sourceType.equals(targetType) && sourceId.equals(targetId)) {
            return false;
        }
        
        // 验证实体类型有效性
        if (!isValidEntityType(sourceType) || !isValidEntityType(targetType)) {
            return false;
        }
        
        // TODO: 验证实体是否存在
        // TODO: 检查循环依赖
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cascadeUpdateOnMerge(Integer accountSetsId, String oldEntityId, String newGroupId, String entityType) {
        log.info("归并时级联更新关联关系，账套ID: {}, 原实体ID: {}, 新组ID: {}, 实体类型: {}", 
                accountSetsId, oldEntityId, newGroupId, entityType);
        
        // 查询原实体的所有关联关系
        List<EntityRelation> relations = entityRelationMapper.findAllRelationsByEntity(entityType, oldEntityId, accountSetsId);
        
        int updateCount = 0;
        String newEntityType = entityType.contains("DOCUMENT") ? "DOCUMENT_GROUP" : "RECEIPT_GROUP";
        
        for (EntityRelation relation : relations) {
            // 更新关联关系，将原实体替换为新组
            if (relation.getSourceType().equals(entityType) && relation.getSourceId().equals(oldEntityId)) {
                relation.setSourceType(newEntityType);
                relation.setSourceId(newGroupId);
            } else if (relation.getTargetType().equals(entityType) && relation.getTargetId().equals(oldEntityId)) {
                relation.setTargetType(newEntityType);
                relation.setTargetId(newGroupId);
            }
            
            entityRelationMapper.updateById(relation);
            updateCount++;
        }
        
        return updateCount;
    }

    // 私有辅助方法
    private boolean isCrossTypeRelation(EntityRelation relation) {
        boolean sourceIsDocument = relation.getSourceType().startsWith("DOCUMENT");
        boolean targetIsDocument = relation.getTargetType().startsWith("DOCUMENT");
        boolean sourceIsReceipt = relation.getSourceType().startsWith("RECEIPT");
        boolean targetIsReceipt = relation.getTargetType().startsWith("RECEIPT");
        
        return (sourceIsDocument && targetIsReceipt) || (sourceIsReceipt && targetIsDocument);
    }
    
    private boolean isValidEntityType(String entityType) {
        return EntityRelation.EntityType.DOCUMENT.getCode().equals(entityType) ||
               EntityRelation.EntityType.DOCUMENT_GROUP.getCode().equals(entityType) ||
               EntityRelation.EntityType.RECEIPT.getCode().equals(entityType) ||
               EntityRelation.EntityType.RECEIPT_GROUP.getCode().equals(entityType);
    }
}
