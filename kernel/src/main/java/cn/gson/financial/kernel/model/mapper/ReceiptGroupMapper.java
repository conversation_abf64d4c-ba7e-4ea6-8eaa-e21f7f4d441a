package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.ReceiptGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 银证归并组Mapper接口
 */
@Mapper
public interface ReceiptGroupMapper extends BaseMapper<ReceiptGroup> {

    /**
     * 根据账套ID查询活跃的银证归并组
     */
    @Select("SELECT * FROM fxy_financial_receipt_groups " +
            "WHERE account_sets_id = #{accountSetsId} " +
            "AND status = 'ACTIVE' " +
            "ORDER BY created_at DESC")
    List<ReceiptGroup> findActiveGroupsByAccountSets(@Param("accountSetsId") Integer accountSetsId);

    /**
     * 根据归并规则ID查询银证归并组
     */
    @Select("SELECT * FROM fxy_financial_receipt_groups " +
            "WHERE merge_rule_id = #{ruleId} " +
            "AND status = 'ACTIVE' " +
            "ORDER BY created_at DESC")
    List<ReceiptGroup> findGroupsByRuleId(@Param("ruleId") String ruleId);

    /**
     * 更新归并组统计信息
     */
    @Update("UPDATE fxy_financial_receipt_groups " +
            "SET total_amount = #{totalAmount}, item_count = #{itemCount}, updated_at = NOW() " +
            "WHERE group_id = #{groupId}")
    int updateGroupStatistics(@Param("groupId") String groupId,
                             @Param("totalAmount") BigDecimal totalAmount,
                             @Param("itemCount") Integer itemCount);

    /**
     * 解散归并组
     */
    @Update("UPDATE fxy_financial_receipt_groups " +
            "SET status = 'DISSOLVED', updated_at = NOW() " +
            "WHERE group_id = #{groupId}")
    int dissolveGroup(@Param("groupId") String groupId);
}
