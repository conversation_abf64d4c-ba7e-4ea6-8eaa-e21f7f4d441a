package cn.gson.financial.kernel.model.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 票据实体类
 */
@Data
@TableName(value = "fxy_financial_bill")
public class Bill implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 票据编号
     */
    @TableField(value = "bill_no")
    private String billNo;

    /**
     * 票据日期
     */
    @TableField(value = "bill_date")
    private Date billDate;

    /**
     * 票据类型（发票/收据/欠条/机票等）
     */
    @TableField(value = "type")
    private String type;

    /**
     * 金额
     */
    @TableField(value = "amount")
    private Double amount;

    /**
     * 开票方/收款方
     */
    @TableField(value = "issuer")
    private String issuer;

    /**
     * 收票方/付款方
     */
    @TableField(value = "recipient")
    private String recipient;

    /**
     * 摘要/用途
     */
    @TableField(value = "summary")
    private String summary;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 票据状态（未使用/已使用/已作废）
     */
    @TableField(value = "status")
    private String status;

    // 关联凭证ID字段已删除，使用entity_relations表管理关联关系

    /**
     * 票据归并组ID
     */
    @TableField(value = "doc_group_id")
    private String docGroupId;

    /**
     * 账套ID
     */
    @TableField(value = "account_sets_id")
    private Integer accountSetsId;

    /**
     * 票据年份
     */
    @TableField(value = "bill_year")
    private Integer billYear;

    /**
     * 票据月份
     */
    @TableField(value = "bill_month")
    private Integer billMonth;

    /**
     * 发票号码
     */
    @TableField(value = "invoice_number")
    private String invoiceNumber;

    /**
     * 附件路径
     */
    @TableField(value = "attachment_path")
    private String attachmentPath;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 税率
     */
    @TableField(value = "tax_rate")
    private BigDecimal taxRate;

    /**
     * 合计税额
     */
    @TableField(value = "total_tax_amount")
    private BigDecimal totalTaxAmount;

    /**
     * 小写金额
     */
    @TableField(value = "amount_in_words")
    private String amountInWords;

    /**
     * OCR识别信息
     */
    @TableField(value = "ocr_recognition_info")
    private String ocrRecognitionInfo;

    private static final long serialVersionUID = 1L;
}