package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 批量导入明细实体
 */
@Data
@TableName("fxy_financial_batch_import_detail")
public class BatchImportDetail {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;
    
    /**
     * 原始文件名
     */
    @TableField("file_name")
    private String fileName;
    
    /**
     * 原始文件URL
     */
    @TableField("file_url")
    private String fileUrl;
    
    /**
     * 文件类型：PDF-PDF文件，IMAGE-图片文件
     */
    @TableField("file_type")
    private String fileType;
    
    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;
    
    /**
     * PDF页码（图片文件为NULL）
     */
    @TableField("page_number")
    private Integer pageNumber;
    
    /**
     * 解析后的图片URL
     */
    @TableField("image_url")
    private String imageUrl;
    
    /**
     * 图片宽度
     */
    @TableField("image_width")
    private Integer imageWidth;
    
    /**
     * 图片高度
     */
    @TableField("image_height")
    private Integer imageHeight;

    /**
     * 子图片索引（拆分后的图片序号）
     */
    @TableField("sub_image_index")
    private Integer subImageIndex;

    /**
     * 原始图片URL（拆分前的图片）
     */
    @TableField("original_image_url")
    private String originalImageUrl;

    /**
     * 是否为拆分图片
     */
    @TableField("is_split")
    private Boolean isSplit;

    /**
     * 账套ID
     */
    @TableField("account_sets_id")
    private Integer accountSetsId;

    /**
     * OCR识别原始结果（JSON格式）
     */
    @TableField("recognition_result")
    private String recognitionResult;
    
    /**
     * 解析后的结构化数据（JSON格式）
     */
    @TableField("parsed_data")
    private String parsedData;
    
    /**
     * 用户确认后的最终数据（JSON格式）
     */
    @TableField("final_data")
    private String finalData;
    
    /**
     * OCR识别置信度
     */
    @TableField("confidence_score")
    private BigDecimal confidenceScore;
    
    /**
     * 处理状态
     */
    @TableField("status")
    private String status;
    
    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;
    
    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;
    
    /**
     * 处理耗时（毫秒）
     */
    @TableField("processing_time")
    private Integer processingTime;
    
    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;
    
    // 处理状态常量
    public static final String STATUS_PENDING = "PENDING";
    public static final String STATUS_UPLOADING = "UPLOADING";
    public static final String STATUS_PROCESSING = "PROCESSING";
    public static final String STATUS_RECOGNIZING = "RECOGNIZING";
    public static final String STATUS_SUCCESS = "SUCCESS";
    public static final String STATUS_FAILED = "FAILED";
    public static final String STATUS_SKIPPED = "SKIPPED";
    
    // 文件类型常量
    public static final String FILE_TYPE_PDF = "PDF";
    public static final String FILE_TYPE_IMAGE = "IMAGE";
}
