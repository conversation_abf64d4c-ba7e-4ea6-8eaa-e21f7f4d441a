package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.BillAiResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 票据AI处理结果Service接口
 */
public interface BillAiResultService extends IService<BillAiResult> {
    
    /**
     * 根据票据ID查询AI处理结果
     * @param billId 票据ID
     * @return AI处理结果
     */
    BillAiResult getByBillId(Integer billId);
    
    /**
     * 保存或更新AI处理结果
     * @param billAiResult AI处理结果
     * @return 是否成功
     */
    boolean saveOrUpdateByBillId(BillAiResult billAiResult);
    
    /**
     * 根据票据ID删除AI处理结果
     * @param billId 票据ID
     * @return 是否成功
     */
    boolean deleteByBillId(Integer billId);
}