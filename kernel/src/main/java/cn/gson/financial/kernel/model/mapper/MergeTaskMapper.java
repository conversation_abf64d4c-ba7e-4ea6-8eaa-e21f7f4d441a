package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.MergeTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 归并任务Mapper接口
 */
@Mapper
public interface MergeTaskMapper extends BaseMapper<MergeTask> {

    /**
     * 根据账套ID查询任务列表
     */
    @Select("SELECT * FROM fxy_financial_merge_tasks " +
            "WHERE account_sets_id = #{accountSetsId} " +
            "ORDER BY created_at DESC")
    List<MergeTask> findTasksByAccountSets(@Param("accountSetsId") Integer accountSetsId);

    /**
     * 根据状态查询任务列表
     */
    @Select("SELECT * FROM fxy_financial_merge_tasks " +
            "WHERE status = #{status} " +
            "ORDER BY created_at ASC")
    List<MergeTask> findTasksByStatus(@Param("status") String status);

    /**
     * 更新任务进度
     */
    @Update("UPDATE fxy_financial_merge_tasks " +
            "SET progress = #{progress}, processed_items = #{processedItems} " +
            "WHERE task_id = #{taskId}")
    int updateTaskProgress(@Param("taskId") String taskId,
                          @Param("progress") Integer progress,
                          @Param("processedItems") Integer processedItems);

    /**
     * 更新任务状态
     */
    @Update("UPDATE fxy_financial_merge_tasks " +
            "SET status = #{status}, " +
            "started_at = CASE WHEN #{status} = 'RUNNING' THEN NOW() ELSE started_at END, " +
            "completed_at = CASE WHEN #{status} IN ('COMPLETED', 'FAILED') THEN NOW() ELSE completed_at END " +
            "WHERE task_id = #{taskId}")
    int updateTaskStatus(@Param("taskId") String taskId, @Param("status") String status);

    /**
     * 更新任务错误信息
     */
    @Update("UPDATE fxy_financial_merge_tasks " +
            "SET error_message = #{errorMessage}, status = 'FAILED', completed_at = NOW() " +
            "WHERE task_id = #{taskId}")
    int updateTaskError(@Param("taskId") String taskId, @Param("errorMessage") String errorMessage);
}
