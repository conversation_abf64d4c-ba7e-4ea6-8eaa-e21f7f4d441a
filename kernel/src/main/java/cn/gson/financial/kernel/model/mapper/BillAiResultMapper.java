package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.BillAiResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 票据AI处理结果Mapper接口
 */
@Mapper
public interface BillAiResultMapper extends BaseMapper<BillAiResult> {
    
    /**
     * 根据票据ID查询AI处理结果
     * @param billId 票据ID
     * @return AI处理结果
     */
    @Select("SELECT * FROM fxy_financial_bill_ai_result WHERE bill_id = #{billId} ORDER BY created_time DESC LIMIT 1")
    BillAiResult selectByBillId(@Param("billId") Integer billId);
    
    /**
     * 根据票据ID删除AI处理结果
     * @param billId 票据ID
     * @return 删除的记录数
     */
    int deleteByBillId(@Param("billId") Integer billId);
}