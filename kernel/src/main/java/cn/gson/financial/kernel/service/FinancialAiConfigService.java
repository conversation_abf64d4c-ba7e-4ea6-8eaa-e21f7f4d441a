package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.FinancialAiConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * AI配置服务接口
 */
public interface FinancialAiConfigService extends IService<FinancialAiConfig> {
    
    /**
     * 获取指定账套的AI配置
     */
    Map<String, String> getAiConfig(Integer accountSetsId);
    
    /**
     * 保存AI配置
     */
    boolean saveAiConfig(Integer accountSetsId, Map<String, String> configMap);
    
    /**
     * 获取配置值
     */
    String getConfigValue(Integer accountSetsId, String configKey);
    
    /**
     * 设置配置值
     */
    boolean setConfigValue(Integer accountSetsId, String configKey, String configValue);
    
    /**
     * 获取配置值（带默认值）
     */
    String getConfigValue(Integer accountSetsId, String configKey, String defaultValue);
    
    /**
     * 检查AI是否启用
     */
    boolean isAiEnabled(Integer accountSetsId);
    
    /**
     * 初始化默认配置
     */
    void initDefaultConfig(Integer accountSetsId);

    /**
     * 获取指定用户的AI配置
     */
    Map<String, String> getAiConfigByUser(Integer userId);

    /**
     * 保存用户AI配置
     */
    boolean saveAiConfigByUser(Integer userId, Map<String, String> configMap);

    /**
     * 获取用户配置值
     */
    String getConfigValueByUser(Integer userId, String configKey);

    /**
     * 设置用户配置值
     */
    boolean setConfigValueByUser(Integer userId, String configKey, String configValue);

    /**
     * 检查用户AI是否启用
     */
    boolean isAiEnabledByUser(Integer userId);

    /**
     * 初始化用户默认配置
     */
    void initDefaultConfigByUser(Integer userId);
}
