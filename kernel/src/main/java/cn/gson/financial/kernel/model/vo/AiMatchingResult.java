package cn.gson.financial.kernel.model.vo;

import cn.gson.financial.kernel.model.entity.Subject;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * AI科目匹配结果VO
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
@Data
public class AiMatchingResult implements Serializable {
    
    /**
     * 匹配是否成功
     */
    private Boolean success;
    
    /**
     * 推荐的借方科目列表
     */
    private List<SubjectRecommendation> debitSubjects;
    
    /**
     * 推荐的贷方科目列表
     */
    private List<SubjectRecommendation> creditSubjects;
    
    /**
     * 整体匹配置信度 (0-1)
     */
    private Double confidence;
    
    /**
     * 匹配原因说明
     */
    private String reason;
    
    /**
     * AI分析的摘要建议
     */
    private String suggestedSummary;
    
    /**
     * 错误信息（如果匹配失败）
     */
    private String errorMessage;
    
    /**
     * 匹配用时（毫秒）
     */
    private Long processingTime;
    
    /**
     * 科目推荐信息
     */
    @Data
    public static class SubjectRecommendation implements Serializable {
        
        /**
         * 推荐的科目
         */
        private Subject subject;
        
        /**
         * 推荐置信度 (0-1)
         */
        private Double confidence;
        
        /**
         * 推荐金额
         */
        private Double amount;
        
        /**
         * 推荐原因
         */
        private String reason;
        
        /**
         * 是否为主要科目（用于复合分录）
         */
        private Boolean isPrimary;
        
        /**
         * 辅助核算信息
         */
        private String auxiliaryInfo;
    }
}