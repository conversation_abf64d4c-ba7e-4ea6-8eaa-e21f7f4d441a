package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.entity.BillAiResult;
import cn.gson.financial.kernel.model.mapper.BillAiResultMapper;
import cn.gson.financial.kernel.service.BillAiResultService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 票据AI处理结果Service实现类
 */
@Slf4j
@Service
public class BillAiResultServiceImpl extends ServiceImpl<BillAiResultMapper, BillAiResult> implements BillAiResultService {
    
    @Override
    public BillAiResult getByBillId(Integer billId) {
        if (billId == null) {
            return null;
        }
        
        QueryWrapper<BillAiResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_id", billId)
                   .orderByDesc("created_time")
                   .last("LIMIT 1");
        
        return this.getOne(queryWrapper);
    }
    
    @Override
    public boolean saveOrUpdateByBillId(BillAiResult billAiResult) {
        if (billAiResult == null || billAiResult.getBillId() == null) {
            return false;
        }
        
        try {
            // 查询是否已存在该票据的AI处理结果
            BillAiResult existing = getByBillId(billAiResult.getBillId());
            
            if (existing != null) {
                // 更新现有记录
                billAiResult.setId(existing.getId());
                billAiResult.setUpdatedTime(new Date());
                return this.updateById(billAiResult);
            } else {
                // 创建新记录
                billAiResult.setCreatedTime(new Date());
                billAiResult.setUpdatedTime(new Date());
                return this.save(billAiResult);
            }
        } catch (Exception e) {
            log.error("保存或更新票据AI处理结果失败，billId: {}", billAiResult.getBillId(), e);
            return false;
        }
    }
    
    @Override
    public boolean deleteByBillId(Integer billId) {
        if (billId == null) {
            return false;
        }
        
        try {
            UpdateWrapper<BillAiResult> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("bill_id", billId);
            return this.remove(updateWrapper);
        } catch (Exception e) {
            log.error("删除票据AI处理结果失败，billId: {}", billId, e);
            return false;
        }
    }
}