package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.AiMatchingHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.Date;

@Mapper
public interface AiMatchingHistoryMapper extends BaseMapper<AiMatchingHistory> {

    /**
     * 分页查询AI匹配历史记录
     *
     * @param page 分页对象
     * @param accountSetsId 账套ID
     * @param businessType 业务类型
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return
     */
    IPage<AiMatchingHistory> findHistoryByPage(
            Page<AiMatchingHistory> page,
            @Param("accountSetsId") Integer accountSetsId,
            @Param("businessType") String businessType,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate
    );
}
