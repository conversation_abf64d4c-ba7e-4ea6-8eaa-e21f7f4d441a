package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.dto.ManualMergeDto;
import cn.gson.financial.kernel.model.dto.MergeExecuteDto;
import cn.gson.financial.kernel.model.dto.MergePreviewDto;
import cn.gson.financial.kernel.model.entity.*;
import cn.gson.financial.kernel.model.mapper.*;
import cn.gson.financial.kernel.service.MergeEngineService;
import cn.gson.financial.kernel.service.RelationManagerService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 归并引擎服务实现
 */
@Slf4j
@Service
public class MergeEngineServiceImpl implements MergeEngineService {

    @Autowired
    private MergeRuleMapper mergeRuleMapper;

    @Autowired
    private DocumentGroupMapper documentGroupMapper;

    @Autowired
    private ReceiptGroupMapper receiptGroupMapper;

    @Autowired
    private BillMapper billMapper;

    @Autowired
    private BankReceiptsMapper bankReceiptsMapper;

    @Autowired
    private MergeTaskMapper mergeTaskMapper;

    @Autowired
    private RelationManagerService relationManagerService;

    @Override
    public MergePreviewDto previewDocumentMerge(Integer accountSetsId, String ruleId) {
        log.info("开始预览票据归并，账套ID: {}, 规则ID: {}", accountSetsId, ruleId);
        
        // 获取归并规则
        MergeRule rule = mergeRuleMapper.selectById(ruleId);
        if (rule == null || !rule.getAccountSetsId().equals(accountSetsId)) {
            throw new RuntimeException("归并规则不存在或不属于当前账套");
        }

        // 获取待归并的票据
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .isNull("doc_group_id"); // 只查询未归并的票据

        List<Bill> bills = billMapper.selectList(queryWrapper);
        log.info("查询到待归并票据数量: {}", bills.size());

        // 根据规则进行分组预览
        Map<String, List<Bill>> groupedBills = groupBillsByRule(bills, rule);
        log.info("分组结果: {} 个分组", groupedBills.size());

        // 构建预览结果
        MergePreviewDto result = buildPreviewResult(groupedBills, "DOCUMENT");
        log.info("预览结果: {} 个可归并组, {} 个总项目", result.getTotalGroups(), result.getTotalItems());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object executeDocumentMerge(Integer accountSetsId, MergeExecuteDto executeDto, Integer currentUserId) {
        log.info("开始执行票据归并，账套ID: {}, 规则ID: {}", accountSetsId, executeDto.getRuleId());
        
        if (executeDto.getAsync()) {
            // 异步执行
            return createMergeTask(accountSetsId, executeDto, currentUserId, MergeTask.TaskType.DOCUMENT_MERGE.getCode());
        } else {
            // 同步执行
            return executeDocumentMergeSync(accountSetsId, executeDto, currentUserId);
        }
    }

    @Override
    public MergePreviewDto previewReceiptMerge(Integer accountSetsId, String ruleId) {
        log.info("开始预览银证归并，账套ID: {}, 规则ID: {}", accountSetsId, ruleId);
        
        // 获取归并规则
        MergeRule rule = mergeRuleMapper.selectById(ruleId);
        if (rule == null || !rule.getAccountSetsId().equals(accountSetsId)) {
            throw new RuntimeException("归并规则不存在或不属于当前账套");
        }

        // 获取待归并的银证
        QueryWrapper<BankReceipts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .isNull("receipt_group_id"); // 只查询未归并的银证

        List<BankReceipts> receipts = bankReceiptsMapper.selectList(queryWrapper);
        log.info("查询到待归并银证数量: {}", receipts.size());

        // 根据规则进行分组预览
        Map<String, List<BankReceipts>> groupedReceipts = groupReceiptsByRule(receipts, rule);
        log.info("分组结果: {} 个分组", groupedReceipts.size());

        // 构建预览结果
        MergePreviewDto result = buildReceiptPreviewResult(groupedReceipts, "RECEIPT");
        log.info("预览结果: {} 个可归并组, {} 个总项目", result.getTotalGroups(), result.getTotalItems());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object executeReceiptMerge(Integer accountSetsId, MergeExecuteDto executeDto, Integer currentUserId) {
        log.info("开始执行银证归并，账套ID: {}, 规则ID: {}", accountSetsId, executeDto.getRuleId());
        
        if (executeDto.getAsync()) {
            // 异步执行
            return createMergeTask(accountSetsId, executeDto, currentUserId, MergeTask.TaskType.RECEIPT_MERGE.getCode());
        } else {
            // 同步执行
            return executeReceiptMergeSync(accountSetsId, executeDto, currentUserId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String manualDocumentMerge(Integer accountSetsId, List<Integer> documentIds, String groupName, Integer currentUserId) {
        log.info("开始手动票据归并，账套ID: {}, 票据数量: {}", accountSetsId, documentIds.size());
        
        // 查询票据
        List<Bill> bills = billMapper.selectBatchIds(documentIds);
        if (bills.isEmpty()) {
            throw new RuntimeException("未找到指定的票据");
        }
        
        // 验证票据属于当前账套且未归并
        for (Bill bill : bills) {
            if (!bill.getAccountSetsId().equals(accountSetsId)) {
                throw new RuntimeException("票据不属于当前账套");
            }
            if (bill.getDocGroupId() != null) {
                throw new RuntimeException("票据已归并，无法重复归并");
            }
        }
        
        // 创建归并组
        DocumentGroup group = createDocumentGroup(accountSetsId, groupName, bills, currentUserId);
        
        // 更新票据的归并组ID
        updateBillsGroupId(bills, group.getGroupId());
        
        return group.getGroupId();
    }

    // 私有方法实现...
    private Map<String, List<Bill>> groupBillsByRule(List<Bill> bills, MergeRule rule) {
        JSONObject ruleLogic = JSON.parseObject(rule.getRuleLogic());
        String ruleType = ruleLogic.getString("type");
        
        switch (ruleType) {
            case "SAME_PERSON_DATE_TYPE":
                return groupBySamePersonDateType(bills, ruleLogic);
            case "AMOUNT_RANGE":
                return groupByAmountRange(bills, ruleLogic);
            case "TIME_WINDOW":
                return groupByTimeWindow(bills, ruleLogic);
            default:
                log.warn("未知的归并规则类型: {}", ruleType);
                return new HashMap<>();
        }
    }
    
    private Map<String, List<Bill>> groupBySamePersonDateType(List<Bill> bills, JSONObject ruleLogic) {
        // 按照相同提交人、相同日期、相同类型进行分组
        return bills.stream()
                .collect(Collectors.groupingBy(bill -> 
                    String.format("%s_%s_%s", 
                        bill.getIssuer(), 
                        bill.getBillDate().toString(), 
                        bill.getType())));
    }
    
    private MergePreviewDto buildPreviewResult(Map<String, List<Bill>> groupedBills, String entityType) {
        MergePreviewDto preview = new MergePreviewDto();
        List<MergePreviewDto.PreviewGroup> groups = new ArrayList<>();
        
        int totalItems = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (Map.Entry<String, List<Bill>> entry : groupedBills.entrySet()) {
            if (entry.getValue().size() < 2) continue; // 只显示可以归并的组
            
            MergePreviewDto.PreviewGroup group = new MergePreviewDto.PreviewGroup();
            group.setGroupName("归并组-" + entry.getKey());
            group.setItemCount(entry.getValue().size());
            
            List<MergePreviewDto.PreviewItem> items = new ArrayList<>();
            BigDecimal groupAmount = BigDecimal.ZERO;
            
            for (Bill bill : entry.getValue()) {
                MergePreviewDto.PreviewItem item = new MergePreviewDto.PreviewItem();
                item.setItemId(bill.getId().toString());
                item.setItemNo(bill.getBillNo());
                item.setItemType(bill.getType());
                item.setItemDate(bill.getBillDate().toString());
                item.setAmount(BigDecimal.valueOf(bill.getAmount()));
                item.setSummary(bill.getSummary());
                item.setCounterparty(bill.getIssuer());
                
                items.add(item);
                groupAmount = groupAmount.add(BigDecimal.valueOf(bill.getAmount()));
            }
            
            group.setItems(items);
            group.setTotalAmount(groupAmount);
            group.setMergeReason("符合归并规则条件");
            
            groups.add(group);
            totalItems += entry.getValue().size();
            totalAmount = totalAmount.add(groupAmount);
        }
        
        preview.setGroups(groups);
        preview.setTotalGroups(groups.size());
        preview.setTotalItems(totalItems);
        preview.setTotalAmount(totalAmount);
        
        return preview;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String manualReceiptMerge(Integer accountSetsId, List<Integer> receiptIds, String groupName, Integer currentUserId) {
        log.info("开始手动银证归并，账套ID: {}, 银证数量: {}", accountSetsId, receiptIds.size());

        // 查询银证
        List<BankReceipts> receipts = bankReceiptsMapper.selectBatchIds(receiptIds);
        if (receipts.isEmpty()) {
            throw new RuntimeException("未找到指定的银证");
        }

        // 验证银证属于当前账套且未归并
        for (BankReceipts receipt : receipts) {
            if (!receipt.getAccountSetsId().equals(accountSetsId)) {
                throw new RuntimeException("银证不属于当前账套");
            }
            if (receipt.getReceiptGroupId() != null) {
                throw new RuntimeException("银证已归并，无法重复归并");
            }
        }

        // 创建归并组
        ReceiptGroup group = createReceiptGroup(accountSetsId, groupName, receipts, currentUserId);

        // 更新银证的归并组ID
        updateReceiptsGroupId(receipts, group.getGroupId());

        return group.getGroupId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unmergeDocumentGroup(Integer accountSetsId, String groupId) {
        log.info("开始解散票据归并组，账套ID: {}, 组ID: {}", accountSetsId, groupId);

        // 验证归并组存在且属于当前账套
        DocumentGroup group = documentGroupMapper.selectById(groupId);
        if (group == null || !group.getAccountSetsId().equals(accountSetsId)) {
            throw new RuntimeException("归并组不存在或不属于当前账套");
        }

        // 清除票据的归并组ID
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("doc_group_id", groupId);
        List<Bill> bills = billMapper.selectList(queryWrapper);

        for (Bill bill : bills) {
            bill.setDocGroupId(null);
            billMapper.updateById(bill);
        }

        // 解散归并组
        int result = documentGroupMapper.dissolveGroup(groupId);

        // 删除相关关联关系
        relationManagerService.deleteAllRelationsByEntity(accountSetsId, groupId, "DOCUMENT_GROUP");

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unmergeReceiptGroup(Integer accountSetsId, String groupId) {
        log.info("开始解散银证归并组，账套ID: {}, 组ID: {}", accountSetsId, groupId);

        // 验证归并组存在且属于当前账套
        ReceiptGroup group = receiptGroupMapper.selectById(groupId);
        if (group == null || !group.getAccountSetsId().equals(accountSetsId)) {
            throw new RuntimeException("归并组不存在或不属于当前账套");
        }

        // 清除银证的归并组ID
        QueryWrapper<BankReceipts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("receipt_group_id", groupId);
        List<BankReceipts> receipts = bankReceiptsMapper.selectList(queryWrapper);

        for (BankReceipts receipt : receipts) {
            receipt.setReceiptGroupId(null);
            bankReceiptsMapper.updateById(receipt);
        }

        // 解散归并组
        int result = receiptGroupMapper.dissolveGroup(groupId);

        // 删除相关关联关系
        relationManagerService.deleteAllRelationsByEntity(accountSetsId, groupId, "RECEIPT_GROUP");

        return result > 0;
    }

    @Override
    public boolean incrementalMerge(Integer accountSetsId, String entityId, String entityType, Integer currentUserId) {
        log.info("开始增量归并，账套ID: {}, 实体ID: {}, 实体类型: {}", accountSetsId, entityId, entityType);

        // 获取适用的归并规则
        List<MergeRule> rules = mergeRuleMapper.findActiveRulesByAccountSetsAndEntity(accountSetsId, entityType);

        for (MergeRule rule : rules) {
            try {
                if ("DOCUMENT".equals(entityType)) {
                    if (tryMergeDocumentWithExistingGroups(accountSetsId, Integer.valueOf(entityId), rule)) {
                        return true;
                    }
                } else if ("RECEIPT".equals(entityType)) {
                    if (tryMergeReceiptWithExistingGroups(accountSetsId, Integer.valueOf(entityId), rule)) {
                        return true;
                    }
                }
            } catch (Exception e) {
                log.warn("使用规则 {} 进行增量归并失败: {}", rule.getRuleId(), e.getMessage());
            }
        }

        return false;
    }

    @Override
    public MergeTask getTaskStatus(String taskId) {
        return mergeTaskMapper.selectById(taskId);
    }

    // 私有辅助方法
    private Map<String, List<BankReceipts>> groupReceiptsByRule(List<BankReceipts> receipts, MergeRule rule) {
        JSONObject ruleLogic = JSON.parseObject(rule.getRuleLogic());
        String ruleType = ruleLogic.getString("type");

        switch (ruleType) {
            case "SAME_PERSON_DATE_TYPE":
                return groupReceiptsBySamePersonDateType(receipts, ruleLogic);
            case "AMOUNT_RANGE":
                return groupReceiptsByAmountRange(receipts, ruleLogic);
            case "TIME_WINDOW":
                return groupReceiptsByTimeWindow(receipts, ruleLogic);
            default:
                log.warn("未知的归并规则类型: {}", ruleType);
                return new HashMap<>();
        }
    }

    private Map<String, List<BankReceipts>> groupReceiptsBySamePersonDateType(List<BankReceipts> receipts, JSONObject ruleLogic) {
        return receipts.stream()
                .collect(Collectors.groupingBy(receipt -> {
                    // 根据类型获取交易对手信息
                    String counterparty = "未知";
                    if ("收入".equals(receipt.getType())) {
                        counterparty = receipt.getPayerName() != null ? receipt.getPayerName() :
                                      (receipt.getPayerAccount() != null ? receipt.getPayerAccount() : "未知付款人");
                    } else {
                        counterparty = receipt.getPayeeName() != null ? receipt.getPayeeName() :
                                      (receipt.getPayeeAccount() != null ? receipt.getPayeeAccount() : "未知收款人");
                    }
                    return String.format("%s_%s_%s",
                        counterparty,
                        receipt.getReceiptsDate().toString(),
                        receipt.getType());
                }));
    }

    private Map<String, List<BankReceipts>> groupReceiptsByAmountRange(List<BankReceipts> receipts, JSONObject ruleLogic) {
        // 按金额范围分组的实现
        JSONObject conditions = ruleLogic.getJSONObject("conditions");
        double minAmount = conditions.getDoubleValue("min_amount");
        double maxAmount = conditions.getDoubleValue("max_amount");
        double tolerancePercent = conditions.getDoubleValue("tolerance_percent");

        Map<String, List<BankReceipts>> groups = new HashMap<>();

        for (BankReceipts receipt : receipts) {
            double amount = receipt.getAmount();

            // 检查金额是否在指定范围内
            if (amount >= minAmount && amount <= maxAmount) {
                // 按金额范围分组，允许一定的容差
                String groupKey = getAmountGroupKey(amount, tolerancePercent);
                groups.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(receipt);
            }
        }

        return groups;
    }

    private Map<String, List<BankReceipts>> groupReceiptsByTimeWindow(List<BankReceipts> receipts, JSONObject ruleLogic) {
        // 按时间窗口分组的实现
        JSONObject conditions = ruleLogic.getJSONObject("conditions");
        int windowDays = conditions.getIntValue("window_days");
        boolean sameCounterparty = conditions.getBooleanValue("same_counterparty");

        Map<String, List<BankReceipts>> groups = new HashMap<>();

        for (BankReceipts receipt : receipts) {
            String groupKey = findReceiptTimeWindowGroup(receipt, receipts, windowDays, sameCounterparty);
            groups.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(receipt);
        }

        return groups;
    }

    private String findReceiptTimeWindowGroup(BankReceipts targetReceipt, List<BankReceipts> allReceipts, int windowDays, boolean sameCounterparty) {
        // 为每个银证找到合适的时间窗口分组
        Date targetDate = targetReceipt.getReceiptsDate();
        // 根据类型获取交易对手信息
        String targetCounterparty = null;
        if ("收入".equals(targetReceipt.getType())) {
            targetCounterparty = targetReceipt.getPayerName() != null ? targetReceipt.getPayerName() : targetReceipt.getPayerAccount();
        } else {
            targetCounterparty = targetReceipt.getPayeeName() != null ? targetReceipt.getPayeeName() : targetReceipt.getPayeeAccount();
        }
        if (targetCounterparty == null) {
            targetCounterparty = "未知";
        }

        // 生成基于日期和对手方的分组键
        if (sameCounterparty) {
            return String.format("window_%s_%s", targetCounterparty, getDateWindow(targetDate, windowDays));
        } else {
            return String.format("window_%s", getDateWindow(targetDate, windowDays));
        }
    }

    private Map<String, List<Bill>> groupByAmountRange(List<Bill> bills, JSONObject ruleLogic) {
        // 按金额范围分组的实现
        JSONObject conditions = ruleLogic.getJSONObject("conditions");
        double minAmount = conditions.getDoubleValue("min_amount");
        double maxAmount = conditions.getDoubleValue("max_amount");
        double tolerancePercent = conditions.getDoubleValue("tolerance_percent");

        Map<String, List<Bill>> groups = new HashMap<>();

        for (Bill bill : bills) {
            double amount = bill.getAmount();

            // 检查金额是否在指定范围内
            if (amount >= minAmount && amount <= maxAmount) {
                // 按金额范围分组，允许一定的容差
                String groupKey = getAmountGroupKey(amount, tolerancePercent);
                groups.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(bill);
            }
        }

        return groups;
    }

    private String getAmountGroupKey(double amount, double tolerancePercent) {
        // 根据容差百分比计算分组键
        double tolerance = amount * tolerancePercent / 100.0;
        double groupBase = Math.floor(amount / tolerance) * tolerance;
        return String.format("amount_%.2f", groupBase);
    }

    private Map<String, List<Bill>> groupByTimeWindow(List<Bill> bills, JSONObject ruleLogic) {
        // 按时间窗口分组的实现
        JSONObject conditions = ruleLogic.getJSONObject("conditions");
        int windowDays = conditions.getIntValue("window_days");
        boolean sameCounterparty = conditions.getBooleanValue("same_counterparty");

        Map<String, List<Bill>> groups = new HashMap<>();

        for (Bill bill : bills) {
            String groupKey = findTimeWindowGroup(bill, bills, windowDays, sameCounterparty);
            groups.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(bill);
        }

        return groups;
    }

    private String findTimeWindowGroup(Bill targetBill, List<Bill> allBills, int windowDays, boolean sameCounterparty) {
        // 为每个票据找到合适的时间窗口分组
        Date targetDate = targetBill.getBillDate();
        String targetCounterparty = targetBill.getIssuer();

        // 生成基于日期和对手方的分组键
        if (sameCounterparty) {
            return String.format("window_%s_%s", targetCounterparty, getDateWindow(targetDate, windowDays));
        } else {
            return String.format("window_%s", getDateWindow(targetDate, windowDays));
        }
    }

    private String getDateWindow(Date date, int windowDays) {
        // 将日期归到指定天数的窗口中
        long daysSinceEpoch = date.getTime() / (24 * 60 * 60 * 1000);
        long windowNumber = daysSinceEpoch / windowDays;
        return String.valueOf(windowNumber);
    }

    private MergePreviewDto buildReceiptPreviewResult(Map<String, List<BankReceipts>> groupedReceipts, String entityType) {
        MergePreviewDto preview = new MergePreviewDto();
        List<MergePreviewDto.PreviewGroup> groups = new ArrayList<>();

        int totalItems = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (Map.Entry<String, List<BankReceipts>> entry : groupedReceipts.entrySet()) {
            if (entry.getValue().size() < 2) continue; // 只显示可以归并的组

            MergePreviewDto.PreviewGroup group = new MergePreviewDto.PreviewGroup();
            group.setGroupName("归并组-" + entry.getKey());
            group.setItemCount(entry.getValue().size());

            List<MergePreviewDto.PreviewItem> items = new ArrayList<>();
            BigDecimal groupAmount = BigDecimal.ZERO;

            for (BankReceipts receipt : entry.getValue()) {
                MergePreviewDto.PreviewItem item = new MergePreviewDto.PreviewItem();
                item.setItemId(receipt.getId().toString());
                item.setItemNo(receipt.getReceiptsNo());
                item.setItemType(receipt.getType());
                item.setItemDate(receipt.getReceiptsDate().toString());
                item.setAmount(BigDecimal.valueOf(receipt.getAmount()));
                item.setSummary(receipt.getSummary());
                // 根据类型设置交易对手信息
                String counterparty = "未知";
                if ("收入".equals(receipt.getType())) {
                    counterparty = receipt.getPayerName() != null ? receipt.getPayerName() :
                                  (receipt.getPayerAccount() != null ? receipt.getPayerAccount() : "未知付款人");
                } else {
                    counterparty = receipt.getPayeeName() != null ? receipt.getPayeeName() :
                                  (receipt.getPayeeAccount() != null ? receipt.getPayeeAccount() : "未知收款人");
                }
                item.setCounterparty(counterparty);

                items.add(item);
                groupAmount = groupAmount.add(BigDecimal.valueOf(receipt.getAmount()));
            }

            group.setItems(items);
            group.setTotalAmount(groupAmount);
            group.setMergeReason("符合归并规则条件");

            groups.add(group);
            totalItems += entry.getValue().size();
            totalAmount = totalAmount.add(groupAmount);
        }

        preview.setGroups(groups);
        preview.setTotalGroups(groups.size());
        preview.setTotalItems(totalItems);
        preview.setTotalAmount(totalAmount);

        return preview;
    }

    private String createMergeTask(Integer accountSetsId, MergeExecuteDto executeDto, Integer currentUserId, String taskType) {
        MergeTask task = new MergeTask();
        task.setTaskName("归并任务-" + System.currentTimeMillis());
        task.setTaskType(taskType);
        task.setRuleId(executeDto.getRuleId());
        task.setStatus(MergeTask.Status.PENDING.getCode());
        task.setProgress(0);
        task.setTotalItems(0);
        task.setProcessedItems(0);
        task.setCreatedBy(currentUserId);
        task.setAccountSetsId(accountSetsId);

        mergeTaskMapper.insert(task);

        // TODO: 提交到异步任务队列

        return task.getTaskId();
    }

    private Object executeDocumentMergeSync(Integer accountSetsId, MergeExecuteDto executeDto, Integer currentUserId) {
        log.info("同步执行票据归并，账套ID: {}, 规则ID: {}", accountSetsId, executeDto.getRuleId());

        // 获取归并规则
        MergeRule rule = mergeRuleMapper.selectById(executeDto.getRuleId());
        if (rule == null || !rule.getAccountSetsId().equals(accountSetsId)) {
            throw new RuntimeException("归并规则不存在或不属于当前账套");
        }

        // 获取待归并的票据
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .isNull("doc_group_id"); // 只查询未归并的票据

        List<Bill> bills = billMapper.selectList(queryWrapper);
        if (bills.isEmpty()) {
            log.info("没有找到待归并的票据");
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "没有找到待归并的票据");
            result.put("groupCount", 0);
            return result;
        }

        // 根据规则进行分组
        Map<String, List<Bill>> groupedBills = groupBillsByRule(bills, rule);

        // 收集所有符合归并条件的票据
        List<Bill> allMergeableBills = new ArrayList<>();
        for (Map.Entry<String, List<Bill>> entry : groupedBills.entrySet()) {
            List<Bill> groupBills = entry.getValue();
            if (groupBills.size() > 1) { // 只有多于1个票据的组才需要归并
                allMergeableBills.addAll(groupBills);
            }
        }

        int groupCount = 0;
        int totalMergedItems = 0;

        if (!allMergeableBills.isEmpty()) {
            // 使用传入的组名称，如果没有则生成默认名称
            String groupName = executeDto.getGroupName();
            if (groupName == null || groupName.trim().isEmpty()) {
                groupName = String.format("自动归并组_%s_%d", rule.getRuleName(), System.currentTimeMillis());
            }

            // 创建一个归并组包含所有符合条件的票据
            DocumentGroup group = createDocumentGroup(accountSetsId, groupName, allMergeableBills, currentUserId);
            group.setMergeRuleId(rule.getRuleId());
            documentGroupMapper.updateById(group);

            // 更新票据的归并组ID
            updateBillsGroupId(allMergeableBills, group.getGroupId());

            groupCount = 1;
            totalMergedItems = allMergeableBills.size();

            log.info("创建票据归并组: {}, 包含票据数量: {}", group.getGroupName(), allMergeableBills.size());
        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", String.format("票据归并执行成功，创建了%d个归并组，归并了%d个票据", groupCount, totalMergedItems));
        result.put("groupCount", groupCount);
        result.put("mergedItemCount", totalMergedItems);
        return result;
    }

    private Object executeReceiptMergeSync(Integer accountSetsId, MergeExecuteDto executeDto, Integer currentUserId) {
        log.info("同步执行银证归并，账套ID: {}, 规则ID: {}", accountSetsId, executeDto.getRuleId());

        // 获取归并规则
        MergeRule rule = mergeRuleMapper.selectById(executeDto.getRuleId());
        if (rule == null || !rule.getAccountSetsId().equals(accountSetsId)) {
            throw new RuntimeException("归并规则不存在或不属于当前账套");
        }

        // 获取待归并的银证
        QueryWrapper<BankReceipts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .isNull("receipt_group_id"); // 只查询未归并的银证

        List<BankReceipts> receipts = bankReceiptsMapper.selectList(queryWrapper);
        if (receipts.isEmpty()) {
            log.info("没有找到待归并的银证");
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "没有找到待归并的银证");
            result.put("groupCount", 0);
            return result;
        }

        // 根据规则进行分组
        Map<String, List<BankReceipts>> groupedReceipts = groupReceiptsByRule(receipts, rule);

        // 收集所有符合归并条件的银证
        List<BankReceipts> allMergeableReceipts = new ArrayList<>();
        for (Map.Entry<String, List<BankReceipts>> entry : groupedReceipts.entrySet()) {
            List<BankReceipts> groupReceipts = entry.getValue();
            if (groupReceipts.size() > 1) { // 只有多于1个银证的组才需要归并
                allMergeableReceipts.addAll(groupReceipts);
            }
        }

        int groupCount = 0;
        int totalMergedItems = 0;

        if (!allMergeableReceipts.isEmpty()) {
            // 使用传入的组名称，如果没有则生成默认名称
            String groupName = executeDto.getGroupName();
            if (groupName == null || groupName.trim().isEmpty()) {
                groupName = String.format("自动归并组_%s_%d", rule.getRuleName(), System.currentTimeMillis());
            }

            // 创建一个归并组包含所有符合条件的银证
            ReceiptGroup group = createReceiptGroup(accountSetsId, groupName, allMergeableReceipts, currentUserId);
            group.setMergeRuleId(rule.getRuleId());
            receiptGroupMapper.updateById(group);

            // 更新银证的归并组ID
            updateReceiptsGroupId(allMergeableReceipts, group.getGroupId());

            groupCount = 1;
            totalMergedItems = allMergeableReceipts.size();

            log.info("创建银证归并组: {}, 包含银证数量: {}", group.getGroupName(), allMergeableReceipts.size());
        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", String.format("银证归并执行成功，创建了%d个归并组，归并了%d个银证", groupCount, totalMergedItems));
        result.put("groupCount", groupCount);
        result.put("mergedItemCount", totalMergedItems);
        return result;
    }

    private DocumentGroup createDocumentGroup(Integer accountSetsId, String groupName, List<Bill> bills, Integer currentUserId) {
        DocumentGroup group = new DocumentGroup();
        group.setGroupName(groupName);
        group.setMergeRuleId("MANUAL"); // 手动归并使用特殊规则ID
        group.setGroupSummary("手动归并组");
        group.setStatus(DocumentGroup.Status.ACTIVE.getCode());
        group.setCreatedBy(currentUserId);
        group.setAccountSetsId(accountSetsId);

        // 计算统计信息
        BigDecimal totalAmount = bills.stream()
                .map(bill -> BigDecimal.valueOf(bill.getAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        group.setTotalAmount(totalAmount);
        group.setItemCount(bills.size());

        documentGroupMapper.insert(group);
        return group;
    }

    private ReceiptGroup createReceiptGroup(Integer accountSetsId, String groupName, List<BankReceipts> receipts, Integer currentUserId) {
        ReceiptGroup group = new ReceiptGroup();
        group.setGroupName(groupName);
        group.setMergeRuleId("MANUAL"); // 手动归并使用特殊规则ID
        group.setGroupSummary("手动归并组");
        group.setStatus(ReceiptGroup.Status.ACTIVE.getCode());
        group.setCreatedBy(currentUserId);
        group.setAccountSetsId(accountSetsId);

        // 计算统计信息
        BigDecimal totalAmount = receipts.stream()
                .map(receipt -> BigDecimal.valueOf(receipt.getAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        group.setTotalAmount(totalAmount);
        group.setItemCount(receipts.size());

        receiptGroupMapper.insert(group);
        return group;
    }

    private void updateBillsGroupId(List<Bill> bills, String groupId) {
        for (Bill bill : bills) {
            bill.setDocGroupId(groupId);
            billMapper.updateById(bill);
        }
        log.info("更新了{}个票据的归并组ID为: {}", bills.size(), groupId);
    }

    private void updateReceiptsGroupId(List<BankReceipts> receipts, String groupId) {
        for (BankReceipts receipt : receipts) {
            receipt.setReceiptGroupId(groupId);
            bankReceiptsMapper.updateById(receipt);
        }
        log.info("更新了{}个银证的归并组ID为: {}", receipts.size(), groupId);
    }



    private boolean tryMergeDocumentWithExistingGroups(Integer accountSetsId, Integer documentId, MergeRule rule) {
        // 尝试将票据与现有组归并的实现
        return false;
    }

    private boolean tryMergeReceiptWithExistingGroups(Integer accountSetsId, Integer receiptId, MergeRule rule) {
        // 尝试将银证与现有组归并的实现
        return false;
    }

    @Override
    public List<Bill> getUnmergedDocuments(Integer accountSetsId) {
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .isNull("doc_group_id"); // 只查询未归并的票据
        return billMapper.selectList(queryWrapper);
    }

    @Override
    public List<BankReceipts> getUnmergedReceipts(Integer accountSetsId) {
        QueryWrapper<BankReceipts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .isNull("receipt_group_id"); // 只查询未归并的银证
        return bankReceiptsMapper.selectList(queryWrapper);
    }

    @Override
    public Object manualMergeDocuments(Integer accountSetsId, ManualMergeDto mergeDto, Integer currentUserId) {
        log.info("手动票据归并，账套ID: {}, 票据数量: {}, 组名称: {}",
            accountSetsId, mergeDto.getDocumentIds().size(), mergeDto.getGroupName());

        if (mergeDto.getDocumentIds() == null || mergeDto.getDocumentIds().size() < 2) {
            throw new RuntimeException("至少需要选择2个票据进行归并");
        }

        // 获取选中的票据
        List<Bill> bills = new ArrayList<>();
        for (Integer documentId : mergeDto.getDocumentIds()) {
            Bill bill = billMapper.selectById(documentId);
            if (bill != null && bill.getAccountSetsId().equals(accountSetsId) && bill.getDocGroupId() == null) {
                bills.add(bill);
            }
        }

        if (bills.isEmpty()) {
            throw new RuntimeException("没有找到有效的未归并票据");
        }

        // 创建归并组
        DocumentGroup group = createDocumentGroup(accountSetsId, mergeDto.getGroupName(), bills, currentUserId);
        group.setMergeRuleId("MANUAL"); // 手动归并使用特殊规则ID
        documentGroupMapper.updateById(group);

        // 更新票据的归并组ID
        updateBillsGroupId(bills, group.getGroupId());

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", String.format("手动票据归并成功，创建归并组: %s，归并了%d个票据",
            group.getGroupName(), bills.size()));
        result.put("groupId", group.getGroupId());
        result.put("mergedItemCount", bills.size());
        return result;
    }

    @Override
    public Object manualMergeReceipts(Integer accountSetsId, ManualMergeDto mergeDto, Integer currentUserId) {
        log.info("手动银证归并，账套ID: {}, 银证数量: {}, 组名称: {}",
            accountSetsId, mergeDto.getReceiptIds().size(), mergeDto.getGroupName());

        if (mergeDto.getReceiptIds() == null || mergeDto.getReceiptIds().size() < 2) {
            throw new RuntimeException("至少需要选择2个银证进行归并");
        }

        // 获取选中的银证
        List<BankReceipts> receipts = new ArrayList<>();
        for (Integer receiptId : mergeDto.getReceiptIds()) {
            BankReceipts receipt = bankReceiptsMapper.selectById(receiptId);
            if (receipt != null && receipt.getAccountSetsId().equals(accountSetsId) && receipt.getReceiptGroupId() == null) {
                receipts.add(receipt);
            }
        }

        if (receipts.isEmpty()) {
            throw new RuntimeException("没有找到有效的未归并银证");
        }

        // 创建归并组
        ReceiptGroup group = createReceiptGroup(accountSetsId, mergeDto.getGroupName(), receipts, currentUserId);
        group.setMergeRuleId("MANUAL"); // 手动归并使用特殊规则ID
        receiptGroupMapper.updateById(group);

        // 更新银证的归并组ID
        updateReceiptsGroupId(receipts, group.getGroupId());

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", String.format("手动银证归并成功，创建归并组: %s，归并了%d个银证",
            group.getGroupName(), receipts.size()));
        result.put("groupId", group.getGroupId());
        result.put("mergedItemCount", receipts.size());
        return result;
    }
}
