package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.FinancialAiConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * AI配置Mapper接口
 */
@Mapper
public interface FinancialAiConfigMapper extends BaseMapper<FinancialAiConfig> {
    
    /**
     * 根据账套ID和配置键查询配置
     */
    @Select("SELECT * FROM fxy_financial_ai_config WHERE account_sets_id = #{accountSetsId} AND config_key = #{configKey} AND is_active = 1")
    FinancialAiConfig selectByAccountAndKey(@Param("accountSetsId") Integer accountSetsId, 
                                           @Param("configKey") String configKey);
    
    /**
     * 根据账套ID查询所有配置
     */
    @Select("SELECT * FROM fxy_financial_ai_config WHERE account_sets_id = #{accountSetsId} AND is_active = 1")
    List<FinancialAiConfig> selectByAccountSetsId(@Param("accountSetsId") Integer accountSetsId);
    
    /**
     * 更新配置值
     */
    @Update("UPDATE fxy_financial_ai_config SET config_value = #{configValue}, update_time = NOW() " +
            "WHERE account_sets_id = #{accountSetsId} AND config_key = #{configKey}")
    int updateConfigValue(@Param("accountSetsId") Integer accountSetsId, 
                         @Param("configKey") String configKey, 
                         @Param("configValue") String configValue);
    
    /**
     * 批量插入或更新配置
     */
    int insertOrUpdateBatch(@Param("list") List<FinancialAiConfig> configList);
}
