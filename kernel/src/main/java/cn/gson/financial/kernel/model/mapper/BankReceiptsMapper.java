package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.BankReceipts;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BankReceiptsMapper extends BaseMapper<BankReceipts> {
    int batchInsert(@Param("list") List<BankReceipts> list);

    // 删除或注释掉以下方法
    // Integer selectMaxReceiptsNo(@Param("accountSetsId") Integer accountSetsId, @Param("year") int year, @Param("month") int month);

    List<BankReceipts> selectBankReceipts(@Param(Constants.WRAPPER) Wrapper wrapper);

    IPage<BankReceipts> selectBankReceipts(IPage<BankReceipts> page, @Param(Constants.WRAPPER) Wrapper wrapper);
}
