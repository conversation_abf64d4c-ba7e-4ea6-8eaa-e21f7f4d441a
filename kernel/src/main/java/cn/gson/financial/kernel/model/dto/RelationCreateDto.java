package cn.gson.financial.kernel.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 关联创建DTO
 */
@Data
public class RelationCreateDto {
    
    /**
     * 源类型：DOCUMENT, DOCUMENT_GROUP, RECEIPT, RECEIPT_GROUP
     */
    private String sourceType;
    
    /**
     * 源ID
     */
    private String sourceId;
    
    /**
     * 目标类型：DOCUMENT, DOCUMENT_GROUP, RECEIPT, RECEIPT_GROUP
     */
    private String targetType;
    
    /**
     * 目标ID
     */
    private String targetId;
    
    /**
     * 关联类型
     */
    private String relationType = "ASSOCIATED";
    
    /**
     * 关联金额（部分关联时使用）
     */
    private BigDecimal relationAmount;
    
    /**
     * 关联备注
     */
    private String relationNote;
    
    /**
     * 批量关联创建
     */
    @Data
    public static class BatchRelationCreateDto {
        /**
         * 源类型
         */
        private String sourceType;
        
        /**
         * 源ID
         */
        private String sourceId;
        
        /**
         * 目标关联列表
         */
        private List<TargetRelation> targets;
        
        /**
         * 目标关联
         */
        @Data
        public static class TargetRelation {
            /**
             * 目标类型
             */
            private String targetType;
            
            /**
             * 目标ID
             */
            private String targetId;
            
            /**
             * 关联类型
             */
            private String relationType = "ASSOCIATED";
            
            /**
             * 关联金额
             */
            private BigDecimal relationAmount;
            
            /**
             * 关联备注
             */
            private String relationNote;
        }
    }
}
