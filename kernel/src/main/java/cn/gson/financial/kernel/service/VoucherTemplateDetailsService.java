package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.VoucherTemplateDetails;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月03日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
public interface VoucherTemplateDetailsService extends IService<VoucherTemplateDetails> {


    int batchInsert(List<VoucherTemplateDetails> list);

}



