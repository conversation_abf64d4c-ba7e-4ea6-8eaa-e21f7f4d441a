package cn.gson.financial.kernel.aliyuncs;

import org.springframework.web.multipart.MultipartFile;

/**
 * 阿里云OSS文件上传服务
 */
public interface OssService {
    
    /**
     * 上传文件到OSS
     * @param file 上传的文件
     * @param folder 文件夹路径
     * @return 文件访问URL
     */
    String uploadFile(MultipartFile file, String folder);

    /**
     * 上传文件到OSS（File版本）
     * @param file 上传的文件
     * @param folder 文件夹路径
     * @return 文件访问URL
     */
    String uploadFile(java.io.File file, String folder);
    
    /**
     * 删除OSS文件
     * @param fileUrl 文件URL
     * @return 是否删除成功
     */
    boolean deleteFile(String fileUrl);
}