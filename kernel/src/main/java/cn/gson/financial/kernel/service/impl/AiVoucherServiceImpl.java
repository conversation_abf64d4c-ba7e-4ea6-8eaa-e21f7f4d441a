package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.entity.Voucher;
import cn.gson.financial.kernel.model.entity.VoucherDetails;
import cn.gson.financial.kernel.model.entity.Subject;
import cn.gson.financial.kernel.model.entity.EntityRelation;
import cn.gson.financial.kernel.model.entity.SubjectAiEnhancement;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.mapper.BillMapper;
import cn.gson.financial.kernel.model.mapper.BankReceiptsMapper;
import cn.gson.financial.kernel.model.mapper.SubjectMapper;
import cn.gson.financial.kernel.model.mapper.EntityRelationMapper;
import cn.gson.financial.kernel.model.mapper.AccountSetsMapper;
import cn.gson.financial.kernel.mapper.SubjectAiEnhancementMapper;
import cn.gson.financial.kernel.service.AiVoucherService;
import cn.gson.financial.kernel.service.AiService;
import cn.gson.financial.kernel.service.VoucherService;
import cn.gson.financial.kernel.model.vo.BalanceVo;
import cn.gson.financial.kernel.service.SubjectService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * AI智能凭证生成服务实现
 */
@Service
@Slf4j
public class AiVoucherServiceImpl implements AiVoucherService {

    @Autowired(required = false)
    private AiService aiService;

    @Autowired(required = false)
    private VoucherService voucherService;

    @Autowired(required = false)
    private SubjectService subjectService;

    @Autowired(required = false)
    private BillMapper billMapper;

    @Autowired(required = false)
    private BankReceiptsMapper bankReceiptsMapper;

    @Autowired(required = false)
    private SubjectMapper subjectMapper;

    @Autowired(required = false)
    private EntityRelationMapper entityRelationMapper;

    @Autowired(required = false)
    private SubjectAiEnhancementMapper subjectAiEnhancementMapper;

    @Autowired(required = false)
    private AccountSetsMapper accountSetsMapper;

    @Override
    public Map<String, Object> generateVouchers(Integer accountSetsId, List<Map<String, Object>> sourceItems, Integer userId, Integer voucherYear, Integer voucherMonth) {
        log.info("开始AI凭证生成，账套ID: {}, 用户ID: {}, 数据源数量: {}, 凭证期间: {}-{}", accountSetsId, userId, sourceItems.size(), voucherYear, voucherMonth);

        Map<String, Object> result = new HashMap<>();

        // 检查AI服务是否可用
        if (aiService == null) {
            log.error("AI服务未注入，无法生成凭证");
            result.put("success", false);
            result.put("mode", "unavailable");
            result.put("message", "AI服务未配置，请检查系统配置");
            result.put("data", new ArrayList<>());
            return result;
        }

        // 优先检查用户级别的AI配置
        boolean aiAvailable = false;
        if (userId != null && aiService.isAvailable(userId)) {
            log.info("使用用户{}的AI配置进行凭证生成", userId);
            aiAvailable = true;
        } else if (aiService.isAvailable()) {
            log.info("使用全局AI配置进行凭证生成");
            aiAvailable = true;
        }

        if (!aiAvailable) {
            log.error("AI服务不可用，无法生成凭证");
            result.put("success", false);
            result.put("mode", "unavailable");
            result.put("message", "AI服务不可用，请检查AI配置（API密钥、模型等）。如使用DeepSeek，请确保API密钥正确配置。");
            result.put("data", new ArrayList<>());
            return result;
        }

        // 转换数据格式为AI服务期望的格式
        List<Object> rawSourceItems = new ArrayList<>();
        for (Map<String, Object> item : sourceItems) {
            rawSourceItems.add(item);
        }
        List<Map<String, Object>> formattedSourceItems = formatSourceItemsForAi(rawSourceItems);

        try {
            // 构建AI分析提示词
            String prompt = buildVoucherGenerationPrompt(accountSetsId, formattedSourceItems);
            log.info("构建的AI提示词长度: {}", prompt.length());

            // 调用AI服务生成凭证
            log.info("调用AI服务生成凭证...");
            log.debug("AI提示词长度: {}", prompt.length());
            log.debug("AI提示词内容: {}", prompt.substring(0, Math.min(prompt.length(), 1000)) + "...");

            // 构建AI调用参数
            Map<String, Object> aiParameters = new HashMap<>();
            aiParameters.put("temperature", 0.3); // 降低温度以获得更稳定的结果
            aiParameters.put("max_tokens", 4000); // 增加token数以支持复杂凭证

            String aiResponse;
            if (userId != null && aiService.isAvailable(userId)) {
                log.info("使用用户{}的AI配置调用AI服务", userId);
                aiResponse = aiService.chat(prompt, aiParameters, userId);
            } else {
                log.info("使用全局AI配置调用AI服务");
                aiResponse = aiService.chat(prompt, aiParameters);
            }
            log.info("AI服务响应长度: {}", aiResponse != null ? aiResponse.length() : 0);

            if (aiResponse != null && aiResponse.length() > 0) {
                log.debug("AI响应内容前500字符: {}", aiResponse.substring(0, Math.min(aiResponse.length(), 500)));

                // 解析AI响应
                List<Map<String, Object>> generatedVouchers = parseAiVoucherResponse(aiResponse, formattedSourceItems);
                log.info("AI成功生成凭证数量: {}", generatedVouchers.size());

                if (!generatedVouchers.isEmpty()) {
                    // 构建返回结果
                    result.put("success", true);
                    result.put("mode", "ai");
                    result.put("data", generatedVouchers);
                    result.put("message", String.format("DeepSeek AI成功生成%d张凭证", generatedVouchers.size()));
                    result.put("generatedCount", generatedVouchers.size());
                    result.put("confidence", calculateAverageConfidence(generatedVouchers));
                    return result;
                }
            }

            log.warn("AI服务响应为空或解析失败");
            result.put("success", false);
            result.put("mode", "ai_failed");
            result.put("message", "AI服务响应为空或解析失败，请检查AI配置或重试");
            result.put("data", new ArrayList<>());
            return result;

        } catch (Exception e) {
            log.error("DeepSeek AI凭证生成失败", e);
            result.put("success", false);
            result.put("mode", "error");
            result.put("message", "AI凭证生成失败: " + e.getMessage());
            result.put("data", new ArrayList<>());
            return result;
        }
    }

    /**
     * 格式化源数据为AI服务期望的格式
     */
    private List<Map<String, Object>> formatSourceItemsForAi(List<Object> sourceItems) {
        List<Map<String, Object>> formattedItems = new ArrayList<>();

        for (Object item : sourceItems) {
            if (item instanceof Integer) {
                // 如果是ID，需要查询对应的票据数据
                Map<String, Object> billData = getDataForId((Integer) item, "bill");
                if (!billData.isEmpty()) {
                    Map<String, Object> formattedItem = new HashMap<>();
                    formattedItem.put("type", "bill");
                    formattedItem.put("data", billData);

                    // 查找关联的银行回单
                    List<Map<String, Object>> relatedReceipts = findRelatedReceipts((Integer) item);
                    if (!relatedReceipts.isEmpty()) {
                        formattedItem.put("relatedReceipts", relatedReceipts);
                    }

                    formattedItems.add(formattedItem);
                }
            } else if (item instanceof String) {
                // 如果是组ID，需要查询组数据
                Map<String, Object> groupData = getGroupDataById((String) item);
                if (!groupData.isEmpty()) {
                    Map<String, Object> formattedItem = new HashMap<>();
                    formattedItem.put("type", "group");
                    formattedItem.put("data", groupData);
                    formattedItems.add(formattedItem);
                }
            } else if (item instanceof Map) {
                // 如果已经是Map格式，检查是否有data字段
                @SuppressWarnings("unchecked")
                Map<String, Object> mapItem = (Map<String, Object>) item;

                if (mapItem.containsKey("data")) {
                    // 已经有完整结构，增强关联信息
                    Map<String, Object> enhancedItem = new HashMap<>(mapItem);
                    String type = (String) mapItem.get("type");
                    @SuppressWarnings("unchecked")
                    Map<String, Object> data = (Map<String, Object>) mapItem.get("data");

                    if ("bill".equals(type) && data.containsKey("id")) {
                        Object billId = data.get("id");
                        if (billId instanceof Integer) {
                            List<Map<String, Object>> relatedReceipts = findRelatedReceipts((Integer) billId);
                            if (!relatedReceipts.isEmpty()) {
                                enhancedItem.put("relatedReceipts", relatedReceipts);
                            }
                        }
                    } else if ("receipt".equals(type) && data.containsKey("id")) {
                        Object receiptId = data.get("id");
                        if (receiptId instanceof Integer) {
                            List<Map<String, Object>> relatedBills = findRelatedBills((Integer) receiptId);
                            if (!relatedBills.isEmpty()) {
                                enhancedItem.put("relatedBills", relatedBills);
                            }
                        }
                    }

                    formattedItems.add(enhancedItem);
                } else if (mapItem.containsKey("id") && mapItem.containsKey("type")) {
                    // 只有基本信息，需要补充数据
                    String type = (String) mapItem.get("type");
                    Object id = mapItem.get("id");

                    Map<String, Object> data;
                    if (id instanceof Integer) {
                        if ("bill".equals(type)) {
                            data = getDataForId((Integer) id, "bill");
                        } else if ("receipt".equals(type)) {
                            data = getDataForId((Integer) id, "receipt");
                        } else {
                            data = new HashMap<>();
                        }
                    } else {
                        // 对于组ID
                        data = getGroupDataById(String.valueOf(id));
                    }

                    if (!data.isEmpty()) {
                        Map<String, Object> formattedItem = new HashMap<>();
                        formattedItem.put("type", type);
                        formattedItem.put("data", data);
                        formattedItems.add(formattedItem);
                    }
                } else {
                    // 直接使用
                    formattedItems.add(mapItem);
                }
            }
        }

        return formattedItems;
    }

    /**
     * 根据ID查询实际数据
     */
    private Map<String, Object> getDataForId(Integer id, String type) {
        try {
            if ("bill".equals(type) && billMapper != null) {
                Bill bill = billMapper.selectById(id);
                if (bill != null) {
                    return convertBillToMap(bill);
                }
            } else if ("receipt".equals(type) && bankReceiptsMapper != null) {
                BankReceipts receipt = bankReceiptsMapper.selectById(id);
                if (receipt != null) {
                    return convertReceiptToMap(receipt);
                }
            }

            // 如果查询不到数据，返回空Map
            log.warn("未找到ID为{}的{}数据", id, type);
            return new HashMap<>();

        } catch (Exception e) {
            log.error("查询ID为{}的{}数据失败", id, type, e);
            return new HashMap<>();
        }
    }

    private Map<String, Object> convertBillToMap(Bill bill) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", bill.getId());
        data.put("billNo", bill.getBillNo());
        data.put("amount", bill.getAmount());
        data.put("summary", bill.getSummary());
        data.put("billDate", bill.getBillDate());
        data.put("issuer", bill.getIssuer());
        data.put("recipient", bill.getRecipient());
        data.put("type", bill.getType());
        // 使用issuer作为counterparty
        data.put("counterparty", bill.getIssuer());
        return data;
    }

    private Map<String, Object> convertReceiptToMap(BankReceipts receipt) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", receipt.getId());
        data.put("receiptsNo", receipt.getReceiptsNo());
        data.put("amount", receipt.getAmount());
        data.put("summary", receipt.getSummary());
        data.put("receiptsDate", receipt.getReceiptsDate());
        // 根据类型设置交易对手信息
        String counterparty = null;
        if ("收入".equals(receipt.getType())) {
            counterparty = receipt.getPayerName() != null ? receipt.getPayerName() : receipt.getPayerAccount();
        } else {
            counterparty = receipt.getPayeeName() != null ? receipt.getPayeeName() : receipt.getPayeeAccount();
        }
        data.put("counterparty", counterparty);
        return data;
    }

    /**
     * 根据组ID获取组数据
     */
    private Map<String, Object> getGroupDataById(String groupId) {
        Map<String, Object> data = new HashMap<>();

        try {
            // 这里应该根据实际的组数据表结构来查询
            // 暂时使用模拟数据，实际应该查询归并组表
            data.put("groupId", groupId);
            data.put("groupName", "业务组" + groupId);
            data.put("totalAmount", 50000.0);
            data.put("itemCount", 3);
            data.put("summary", "组合业务" + groupId);

            log.debug("获取组数据: {}", groupId);
        } catch (Exception e) {
            log.error("获取组数据失败: {}", e.getMessage());
        }

        return data;
    }

    /**
     * 查找与票据关联的银行回单
     */
    private List<Map<String, Object>> findRelatedReceipts(Integer billId) {
        List<Map<String, Object>> relatedReceipts = new ArrayList<>();

        try {
            if (entityRelationMapper != null) {
                // 查询以票据为源的关联关系
                List<EntityRelation> relations = entityRelationMapper.findRelationsBySource(
                    EntityRelation.EntityType.DOCUMENT.getCode(),
                    String.valueOf(billId),
                    getCurrentAccountSetsId()
                );

                for (EntityRelation relation : relations) {
                    if (EntityRelation.EntityType.RECEIPT.getCode().equals(relation.getTargetType())) {
                        try {
                            Integer receiptId = Integer.valueOf(relation.getTargetId());
                            Map<String, Object> receiptData = getDataForId(receiptId, "receipt");
                            if (!receiptData.isEmpty()) {
                                receiptData.put("relationId", relation.getRelationId());
                                receiptData.put("relationType", relation.getRelationType());
                                receiptData.put("relationAmount", relation.getRelationAmount());
                                relatedReceipts.add(receiptData);
                            }
                        } catch (NumberFormatException e) {
                            log.warn("无效的银行回单ID: {}", relation.getTargetId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("查找关联银行回单失败: {}", e.getMessage());
        }

        return relatedReceipts;
    }

    /**
     * 查找与银行回单关联的票据
     */
    private List<Map<String, Object>> findRelatedBills(Integer receiptId) {
        List<Map<String, Object>> relatedBills = new ArrayList<>();

        try {
            if (entityRelationMapper != null) {
                // 查询以银行回单为源的关联关系
                List<EntityRelation> relations = entityRelationMapper.findRelationsBySource(
                    EntityRelation.EntityType.RECEIPT.getCode(),
                    String.valueOf(receiptId),
                    getCurrentAccountSetsId()
                );

                for (EntityRelation relation : relations) {
                    if (EntityRelation.EntityType.DOCUMENT.getCode().equals(relation.getTargetType())) {
                        try {
                            Integer billId = Integer.valueOf(relation.getTargetId());
                            Map<String, Object> billData = getDataForId(billId, "bill");
                            if (!billData.isEmpty()) {
                                billData.put("relationId", relation.getRelationId());
                                billData.put("relationType", relation.getRelationType());
                                billData.put("relationAmount", relation.getRelationAmount());
                                relatedBills.add(billData);
                            }
                        } catch (NumberFormatException e) {
                            log.warn("无效的票据ID: {}", relation.getTargetId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("查找关联票据失败: {}", e.getMessage());
        }

        return relatedBills;
    }

    /**
     * 获取当前账套ID（临时方法，实际应该从上下文获取）
     */
    private Integer getCurrentAccountSetsId() {
        // 这里应该从当前用户会话或上下文中获取账套ID
        // 暂时返回1，实际使用时需要修改
        return 1;
    }

    /**
     * 计算平均置信度
     */
    private double calculateAverageConfidence(List<Map<String, Object>> vouchers) {
        if (vouchers.isEmpty()) {
            return 0.0;
        }

        double totalConfidence = 0.0;
        int count = 0;

        for (Map<String, Object> voucher : vouchers) {
            Object confidence = voucher.get("confidence");
            if (confidence instanceof Number) {
                totalConfidence += ((Number) confidence).doubleValue();
                count++;
            }
        }

        return count > 0 ? totalConfidence / count : 85.0; // 默认置信度85%
    }

    /**
     * 构建AI凭证生成提示词
     */
    private String buildVoucherGenerationPrompt(Integer accountSetsId, List<Map<String, Object>> sourceItems) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("# 会计凭证智能生成系统\n\n");
        prompt.append("你是一位资深的会计师，具备深厚的会计理论知识和丰富的实务经验。");
        prompt.append("请根据以下票据和银行回单数据，结合企业会计科目体系，生成标准的会计凭证。\n\n");

        prompt.append("## 会计准则要求\n");
        prompt.append("1. **借贷平衡**: 每张凭证的借方金额必须等于贷方金额\n");
        prompt.append("2. **科目选择**: 根据业务性质选择正确的会计科目，必须使用科目编码+科目名称格式\n");
        prompt.append("3. **摘要规范**: 摘要应简明扼要地描述经济业务，便于理解和查找\n");
        prompt.append("4. **金额准确**: 金额必须与原始单据一致，保留两位小数\n");
        prompt.append("5. **日期合理**: 凭证日期应在业务发生期间\n");
        prompt.append("6. **凭证格式**: 凭证字号格式为'记-序号'，如'记-1'、'记-2'\n");
        prompt.append("7. **关联处理**: 当票据与银行回单存在关联关系时，应生成完整的业务循环凭证\n");
        prompt.append("8. **业务逻辑**: 根据票据类型和银行回单性质，判断是收入、支出、资产购置等业务类型\n\n");

        prompt.append("## 常见业务处理模式\n");
        prompt.append("### 销售业务（发票+银行回单）\n");
        prompt.append("- 开具销售发票时：借记应收账款，贷记主营业务收入、应交税费\n");
        prompt.append("- 收到货款时：借记银行存款，贷记应收账款\n\n");

        prompt.append("### 采购业务（发票+银行回单）\n");
        prompt.append("- 收到采购发票时：借记原材料/库存商品，借记应交税费，贷记应付账款\n");
        prompt.append("- 支付货款时：借记应付账款，贷记银行存款\n\n");

        prompt.append("### 费用业务（发票+银行回单）\n");
        prompt.append("- 收到费用发票时：借记管理费用/销售费用，借记应交税费，贷记应付账款\n");
        prompt.append("- 支付费用时：借记应付账款，贷记银行存款\n\n");

        prompt.append("## 科目余额分析指导\n");
        prompt.append("**重要提示**：在生成凭证时，请特别关注科目的当前余额情况：\n");
        prompt.append("1. **应收账款余额**：如果应收账款有借方余额，说明客户欠款，收款时应贷记应收账款\n");
        prompt.append("2. **应付账款余额**：如果应付账款有贷方余额，说明欠供应商款，付款时应借记应付账款\n");
        prompt.append("3. **银行存款余额**：关注银行存款余额是否充足，避免出现负数\n");
        prompt.append("4. **费用科目余额**：费用科目通常为借方余额，累计本期发生额\n");
        prompt.append("5. **收入科目余额**：收入科目通常为贷方余额，累计本期发生额\n");
        prompt.append("6. **余额为零的科目**：可能是新设立的科目或已结清的科目\n\n");

        // 添加科目体系信息
        prompt.append("## 企业会计科目体系\n");
        prompt.append(buildSubjectSystemInfo(accountSetsId));
        prompt.append("\n");
        
        prompt.append("## 常用会计科目参考\n");
        prompt.append("- **资产类**: 库存现金、银行存款、应收账款、预付账款、存货、固定资产等\n");
        prompt.append("- **负债类**: 应付账款、预收账款、应付职工薪酬、应交税费等\n");
        prompt.append("- **所有者权益**: 实收资本、资本公积、盈余公积、未分配利润等\n");
        prompt.append("- **收入类**: 主营业务收入、其他业务收入、营业外收入等\n");
        prompt.append("- **费用类**: 主营业务成本、销售费用、管理费用、财务费用等\n\n");
        
        prompt.append("## 待处理数据\n\n");
        
        for (int i = 0; i < sourceItems.size(); i++) {
            Map<String, Object> item = sourceItems.get(i);
            String type = (String) item.get("type");
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) item.get("data");

            // 添加空值检查
            if (data == null) {
                log.warn("数据项{}的data为null，跳过处理", i + 1);
                continue;
            }

            prompt.append(String.format("### 数据项%d [%s]\n", i + 1, type));

            if ("bill".equals(type)) {
                // 票据数据
                prompt.append(String.format("**票据编号**: %s\n", data.getOrDefault("billNo", "未知")));
                prompt.append(String.format("**金额**: ¥%s\n", data.getOrDefault("amount", 0.0)));
                prompt.append(String.format("**开票方**: %s\n", data.getOrDefault("issuer", "未知")));
                prompt.append(String.format("**收票方**: %s\n", data.getOrDefault("recipient", "未知")));
                prompt.append(String.format("**摘要**: %s\n", data.getOrDefault("summary", "未知")));
                prompt.append(String.format("**日期**: %s\n", data.getOrDefault("billDate", "未知")));
                prompt.append(String.format("**票据类型**: %s\n", data.getOrDefault("type", "未知")));

                // 添加关联的银行回单信息
                if (item.containsKey("relatedReceipts")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> relatedReceipts = (List<Map<String, Object>>) item.get("relatedReceipts");
                    if (!relatedReceipts.isEmpty()) {
                        prompt.append("**关联银行回单**:\n");
                        for (Map<String, Object> receipt : relatedReceipts) {
                            prompt.append(String.format("  - 回单号: %s, 金额: ¥%s, 关联类型: %s\n",
                                receipt.getOrDefault("receiptsNo", "未知"),
                                receipt.getOrDefault("amount", 0.0),
                                receipt.getOrDefault("relationType", "未知")
                            ));
                        }
                    }
                }

            } else if ("receipt".equals(type)) {
                // 银行回单数据
                prompt.append(String.format("**回单编号**: %s\n", data.getOrDefault("receiptsNo", "未知")));
                prompt.append(String.format("**金额**: ¥%s\n", data.getOrDefault("amount", 0.0)));
                prompt.append(String.format("**交易对手**: %s\n", data.getOrDefault("counterparty", "未知")));
                prompt.append(String.format("**摘要**: %s\n", data.getOrDefault("summary", "未知")));
                prompt.append(String.format("**日期**: %s\n", data.getOrDefault("receiptsDate", "未知")));
                prompt.append(String.format("**交易类型**: %s\n", data.getOrDefault("type", "未知")));

                // 添加关联的票据信息
                if (item.containsKey("relatedBills")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> relatedBills = (List<Map<String, Object>>) item.get("relatedBills");
                    if (!relatedBills.isEmpty()) {
                        prompt.append("**关联票据**:\n");
                        for (Map<String, Object> bill : relatedBills) {
                            prompt.append(String.format("  - 票据号: %s, 金额: ¥%s, 关联类型: %s\n",
                                bill.getOrDefault("billNo", "未知"),
                                bill.getOrDefault("amount", 0.0),
                                bill.getOrDefault("relationType", "未知")
                            ));
                        }
                    }
                }

            } else if ("relation".equals(type)) {
                // 关联数据
                prompt.append(String.format("**票据编号**: %s\n", data.getOrDefault("billNo", "未知")));
                prompt.append(String.format("**银行回单编号**: %s\n", data.getOrDefault("receiptNo", "未知")));
                prompt.append(String.format("**关联金额**: ¥%s\n", data.getOrDefault("amount", 0.0)));
                prompt.append(String.format("**关联类型**: %s\n", data.getOrDefault("relationType", "未知")));
                prompt.append(String.format("**业务描述**: %s\n", data.getOrDefault("description", "未知")));

            } else if ("group".equals(type)) {
                // 组数据
                prompt.append(String.format("**组ID**: %s\n", data.getOrDefault("groupId", "未知")));
                prompt.append(String.format("**组名称**: %s\n", data.getOrDefault("groupName", "未知")));
                prompt.append(String.format("**总金额**: ¥%s\n", data.getOrDefault("totalAmount", 0.0)));
                prompt.append(String.format("**项目数量**: %s\n", data.getOrDefault("itemCount", 0)));
                prompt.append(String.format("**摘要**: %s\n", data.getOrDefault("summary", "未知")));
            }
            prompt.append("\n");
        }
        
        prompt.append("## 输出要求\n\n");
        prompt.append("请严格按照以下JSON格式返回生成的会计凭证，不要包含任何其他文字：\n\n");
        prompt.append("```json\n");
        prompt.append("{\n");
        prompt.append("  \"vouchers\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"word\": \"记\",\n");
        prompt.append("      \"code\": 1,\n");
        prompt.append("      \"remark\": \"凭证摘要\",\n");
        prompt.append("      \"totalAmount\": 10000.00,\n");
        prompt.append("      \"confidence\": 95,\n");
        prompt.append("      \"aiReasoning\": \"AI分析过程和依据\",\n");
        prompt.append("      \"sourceData\": {\n");
        prompt.append("        \"billId\": 票据ID(如果有),\n");
        prompt.append("        \"receiptId\": 银行回单ID(如果有)\n");
        prompt.append("      },\n");
        prompt.append("      \"details\": [\n");
        prompt.append("        {\n");
        prompt.append("          \"subjectCode\": \"1001\",\n");
        prompt.append("          \"subjectName\": \"库存现金\",\n");
        prompt.append("          \"summary\": \"分录摘要\",\n");
        prompt.append("          \"debitAmount\": 10000.00,\n");
        prompt.append("          \"creditAmount\": null\n");
        prompt.append("        },\n");
        prompt.append("        {\n");
        prompt.append("          \"subjectCode\": \"5401\",\n");
        prompt.append("          \"subjectName\": \"主营业务成本\",\n");
        prompt.append("          \"summary\": \"分录摘要\",\n");
        prompt.append("          \"debitAmount\": null,\n");
        prompt.append("          \"creditAmount\": 10000.00\n");
        prompt.append("        }\n");
        prompt.append("      ]\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n");
        prompt.append("```\n\n");
        
        prompt.append("注意事项：\n");
        prompt.append("1. 每张凭证必须借贷平衡\n");
        prompt.append("2. 科目代码和名称要准确\n");
        prompt.append("3. 置信度范围0-100，反映生成质量\n");
        prompt.append("4. aiReasoning要详细说明分析依据\n");
        prompt.append("5. 确保JSON格式正确，可以被程序解析\n");
        prompt.append("6. **不要生成voucherDate字段**，凭证日期由系统根据当前期间自动设置\n");
        
        return prompt.toString();
    }

    /**
     * 构建科目体系信息
     */
    private String buildSubjectSystemInfo(Integer accountSetsId) {
        StringBuilder subjectInfo = new StringBuilder();

        try {
            subjectInfo.append("### 企业会计科目体系（含余额信息）\n");
            subjectInfo.append("| 科目编码 | 科目名称 | 科目类型 | 余额方向 | 当前余额 | AI增强信息 |\n");
            subjectInfo.append("|---------|---------|---------|---------|---------|----------|\n");

            // 查询账套的科目信息
            if (subjectMapper != null && subjectService != null) {
                List<Subject> subjects = subjectService.list(
                    new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Subject>()
                        .eq("account_sets_id", accountSetsId)
                        .eq("status", true)
                        .orderByAsc("code")
                        .last("LIMIT 200") // 增加科目数量限制，确保AI能看到完整的科目体系
                );

                // 获取科目余额信息
                java.util.Date currentDate = new java.util.Date();
                List<BalanceVo> balanceList = null;
                try {
                    balanceList = subjectService.subjectBalance(currentDate, accountSetsId, false);
                } catch (Exception e) {
                    log.debug("获取科目余额失败: {}", e.getMessage());
                }

                // 创建余额映射
                Map<Integer, BalanceVo> balanceMap = new HashMap<>();
                if (balanceList != null) {
                    for (BalanceVo balance : balanceList) {
                        balanceMap.put(balance.getSubjectId(), balance);
                    }
                }

                for (Subject subject : subjects) {
                    String aiInfo = getSubjectAiInfo(subject.getId(), accountSetsId);
                    String balanceInfo = getSubjectBalanceInfo(subject.getId(), balanceMap);

                    subjectInfo.append(String.format("| %s | %s | %s | %s | %s | %s |\n",
                        subject.getCode(),
                        subject.getName(),
                        subject.getType() != null ? subject.getType().toString() : "未知",
                        subject.getBalanceDirection() != null ? subject.getBalanceDirection().toString() : "未知",
                        balanceInfo,
                        aiInfo
                    ));
                }
            }

            // 如果没有查询到科目或查询失败，使用默认科目
            if (subjectInfo.toString().split("\n").length <= 3) {
                subjectInfo.append("| 1001 | 库存现金 | 资产 | 借方 | 现金收支业务 |\n");
                subjectInfo.append("| 1002 | 银行存款 | 资产 | 借方 | 银行存款收支 |\n");
                subjectInfo.append("| 1122 | 应收账款 | 资产 | 借方 | 应收账款管理 |\n");
                subjectInfo.append("| 2202 | 应付账款 | 负债 | 贷方 | 应付账款管理 |\n");
                subjectInfo.append("| 5001 | 主营业务收入 | 损益 | 贷方 | 主营业务收入 |\n");
                subjectInfo.append("| 5401 | 主营业务成本 | 损益 | 借方 | 主营业务成本 |\n");
                subjectInfo.append("| 5601 | 销售费用 | 损益 | 借方 | 销售费用 |\n");
                subjectInfo.append("| 5602 | 管理费用 | 损益 | 借方 | 管理费用 |\n");
                subjectInfo.append("| 5603 | 财务费用 | 损益 | 借方 | 财务费用 |\n");
            }

            // 添加关联关系信息
            subjectInfo.append("\n### 票据银证关联关系\n");
            subjectInfo.append(buildRelationInfo(accountSetsId));

        } catch (Exception e) {
            log.warn("获取科目信息失败: {}", e.getMessage());
            subjectInfo.append("科目信息获取失败，使用标准会计科目。\n");
            // 添加默认科目信息
            subjectInfo.append("| 1001 | 库存现金 | 资产 | 借方 | 现金收支业务 |\n");
            subjectInfo.append("| 1002 | 银行存款 | 资产 | 借方 | 银行存款收支 |\n");
            subjectInfo.append("| 1122 | 应收账款 | 资产 | 借方 | 应收账款管理 |\n");
            subjectInfo.append("| 2202 | 应付账款 | 负债 | 贷方 | 应付账款管理 |\n");
            subjectInfo.append("| 5001 | 主营业务收入 | 损益 | 贷方 | 主营业务收入 |\n");
            subjectInfo.append("| 5401 | 主营业务成本 | 损益 | 借方 | 主营业务成本 |\n");
            subjectInfo.append("| 5601 | 销售费用 | 损益 | 借方 | 销售费用 |\n");
            subjectInfo.append("| 5602 | 管理费用 | 损益 | 借方 | 管理费用 |\n");
            subjectInfo.append("| 5603 | 财务费用 | 损益 | 借方 | 财务费用 |\n");
        }

        return subjectInfo.toString();
    }

    /**
     * 获取科目AI增强信息
     */
    private String getSubjectAiInfo(Integer subjectId, Integer accountSetsId) {
        try {
            if (subjectAiEnhancementMapper != null) {
                SubjectAiEnhancement enhancement = subjectAiEnhancementMapper.getBySubjectAndAccount(subjectId, accountSetsId);
                if (enhancement != null) {
                    return String.format("关键词:%s,置信度:%.1f",
                        enhancement.getAiKeywords() != null ? enhancement.getAiKeywords() : "无",
                        enhancement.getConfidenceScore() != null ? enhancement.getConfidenceScore().doubleValue() : 0.0);
                }
            }
            return "无AI增强";
        } catch (Exception e) {
            log.debug("获取科目AI增强信息失败: {}", e.getMessage());
            return "无AI增强";
        }
    }

    /**
     * 获取科目余额信息
     */
    private String getSubjectBalanceInfo(Integer subjectId, Map<Integer, BalanceVo> balanceMap) {
        try {
            BalanceVo balance = balanceMap.get(subjectId);
            if (balance != null) {
                // 计算期末余额
                Double endingDebitBalance = balance.getEndingDebitBalance();
                Double endingCreditBalance = balance.getEndingCreditBalance();

                if (endingDebitBalance != null && endingDebitBalance != 0) {
                    return String.format("借方¥%.2f", endingDebitBalance);
                } else if (endingCreditBalance != null && endingCreditBalance != 0) {
                    return String.format("贷方¥%.2f", endingCreditBalance);
                } else {
                    return "余额为零";
                }
            }
            return "无余额数据";
        } catch (Exception e) {
            log.debug("获取科目余额信息失败: {}", e.getMessage());
            return "余额获取失败";
        }
    }

    /**
     * 构建关联关系信息
     */
    private String buildRelationInfo(Integer accountSetsId) {
        StringBuilder relationInfo = new StringBuilder();

        try {
            if (entityRelationMapper != null) {
                // 查询跨类型关联关系（票据与银证的关联）
                List<EntityRelation> relations = entityRelationMapper.findCrossTypeRelations(accountSetsId);

                if (!relations.isEmpty()) {
                    relationInfo.append("当前存在以下票据银证关联关系，请在生成凭证时考虑这些关联：\n");
                    for (EntityRelation relation : relations) {
                        relationInfo.append(String.format("- %s(%s) 关联 %s(%s), 关联类型: %s, 金额: ¥%.2f\n",
                            relation.getSourceType(), relation.getSourceId(),
                            relation.getTargetType(), relation.getTargetId(),
                            relation.getRelationType(),
                            relation.getRelationAmount() != null ? relation.getRelationAmount().doubleValue() : 0.0
                        ));
                    }
                } else {
                    relationInfo.append("暂无票据银证关联关系。\n");
                }
            }
        } catch (Exception e) {
            log.debug("获取关联关系信息失败: {}", e.getMessage());
            relationInfo.append("关联关系信息获取失败。\n");
        }

        return relationInfo.toString();
    }



    /**
     * 解析AI凭证响应
     */
    private List<Map<String, Object>> parseAiVoucherResponse(String aiResponse, List<Map<String, Object>> sourceItems) {
        List<Map<String, Object>> vouchers = new ArrayList<>();
        
        try {
            // 提取JSON部分
            String jsonStr = extractJsonFromResponse(aiResponse);
            log.info("提取的JSON: {}", jsonStr.substring(0, Math.min(jsonStr.length(), 500)) + "...");
            
            JSONObject jsonResponse = JSON.parseObject(jsonStr);
            
            if (jsonResponse.containsKey("vouchers")) {
                JSONArray vouchersArray = jsonResponse.getJSONArray("vouchers");
                
                for (int i = 0; i < vouchersArray.size(); i++) {
                    JSONObject voucherJson = vouchersArray.getJSONObject(i);
                    Map<String, Object> voucher = parseVoucherFromJson(voucherJson, sourceItems);
                    if (voucher != null) {
                        vouchers.add(voucher);
                    }
                }
            }
            
            log.info("成功解析AI凭证数量: {}", vouchers.size());
            
        } catch (Exception e) {
            log.warn("解析AI凭证响应失败: {}", e.getMessage(), e);
        }
        
        return vouchers;
    }

    /**
     * 从JSON解析单个凭证
     */
    private Map<String, Object> parseVoucherFromJson(JSONObject voucherJson, List<Map<String, Object>> sourceItems) {
        try {
            Map<String, Object> voucher = new HashMap<>();
            
            // 基本信息
            voucher.put("id", "temp_" + System.currentTimeMillis() + "_" + Math.random());
            voucher.put("word", voucherJson.getString("word"));
            voucher.put("code", voucherJson.getInteger("code"));
            voucher.put("remark", voucherJson.getString("remark"));
            // 强制不设置voucherDate，即使AI生成了这个字段也忽略
            // 凭证日期将由后端根据当前期间自动设置
            voucher.put("totalAmount", voucherJson.getDouble("totalAmount"));
            voucher.put("confidence", voucherJson.getInteger("confidence"));
            voucher.put("aiReasoning", voucherJson.getString("aiReasoning"));

            log.info("解析AI凭证时忽略voucherDate字段，将使用当前期间设置日期");
            
            // 源数据信息
            if (voucherJson.containsKey("sourceData")) {
                JSONObject sourceData = voucherJson.getJSONObject("sourceData");
                voucher.put("sourceData", sourceData);
                
                // 查找并关联源数据对象
                if (sourceData.containsKey("billId")) {
                    Object billIdObj = sourceData.get("billId");
                    // 从sourceItems中查找对应的票据
                    for (Map<String, Object> item : sourceItems) {
                        if ("bill".equals(item.get("type"))) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> billData = (Map<String, Object>) item.get("data");

                            // 支持多种ID格式匹配
                            Object dataId = billData.get("id");
                            String dataBillNo = (String) billData.get("billNo");

                            if (billIdObj.equals(dataId) ||
                                billIdObj.equals(dataBillNo) ||
                                String.valueOf(billIdObj).equals(String.valueOf(dataId)) ||
                                String.valueOf(billIdObj).equals(dataBillNo)) {
                                voucher.put("sourceBill", billData);
                                break;
                            }
                        }
                    }
                }
                
                if (sourceData.containsKey("receiptId")) {
                    Object receiptIdObj = sourceData.get("receiptId");
                    // 从sourceItems中查找对应的银行回单
                    for (Map<String, Object> item : sourceItems) {
                        if ("receipt".equals(item.get("type"))) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> receiptData = (Map<String, Object>) item.get("data");

                            // 支持多种ID格式匹配
                            Object dataId = receiptData.get("id");
                            String dataReceiptNo = (String) receiptData.get("receiptsNo");

                            if (receiptIdObj.equals(dataId) ||
                                receiptIdObj.equals(dataReceiptNo) ||
                                String.valueOf(receiptIdObj).equals(String.valueOf(dataId)) ||
                                String.valueOf(receiptIdObj).equals(dataReceiptNo)) {
                                voucher.put("sourceReceipt", receiptData);
                                break;
                            }
                        }
                    }
                }
            }
            
            // 凭证明细
            if (voucherJson.containsKey("details")) {
                JSONArray detailsArray = voucherJson.getJSONArray("details");
                List<Map<String, Object>> details = new ArrayList<>();
                
                for (int j = 0; j < detailsArray.size(); j++) {
                    JSONObject detailJson = detailsArray.getJSONObject(j);
                    Map<String, Object> detail = new HashMap<>();

                    detail.put("id", "temp_detail_" + j);
                    detail.put("subjectCode", detailJson.getString("subjectCode"));
                    detail.put("subjectName", detailJson.getString("subjectName"));
                    detail.put("summary", detailJson.getString("summary"));

                    // 安全地解析金额，支持null值
                    Object debitAmountObj = detailJson.get("debitAmount");
                    Object creditAmountObj = detailJson.get("creditAmount");

                    Double debitAmount = null;
                    Double creditAmount = null;

                    if (debitAmountObj != null && !"null".equals(String.valueOf(debitAmountObj))) {
                        try {
                            debitAmount = Double.valueOf(String.valueOf(debitAmountObj));
                        } catch (NumberFormatException e) {
                            log.warn("解析借方金额失败: {}", debitAmountObj);
                        }
                    }

                    if (creditAmountObj != null && !"null".equals(String.valueOf(creditAmountObj))) {
                        try {
                            creditAmount = Double.valueOf(String.valueOf(creditAmountObj));
                        } catch (NumberFormatException e) {
                            log.warn("解析贷方金额失败: {}", creditAmountObj);
                        }
                    }

                    detail.put("debitAmount", debitAmount);
                    detail.put("creditAmount", creditAmount);

                    details.add(detail);
                }
                
                voucher.put("details", details);
            }
            
            return voucher;
            
        } catch (Exception e) {
            log.warn("解析单个凭证失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从响应中提取JSON
     */
    private String extractJsonFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return "{}";
        }
        
        // 移除markdown代码块标记
        String cleaned = response.replaceAll("```json\\s*", "").replaceAll("```\\s*", "");
        
        // 尝试提取JSON部分
        int jsonStart = cleaned.indexOf("{");
        int jsonEnd = findMatchingBrace(cleaned, jsonStart);

        if (jsonStart >= 0 && jsonEnd > jsonStart) {
            return cleaned.substring(jsonStart, jsonEnd + 1);
        }

        log.warn("无法从AI响应中提取有效JSON");
        return "{}";
    }

    /**
     * 查找匹配的大括号
     */
    private int findMatchingBrace(String text, int start) {
        if (start < 0 || start >= text.length() || text.charAt(start) != '{') {
            return -1;
        }
        
        int count = 1;
        for (int i = start + 1; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == '{') {
                count++;
            } else if (c == '}') {
                count--;
                if (count == 0) {
                    return i;
                }
            }
        }
        return -1;
    }





    @Override
    public Map<String, Object> getGenerationStatistics(Integer accountSetsId, Integer userId) {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 这里应该查询真实的统计数据
            // 暂时返回模拟数据
            statistics.put("todayGenerated", 12);
            statistics.put("accuracy", 95.8);
            statistics.put("totalGenerated", 156);
            statistics.put("confirmedCount", 142);
            statistics.put("rejectedCount", 14);

            log.info("获取AI凭证生成统计，账套ID: {}, 用户ID: {}", accountSetsId, userId);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            // 返回默认值
            statistics.put("todayGenerated", 0);
            statistics.put("accuracy", 0.0);
            statistics.put("totalGenerated", 0);
            statistics.put("confirmedCount", 0);
            statistics.put("rejectedCount", 0);
        }

        return statistics;
    }

    @Override
    public Map<String, Object> getGenerationHistory(Integer accountSetsId, Integer userId, Integer page, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 这里应该查询真实的历史记录
            List<Map<String, Object>> records = new ArrayList<>();

            // 暂时返回空数据
            result.put("records", records);
            result.put("total", 0);
            result.put("page", page);
            result.put("pageSize", pageSize);

            log.info("获取AI凭证生成历史，账套ID: {}, 用户ID: {}, 页码: {}", accountSetsId, userId, page);
        } catch (Exception e) {
            log.error("获取生成历史失败", e);
            result.put("records", new ArrayList<>());
            result.put("total", 0);
            result.put("page", page);
            result.put("pageSize", pageSize);
        }

        return result;
    }

    @Override
    public Map<String, Object> regenerateVoucher(Integer accountSetsId, Integer originalVoucherId,
                                                Map<String, Object> parameters, Integer userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("重新生成凭证，原凭证ID: {}, 账套ID: {}, 用户ID: {}", originalVoucherId, accountSetsId, userId);

            // 这里应该根据原凭证和新参数重新生成
            // 暂时返回失败
            result.put("success", false);
            result.put("message", "重新生成功能暂未实现");
            result.put("data", new ArrayList<>());

        } catch (Exception e) {
            log.error("重新生成凭证失败", e);
            result.put("success", false);
            result.put("message", "重新生成失败: " + e.getMessage());
            result.put("data", new ArrayList<>());
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getVoucherTemplates(Integer accountSetsId) {
        List<Map<String, Object>> templates = new ArrayList<>();

        try {
            log.info("获取AI凭证模板，账套ID: {}", accountSetsId);

            // 这里应该查询真实的模板数据
            // 暂时返回空列表

        } catch (Exception e) {
            log.error("获取凭证模板失败", e);
        }

        return templates;
    }

    @Override
    public boolean saveVoucherTemplate(Integer accountSetsId, Map<String, Object> template, Integer userId) {
        try {
            log.info("保存AI凭证模板，账套ID: {}, 用户ID: {}", accountSetsId, userId);
            log.info("模板数据: {}", template);

            // 这里应该保存模板到数据库
            // 暂时返回成功
            return true;

        } catch (Exception e) {
            log.error("保存凭证模板失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> previewVoucher(Integer accountSetsId, List<Map<String, Object>> sourceItems, Integer userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("预览AI凭证，账套ID: {}, 用户ID: {}, 数据源数量: {}", accountSetsId, userId, sourceItems.size());

            // 这里应该生成预览数据，不保存到数据库
            // 可以复用generateVouchers的逻辑，但不执行保存操作
            // 使用当前年月作为默认值
            LocalDate now = LocalDate.now();
            Map<String, Object> generateResult = generateVouchers(accountSetsId, sourceItems, userId, now.getYear(), now.getMonthValue());

            result.put("success", generateResult.get("success"));
            result.put("data", generateResult.get("data"));
            result.put("message", "预览生成完成");

        } catch (Exception e) {
            log.error("预览凭证失败", e);
            result.put("success", false);
            result.put("message", "预览失败: " + e.getMessage());
            result.put("data", new ArrayList<>());
        }

        return result;
    }

    @Override
    public Map<String, Object> analyzeSourceData(Integer accountSetsId, Map<String, Object> sourceData, Integer userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("分析源数据，账套ID: {}, 用户ID: {}", accountSetsId, userId);
            log.info("源数据: {}", sourceData);

            // 这里应该使用AI分析源数据
            // 暂时返回基本分析结果
            result.put("businessType", "未知业务");
            result.put("suggestedSubjects", new ArrayList<>());
            result.put("confidence", 0.5);
            result.put("analysis", "数据分析功能暂未实现");

        } catch (Exception e) {
            log.error("分析源数据失败", e);
            result.put("businessType", "分析失败");
            result.put("suggestedSubjects", new ArrayList<>());
            result.put("confidence", 0.0);
            result.put("analysis", "分析失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmGeneratedVoucher(Integer accountSetsId, Map<String, Object> voucherData, Integer userId) {
        try {
            log.info("确认AI生成的凭证，账套ID: {}, 用户ID: {}", accountSetsId, userId);
            log.info("凭证数据: {}", voucherData);

            // 验证凭证数据完整性
            if (!validateVoucherData(voucherData)) {
                log.error("凭证数据验证失败");
                throw new RuntimeException("凭证数据不完整或格式错误");
            }

            // 获取账套当前期间
            AccountSets accountSets = accountSetsMapper.selectById(accountSetsId);
            Integer currentYear = null;
            Integer currentMonth = null;

            if (accountSets != null && accountSets.getCurrentAccountDate() != null) {
                LocalDate currentAccountDate = accountSets.getCurrentAccountDate().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate();
                currentYear = currentAccountDate.getYear();
                currentMonth = currentAccountDate.getMonthValue();
                log.info("使用账套当前期间: {}-{}", currentYear, currentMonth);
            } else {
                // 如果没有当前期间，使用当前日期
                LocalDate now = LocalDate.now();
                currentYear = now.getYear();
                currentMonth = now.getMonthValue();
                log.warn("账套当前期间为空，使用当前日期: {}-{}", currentYear, currentMonth);
            }

            // 将Map转换为Voucher实体
            Voucher voucher = convertMapToVoucher(voucherData, accountSetsId, userId, currentYear, currentMonth);

            // 验证借贷平衡
            if (!isVoucherBalanced(voucher)) {
                log.error("凭证借贷不平衡，借方: {}, 贷方: {}", voucher.getDebitAmount(), voucher.getCreditAmount());
                throw new RuntimeException("凭证借贷不平衡");
            }

            // 保存凭证到数据库
            if (voucherService != null) {
                boolean success = voucherService.save(voucher);
                if (success) {
                    log.info("✅ AI凭证成功保存到数据库，凭证ID: {}, 字号: {}-{}, 金额: {}",
                        voucher.getId(), voucher.getWord(), voucher.getCode(), voucher.getDebitAmount());
                    return true;
                } else {
                    log.error("❌ 凭证保存失败");
                    throw new RuntimeException("凭证保存失败");
                }
            } else {
                // VoucherService不可用时的简化处理
                log.info("📝 VoucherService不可用，AI凭证生成功能独立运行");
                log.info("✅ AI凭证生成完成，字号: {}-{}, 金额: {}, 分录数: {}",
                    voucher.getWord(), voucher.getCode(), voucher.getDebitAmount(),
                    voucher.getDetails() != null ? voucher.getDetails().size() : 0);

                // 在实际应用中，这里应该调用标准的凭证保存API
                // 目前返回成功，表示AI凭证生成功能可以独立工作
                return true;
            }

        } catch (Exception e) {
            log.error("确认AI凭证失败", e);
            throw new RuntimeException("确认凭证失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public int batchConfirmVouchers(Integer accountSetsId, List<Map<String, Object>> vouchers, Integer userId) {
        int confirmedCount = 0;

        for (Map<String, Object> voucherData : vouchers) {
            try {
                if (confirmGeneratedVoucher(accountSetsId, voucherData, userId)) {
                    confirmedCount++;
                }
            } catch (Exception e) {
                log.warn("批量确认凭证失败，跳过该凭证: {}", e.getMessage());
            }
        }

        log.info("批量确认完成，成功确认: {}/{}", confirmedCount, vouchers.size());
        return confirmedCount;
    }

    /**
     * 验证凭证数据完整性
     */
    private boolean validateVoucherData(Map<String, Object> voucherData) {
        if (voucherData == null || voucherData.isEmpty()) {
            log.error("凭证数据为空");
            return false;
        }

        // 检查必要字段
        String[] requiredFields = {"voucherWord", "voucherNumber", "entries"};
        for (String field : requiredFields) {
            if (!voucherData.containsKey(field) || voucherData.get(field) == null) {
                log.error("缺少必要字段: {}", field);
                return false;
            }
        }

        // 检查分录数据
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> entries = (List<Map<String, Object>>) voucherData.get("entries");
        if (entries == null || entries.isEmpty()) {
            log.error("凭证分录为空");
            return false;
        }

        // 验证每个分录
        for (Map<String, Object> entry : entries) {
            if (!validateEntryData(entry)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证分录数据
     */
    private boolean validateEntryData(Map<String, Object> entry) {
        if (entry == null) {
            return false;
        }

        // 检查科目代码和名称
        String subjectCode = (String) entry.get("subjectCode");
        String subjectName = (String) entry.get("subjectName");

        if (subjectCode == null || subjectCode.trim().isEmpty()) {
            log.error("分录缺少科目代码");
            return false;
        }

        if (subjectName == null || subjectName.trim().isEmpty()) {
            log.error("分录缺少科目名称");
            return false;
        }

        // 检查金额（借方或贷方至少有一个）
        Double debitAmount = convertToDouble(entry.get("debitAmount"));
        Double creditAmount = convertToDouble(entry.get("creditAmount"));

        if ((debitAmount == null || debitAmount == 0) && (creditAmount == null || creditAmount == 0)) {
            log.error("分录借贷金额都为空或零");
            return false;
        }

        return true;
    }

    /**
     * 安全地将Object转换为Double
     */
    private Double convertToDouble(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Double) {
            return (Double) value;
        } else if (value instanceof BigDecimal) {
            return ((BigDecimal) value).doubleValue();
        } else if (value instanceof Integer) {
            return ((Integer) value).doubleValue();
        } else if (value instanceof Long) {
            return ((Long) value).doubleValue();
        } else if (value instanceof Float) {
            return ((Float) value).doubleValue();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为Double: {}", value);
                return null;
            }
        }

        log.warn("无法将类型{}转换为Double: {}", value.getClass().getSimpleName(), value);
        return null;
    }

    /**
     * 验证凭证借贷平衡
     */
    private boolean isVoucherBalanced(Voucher voucher) {
        if (voucher.getDetails() == null || voucher.getDetails().isEmpty()) {
            return false;
        }

        double totalDebit = 0.0;
        double totalCredit = 0.0;

        for (VoucherDetails detail : voucher.getDetails()) {
            if (detail.getDebitAmount() != null) {
                totalDebit += detail.getDebitAmount();
            }
            if (detail.getCreditAmount() != null) {
                totalCredit += detail.getCreditAmount();
            }
        }

        // 允许小数点后2位的误差
        double difference = Math.abs(totalDebit - totalCredit);
        boolean balanced = difference < 0.01;

        if (!balanced) {
            log.error("凭证借贷不平衡，借方合计: {}, 贷方合计: {}, 差额: {}", totalDebit, totalCredit, difference);
        }

        return balanced;
    }

    /**
     * 将Map转换为Voucher实体
     */
    private Voucher convertMapToVoucher(Map<String, Object> voucherData, Integer accountSetsId, Integer userId, Integer voucherYear, Integer voucherMonth) {
        Voucher voucher = new Voucher();

        // 设置基本信息
        voucher.setWord((String) voucherData.get("voucherWord"));

        // 处理凭证号码 - 使用VoucherService.loadCode获取下一个可用的凭证号
        String voucherWord = (String) voucherData.get("voucherWord");
        if (voucherWord == null || voucherWord.trim().isEmpty()) {
            voucherWord = "记"; // 默认凭证字
        }

        // 先设置年月信息，因为loadCode方法需要用到
        if (voucherYear != null) {
            voucher.setVoucherYear(voucherYear);
        }
        if (voucherMonth != null) {
            voucher.setVoucherMonth(voucherMonth);
        }

        // 构建凭证日期用于loadCode方法
        Date currentAccountDate;
        if (voucherYear != null && voucherMonth != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(voucherYear, voucherMonth - 1, 1); // 月份从0开始
            currentAccountDate = calendar.getTime();
        } else {
            currentAccountDate = new Date();
        }

        // 调用VoucherService.loadCode获取下一个可用的凭证号
        int nextCode = 1; // 默认值
        if (voucherService != null) {
            try {
                nextCode = voucherService.loadCode(accountSetsId, voucherWord, currentAccountDate);
                log.info("通过VoucherService.loadCode获取到下一个可用凭证号: {}-{}", voucherWord, nextCode);
            } catch (Exception e) {
                log.warn("调用VoucherService.loadCode失败，使用默认凭证号1: {}", e.getMessage());
                nextCode = 1;
            }
        } else {
            log.warn("VoucherService未注入，使用默认凭证号1");
        }

        voucher.setCode(nextCode);

        voucher.setRemark((String) voucherData.get("aiAnalysis"));
        voucher.setAccountSetsId(accountSetsId);
        voucher.setCreateMember(userId);
        voucher.setCreateDate(new Date());

        // 设置结转损益标志，默认为false（非结转凭证）
        voucher.setCarryForward(false);

        // 设置年月信息（使用传入的参数）
        if (voucherYear != null) {
            voucher.setVoucherYear(voucherYear);
        }
        if (voucherMonth != null) {
            voucher.setVoucherMonth(voucherMonth);
        }

        // 设置附件数量
        Integer attachmentCount = (Integer) voucherData.get("attachmentCount");
        if (attachmentCount != null) {
            voucher.setReceiptNum(attachmentCount);
        } else {
            voucher.setReceiptNum(1); // 默认1张附件
        }

        // 使用传入的年月参数构建凭证日期
        LocalDate voucherDate;
        if (voucherYear != null && voucherMonth != null) {
            // 使用传入的年月，日期设为当月第一天
            voucherDate = LocalDate.of(voucherYear, voucherMonth, 1);
            log.info("使用页面传入的凭证期间: {}-{}", voucherYear, voucherMonth);
        } else {
            // 如果没有传入年月，使用当前日期
            voucherDate = LocalDate.now();
            log.warn("未传入凭证年月，使用当前日期: {}", voucherDate);
        }

        // 获取账套启用日期进行验证
        AccountSets accountSets = accountSetsMapper.selectById(accountSetsId);
        if (accountSets != null && accountSets.getEnableDate() != null) {
            LocalDate enableDate = accountSets.getEnableDate().toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDate();

            // 检查凭证日期是否早于账套启用日期
            if (voucherDate.isBefore(enableDate)) {
                log.warn("凭证日期 {} 早于账套启用日期 {}，调整为账套启用日期", voucherDate, enableDate);
                voucherDate = enableDate;
                // 更新年月信息
                voucher.setVoucherYear(voucherDate.getYear());
                voucher.setVoucherMonth(voucherDate.getMonthValue());
            }
        }

        // 设置凭证日期
        Date sqlDate = java.sql.Date.valueOf(voucherDate);
        voucher.setVoucherDate(sqlDate);

        // 转换凭证明细（AI生成的数据使用"entries"字段）
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> entriesData = (List<Map<String, Object>>) voucherData.get("entries");
        if (entriesData != null && !entriesData.isEmpty()) {
            List<VoucherDetails> details = new ArrayList<>();
            double totalDebit = 0.0;
            double totalCredit = 0.0;

            for (Map<String, Object> entryData : entriesData) {
                VoucherDetails detail = convertMapToVoucherDetail(entryData, accountSetsId);
                details.add(detail);

                // 累计借贷金额
                if (detail.getDebitAmount() != null) {
                    totalDebit += detail.getDebitAmount();
                }
                if (detail.getCreditAmount() != null) {
                    totalCredit += detail.getCreditAmount();
                }
            }

            voucher.setDetails(details);
            voucher.setDebitAmount(totalDebit);
            voucher.setCreditAmount(totalCredit);

            log.info("凭证分录转换完成，分录数: {}, 借方合计: {}, 贷方合计: {}",
                details.size(), totalDebit, totalCredit);
        } else {
            log.error("凭证分录数据为空");
            throw new RuntimeException("凭证分录数据为空");
        }

        return voucher;
    }

    /**
     * 将Map转换为VoucherDetails实体
     */
    private VoucherDetails convertMapToVoucherDetail(Map<String, Object> detailData, Integer accountSetsId) {
        VoucherDetails detail = new VoucherDetails();

        String subjectCode = (String) detailData.get("subjectCode");
        String subjectName = (String) detailData.get("subjectName");

        detail.setSubjectCode(subjectCode);
        detail.setSubjectName(subjectName);
        detail.setSummary((String) detailData.get("summary"));
        detail.setAccountSetsId(accountSetsId);

        // 根据科目代码查找科目ID
        if (subjectCode != null && subjectService != null) {
            try {
                Subject subject = subjectService.getOne(
                    new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Subject>()
                        .eq("account_sets_id", accountSetsId)
                        .eq("code", subjectCode)
                        .eq("status", true)
                );
                if (subject != null) {
                    detail.setSubjectId(subject.getId());
                    log.debug("找到科目ID: {} for 科目代码: {}", subject.getId(), subjectCode);
                } else {
                    log.error("未找到科目代码: {} 对应的科目，账套ID: {}", subjectCode, accountSetsId);
                    throw new RuntimeException("未找到科目代码: " + subjectCode + " 对应的科目");
                }
            } catch (Exception e) {
                log.error("查找科目ID失败，科目代码: {}, 错误: {}", subjectCode, e.getMessage());
                throw new RuntimeException("查找科目ID失败: " + e.getMessage());
            }
        } else {
            log.error("科目代码为空或subjectService不可用");
            throw new RuntimeException("科目代码为空或subjectService不可用");
        }

        // 设置金额
        Object debitAmount = detailData.get("debitAmount");
        Object creditAmount = detailData.get("creditAmount");

        if (debitAmount != null) {
            detail.setDebitAmount(convertToDouble(debitAmount));
        }
        if (creditAmount != null) {
            detail.setCreditAmount(convertToDouble(creditAmount));
        }

        // 设置必需的默认值
        detail.setCarryForward(false); // 默认不是结转凭证
        detail.setCumulativeDebit(0.0); // 累计借方金额默认为0
        detail.setCumulativeCredit(0.0); // 累计贷方金额默认为0
        detail.setCumulativeDebitNum(0.0); // 累计借方数量默认为0
        detail.setCumulativeCreditNum(0.0); // 累计贷方数量默认为0

        return detail;
    }


}
