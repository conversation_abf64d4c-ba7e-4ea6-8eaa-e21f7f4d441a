package cn.gson.financial.kernel.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 统一查询DTO
 */
@Data
public class UnifiedQueryDto {
    
    /**
     * 查询条件
     */
    private QueryCondition condition;
    
    /**
     * 分页参数
     */
    private PageParam pageParam;
    
    /**
     * 查询条件
     */
    @Data
    public static class QueryCondition {
        /**
         * 实体类型过滤：DOCUMENT, RECEIPT, DOCUMENT_GROUP, RECEIPT_GROUP
         */
        private List<String> entityTypes;
        
        /**
         * 关键字搜索
         */
        private String keyword;
        
        /**
         * 日期范围
         */
        private Date startDate;
        private Date endDate;
        
        /**
         * 金额范围
         */
        private BigDecimal minAmount;
        private BigDecimal maxAmount;
        
        /**
         * 状态过滤
         */
        private List<String> statuses;
        
        /**
         * 是否包含关联信息
         */
        private Boolean includeRelations = false;
        
        /**
         * 是否只查询有关联的记录
         */
        private Boolean onlyWithRelations = false;
        
        /**
         * 是否只查询跨类型关联
         */
        private Boolean onlyCrossTypeRelations = false;
    }
    
    /**
     * 分页参数
     */
    @Data
    public static class PageParam {
        /**
         * 页码（从1开始）
         */
        private Integer pageNum = 1;
        
        /**
         * 页大小
         */
        private Integer pageSize = 20;
        
        /**
         * 排序字段
         */
        private String orderBy = "created_at";
        
        /**
         * 排序方向：ASC, DESC
         */
        private String orderDirection = "DESC";
    }
}
