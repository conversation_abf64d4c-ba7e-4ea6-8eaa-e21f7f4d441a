package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 批量导入任务实体
 */
@Data
@TableName("fxy_financial_batch_import_task")
public class BatchImportTask {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 任务ID，使用UUID
     */
    @TableField("task_id")
    private String taskId;
    
    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;
    
    /**
     * 导入类型：BANK_RECEIPT-银行回单，INVOICE-发票
     */
    @TableField("import_type")
    private String importType;
    
    /**
     * 总文件数
     */
    @TableField("total_files")
    private Integer totalFiles;
    
    /**
     * 总图片数（PDF解析后）
     */
    @TableField("total_images")
    private Integer totalImages;
    
    /**
     * 已处理文件数
     */
    @TableField("processed_files")
    private Integer processedFiles;
    
    /**
     * 已处理图片数
     */
    @TableField("processed_images")
    private Integer processedImages;
    
    /**
     * 成功识别数量
     */
    @TableField("success_count")
    private Integer successCount;
    
    /**
     * 失败数量
     */
    @TableField("failed_count")
    private Integer failedCount;
    
    /**
     * 任务状态
     */
    @TableField("status")
    private String status;
    
    /**
     * 进度百分比
     */
    @TableField("progress_percentage")
    private BigDecimal progressPercentage;
    
    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;
    
    /**
     * 开始处理时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;
    
    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;
    
    /**
     * 账套ID
     */
    @TableField("account_sets_id")
    private Integer accountSetsId;
    
    /**
     * 创建用户ID
     */
    @TableField("create_user")
    private Integer createUser;
    
    // 任务状态常量
    public static final String STATUS_UPLOADING = "UPLOADING";
    public static final String STATUS_PROCESSING = "PROCESSING";
    public static final String STATUS_RECOGNIZING = "RECOGNIZING";
    public static final String STATUS_PREVIEWING = "PREVIEWING";
    public static final String STATUS_SAVING = "SAVING";
    public static final String STATUS_COMPLETED = "COMPLETED";
    public static final String STATUS_PARTIAL_SUCCESS = "PARTIAL_SUCCESS";
    public static final String STATUS_FAILED = "FAILED";
    public static final String STATUS_CANCELLED = "CANCELLED";
    
    // 导入类型常量
    public static final String TYPE_BANK_RECEIPT = "BANK_RECEIPT";
    public static final String TYPE_INVOICE = "INVOICE";
}
