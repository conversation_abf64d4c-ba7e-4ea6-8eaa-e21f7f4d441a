package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 银行回单实体类 - 银行回单或公司出纳能提供的资金收付证明
 */
@Data
@TableName(value = "fxy_financial_bank_receipts")
public class BankReceipts implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 单据编号
     */
    @TableField(value = "receipts_no")
    private String receiptsNo;

    /**
     * 单据日期
     */
    @TableField(value = "receipts_date")
    private Date receiptsDate;

    /**
     * 类型：收入/支出
     */
    @TableField(value = "type")
    private String type;

    /**
     * 金额
     */
    @TableField(value = "amount")
    private Double amount;

    /**
     * 收款人姓名
     */
    @TableField(value = "payee_name")
    private String payeeName;

    /**
     * 收款人账号
     */
    @TableField(value = "payee_account")
    private String payeeAccount;

    /**
     * 收款人开户行
     */
    @TableField(value = "payee_bank")
    private String payeeBank;

    /**
     * 付款人姓名
     */
    @TableField(value = "payer_name")
    private String payerName;

    /**
     * 付款人账号
     */
    @TableField(value = "payer_account")
    private String payerAccount;

    /**
     * 付款人开户行
     */
    @TableField(value = "payer_bank")
    private String payerBank;

    /**
     * 摘要
     */
    @TableField(value = "summary")
    private String summary;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 附单据数量
     */
    @TableField(value = "receipt_num")
    private Integer receiptNum;

    /**
     * 制单人
     */
    @TableField(value = "create_member")
    private Integer createMember;

    @TableField(value = "create_date")
    private Date createDate;

    @TableField(value = "account_sets_id")
    private Integer accountSetsId;

    @TableField(value = "receipts_year")
    private Integer receiptsYear;

    @TableField(value = "receipts_month")
    private Integer receiptsMonth;

    // 关联凭证ID字段已删除，使用entity_relations表管理关联关系

    /**
     * 银证归并组ID
     */
    @TableField(value = "receipt_group_id")
    private String receiptGroupId;

    /**
     * 银行账号
     */
    @TableField(value = "bank_account")
    private String bankAccount;

    /**
     * 支付方式
     */
    @TableField(value = "payment_method")
    private String paymentMethod;

    /**
     * 文件路径
     */
    @TableField(value = "file_path")
    private String filePath;

    /**
     * 转账日期
     */
    @TableField(value = "transfer_date")
    private Date transferDate;

    /**
     * 提交日期
     */
    @TableField(value = "submission_date")
    private Date submissionDate;

    /**
     * 流水号
     */
    @TableField(value = "serial_number")
    private String serialNumber;

    /**
     * 付款人银行代码
     */
    @TableField(value = "payer_bank_code")
    private String payerBankCode;

    /**
     * 收款人银行代码
     */
    @TableField(value = "payee_bank_code")
    private String payeeBankCode;

    /**
     * 金额大写
     */
    @TableField(value = "amount_in_words")
    private String amountInWords;

    /**
     * 交易机构
     */
    @TableField(value = "transaction_institution")
    private String transactionInstitution;

    /**
     * 回单名称
     */
    @TableField(value = "receipt_title")
    private String receiptTitle;

    /**
     * OCR识别信息
     */
    @TableField(value = "ocr_recognition_info")
    private String ocrRecognitionInfo;

    /**
     * 关联的票据编号列表（非数据库字段，用于前端传递）
     */
    @TableField(exist = false)
    private List<String> relatedBillNos;

    /**
     * 关联的票据列表（非数据库字段，用于查询结果）
     */
    @TableField(exist = false)
    private List<Bill> relatedBills;
}