package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 字段映射模板实体类
 * 用于缓存OCR字段到标准字段的映射规则
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-10
 */
@Data
@TableName("fxy_financial_field_mapping_template")
public class FieldMappingTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 银行标识（如：交通银行、BANK OF COMMUNICATIONS）
     */
    private String bankIdentifier;

    /**
     * 票据类型（BANK_RECEIPT-银行回单, INVOICE-发票）
     */
    private String documentType;

    /**
     * 回单类型（如：支付转账、代发工资、汇总收费扣款）
     */
    private String receiptType;

    /**
     * OCR识别字段数量
     */
    private Integer fieldCount;

    /**
     * 字段特征签名（字段名称的hash值）
     */
    private String fieldSignature;

    /**
     * 字段映射规则（JSON格式）
     */
    private String mappingRules;

    /**
     * 样本OCR数据（用于调试和验证）
     */
    private String sampleOcrData;

    /**
     * 使用次数
     */
    private Integer usageCount;

    /**
     * 成功率（百分比）
     */
    private BigDecimal successRate;

    /**
     * 最后使用时间
     */
    private LocalDateTime lastUsedTime;

    /**
     * 账套ID
     */
    private Integer accountSetsId;

    /**
     * 创建用户ID
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 是否启用（1-启用，0-禁用）
     */
    private Boolean isActive;
}
