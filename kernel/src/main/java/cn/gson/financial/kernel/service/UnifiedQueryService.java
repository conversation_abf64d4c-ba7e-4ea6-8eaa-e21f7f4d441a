package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.dto.UnifiedQueryDto;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Map;

/**
 * 统一查询服务接口
 */
public interface UnifiedQueryService {

    /**
     * 票据统一查询
     *
     * @param accountSetsId 账套ID
     * @param queryDto 查询条件
     * @return 分页查询结果
     */
    Page<Map<String, Object>> queryDocuments(Integer accountSetsId, UnifiedQueryDto queryDto);

    /**
     * 银证统一查询
     *
     * @param accountSetsId 账套ID
     * @param queryDto 查询条件
     * @return 分页查询结果
     */
    Page<Map<String, Object>> queryReceipts(Integer accountSetsId, UnifiedQueryDto queryDto);

    /**
     * 跨类型关联查询
     *
     * @param accountSetsId 账套ID
     * @param queryDto 查询条件
     * @return 分页查询结果
     */
    Page<Map<String, Object>> queryCrossTypeRelations(Integer accountSetsId, UnifiedQueryDto queryDto);

    /**
     * 构建统一查询视图
     *
     * @param accountSetsId 账套ID
     * @param entityTypes 实体类型列表
     * @return 查询SQL
     */
    String buildUnifiedQuery(Integer accountSetsId, java.util.List<String> entityTypes);

    /**
     * 添加关联关系筛选
     *
     * @param baseSql 基础SQL
     * @param includeRelations 是否包含关联信息
     * @param onlyWithRelations 是否只查询有关联的记录
     * @return 修改后的SQL
     */
    String addRelationFilter(String baseSql, Boolean includeRelations, Boolean onlyWithRelations);

    /**
     * 添加归并组筛选
     *
     * @param baseSql 基础SQL
     * @param entityType 实体类型
     * @return 修改后的SQL
     */
    String addMergeGroupFilter(String baseSql, String entityType);

    /**
     * 查询优化
     *
     * @param sql 原始SQL
     * @return 优化后的SQL
     */
    String optimizeQuery(String sql);

    /**
     * 执行分页查询
     *
     * @param sql 查询SQL
     * @param pageParam 分页参数
     * @return 分页结果
     */
    Page<Map<String, Object>> executePaginatedQuery(String sql, UnifiedQueryDto.PageParam pageParam);
}
