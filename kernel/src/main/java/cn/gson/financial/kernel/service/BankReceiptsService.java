package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.vo.UserVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

public interface BankReceiptsService extends IService<BankReceipts> {

    int batchInsert(List<BankReceipts> list);

    String generateReceiptsNo(Integer accountSetsId, Date receiptsDate);

    /**
     * 保存银行回单
     * @param accountSetsId 账套ID
     * @param bankReceipts 银行回单对象
     * @param currentUser 当前用户
     * @return 保存后的银行回单对象
     */
    BankReceipts save(Integer accountSetsId, BankReceipts bankReceipts, UserVo currentUser);

    /**
     * 更新银行回单
     * @param accountSetsId 账套ID
     * @param bankReceipts 银行回单对象
     * @return 更新后的银行回单对象
     */
    BankReceipts update(Integer accountSetsId, BankReceipts bankReceipts);

    /**
     * 删除银行回单
     * @param accountSetsId 账套ID
     * @param id 银行回单ID
     */
    void delete(Integer accountSetsId, Integer id);

    /**
     * 根据编号获取银行回单
     * @param receiptsNo 银行回单编号
     * @param accountSetsId 账套ID
     * @return 银行回单对象
     */
    BankReceipts getByReceiptsNo(String receiptsNo, Integer accountSetsId);

    /**
     * 检查银行回单是否重复
     * @param accountSetsId 账套ID
     * @param serialNumber 流水号
     * @param amount 金额
     * @param transferDate 转账日期
     * @param payerAccount 付款账号
     * @param excludeReceiptId 排除的银行回单ID（用于编辑时排除自身）
     * @return 重复信息，如果不重复返回null
     */
    String checkBankReceiptDuplicate(Integer accountSetsId, String serialNumber, Double amount,
                                   Date transferDate, String payerAccount, Integer excludeReceiptId);
}
