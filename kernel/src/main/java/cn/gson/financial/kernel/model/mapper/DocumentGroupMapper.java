package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.DocumentGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 票据归并组Mapper接口
 */
@Mapper
public interface DocumentGroupMapper extends BaseMapper<DocumentGroup> {

    /**
     * 根据账套ID查询活跃的票据归并组
     */
    @Select("SELECT * FROM fxy_financial_document_groups " +
            "WHERE account_sets_id = #{accountSetsId} " +
            "AND status = 'ACTIVE' " +
            "ORDER BY created_at DESC")
    List<DocumentGroup> findActiveGroupsByAccountSets(@Param("accountSetsId") Integer accountSetsId);

    /**
     * 根据归并规则ID查询票据归并组
     */
    @Select("SELECT * FROM fxy_financial_document_groups " +
            "WHERE merge_rule_id = #{ruleId} " +
            "AND status = 'ACTIVE' " +
            "ORDER BY created_at DESC")
    List<DocumentGroup> findGroupsByRuleId(@Param("ruleId") String ruleId);

    /**
     * 更新归并组统计信息
     */
    @Update("UPDATE fxy_financial_document_groups " +
            "SET total_amount = #{totalAmount}, item_count = #{itemCount}, updated_at = NOW() " +
            "WHERE group_id = #{groupId}")
    int updateGroupStatistics(@Param("groupId") String groupId,
                             @Param("totalAmount") BigDecimal totalAmount,
                             @Param("itemCount") Integer itemCount);

    /**
     * 解散归并组
     */
    @Update("UPDATE fxy_financial_document_groups " +
            "SET status = 'DISSOLVED', updated_at = NOW() " +
            "WHERE group_id = #{groupId}")
    int dissolveGroup(@Param("groupId") String groupId);
}
