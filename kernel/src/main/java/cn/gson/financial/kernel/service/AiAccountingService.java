package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.Subject;
import cn.gson.financial.kernel.model.entity.Voucher;
import cn.gson.financial.kernel.model.vo.AiMatchingResult;
import cn.gson.financial.kernel.model.vo.SubjectMatchingRequest;

import java.util.List;

/**
 * AI智能会计服务接口
 * 基于大模型实现银证和票据的自动科目匹配及凭证生成
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
public interface AiAccountingService {
    
    /**
     * 智能匹配银行回单对应的会计科目
     *
     * @param bankReceipts 银行回单信息
     * @param relatedBills 关联的票据列表
     * @param accountSetsId 账套ID
     * @return 匹配结果，包含推荐的科目和置信度
     */
    AiMatchingResult matchSubjectsForBankReceipts(BankReceipts bankReceipts,
                                                  List<Bill> relatedBills,
                                                  Integer accountSetsId);
    
    /**
     * 智能匹配票据对应的会计科目
     * 
     * @param bill 票据信息
     * @param accountSetsId 账套ID
     * @return 匹配结果，包含推荐的科目和置信度
     */
    AiMatchingResult matchSubjectsForBill(Bill bill, Integer accountSetsId);
    
    /**
     * 基于银行回单和票据自动生成会计凭证
     *
     * @param bankReceipts 银行回单信息
     * @param relatedBills 关联票据列表
     * @param accountSetsId 账套ID
     * @param userId 操作用户ID
     * @return 生成的凭证ID
     */
    Integer generateVoucherAutomatically(BankReceipts bankReceipts,
                                        List<Bill> relatedBills,
                                        Integer accountSetsId,
                                        Integer userId);
    
    /**
     * 批量处理银行回单和票据，自动生成凭证
     *
     * @param bankReceiptsIds 银行回单ID列表
     * @param accountSetsId 账套ID
     * @param userId 操作用户ID
     * @return 成功处理的数量
     */
    Integer batchGenerateVouchers(List<Integer> bankReceiptsIds,
                                 Integer accountSetsId,
                                 Integer userId);
    
    /**
     * 更新科目的AI描述信息，用于提升匹配准确度
     * 
     * @param subjectId 科目ID
     * @param aiDescription AI描述信息
     * @param keywords 关键词列表
     * @return 是否更新成功
     */
    Boolean updateSubjectAiDescription(Integer subjectId, 
                                      String aiDescription, 
                                      List<String> keywords);
    
    /**
     * 获取科目匹配的历史统计信息，用于优化匹配算法
     * 
     * @param accountSetsId 账套ID
     * @param days 统计天数
     * @return 统计结果
     */
    List<Object> getMatchingStatistics(Integer accountSetsId, Integer days);
}