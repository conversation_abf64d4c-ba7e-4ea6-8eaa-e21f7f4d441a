package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.EntityRelation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 统一关联Mapper接口
 */
@Mapper
public interface EntityRelationMapper extends BaseMapper<EntityRelation> {

    /**
     * 根据源实体查询关联关系
     */
    @Select("SELECT * FROM fxy_financial_entity_relations " +
            "WHERE source_type = #{sourceType} AND source_id = #{sourceId} " +
            "AND account_sets_id = #{accountSetsId} " +
            "ORDER BY created_at DESC")
    List<EntityRelation> findRelationsBySource(@Param("sourceType") String sourceType,
                                              @Param("sourceId") String sourceId,
                                              @Param("accountSetsId") Integer accountSetsId);

    /**
     * 根据目标实体查询关联关系
     */
    @Select("SELECT * FROM fxy_financial_entity_relations " +
            "WHERE target_type = #{targetType} AND target_id = #{targetId} " +
            "AND account_sets_id = #{accountSetsId} " +
            "ORDER BY created_at DESC")
    List<EntityRelation> findRelationsByTarget(@Param("targetType") String targetType,
                                              @Param("targetId") String targetId,
                                              @Param("accountSetsId") Integer accountSetsId);

    /**
     * 查询实体的所有关联关系（作为源或目标）
     */
    @Select("SELECT * FROM fxy_financial_entity_relations " +
            "WHERE ((source_type = #{entityType} AND source_id = #{entityId}) " +
            "OR (target_type = #{entityType} AND target_id = #{entityId})) " +
            "AND account_sets_id = #{accountSetsId} " +
            "ORDER BY created_at DESC")
    List<EntityRelation> findAllRelationsByEntity(@Param("entityType") String entityType,
                                                 @Param("entityId") String entityId,
                                                 @Param("accountSetsId") Integer accountSetsId);

    /**
     * 查询跨类型关联关系
     */
    @Select("SELECT * FROM fxy_financial_entity_relations " +
            "WHERE ((source_type LIKE 'DOCUMENT%' AND target_type LIKE 'RECEIPT%') " +
            "OR (source_type LIKE 'RECEIPT%' AND target_type LIKE 'DOCUMENT%')) " +
            "AND account_sets_id = #{accountSetsId} " +
            "ORDER BY created_at DESC")
    List<EntityRelation> findCrossTypeRelations(@Param("accountSetsId") Integer accountSetsId);

    /**
     * 检查关联关系是否存在
     */
    @Select("SELECT COUNT(*) FROM fxy_financial_entity_relations " +
            "WHERE source_type = #{sourceType} AND source_id = #{sourceId} " +
            "AND target_type = #{targetType} AND target_id = #{targetId} " +
            "AND account_sets_id = #{accountSetsId}")
    int checkRelationExists(@Param("sourceType") String sourceType,
                           @Param("sourceId") String sourceId,
                           @Param("targetType") String targetType,
                           @Param("targetId") String targetId,
                           @Param("accountSetsId") Integer accountSetsId);

    /**
     * 删除实体的所有关联关系
     */
    @Delete("DELETE FROM fxy_financial_entity_relations " +
            "WHERE ((source_type = #{entityType} AND source_id = #{entityId}) " +
            "OR (target_type = #{entityType} AND target_id = #{entityId})) " +
            "AND account_sets_id = #{accountSetsId}")
    int deleteAllRelationsByEntity(@Param("entityType") String entityType,
                                  @Param("entityId") String entityId,
                                  @Param("accountSetsId") Integer accountSetsId);

    /**
     * 根据关联ID删除关联关系
     */
    @Delete("DELETE FROM fxy_financial_entity_relations WHERE relation_id = #{relationId}")
    int deleteByRelationId(@Param("relationId") String relationId);

    /**
     * 分页查询账套的关联关系
     */
    @Select("SELECT * FROM fxy_financial_entity_relations " +
            "WHERE account_sets_id = #{accountSetsId} " +
            "ORDER BY created_at DESC " +
            "LIMIT #{size} OFFSET #{offset}")
    List<EntityRelation> findByAccountSetsIdWithPaging(@Param("accountSetsId") Integer accountSetsId,
                                                       @Param("offset") int offset,
                                                       @Param("size") int size);

    /**
     * 统计账套的关联关系总数
     */
    @Select("SELECT COUNT(*) FROM fxy_financial_entity_relations " +
            "WHERE account_sets_id = #{accountSetsId}")
    int countByAccountSetsId(@Param("accountSetsId") Integer accountSetsId);
}
