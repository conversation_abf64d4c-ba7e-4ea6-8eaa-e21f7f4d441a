package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.SubjectAiEnhancement;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 科目AI增强服务接口
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
public interface SubjectAiEnhancementService extends IService<SubjectAiEnhancement> {

    /**
     * 智能匹配科目
     * 根据关键词、金额、业务类型等信息智能推荐最合适的科目
     * 
     * @param accountSetsId 账套ID
     * @param description 业务描述
     * @param amount 金额
     * @param businessType 业务类型
     * @return 推荐的科目列表，按匹配度排序
     */
    List<SubjectAiEnhancement> intelligentMatchSubjects(Integer accountSetsId, 
                                                        String description, 
                                                        BigDecimal amount, 
                                                        String businessType);

    /**
     * 根据关键词搜索科目
     * 
     * @param accountSetsId 账套ID
     * @param keyword 关键词
     * @return 匹配的科目列表
     */
    List<SubjectAiEnhancement> searchSubjectsByKeyword(Integer accountSetsId, String keyword);

    /**
     * 获取使用频率最高的科目
     * 
     * @param accountSetsId 账套ID
     * @param limit 限制数量
     * @return 使用频率最高的科目列表
     */
    List<SubjectAiEnhancement> getTopUsedSubjects(Integer accountSetsId, Integer limit);

    /**
     * 获取推荐科目（基于置信度）
     * 
     * @param accountSetsId 账套ID
     * @param minConfidence 最小置信度
     * @return 推荐科目列表
     */
    List<SubjectAiEnhancement> getRecommendedSubjects(Integer accountSetsId, BigDecimal minConfidence);

    /**
     * 记录科目使用情况，用于学习优化
     * 
     * @param subjectId 科目ID
     * @param accountSetsId 账套ID
     * @param matchingContext 匹配上下文信息
     * @param isCorrect 用户是否确认匹配正确
     */
    void recordSubjectUsage(Integer subjectId, 
                           Integer accountSetsId, 
                           Map<String, Object> matchingContext, 
                           Boolean isCorrect);

    /**
     * 更新科目的AI描述和关键词
     * 
     * @param subjectId 科目ID
     * @param accountSetsId 账套ID
     * @param aiDescription AI描述
     * @param aiKeywords AI关键词
     * @return 是否更新成功
     */
    boolean updateSubjectAiInfo(Integer subjectId, 
                               Integer accountSetsId, 
                               String aiDescription, 
                               String aiKeywords);

    /**
     * 批量初始化科目的AI增强信息
     * 
     * @param accountSetsId 账套ID
     * @return 初始化的科目数量
     */
    int initializeSubjectAiEnhancement(Integer accountSetsId);

    /**
     * 根据历史数据优化匹配规则
     * 
     * @param accountSetsId 账套ID
     * @return 优化的科目数量
     */
    int optimizeMatchingRules(Integer accountSetsId);

    /**
     * 获取科目的AI增强信息
     * 
     * @param subjectId 科目ID
     * @param accountSetsId 账套ID
     * @return AI增强信息
     */
    SubjectAiEnhancement getSubjectAiEnhancement(Integer subjectId, Integer accountSetsId);

    /**
     * 创建或更新科目AI增强信息
     * 
     * @param enhancement AI增强信息
     * @return 是否成功
     */
    boolean saveOrUpdateEnhancement(SubjectAiEnhancement enhancement);

    /**
     * 启用或禁用科目AI增强
     * 
     * @param id 主键ID
     * @param status 状态（1-启用，0-禁用）
     * @return 是否成功
     */
    boolean updateStatus(Integer id, Integer status);

    /**
     * 获取账套下所有启用的AI增强科目
     * 
     * @param accountSetsId 账套ID
     * @return AI增强科目列表
     */
    List<SubjectAiEnhancement> getAllEnabledSubjects(Integer accountSetsId);

    /**
     * 分析科目使用统计
     * 
     * @param accountSetsId 账套ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    Map<String, Object> analyzeSubjectUsageStatistics(Integer accountSetsId, 
                                                      String startDate, 
                                                      String endDate);

    /**
     * 导出科目AI配置
     * 
     * @param accountSetsId 账套ID
     * @return 配置数据
     */
    List<Map<String, Object>> exportAiConfiguration(Integer accountSetsId);

    /**
     * 导入科目AI配置
     * 
     * @param accountSetsId 账套ID
     * @param configData 配置数据
     * @return 导入成功的数量
     */
    int importAiConfiguration(Integer accountSetsId, List<Map<String, Object>> configData);

    /**
     * 智能学习用户的科目选择习惯
     * 基于用户的历史选择，自动调整匹配规则和置信度
     * 
     * @param accountSetsId 账套ID
     * @param userId 用户ID
     * @return 学习到的模式数量
     */
    int learnUserPreferences(Integer accountSetsId, Integer userId);

    /**
     * 获取科目匹配建议
     * 基于当前输入内容，实时提供科目匹配建议
     * 
     * @param accountSetsId 账套ID
     * @param inputText 输入文本
     * @param contextInfo 上下文信息
     * @return 匹配建议列表
     */
    List<Map<String, Object>> getMatchingSuggestions(Integer accountSetsId, 
                                                     String inputText, 
                                                     Map<String, Object> contextInfo);
}