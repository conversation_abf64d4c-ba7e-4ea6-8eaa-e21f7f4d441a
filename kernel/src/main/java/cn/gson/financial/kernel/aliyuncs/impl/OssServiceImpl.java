package cn.gson.financial.kernel.aliyuncs.impl;

import cn.gson.financial.kernel.aliyuncs.OssService;
import cn.gson.financial.kernel.exception.ServiceException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

@Service
@Slf4j
public class OssServiceImpl implements OssService {
    
    @Value("${aliyun.oss.endpoint:oss-cn-beijing.aliyuncs.com}")
    private String endpoint;
    
    @Value("${aliyun.oss.bucketName:aiform-f}")
    private String bucketName;
    
    @Value("${aliyun.accessKeyId}")
    private String accessKeyId;
    
    @Value("${aliyun.accessKeySecret}")
    private String accessKeySecret;
    
    @Override
    public String uploadFile(MultipartFile file, String folder) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }
        
        // 创建OSS客户端
        OSS ossClient = new OSSClientBuilder().build("https://" + endpoint, accessKeyId, accessKeySecret);
        
        try {
            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                throw new ServiceException("文件名不能为空");
            }

            String extension = "";
            int lastDotIndex = originalFilename.lastIndexOf(".");
            if (lastDotIndex > 0) {
                extension = originalFilename.substring(lastDotIndex);
            }
            String fileName = UUID.randomUUID().toString().replace("-", "") + extension;
            
            // 生成文件路径：folder/yyyy/MM/dd/filename
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            String datePath = sdf.format(new Date());
            String objectKey = folder + "/" + datePath + "/" + fileName;
            
            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectKey, file.getInputStream());
            ossClient.putObject(putObjectRequest);
            
            // 返回文件访问URL
            String fileUrl = "https://" + bucketName + "." + endpoint + "/" + objectKey;
            log.info("文件上传成功，URL: {}", fileUrl);
            return fileUrl;
            
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new ServiceException("文件上传失败: " + e.getMessage());
        } finally {
            // 关闭OSS客户端
            ossClient.shutdown();
        }
    }
    
    @Override
    public boolean deleteFile(String fileUrl) {
        if (fileUrl == null || fileUrl.isEmpty()) {
            return false;
        }
        
        OSS ossClient = new OSSClientBuilder().build("https://" + endpoint, accessKeyId, accessKeySecret);
        
        try {
            // 从URL中提取objectKey
            String objectKey = fileUrl.substring(fileUrl.indexOf(endpoint) + endpoint.length() + 1);
            ossClient.deleteObject(bucketName, objectKey);
            log.info("文件删除成功: {}", fileUrl);
            return true;
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return false;
        } finally {
            ossClient.shutdown();
        }
    }

    @Override
    public String uploadFile(java.io.File file, String folder) {
        if (file == null || !file.exists()) {
            throw new ServiceException("上传文件不存在");
        }

        // 创建OSS客户端
        OSS ossClient = new OSSClientBuilder().build("https://" + endpoint, accessKeyId, accessKeySecret);

        try {
            // 生成文件名
            String originalFilename = file.getName();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = UUID.randomUUID().toString().replace("-", "") + extension;

            // 生成文件路径：folder/yyyy/MM/dd/filename
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            String datePath = sdf.format(new Date());
            String objectName = folder + "/" + datePath + "/" + fileName;

            // 上传文件
            java.io.FileInputStream fis = new java.io.FileInputStream(file);
            ossClient.putObject(bucketName, objectName, fis);
            fis.close();

            // 返回文件访问URL
            String fileUrl = "https://" + bucketName + "." + endpoint + "/" + objectName;
            log.info("文件上传成功: {}", fileUrl);
            return fileUrl;

        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException("文件上传失败: " + e.getMessage());
        } finally {
            // 关闭OSS客户端
            ossClient.shutdown();
        }
    }
}