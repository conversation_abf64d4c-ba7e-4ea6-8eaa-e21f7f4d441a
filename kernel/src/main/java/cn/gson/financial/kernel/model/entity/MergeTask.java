package cn.gson.financial.kernel.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 归并任务实体类
 */
@Data
@TableName(value = "fxy_financial_merge_tasks")
public class MergeTask implements Serializable {
    
    /**
     * 任务ID
     */
    @TableId(value = "task_id", type = IdType.ID_WORKER_STR)
    private String taskId;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 任务类型：DOCUMENT_MERGE-票据归并，RECEIPT_MERGE-银证归并，RELATION_CREATE-关联创建
     */
    @TableField(value = "task_type")
    private String taskType;

    /**
     * 使用的规则ID
     */
    @TableField(value = "rule_id")
    private String ruleId;

    /**
     * 任务状态：PENDING-待处理，RUNNING-运行中，COMPLETED-已完成，FAILED-失败
     */
    @TableField(value = "status")
    private String status;

    /**
     * 进度百分比
     */
    @TableField(value = "progress")
    private Integer progress;

    /**
     * 总项目数
     */
    @TableField(value = "total_items")
    private Integer totalItems;

    /**
     * 已处理项目数
     */
    @TableField(value = "processed_items")
    private Integer processedItems;

    /**
     * 结果摘要（JSON格式）
     */
    @TableField(value = "result_summary")
    private String resultSummary;

    /**
     * 错误信息
     */
    @TableField(value = "error_message")
    private String errorMessage;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     * 开始时间
     */
    @TableField(value = "started_at")
    private Date startedAt;

    /**
     * 完成时间
     */
    @TableField(value = "completed_at")
    private Date completedAt;

    /**
     * 创建人ID
     */
    @TableField(value = "created_by")
    private Integer createdBy;

    /**
     * 账套ID
     */
    @TableField(value = "account_sets_id")
    private Integer accountSetsId;

    private static final long serialVersionUID = 1L;

    /**
     * 任务类型枚举
     */
    public enum TaskType {
        DOCUMENT_MERGE("DOCUMENT_MERGE", "票据归并"),
        RECEIPT_MERGE("RECEIPT_MERGE", "银证归并"),
        RELATION_CREATE("RELATION_CREATE", "关联创建");

        private final String code;
        private final String description;

        TaskType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 任务状态枚举
     */
    public enum Status {
        PENDING("PENDING", "待处理"),
        RUNNING("RUNNING", "运行中"),
        COMPLETED("COMPLETED", "已完成"),
        FAILED("FAILED", "失败");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
