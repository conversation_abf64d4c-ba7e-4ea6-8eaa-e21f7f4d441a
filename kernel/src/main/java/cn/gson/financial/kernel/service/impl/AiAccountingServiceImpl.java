package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.entity.*;
import cn.gson.financial.kernel.model.mapper.*;
import cn.gson.financial.kernel.model.vo.AiMatchingResult;
import cn.gson.financial.kernel.model.vo.SubjectMatchingRequest;
import cn.gson.financial.kernel.service.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AI智能会计服务实现类
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
@Slf4j
@Service
@AllArgsConstructor
public class AiAccountingServiceImpl implements AiAccountingService {
    
    private final SubjectService subjectService;
    private final VoucherService voucherService;
    private final BankReceiptsService bankReceiptsService;
    private final BillService billService;
    private final SubjectMapper subjectMapper;
    private final VoucherMapper voucherMapper;
    private final VoucherDetailsMapper voucherDetailsMapper;
    
    @Override
    public AiMatchingResult matchSubjectsForBankReceipts(BankReceipts bankReceipts,
                                                        List<Bill> relatedBills,
                                                        Integer accountSetsId) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 构建匹配请求
            SubjectMatchingRequest request = buildMatchingRequest(bankReceipts, relatedBills, accountSetsId);

            // 执行AI匹配
            AiMatchingResult result = performAiMatching(request);

            // 记录匹配历史
            recordMatchingHistory("bank_receipts", bankReceipts.getId(), result, accountSetsId, null);
            
            result.setProcessingTime(System.currentTimeMillis() - startTime);
            return result;
            
        } catch (Exception e) {
            log.error("银证科目匹配失败: {}", e.getMessage(), e);
            return createErrorResult("匹配失败: " + e.getMessage(), System.currentTimeMillis() - startTime);
        }
    }
    
    @Override
    public AiMatchingResult matchSubjectsForBill(Bill bill, Integer accountSetsId) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 构建匹配请求
            SubjectMatchingRequest request = buildMatchingRequestForBill(bill, accountSetsId);
            
            // 执行AI匹配
            AiMatchingResult result = performAiMatching(request);
            
            // 记录匹配历史
            recordMatchingHistory("bill", bill.getId(), result, accountSetsId, null);
            
            result.setProcessingTime(System.currentTimeMillis() - startTime);
            return result;
            
        } catch (Exception e) {
            log.error("票据科目匹配失败: {}", e.getMessage(), e);
            return createErrorResult("匹配失败: " + e.getMessage(), System.currentTimeMillis() - startTime);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer generateVoucherAutomatically(BankReceipts bankReceipts,
                                              List<Bill> relatedBills,
                                              Integer accountSetsId,
                                              Integer userId) {
        try {
            // 获取AI匹配结果
            AiMatchingResult matchingResult = matchSubjectsForBankReceipts(bankReceipts, relatedBills, accountSetsId);

            if (!matchingResult.getSuccess() || matchingResult.getConfidence() < 0.7) {
                throw new RuntimeException("AI匹配置信度不足，无法自动生成凭证");
            }

            // 创建凭证主表
            Voucher voucher = createVoucherFromMatching(bankReceipts, matchingResult, accountSetsId, userId);
            voucherService.save(voucher);
            
            // 创建凭证明细
            createVoucherDetails(voucher.getId(), matchingResult, accountSetsId);
            
            // 注意：关联凭证ID字段已删除，如需要可以通过entity_relations表管理凭证关联关系
            // 这里暂时移除对related_voucher_id字段的更新
            log.info("凭证生成成功，凭证ID: {}", voucher.getId());
            
            // 更新科目使用频率
            updateSubjectUsageFrequency(matchingResult);
            
            log.info("自动生成凭证成功，凭证ID: {}, 银行回单ID: {}", voucher.getId(), bankReceipts.getId());
            return voucher.getId();
            
        } catch (Exception e) {
            log.error("自动生成凭证失败: {}", e.getMessage(), e);
            throw new RuntimeException("自动生成凭证失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchGenerateVouchers(List<Integer> bankReceiptsIds,
                                        Integer accountSetsId,
                                        Integer userId) {
        int successCount = 0;
        
        for (Integer bankReceiptsId : bankReceiptsIds) {
            try {
                BankReceipts bankReceipts = bankReceiptsService.getById(bankReceiptsId);
                if (bankReceipts == null) {
                    continue; // 跳过不存在的银行回单
                }
                // 注意：related_voucher_id字段已删除，如需要可以通过entity_relations表检查关联状态
                
                // 获取关联票据
                List<Bill> relatedBills = getRelatedBills(bankReceipts.getReceiptsNo());

                // 生成凭证
                generateVoucherAutomatically(bankReceipts, relatedBills, accountSetsId, userId);
                successCount++;

            } catch (Exception e) {
                log.error("批量生成凭证失败，银行回单ID: {}, 错误: {}", bankReceiptsId, e.getMessage());
            }
        }
        
        return successCount;
    }
    
    @Override
    public Boolean updateSubjectAiDescription(Integer subjectId, 
                                            String aiDescription, 
                                            List<String> keywords) {
        try {
            Subject subject = subjectService.getById(subjectId);
            if (subject == null) {
                return false;
            }
            
            // 更新AI描述和关键词
            QueryWrapper<Subject> updateWrapper = new QueryWrapper<>();
            updateWrapper.eq("id", subjectId);
            
            Subject updateSubject = new Subject();
            updateSubject.setId(subjectId);
            // 注意：这里需要根据实际的Subject实体类字段进行调整
            // updateSubject.setAiDescription(aiDescription);
            // updateSubject.setAiKeywords(String.join(",", keywords));
            
            return subjectService.updateById(updateSubject);
            
        } catch (Exception e) {
            log.error("更新科目AI描述失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public List<Object> getMatchingStatistics(Integer accountSetsId, Integer days) {
        // 这里需要实现统计查询逻辑
        // 可以查询匹配历史表，统计成功率、常用科目等信息
        return new ArrayList<>();
    }
    
    /**
     * 构建银行回单匹配请求
     */
    private SubjectMatchingRequest buildMatchingRequest(BankReceipts bankReceipts,
                                                       List<Bill> relatedBills,
                                                       Integer accountSetsId) {
        SubjectMatchingRequest request = new SubjectMatchingRequest();
        request.setAccountSetsId(accountSetsId);
        request.setBusinessType("bank_receipts");
        request.setTransactionType(bankReceipts.getType());
        request.setAmount(bankReceipts.getAmount());
        // 根据类型设置交易对手
        String counterparty = null;
        if ("收入".equals(bankReceipts.getType())) {
            counterparty = bankReceipts.getPayerName() != null ? bankReceipts.getPayerName() : bankReceipts.getPayerAccount();
        } else {
            counterparty = bankReceipts.getPayeeName() != null ? bankReceipts.getPayeeName() : bankReceipts.getPayeeAccount();
        }
        request.setCounterparty(counterparty);
        request.setSummary(bankReceipts.getSummary());
        request.setRemark(bankReceipts.getRemark());
        request.setBusinessDate(bankReceipts.getReceiptsDate());
        request.setBankAccount(bankReceipts.getBankAccount());
        request.setPaymentMethod(bankReceipts.getPaymentMethod());
        
        // 添加关联票据信息
        if (relatedBills != null && !relatedBills.isEmpty()) {
            List<SubjectMatchingRequest.BillInfo> billInfos = relatedBills.stream()
                .map(this::convertToBillInfo)
                .collect(Collectors.toList());
            request.setRelatedBills(billInfos);
        }
        
        return request;
    }
    
    /**
     * 构建票据匹配请求
     */
    private SubjectMatchingRequest buildMatchingRequestForBill(Bill bill, Integer accountSetsId) {
        SubjectMatchingRequest request = new SubjectMatchingRequest();
        request.setAccountSetsId(accountSetsId);
        request.setBusinessType("bill");
        request.setAmount(bill.getAmount());
        request.setSummary(bill.getSummary());
        request.setRemark(bill.getRemark());
        request.setBusinessDate(bill.getBillDate());
        request.setBillType(bill.getType());
        request.setIssuer(bill.getIssuer());
        request.setRecipient(bill.getRecipient());
        
        // 根据票据类型推断交易类型
        String transactionType = inferTransactionType(bill.getType());
        request.setTransactionType(transactionType);
        
        return request;
    }
    
    /**
     * 根据票据类型推断交易类型
     */
    private String inferTransactionType(String billType) {
        if (billType == null) {
            return "其他";
        }
        
        // 收入相关的票据类型
        if (billType.contains("销售") || billType.contains("收入") || 
            billType.contains("发票") || billType.contains("收款")) {
            return "收入";
        }
        
        // 支出相关的票据类型
        if (billType.contains("采购") || billType.contains("支出") || 
            billType.contains("费用") || billType.contains("付款")) {
            return "支出";
        }
        
        // 默认根据票据内容进一步判断
        return "收入"; // 默认为收入类型
    }
    
    /**
     * 执行AI匹配（核心算法）
     */
    private AiMatchingResult performAiMatching(SubjectMatchingRequest request) {
        AiMatchingResult result = new AiMatchingResult();
        
        try {
            // 1. 获取账套的所有可用科目
            List<Subject> availableSubjects = getAvailableSubjects(request.getAccountSetsId());
            
            // 2. 基于规则的初步匹配
            List<Subject> candidateSubjects = performRuleBasedMatching(request, availableSubjects);
            
            // 3. 基于历史数据的匹配优化
            candidateSubjects = optimizeWithHistoricalData(request, candidateSubjects);
            
            // 4. 生成匹配结果
            result = generateMatchingResult(request, candidateSubjects);
            
            result.setSuccess(true);
            
        } catch (Exception e) {
            log.error("AI匹配执行失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取可用科目列表
     */
    private List<Subject> getAvailableSubjects(Integer accountSetsId) {
        QueryWrapper<Subject> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_sets_id", accountSetsId)
                   .eq("status", true)
                   .orderByAsc("code");
        return subjectService.list(queryWrapper);
    }
    
    /**
     * 基于规则的匹配
     */
    private List<Subject> performRuleBasedMatching(SubjectMatchingRequest request, 
                                                  List<Subject> availableSubjects) {
        List<Subject> candidates = new ArrayList<>();
        
        // 根据交易类型和关键词进行匹配
        String keywords = buildKeywords(request);
        
        for (Subject subject : availableSubjects) {
            if (isSubjectMatch(subject, request, keywords)) {
                candidates.add(subject);
            }
        }
        
        return candidates;
    }
    
    /**
     * 构建关键词
     */
    private String buildKeywords(SubjectMatchingRequest request) {
        StringBuilder keywords = new StringBuilder();
        
        if (StringUtils.hasText(request.getSummary())) {
            keywords.append(request.getSummary()).append(" ");
        }
        if (StringUtils.hasText(request.getCounterparty())) {
            keywords.append(request.getCounterparty()).append(" ");
        }
        if (StringUtils.hasText(request.getPaymentMethod())) {
            keywords.append(request.getPaymentMethod()).append(" ");
        }
        if (StringUtils.hasText(request.getBillType())) {
            keywords.append(request.getBillType()).append(" ");
        }
        
        return keywords.toString().toLowerCase();
    }
    
    /**
     * 判断科目是否匹配
     */
    private boolean isSubjectMatch(Subject subject, SubjectMatchingRequest request, String keywords) {
        // 这里实现具体的匹配逻辑
        // 可以基于科目名称、AI关键词、交易类型等进行匹配
        
        // 简单的关键词匹配示例
        String subjectName = subject.getName().toLowerCase();
        
        // 银行相关交易
        if ("bank_receipts".equals(request.getBusinessType())) {
            if (keywords.contains("银行") || keywords.contains("转账")) {
                return subjectName.contains("银行存款") || subjectName.contains("其他货币资金");
            }
        }
        
        // 现金相关交易
        if (keywords.contains("现金")) {
            return subjectName.contains("库存现金");
        }
        
        // 收入相关
        if ("收入".equals(request.getTransactionType())) {
            return subject.getType().toString().equals("损益") && 
                   "贷".equals(subject.getBalanceDirection().toString());
        }
        
        // 支出相关
        if ("支出".equals(request.getTransactionType())) {
            return subject.getType().toString().equals("损益") && 
                   "借".equals(subject.getBalanceDirection().toString());
        }
        
        return false;
    }
    
    /**
     * 基于历史数据优化匹配
     */
    private List<Subject> optimizeWithHistoricalData(SubjectMatchingRequest request, 
                                                    List<Subject> candidates) {
        // 这里可以实现基于历史匹配数据的优化逻辑
        // 例如：提升经常使用的科目的优先级
        return candidates.stream()
                .sorted((s1, s2) -> {
                    // 按使用频率排序（需要先添加相应字段）
                    // return Integer.compare(s2.getUsageFrequency(), s1.getUsageFrequency());
                    return s1.getCode().compareTo(s2.getCode());
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 生成匹配结果
     */
    private AiMatchingResult generateMatchingResult(SubjectMatchingRequest request, 
                                                   List<Subject> candidates) {
        AiMatchingResult result = new AiMatchingResult();
        
        if (candidates.isEmpty()) {
            result.setSuccess(false);
            result.setErrorMessage("未找到匹配的科目");
            result.setConfidence(0.0);
            return result;
        }
        
        // 生成借贷方科目推荐
        List<AiMatchingResult.SubjectRecommendation> debitSubjects = new ArrayList<>();
        List<AiMatchingResult.SubjectRecommendation> creditSubjects = new ArrayList<>();
        
        // 根据交易类型确定借贷方向
        if ("收入".equals(request.getTransactionType())) {
            // 收入：借记银行存款/库存现金，贷记收入科目
            addBankOrCashSubject(debitSubjects, candidates, request.getAmount(), "收款到账");
            addIncomeSubjects(creditSubjects, candidates, request.getAmount(), "业务收入");
        } else if ("支出".equals(request.getTransactionType())) {
            // 支出：借记费用/成本科目，贷记银行存款/库存现金
            addExpenseSubjects(debitSubjects, candidates, request.getAmount(), "业务支出");
            addBankOrCashSubject(creditSubjects, candidates, request.getAmount(), "付款支出");
        }
        
        result.setDebitSubjects(debitSubjects);
        result.setCreditSubjects(creditSubjects);
        result.setConfidence(calculateConfidence(debitSubjects, creditSubjects));
        result.setSuggestedSummary(generateSuggestedSummary(request));
        result.setReason("基于业务类型和关键词匹配");
        
        return result;
    }
    
    /**
     * 添加银行或现金科目
     */
    private void addBankOrCashSubject(List<AiMatchingResult.SubjectRecommendation> subjects, 
                                     List<Subject> candidates, 
                                     Double amount, 
                                     String reason) {
        Subject bankSubject = candidates.stream()
                .filter(s -> s.getName().contains("银行存款"))
                .findFirst().orElse(null);
        
        if (bankSubject != null) {
            AiMatchingResult.SubjectRecommendation recommendation = new AiMatchingResult.SubjectRecommendation();
            recommendation.setSubject(bankSubject);
            recommendation.setAmount(amount);
            recommendation.setConfidence(0.9);
            recommendation.setReason(reason);
            recommendation.setIsPrimary(true);
            subjects.add(recommendation);
        }
    }
    
    /**
     * 添加收入科目
     */
    private void addIncomeSubjects(List<AiMatchingResult.SubjectRecommendation> subjects, 
                                  List<Subject> candidates, 
                                  Double amount, 
                                  String reason) {
        Subject incomeSubject = candidates.stream()
                .filter(s -> s.getName().contains("主营业务收入") || s.getName().contains("其他业务收入"))
                .findFirst().orElse(null);
        
        if (incomeSubject != null) {
            AiMatchingResult.SubjectRecommendation recommendation = new AiMatchingResult.SubjectRecommendation();
            recommendation.setSubject(incomeSubject);
            recommendation.setAmount(amount);
            recommendation.setConfidence(0.8);
            recommendation.setReason(reason);
            recommendation.setIsPrimary(true);
            subjects.add(recommendation);
        }
    }
    
    /**
     * 添加费用科目
     */
    private void addExpenseSubjects(List<AiMatchingResult.SubjectRecommendation> subjects, 
                                   List<Subject> candidates, 
                                   Double amount, 
                                   String reason) {
        Subject expenseSubject = candidates.stream()
                .filter(s -> s.getName().contains("管理费用") || s.getName().contains("销售费用"))
                .findFirst().orElse(null);
        
        if (expenseSubject != null) {
            AiMatchingResult.SubjectRecommendation recommendation = new AiMatchingResult.SubjectRecommendation();
            recommendation.setSubject(expenseSubject);
            recommendation.setAmount(amount);
            recommendation.setConfidence(0.7);
            recommendation.setReason(reason);
            recommendation.setIsPrimary(true);
            subjects.add(recommendation);
        }
    }
    
    /**
     * 计算整体置信度
     */
    private Double calculateConfidence(List<AiMatchingResult.SubjectRecommendation> debitSubjects, 
                                     List<AiMatchingResult.SubjectRecommendation> creditSubjects) {
        if (debitSubjects.isEmpty() || creditSubjects.isEmpty()) {
            return 0.0;
        }
        
        double avgDebitConfidence = debitSubjects.stream()
                .mapToDouble(AiMatchingResult.SubjectRecommendation::getConfidence)
                .average().orElse(0.0);
        
        double avgCreditConfidence = creditSubjects.stream()
                .mapToDouble(AiMatchingResult.SubjectRecommendation::getConfidence)
                .average().orElse(0.0);
        
        return (avgDebitConfidence + avgCreditConfidence) / 2;
    }
    
    /**
     * 生成建议摘要
     */
    private String generateSuggestedSummary(SubjectMatchingRequest request) {
        if (StringUtils.hasText(request.getSummary())) {
            return request.getSummary();
        }
        
        StringBuilder summary = new StringBuilder();
        if (StringUtils.hasText(request.getCounterparty())) {
            summary.append(request.getCounterparty());
        }
        
        if ("收入".equals(request.getTransactionType())) {
            summary.append("收款");
        } else if ("支出".equals(request.getTransactionType())) {
            summary.append("付款");
        }
        
        return summary.toString();
    }
    
    // 其他辅助方法...
    
    private SubjectMatchingRequest.BillInfo convertToBillInfo(Bill bill) {
        SubjectMatchingRequest.BillInfo billInfo = new SubjectMatchingRequest.BillInfo();
        billInfo.setBillNo(bill.getBillNo());
        billInfo.setType(bill.getType());
        billInfo.setAmount(bill.getAmount());
        billInfo.setIssuer(bill.getIssuer());
        billInfo.setRecipient(bill.getRecipient());
        billInfo.setSummary(bill.getSummary());
        billInfo.setBillDate(bill.getBillDate());
        return billInfo;
    }
    
    private List<Bill> getRelatedBills(String receiptsNo) {
        // 注意：由于删除了bill_bank_receipts_relation表，这里暂时返回空列表
        // 如需要可以通过entity_relations表查询关联关系
        log.warn("getRelatedBills方法需要重新实现以使用entity_relations表");
        return new ArrayList<>();
    }
    
    private AiMatchingResult createErrorResult(String errorMessage, long processingTime) {
        AiMatchingResult result = new AiMatchingResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setConfidence(0.0);
        result.setProcessingTime(processingTime);
        return result;
    }
    
    private void recordMatchingHistory(String businessType, Integer businessId, 
                                     AiMatchingResult result, Integer accountSetsId, Integer userId) {
        // 这里实现匹配历史记录逻辑
        log.info("记录匹配历史: 业务类型={}, 业务ID={}, 置信度={}", 
                businessType, businessId, result.getConfidence());
    }
    
    private Voucher createVoucherFromMatching(BankReceipts bankReceipts,
                                            AiMatchingResult matchingResult,
                                            Integer accountSetsId,
                                            Integer userId) {
        Voucher voucher = new Voucher();
        voucher.setWord("记");
        voucher.setAccountSetsId(accountSetsId);
        voucher.setVoucherDate(bankReceipts.getReceiptsDate());
        voucher.setVoucherYear(bankReceipts.getReceiptsYear());
        voucher.setVoucherMonth(bankReceipts.getReceiptsMonth());
        voucher.setRemark(matchingResult.getSuggestedSummary());
        voucher.setCreateMember(userId);
        voucher.setCreateDate(new Date());
        voucher.setReceiptNum(bankReceipts.getReceiptNum());
        
        // 计算总金额
        double totalAmount = matchingResult.getDebitSubjects().stream()
                .mapToDouble(AiMatchingResult.SubjectRecommendation::getAmount)
                .sum();
        voucher.setDebitAmount(totalAmount);
        voucher.setCreditAmount(totalAmount);
        
        return voucher;
    }
    
    private void createVoucherDetails(Integer voucherId, 
                                    AiMatchingResult matchingResult, 
                                    Integer accountSetsId) {
        List<VoucherDetails> detailsList = new ArrayList<>();
        
        // 创建借方明细
        for (AiMatchingResult.SubjectRecommendation debitRec : matchingResult.getDebitSubjects()) {
            VoucherDetails details = new VoucherDetails();
            details.setVoucherId(voucherId);
            details.setAccountSetsId(accountSetsId);
            details.setSubjectId(debitRec.getSubject().getId());
            details.setSubjectCode(debitRec.getSubject().getCode());
            details.setSubjectName(debitRec.getSubject().getName());
            details.setSummary(debitRec.getReason());
            details.setDebitAmount(debitRec.getAmount());
            details.setCreditAmount(0.0);
            detailsList.add(details);
        }
        
        // 创建贷方明细
        for (AiMatchingResult.SubjectRecommendation creditRec : matchingResult.getCreditSubjects()) {
            VoucherDetails details = new VoucherDetails();
            details.setVoucherId(voucherId);
            details.setAccountSetsId(accountSetsId);
            details.setSubjectId(creditRec.getSubject().getId());
            details.setSubjectCode(creditRec.getSubject().getCode());
            details.setSubjectName(creditRec.getSubject().getName());
            details.setSummary(creditRec.getReason());
            details.setDebitAmount(0.0);
            details.setCreditAmount(creditRec.getAmount());
            detailsList.add(details);
        }
        
        // 批量保存明细
        for (VoucherDetails details : detailsList) {
            voucherDetailsMapper.insert(details);
        }
    }
    
    private void updateSubjectUsageFrequency(AiMatchingResult matchingResult) {
        // 更新科目使用频率统计
        List<Subject> allSubjects = new ArrayList<>();
        matchingResult.getDebitSubjects().forEach(rec -> allSubjects.add(rec.getSubject()));
        matchingResult.getCreditSubjects().forEach(rec -> allSubjects.add(rec.getSubject()));
        
        for (Subject subject : allSubjects) {
            // 这里需要实现更新使用频率的逻辑
            log.debug("更新科目使用频率: {}", subject.getName());
        }
    }
}