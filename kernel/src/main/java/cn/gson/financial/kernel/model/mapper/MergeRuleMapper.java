package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.MergeRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 归并规则Mapper接口
 */
@Mapper
public interface MergeRuleMapper extends BaseMapper<MergeRule> {

    /**
     * 根据账套ID和适用实体查询活跃的归并规则
     */
    @Select("SELECT * FROM fxy_financial_merge_rules " +
            "WHERE account_sets_id = #{accountSetsId} " +
            "AND (applicable_entity = #{applicableEntity} OR applicable_entity = 'BOTH') " +
            "AND is_active = 1 " +
            "ORDER BY created_at DESC")
    List<MergeRule> findActiveRulesByAccountSetsAndEntity(@Param("accountSetsId") Integer accountSetsId,
                                                          @Param("applicableEntity") String applicableEntity);

    /**
     * 根据账套ID查询所有活跃的归并规则
     */
    @Select("SELECT * FROM fxy_financial_merge_rules " +
            "WHERE account_sets_id = #{accountSetsId} " +
            "AND is_active = 1 " +
            "ORDER BY created_at DESC")
    List<MergeRule> findActiveRulesByAccountSets(@Param("accountSetsId") Integer accountSetsId);
}
