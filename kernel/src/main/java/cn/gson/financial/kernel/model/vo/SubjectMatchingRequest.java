package cn.gson.financial.kernel.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 科目匹配请求VO
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
@Data
public class SubjectMatchingRequest implements Serializable {
    
    /**
     * 账套ID
     */
    private Integer accountSetsId;
    
    /**
     * 业务类型（银证/票据）
     */
    private String businessType;
    
    /**
     * 交易类型（收入/支出）
     */
    private String transactionType;
    
    /**
     * 金额
     */
    private Double amount;
    
    /**
     * 交易对手方
     */
    private String counterparty;
    
    /**
     * 摘要信息
     */
    private String summary;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 业务日期
     */
    private Date businessDate;
    
    /**
     * 银行账号
     */
    private String bankAccount;
    
    /**
     * 支付方式
     */
    private String paymentMethod;
    
    /**
     * 票据类型（如果是票据）
     */
    private String billType;
    
    /**
     * 开票方（如果是票据）
     */
    private String issuer;
    
    /**
     * 收票方（如果是票据）
     */
    private String recipient;
    
    /**
     * 关联票据信息
     */
    private List<BillInfo> relatedBills;
    
    /**
     * 历史匹配偏好
     */
    private Map<String, Object> historicalPreferences;
    
    /**
     * 是否需要详细分析
     */
    private Boolean needDetailedAnalysis = false;
    
    /**
     * 票据信息内部类
     */
    @Data
    public static class BillInfo implements Serializable {
        private String billNo;
        private String type;
        private Double amount;
        private String issuer;
        private String recipient;
        private String summary;
        private Date billDate;
    }
}