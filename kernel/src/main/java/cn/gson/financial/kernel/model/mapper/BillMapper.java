package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.Bill;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
// import org.apache.ibatis.annotations.Select; // 不再需要

import java.util.List;

@Mapper
public interface BillMapper extends BaseMapper<Bill> {
    int batchInsert(@Param("list") List<Bill> list);

    // 在现有BillMapper接口中添加以下方法
    
    // 关联功能已移至关联管理模块，此方法已删除
    
    // 关联功能已移至关联管理模块，此方法已删除
}