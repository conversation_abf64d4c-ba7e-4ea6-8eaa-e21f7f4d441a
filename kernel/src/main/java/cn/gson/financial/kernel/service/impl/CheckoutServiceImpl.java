package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.common.DateUtil;
import cn.gson.financial.kernel.common.DoubleComparer;
import cn.gson.financial.kernel.common.DoubleValueUtil;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.entity.Checkout;
import cn.gson.financial.kernel.model.entity.VoucherDetails;
import cn.gson.financial.kernel.model.mapper.AccountSetsMapper;
import cn.gson.financial.kernel.model.mapper.CheckoutMapper;
import cn.gson.financial.kernel.model.mapper.VoucherDetailsMapper;
import cn.gson.financial.kernel.model.mapper.VoucherMapper;
import cn.gson.financial.kernel.model.vo.UserVo;
import cn.gson.financial.kernel.service.CheckoutService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月23日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Slf4j
@Service
@AllArgsConstructor
public class CheckoutServiceImpl extends ServiceImpl<CheckoutMapper, Checkout> implements CheckoutService {

    private VoucherDetailsMapper voucherDetailsMapper;
    private VoucherMapper voucherMapper;
    private AccountSetsMapper accountSetsMapper;

    @Override
    public int batchInsert(List<Checkout> list) {
        return baseMapper.batchInsert(list);
    }

    /**
     * 期初检查
     *
     * @param accountSetsId
     * @return
     */
    @Override
    public boolean initialCheck(Integer accountSetsId) {
        try {
            log.info("开始期初平衡检查，账套ID: {}", accountSetsId);

            Map<String, Double> maps = voucherDetailsMapper.selectListInitialCheckData(accountSetsId);
            if (maps == null) {
                log.info("未找到期初数据，视为平衡");
                return true;
            }

            Double debitAmount = maps.getOrDefault("debit_amount", 0d);
            Double creditAmount = maps.getOrDefault("credit_amount", 0d);

            boolean isBalanced = DoubleComparer.considerEqual(debitAmount, creditAmount);

            log.info("期初平衡检查结果 - 借方总额: {}, 贷方总额: {}, 是否平衡: {}",
                    debitAmount, creditAmount, isBalanced);

            if (!isBalanced) {
                double difference = Math.abs(debitAmount - creditAmount);
                log.warn("期初余额不平衡，差额: {}", difference);
            }

            return isBalanced;

        } catch (Exception e) {
            log.error("期初平衡检查时发生异常", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> detailedInitialCheck(Integer accountSetsId) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始详细期初平衡检查，账套ID: {}", accountSetsId);

            // 获取期初数据汇总
            Map<String, Double> maps = voucherDetailsMapper.selectListInitialCheckData(accountSetsId);

            Double totalDebitAmount = maps != null ? maps.getOrDefault("debit_amount", 0d) : 0d;
            Double totalCreditAmount = maps != null ? maps.getOrDefault("credit_amount", 0d) : 0d;

            // 基本平衡检查
            boolean isBalanced = DoubleComparer.considerEqual(totalDebitAmount, totalCreditAmount);
            double difference = Math.abs(totalDebitAmount - totalCreditAmount);

            result.put("isBalanced", isBalanced);
            result.put("totalDebitAmount", totalDebitAmount);
            result.put("totalCreditAmount", totalCreditAmount);
            result.put("difference", difference);

            // 按科目类型分类统计
            Map<String, Map<String, Double>> subjectTypeBalances = getSubjectTypeBalances(accountSetsId);
            result.put("subjectTypeBalances", subjectTypeBalances);

            // 检查各科目类型的平衡性
            Map<String, Boolean> typeBalanceCheck = new HashMap<>();
            for (Map.Entry<String, Map<String, Double>> entry : subjectTypeBalances.entrySet()) {
                String type = entry.getKey();
                Map<String, Double> typeBalance = entry.getValue();
                Double typeDebit = typeBalance.getOrDefault("debit", 0d);
                Double typeCredit = typeBalance.getOrDefault("credit", 0d);

                // 对于资产、成本类科目，借方余额正常；对于负债、权益、损益类科目，贷方余额正常
                boolean typeBalanced = true;
                if ("资产".equals(type) || "成本".equals(type)) {
                    typeBalanced = typeDebit >= typeCredit;
                } else if ("负债".equals(type) || "权益".equals(type)) {
                    typeBalanced = typeCredit >= typeDebit;
                }

                typeBalanceCheck.put(type, typeBalanced);
            }
            result.put("typeBalanceCheck", typeBalanceCheck);

            // 生成检查报告
            StringBuilder report = new StringBuilder();
            report.append("期初余额平衡检查报告\n");
            report.append("==================\n");
            report.append(String.format("总借方金额: %.2f\n", totalDebitAmount));
            report.append(String.format("总贷方金额: %.2f\n", totalCreditAmount));
            report.append(String.format("差额: %.2f\n", difference));
            report.append(String.format("是否平衡: %s\n", isBalanced ? "是" : "否"));

            if (!isBalanced) {
                report.append("\n⚠️ 期初余额不平衡，请检查以下科目的期初数据：\n");
                // 这里可以添加更详细的不平衡科目列表
            }

            result.put("report", report.toString());

            log.info("详细期初平衡检查完成 - 是否平衡: {}, 差额: {}", isBalanced, difference);

        } catch (Exception e) {
            log.error("详细期初平衡检查时发生异常", e);
            result.put("error", "检查过程中发生异常: " + e.getMessage());
            result.put("isBalanced", false);
        }

        return result;
    }

    /**
     * 获取按科目类型分类的余额统计
     */
    private Map<String, Map<String, Double>> getSubjectTypeBalances(Integer accountSetsId) {
        Map<String, Map<String, Double>> typeBalances = new HashMap<>();

        try {
            // 这里应该调用具体的SQL查询，按科目类型统计期初余额
            // 暂时返回空的统计，实际实现需要根据具体的数据库查询
            String[] types = {"资产", "负债", "权益", "成本", "损益", "共同"};
            for (String type : types) {
                Map<String, Double> balance = new HashMap<>();
                balance.put("debit", 0d);
                balance.put("credit", 0d);
                typeBalances.put(type, balance);
            }

        } catch (Exception e) {
            log.error("获取科目类型余额统计时发生异常", e);
        }

        return typeBalances;
    }

    /**
     * 期末检查
     *
     * @param accountSetsId
     * @param year
     * @param month
     * @return
     */
    @Override
    public boolean finalCheck(Integer accountSetsId, Integer year, Integer month) {
        VoucherDetails details = voucherDetailsMapper.selectFinalCheckData(accountSetsId, year, month);
        if (details == null) {
            return true;
        }
        if (details.getDebitAmount() != null && details.getCreditAmount() != null) {
            return DoubleComparer.considerEqual(details.getDebitAmount(), details.getCreditAmount());
        } else return details.getDebitAmount() == null && details.getCreditAmount() == null;
    }

    /**
     * 报表检查
     *
     * @param accountSetsId
     * @param year
     * @param month
     * @return
     */
    @Override
    public Map<String, Object> reportCheck(Integer accountSetsId, Integer year, Integer month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 1);

        List<VoucherDetails> details = voucherDetailsMapper.assetStatistics(accountSetsId, DateUtil.getMonthEnd(cal.getTime()));
        Map<String, Object> result = new HashMap<>(4);
        result.put("result", true);
        if (details != null) {
            Optional<VoucherDetails> assets = details.stream().filter(vd -> vd.getSubjectName().equals("资产")).findFirst();
            Optional<VoucherDetails> liabilities = details.stream().filter(vd -> vd.getSubjectName().equals("负债")).findFirst();
            Optional<VoucherDetails> rightsAndInterests = details.stream().filter(vd -> vd.getSubjectName().equals("权益")).findFirst();

            double assetsNum = 0, liabilitiesNum = 0, rightsAndInterestsNum = 0;

            if (assets.isPresent()) {
                assetsNum = DoubleValueUtil.getNotNullVal(assets.get().getDebitAmount()) - DoubleValueUtil.getNotNullVal(assets.get().getCreditAmount());
            }

            if (liabilities.isPresent()) {
                liabilitiesNum = DoubleValueUtil.getNotNullVal(liabilities.get().getDebitAmount()) - DoubleValueUtil.getNotNullVal(liabilities.get().getCreditAmount());
            }

            if (rightsAndInterests.isPresent()) {
                rightsAndInterestsNum = DoubleValueUtil.getNotNullVal(rightsAndInterests.get().getDebitAmount()) - DoubleValueUtil.getNotNullVal(rightsAndInterests.get().getCreditAmount());
            }

            //资产类 - 负债类 = 权益类
            result.put("result", DoubleComparer.considerEqual(Math.abs(assetsNum - liabilitiesNum), Math.abs(rightsAndInterestsNum)));
            result.put("资产类", assetsNum);
            result.put("负债类", liabilitiesNum);
            result.put("权益类", rightsAndInterestsNum);
        }
        return result;
    }

    /**
     * 断号检查
     *
     * @param accountSetsId
     * @param year
     * @param month
     * @return
     */
    @Override
    public boolean brokenCheck(Integer accountSetsId, Integer year, Integer month) {
        List<Map<String, Object>> data = this.voucherMapper.selectBrokenData(accountSetsId, year, month);
        return data.stream().allMatch(map -> map.get("total").equals(((Integer) map.get("code")).longValue()));
    }

    /**
     * 结账
     *
     * @param user
     * @param year
     * @param month
     * @return
     */
    @Override
    public boolean invoicing(UserVo user, Integer year, Integer month) {
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.YEAR, year);
        instance.set(Calendar.MONTH, month - 1);

        LambdaQueryWrapper<Checkout> qw = Wrappers.lambdaQuery();
        qw.eq(Checkout::getAccountSetsId, user.getAccountSetsId());
        qw.eq(Checkout::getCheckYear, year);
        qw.eq(Checkout::getCheckMonth, month);
        Checkout checkout = new Checkout();
        checkout.setStatus(2);
        checkout.setCheckDate(DateUtil.getMonthBegin(instance.getTime()));

        this.baseMapper.update(checkout, qw);

        DateFormat df = new SimpleDateFormat("yyyyMM");
        if (df.format(user.getAccountSets().getCurrentAccountDate()).equals(df.format(instance.getTime()))) {
            checkout = new Checkout();
            checkout.setAccountSetsId(user.getAccountSetsId());
            Date nextMonth = DateUtil.getMonthEnd(DateUtils.addMonths(user.getAccountSets().getCurrentAccountDate(), 1));
            instance = Calendar.getInstance();
            instance.setTime(nextMonth);
            checkout.setCheckYear(instance.get(Calendar.YEAR));
            checkout.setCheckMonth(instance.get(Calendar.MONTH) + 1);

            this.baseMapper.insert(checkout);
            //更新账套当前期间
            AccountSets accountSets = user.getAccountSets();
            accountSets.setCurrentAccountDate(nextMonth);
            this.accountSetsMapper.updateById(accountSets);
        }
        return true;
    }

    /**
     * 反结账
     *
     * @param currentUser
     * @param year
     * @param month
     * @return
     */
    @Override
    public boolean unCheck(UserVo currentUser, Integer year, Integer month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 2);
        LambdaQueryWrapper<Checkout> cqw = Wrappers.lambdaQuery();
        cqw.eq(Checkout::getAccountSetsId, currentUser.getAccountSetsId());
        cqw.eq(Checkout::getStatus, 2);
        cqw.gt(Checkout::getCheckDate, DateUtil.getMonthEnd(cal.getTime()));
        Checkout checkout = new Checkout();
        checkout.setStatus(0);
        checkout.setCheckDate(null);
        baseMapper.update(checkout, cqw);
        return true;
    }

    @Override
    public List<Checkout> list(Wrapper<Checkout> queryWrapper) {
        QueryWrapper qw = (QueryWrapper) queryWrapper;
        qw.orderByDesc("check_year", "check_month");
        return super.list(qw);
    }
}

