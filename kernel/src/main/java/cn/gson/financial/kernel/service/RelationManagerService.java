package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.dto.RelationCreateDto;
import cn.gson.financial.kernel.model.entity.EntityRelation;

import java.util.List;

/**
 * 关联管理服务接口
 */
public interface RelationManagerService {

    /**
     * 创建关联关系
     *
     * @param accountSetsId 账套ID
     * @param createDto 创建参数
     * @param currentUserId 当前用户ID
     * @return 关联ID
     */
    String createRelation(Integer accountSetsId, RelationCreateDto createDto, Integer currentUserId);

    /**
     * 批量创建关联关系
     *
     * @param accountSetsId 账套ID
     * @param batchCreateDto 批量创建参数
     * @param currentUserId 当前用户ID
     * @return 创建的关联ID列表
     */
    List<String> createBatchRelations(Integer accountSetsId, RelationCreateDto.BatchRelationCreateDto batchCreateDto, Integer currentUserId);

    /**
     * 查询票据相关关联
     *
     * @param accountSetsId 账套ID
     * @param entityId 实体ID
     * @param entityType 实体类型：DOCUMENT, DOCUMENT_GROUP
     * @return 关联关系列表
     */
    List<EntityRelation> queryDocumentRelations(Integer accountSetsId, String entityId, String entityType);

    /**
     * 查询银证相关关联
     *
     * @param accountSetsId 账套ID
     * @param entityId 实体ID
     * @param entityType 实体类型：RECEIPT, RECEIPT_GROUP
     * @return 关联关系列表
     */
    List<EntityRelation> queryReceiptRelations(Integer accountSetsId, String entityId, String entityType);

    /**
     * 查询跨类型关联
     *
     * @param accountSetsId 账套ID
     * @param entityId 实体ID
     * @param entityType 实体类型
     * @return 跨类型关联关系列表
     */
    List<EntityRelation> queryCrossTypeRelations(Integer accountSetsId, String entityId, String entityType);

    /**
     * 查询所有关联关系
     *
     * @param accountSetsId 账套ID
     * @param entityId 实体ID
     * @param entityType 实体类型
     * @return 所有关联关系列表
     */
    List<EntityRelation> queryAllRelations(Integer accountSetsId, String entityId, String entityType);

    /**
     * 删除关联关系
     *
     * @param accountSetsId 账套ID
     * @param relationId 关联ID
     * @return 是否成功
     */
    boolean deleteRelation(Integer accountSetsId, String relationId);

    /**
     * 删除实体的所有关联关系
     *
     * @param accountSetsId 账套ID
     * @param entityId 实体ID
     * @param entityType 实体类型
     * @return 删除的关联数量
     */
    int deleteAllRelationsByEntity(Integer accountSetsId, String entityId, String entityType);

    /**
     * 验证关联关系有效性
     *
     * @param accountSetsId 账套ID
     * @param sourceType 源类型
     * @param sourceId 源ID
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 验证结果
     */
    boolean validateRelation(Integer accountSetsId, String sourceType, String sourceId, String targetType, String targetId);

    /**
     * 归并时级联更新关联关系
     *
     * @param accountSetsId 账套ID
     * @param oldEntityId 原实体ID
     * @param newGroupId 新组ID
     * @param entityType 实体类型
     * @return 更新的关联数量
     */
    int cascadeUpdateOnMerge(Integer accountSetsId, String oldEntityId, String newGroupId, String entityType);
}
