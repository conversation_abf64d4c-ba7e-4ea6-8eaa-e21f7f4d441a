package cn.gson.financial.kernel.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 限流服务
 * 用于控制API调用频率，避免超出第三方服务的QPS限制
 */
@Service
@Slf4j
public class RateLimiterService {

    // 不同服务的限流器
    private final ConcurrentHashMap<String, ServiceRateLimiter> rateLimiters = new ConcurrentHashMap<>();

    /**
     * 获取或创建限流器
     * @param serviceName 服务名称
     * @param qps 每秒请求数限制
     * @return 限流器
     */
    public ServiceRateLimiter getRateLimiter(String serviceName, int qps) {
        return rateLimiters.computeIfAbsent(serviceName, k -> new ServiceRateLimiter(serviceName, qps));
    }

    /**
     * 腾讯云OCR限流器
     * @return OCR限流器
     */
    public ServiceRateLimiter getTencentOcrRateLimiter() {
        // 腾讯云OCR限制每秒10次请求，我们设置为8次以留出安全边界
        return getRateLimiter("TENCENT_OCR", 8);
    }

    /**
     * 服务限流器内部类
     */
    public static class ServiceRateLimiter {
        private final String serviceName;
        private final int qps;
        private final Semaphore semaphore;
        private final AtomicLong lastResetTime;
        private final AtomicLong requestCount;

        public ServiceRateLimiter(String serviceName, int qps) {
            this.serviceName = serviceName;
            this.qps = qps;
            this.semaphore = new Semaphore(qps);
            this.lastResetTime = new AtomicLong(System.currentTimeMillis());
            this.requestCount = new AtomicLong(0);
        }

        /**
         * 尝试获取许可（非阻塞）
         * @return 是否获取成功
         */
        public boolean tryAcquire() {
            resetIfNeeded();
            boolean acquired = semaphore.tryAcquire();
            if (acquired) {
                requestCount.incrementAndGet();
                log.debug("{}限流器：获取许可成功，当前秒内已使用: {}/{}", serviceName, requestCount.get(), qps);
            } else {
                log.warn("{}限流器：已达到QPS限制 {}/秒，请求被拒绝", serviceName, qps);
            }
            return acquired;
        }

        /**
         * 获取许可（阻塞等待）
         * @param timeout 超时时间
         * @param unit 时间单位
         * @return 是否获取成功
         * @throws InterruptedException 中断异常
         */
        public boolean acquire(long timeout, TimeUnit unit) throws InterruptedException {
            resetIfNeeded();
            boolean acquired = semaphore.tryAcquire(timeout, unit);
            if (acquired) {
                requestCount.incrementAndGet();
                log.debug("{}限流器：获取许可成功，当前秒内已使用: {}/{}", serviceName, requestCount.get(), qps);
            } else {
                log.warn("{}限流器：等待超时，未能获取许可", serviceName);
            }
            return acquired;
        }

        /**
         * 智能等待获取许可
         * 如果当前秒内许可已用完，会等待到下一秒
         */
        public void acquireWithSmartWait() throws InterruptedException {
            resetIfNeeded();
            
            if (!semaphore.tryAcquire()) {
                // 计算需要等待到下一秒的时间
                long currentTime = System.currentTimeMillis();
                long nextSecond = ((currentTime / 1000) + 1) * 1000;
                long waitTime = nextSecond - currentTime;
                
                log.info("{}限流器：当前秒内许可已用完，等待{}ms到下一秒", serviceName, waitTime);
                Thread.sleep(waitTime + 10); // 多等待10ms确保进入下一秒
                
                // 重置并获取许可
                resetIfNeeded();
                semaphore.acquire();
            }
            
            requestCount.incrementAndGet();
            log.debug("{}限流器：获取许可成功，当前秒内已使用: {}/{}", serviceName, requestCount.get(), qps);
        }

        /**
         * 释放许可
         */
        public void release() {
            semaphore.release();
        }

        /**
         * 如果需要则重置计数器
         */
        private void resetIfNeeded() {
            long currentTime = System.currentTimeMillis();
            long lastReset = lastResetTime.get();
            
            // 如果距离上次重置超过1秒，则重置
            if (currentTime - lastReset >= 1000) {
                if (lastResetTime.compareAndSet(lastReset, currentTime)) {
                    // 重置信号量
                    semaphore.drainPermits();
                    semaphore.release(qps);
                    requestCount.set(0);
                    log.debug("{}限流器：重置计数器，可用许可: {}", serviceName, qps);
                }
            }
        }

        /**
         * 获取当前状态信息
         */
        public String getStatus() {
            resetIfNeeded();
            return String.format("%s限流器状态 - QPS限制: %d, 当前秒内已使用: %d, 可用许可: %d", 
                    serviceName, qps, requestCount.get(), semaphore.availablePermits());
        }

        /**
         * 获取当前可用许可数
         */
        public int getAvailablePermits() {
            resetIfNeeded();
            return semaphore.availablePermits();
        }

        /**
         * 获取当前秒内已使用的请求数
         */
        public long getCurrentSecondUsage() {
            resetIfNeeded();
            return requestCount.get();
        }
    }
}
