package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.entity.FinancialAiConfig;
import cn.gson.financial.kernel.model.mapper.FinancialAiConfigMapper;
import cn.gson.financial.kernel.service.FinancialAiConfigService;
import cn.gson.financial.kernel.service.CacheService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI配置服务实现类
 */
@Service
public class FinancialAiConfigServiceImpl extends ServiceImpl<FinancialAiConfigMapper, FinancialAiConfig>
        implements FinancialAiConfigService {

    @Autowired(required = false)
    private CacheService cacheService;
    
    @Override
    public Map<String, String> getAiConfig(Integer accountSetsId) {
        // 先尝试从缓存获取
        if (cacheService != null && cacheService.isRedisAvailable()) {
            Map<String, String> cachedConfig = cacheService.getCachedAiConfigs(accountSetsId);
            if (cachedConfig != null) {
                return cachedConfig;
            }
        }

        // 从数据库获取
        List<FinancialAiConfig> configList = baseMapper.selectByAccountSetsId(accountSetsId);
        Map<String, String> configMap = new HashMap<>();

        for (FinancialAiConfig config : configList) {
            configMap.put(config.getConfigKey(), config.getConfigValue());
        }

        // 缓存结果
        if (cacheService != null && cacheService.isRedisAvailable() && !configMap.isEmpty()) {
            cacheService.cacheAiConfigs(accountSetsId, configMap);
        }

        return configMap;
    }
    
    @Override
    @Transactional
    public boolean saveAiConfig(Integer accountSetsId, Map<String, String> configMap) {
        try {
            for (Map.Entry<String, String> entry : configMap.entrySet()) {
                setConfigValue(accountSetsId, entry.getKey(), entry.getValue());
            }

            // 清除缓存，确保下次获取时从数据库重新加载
            if (cacheService != null && cacheService.isRedisAvailable()) {
                cacheService.clearAiConfigCache(accountSetsId);
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public String getConfigValue(Integer accountSetsId, String configKey) {
        FinancialAiConfig config = baseMapper.selectByAccountAndKey(accountSetsId, configKey);
        return config != null ? config.getConfigValue() : null;
    }
    
    @Override
    public String getConfigValue(Integer accountSetsId, String configKey, String defaultValue) {
        String value = getConfigValue(accountSetsId, configKey);
        return value != null ? value : defaultValue;
    }
    
    @Override
    @Transactional
    public boolean setConfigValue(Integer accountSetsId, String configKey, String configValue) {
        try {
            FinancialAiConfig existingConfig = baseMapper.selectByAccountAndKey(accountSetsId, configKey);
            
            if (existingConfig != null) {
                // 更新现有配置
                existingConfig.setConfigValue(configValue);
                existingConfig.setUpdateTime(LocalDateTime.now());
                updateById(existingConfig);
            } else {
                // 创建新配置
                FinancialAiConfig newConfig = new FinancialAiConfig();
                newConfig.setAccountSetsId(accountSetsId);
                newConfig.setConfigKey(configKey);
                newConfig.setConfigValue(configValue);
                newConfig.setIsActive(true);
                newConfig.setCreateTime(LocalDateTime.now());
                newConfig.setUpdateTime(LocalDateTime.now());
                save(newConfig);
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public boolean isAiEnabled(Integer accountSetsId) {
        String enabled = getConfigValue(accountSetsId, FinancialAiConfig.KEY_ENABLED, "false");
        return "true".equalsIgnoreCase(enabled);
    }
    
    @Override
    @Transactional
    public void initDefaultConfig(Integer accountSetsId) {
        // 检查是否已有配置
        List<FinancialAiConfig> existingConfigs = baseMapper.selectByAccountSetsId(accountSetsId);
        if (!existingConfigs.isEmpty()) {
            return; // 已有配置，不需要初始化
        }
        
        // 初始化默认配置
        Map<String, String> defaultConfigs = new HashMap<>();
        defaultConfigs.put(FinancialAiConfig.KEY_ENABLED, "false");
        defaultConfigs.put(FinancialAiConfig.KEY_BASE_URL, "https://api.openai.com/v1");
        defaultConfigs.put(FinancialAiConfig.KEY_DEFAULT_MODEL, "gpt-3.5-turbo");
        defaultConfigs.put(FinancialAiConfig.KEY_TIMEOUT, "60");
        defaultConfigs.put(FinancialAiConfig.KEY_MAX_RETRIES, "3");
        defaultConfigs.put(FinancialAiConfig.KEY_TEMPERATURE, "0.7");
        defaultConfigs.put(FinancialAiConfig.KEY_MAX_TOKENS, "2000");
        
        saveAiConfig(accountSetsId, defaultConfigs);
    }

    @Override
    public Map<String, String> getAiConfigByUser(Integer userId) {
        QueryWrapper<FinancialAiConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("is_active", 1);

        List<FinancialAiConfig> configs = this.list(queryWrapper);
        Map<String, String> configMap = new HashMap<>();

        for (FinancialAiConfig config : configs) {
            configMap.put(config.getConfigKey(), config.getConfigValue());
        }

        return configMap;
    }

    @Override
    public boolean saveAiConfigByUser(Integer userId, Map<String, String> configMap) {
        try {
            for (Map.Entry<String, String> entry : configMap.entrySet()) {
                setConfigValueByUser(userId, entry.getKey(), entry.getValue());
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public String getConfigValueByUser(Integer userId, String configKey) {
        QueryWrapper<FinancialAiConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("config_key", configKey);
        queryWrapper.eq("is_active", 1);

        FinancialAiConfig config = this.getOne(queryWrapper);
        return config != null ? config.getConfigValue() : null;
    }

    @Override
    public boolean setConfigValueByUser(Integer userId, String configKey, String configValue) {
        try {
            QueryWrapper<FinancialAiConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.eq("config_key", configKey);
            queryWrapper.eq("is_active", 1);

            FinancialAiConfig existingConfig = this.getOne(queryWrapper);

            if (existingConfig != null) {
                // 更新现有配置
                existingConfig.setConfigValue(configValue);
                existingConfig.setUpdateTime(LocalDateTime.now());
                return this.updateById(existingConfig);
            } else {
                // 创建新配置
                FinancialAiConfig newConfig = new FinancialAiConfig();
                newConfig.setUserId(userId);
                newConfig.setConfigKey(configKey);
                newConfig.setConfigValue(configValue);
                newConfig.setDescription("用户AI配置");
                newConfig.setIsActive(true);
                newConfig.setCreateTime(LocalDateTime.now());
                newConfig.setUpdateTime(LocalDateTime.now());
                return this.save(newConfig);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean isAiEnabledByUser(Integer userId) {
        String enabled = getConfigValueByUser(userId, "enabled");
        return "true".equalsIgnoreCase(enabled);
    }

    @Override
    public void initDefaultConfigByUser(Integer userId) {
        // 检查是否已存在配置
        QueryWrapper<FinancialAiConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("is_active", 1);

        List<FinancialAiConfig> existingConfigs = this.list(queryWrapper);
        if (!existingConfigs.isEmpty()) {
            return; // 已存在配置，不需要初始化
        }

        // 创建默认配置
        Map<String, String> defaultConfigs = new HashMap<>();
        defaultConfigs.put("enabled", "false");
        defaultConfigs.put("base_url", "https://api.deepseek.com/v1");
        defaultConfigs.put("api_key", "");
        defaultConfigs.put("default_model", "deepseek-chat");
        defaultConfigs.put("timeout", "30");
        defaultConfigs.put("max_tokens", "2000");
        defaultConfigs.put("temperature", "0.7");

        saveAiConfigByUser(userId, defaultConfigs);
    }
}
