package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.mapper.SubjectAiEnhancementMapper;
import cn.gson.financial.kernel.model.mapper.SubjectMapper;
import cn.gson.financial.kernel.model.entity.Subject;
import cn.gson.financial.kernel.model.entity.SubjectAiEnhancement;
import cn.gson.financial.kernel.service.SubjectAiEnhancementService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 科目AI增强服务实现类
 * 
 * <AUTHOR> Financial System
 * @since 2024-01-01
 */
@Slf4j
@Service
public class SubjectAiEnhancementServiceImpl extends ServiceImpl<SubjectAiEnhancementMapper, SubjectAiEnhancement> 
        implements SubjectAiEnhancementService {

    @Autowired
    private SubjectAiEnhancementMapper subjectAiEnhancementMapper;
    
    @Autowired
    private SubjectMapper subjectMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<SubjectAiEnhancement> intelligentMatchSubjects(Integer accountSetsId, 
                                                              String description, 
                                                              BigDecimal amount, 
                                                              String businessType) {
        log.info("智能匹配科目 - 账套ID: {}, 描述: {}, 金额: {}, 业务类型: {}", 
                accountSetsId, description, amount, businessType);
        
        // 提取关键词
        String keywords = extractKeywords(description);
        
        // 调用智能匹配
        List<SubjectAiEnhancement> matches = subjectAiEnhancementMapper.intelligentMatch(
                accountSetsId, keywords, amount, businessType);
        
        // 记录匹配日志
        log.debug("匹配到 {} 个科目", matches.size());
        
        return matches;
    }

    @Override
    public List<SubjectAiEnhancement> searchSubjectsByKeyword(Integer accountSetsId, String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return Collections.emptyList();
        }
        
        return subjectAiEnhancementMapper.searchByKeyword(accountSetsId, keyword.trim());
    }

    @Override
    public List<SubjectAiEnhancement> getTopUsedSubjects(Integer accountSetsId, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10; // 默认返回前10个
        }
        
        return subjectAiEnhancementMapper.getTopUsedSubjects(accountSetsId, limit);
    }

    @Override
    public List<SubjectAiEnhancement> getRecommendedSubjects(Integer accountSetsId, BigDecimal minConfidence) {
        if (minConfidence == null) {
            minConfidence = SubjectAiEnhancement.DEFAULT_CONFIDENCE_THRESHOLD;
        }
        
        return subjectAiEnhancementMapper.getRecommendedSubjects(accountSetsId, minConfidence);
    }

    @Override
    @Transactional
    public void recordSubjectUsage(Integer subjectId, 
                                  Integer accountSetsId, 
                                  Map<String, Object> matchingContext, 
                                  Boolean isCorrect) {
        try {
            // 获取AI增强信息
            SubjectAiEnhancement enhancement = getSubjectAiEnhancement(subjectId, accountSetsId);
            if (enhancement == null) {
                log.warn("未找到科目AI增强信息 - 科目ID: {}, 账套ID: {}", subjectId, accountSetsId);
                return;
            }
            
            // 更新使用频率
            subjectAiEnhancementMapper.incrementUsageFrequency(enhancement.getId());
            
            // 更新学习数据
            updateLearningData(enhancement, matchingContext, isCorrect);
            
            // 如果匹配正确，提高置信度；如果错误，降低置信度
            if (isCorrect != null) {
                adjustConfidenceScore(enhancement, isCorrect);
            }
            
            log.info("记录科目使用情况 - 科目ID: {}, 是否正确: {}", subjectId, isCorrect);
            
        } catch (Exception e) {
            log.error("记录科目使用情况失败", e);
        }
    }

    @Override
    @Transactional
    public boolean updateSubjectAiInfo(Integer subjectId, 
                                      Integer accountSetsId, 
                                      String aiDescription, 
                                      String aiKeywords) {
        try {
            SubjectAiEnhancement enhancement = getSubjectAiEnhancement(subjectId, accountSetsId);
            
            if (enhancement == null) {
                // 创建新的AI增强信息
                enhancement = new SubjectAiEnhancement();
                enhancement.setSubjectId(subjectId);
                enhancement.setAccountSetsId(accountSetsId);
                enhancement.setAiDescription(aiDescription);
                enhancement.setAiKeywords(aiKeywords);
                enhancement.setStatus(SubjectAiEnhancement.Status.ENABLED);
                enhancement.setUsageFrequency(0);
                enhancement.setConfidenceScore(new BigDecimal("0.5000"));
                enhancement.setCreateTime(LocalDateTime.now());
                
                return save(enhancement);
            } else {
                // 更新现有信息
                enhancement.setAiDescription(aiDescription);
                enhancement.setAiKeywords(aiKeywords);
                enhancement.setUpdateTime(LocalDateTime.now());
                
                return updateById(enhancement);
            }
            
        } catch (Exception e) {
            log.error("更新科目AI信息失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public int initializeSubjectAiEnhancement(Integer accountSetsId) {
        try {
            // 获取账套下所有科目
            QueryWrapper<Subject> subjectQuery = new QueryWrapper<>();
            subjectQuery.eq("account_sets_id", accountSetsId)
                       .eq("status", 1); // 只处理启用的科目
            
            List<Subject> subjects = subjectMapper.selectList(subjectQuery);
            
            int initializedCount = 0;
            
            for (Subject subject : subjects) {
                // 检查是否已存在AI增强信息
                SubjectAiEnhancement existing = getSubjectAiEnhancement(subject.getId(), accountSetsId);
                if (existing != null) {
                    continue; // 已存在，跳过
                }
                
                // 创建默认的AI增强信息
                SubjectAiEnhancement enhancement = createDefaultEnhancement(subject, accountSetsId);
                if (save(enhancement)) {
                    initializedCount++;
                }
            }
            
            log.info("初始化科目AI增强信息完成 - 账套ID: {}, 初始化数量: {}", accountSetsId, initializedCount);
            return initializedCount;
            
        } catch (Exception e) {
            log.error("初始化科目AI增强信息失败", e);
            return 0;
        }
    }

    @Override
    public int optimizeMatchingRules(Integer accountSetsId) {
        // TODO: 实现基于历史数据的匹配规则优化
        log.info("优化匹配规则 - 账套ID: {}", accountSetsId);
        return 0;
    }

    @Override
    public SubjectAiEnhancement getSubjectAiEnhancement(Integer subjectId, Integer accountSetsId) {
        return subjectAiEnhancementMapper.getBySubjectAndAccount(subjectId, accountSetsId);
    }

    @Override
    @Transactional
    public boolean saveOrUpdateEnhancement(SubjectAiEnhancement enhancement) {
        if (enhancement.getId() == null) {
            // 新增
            enhancement.setCreateTime(LocalDateTime.now());
            enhancement.setUpdateTime(LocalDateTime.now());
            return save(enhancement);
        } else {
            // 更新
            enhancement.setUpdateTime(LocalDateTime.now());
            return updateById(enhancement);
        }
    }

    @Override
    public boolean updateStatus(Integer id, Integer status) {
        SubjectAiEnhancement enhancement = getById(id);
        if (enhancement == null) {
            return false;
        }
        
        enhancement.setStatus(status);
        enhancement.setUpdateTime(LocalDateTime.now());
        return updateById(enhancement);
    }

    @Override
    public List<SubjectAiEnhancement> getAllEnabledSubjects(Integer accountSetsId) {
        return subjectAiEnhancementMapper.getAllEnabledByAccount(accountSetsId);
    }

    @Override
    public Map<String, Object> analyzeSubjectUsageStatistics(Integer accountSetsId, 
                                                            String startDate, 
                                                            String endDate) {
        // TODO: 实现科目使用统计分析
        Map<String, Object> result = new HashMap<>();
        result.put("accountSetsId", accountSetsId);
        result.put("startDate", startDate);
        result.put("endDate", endDate);
        result.put("totalSubjects", 0);
        result.put("activeSubjects", 0);
        result.put("topUsedSubjects", Collections.emptyList());
        
        return result;
    }

    @Override
    public List<Map<String, Object>> exportAiConfiguration(Integer accountSetsId) {
        List<SubjectAiEnhancement> enhancements = getAllEnabledSubjects(accountSetsId);
        
        return enhancements.stream().map(enhancement -> {
            Map<String, Object> config = new HashMap<>();
            config.put("subjectId", enhancement.getSubjectId());
            config.put("aiDescription", enhancement.getAiDescription());
            config.put("aiKeywords", enhancement.getAiKeywords());
            config.put("matchingRules", enhancement.getMatchingRules());
            config.put("confidenceScore", enhancement.getConfidenceScore());
            return config;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public int importAiConfiguration(Integer accountSetsId, List<Map<String, Object>> configData) {
        int importedCount = 0;
        
        for (Map<String, Object> config : configData) {
            try {
                Integer subjectId = (Integer) config.get("subjectId");
                String aiDescription = (String) config.get("aiDescription");
                String aiKeywords = (String) config.get("aiKeywords");
                
                if (updateSubjectAiInfo(subjectId, accountSetsId, aiDescription, aiKeywords)) {
                    importedCount++;
                }
                
            } catch (Exception e) {
                log.error("导入AI配置失败", e);
            }
        }
        
        return importedCount;
    }

    @Override
    public int learnUserPreferences(Integer accountSetsId, Integer userId) {
        // TODO: 实现用户偏好学习
        log.info("学习用户偏好 - 账套ID: {}, 用户ID: {}", accountSetsId, userId);
        return 0;
    }

    @Override
    public List<Map<String, Object>> getMatchingSuggestions(Integer accountSetsId, 
                                                           String inputText, 
                                                           Map<String, Object> contextInfo) {
        if (!StringUtils.hasText(inputText)) {
            return Collections.emptyList();
        }
        
        // 搜索匹配的科目
        List<SubjectAiEnhancement> matches = searchSubjectsByKeyword(accountSetsId, inputText);
        
        return matches.stream().limit(5).map(enhancement -> {
            Map<String, Object> suggestion = new HashMap<>();
            suggestion.put("subjectId", enhancement.getSubjectId());
            suggestion.put("subjectCode", ""); // 需要关联查询科目信息
            suggestion.put("subjectName", ""); // 需要关联查询科目信息
            suggestion.put("aiDescription", enhancement.getAiDescription());
            suggestion.put("confidenceScore", enhancement.getConfidenceScore());
            suggestion.put("usageFrequency", enhancement.getUsageFrequency());
            return suggestion;
        }).collect(Collectors.toList());
    }

    /**
     * 提取关键词
     */
    private String extractKeywords(String description) {
        if (!StringUtils.hasText(description)) {
            return "";
        }
        
        // 简单的关键词提取逻辑，可以后续优化为更智能的NLP处理
        return description.trim().replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s]", " ")
                         .replaceAll("\\s+", " ")
                         .trim();
    }

    /**
     * 更新学习数据
     */
    private void updateLearningData(SubjectAiEnhancement enhancement, 
                                   Map<String, Object> matchingContext, 
                                   Boolean isCorrect) {
        try {
            Map<String, Object> learningData = new HashMap<>();
            
            // 解析现有学习数据
            if (StringUtils.hasText(enhancement.getLearningData())) {
                learningData = objectMapper.readValue(enhancement.getLearningData(), Map.class);
            }
            
            // 添加新的学习记录
            List<Map<String, Object>> records = (List<Map<String, Object>>) learningData.get("records");
            if (records == null) {
                records = new ArrayList<>();
            }
            
            Map<String, Object> record = new HashMap<>();
            record.put("timestamp", LocalDateTime.now().toString());
            record.put("context", matchingContext);
            record.put("isCorrect", isCorrect);
            
            records.add(record);
            
            // 保持最近100条记录
            if (records.size() > 100) {
                records = records.subList(records.size() - 100, records.size());
            }
            
            learningData.put("records", records);
            learningData.put("lastUpdated", LocalDateTime.now().toString());
            
            // 更新学习数据
            enhancement.setLearningData(objectMapper.writeValueAsString(learningData));
            updateById(enhancement);
            
        } catch (JsonProcessingException e) {
            log.error("更新学习数据失败", e);
        }
    }

    /**
     * 调整置信度评分
     */
    private void adjustConfidenceScore(SubjectAiEnhancement enhancement, Boolean isCorrect) {
        BigDecimal currentScore = enhancement.getConfidenceScore();
        if (currentScore == null) {
            currentScore = new BigDecimal("0.5000");
        }
        
        BigDecimal adjustment = isCorrect ? new BigDecimal("0.01") : new BigDecimal("-0.02");
        BigDecimal newScore = currentScore.add(adjustment);
        
        // 确保评分在0-1之间
        if (newScore.compareTo(BigDecimal.ZERO) < 0) {
            newScore = BigDecimal.ZERO;
        } else if (newScore.compareTo(BigDecimal.ONE) > 0) {
            newScore = BigDecimal.ONE;
        }
        
        subjectAiEnhancementMapper.updateConfidenceScore(
                enhancement.getSubjectId(), 
                enhancement.getAccountSetsId(), 
                newScore);
    }

    /**
     * 创建默认的AI增强信息
     */
    private SubjectAiEnhancement createDefaultEnhancement(Subject subject, Integer accountSetsId) {
        SubjectAiEnhancement enhancement = new SubjectAiEnhancement();
        enhancement.setSubjectId(subject.getId());
        enhancement.setAccountSetsId(accountSetsId);
        enhancement.setAiDescription(generateDefaultDescription(subject));
        enhancement.setAiKeywords(generateDefaultKeywords(subject));
        enhancement.setStatus(SubjectAiEnhancement.Status.ENABLED);
        enhancement.setUsageFrequency(0);
        enhancement.setConfidenceScore(new BigDecimal("0.5000"));
        enhancement.setCreateTime(LocalDateTime.now());
        enhancement.setUpdateTime(LocalDateTime.now());
        
        return enhancement;
    }

    /**
     * 生成默认描述
     */
    private String generateDefaultDescription(Subject subject) {
        return String.format("科目：%s（%s），类型：%s", 
                subject.getName(), subject.getCode(), subject.getType());
    }

    /**
     * 生成默认关键词
     */
    private String generateDefaultKeywords(Subject subject) {
        List<String> keywords = new ArrayList<>();
        keywords.add(subject.getName());
        keywords.add(subject.getCode());
        
        if (StringUtils.hasText(subject.getMnemonicCode())) {
            keywords.add(subject.getMnemonicCode());
        }
        
        return String.join(",", keywords);
    }
}