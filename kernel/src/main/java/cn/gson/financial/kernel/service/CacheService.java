package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.entity.FieldMappingTemplate;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Redis缓存服务
 * 用于缓存字段映射模板和AI配置等数据
 */
@Service
@Slf4j
public class CacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    // 缓存键前缀
    private static final String FIELD_MAPPING_TEMPLATE_PREFIX = "field_mapping_template:";
    private static final String AI_CONFIG_PREFIX = "ai_config:";
    private static final String OCR_RESULT_PREFIX = "ocr_result:";
    
    // 缓存过期时间
    private static final long TEMPLATE_CACHE_EXPIRE = 24; // 24小时
    private static final long AI_CONFIG_CACHE_EXPIRE = 12; // 12小时
    private static final long OCR_RESULT_CACHE_EXPIRE = 1; // 1小时

    /**
     * 缓存字段映射模板
     * @param accountSetsId 账套ID
     * @param documentType 文档类型
     * @param fieldSignature 字段签名
     * @param template 模板对象
     */
    public void cacheFieldMappingTemplate(Integer accountSetsId, String documentType, 
                                        String fieldSignature, FieldMappingTemplate template) {
        try {
            String key = buildFieldMappingTemplateKey(accountSetsId, documentType, fieldSignature);
            redisTemplate.opsForValue().set(key, template, TEMPLATE_CACHE_EXPIRE, TimeUnit.HOURS);
            log.debug("缓存字段映射模板: {}", key);
        } catch (Exception e) {
            log.error("缓存字段映射模板失败", e);
        }
    }

    /**
     * 获取缓存的字段映射模板
     * @param accountSetsId 账套ID
     * @param documentType 文档类型
     * @param fieldSignature 字段签名
     * @return 模板对象，如果不存在则返回null
     */
    public FieldMappingTemplate getCachedFieldMappingTemplate(Integer accountSetsId, String documentType, 
                                                            String fieldSignature) {
        try {
            String key = buildFieldMappingTemplateKey(accountSetsId, documentType, fieldSignature);
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached instanceof FieldMappingTemplate) {
                log.debug("命中字段映射模板缓存: {}", key);
                return (FieldMappingTemplate) cached;
            }
        } catch (Exception e) {
            log.error("获取缓存的字段映射模板失败", e);
        }
        return null;
    }

    /**
     * 缓存AI配置
     * @param accountSetsId 账套ID
     * @param configs AI配置列表
     */
    public void cacheAiConfigs(Integer accountSetsId, Map<String, String> configs) {
        try {
            String key = buildAiConfigKey(accountSetsId);
            redisTemplate.opsForValue().set(key, configs, AI_CONFIG_CACHE_EXPIRE, TimeUnit.HOURS);
            log.debug("缓存AI配置: {}, 配置数量: {}", key, configs.size());
        } catch (Exception e) {
            log.error("缓存AI配置失败", e);
        }
    }

    /**
     * 获取缓存的AI配置
     * @param accountSetsId 账套ID
     * @return AI配置Map，如果不存在则返回null
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> getCachedAiConfigs(Integer accountSetsId) {
        try {
            String key = buildAiConfigKey(accountSetsId);
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached instanceof Map) {
                log.debug("命中AI配置缓存: {}", key);
                return (Map<String, String>) cached;
            }
        } catch (Exception e) {
            log.error("获取缓存的AI配置失败", e);
        }
        return null;
    }

    /**
     * 缓存OCR识别结果
     * @param imageUrl 图片URL
     * @param ocrResult OCR识别结果
     */
    public void cacheOcrResult(String imageUrl, Map<String, Object> ocrResult) {
        try {
            String key = buildOcrResultKey(imageUrl);
            redisTemplate.opsForValue().set(key, ocrResult, OCR_RESULT_CACHE_EXPIRE, TimeUnit.HOURS);
            log.debug("缓存OCR识别结果: {}", key);
        } catch (Exception e) {
            log.error("缓存OCR识别结果失败", e);
        }
    }

    /**
     * 获取缓存的OCR识别结果
     * @param imageUrl 图片URL
     * @return OCR识别结果，如果不存在则返回null
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getCachedOcrResult(String imageUrl) {
        try {
            String key = buildOcrResultKey(imageUrl);
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached instanceof Map) {
                log.debug("命中OCR结果缓存: {}", key);
                return (Map<String, Object>) cached;
            }
        } catch (Exception e) {
            log.error("获取缓存的OCR识别结果失败", e);
        }
        return null;
    }

    /**
     * 清除字段映射模板缓存
     * @param accountSetsId 账套ID
     * @param documentType 文档类型
     */
    public void clearFieldMappingTemplateCache(Integer accountSetsId, String documentType) {
        try {
            String pattern = FIELD_MAPPING_TEMPLATE_PREFIX + accountSetsId + ":" + documentType + ":*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            log.info("清除字段映射模板缓存: {}", pattern);
        } catch (Exception e) {
            log.error("清除字段映射模板缓存失败", e);
        }
    }

    /**
     * 清除所有字段映射模板缓存
     */
    public void clearAllFieldMappingTemplateCache() {
        try {
            String pattern = FIELD_MAPPING_TEMPLATE_PREFIX + "*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            log.info("清除所有字段映射模板缓存: {}", pattern);
        } catch (Exception e) {
            log.error("清除所有字段映射模板缓存失败", e);
        }
    }

    /**
     * 清除AI配置缓存
     * @param accountSetsId 账套ID
     */
    public void clearAiConfigCache(Integer accountSetsId) {
        try {
            String key = buildAiConfigKey(accountSetsId);
            redisTemplate.delete(key);
            log.info("清除AI配置缓存: {}", key);
        } catch (Exception e) {
            log.error("清除AI配置缓存失败", e);
        }
    }

    /**
     * 构建字段映射模板缓存键
     */
    private String buildFieldMappingTemplateKey(Integer accountSetsId, String documentType, String fieldSignature) {
        return FIELD_MAPPING_TEMPLATE_PREFIX + accountSetsId + ":" + documentType + ":" + fieldSignature;
    }

    /**
     * 构建AI配置缓存键
     */
    private String buildAiConfigKey(Integer accountSetsId) {
        return AI_CONFIG_PREFIX + accountSetsId;
    }

    /**
     * 构建OCR结果缓存键
     */
    private String buildOcrResultKey(String imageUrl) {
        // 使用URL的hash值作为键，避免键过长
        return OCR_RESULT_PREFIX + Math.abs(imageUrl.hashCode());
    }

    /**
     * 检查Redis连接状态
     * @return true表示连接正常
     */
    public boolean isRedisAvailable() {
        try {
            redisTemplate.opsForValue().get("test");
            return true;
        } catch (Exception e) {
            log.warn("Redis连接不可用: {}", e.getMessage());
            return false;
        }
    }
}
