package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.dto.ManualMergeDto;
import cn.gson.financial.kernel.model.dto.MergeExecuteDto;
import cn.gson.financial.kernel.model.dto.MergePreviewDto;
import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.entity.MergeTask;

/**
 * 归并引擎服务接口
 */
public interface MergeEngineService {

    /**
     * 预览票据归并结果
     *
     * @param accountSetsId 账套ID
     * @param ruleId 规则ID
     * @return 预览结果
     */
    MergePreviewDto previewDocumentMerge(Integer accountSetsId, String ruleId);

    /**
     * 执行票据归并
     *
     * @param accountSetsId 账套ID
     * @param executeDto 执行参数
     * @param currentUserId 当前用户ID
     * @return 任务ID（异步执行时）或执行结果
     */
    Object executeDocumentMerge(Integer accountSetsId, MergeExecuteDto executeDto, Integer currentUserId);

    /**
     * 预览银证归并结果
     *
     * @param accountSetsId 账套ID
     * @param ruleId 规则ID
     * @return 预览结果
     */
    MergePreviewDto previewReceiptMerge(Integer accountSetsId, String ruleId);

    /**
     * 执行银证归并
     *
     * @param accountSetsId 账套ID
     * @param executeDto 执行参数
     * @param currentUserId 当前用户ID
     * @return 任务ID（异步执行时）或执行结果
     */
    Object executeReceiptMerge(Integer accountSetsId, MergeExecuteDto executeDto, Integer currentUserId);

    /**
     * 手动票据归并
     *
     * @param accountSetsId 账套ID
     * @param documentIds 票据ID列表
     * @param groupName 组名称
     * @param currentUserId 当前用户ID
     * @return 归并组ID
     */
    String manualDocumentMerge(Integer accountSetsId, java.util.List<Integer> documentIds, String groupName, Integer currentUserId);

    /**
     * 手动银证归并
     *
     * @param accountSetsId 账套ID
     * @param receiptIds 银证ID列表
     * @param groupName 组名称
     * @param currentUserId 当前用户ID
     * @return 归并组ID
     */
    String manualReceiptMerge(Integer accountSetsId, java.util.List<Integer> receiptIds, String groupName, Integer currentUserId);

    /**
     * 解散票据归并组
     *
     * @param accountSetsId 账套ID
     * @param groupId 组ID
     * @return 是否成功
     */
    boolean unmergeDocumentGroup(Integer accountSetsId, String groupId);

    /**
     * 解散银证归并组
     *
     * @param accountSetsId 账套ID
     * @param groupId 组ID
     * @return 是否成功
     */
    boolean unmergeReceiptGroup(Integer accountSetsId, String groupId);

    /**
     * 增量归并新记录
     *
     * @param accountSetsId 账套ID
     * @param entityId 实体ID
     * @param entityType 实体类型：DOCUMENT, RECEIPT
     * @param currentUserId 当前用户ID
     * @return 是否成功归并
     */
    boolean incrementalMerge(Integer accountSetsId, String entityId, String entityType, Integer currentUserId);

    /**
     * 查询归并任务状态
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    MergeTask getTaskStatus(String taskId);

    /**
     * 获取未归并的票据
     *
     * @param accountSetsId 账套ID
     * @return 未归并的票据列表
     */
    java.util.List<Bill> getUnmergedDocuments(Integer accountSetsId);

    /**
     * 获取未归并的银证
     *
     * @param accountSetsId 账套ID
     * @return 未归并的银证列表
     */
    java.util.List<BankReceipts> getUnmergedReceipts(Integer accountSetsId);

    /**
     * 手动票据归并（新版本，使用DTO）
     *
     * @param accountSetsId 账套ID
     * @param mergeDto 归并参数
     * @param currentUserId 当前用户ID
     * @return 执行结果
     */
    Object manualMergeDocuments(Integer accountSetsId, ManualMergeDto mergeDto, Integer currentUserId);

    /**
     * 手动银证归并（新版本，使用DTO）
     *
     * @param accountSetsId 账套ID
     * @param mergeDto 归并参数
     * @param currentUserId 当前用户ID
     * @return 执行结果
     */
    Object manualMergeReceipts(Integer accountSetsId, ManualMergeDto mergeDto, Integer currentUserId);
}
