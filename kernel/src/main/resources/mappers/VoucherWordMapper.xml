<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gson.financial.kernel.model.mapper.VoucherWordMapper">
  <resultMap id="BaseResultMap" type="cn.gson.financial.kernel.model.entity.VoucherWord">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="word" jdbcType="VARCHAR" property="word" />
    <result column="print_title" jdbcType="VARCHAR" property="printTitle" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
    <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, word, print_title, is_default, account_sets_id
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into fxy_financial_voucher_word
    (word, print_title, is_default, account_sets_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.word,jdbcType=VARCHAR}, #{item.printTitle,jdbcType=VARCHAR}, #{item.isDefault,jdbcType=BIT}, 
        #{item.accountSetsId,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>