<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gson.financial.kernel.model.mapper.BankReceiptsMapper">
    <resultMap id="BaseResultMap" type="cn.gson.financial.kernel.model.entity.BankReceipts">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="receipts_no" jdbcType="VARCHAR" property="receiptsNo"/>
        <result column="receipts_date" jdbcType="DATE" property="receiptsDate"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="amount" jdbcType="DOUBLE" property="amount"/>
        <result column="summary" jdbcType="VARCHAR" property="summary"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="receipt_num" jdbcType="INTEGER" property="receiptNum"/>
        <result column="create_member" jdbcType="INTEGER" property="createMember"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId"/>
        <result column="receipts_year" jdbcType="INTEGER" property="receiptsYear"/>
        <result column="receipts_month" jdbcType="INTEGER" property="receiptsMonth"/>
        <result column="receipt_group_id" jdbcType="VARCHAR" property="receiptGroupId"/>
        <result column="bank_account" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod"/>
        <result column="file_path" jdbcType="VARCHAR" property="filePath"/>
        <result column="payee_name" jdbcType="VARCHAR" property="payeeName"/>
        <result column="payee_account" jdbcType="VARCHAR" property="payeeAccount"/>
        <result column="payee_bank" jdbcType="VARCHAR" property="payeeBank"/>
        <result column="payer_name" jdbcType="VARCHAR" property="payerName"/>
        <result column="payer_account" jdbcType="VARCHAR" property="payerAccount"/>
        <result column="payer_bank" jdbcType="VARCHAR" property="payerBank"/>
        <result column="transfer_date" jdbcType="DATE" property="transferDate"/>
        <result column="submission_date" jdbcType="DATE" property="submissionDate"/>
        <result column="serial_number" jdbcType="VARCHAR" property="serialNumber"/>
        <result column="payer_bank_code" jdbcType="VARCHAR" property="payerBankCode"/>
        <result column="payee_bank_code" jdbcType="VARCHAR" property="payeeBankCode"/>
        <result column="amount_in_words" jdbcType="VARCHAR" property="amountInWords"/>
        <result column="transaction_institution" jdbcType="VARCHAR" property="transactionInstitution"/>
        <result column="receipt_title" jdbcType="VARCHAR" property="receiptTitle"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        id, receipts_no, receipts_date, type, amount, summary, remark, receipt_num,
        create_member, create_date, account_sets_id, receipts_year, receipts_month, receipt_group_id,
        bank_account, payment_method, file_path, payee_name, payee_account, payee_bank,
        payer_name, payer_account, payer_bank, transfer_date, submission_date, serial_number,
        payer_bank_code, payee_bank_code, amount_in_words, transaction_institution, receipt_title, ocr_recognition_info
    </sql>
    
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into fxy_financial_bank_receipts
        (receipts_no, receipts_date, type, amount, summary, remark, receipt_num,
        create_member, create_date, account_sets_id, receipts_year, receipts_month, receipt_group_id,
        bank_account, payment_method, file_path, payee_name, payee_account, payee_bank,
        payer_name, payer_account, payer_bank, transfer_date, submission_date, serial_number,
        payer_bank_code, payee_bank_code, amount_in_words, transaction_institution, receipt_title, ocr_recognition_info)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.receiptsNo,jdbcType=VARCHAR}, #{item.receiptsDate,jdbcType=DATE},
            #{item.type,jdbcType=VARCHAR}, #{item.amount,jdbcType=DOUBLE},
            #{item.summary,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
            #{item.receiptNum,jdbcType=INTEGER}, #{item.createMember,jdbcType=INTEGER},
            #{item.createDate,jdbcType=TIMESTAMP}, #{item.accountSetsId,jdbcType=INTEGER},
            #{item.receiptsYear,jdbcType=INTEGER}, #{item.receiptsMonth,jdbcType=INTEGER},
            #{item.receiptGroupId,jdbcType=VARCHAR}, #{item.bankAccount,jdbcType=VARCHAR},
            #{item.paymentMethod,jdbcType=VARCHAR}, #{item.filePath,jdbcType=VARCHAR},
            #{item.payeeName,jdbcType=VARCHAR}, #{item.payeeAccount,jdbcType=VARCHAR},
            #{item.payeeBank,jdbcType=VARCHAR}, #{item.payerName,jdbcType=VARCHAR},
            #{item.payerAccount,jdbcType=VARCHAR}, #{item.payerBank,jdbcType=VARCHAR},
            #{item.transferDate,jdbcType=DATE}, #{item.submissionDate,jdbcType=DATE},
            #{item.serialNumber,jdbcType=VARCHAR}, #{item.payerBankCode,jdbcType=VARCHAR},
            #{item.payeeBankCode,jdbcType=VARCHAR}, #{item.amountInWords,jdbcType=VARCHAR},
            #{item.transactionInstitution,jdbcType=VARCHAR}, #{item.receiptTitle,jdbcType=VARCHAR},
            #{item.ocrRecognitionInfo,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>
    
    <select id="selectBankReceipts" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fxy_financial_bank_receipts
        ${ew.customSqlSegment}
    </select>
</mapper>
