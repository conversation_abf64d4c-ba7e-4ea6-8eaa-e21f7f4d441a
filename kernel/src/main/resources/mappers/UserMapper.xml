<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gson.financial.kernel.model.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="cn.gson.financial.kernel.model.entity.User">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="union_id" jdbcType="VARCHAR" property="unionId"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="avatar_url" jdbcType="VARCHAR" property="avatarUrl"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="init_password" jdbcType="VARCHAR" property="initPassword"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, mobile, `password`, union_id, open_id, nickname, avatar_url, real_name, account_sets_id,
        create_date, init_password, email
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into fxy_financial_user
        (mobile, `password`, union_id, open_id, nickname, avatar_url, real_name, account_sets_id,
        create_date, init_password, email)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.mobile,jdbcType=VARCHAR}, #{item.password,jdbcType=VARCHAR}, #{item.unionId,jdbcType=VARCHAR},
            #{item.openId,jdbcType=VARCHAR}, #{item.nickname,jdbcType=VARCHAR}, #{item.avatarUrl,jdbcType=VARCHAR},
            #{item.realName,jdbcType=VARCHAR}, #{item.accountSetsId,jdbcType=INTEGER}, #{item.createDate,jdbcType=TIMESTAMP},
            #{item.initPassword,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectByAccountSetId" resultType="cn.gson.financial.kernel.model.vo.UserVo">
        select ffu.*,ffuas.role_type as role
        from fxy_financial_user ffu
        left join fxy_financial_user_account_sets ffuas on ffu.id = ffuas.user_id
        where ffuas.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
    </select>
</mapper>