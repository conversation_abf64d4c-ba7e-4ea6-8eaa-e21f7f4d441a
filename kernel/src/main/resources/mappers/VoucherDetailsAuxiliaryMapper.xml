<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gson.financial.kernel.model.mapper.VoucherDetailsAuxiliaryMapper">
    <resultMap id="BaseResultMap" type="cn.gson.financial.kernel.model.entity.VoucherDetailsAuxiliary">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="voucher_details_id" jdbcType="INTEGER" property="voucherDetailsId"/>
        <result column="accounting_category_id" jdbcType="INTEGER" property="accountingCategoryId"/>
        <result column="accounting_category_details_id" jdbcType="INTEGER" property="accountingCategoryDetailsId"/>
        <association property="accountingCategoryDetails" javaType="cn.gson.financial.kernel.model.entity.AccountingCategoryDetails" column="accounting_category_details_id" select="cn.gson.financial.kernel.model.mapper.AccountingCategoryDetailsMapper.selectById"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, voucher_details_id, accounting_category_id, accounting_category_details_id
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into fxy_financial_voucher_details_auxiliary
        (voucher_details_id, accounting_category_id, accounting_category_details_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.voucherDetailsId,jdbcType=INTEGER}, #{item.accountingCategoryId,jdbcType=INTEGER},
            #{item.accountingCategoryDetailsId,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="selectByDetailsId" resultMap="BaseResultMap">
        select * from fxy_financial_voucher_details_auxiliary where voucher_details_id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByAccountBlock" resultMap="cn.gson.financial.kernel.model.mapper.AccountingCategoryDetailsMapper.BaseResultMap">
        select * from fxy_financial_accounting_category_details ffacd where ffacd.id in (
        select distinct ffvda.accounting_category_details_id
        from fxy_financial_voucher_details_auxiliary ffvda
        left join fxy_financial_voucher_details ffvd on ffvda.voucher_details_id = ffvd.id
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        where ffv.account_sets_id=#{accountSetsId,jdbcType=INTEGER} and ffvda.accounting_category_id = #{auxiliaryId,jdbcType=INTEGER})
    </select>

    <select id="selectAccountBookDetails" resultType="cn.gson.financial.kernel.model.vo.VoucherDetailVo">
        select ffv.voucher_date,
        ffv.word,
        ffv.code,
        ffvd.voucher_id,
        ffvd.summary,
        ffvd.debit_amount,
        ffvd.credit_amount,
        ffs.balance_direction
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
        left join fxy_financial_voucher_details_auxiliary ffvda on ffvd.id = ffvda.voucher_details_id
        where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffvda.accounting_category_id = #{auxiliaryId,jdbcType=INTEGER}
        and ffvda.accounting_category_details_id = #{auxiliaryItemId,jdbcType=INTEGER,jdbcType=INTEGER}
        and date_format(ffv.voucher_date, '%Y%m') = date_format(#{accountDate}, '%Y%m')
        order by ffvd.voucher_id
    </select>

    <select id="selectAccountBookStatistical" resultType="cn.gson.financial.kernel.model.vo.VoucherDetailVo">
        select
        <if test="auxiliaryItemId == null">
            ffvda.accounting_category_details_id detailsId,
        </if>
        sum(ffvd.debit_amount) debit_amount,
        sum(ffvd.credit_amount) credit_amount,
        ffs.balance_direction
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
        left join fxy_financial_voucher_details_auxiliary ffvda on ffvd.id = ffvda.voucher_details_id
        where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffvd.voucher_id is not null
        and ffvda.accounting_category_id = #{auxiliaryId,jdbcType=INTEGER}
        <if test="auxiliaryItemId != null">
            and ffvda.accounting_category_details_id = #{auxiliaryItemId,jdbcType=INTEGER,jdbcType=INTEGER}
        </if>
        <if test="startDate != null and endDate != null">
            and ffv.voucher_date between #{startDate,jdbcType=TIMESTAMP} and #{endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="startDate != null and endDate == null">
            and ffv.voucher_date >= #{startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="startDate == null and endDate != null">
            and ffv.voucher_date <![CDATA[< ]]> #{endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="auxiliaryItemId == null">
            group by ffvda.accounting_category_details_id
        </if>
    </select>

</mapper>