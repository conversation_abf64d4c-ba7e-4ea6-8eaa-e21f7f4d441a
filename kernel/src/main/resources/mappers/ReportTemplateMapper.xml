<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gson.financial.kernel.model.mapper.ReportTemplateMapper">
    <resultMap id="BaseResultMap" type="cn.gson.financial.kernel.model.entity.ReportTemplate">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId"/>
        <result column="template_key" jdbcType="VARCHAR" property="templateKey"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, account_sets_id, template_key, `type`
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into fxy_financial_report_template
        (`name`, account_sets_id, template_key, `type`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.name,jdbcType=VARCHAR}, #{item.accountSetsId,jdbcType=INTEGER}, #{item.templateKey,jdbcType=VARCHAR},
            #{item.type,jdbcType=INTEGER})
        </foreach>
    </insert>
</mapper>