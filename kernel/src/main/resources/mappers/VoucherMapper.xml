<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gson.financial.kernel.model.mapper.VoucherMapper">
    <resultMap id="BaseResultMap" type="cn.gson.financial.kernel.model.entity.Voucher">
        <!--@mbg.generated-->
        <!--@Table fxy_financial_voucher-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="word" jdbcType="VARCHAR" property="word"/>
        <result column="code" jdbcType="INTEGER" property="code"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="receipt_num" jdbcType="INTEGER" property="receiptNum"/>
        <result column="create_member" jdbcType="INTEGER" property="createMember"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="debit_amount" jdbcType="DOUBLE" property="debitAmount"/>
        <result column="credit_amount" jdbcType="DOUBLE" property="creditAmount"/>
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId"/>
        <result column="voucher_year" jdbcType="INTEGER" property="voucherYear"/>
        <result column="voucher_month" jdbcType="INTEGER" property="voucherMonth"/>
        <result column="voucher_date" jdbcType="DATE" property="voucherDate"/>
        <result column="audit_member_id" jdbcType="INTEGER" property="auditMemberId"/>
        <result column="audit_member_name" jdbcType="VARCHAR" property="auditMemberName"/>
        <result column="audit_date" jdbcType="TIMESTAMP" property="auditDate"/>
        <result column="carry_forward" jdbcType="BIT" property="carryForward"/>
        <collection property="details" column="id" select="selectVoucherDetails"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, word, code, remark, receipt_num, create_member, create_date, debit_amount, credit_amount,
        account_sets_id, voucher_year, voucher_month, voucher_date, audit_member_id, audit_member_name,
        audit_date, carry_forward
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into fxy_financial_voucher
        (word, code, remark, receipt_num, create_member, create_date, debit_amount, credit_amount,
        account_sets_id, voucher_year, voucher_month, voucher_date, audit_member_id, audit_member_name,
        audit_date, carry_forward)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.word,jdbcType=VARCHAR}, #{item.code,jdbcType=INTEGER}, #{item.remark,jdbcType=VARCHAR},
            #{item.receiptNum,jdbcType=INTEGER}, #{item.createMember,jdbcType=INTEGER}, #{item.createDate,jdbcType=TIMESTAMP},
            #{item.debitAmount,jdbcType=DOUBLE}, #{item.creditAmount,jdbcType=DOUBLE}, #{item.accountSetsId,jdbcType=INTEGER},
            #{item.voucherYear,jdbcType=INTEGER}, #{item.voucherMonth,jdbcType=INTEGER}, #{item.voucherDate,jdbcType=DATE},
            #{item.auditMemberId,jdbcType=INTEGER}, #{item.auditMemberName,jdbcType=VARCHAR},
            #{item.auditDate,jdbcType=TIMESTAMP}, #{item.carryForward,jdbcType=BIT})
        </foreach>
    </insert>

    <select id="selectMaxCode" resultType="java.lang.Integer">
        select max(code) + 1 as code
        from fxy_financial_voucher ffv
        where account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and word = #{word,jdbcType=VARCHAR}
        and voucher_year = #{year}
        and voucher_month = #{month}
    </select>
    <select id="selectVoucher" resultMap="BaseResultMap">
        select *
        from fxy_financial_voucher ${ew.customSqlSegment}
        order by word, code
    </select>

    <select id="selectVoucherDetails" resultMap="cn.gson.financial.kernel.model.mapper.VoucherDetailsMapper.BaseResultMap">
        select *
        from fxy_financial_voucher_details
        where voucher_id = #{id}
    </select>

    <select id="selectAccountBookDetails" resultType="cn.gson.financial.kernel.model.vo.VoucherDetailVo">
        select ffv.voucher_date,
        ffv.word,
        ffv.code,
        ffvd.voucher_id,
        <if test="subjectIds != null and subjectIds.size() == 1">
            concat(ffs.code,'-',ffs.name) subject_name,
        </if>
        <if test="subjectIds != null and subjectIds.size() &gt; 1">
            ffvd.subject_name,
        </if>
        ffvd.summary,
        ffvd.debit_amount,
        ffvd.credit_amount,
        ffvd.price,
        ffvd.num,
        ffs.balance_direction
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
        where ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffvd.subject_id in
        <foreach close=")" collection="subjectIds" index="index" item="id" open="(" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        and date_format(ffv.voucher_date, '%Y%m') = date_format(#{accountDate}, '%Y%m')
        order by ffv.code asc
    </select>

    <select id="selectAccountBookStatistical" resultType="cn.gson.financial.kernel.model.vo.VoucherDetailVo">
        select
        (SELECT concat( tffs.CODE, '-', tffs.NAME ) FROM fxy_financial_subject tffs where tffs.id=#{subjectId,jdbcType=INTEGER}) subject_name,
        '本期合计' as summary,
        sum(ffvd.debit_amount) debit_amount,
        sum(ffvd.credit_amount) credit_amount,
        (SELECT balance_direction FROM fxy_financial_subject WHERE id=#{subjectId,jdbcType=INTEGER}) balance_direction,
        sum(ffvd.num) num,
        avg(ffvd.price) price
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
        where ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffvd.subject_id in
        <foreach close=")" collection="subjectIds" index="index" item="id" open="(" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        and date_format(ffv.voucher_date, '%Y%m') = date_format(#{accountDate}, '%Y%m')
        group by date_format( ffv.voucher_date, '%Y%m' )
        union
        select
        (SELECT concat( tffs.CODE, '-', tffs.NAME ) FROM fxy_financial_subject tffs where tffs.id=#{subjectId,jdbcType=INTEGER}) subject_name,
        '本年累计' as summary,
        sum(ffvd.debit_amount) debit_amount,
        sum(ffvd.credit_amount) credit_amount,
        (SELECT balance_direction FROM fxy_financial_subject WHERE id=#{subjectId,jdbcType=INTEGER}) balance_direction,
        sum(ffvd.num) num,
        avg(ffvd.price) price
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
        where ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffvd.subject_id in
        <foreach close=")" collection="subjectIds" index="index" item="id" open="(" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        AND ffv.voucher_date between concat(date_format(#{accountDate}, '%Y'),'-01-01') and #{accountDate,jdbcType=TIMESTAMP}
        group by date_format( ffv.voucher_date, '%Y' )
    </select>

    <!--科目期初-->
    <select id="selectAccountBookInitialBalance" resultType="cn.gson.financial.kernel.model.vo.VoucherDetailVo">
        select
        concat(ffs.code,'-',ffs.name) subject_name,
        '期初余额' summary,
        sum(ffvd.credit_amount) credit_amount,
        sum(ffvd.debit_amount) debit_amount ,
        ffs.balance_direction,
        sum(ifnull(IF(ffs.balance_direction = '借', ffvd.debit_amount, ffvd.credit_amount),0))-sum(ifnull(IF(ffs.balance_direction = '借', ffvd.credit_amount, ffvd.debit_amount),0)) balance
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffv.id = ffvd.voucher_id
        left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
        where ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffvd.subject_id in
        <foreach close=")" collection="subjectIds" index="index" item="id" open="(" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        and (ffvd.voucher_id is null or ffv.voucher_date <![CDATA[< ]]>#{accountDate,jdbcType=TIMESTAMP})
        and (ffvd.debit_amount is not null or ffvd.credit_amount is not null)
        group by ffs.id, ffs.code, ffs.name, ffs.balance_direction
    </select>

    <!--科目汇总详情-->
    <select id="selectSubjectDetail" resultType="cn.gson.financial.kernel.model.vo.VoucherDetailVo">
        <choose>
            <when test="showNumPrice">
                select ffvd.subject_id, sum(ffvd.credit_amount) credit_amount, sum(ffvd.debit_amount) debit_amount, sum(ffvd.num) num, avg(ffvd.price) price
                FROM fxy_financial_voucher_details ffvd
                LEFT JOIN fxy_financial_voucher ffv
                ON ffvd.voucher_id = ffv.id
                WHERE ffvd.subject_id IN
                <foreach close=")" collection="subjectIds" index="index" item="id" open="(" separator=",">
                    #{id,jdbcType=INTEGER}
                </foreach>
                AND ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
                <choose>
                    <when test="monthBegin != null and monthEnd != null">
                        AND ffv.voucher_date BETWEEN #{monthBegin,jdbcType=TIMESTAMP} AND #{monthEnd,jdbcType=TIMESTAMP}
                    </when>
                    <otherwise>
                        AND ffv.voucher_date <![CDATA[< ]]>#{monthBegin,jdbcType=TIMESTAMP}
                    </otherwise>
                </choose>
                <if test="monthEnd == null">
                    OR (ffvd.voucher_id is null and (ffvd.debit_amount is not null or ffvd.credit_amount is not null) and ffvd.subject_id IN
                    <foreach close=")" collection="subjectIds" index="index" item="id" open="(" separator=",">
                        #{id,jdbcType=INTEGER}
                    </foreach>)
                </if>
                group by ffvd.subject_id;
            </when>
            <otherwise>
                select ffvd.subject_id, sum(ffvd.credit_amount) credit_amount, sum(ffvd.debit_amount) debit_amount, sum(ffvd.num) num, avg(ffvd.price) price
                FROM fxy_financial_voucher_details ffvd
                LEFT JOIN fxy_financial_voucher ffv
                ON ffvd.voucher_id = ffv.id
                WHERE ffvd.subject_id IN
                <foreach close=")" collection="subjectIds" index="index" item="id" open="(" separator=",">
                    #{id,jdbcType=INTEGER}
                </foreach>
                AND ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
                <choose>
                    <when test="monthBegin != null and monthEnd != null">
                        AND ffv.voucher_date BETWEEN #{monthBegin,jdbcType=TIMESTAMP} AND #{monthEnd,jdbcType=TIMESTAMP}
                    </when>
                    <otherwise>
                        AND ffv.voucher_date <![CDATA[< ]]>#{monthBegin,jdbcType=TIMESTAMP}
                    </otherwise>
                </choose>
                <if test="monthEnd == null">
                    OR (ffvd.voucher_id is null and (ffvd.debit_amount is not null or ffvd.credit_amount is not null) and ffvd.subject_id IN
                    <foreach close=")" collection="subjectIds" index="index" item="id" open="(" separator=",">
                        #{id,jdbcType=INTEGER}
                    </foreach>)
                </if>
                group by ffvd.subject_id;
            </otherwise>
        </choose>
    </select>

    <select id="selectBrokenData" resultType="java.util.Map">
        select word, count(1) total, max(code) code
        from fxy_financial_voucher ffv
        where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and voucher_year = #{year,jdbcType=INTEGER}
        and voucher_month = #{month,jdbcType=INTEGER}
        group by word;
    </select>

    <select id="selectReportStatistical" resultType="cn.gson.financial.kernel.model.vo.VoucherDetailVo">
        <foreach close="" collection="codes" index="index" item="code" open="" separator="union">
            select
            '${code}' subject_code,
            ffs.balance_direction,
            '本期' as summary,
            sum(ffvd.debit_amount) debit_amount,
            sum(ffvd.credit_amount) credit_amount,
            sum(ffvd.num) num,
            avg(ffvd.price) price
            from fxy_financial_voucher_details ffvd
            left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
            left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
            where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffvd.carry_forward = 0
            and ffvd.subject_code like '${code}%'
            and date_format(ffv.voucher_date, '%Y%m') = date_format(#{accountDate}, '%Y%m')
            union
            select
            '${code}' subject_code,
            ffs.balance_direction,
            '本年' as summary,
            sum(ffvd.debit_amount) debit_amount,
            sum(ffvd.credit_amount) credit_amount,
            sum(ffvd.num) num,
            avg(ffvd.price) price
            from fxy_financial_voucher_details ffvd
            left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
            left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
            where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffvd.carry_forward = 0
            and ffvd.subject_code like '${code}%'
            and date_format(ffv.voucher_date, '%Y') = date_format(#{accountDate}, '%Y') and ffv.voucher_date <![CDATA[<=]]> #{accountDate,jdbcType=TIMESTAMP}
        </foreach>
    </select>

    <!--首页图表数据-->
    <select id="selectHomeReport" resultType="java.util.Map">
        select '本年利润' type,ffv.voucher_year,ffv.voucher_month, sum(ffvd.debit_amount) debit_amount, sum(ffvd.credit_amount) credit_amount
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        where ffvd.subject_id in (select id from fxy_financial_subject ffs where ffs.code like '3103%')
        and ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffv.voucher_year = #{voucherYear,jdbcType=INTEGER}
        group by ffv.voucher_month
        union
        select '主营业务收入' type,ffv.voucher_year,ffv.voucher_month, sum(ffvd.debit_amount) debit_amount, sum(ffvd.credit_amount) credit_amount
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        where ffvd.subject_id in (select id from fxy_financial_subject ffs where ffs.code like '5001%')
        and ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffv.voucher_year = #{voucherYear,jdbcType=INTEGER}
        group by ffv.voucher_month
    </select>

    <select id="selectHomeCostReport" resultType="java.util.Map">
        select '销售费用' type,ffv.voucher_year,ffv.voucher_month, sum(ffvd.debit_amount) debit_amount, sum(ffvd.credit_amount) credit_amount
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        where ffvd.subject_id in (select id from fxy_financial_subject ffs where ffs.code like '5601%')
        and ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffv.voucher_year = #{voucherYear,jdbcType=INTEGER}
        and ffv.voucher_month = #{voucherMonth,jdbcType=INTEGER}
        union
        select '管理费用' type,ffv.voucher_year,ffv.voucher_month, sum(ffvd.debit_amount) debit_amount, sum(ffvd.credit_amount) credit_amount
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        where ffvd.subject_id in (select id from fxy_financial_subject ffs where ffs.code like '5602%')
        and ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffv.voucher_year = #{voucherYear,jdbcType=INTEGER}
        and ffv.voucher_month = #{voucherMonth,jdbcType=INTEGER}
        union
        select '财务费用' type,ffv.voucher_year,ffv.voucher_month, sum(ffvd.debit_amount) debit_amount, sum(ffvd.credit_amount) credit_amount
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        where ffvd.subject_id in (select id from fxy_financial_subject ffs where ffs.code like '5603%')
        and ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffv.voucher_year = #{voucherYear,jdbcType=INTEGER}
        and ffv.voucher_month = #{voucherMonth,jdbcType=INTEGER}
    </select>

    <select id="selectHomeCashReport" resultType="java.util.Map">
        select '库存现金' type,ffv.voucher_year,ffv.voucher_month, sum(ffvd.debit_amount) debit_amount, sum(ffvd.credit_amount) credit_amount
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        where ffvd.subject_id in (select id from fxy_financial_subject ffs where ffs.code like '1001%')
        and ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffv.voucher_year = #{voucherYear,jdbcType=INTEGER}
        and ffv.voucher_month = #{voucherMonth,jdbcType=INTEGER}
        union
        select '银行存款' type,ffv.voucher_year,ffv.voucher_month, sum(ffvd.debit_amount) debit_amount, sum(ffvd.credit_amount) credit_amount
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        where ffvd.subject_id in (select id from fxy_financial_subject ffs where ffs.code like '1002%')
        and ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffv.voucher_year = #{voucherYear,jdbcType=INTEGER}
        and ffv.voucher_month = #{voucherMonth,jdbcType=INTEGER}
        union
        select '其他货币资金' type,ffv.voucher_year,ffv.voucher_month, sum(ffvd.debit_amount) debit_amount, sum(ffvd.credit_amount) credit_amount
        from fxy_financial_voucher_details ffvd
        left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
        where ffvd.subject_id in (select id from fxy_financial_subject ffs where ffs.code like '1012%')
        and ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffv.voucher_year = #{voucherYear,jdbcType=INTEGER}
        and ffv.voucher_month = #{voucherMonth,jdbcType=INTEGER}
    </select>

    <select id="selectBeforeId" resultType="java.lang.Integer">
        select id
        from fxy_financial_voucher ffv
        where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        <if test="currentId != null">
            and
            ffv.id <![CDATA[<]]> #{currentId,jdbcType=INTEGER}
        </if>
        order by id desc
        limit 1
    </select>

    <select id="selectNextId" resultType="java.lang.Integer">
        select id
        from fxy_financial_voucher ffv
        where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        <if test="currentId != null">
            and
            ffv.id <![CDATA[>]]> #{currentId,jdbcType=INTEGER}
        </if>
        order by id asc
        limit 1
    </select>

    <!--反更新-->
    <update id="updateAudit">
        update fxy_financial_voucher set audit_member_id = null,audit_member_name = null,audit_date = null ${ew.customSqlSegment}
    </update>

    <select id="selectReportBalanceStatistical" resultType="cn.gson.financial.kernel.model.vo.VoucherDetailVo">
        <foreach close="" collection="codes" index="index" item="code" open="" separator="union">
            select
            '${code}' subject_code,
            ffs.balance_direction,
            '本期' as summary,
            sum(ffvd.debit_amount) debit_amount,
            sum(ffvd.credit_amount) credit_amount,
            sum(ffvd.num) num,
            avg(ffvd.price) price
            from fxy_financial_voucher_details ffvd
            left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
            left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
            where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
            and ffvd.subject_code like '${code}%'
            and ffv.voucher_date <![CDATA[<= ]]> #{accountDate}
            union
            select
            '${code}' subject_code,
            ffs.balance_direction,
            '本年' as summary,
            sum(ffvd.debit_amount) debit_amount,
            sum(ffvd.credit_amount) credit_amount,
            sum(ffvd.num) num,
            avg(ffvd.price) price
            from fxy_financial_voucher_details ffvd
            left join fxy_financial_voucher ffv on ffvd.voucher_id = ffv.id
            left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
            where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
            and ffvd.subject_code like '${code}%'
            and ffv.voucher_date <![CDATA[< ]]> concat(date_format(#{accountDate}, '%Y'),'-01-01')
        </foreach>
    </select>
    <select id="selectReportInitBalance" resultType="cn.gson.financial.kernel.model.vo.VoucherDetailVo">
        <foreach close="" collection="codes" index="index" item="code" open="" separator="union">
            select
            '${code}' subject_code,
            ffs.balance_direction,
            '本年' as summary,
            sum(IFNULL(IF(ffs.balance_direction = '借', ffvd.debit_amount, ffvd.credit_amount),0)) + sum(ifnull(ffvd.cumulative_debit,0)) - sum(ifnull(ffvd.cumulative_credit,0)) debit_amount,
            sum(ifnull(ffvd.cumulative_credit_num,0)) num
            from fxy_financial_voucher_details ffvd
            left join fxy_financial_subject ffs on ffvd.subject_id = ffs.id
            where
            ffvd.voucher_id is null
            and ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
            and ffvd.subject_code like '${code}%'
        </foreach>
    </select>

    <select id="selectMaxVoucherDate" resultType="java.util.Date">
        select max(ffv.voucher_date) date from fxy_financial_voucher ffv where account_sets_id = #{accountSetsId,jdbcType=INTEGER} limit 1
    </select>
</mapper>