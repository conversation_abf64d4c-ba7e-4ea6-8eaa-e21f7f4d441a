<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gson.financial.kernel.model.mapper.CheckoutMapper">
    <resultMap id="BaseResultMap" type="cn.gson.financial.kernel.model.entity.Checkout">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId"/>
        <result column="check_year" jdbcType="INTEGER" property="checkYear"/>
        <result column="check_month" jdbcType="INTEGER" property="checkMonth"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="check_date" jdbcType="DATE" property="checkDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, account_sets_id, check_year, check_month, `status`, check_date
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into fxy_financial_checkout
        (account_sets_id, check_year, check_month, `status`, check_date)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountSetsId,jdbcType=INTEGER}, #{item.checkYear,jdbcType=INTEGER}, #{item.checkMonth,jdbcType=INTEGER},
            #{item.status,jdbcType=INTEGER}, #{item.checkDate,jdbcType=DATE})
        </foreach>
    </insert>
</mapper>