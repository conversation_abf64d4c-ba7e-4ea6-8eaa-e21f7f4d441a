<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gson.financial.kernel.model.mapper.BillMapper">
    <resultMap id="BaseResultMap" type="cn.gson.financial.kernel.model.entity.Bill">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="bill_no" jdbcType="VARCHAR" property="billNo"/>
        <result column="bill_date" jdbcType="DATE" property="billDate"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="amount" jdbcType="DOUBLE" property="amount"/>
        <result column="issuer" jdbcType="VARCHAR" property="issuer"/>
        <result column="recipient" jdbcType="VARCHAR" property="recipient"/>
        <result column="summary" jdbcType="VARCHAR" property="summary"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <!-- related_voucher_id 字段已删除，使用 entity_relations 表管理关联关系 -->
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId"/>
        <result column="bill_year" jdbcType="INTEGER" property="billYear"/>
        <result column="bill_month" jdbcType="INTEGER" property="billMonth"/>
        <result column="attachment_path" jdbcType="VARCHAR" property="attachmentPath"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="invoice_number" jdbcType="VARCHAR" property="invoiceNumber"/>
        <result column="ocr_recognition_info" jdbcType="LONGVARCHAR" property="ocrRecognitionInfo"/>
    </resultMap>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into fxy_financial_bill
        (bill_no, bill_date, type, amount, issuer, recipient, summary, remark, status,
         doc_group_id, account_sets_id, bill_year, bill_month, attachment_path,
         create_time, update_time, invoice_number, ocr_recognition_info)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.billNo,jdbcType=VARCHAR}, #{item.billDate,jdbcType=DATE}, 
             #{item.type,jdbcType=VARCHAR}, #{item.amount,jdbcType=DOUBLE}, 
             #{item.issuer,jdbcType=VARCHAR}, #{item.recipient,jdbcType=VARCHAR}, 
             #{item.summary,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
             #{item.status,jdbcType=VARCHAR}, #{item.docGroupId,jdbcType=VARCHAR},
             #{item.accountSetsId,jdbcType=INTEGER}, #{item.billYear,jdbcType=INTEGER},
             #{item.billMonth,jdbcType=INTEGER}, #{item.attachmentPath,jdbcType=VARCHAR},
             #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
             #{item.invoiceNumber,jdbcType=VARCHAR}, #{item.ocrRecognitionInfo,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>
</mapper>