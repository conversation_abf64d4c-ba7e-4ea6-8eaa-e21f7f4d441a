<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.gson.financial.kernel.model.mapper.AiMatchingHistoryMapper">

    <select id="findHistoryByPage" resultType="cn.gson.financial.kernel.model.entity.AiMatchingHistory">
        SELECT
            *
        FROM
            fxy_financial_ai_matching_history
        WHERE
            account_sets_id = #{accountSetsId}
            <if test="businessType != null and businessType != ''">
                AND business_type = #{businessType}
            </if>
            <if test="startDate != null">
                AND create_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND create_time <= #{endDate}
            </if>
        ORDER BY
            create_time DESC
    </select>

</mapper>
