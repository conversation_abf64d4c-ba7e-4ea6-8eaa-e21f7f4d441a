<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gson.financial.kernel.model.mapper.FinancialAiConfigMapper">

    <!-- 批量插入或更新配置 -->
    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        INSERT INTO fxy_financial_ai_config 
        (account_sets_id, config_key, config_value, description, is_active, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.accountSetsId}, #{item.configKey}, #{item.configValue}, #{item.description}, 
             #{item.isActive}, #{item.createTime}, #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        config_value = VALUES(config_value),
        description = VALUES(description),
        is_active = VALUES(is_active),
        update_time = VALUES(update_time)
    </insert>

</mapper>
