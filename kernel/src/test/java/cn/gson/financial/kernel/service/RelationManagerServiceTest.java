package cn.gson.financial.kernel.service;

import cn.gson.financial.kernel.model.dto.RelationCreateDto;
import cn.gson.financial.kernel.model.entity.EntityRelation;
import cn.gson.financial.kernel.model.mapper.EntityRelationMapper;
import cn.gson.financial.kernel.service.impl.RelationManagerServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 关联管理服务测试
 */
@ExtendWith(MockitoExtension.class)
public class RelationManagerServiceTest {

    @Mock
    private EntityRelationMapper entityRelationMapper;

    @InjectMocks
    private RelationManagerServiceImpl relationManagerService;

    @Test
    public void testCreateRelation() {
        // 准备测试数据
        RelationCreateDto createDto = new RelationCreateDto();
        createDto.setSourceType("DOCUMENT");
        createDto.setSourceId("1");
        createDto.setTargetType("RECEIPT");
        createDto.setTargetId("1");
        createDto.setRelationType("ASSOCIATED");
        createDto.setRelationAmount(BigDecimal.valueOf(1000.00));

        // 模拟数据库操作
        when(entityRelationMapper.checkRelationExists(anyString(), anyString(), anyString(), anyString(), anyInt()))
                .thenReturn(0);
        when(entityRelationMapper.insert(any(EntityRelation.class))).thenAnswer(invocation -> {
            EntityRelation relation = invocation.getArgument(0);
            relation.setRelationId("test-relation-id-" + System.currentTimeMillis());
            return 1;
        });

        // 执行测试
        String relationId = relationManagerService.createRelation(1, createDto, 1);

        // 验证结果
        assertNotNull(relationId);
        verify(entityRelationMapper, times(1)).insert(any(EntityRelation.class));
    }

    @Test
    public void testCreateRelationAlreadyExists() {
        // 准备测试数据
        RelationCreateDto createDto = new RelationCreateDto();
        createDto.setSourceType("DOCUMENT");
        createDto.setSourceId("1");
        createDto.setTargetType("RECEIPT");
        createDto.setTargetId("1");

        // 模拟关联关系已存在
        when(entityRelationMapper.checkRelationExists(anyString(), anyString(), anyString(), anyString(), anyInt()))
                .thenReturn(1);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            relationManagerService.createRelation(1, createDto, 1);
        });
    }

    @Test
    public void testQueryDocumentRelations() {
        // 准备测试数据
        EntityRelation relation1 = new EntityRelation();
        relation1.setRelationId("relation-1");
        relation1.setSourceType("DOCUMENT");
        relation1.setTargetType("RECEIPT");

        EntityRelation relation2 = new EntityRelation();
        relation2.setRelationId("relation-2");
        relation2.setSourceType("DOCUMENT");
        relation2.setTargetType("DOCUMENT_GROUP");

        List<EntityRelation> mockRelations = Arrays.asList(relation1, relation2);

        // 模拟数据库查询
        when(entityRelationMapper.findAllRelationsByEntity(anyString(), anyString(), anyInt()))
                .thenReturn(mockRelations);

        // 执行测试
        List<EntityRelation> relations = relationManagerService.queryDocumentRelations(1, "1", "DOCUMENT");

        // 验证结果
        assertNotNull(relations);
        assertEquals(2, relations.size());
        verify(entityRelationMapper, times(1)).findAllRelationsByEntity("DOCUMENT", "1", 1);
    }

    @Test
    public void testQueryDocumentRelationsWithInvalidType() {
        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            relationManagerService.queryDocumentRelations(1, "1", "RECEIPT");
        });
    }

    @Test
    public void testValidateRelation() {
        // 测试有效的关联关系
        boolean valid = relationManagerService.validateRelation(1, "DOCUMENT", "1", "RECEIPT", "1");
        assertTrue(valid);

        // 测试无效的关联关系（自己关联自己）
        boolean invalid = relationManagerService.validateRelation(1, "DOCUMENT", "1", "DOCUMENT", "1");
        assertFalse(invalid);

        // 测试无效的关联关系（空参数）
        boolean invalidNull = relationManagerService.validateRelation(1, null, "1", "RECEIPT", "1");
        assertFalse(invalidNull);
    }

    @Test
    public void testDeleteRelation() {
        // 准备测试数据
        EntityRelation relation = new EntityRelation();
        relation.setRelationId("test-relation-id");
        relation.setAccountSetsId(1);

        // 模拟数据库操作
        when(entityRelationMapper.selectById(anyString())).thenReturn(relation);
        when(entityRelationMapper.deleteById(anyString())).thenReturn(1);

        // 执行测试
        boolean result = relationManagerService.deleteRelation(1, "test-relation-id");

        // 验证结果
        assertTrue(result);
        verify(entityRelationMapper, times(1)).deleteById("test-relation-id");
    }

    @Test
    public void testDeleteRelationNotExists() {
        // 模拟关联关系不存在
        when(entityRelationMapper.selectById(anyString())).thenReturn(null);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            relationManagerService.deleteRelation(1, "non-existent-id");
        });
    }

    @Test
    public void testDeleteRelationWrongAccountSets() {
        // 准备测试数据
        EntityRelation relation = new EntityRelation();
        relation.setRelationId("test-relation-id");
        relation.setAccountSetsId(2); // 不同的账套ID

        // 模拟数据库操作
        when(entityRelationMapper.selectById(anyString())).thenReturn(relation);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            relationManagerService.deleteRelation(1, "test-relation-id");
        });
    }
}
