package cn.gson.financial.kernel.service.impl;

import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.service.BillService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * BillServiceImpl 测试类
 * 主要测试票据号生成逻辑的容错性
 */
@SpringJUnitConfig
public class BillServiceImplTest {

    @Test
    public void testBillNoParsingLogic() {
        // 测试不同格式的票据号解析逻辑
        
        // 测试标准格式
        String standardFormat = "PJ202501-0001";
        String result1 = extractNumberFromBillNo(standardFormat);
        assertEquals("0001", result1);
        
        // 测试旧格式
        String oldFormat = "XS202506003";
        String result2 = extractNumberFromBillNo(oldFormat);
        assertEquals("003", result2);
        
        // 测试其他格式
        String otherFormat = "ABC123456";
        String result3 = extractNumberFromBillNo(otherFormat);
        assertEquals("123456", result3);
        
        System.out.println("所有票据号格式解析测试通过");
    }
    
    /**
     * 模拟票据号解析逻辑（从实际代码中提取）
     */
    private String extractNumberFromBillNo(String billNo) {
        try {
            String lastNoStr;
            
            // 检查票据号格式，支持多种格式
            if (billNo.contains("-")) {
                // 标准格式：PJ202501-0001
                lastNoStr = billNo.substring(billNo.lastIndexOf("-") + 1);
            } else if (billNo.matches("^[A-Z]+\\d+$")) {
                // 旧格式：XS202506003，提取末尾数字部分
                lastNoStr = billNo.replaceAll("^[A-Z]+", "");
                // 如果是年月+序号格式，只取最后3位作为序号
                if (lastNoStr.length() > 3) {
                    lastNoStr = lastNoStr.substring(lastNoStr.length() - 3);
                }
            } else {
                // 其他格式，尝试提取末尾的数字
                lastNoStr = billNo.replaceAll(".*?(\\d+)$", "$1");
            }
            
            return lastNoStr;
        } catch (Exception e) {
            return "1"; // 默认值
        }
    }
    
    @Test
    public void testNumberParsing() {
        // 测试数字解析
        try {
            int num1 = Integer.parseInt("0001");
            assertEquals(1, num1);
            
            int num2 = Integer.parseInt("003");
            assertEquals(3, num2);
            
            int num3 = Integer.parseInt("123456");
            assertEquals(123456, num3);
            
            System.out.println("数字解析测试通过");
        } catch (NumberFormatException e) {
            fail("数字解析失败: " + e.getMessage());
        }
    }
}
