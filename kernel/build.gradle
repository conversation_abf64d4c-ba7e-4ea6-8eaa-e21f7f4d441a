plugins {
    id 'java'
}

group 'cn.gson'
version '0.1'

sourceCompatibility = 1.8



repositories {
    maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web:2.7.18'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa:2.7.18'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis:2.7.18'
    implementation("com.aliyun:aliyun-java-sdk-dysmsapi:1.1.0")
    implementation("com.aliyun:aliyun-java-sdk-dm:3.3.1")
    implementation("com.aliyun.oss:aliyun-sdk-oss:3.4.0")
    implementation("com.belerweb:pinyin4j:2.5.1")
    implementation 'com.baomidou:mybatis-plus-boot-starter:3.2.0'
    implementation('com.alibaba:fastjson:1.2.51')
    compileOnly 'org.projectlombok:lombok:1.18.20'
    annotationProcessor 'org.projectlombok:lombok:1.18.20'

    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test:2.7.18'
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.2'
    testImplementation 'org.junit.jupiter:junit-jupiter-engine:5.8.2'
    testImplementation 'org.mockito:mockito-core:4.6.1'
    testImplementation 'org.mockito:mockito-junit-jupiter:4.6.1'
    testImplementation 'org.mockito:mockito-inline:4.6.1'
}

tasks.named('test') {
    useJUnitPlatform()
}
