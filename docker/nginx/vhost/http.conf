server_names_hash_bucket_size 128;
client_header_buffer_size 32k;
large_client_header_buffers 4 32k;
client_max_body_size 1024m;
client_body_buffer_size 10m;
tcp_nopush on;
server_tokens off;
tcp_nodelay on;

fastcgi_connect_timeout 300;
fastcgi_send_timeout 300;
fastcgi_read_timeout 300;
fastcgi_buffer_size 64k;
fastcgi_buffers 4 64k;
fastcgi_busy_buffers_size 128k;
fastcgi_temp_file_write_size 128k;
fastcgi_intercept_errors on;

#Gzip Compression
gzip on;
gzip_buffers 16 8k;
gzip_comp_level 6;
gzip_http_version 1.1;
gzip_min_length 256;
gzip_proxied any;
gzip_vary on;
gzip_types
text/xml application/xml application/atom+xml application/rss+xml application/xhtml+xml image/svg+xml
text/javascript application/javascript application/x-javascript
text/x-json application/json application/x-web-app-manifest+json
text/css text/plain text/x-component
font/opentype application/x-font-ttf application/vnd.ms-fontobject
image/x-icon;
gzip_disable "MSIE [1-6]\.(?!.*SV1)";

log_format json escape=json '{"@timestamp":"$time_iso8601",'
                  '"server_addr":"$server_addr",'
                  '"remote_addr":"$remote_addr",'
                  '"scheme":"$scheme",'
                  '"request_method":"$request_method",'
                  '"request_uri": "$request_uri",'
                  '"request_length": "$request_length",'
                  '"uri": "$uri", '
                  '"request_time":$request_time,'
                  '"body_bytes_sent":$body_bytes_sent,'
                  '"bytes_sent":$bytes_sent,'
                  '"status":"$status",'
                  '"upstream_time":"$upstream_response_time",'
                  '"upstream_host":"$upstream_addr",'
                  '"upstream_status":"$upstream_status",'
                  '"host":"$host",'
                  '"http_referer":"$http_referer",'
                  '"http_user_agent":"$http_user_agent"'
                  '}';
