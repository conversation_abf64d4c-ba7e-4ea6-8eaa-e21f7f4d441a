server {
  listen 80;
  listen [::]:80;
  server_name  _;
  access_log /var/log/nginx/client_nginx.log combined;
  index index.html index.htm index.php;
  root /usr/share/nginx/html/client;

  location ^~ /api/ {
	  index  index.html index.htm;
	   if ( !-e $request_filename ) {
	    rewrite ^/api/(.*)$ /$1 break;
	    proxy_pass http://client:8012;
	   }
	   proxy_connect_timeout 300s;
       proxy_send_timeout 900;
       proxy_read_timeout 900;
       proxy_buffer_size 32k;
       proxy_buffers 4 64k;
       proxy_busy_buffers_size 128k;
       proxy_redirect off;
       proxy_hide_header Vary;
       proxy_set_header Accept-Encoding '';
       proxy_set_header Referer $http_referer;
       proxy_set_header Cookie $http_cookie;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
  }

  location / {
      try_files $uri $uri/ /index.html;
  }
}
