# 部署配置文件
# 请根据你的实际服务器环境修改以下配置

# 服务器配置
SERVER_HOST="your-server.com"           # 服务器地址
SERVER_USER="root"                      # SSH 用户名
SERVER_PORT="22"                        # SSH 端口，默认 22

# 服务器路径配置
SERVER_PATH="/www/sites/fin.aiform.com"        # 主应用部署路径
ADMIN_SERVER_PATH="/www/sites/admin.aiform.com" # 管理员页面部署路径

# 域名配置（用于健康检查）
MAIN_DOMAIN="https://fin.aiform.com"           # 主应用域名
ADMIN_DOMAIN="https://admin.aiform.com"        # 管理员页面域名

# 后端服务配置
BACKEND_SERVICE_NAME="aifinancial-backend"     # 后端服务名（如果使用 systemd）
BACKEND_PORT="9080"                           # 后端服务端口

# 数据库配置（如果需要）
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="financial"

# 构建配置
SKIP_TESTS="true"                             # 是否跳过测试
PARALLEL_BUILD="true"                         # 是否并行构建
BUILD_TIMEOUT="1800"                          # 构建超时时间（秒）

# 备份配置
BACKUP_RETENTION_DAYS="7"                     # 备份保留天数
BACKUP_PATH="/backup/aifinancial"             # 备份存储路径

# 通知配置（可选）
NOTIFICATION_ENABLED="false"                  # 是否启用通知
NOTIFICATION_WEBHOOK=""                       # Webhook URL（钉钉、企业微信等）
NOTIFICATION_EMAIL=""                         # 通知邮箱

# Docker 配置（如果使用 Docker 部署）
DOCKER_ENABLED="false"                        # 是否使用 Docker
DOCKER_REGISTRY=""                            # Docker 镜像仓库
DOCKER_IMAGE_TAG="latest"                     # 镜像标签
