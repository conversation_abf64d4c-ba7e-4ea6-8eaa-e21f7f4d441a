# 票据管理系统技术介绍文档

## 概述

本文档详细介绍了从纷析云财务系统中提取的票据管理和票据归并功能，包括数据库设计、OCR识别、字段映射、移动端支持等核心技术。该功能可以独立部署到其他项目中，提供完整的票据数字化管理能力。

## 核心功能特性

### 1. 票据数字化管理
- **多类型支持**：增值税专用发票、增值税普通发票、收据等
- **OCR自动识别**：基于腾讯云OCR的票据信息自动提取
- **智能字段映射**：AI驱动的字段映射和模板缓存机制
- **移动端优化**：专门的移动端拍照上传和识别界面

### 2. 票据归并功能
- **手动归并**：用户手动勾选票据进行归并，通过备注字段记录归并信息
- **归并组管理**：支持创建、查看、解散票据归并组
- **灵活分组**：用户可以根据业务需要自由组合票据

## 数据库设计

### 核心表结构

#### 1. 票据主表 (`fxy_financial_bill`)

```sql
CREATE TABLE `fxy_financial_bill` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bill_no` varchar(50) NOT NULL COMMENT '票据编号',
  `bill_date` date NOT NULL COMMENT '票据日期',
  `type` varchar(50) NOT NULL COMMENT '票据类型（发票/收据/欠条/机票等）',
  `amount` double NOT NULL COMMENT '金额',
  `issuer` varchar(200) DEFAULT NULL COMMENT '开票方/收款方',
  `recipient` varchar(200) DEFAULT NULL COMMENT '收票方/付款方',
  `summary` varchar(200) DEFAULT NULL COMMENT '摘要/用途',
  `remark` text COMMENT '备注',
  `status` varchar(20) DEFAULT '未使用' COMMENT '状态',
  `doc_group_id` varchar(36) DEFAULT NULL COMMENT '票据归并组ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  `bill_year` int DEFAULT NULL COMMENT '票据年份',
  `bill_month` int DEFAULT NULL COMMENT '票据月份',
  `invoice_number` varchar(50) DEFAULT NULL COMMENT '发票号码',
  `attachment_path` varchar(500) DEFAULT NULL COMMENT '附件路径',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tax_rate` decimal(5,4) DEFAULT NULL COMMENT '税率',
  `total_tax_amount` decimal(15,2) DEFAULT NULL COMMENT '合计税额',
  `amount_in_words` varchar(200) DEFAULT NULL COMMENT '小写金额',
  `ocr_recognition_info` text COMMENT 'OCR识别的原始信息（JSON格式）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_no_account` (`bill_no`, `account_sets_id`),
  UNIQUE KEY `uk_invoice_number_account_sets` (`invoice_number`, `account_sets_id`),
  KEY `idx_account_sets_date` (`account_sets_id`, `bill_date`),
  KEY `idx_bill_year_month` (`bill_year`, `bill_month`),
  KEY `idx_doc_group` (`doc_group_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据表';
```

#### 2. 票据AI处理结果表 (`fxy_financial_bill_ai_result`)

```sql
CREATE TABLE `fxy_financial_bill_ai_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bill_id` int NOT NULL COMMENT '关联的票据ID',
  `ai_processed` tinyint(1) DEFAULT '0' COMMENT 'AI处理状态：0-未处理，1-已处理',
  `confidence` decimal(5,4) DEFAULT NULL COMMENT 'AI置信度(0-1)',
  `ai_analysis` text COMMENT 'AI分析结果',
  `suggested_subjects` json DEFAULT NULL COMMENT '推荐科目信息(JSON格式)',
  `debit_subjects` json DEFAULT NULL COMMENT '借方科目推荐(JSON格式)',
  `credit_subjects` json DEFAULT NULL COMMENT '贷方科目推荐(JSON格式)',
  `matching_reason` text COMMENT 'AI匹配原因说明',
  `ai_process_time` datetime DEFAULT NULL COMMENT 'AI处理时间',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_ai_processed` (`ai_processed`),
  KEY `idx_confidence` (`confidence`),
  CONSTRAINT `fk_bill_ai_result_bill` FOREIGN KEY (`bill_id`) REFERENCES `fxy_financial_bill` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据AI处理结果表';
```

#### 3. 票据归并组表 (`fxy_financial_document_groups`)

```sql
CREATE TABLE `fxy_financial_document_groups` (
  `group_id` varchar(36) NOT NULL COMMENT '组ID，使用UUID',
  `group_name` varchar(100) NOT NULL COMMENT '组名称',
  `merge_rule_id` varchar(36) DEFAULT NULL COMMENT '使用的归并规则ID',
  `rule_params` json DEFAULT NULL COMMENT '规则参数（JSON格式）',
  `group_summary` text COMMENT '组摘要信息',
  `total_amount` decimal(15,2) DEFAULT '0.00' COMMENT '组内总金额',
  `item_count` int DEFAULT '0' COMMENT '组内项目数量',
  `status` enum('ACTIVE','ARCHIVED','DELETED') DEFAULT 'ACTIVE' COMMENT '组状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int DEFAULT NULL COMMENT '创建人ID',
  `account_sets_id` int NOT NULL COMMENT '账套ID',
  PRIMARY KEY (`group_id`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_merge_rule` (`merge_rule_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='票据归并组表';
```



### 字段映射配置表（性能优化）

```sql
-- 字段映射模板主表
CREATE TABLE `fxy_financial_field_mapping_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `document_type` varchar(50) NOT NULL COMMENT '票据类型（INVOICE-发票）',
  `field_count` int(11) DEFAULT NULL COMMENT 'OCR识别字段数量',
  `field_signature` varchar(500) DEFAULT NULL COMMENT '字段特征签名（字段名称的hash值）',
  `mapping_rules` text NOT NULL COMMENT '字段映射规则（JSON格式）',
  `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `success_rate` decimal(5,2) DEFAULT 100.00 COMMENT '成功率（百分比）',
  `last_used_time` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `account_sets_id` int(11) DEFAULT NULL COMMENT '账套ID（NULL表示系统级模板）',
  `create_user` int(11) DEFAULT NULL COMMENT '创建用户ID（NULL表示系统创建）',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用（1-启用，0-禁用）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_signature` (`account_sets_id`, `document_type`, `field_signature`),
  KEY `idx_account_sets` (`account_sets_id`),
  KEY `idx_usage` (`usage_count`, `success_rate`),
  KEY `idx_last_used` (`last_used_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字段映射模板表';
```

## OCR识别和字段映射技术

### 1. OCR识别流程

#### 技术架构
```
用户上传图片 → 腾讯云OCR识别 → 原始字段提取 → 智能字段映射 → 标准化数据输出
```

#### 核心服务类结构

**OcrService.java** - 纯OCR识别服务
```java
@Service
public class OcrService {
    // 识别增值税发票
    public Map<String, Object> recognizeVatInvoice(String imageUrl);
    
    // 解析发票识别结果
    private Map<String, Object> parseVatInvoiceResult(VatInvoiceOCRResponse response);
    
    // 格式化OCR数据为易读文本
    public String formatOcrDataToReadableText(Map<String, String> rawOcrData, String documentType);
    
    // 格式化OCR数据为JSON
    public String formatOcrDataToJson(Map<String, String> rawOcrData);
}
```

**SmartFieldMappingService.java** - 智能字段映射服务
```java
@Service
public class SmartFieldMappingService {
    // 智能映射发票字段
    public Map<String, Object> mapInvoiceFields(Map<String, Object> ocrData, Integer accountSetsId, Integer userId);
    
    // 构建发票字段映射的LLM提示词
    private String buildInvoiceFieldMappingPrompt(Map<String, Object> ocrData);
    
    // 解析AI映射响应
    private Map<String, Object> parseAiMappingResponse(String aiResponse);
}
```

### 2. 字段映射机制

#### 三层映射策略

1. **模板匹配**（优先级最高）
   - 基于字段特征签名匹配历史模板
   - 支持系统级和账套级模板
   - 缓存成功的映射规则，提高性能

2. **AI智能映射**（备选方案）
   - 使用大语言模型进行字段语义理解
   - 支持不同格式发票的智能识别
   - 自动学习并生成新的映射模板

3. **原始数据返回**（兜底方案）
   - 当前两种方式都失败时，返回格式化的原始OCR数据
   - 保证系统的可用性和数据完整性

#### 发票字段映射配置

基于分析，发票字段映射主要支持两种类型：

**增值税专用发票映射配置**
```json
{
  "template_name": "增值税专用发票标准模板",
  "document_type": "INVOICE",
  "mapping_rules": {
    "发票号码": "invoice_number",
    "开票日期": "bill_date",
    "销售方名称": "issuer",
    "购买方名称": "recipient", 
    "价税合计(小写)": "amount",
    "价税合计（小写）": "amount",
    "小写金额": "amount",
    "价税合计(大写)": "amount_in_words",
    "合计税额": "total_tax_amount",
    "税率": "tax_rate",
    "备注": "remark"
  },
  "field_transforms": {
    "amount": "parseFloat",
    "bill_date": "formatDate:yyyy-MM-dd",
    "tax_rate": "parseFloat",
    "total_tax_amount": "parseFloat"
  }
}
```

**增值税普通发票映射配置**
```json
{
  "template_name": "增值税普通发票标准模板", 
  "document_type": "INVOICE",
  "mapping_rules": {
    "发票号码": "invoice_number",
    "开票日期": "bill_date",
    "销售方名称": "issuer",
    "购买方名称": "recipient",
    "价税合计": "amount",
    "合计金额": "amount",
    "不含税金额": "amount",
    "税额": "total_tax_amount",
    "税率": "tax_rate",
    "备注": "remark"
  },
  "field_transforms": {
    "amount": "parseFloat",
    "bill_date": "formatDate:yyyy-MM-dd", 
    "tax_rate": "parseFloat",
    "total_tax_amount": "parseFloat"
  }
}
```

### 3. AI提示词设计

#### 发票字段映射提示词模板
```
请将以下增值税发票OCR识别结果映射到标准字段格式。

标准字段格式（JSON）：
- type: 票据类型（如：发票、收据等）
- amount: 金额（数字，必填字段）
- issuer: 开票方/销售方名称
- recipient: 收票方/购买方名称
- invoice_number: 发票号码
- bill_date: 开票日期（格式：yyyy-MM-dd）
- summary: 摘要/用途（必填字段）
- tax_rate: 税率（数字）
- total_tax_amount: 合计税额（数字）
- amount_in_words: 大写金额
- remark: 备注信息

OCR识别的原始数据：
{OCR_RAW_DATA}

请分析上述OCR数据，将其映射到标准字段。注意：
1. amount（金额）是必填字段，请转换为数字格式，去除逗号等符号
2. bill_date（开票日期）请转换为yyyy-MM-dd格式
3. summary（摘要）是必填字段，如果OCR数据中没有明确的摘要，可以根据商品名称或发票类型生成合适的摘要
4. type（票据类型）请设置为"发票"
5. issuer（开票方）通常是销售方名称
6. recipient（收票方）通常是购买方名称
7. tax_rate（税率）和total_tax_amount（税额）请转换为数字格式
8. 如果某个非必填字段在OCR数据中找不到对应值，请设为null
9. 请只返回JSON格式的映射结果，不要包含其他文字说明
```

## 移动端技术实现

### 1. 移动端架构

#### 页面结构
```
/mobile
├── /                    # 入口页面 - 自动检测登录状态并重定向
├── /login              # 登录页面 - 手机号+密码登录
├── /bills              # 票据列表 - 主页面，展示所有票据
└── /bill/add           # 新增票据 - 核心功能页面
```

#### 核心组件

**BillAdd.vue** - 移动端票据新增页面
```vue
<template>
  <div class="mobile-bill-add">
    <!-- 图片上传区域 -->
    <div class="upload-section">
      <div class="upload-area" @click="triggerFileInput">
        <div v-if="!form.attachmentPath" class="upload-placeholder">
          <div class="upload-icon">📷</div>
          <p>点击拍照或选择图片</p>
          <p class="upload-tip">支持OCR自动识别</p>
        </div>
        
        <div v-else class="uploaded-image">
          <img :src="form.attachmentPath" alt="票据图片" />
          <div class="image-actions">
            <button @click.stop="recognizeImage" :disabled="recognizing">
              {{ recognizing ? '识别中...' : 'OCR识别' }}
            </button>
          </div>
        </div>
      </div>
      
      <input 
        ref="fileInput"
        type="file" 
        accept="image/*" 
        capture="environment"
        style="display: none"
        @change="handleFileSelect"
      />
    </div>

    <!-- 表单区域 -->
    <div class="form-section">
      <!-- 简化的表单字段 -->
    </div>
  </div>
</template>
```

### 2. 移动端OCR识别流程

#### 技术流程
```
拍照/选择图片 → 文件上传 → 自动触发OCR → 字段映射 → 表单填充 → 用户确认 → 保存票据
```

#### 手动归并流程
```
票据列表 → 用户勾选票据 → 创建归并组 → 填写归并备注 → 确认归并 → 更新票据归并组ID
```

#### 关键方法实现
```javascript
// 文件上传
async uploadFile(file) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('folder', 'bills')
  
  const response = await this.$api.upload.file(formData, 'bills')
  if (response.success) {
    this.form.attachmentPath = response.data.filePath
    // 自动触发OCR识别
    setTimeout(() => {
      this.recognizeImage()
    }, 500)
  }
}

// OCR识别
async recognizeImage() {
  const response = await this.$api.upload.recognizeWithEndpoint(
    '/api/upload/recognize-vat-invoice',
    this.form.attachmentPath
  )
  
  if (response.success && response.data) {
    this.fillFormFromOCR(response.data)
  }
}

// 表单填充
fillFormFromOCR(ocrData) {
  if (ocrData.amount) {
    this.form.amount = ocrData.amount
  }
  if (ocrData.bill_date) {
    this.form.billDate = ocrData.bill_date
  }
  if (ocrData.issuer) {
    this.form.summary = `${ocrData.issuer}开具的票据`
  }
}
```

### 3. 移动端优化特性

#### UI/UX优化
- **渐变背景**：现代化的紫色渐变主题
- **卡片设计**：圆角卡片布局，层次分明
- **触摸优化**：针对触摸操作优化的交互体验
- **响应式设计**：适配各种手机屏幕尺寸

#### 性能优化
- **图片压缩**：上传前自动压缩图片
- **缓存策略**：OCR结果缓存，避免重复识别
- **懒加载**：组件按需加载
- **离线支持**：基础的离线缓存和网络检测

## API接口设计

### 1. 文件上传接口

```java
@RestController
@RequestMapping("/api/upload")
public class FileUploadController {
    
    /**
     * 上传文件到OSS
     */
    @PostMapping("/file")
    public JsonResult uploadFile(@RequestParam("file") MultipartFile file,
                                @RequestParam(value = "folder", defaultValue = "bills") String folder);
    
    /**
     * 识别增值税发票
     */
    @PostMapping("/recognize-vat-invoice")
    public JsonResult recognizeVatInvoice(@RequestBody Map<String, String> request);
    
    /**
     * 智能识别发票（包含字段映射）
     */
    @PostMapping("/smart-recognize-invoice")
    public JsonResult smartRecognizeInvoice(@RequestBody Map<String, Object> request);
}
```

### 2. 票据管理接口

```java
@RestController
@RequestMapping("/bill")
public class BillController {
    
    /**
     * 获取票据列表
     */
    @GetMapping("/list")
    public JsonResult list(Integer bill_year, Integer bill_month, Integer page);
    
    /**
     * 保存票据
     */
    @PostMapping
    public JsonResult save(@RequestBody Bill bill);
    
    /**
     * 更新票据
     */
    @PutMapping
    public JsonResult update(@RequestBody Bill bill);
    
    /**
     * 删除票据
     */
    @DeleteMapping("{id}")
    public JsonResult delete(@PathVariable Integer id);
    
    /**
     * 生成票据编号
     */
    @GetMapping("/bill-no")
    public JsonResult loadBillNo();
}
```

### 3. 归并管理接口

```java
@RestController
@RequestMapping("/bill-group")
public class BillGroupController {
    
    /**
     * 手动创建票据归并组
     */
    @PostMapping("/create")
    public JsonResult createBillGroup(@RequestBody CreateBillGroupDto request);
    
    /**
     * 获取票据归并组列表
     */
    @GetMapping("/list")
    public JsonResult getBillGroupList(@RequestParam Integer accountSetsId);
    
    /**
     * 获取票据归并组详情
     */
    @GetMapping("/{groupId}")
    public JsonResult getBillGroupDetail(@PathVariable String groupId);
    
    /**
     * 解散票据组
     */
    @DeleteMapping("/{groupId}")
    public JsonResult dissolveBillGroup(@PathVariable String groupId);
    
    /**
     * 添加票据到归并组
     */
    @PostMapping("/{groupId}/add-bills")
    public JsonResult addBillsToGroup(@PathVariable String groupId, @RequestBody List<Integer> billIds);
    
    /**
     * 从归并组移除票据
     */
    @PostMapping("/{groupId}/remove-bills")
    public JsonResult removeBillsFromGroup(@PathVariable String groupId, @RequestBody List<Integer> billIds);
}
```

## 部署和集成指南

### 1. 数据库部署

#### 执行顺序
```sql
-- 1. 创建核心票据表
CREATE TABLE fxy_financial_bill (...);
CREATE TABLE fxy_financial_bill_ai_result (...);

-- 2. 创建归并相关表
CREATE TABLE fxy_financial_document_groups (...);

-- 3. 创建字段映射模板表
CREATE TABLE fxy_financial_field_mapping_template (...);

-- 4. 创建索引
CREATE INDEX idx_bill_account_sets_date ON fxy_financial_bill(account_sets_id, bill_date);
CREATE INDEX idx_bill_status ON fxy_financial_bill(status);
CREATE INDEX idx_doc_group ON fxy_financial_bill(doc_group_id);
CREATE INDEX idx_document_groups_account ON fxy_financial_document_groups(account_sets_id);
CREATE INDEX idx_document_groups_status ON fxy_financial_document_groups(status);
```

### 2. 后端集成

#### 依赖配置
```xml
<!-- 腾讯云OCR SDK -->
<dependency>
    <groupId>com.tencentcloudapi</groupId>
    <artifactId>tencentcloud-sdk-java</artifactId>
    <version>3.1.423</version>
</dependency>

<!-- MyBatis Plus -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.4.3</version>
</dependency>

<!-- FastJSON -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>fastjson</artifactId>
    <version>1.2.83</version>
</dependency>
```

#### 配置文件
```yaml
# application.yml
tencent:
  cloud:
    secret-id: ${TENCENT_SECRET_ID}
    secret-key: ${TENCENT_SECRET_KEY}
    region: ap-beijing

# OSS配置
oss:
  endpoint: ${OSS_ENDPOINT}
  access-key-id: ${OSS_ACCESS_KEY_ID}
  access-key-secret: ${OSS_ACCESS_KEY_SECRET}
  bucket-name: ${OSS_BUCKET_NAME}
```

### 3. 前端集成

#### 移动端路由配置
```javascript
// router.js
{
  path: '/mobile/bills',
  name: 'MobileBillList',
  component: () => import('./views/mobile/BillList'),
  meta: {
    title: '我的票据',
    mobile: true
  }
},
{
  path: '/mobile/bill/add',
  name: 'MobileBillAdd', 
  component: () => import('./views/mobile/BillAdd'),
  meta: {
    title: '新增票据',
    mobile: true
  }
}
```

#### API配置
```javascript
// api/bill.js
export default {
  list(params) {
    return Ajax.get("/bill/list", params)
  },
  save(params) {
    return Ajax.post("/bill", params)
  },
  loadBillNo(params) {
    return Ajax.get("/bill/bill-no", params)
  }
}

// api/upload.js
export default {
  file(formData, folder = 'bills') {
    return Ajax.post('/api/upload/file?folder=' + folder, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  recognizeWithEndpoint(endpoint, fileUrl) {
    return Ajax.post(endpoint, { fileUrl })
  }
}
```

## 性能优化建议

### 1. 数据库优化
- **索引策略**：为常用查询字段创建复合索引
- **分区表**：按年月对票据表进行分区
- **读写分离**：OCR识别结果缓存到Redis

### 2. OCR识别优化
- **结果缓存**：相同图片的OCR结果缓存24小时
- **限流控制**：控制OCR API调用频率
- **模板匹配**：优先使用字段映射模板，减少AI调用

### 3. 移动端优化
- **图片压缩**：上传前压缩图片到合适尺寸
- **离线缓存**：缓存常用数据，支持离线查看
- **懒加载**：列表数据分页加载

## 扩展功能建议

### 1. 功能扩展
- **多语言支持**：支持不同语言的发票识别
- **数据导出**：支持Excel、PDF格式导出
- **统计分析**：票据金额统计和趋势分析
- **归并组管理**：更丰富的归并组操作和统计功能

### 2. 技术扩展
- **微服务架构**：将OCR识别独立为微服务
- **容器化部署**：支持Docker和Kubernetes部署
- **监控告警**：添加系统监控和异常告警
- **性能优化**：OCR结果缓存、限流控制等

## 总结

本票据管理系统提供了完整的票据数字化解决方案，包括：

1. **完整的数据库设计**：支持票据管理和手动归并功能
2. **智能OCR识别**：基于腾讯云OCR + AI字段映射的双重保障
3. **移动端优化**：专门的移动端界面，支持拍照识别
4. **简化的归并功能**：用户手动勾选票据进行归并，操作简单直观
5. **高性能架构**：模板缓存、限流控制等优化措施
6. **易于集成**：清晰的API设计和部署指南

该系统可以独立部署到其他项目中，提供企业级的票据数字化管理能力。通过简化的设计和合理的配置，可以快速满足票据管理的核心需求。