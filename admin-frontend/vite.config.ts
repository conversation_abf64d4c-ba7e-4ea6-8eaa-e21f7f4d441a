import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'

export default defineConfig({
  plugins: [
    vue()
    // 暂时禁用自动导入插件进行调试
    // AutoImport({
    //   resolvers: [ElementPlusResolver()],
    //   imports: [
    //     'vue',
    //     'vue-router',
    //     'pinia',
    //     {
    //       'element-plus': [
    //         'ElMessage',
    //         'ElMessageBox',
    //         'ElNotification',
    //         'ElLoading'
    //       ]
    //     }
    //   ],
    //   dts: true,
    //   eslintrc: {
    //     enabled: true
    //   }
    // }),
    // Components({
    //   resolvers: [ElementPlusResolver()],
    //   dts: true
    // })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3001,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: 'http://localhost:9080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    target: 'es2015',
    minify: 'terser'
    // 暂时移除手动分包，使用默认策略
    // rollupOptions: {
    //   output: {
    //     manualChunks(id) {
    //       // Vue 核心库
    //       if (id.includes('vue') && !id.includes('vue-echarts')) {
    //         return 'vue-vendor'
    //       }
    //       // Element Plus 相关
    //       if (id.includes('element-plus') || id.includes('@element-plus')) {
    //         return 'element-plus'
    //       }
    //       // ECharts 相关
    //       if (id.includes('echarts')) {
    //         return 'echarts'
    //       }
    //       // 其他第三方库
    //       if (id.includes('node_modules')) {
    //         return 'vendor'
    //       }
    //     }
    //   }
    // }
  },
  css: {
    postcss: {
      plugins: [
        tailwindcss,
        autoprefixer
      ]
    }
  }
})
