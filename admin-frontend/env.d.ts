/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_VERSION: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_DEV_PORT: number
  readonly VITE_OPEN_BROWSER: boolean
  readonly VITE_SOURCEMAP: boolean
  readonly VITE_DROP_CONSOLE: boolean
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
