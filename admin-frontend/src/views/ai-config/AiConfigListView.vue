<template>
  <div class="page-container">
    <div class="page-header">
      <h2>AI配置管理</h2>
      <p class="page-description">管理用户的AI功能配置，包括API设置、模型参数等</p>
    </div>

    <div class="content-card">
      <!-- 搜索和操作区域 -->
      <div class="toolbar-section">
        <div class="search-area">
          <el-form :model="searchForm" inline class="search-form">
            <el-form-item label="用户">
              <el-select
                v-model="searchForm.userId"
                placeholder="请选择用户"
                style="width: 200px"
                clearable
                filterable
              >
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="`${user.realName || user.username} (${user.username})`"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="账套">
              <el-select
                v-model="searchForm.accountSetsId"
                placeholder="请选择账套"
                style="width: 200px"
                clearable
                filterable
              >
                <el-option
                  v-for="accountSet in accountSetsList"
                  :key="accountSet.id"
                  :label="accountSet.companyName"
                  :value="accountSet.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="AI状态">
              <el-select
                v-model="searchForm.enabled"
                placeholder="请选择状态"
                style="width: 120px"
                clearable
              >
                <el-option label="已启用" :value="true" />
                <el-option label="已禁用" :value="false" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchConfigs">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="resetSearch">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="action-area">
          <el-button type="primary" @click="showBatchConfigDialog">
            <el-icon><Setting /></el-icon>
            批量配置
          </el-button>
          <el-button type="success" @click="exportConfigs">
            <el-icon><Download /></el-icon>
            导出配置
          </el-button>
          <el-button type="info" @click="showTemplateDialog">
            <el-icon><Document /></el-icon>
            配置模板
          </el-button>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总用户数" :value="statistics.totalUsers" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="已启用AI" :value="statistics.enabledUsers" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="配置完整度" :value="statistics.configCompleteness" suffix="%" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="本月调用次数" :value="statistics.monthlyApiCalls" />
          </el-col>
        </el-row>
      </div>

      <!-- AI配置列表表格 -->
      <div class="table-section">
        <el-table
          :data="configList"
          v-loading="loading"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="userId" label="用户ID" width="80" />
          <el-table-column prop="userName" label="用户名" width="120" />
          <el-table-column prop="realName" label="真实姓名" width="120" />
          <el-table-column prop="accountSetsName" label="账套" width="150" show-overflow-tooltip />
          <el-table-column prop="enabled" label="AI状态" width="100">
            <template #default="{ row }">
              <el-switch
                v-model="row.enabled"
                @change="toggleAiStatus(row)"
                active-text="启用"
                inactive-text="禁用"
              />
            </template>
          </el-table-column>
          <el-table-column prop="baseUrl" label="API地址" width="200" show-overflow-tooltip />
          <el-table-column prop="defaultModel" label="默认模型" width="120" />
          <el-table-column prop="configCompleteness" label="配置完整度" width="120">
            <template #default="{ row }">
              <el-progress
                :percentage="row.configCompleteness"
                :color="getCompletenessColor(row.configCompleteness)"
                :stroke-width="8"
                text-inside
              />
            </template>
          </el-table-column>
          <el-table-column prop="lastTestTime" label="最后测试" width="160" />
          <el-table-column prop="updateTime" label="更新时间" width="160" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="editConfig(row)"
              >
                配置
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="testConfig(row)"
                :disabled="!row.enabled"
              >
                测试
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="viewUsage(row)"
              >
                使用情况
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- AI配置编辑对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      :title="`配置AI - ${currentUser?.realName || currentUser?.username}`"
      width="800px"
      :close-on-click-modal="false"
      @close="handleConfigDialogClose"
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="configRules"
        label-width="120px"
      >
        <el-tabs v-model="activeConfigTab">
          <!-- 基础配置 -->
          <el-tab-pane label="基础配置" name="basic">
            <el-form-item label="启用AI功能" prop="enabled">
              <el-switch
                v-model="configForm.enabled"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>

            <el-form-item label="API地址" prop="baseUrl">
              <div class="api-url-container">
                <el-input
                  v-model="configForm.baseUrl"
                  placeholder="请输入API地址，如：https://api.openai.com/v1"
                />
                <div class="quick-config-buttons">
                  <el-button
                    size="small"
                    type="primary"
                    @click="applyTemplate('deepseek')"
                  >
                    🧠 DeepSeek
                  </el-button>
                  <el-button
                    size="small"
                    type="success"
                    @click="applyTemplate('industry')"
                  >
                    🏭 行业智能体
                  </el-button>
                  <el-button
                    size="small"
                    type="info"
                    @click="applyTemplate('openai')"
                  >
                    🤖 公司专属智能体
                  </el-button>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="API密钥" prop="apiKey">
              <el-input
                v-model="configForm.apiKey"
                type="password"
                placeholder="请输入API密钥"
                show-password
              />
            </el-form-item>

            <el-form-item label="默认模型" prop="defaultModel">
              <div class="model-container">
                <el-select
                  v-model="configForm.defaultModel"
                  placeholder="请选择默认模型"
                  style="width: 100%"
                  filterable
                  allow-create
                >
                  <el-option
                    v-for="model in availableModels"
                    :key="model.id"
                    :label="`${model.id} ${model.name ? '(' + model.name + ')' : ''}`"
                    :value="model.id"
                  />
                  <!-- 默认选项 -->
                  <el-option label="GPT-4" value="gpt-4" />
                  <el-option label="GPT-4 Turbo" value="gpt-4-turbo" />
                  <el-option label="GPT-3.5 Turbo" value="gpt-3.5-turbo" />
                  <el-option label="DeepSeek Chat" value="deepseek-chat" />
                  <el-option label="Qwen Turbo" value="qwen-turbo" />
                </el-select>
                <div class="model-buttons">
                  <el-button
                    size="small"
                    type="primary"
                    :loading="loadingModels"
                    @click="refreshModels"
                  >
                    {{ loadingModels ? '刷新中...' : '🔄 刷新模型' }}
                  </el-button>
                  <el-button
                    size="small"
                    type="success"
                    @click="smartSelectModel"
                  >
                    🤖 智能推荐
                  </el-button>
                </div>
                <div class="model-tip">
                  💡 点击"刷新模型"获取最新可用模型列表，或直接输入模型ID
                </div>
              </div>
            </el-form-item>
          </el-tab-pane>

          <!-- 高级配置 -->
          <el-tab-pane label="高级配置" name="advanced">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="超时时间" prop="timeout">
                  <el-input-number
                    v-model="configForm.timeout"
                    :min="5"
                    :max="300"
                    :step="5"
                    controls-position="right"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：秒，范围：5-300秒</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大重试次数" prop="maxRetries">
                  <el-input-number
                    v-model="configForm.maxRetries"
                    :min="0"
                    :max="5"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="温度参数" prop="temperature">
                  <el-slider
                    v-model="configForm.temperature"
                    :min="0"
                    :max="2"
                    :step="0.1"
                    show-input
                    :show-input-controls="false"
                  />
                  <div class="form-tip">控制输出的随机性，0-2之间</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大令牌数" prop="maxTokens">
                  <el-input-number
                    v-model="configForm.maxTokens"
                    :min="100"
                    :max="4000"
                    :step="100"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 功能开关 -->
          <el-tab-pane label="功能开关" name="features">
            <el-form-item label="智能字段映射">
              <el-switch
                v-model="configForm.enableFieldMapping"
                active-text="启用"
                inactive-text="禁用"
              />
              <div class="form-tip">使用AI进行OCR字段到标准字段的智能映射</div>
            </el-form-item>

            <el-form-item label="智能关联">
              <el-switch
                v-model="configForm.enableSmartRelation"
                active-text="启用"
                inactive-text="禁用"
              />
              <div class="form-tip">使用AI进行票据间的智能关联</div>
            </el-form-item>

            <el-form-item label="科目AI增强">
              <el-switch
                v-model="configForm.enableSubjectAi"
                active-text="启用"
                inactive-text="禁用"
              />
              <div class="form-tip">使用AI增强会计科目的智能匹配</div>
            </el-form-item>

            <el-form-item label="凭证生成">
              <el-switch
                v-model="configForm.enableVoucherGeneration"
                active-text="启用"
                inactive-text="禁用"
              />
              <div class="form-tip">使用AI自动生成会计凭证</div>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button
          type="info"
          :loading="testLoading"
          @click="testCurrentConfig"
        >
          测试连接
        </el-button>
        <el-button
          type="primary"
          :loading="saveLoading"
          @click="saveConfig"
        >
          保存配置
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量配置对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量配置AI"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="batchForm" label-width="120px">
        <el-form-item label="选择用户">
          <el-select
            v-model="batchForm.userIds"
            multiple
            placeholder="请选择要配置的用户"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="`${user.realName || user.username} (${user.username})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="配置模板">
          <el-select
            v-model="batchForm.templateId"
            placeholder="请选择配置模板"
            style="width: 100%"
          >
            <el-option
              v-for="template in configTemplates"
              :key="template.id"
              :label="template.name"
              :value="template.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="覆盖现有配置">
          <el-switch
            v-model="batchForm.overwrite"
            active-text="是"
            inactive-text="否"
          />
          <div class="form-tip">是否覆盖用户现有的AI配置</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="batchLoading"
          @click="applyBatchConfig"
        >
          应用配置
        </el-button>
      </template>
    </el-dialog>

    <!-- 配置模板管理对话框 -->
    <el-dialog
      v-model="templateDialogVisible"
      title="配置模板管理"
      width="800px"
    >
      <div class="template-section">
        <div class="template-header">
          <el-button type="primary" @click="showCreateTemplateDialog">
            <el-icon><Plus /></el-icon>
            创建模板
          </el-button>
        </div>

        <el-table :data="configTemplates" border style="width: 100%">
          <el-table-column prop="name" label="模板名称" />
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="editTemplate(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteTemplate(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 使用情况对话框 -->
    <el-dialog
      v-model="usageDialogVisible"
      title="AI使用情况"
      width="900px"
    >
      <div v-if="currentUsage" class="usage-section">
        <el-row :gutter="20" class="usage-stats">
          <el-col :span="6">
            <el-statistic title="本月调用次数" :value="currentUsage.monthlyApiCalls" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="成功率" :value="currentUsage.successRate" suffix="%" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="平均响应时间" :value="currentUsage.avgResponseTime" suffix="ms" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="本月费用" :value="currentUsage.monthlyCost" prefix="¥" />
          </el-col>
        </el-row>

        <div class="usage-chart">
          <h3>使用趋势</h3>
          <div class="chart-placeholder">
            <p>使用趋势图表（需要集成图表库）</p>
          </div>
        </div>

        <div class="usage-logs">
          <h3>最近调用记录</h3>
          <el-table :data="currentUsage.recentLogs" border style="width: 100%">
            <el-table-column prop="time" label="时间" width="160" />
            <el-table-column prop="function" label="功能" width="120" />
            <el-table-column prop="model" label="模型" width="120" />
            <el-table-column prop="tokens" label="令牌数" width="100" />
            <el-table-column prop="cost" label="费用" width="80" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                  {{ row.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Search, Refresh, Setting, Download, Document, Plus } from '@element-plus/icons-vue'
import { request } from '@/utils/request'

// 类型定义
interface User {
  id: number
  username: string
  realName?: string
}

interface AccountSet {
  id: number
  companyName: string
}

interface AiConfig {
  id?: number
  userId: number
  userName: string
  realName?: string
  accountSetsId?: number
  accountSetsName?: string
  enabled: boolean
  baseUrl: string
  apiKey: string
  defaultModel: string
  timeout: number
  maxRetries: number
  temperature: number
  maxTokens: number
  enableFieldMapping: boolean
  enableSmartRelation: boolean
  enableSubjectAi: boolean
  enableVoucherGeneration: boolean
  configCompleteness: number
  lastTestTime?: string
  updateTime?: string
}

interface ConfigTemplate {
  id: number
  name: string
  description: string
  config: Partial<AiConfig>
  createTime: string
}

interface UsageData {
  monthlyApiCalls: number
  successRate: number
  avgResponseTime: number
  monthlyCost: number
  recentLogs: Array<{
    time: string
    function: string
    model: string
    tokens: number
    cost: number
    status: string
  }>
}

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const testLoading = ref(false)
const batchLoading = ref(false)
const configDialogVisible = ref(false)
const batchDialogVisible = ref(false)
const templateDialogVisible = ref(false)
const usageDialogVisible = ref(false)
const activeConfigTab = ref('basic')
const configFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  userId: null as number | null,
  accountSetsId: null as number | null,
  enabled: null as boolean | null
})

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 统计信息
const statistics = reactive({
  totalUsers: 0,
  enabledUsers: 0,
  configCompleteness: 0,
  monthlyApiCalls: 0
})

// 配置表单
const configForm = reactive<AiConfig>({
  userId: 0,
  userName: '',
  enabled: false,
  baseUrl: '',
  apiKey: '',
  defaultModel: 'gpt-3.5-turbo',
  timeout: 30,
  maxRetries: 3,
  temperature: 0.7,
  maxTokens: 1000,
  enableFieldMapping: true,
  enableSmartRelation: true,
  enableSubjectAi: true,
  enableVoucherGeneration: false,
  configCompleteness: 0
})

// 批量配置表单
const batchForm = reactive({
  userIds: [] as number[],
  templateId: null as number | null,
  overwrite: false
})

// 当前用户和使用情况
const currentUser = ref<User | null>(null)
const currentUsage = ref<UsageData | null>(null)
const selectedConfigs = ref<AiConfig[]>([])

// 用户列表
const userList = ref<User[]>([])

// 账套列表
const accountSetsList = ref<AccountSet[]>([])

// AI配置列表
const configList = ref<AiConfig[]>([])

// 可用模型列表
const availableModels = ref<any[]>([])

// 加载状态
const loadingModels = ref(false)

// 配置模板
const configTemplates = ref<ConfigTemplate[]>([
  {
    id: 1,
    name: '标准配置',
    description: '适用于大多数用户的标准AI配置',
    config: {
      enabled: true,
      baseUrl: 'https://api.openai.com/v1',
      defaultModel: 'gpt-3.5-turbo',
      timeout: 30,
      maxRetries: 3,
      temperature: 0.7,
      maxTokens: 1000,
      enableFieldMapping: true,
      enableSmartRelation: true,
      enableSubjectAi: true,
      enableVoucherGeneration: false
    },
    createTime: '2024-01-01 10:00:00'
  }
])

// 表单验证规则
const configRules: FormRules = {
  baseUrl: [
    { required: true, message: '请输入API地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  apiKey: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ],
  defaultModel: [
    { required: true, message: '请选择默认模型', trigger: 'change' }
  ]
}

// 方法
const loadConfigs = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params: any = {
      page: pagination.current,
      size: pagination.size
    }

    // 添加搜索条件
    if (searchForm.userId) {
      params.userId = searchForm.userId
    }
    if (searchForm.accountSetsId) {
      params.accountSetsId = searchForm.accountSetsId
    }
    if (searchForm.enabled !== null) {
      params.enabled = searchForm.enabled
    }

    // 调用API获取AI配置列表
    const result = await request.get('/admin/ai-config/list', { params })

    configList.value = result.data.list || []
    pagination.total = result.data.total || 0

    // 更新统计信息
    statistics.totalUsers = result.data.total || 0
    statistics.enabledUsers = configList.value.filter(c => c.enabled).length
    statistics.configCompleteness = configList.value.length > 0
      ? Math.round(configList.value.reduce((sum, c) => sum + c.configCompleteness, 0) / configList.value.length)
      : 0
    statistics.monthlyApiCalls = result.data.monthlyApiCalls || 0

  } catch (error) {
    console.error('Load configs error:', error)
    ElMessage.error('加载AI配置列表失败')
  } finally {
    loading.value = false
  }
}

// 加载用户列表
const loadUsers = async () => {
  try {
    const result = await request.get('/admin/user/list')
    userList.value = result.data || []
  } catch (error) {
    console.error('Load users error:', error)
  }
}

// 加载账套列表
const loadAccountSets = async () => {
  try {
    const result = await request.get('/admin/account-sets/list')
    accountSetsList.value = result.data || []
  } catch (error) {
    console.error('Load account sets error:', error)
  }
}

const searchConfigs = () => {
  pagination.current = 1
  loadConfigs()
}

const resetSearch = () => {
  searchForm.userId = null
  searchForm.accountSetsId = null
  searchForm.enabled = null
  pagination.current = 1
  loadConfigs()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadConfigs()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadConfigs()
}

const handleSelectionChange = (selection: AiConfig[]) => {
  selectedConfigs.value = selection
}

const editConfig = (config: AiConfig) => {
  currentUser.value = { id: config.userId, username: config.userName, realName: config.realName }
  Object.assign(configForm, config)
  configDialogVisible.value = true
}

const toggleAiStatus = async (config: AiConfig) => {
  try {
    await request.post(`/admin/ai-config/user/${config.userId}/toggle-status`, { enabled: config.enabled })
    ElMessage.success(`AI功能已${config.enabled ? '启用' : '禁用'}`)
    loadConfigs()
  } catch (error) {
    console.error('Toggle AI status error:', error)
    ElMessage.error('切换AI状态失败')
    config.enabled = !config.enabled // 回滚状态
  }
}

const testConfig = async (config: AiConfig) => {
  try {
    testLoading.value = true

    // TODO: 调用API测试配置
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用

    config.lastTestTime = new Date().toLocaleString('zh-CN')
    ElMessage.success('AI配置测试成功')
  } catch (error) {
    console.error('Test config error:', error)
    ElMessage.error('AI配置测试失败')
  } finally {
    testLoading.value = false
  }
}

const viewUsage = (config: AiConfig) => {
  currentUsage.value = {
    monthlyApiCalls: 156,
    successRate: 95.5,
    avgResponseTime: 1200,
    monthlyCost: 25.80,
    recentLogs: [
      {
        time: '2024-01-15 14:30:00',
        function: '字段映射',
        model: 'gpt-4',
        tokens: 150,
        cost: 0.003,
        status: 'success'
      },
      {
        time: '2024-01-15 14:25:00',
        function: '智能关联',
        model: 'gpt-4',
        tokens: 200,
        cost: 0.004,
        status: 'success'
      }
    ]
  }
  usageDialogVisible.value = true
}

const handleConfigDialogClose = () => {
  resetConfigForm()
  if (configFormRef.value) {
    configFormRef.value.resetFields()
  }
}

const resetConfigForm = () => {
  Object.assign(configForm, {
    userId: 0,
    userName: '',
    enabled: false,
    baseUrl: '',
    apiKey: '',
    defaultModel: 'gpt-3.5-turbo',
    timeout: 30,
    maxRetries: 3,
    temperature: 0.7,
    maxTokens: 1000,
    enableFieldMapping: true,
    enableSmartRelation: true,
    enableSubjectAi: true,
    enableVoucherGeneration: false,
    configCompleteness: 0
  })
}

const saveConfig = async () => {
  if (!configFormRef.value) return

  try {
    await configFormRef.value.validate()
    saveLoading.value = true

    // 构建配置数据
    const configData = {
      enabled: configForm.enabled.toString(),
      base_url: configForm.baseUrl,
      api_key: configForm.apiKey,
      default_model: configForm.defaultModel,
      timeout: configForm.timeout.toString(),
      max_retries: configForm.maxRetries.toString(),
      temperature: configForm.temperature.toString(),
      max_tokens: configForm.maxTokens.toString(),
      enable_field_mapping: configForm.enableFieldMapping.toString(),
      enable_smart_relation: configForm.enableSmartRelation.toString(),
      enable_subject_ai: configForm.enableSubjectAi.toString(),
      enable_voucher_generation: configForm.enableVoucherGeneration.toString()
    }

    // 调用API保存配置
    await request.post(`/admin/ai-config/user/${configForm.userId}`, configData)

    ElMessage.success('AI配置保存成功')
    configDialogVisible.value = false
    loadConfigs()

  } catch (error) {
    console.error('Save config error:', error)
    ElMessage.error('保存AI配置失败')
  } finally {
    saveLoading.value = false
  }
}

const testCurrentConfig = async () => {
  if (!configFormRef.value) return

  try {
    // 只验证必要字段
    if (!configForm.baseUrl || !configForm.apiKey) {
      ElMessage.error('请先填写API地址和密钥')
      return
    }

    testLoading.value = true

    // 调用API测试连接
    await request.post(`/admin/ai-config/user/${configForm.userId}/test`, {
      base_url: configForm.baseUrl,
      api_key: configForm.apiKey,
      default_model: configForm.defaultModel
    })

    ElMessage.success('连接测试成功')

  } catch (error) {
    console.error('Test connection error:', error)
    ElMessage.error('连接测试失败')
  } finally {
    testLoading.value = false
  }
}

const showBatchConfigDialog = () => {
  batchForm.userIds = []
  batchForm.templateId = null
  batchForm.overwrite = false
  batchDialogVisible.value = true
}

const applyBatchConfig = async () => {
  if (!batchForm.userIds.length || !batchForm.templateId) {
    ElMessage.error('请选择用户和配置模板')
    return
  }

  try {
    batchLoading.value = true

    // TODO: 调用API应用批量配置
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用

    ElMessage.success(`成功为 ${batchForm.userIds.length} 个用户应用配置`)
    batchDialogVisible.value = false
    loadConfigs()
  } catch (error) {
    console.error('Apply batch config error:', error)
    ElMessage.error('批量配置应用失败')
  } finally {
    batchLoading.value = false
  }
}

const showTemplateDialog = () => {
  templateDialogVisible.value = true
}

const showCreateTemplateDialog = () => {
  // TODO: 实现创建模板对话框
  ElMessage.info('创建模板功能开发中')
}

const editTemplate = (template: ConfigTemplate) => {
  // TODO: 实现编辑模板功能
  ElMessage.info('编辑模板功能开发中')
}

const deleteTemplate = (template: ConfigTemplate) => {
  ElMessageBox.confirm(`确定要删除模板 "${template.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = configTemplates.value.findIndex(t => t.id === template.id)
    if (index !== -1) {
      configTemplates.value.splice(index, 1)
      ElMessage.success('模板删除成功')
    }
  })
}

const exportConfigs = () => {
  const dataToExport = selectedConfigs.value.length > 0 ? selectedConfigs.value : configList.value
  const jsonData = JSON.stringify(dataToExport, null, 2)
  const blob = new Blob([jsonData], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `ai_configs_${new Date().toISOString().slice(0, 10)}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('AI配置导出成功')
}

const calculateCompleteness = (config: AiConfig): number => {
  const requiredFields = ['baseUrl', 'apiKey', 'defaultModel']
  const optionalFields = ['timeout', 'maxRetries', 'temperature', 'maxTokens']

  let score = 0
  const totalFields = requiredFields.length + optionalFields.length

  // 必填字段权重更高
  requiredFields.forEach(field => {
    if (config[field as keyof AiConfig]) {
      score += 2
    }
  })

  optionalFields.forEach(field => {
    if (config[field as keyof AiConfig]) {
      score += 1
    }
  })

  return Math.round((score / (requiredFields.length * 2 + optionalFields.length)) * 100)
}

const getCompletenessColor = (completeness: number) => {
  if (completeness >= 80) return '#67c23a'
  if (completeness >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 刷新模型列表
const refreshModels = async () => {
  if (!configForm.baseUrl || !configForm.apiKey) {
    ElMessage.error('请先填写API地址和密钥')
    return
  }

  loadingModels.value = true
  try {
    const result = await request.post(`/admin/ai-config/user/${configForm.userId}/models`, {
      baseUrl: configForm.baseUrl,
      apiKey: configForm.apiKey
    })

    if (result.data && Array.isArray(result.data)) {
      availableModels.value = result.data
      ElMessage.success(`成功加载 ${result.data.length} 个模型`)

      // 如果当前没有选择模型，自动选择第一个
      if (!configForm.defaultModel && result.data.length > 0) {
        configForm.defaultModel = result.data[0].id
      }
    } else {
      ElMessage.error('获取模型列表失败')
    }
  } catch (error) {
    console.error('Refresh models error:', error)
    ElMessage.error('刷新模型列表失败')
  } finally {
    loadingModels.value = false
  }
}

// 智能推荐模型
const smartSelectModel = () => {
  if (availableModels.value.length === 0) {
    ElMessage.error('请先刷新模型列表')
    return
  }

  const baseUrl = configForm.baseUrl.toLowerCase()
  let recommendedModel = null

  if (baseUrl.includes('deepseek')) {
    recommendedModel = availableModels.value.find(m => m.id.includes('chat')) || availableModels.value[0]
  } else if (baseUrl.includes('dashscope')) {
    recommendedModel = availableModels.value.find(m => m.id.includes('turbo')) || availableModels.value[0]
  } else if (baseUrl.includes('openai')) {
    recommendedModel = availableModels.value.find(m => m.id.includes('3.5-turbo')) || availableModels.value[0]
  } else {
    recommendedModel = availableModels.value[0]
  }

  if (recommendedModel) {
    configForm.defaultModel = recommendedModel.id
    ElMessage.success(`已智能推荐模型: ${recommendedModel.id}`)
  } else {
    ElMessage.error('无法找到合适的模型，请手动选择')
  }
}

// 应用配置模板
const applyTemplate = async (templateId: string) => {
  try {
    const result = await request.get('/admin/ai-config/templates')
    const templates = result.data || []
    const template = templates.find((t: any) => t.id === templateId)

    if (template) {
      configForm.baseUrl = template.baseUrl
      configForm.defaultModel = template.defaultModel
      ElMessage.success(`已应用 ${template.name} 配置模板`)

      // 自动刷新模型列表
      if (configForm.apiKey) {
        setTimeout(() => {
          refreshModels()
        }, 500)
      }
    } else {
      ElMessage.error('配置模板不存在')
    }
  } catch (error) {
    console.error('Apply template error:', error)
    ElMessage.error('应用配置模板失败')
  }
}

// 生命周期
onMounted(() => {
  loadUsers()
  loadAccountSets()
  loadConfigs()
})
</script>

<style scoped>
.page-description {
  color: #666;
  margin-top: 8px;
  margin-bottom: 0;
}

.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.search-area {
  flex: 1;
}

.search-form {
  margin: 0;
}

.action-area {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.stats-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.table-section {
  margin-top: 20px;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.template-section {
  padding: 10px 0;
}

.template-header {
  margin-bottom: 20px;
  text-align: right;
}

.usage-section {
  padding: 10px 0;
}

.usage-stats {
  margin-bottom: 30px;
}

.usage-chart {
  margin-bottom: 30px;
}

.usage-chart h3,
.usage-logs h3 {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
}

.chart-placeholder {
  height: 200px;
  background: #f5f5f5;
  border: 1px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

/* API地址容器样式 */
.api-url-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quick-config-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-config-buttons .el-button {
  font-size: 12px;
  padding: 4px 8px;
}

/* 模型容器样式 */
.model-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.model-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.model-buttons .el-button {
  font-size: 12px;
  padding: 4px 8px;
}

.model-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}
</style>
