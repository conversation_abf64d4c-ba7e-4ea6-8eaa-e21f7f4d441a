<template>
  <div class="page-container">
    <div class="page-header">
      <h2>仪表盘</h2>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">用户总数</div>
              <div class="stat-value">{{ stats.userCount }}</div>
              <div class="stat-trend up">
                <el-icon><TrendCharts /></el-icon>
                +12%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon account-icon">
              <el-icon><Files /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">账套总数</div>
              <div class="stat-value">{{ stats.accountCount }}</div>
              <div class="stat-trend up">
                <el-icon><TrendCharts /></el-icon>
                +8%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon ai-icon">
              <el-icon><MagicStick /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">AI配置数</div>
              <div class="stat-value">{{ stats.aiConfigCount }}</div>
              <div class="stat-trend up">
                <el-icon><TrendCharts /></el-icon>
                +25%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon template-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">模板数量</div>
              <div class="stat-value">{{ stats.templateCount }}</div>
              <div class="stat-trend up">
                <el-icon><TrendCharts /></el-icon>
                +5%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>用户增长趋势</span>
              <el-button link size="small">查看详情</el-button>
            </div>
          </template>
          <div class="chart-container" ref="userChartRef"></div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>AI使用情况</span>
              <el-button link size="small">查看详情</el-button>
            </div>
          </template>
          <div class="chart-container" ref="aiChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近操作日志 -->
    <el-row :gutter="20" class="logs-row">
      <el-col :span="24">
        <el-card class="logs-card">
          <template #header>
            <div class="card-header">
              <span>最近操作日志</span>
              <el-button link size="small" @click="$router.push('/system/logs')">
                查看全部
              </el-button>
            </div>
          </template>
          
          <el-table :data="recentLogs" style="width: 100%" v-loading="logsLoading">
            <el-table-column prop="adminUsername" label="操作人" width="120" />
            <el-table-column prop="operationType" label="操作类型" width="120">
              <template #default="scope">
                <el-tag :type="getOperationTypeTag(scope.row.operationType)" size="small">
                  {{ scope.row.operationType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="operationModule" label="操作模块" width="120" />
            <el-table-column prop="operationDesc" label="操作描述" />
            <el-table-column prop="createTime" label="操作时间" width="180">
              <template #default="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status ? 'success' : 'danger'" size="small">
                  {{ scope.row.status ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { DashboardStats, OperationLog } from '@/types'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const logsLoading = ref(false)
const userChartRef = ref<HTMLElement>()
const aiChartRef = ref<HTMLElement>()

// 统计数据
const stats = ref<DashboardStats>({
  userCount: 0,
  accountCount: 0,
  aiConfigCount: 0,
  templateCount: 0,
  userTrend: [],
  accountTrend: [],
  aiConfigTrend: [],
  templateTrend: [],
  userActivity: [],
  aiUsage: []
})

// 最近操作日志
const recentLogs = ref<OperationLog[]>([])

// 图表实例
let userChart: echarts.ECharts | null = null
let aiChart: echarts.ECharts | null = null

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadRecentLogs()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('Refresh data error:', error)
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  // 模拟数据，实际应该调用API
  stats.value = {
    userCount: 1248,
    accountCount: 89,
    aiConfigCount: 156,
    templateCount: 23,
    userTrend: [120, 132, 101, 134, 90, 230, 210],
    accountTrend: [80, 82, 85, 87, 89, 89, 89],
    aiConfigTrend: [100, 110, 120, 135, 145, 150, 156],
    templateTrend: [20, 21, 21, 22, 22, 23, 23],
    userActivity: [
      { date: '2024-01-01', count: 120 },
      { date: '2024-01-02', count: 132 },
      { date: '2024-01-03', count: 101 },
      { date: '2024-01-04', count: 134 },
      { date: '2024-01-05', count: 90 },
      { date: '2024-01-06', count: 230 },
      { date: '2024-01-07', count: 210 }
    ],
    aiUsage: [
      { provider: 'OpenAI', count: 45 },
      { provider: 'DeepSeek', count: 78 },
      { provider: 'Qwen', count: 33 }
    ]
  }
}

const loadRecentLogs = async () => {
  logsLoading.value = true
  try {
    // 模拟数据，实际应该调用API
    recentLogs.value = [
      {
        id: 1,
        adminUserId: 1,
        adminUsername: 'admin',
        operationType: '创建',
        operationModule: '用户管理',
        operationDesc: '创建用户：张三',
        status: true,
        createTime: '2024-01-15 10:30:00'
      },
      {
        id: 2,
        adminUserId: 1,
        adminUsername: 'admin',
        operationType: '编辑',
        operationModule: 'AI配置',
        operationDesc: '修改用户AI配置',
        status: true,
        createTime: '2024-01-15 10:25:00'
      },
      {
        id: 3,
        adminUserId: 1,
        adminUsername: 'admin',
        operationType: '删除',
        operationModule: '账套管理',
        operationDesc: '删除账套：测试账套',
        status: false,
        createTime: '2024-01-15 10:20:00'
      }
    ]
  } catch (error) {
    console.error('Load recent logs error:', error)
  } finally {
    logsLoading.value = false
  }
}

const initCharts = async () => {
  await nextTick()
  
  // 初始化用户增长趋势图
  if (userChartRef.value) {
    userChart = echarts.init(userChartRef.value)
    const userOption = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: stats.value.userTrend,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
            { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
          ])
        },
        lineStyle: {
          color: '#3b82f6'
        },
        itemStyle: {
          color: '#3b82f6'
        }
      }]
    }
    userChart.setOption(userOption)
  }
  
  // 初始化AI使用情况图
  if (aiChartRef.value) {
    aiChart = echarts.init(aiChartRef.value)
    const aiOption = {
      tooltip: {
        trigger: 'item'
      },
      series: [{
        type: 'pie',
        radius: '60%',
        data: stats.value.aiUsage.map(item => ({
          name: item.provider,
          value: item.count
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    aiChart.setOption(aiOption)
  }
}

const getOperationTypeTag = (type: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const tagMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    '创建': 'success',
    '编辑': 'warning',
    '删除': 'danger',
    '查看': 'info'
  }
  return tagMap[type] || 'info'
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(async () => {
  await loadStats()
  await loadRecentLogs()
  await initCharts()
  
  // 监听窗口大小变化，重新调整图表
  window.addEventListener('resize', () => {
    userChart?.resize()
    aiChart?.resize()
  })
})

// 组件卸载时销毁图表
onUnmounted(() => {
  userChart?.dispose()
  aiChart?.dispose()
  window.removeEventListener('resize', () => {
    userChart?.resize()
    aiChart?.resize()
  })
})
</script>

<style scoped lang="scss">
.stats-row {
  margin-bottom: 20px;
}

.charts-row {
  margin-bottom: 20px;
}

.logs-row {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
  
  .stat-content {
    display: flex;
    align-items: center;
    height: 100%;
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      
      .el-icon {
        font-size: 24px;
        color: white;
      }
      
      &.user-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.account-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.ai-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.template-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
    
    .stat-info {
      flex: 1;
      
      .stat-title {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 4px;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;
      }
      
      .stat-trend {
        display: flex;
        align-items: center;
        font-size: 12px;
        
        &.up {
          color: #10b981;
        }
        
        &.down {
          color: #ef4444;
        }
        
        .el-icon {
          margin-right: 2px;
        }
      }
    }
  }
}

.chart-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .chart-container {
    height: 300px;
    width: 100%;
  }
}

.logs-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stats-row {
    .el-col {
      margin-bottom: 12px;
    }
  }
  
  .charts-row {
    .el-col {
      margin-bottom: 20px;
    }
  }
  
  .stat-card {
    .stat-content {
      .stat-icon {
        width: 48px;
        height: 48px;
        margin-right: 12px;
        
        .el-icon {
          font-size: 20px;
        }
      }
      
      .stat-info {
        .stat-value {
          font-size: 20px;
        }
      }
    }
  }
  
  .chart-container {
    height: 250px !important;
  }
}
</style>
