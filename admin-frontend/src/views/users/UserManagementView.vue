<template>
  <div class="user-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">用户管理</h1>
      <p class="page-description">管理系统中的所有用户账号</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="search-bar">
      <div class="search-left">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索用户（手机号、姓名、昵称）"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="searchForm.accountSetsId"
          placeholder="选择账套"
          style="width: 200px; margin-left: 10px"
          clearable
        >
          <el-option
            v-for="accountSets in accountSetsList"
            :key="accountSets.id"
            :label="accountSets.companyName"
            :value="accountSets.id"
          />
        </el-select>
        
        <el-button type="primary" @click="handleSearch" style="margin-left: 10px">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="handleReset" style="margin-left: 10px">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      
      <div class="search-right">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新建用户
        </el-button>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="mobile" label="手机号" width="120" />
        
        <el-table-column prop="realName" label="真实姓名" width="100" />
        
        <el-table-column prop="nickname" label="昵称" width="100" />
        
        <el-table-column prop="email" label="邮箱" width="180" />
        
        <el-table-column label="主账套" width="200">
          <template #default="scope">
            <span v-if="scope.row.accountSetsName">{{ scope.row.accountSetsName }}</span>
            <span v-else class="text-gray-400">未设置</span>
          </template>
        </el-table-column>
        
        <el-table-column label="角色" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.role" :type="getRoleTagType(scope.row.role)">
              {{ getRoleDisplayName(scope.row.role) }}
            </el-tag>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="账套数量" width="100">
          <template #default="scope">
            <el-badge :value="scope.row.accountSetsCount" class="item">
              <el-icon><Files /></el-icon>
            </el-badge>
          </template>
        </el-table-column>
        
        <el-table-column prop="createDate" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.createDate) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button link size="small" @click="handleView(scope.row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button link size="small" @click="handleEdit(scope.row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button link size="small" @click="handleResetPassword(scope.row)">
              <el-icon><Key /></el-icon>
              重置密码
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="用户详情"
      width="800px"
      :before-close="handleDetailDialogClose"
    >
      <div v-if="currentUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ currentUser.id }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ currentUser.mobile }}</el-descriptions-item>
          <el-descriptions-item label="真实姓名">{{ currentUser.realName }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ currentUser.nickname }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentUser.email || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentUser.createDate) }}</el-descriptions-item>
        </el-descriptions>
        
        <h3 style="margin-top: 20px; margin-bottom: 10px">账套权限</h3>
        <el-table :data="currentUser.accountSetsList" style="width: 100%">
          <el-table-column prop="companyName" label="公司名称" />
          <el-table-column label="角色">
            <template #default="scope">
              <el-tag :type="getRoleTagType(scope.row.role)">
                {{ getRoleDisplayName(scope.row.role) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="enableDate" label="启用日期">
            <template #default="scope">
              {{ formatDate(scope.row.enableDate) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEdit ? '编辑用户' : '新建用户'"
      width="600px"
      :before-close="handleFormDialogClose"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
      >
        <el-form-item label="手机号" prop="mobile">
          <el-input
            v-model="userForm.mobile"
            placeholder="请输入手机号"
            :disabled="isEdit"
          />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="userForm.nickname" placeholder="请输入昵称" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item v-if="!isEdit" label="密码" prop="password">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码（至少6位）"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="主账套" prop="accountSetsId">
          <el-select
            v-model="userForm.accountSetsId"
            placeholder="选择主账套"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="accountSets in accountSetsList"
              :key="accountSets.id"
              :label="accountSets.companyName"
              :value="accountSets.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="userForm.accountSetsId" label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="选择角色" style="width: 100%">
            <el-option label="管理员" value="Manager" />
            <el-option label="会计" value="Accountant" />
            <el-option label="出纳" value="Cashier" />
            <el-option label="查看者" value="View" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  View,
  Edit,
  Key,
  Files
} from '@element-plus/icons-vue'
import { userApi, accountSetsApi } from '@/api'
import type { User, AccountSet, UserForm, CreateUserData } from '@/types'
import { formatDate as utilFormatDate } from '@/utils'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const userList = ref([])
const accountSetsList = ref([])
const selectedUsers = ref([])
const currentUser = ref(null)
const currentEditUserId = ref(null)

// 计算属性用于类型安全
const typedCurrentUser = computed(() => currentUser.value)
const typedAccountSetsList = computed(() => accountSetsList.value)

// 对话框状态
const detailDialogVisible = ref(false)
const formDialogVisible = ref(false)
const isEdit = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  accountSetsId: null
})

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 用户表单
const userForm = reactive({
  mobile: '',
  realName: '',
  nickname: '',
  email: '',
  password: '',
  accountSetsId: null,
  role: ''
})

const userFormRef = ref<FormInstance>()

// 表单验证规则
const userFormRules: FormRules = {
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 方法
const loadUserList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword || undefined,
      accountSetsId: searchForm.accountSetsId || undefined
    }
    
    const response = await userApi.getUserList(params)
    if (response.success) {
      userList.value = response.data.list
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const loadAccountSetsList = async () => {
  try {
    const response = await accountSetsApi.getAccountSetsList({ page: 1, size: 1000 })
    if (response.success) {
      accountSetsList.value = response.data.list
    }
  } catch (error) {
    console.error('获取账套列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadUserList()
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.accountSetsId = null
  pagination.current = 1
  loadUserList()
}

const handleCreate = () => {
  isEdit.value = false
  resetUserForm()
  formDialogVisible.value = true
}

const handleView = async (user: any) => {
  try {
    const response = await userApi.getUserDetail(user.id)
    if (response.success) {
      currentUser.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取用户详情失败')
    }
  } catch (error) {
    ElMessage.error('获取用户详情失败')
  }
}

const handleEdit = (user: any) => {
  isEdit.value = true
  userForm.mobile = user.mobile
  userForm.realName = user.realName
  userForm.nickname = user.nickname
  userForm.email = user.email
  userForm.accountSetsId = user.accountSetsId
  userForm.role = user.role
  currentEditUserId.value = user.id
  formDialogVisible.value = true
}

const handleResetPassword = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 ${user.realName}(${user.mobile}) 的密码吗？`,
      '重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await userApi.resetPassword(user.id)
    if (response.success) {
      ElMessageBox.alert(
        `新密码：${response.data.newPassword}`,
        '密码重置成功',
        {
          confirmButtonText: '确定',
          type: 'success'
        }
      )
    } else {
      ElMessage.error(response.message || '重置密码失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置密码失败')
    }
  }
}

const handleSubmit = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    submitting.value = true
    
    let response
    if (isEdit.value) {
      response = await userApi.updateUser(currentEditUserId.value, userForm)
    } else {
      response = await userApi.createUser(userForm)
    }
    
    if (response.success) {
      ElMessage.success(isEdit.value ? '用户更新成功' : '用户创建成功')
      formDialogVisible.value = false
      loadUserList()
    } else {
      ElMessage.error(response.message || (isEdit.value ? '更新用户失败' : '创建用户失败'))
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

const resetUserForm = () => {
  userForm.mobile = ''
  userForm.realName = ''
  userForm.nickname = ''
  userForm.email = ''
  userForm.password = ''
  userForm.accountSetsId = null
  userForm.role = ''
  currentEditUserId.value = null
}

const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  loadUserList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadUserList()
}

const handleDetailDialogClose = () => {
  detailDialogVisible.value = false
  currentUser.value = null
}

const handleFormDialogClose = () => {
  formDialogVisible.value = false
  resetUserForm()
}

const getRoleTagType = (role: string) => {
  const roleTypes = {
    Manager: 'danger',
    Accountant: 'primary',
    Cashier: 'warning',
    View: 'info'
  }
  return roleTypes[role] || 'info'
}

const getRoleDisplayName = (role: string) => {
  const roleNames = {
    Manager: '管理员',
    Accountant: '会计',
    Cashier: '出纳',
    View: '查看者'
  }
  return roleNames[role] || role
}

const formatDate = (date: string) => {
  if (!date) return '-'
  return utilFormatDate(date)
}

// 生命周期
onMounted(() => {
  loadUserList()
  loadAccountSetsList()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  color: #606266;
  margin: 0;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-left {
  display: flex;
  align-items: center;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-container {
  padding: 16px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.user-detail {
  padding: 10px 0;
}

.text-gray-400 {
  color: #9ca3af;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
