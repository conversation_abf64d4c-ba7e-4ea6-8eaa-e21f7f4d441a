<template>
  <div class="page-container">
    <div class="page-header">
      <h2>字段映射模板管理</h2>
      <p class="page-description">管理OCR字段到标准字段的映射模板，提高识别准确率</p>
    </div>

    <div class="content-card">
      <!-- 搜索和操作区域 -->
      <div class="toolbar-section">
        <div class="search-area">
          <el-form :model="searchForm" inline class="search-form">
            <el-form-item label="模板名称">
              <el-input
                v-model="searchForm.templateName"
                placeholder="请输入模板名称"
                style="width: 200px"
                clearable
              />
            </el-form-item>
            <el-form-item label="单据类型">
              <el-select
                v-model="searchForm.documentType"
                placeholder="请选择单据类型"
                style="width: 150px"
                clearable
              >
                <el-option label="银行回单" value="BANK_RECEIPT" />
                <el-option label="发票" value="INVOICE" />
              </el-select>
            </el-form-item>
            <el-form-item label="银行标识">
              <el-input
                v-model="searchForm.bankIdentifier"
                placeholder="请输入银行标识"
                style="width: 150px"
                clearable
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="searchForm.isActive"
                placeholder="请选择状态"
                style="width: 120px"
                clearable
              >
                <el-option label="启用" :value="true" />
                <el-option label="禁用" :value="false" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchTemplates">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="resetSearch">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="action-area">
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            创建模板
          </el-button>
          <el-button
            type="warning"
            :disabled="!selectedTemplates.length"
            @click="batchToggleStatus"
          >
            <el-icon><Switch /></el-icon>
            批量切换状态
          </el-button>
          <el-button
            type="danger"
            :disabled="!selectedTemplates.length"
            @click="batchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button type="success" @click="exportTemplates">
            <el-icon><Download /></el-icon>
            导出模板
          </el-button>
          <el-upload
            ref="uploadRef"
            :show-file-list="false"
            :before-upload="importTemplates"
            accept=".json"
            style="display: inline-block"
          >
            <el-button type="info">
              <el-icon><Upload /></el-icon>
              导入模板
            </el-button>
          </el-upload>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总模板数" :value="statistics.total" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="启用模板" :value="statistics.active" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="平均成功率" :value="statistics.avgSuccessRate" suffix="%" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="本月使用次数" :value="statistics.monthlyUsage" />
          </el-col>
        </el-row>
      </div>

      <!-- 模板列表表格 -->
      <div class="table-section">
        <el-table
          :data="templateList"
          v-loading="loading"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="templateName" label="模板名称" min-width="200" />
          <el-table-column prop="documentType" label="单据类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getDocumentTypeColor(row.documentType)">
                {{ getDocumentTypeText(row.documentType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="bankIdentifier" label="银行标识" width="150" />
          <el-table-column prop="receiptType" label="回单类型" width="150" show-overflow-tooltip />
          <el-table-column prop="fieldCount" label="字段数" width="80" />
          <el-table-column prop="successRate" label="成功率" width="100">
            <template #default="{ row }">
              <el-progress
                :percentage="parseFloat(row.successRate || 0)"
                :color="getSuccessRateColor(row.successRate)"
                :stroke-width="8"
                text-inside
              />
            </template>
          </el-table-column>
          <el-table-column prop="usageCount" label="使用次数" width="100" />
          <el-table-column prop="isActive" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.isActive ? 'success' : 'danger'">
                {{ row.isActive ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="lastUsedTime" label="最后使用" width="160" />
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="viewTemplate(row)"
              >
                查看
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="editTemplate(row)"
              >
                编辑
              </el-button>
              <el-button
                :type="row.isActive ? 'warning' : 'success'"
                size="small"
                @click="toggleStatus(row)"
              >
                {{ row.isActive ? '禁用' : '启用' }}
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="testTemplate(row)"
              >
                测试
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteTemplate(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑模板对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form
        ref="templateFormRef"
        :model="templateForm"
        :rules="templateRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input
                v-model="templateForm.templateName"
                placeholder="请输入模板名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单据类型" prop="documentType">
              <el-select
                v-model="templateForm.documentType"
                placeholder="请选择单据类型"
                style="width: 100%"
              >
                <el-option label="银行回单" value="BANK_RECEIPT" />
                <el-option label="发票" value="INVOICE" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="银行标识" prop="bankIdentifier">
              <el-input
                v-model="templateForm.bankIdentifier"
                placeholder="请输入银行标识"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回单类型" prop="receiptType">
              <el-input
                v-model="templateForm.receiptType"
                placeholder="请输入回单类型"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="字段映射规则" prop="mappingRules">
          <el-input
            v-model="templateForm.mappingRules"
            type="textarea"
            :rows="8"
            placeholder="请输入JSON格式的字段映射规则"
          />
          <div class="form-tip">
            示例：{"发票号码": "invoice_number", "开票日期": "invoice_date"}
          </div>
        </el-form-item>

        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="templateForm.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="saveLoading"
          @click="saveTemplate"
        >
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 查看模板详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="模板详情"
      width="900px"
    >
      <div v-if="viewingTemplate" class="template-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板ID">
            {{ viewingTemplate.id }}
          </el-descriptions-item>
          <el-descriptions-item label="模板名称">
            {{ viewingTemplate.templateName }}
          </el-descriptions-item>
          <el-descriptions-item label="单据类型">
            <el-tag :type="getDocumentTypeColor(viewingTemplate.documentType)">
              {{ getDocumentTypeText(viewingTemplate.documentType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="银行标识">
            {{ viewingTemplate.bankIdentifier }}
          </el-descriptions-item>
          <el-descriptions-item label="回单类型">
            {{ viewingTemplate.receiptType }}
          </el-descriptions-item>
          <el-descriptions-item label="字段数量">
            {{ viewingTemplate.fieldCount }}
          </el-descriptions-item>
          <el-descriptions-item label="成功率">
            {{ viewingTemplate.successRate }}%
          </el-descriptions-item>
          <el-descriptions-item label="使用次数">
            {{ viewingTemplate.usageCount }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="viewingTemplate.isActive ? 'success' : 'danger'">
              {{ viewingTemplate.isActive ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ viewingTemplate.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="最后使用">
            {{ viewingTemplate.lastUsedTime || '未使用' }}
          </el-descriptions-item>
          <el-descriptions-item label="字段签名">
            {{ viewingTemplate.fieldSignature }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="mapping-rules-section">
          <h3>字段映射规则</h3>
          <pre class="json-display">{{ formatJson(viewingTemplate.mappingRules) }}</pre>
        </div>

        <div v-if="viewingTemplate.sampleOcrData" class="sample-data-section">
          <h3>样本OCR数据</h3>
          <pre class="json-display">{{ formatJson(viewingTemplate.sampleOcrData) }}</pre>
        </div>
      </div>
    </el-dialog>

    <!-- 测试模板对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="测试模板"
      width="700px"
    >
      <div class="test-section">
        <el-form :model="testForm" label-width="120px">
          <el-form-item label="测试数据">
            <el-input
              v-model="testForm.testData"
              type="textarea"
              :rows="6"
              placeholder="请输入JSON格式的OCR测试数据"
            />
          </el-form-item>
        </el-form>

        <div class="test-result" v-if="testResult">
          <h4>测试结果：</h4>
          <pre class="json-display">{{ formatJson(testResult) }}</pre>
        </div>
      </div>

      <template #footer>
        <el-button @click="testDialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          :loading="testLoading"
          @click="runTest"
        >
          运行测试
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules, type UploadInstance } from 'element-plus'
import { Search, Refresh, Plus, Switch, Delete, Download, Upload } from '@element-plus/icons-vue'
import { request } from '@/utils/request'

// 类型定义
interface FieldMappingTemplate {
  id?: number
  templateName: string
  documentType: string
  bankIdentifier: string
  receiptType: string
  fieldCount?: number
  fieldSignature?: string
  mappingRules: string
  sampleOcrData?: string
  usageCount?: number
  successRate?: string
  lastUsedTime?: string
  isActive: boolean
  createTime?: string
}

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const testLoading = ref(false)
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const testDialogVisible = ref(false)
const isEdit = ref(false)
const templateFormRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()

// 搜索表单
const searchForm = reactive({
  templateName: '',
  documentType: '',
  bankIdentifier: '',
  isActive: null as boolean | null
})

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 统计信息
const statistics = reactive({
  total: 0,
  active: 0,
  avgSuccessRate: 0,
  monthlyUsage: 0
})

// 模板表单
const templateForm = reactive<FieldMappingTemplate>({
  templateName: '',
  documentType: '',
  bankIdentifier: '',
  receiptType: '',
  mappingRules: '',
  isActive: true
})

// 测试表单
const testForm = reactive({
  testData: ''
})

// 选中的模板
const selectedTemplates = ref<FieldMappingTemplate[]>([])
const viewingTemplate = ref<FieldMappingTemplate | null>(null)
const testResult = ref('')

// 模板列表
const templateList = ref<FieldMappingTemplate[]>([])

// 表单验证规则
const templateRules: FormRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  documentType: [
    { required: true, message: '请选择单据类型', trigger: 'change' }
  ],
  mappingRules: [
    { required: true, message: '请输入字段映射规则', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        try {
          JSON.parse(value)
          callback()
        } catch {
          callback(new Error('字段映射规则必须是有效的JSON格式'))
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑模板' : '创建模板'
})

// 方法
const loadTemplates = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params: any = {
      page: pagination.current,
      size: pagination.size
    }

    // 添加搜索条件
    if (searchForm.templateName) {
      params.templateName = searchForm.templateName
    }
    if (searchForm.documentType) {
      params.documentType = searchForm.documentType
    }
    if (searchForm.bankIdentifier) {
      params.bankIdentifier = searchForm.bankIdentifier
    }
    if (searchForm.isActive !== null) {
      params.isActive = searchForm.isActive
    }

    // 调用API获取模板列表
    const result = await request.get('/admin/field-mapping-template/list', { params })

    templateList.value = result.data.list || []
    pagination.total = result.data.total || 0

    // 更新统计信息
    statistics.total = result.data.total || 0
    statistics.active = templateList.value.filter(t => t.isActive).length
    statistics.avgSuccessRate = templateList.value.length > 0
      ? Math.round(templateList.value.reduce((sum, t) => sum + parseFloat(t.successRate || '0'), 0) / templateList.value.length)
      : 0
    statistics.monthlyUsage = templateList.value.reduce((sum, t) => sum + (t.usageCount || 0), 0)

  } catch (error) {
    console.error('Load templates error:', error)
    ElMessage.error('加载模板列表失败')
  } finally {
    loading.value = false
  }
}

const searchTemplates = () => {
  pagination.current = 1
  loadTemplates()
}

const resetSearch = () => {
  searchForm.templateName = ''
  searchForm.documentType = ''
  searchForm.bankIdentifier = ''
  searchForm.isActive = null
  pagination.current = 1
  loadTemplates()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadTemplates()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadTemplates()
}

const handleSelectionChange = (selection: FieldMappingTemplate[]) => {
  selectedTemplates.value = selection
}

const showCreateDialog = () => {
  isEdit.value = false
  resetTemplateForm()
  dialogVisible.value = true
}

const editTemplate = (template: FieldMappingTemplate) => {
  isEdit.value = true
  Object.assign(templateForm, {
    id: template.id,
    templateName: template.templateName,
    documentType: template.documentType,
    bankIdentifier: template.bankIdentifier,
    receiptType: template.receiptType,
    mappingRules: template.mappingRules,
    isActive: template.isActive
  })
  dialogVisible.value = true
}

const viewTemplate = (template: FieldMappingTemplate) => {
  viewingTemplate.value = template
  viewDialogVisible.value = true
}

const testTemplate = (template: FieldMappingTemplate) => {
  viewingTemplate.value = template
  testForm.testData = template.sampleOcrData || ''
  testResult.value = ''
  testDialogVisible.value = true
}

const handleDialogClose = () => {
  resetTemplateForm()
  if (templateFormRef.value) {
    templateFormRef.value.resetFields()
  }
}

const resetTemplateForm = () => {
  Object.assign(templateForm, {
    id: undefined,
    templateName: '',
    documentType: '',
    bankIdentifier: '',
    receiptType: '',
    mappingRules: '',
    isActive: true
  })
}

const saveTemplate = async () => {
  if (!templateFormRef.value) return

  try {
    await templateFormRef.value.validate()
    saveLoading.value = true

    const templateData = {
      templateName: templateForm.templateName,
      documentType: templateForm.documentType,
      bankIdentifier: templateForm.bankIdentifier,
      receiptType: templateForm.receiptType,
      mappingRules: templateForm.mappingRules,
      isActive: templateForm.isActive
    }

    if (isEdit.value) {
      // 编辑模板
      await request.put(`/admin/field-mapping-template/${templateForm.id}`, templateData)
    } else {
      // 创建模板
      await request.post('/admin/field-mapping-template', templateData)
    }

    ElMessage.success(isEdit.value ? '模板更新成功' : '模板创建成功')
    dialogVisible.value = false
    loadTemplates()

  } catch (error) {
    console.error('Save template error:', error)
    ElMessage.error('保存模板失败')
  } finally {
    saveLoading.value = false
  }
}

const toggleStatus = async (template: FieldMappingTemplate) => {
  const action = template.isActive ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}模板 "${template.templateName}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await request.put(`/admin/field-mapping-template/${template.id}/toggle-status`)

    ElMessage.success(`模板${action}成功`)
    loadTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Toggle status error:', error)
      ElMessage.error(`模板${action}失败`)
    }
  }
}

const deleteTemplate = async (template: FieldMappingTemplate) => {
  try {
    await ElMessageBox.confirm(`确定要删除模板 "${template.templateName}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await request.delete(`/admin/field-mapping-template/${template.id}`)

    ElMessage.success('模板删除成功')
    loadTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete template error:', error)
      ElMessage.error('模板删除失败')
    }
  }
}

const batchToggleStatus = () => {
  ElMessageBox.confirm(`确定要批量切换 ${selectedTemplates.value.length} 个模板的状态吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    selectedTemplates.value.forEach(template => {
      template.isActive = !template.isActive
    })
    ElMessage.success('批量状态切换成功')
    loadTemplates()
  })
}

const batchDelete = () => {
  ElMessageBox.confirm(`确定要批量删除 ${selectedTemplates.value.length} 个模板吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const idsToDelete = selectedTemplates.value.map(t => t.id)
    templateList.value = templateList.value.filter(t => !idsToDelete.includes(t.id))
    ElMessage.success('批量删除成功')
    loadTemplates()
  })
}

const exportTemplates = () => {
  const dataToExport = selectedTemplates.value.length > 0 ? selectedTemplates.value : templateList.value
  const jsonData = JSON.stringify(dataToExport, null, 2)
  const blob = new Blob([jsonData], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `field_mapping_templates_${new Date().toISOString().slice(0, 10)}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('模板导出成功')
}

const importTemplates = (file: File) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const importedData = JSON.parse(e.target?.result as string)
      if (Array.isArray(importedData)) {
        importedData.forEach(template => {
          template.id = Date.now() + Math.random() // 生成新ID
          templateList.value.push(template)
        })
        ElMessage.success(`成功导入 ${importedData.length} 个模板`)
        loadTemplates()
      } else {
        ElMessage.error('导入文件格式不正确')
      }
    } catch (error) {
      ElMessage.error('导入文件解析失败')
    }
  }
  reader.readAsText(file)
  return false // 阻止默认上传行为
}

const runTest = async () => {
  if (!testForm.testData.trim()) {
    ElMessage.error('请输入测试数据')
    return
  }

  try {
    testLoading.value = true
    JSON.parse(testForm.testData) // 验证JSON格式

    // TODO: 调用API测试模板
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    // 模拟测试结果
    testResult.value = JSON.stringify({
      success: true,
      mappedData: {
        invoice_number: 'INV001',
        invoice_date: '2024-01-15',
        amount: '1000.00'
      },
      confidence: 0.95
    }, null, 2)

    ElMessage.success('模板测试完成')
  } catch (error) {
    ElMessage.error('测试数据格式不正确')
  } finally {
    testLoading.value = false
  }
}

const getDocumentTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    BANK_RECEIPT: '银行回单',
    INVOICE: '发票'
  }
  return typeMap[type] || type
}

const getDocumentTypeColor = (type: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const colorMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    BANK_RECEIPT: 'primary',
    INVOICE: 'success'
  }
  return colorMap[type] || 'info'
}

const getSuccessRateColor = (rate: string | undefined) => {
  const rateNum = parseFloat(rate || '0')
  if (rateNum >= 90) return '#67c23a'
  if (rateNum >= 70) return '#e6a23c'
  return '#f56c6c'
}

const formatJson = (jsonStr: string) => {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch {
    return jsonStr
  }
}

// 生命周期
onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
.page-description {
  color: #666;
  margin-top: 8px;
  margin-bottom: 0;
}

.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.search-area {
  flex: 1;
}

.search-form {
  margin: 0;
}

.action-area {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.stats-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.table-section {
  margin-top: 20px;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.template-detail {
  padding: 10px 0;
}

.mapping-rules-section,
.sample-data-section {
  margin-top: 20px;
}

.mapping-rules-section h3,
.sample-data-section h3 {
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 500;
}

.json-display {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
  border: 1px solid #e4e7ed;
}

.test-section {
  padding: 10px 0;
}

.test-result {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.test-result h4 {
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 500;
}
</style>
