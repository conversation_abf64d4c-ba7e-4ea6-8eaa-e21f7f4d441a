<template>
  <div class="batch-task-management">
    <!-- 页面标题和统计信息 -->
    <div class="page-header">
      <div class="header-content">
        <h1>批量任务管理</h1>
        <p class="description">管理系统中的所有批量导入任务，包括继续、删除和重新执行操作</p>
      </div>
      
      <!-- 统计卡片 -->
      <div class="statistics-cards" v-if="statistics">
        <div class="stat-card">
          <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
          <div class="stat-label">总任务数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ statistics.todayCount || 0 }}</div>
          <div class="stat-label">今日任务</div>
        </div>
        <div class="stat-card stuck">
          <div class="stat-number">{{ statistics.stuckCount || 0 }}</div>
          <div class="stat-label">卡住任务</div>
        </div>
      </div>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="search-bar">
      <div class="search-filters">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索任务名称或ID"
          style="width: 200px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="searchForm.status"
          placeholder="任务状态"
          style="width: 150px"
          clearable
        >
          <el-option label="上传中" value="UPLOADING" />
          <el-option label="处理中" value="PROCESSING" />
          <el-option label="识别中" value="RECOGNIZING" />
          <el-option label="预览中" value="PREVIEWING" />
          <el-option label="保存中" value="SAVING" />
          <el-option label="已完成" value="COMPLETED" />
          <el-option label="部分成功" value="PARTIAL_SUCCESS" />
          <el-option label="失败" value="FAILED" />
          <el-option label="已取消" value="CANCELLED" />
        </el-select>
        
        <el-select
          v-model="searchForm.importType"
          placeholder="导入类型"
          style="width: 120px"
          clearable
        >
          <el-option label="银行回单" value="BANK_RECEIPT" />
          <el-option label="发票" value="INVOICE" />
        </el-select>
        
        <el-input
          v-model="searchForm.accountSetsId"
          placeholder="账套ID"
          style="width: 100px"
          clearable
        />
        
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      
      <div class="action-buttons">
        <el-button type="warning" @click="handleCleanupStuck">
          <el-icon><Delete /></el-icon>
          清理卡住任务
        </el-button>
        
        <el-button @click="loadData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="table-container">
      <el-table
        :data="taskList"
        v-loading="loading"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="taskId" label="任务ID" width="280" show-overflow-tooltip />
        
        <el-table-column prop="taskName" label="任务名称" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="importType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.importType === 'BANK_RECEIPT' ? 'primary' : 'success'">
              {{ row.importType === 'BANK_RECEIPT' ? '银行回单' : '发票' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="progressPercentage" label="进度" width="100">
          <template #default="{ row }">
            <el-progress
              :percentage="Number(row.progressPercentage || 0)"
              :status="getProgressStatus(row.status)"
              :stroke-width="6"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="totalFiles" label="文件数" width="80" />
        <el-table-column prop="totalImages" label="图片数" width="80" />
        <el-table-column prop="successCount" label="成功" width="70" />
        <el-table-column prop="failedCount" label="失败" width="70" />
        <el-table-column prop="accountSetsId" label="账套ID" width="80" />
        
        <el-table-column prop="createdTime" label="创建时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.createdTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="updatedTime" label="更新时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.updatedTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                size="small"
                @click="handleViewDetail(row)"
              >
                详情
              </el-button>
              
              <el-button
                v-if="canContinue(row.status)"
                size="small"
                type="primary"
                @click="handleContinue(row)"
              >
                继续
              </el-button>
              
              <el-button
                v-if="canRetry(row.status)"
                size="small"
                type="warning"
                @click="handleRetry(row)"
              >
                重试
              </el-button>
              
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 任务详情弹窗 -->
    <TaskDetailDialog
      v-model:visible="detailDialogVisible"
      :task-id="selectedTaskId"
      @refresh="loadData"
    />

    <!-- 清理卡住任务弹窗 -->
    <CleanupDialog
      v-model:visible="cleanupDialogVisible"
      @success="loadData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Delete } from '@element-plus/icons-vue'
import TaskDetailDialog from './components/TaskDetailDialog.vue'
import CleanupDialog from './components/CleanupDialog.vue'
import { batchTaskApi } from '@/api/batch-task'

// 响应式数据
const loading = ref(false)
const taskList = ref([])
const statistics = ref(null)
const detailDialogVisible = ref(false)
const cleanupDialogVisible = ref(false)
const selectedTaskId = ref('')

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  importType: '',
  accountSetsId: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 排序信息
const sortInfo = reactive({
  prop: '',
  order: ''
})

// 计算属性
const canContinue = (status: string) => {
  return ['PROCESSING', 'RECOGNIZING', 'FAILED', 'CANCELLED'].includes(status)
}

const canRetry = (status: string) => {
  return ['FAILED', 'CANCELLED', 'PARTIAL_SUCCESS'].includes(status)
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const response = await batchTaskApi.getTaskList(params)
    taskList.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await batchTaskApi.getStatistics()
    statistics.value = response.data
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    importType: '',
    accountSetsId: ''
  })
  pagination.current = 1
  loadData()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadData()
}

const handleSortChange = ({ prop, order }: any) => {
  sortInfo.prop = prop
  sortInfo.order = order
  loadData()
}

const handleViewDetail = (row: any) => {
  selectedTaskId.value = row.taskId
  detailDialogVisible.value = true
}

const handleContinue = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要继续任务 "${row.taskName}" 吗？`,
      '确认继续',
      { type: 'warning' }
    )
    
    await batchTaskApi.continueTask(row.taskId)
    ElMessage.success('任务已重新启动')
    loadData()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '继续任务失败')
    }
  }
}

const handleRetry = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要重新执行任务 "${row.taskName}" 吗？这将重置所有处理进度。`,
      '确认重试',
      { type: 'warning' }
    )
    
    await batchTaskApi.retryTask(row.taskId)
    ElMessage.success('任务已重新执行')
    loadData()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '重试任务失败')
    }
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${row.taskName}" 吗？此操作不可恢复。`,
      '确认删除',
      { type: 'error' }
    )
    
    await batchTaskApi.deleteTask(row.taskId)
    ElMessage.success('任务已删除')
    loadData()
    loadStatistics()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除任务失败')
    }
  }
}

const handleCleanupStuck = () => {
  cleanupDialogVisible.value = true
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'UPLOADING': 'info',
    'PROCESSING': 'warning',
    'RECOGNIZING': 'warning',
    'PREVIEWING': 'primary',
    'SAVING': 'warning',
    'COMPLETED': 'success',
    'PARTIAL_SUCCESS': 'warning',
    'FAILED': 'danger',
    'CANCELLED': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'UPLOADING': '上传中',
    'PROCESSING': '处理中',
    'RECOGNIZING': '识别中',
    'PREVIEWING': '预览中',
    'SAVING': '保存中',
    'COMPLETED': '已完成',
    'PARTIAL_SUCCESS': '部分成功',
    'FAILED': '失败',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const getProgressStatus = (status: string) => {
  if (status === 'COMPLETED') return 'success'
  if (status === 'FAILED') return 'exception'
  return undefined
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadData()
  loadStatistics()
})
</script>

<style scoped>
.batch-task-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.header-content h1 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
}

.header-content .description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.statistics-cards {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  min-width: 120px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.stuck {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-filters {
  display: flex;
  gap: 15px;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table) {
  border-radius: 8px 8px 0 0;
}

:deep(.el-table th) {
  background: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-progress-bar__outer) {
  border-radius: 3px;
}

:deep(.el-button-group .el-button) {
  margin: 0;
}
</style>
