<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务详情"
    width="80%"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="task-detail">
      <div v-if="taskDetail" class="detail-content">
        <!-- 任务基本信息 -->
        <div class="task-info">
          <h3>基本信息</h3>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="任务ID">
              {{ taskDetail.task.taskId }}
            </el-descriptions-item>
            <el-descriptions-item label="任务名称">
              {{ taskDetail.task.taskName }}
            </el-descriptions-item>
            <el-descriptions-item label="导入类型">
              <el-tag :type="taskDetail.task.importType === 'BANK_RECEIPT' ? 'primary' : 'success'">
                {{ taskDetail.task.importType === 'BANK_RECEIPT' ? '银行回单' : '发票' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="任务状态">
              <el-tag :type="getStatusType(taskDetail.task.status)">
                {{ getStatusText(taskDetail.task.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="进度">
              <el-progress
                :percentage="Number(taskDetail.task.progressPercentage || 0)"
                :status="getProgressStatus(taskDetail.task.status)"
              />
            </el-descriptions-item>
            <el-descriptions-item label="账套ID">
              {{ taskDetail.task.accountSetsId }}
            </el-descriptions-item>
            <el-descriptions-item label="总文件数">
              {{ taskDetail.task.totalFiles }}
            </el-descriptions-item>
            <el-descriptions-item label="总图片数">
              {{ taskDetail.task.totalImages }}
            </el-descriptions-item>
            <el-descriptions-item label="成功数量">
              <span class="success-count">{{ taskDetail.task.successCount }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="失败数量">
              <span class="failed-count">{{ taskDetail.task.failedCount }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(taskDetail.task.createdTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDateTime(taskDetail.task.updatedTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="开始时间" v-if="taskDetail.task.startTime">
              {{ formatDateTime(taskDetail.task.startTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="结束时间" v-if="taskDetail.task.endTime">
              {{ formatDateTime(taskDetail.task.endTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="错误信息" v-if="taskDetail.task.errorMessage" :span="3">
              <el-text type="danger">{{ taskDetail.task.errorMessage }}</el-text>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 明细状态统计 -->
        <div class="detail-stats" v-if="taskDetail.detailStatusCount">
          <h3>明细状态统计</h3>
          <div class="stats-cards">
            <div class="stat-card" v-for="(count, status) in taskDetail.detailStatusCount" :key="status">
              <div class="stat-number">{{ count }}</div>
              <div class="stat-label">{{ getDetailStatusText(status) }}</div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button
            v-if="canContinue(taskDetail.task.status)"
            type="primary"
            @click="handleContinue"
          >
            继续任务
          </el-button>
          
          <el-button
            v-if="canRetry(taskDetail.task.status)"
            type="warning"
            @click="handleRetry"
          >
            重新执行
          </el-button>
          
          <el-button
            type="info"
            @click="handleUpdateStatus"
          >
            更新状态
          </el-button>
          
          <el-button
            type="danger"
            @click="handleDelete"
          >
            删除任务
          </el-button>
        </div>

        <!-- 明细列表 -->
        <div class="detail-list">
          <h3>处理明细 ({{ taskDetail.totalDetails }})</h3>
          <el-table
            :data="taskDetail.details"
            stripe
            max-height="400"
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="fileName" label="文件名" min-width="200" show-overflow-tooltip />
            <el-table-column prop="fileType" label="类型" width="80">
              <template #default="{ row }">
                <el-tag size="small" :type="row.fileType === 'PDF' ? 'primary' : 'success'">
                  {{ row.fileType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag size="small" :type="getDetailStatusType(row.status)">
                  {{ getDetailStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="pageNumber" label="页码" width="80" />
            <el-table-column prop="subImageIndex" label="子图" width="80" />
            <el-table-column prop="confidenceScore" label="置信度" width="100">
              <template #default="{ row }">
                <span v-if="row.confidenceScore">
                  {{ (Number(row.confidenceScore) * 100).toFixed(1) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="retryCount" label="重试次数" width="100" />
            <el-table-column prop="processingTime" label="处理时间(ms)" width="120" />
            <el-table-column prop="updatedTime" label="更新时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.updatedTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="errorMessage" label="错误信息" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <el-text v-if="row.errorMessage" type="danger">{{ row.errorMessage }}</el-text>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="loadTaskDetail">刷新</el-button>
    </template>

    <!-- 更新状态弹窗 -->
    <UpdateStatusDialog
      v-model:visible="updateStatusVisible"
      :task-id="taskId"
      :current-status="taskDetail?.task?.status"
      @success="handleUpdateSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import UpdateStatusDialog from './UpdateStatusDialog.vue'
import { batchTaskApi } from '@/api/batch-task'

// Props
interface Props {
  visible: boolean
  taskId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const taskDetail = ref<any>(null)
const updateStatusVisible = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const canContinue = (status: string) => {
  return ['PROCESSING', 'RECOGNIZING', 'FAILED', 'CANCELLED'].includes(status)
}

const canRetry = (status: string) => {
  return ['FAILED', 'CANCELLED', 'PARTIAL_SUCCESS'].includes(status)
}

// 方法
const loadTaskDetail = async () => {
  if (!props.taskId) return
  
  loading.value = true
  try {
    const response = await batchTaskApi.getTaskDetail(props.taskId)
    taskDetail.value = response.data
  } catch (error) {
    ElMessage.error('加载任务详情失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleContinue = async () => {
  try {
    await ElMessageBox.confirm('确定要继续这个任务吗？', '确认继续', { type: 'warning' })
    await batchTaskApi.continueTask(props.taskId)
    ElMessage.success('任务已重新启动')
    loadTaskDetail()
    emit('refresh')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '继续任务失败')
    }
  }
}

const handleRetry = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重新执行这个任务吗？这将重置所有处理进度。',
      '确认重试',
      { type: 'warning' }
    )
    await batchTaskApi.retryTask(props.taskId)
    ElMessage.success('任务已重新执行')
    loadTaskDetail()
    emit('refresh')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '重试任务失败')
    }
  }
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个任务吗？此操作不可恢复。',
      '确认删除',
      { type: 'error' }
    )
    await batchTaskApi.deleteTask(props.taskId)
    ElMessage.success('任务已删除')
    handleClose()
    emit('refresh')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除任务失败')
    }
  }
}

const handleUpdateStatus = () => {
  updateStatusVisible.value = true
}

const handleUpdateSuccess = () => {
  loadTaskDetail()
  emit('refresh')
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'UPLOADING': 'info',
    'PROCESSING': 'warning',
    'RECOGNIZING': 'warning',
    'PREVIEWING': 'primary',
    'SAVING': 'warning',
    'COMPLETED': 'success',
    'PARTIAL_SUCCESS': 'warning',
    'FAILED': 'danger',
    'CANCELLED': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'UPLOADING': '上传中',
    'PROCESSING': '处理中',
    'RECOGNIZING': '识别中',
    'PREVIEWING': '预览中',
    'SAVING': '保存中',
    'COMPLETED': '已完成',
    'PARTIAL_SUCCESS': '部分成功',
    'FAILED': '失败',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const getProgressStatus = (status: string) => {
  if (status === 'COMPLETED') return 'success'
  if (status === 'FAILED') return 'exception'
  return undefined
}

const getDetailStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': 'info',
    'UPLOADING': 'info',
    'PROCESSING': 'warning',
    'RECOGNIZING': 'warning',
    'SUCCESS': 'success',
    'FAILED': 'danger',
    'SKIPPED': 'info'
  }
  return statusMap[status] || 'info'
}

const getDetailStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': '待处理',
    'UPLOADING': '上传中',
    'PROCESSING': '处理中',
    'RECOGNIZING': '识别中',
    'SUCCESS': '成功',
    'FAILED': '失败',
    'SKIPPED': '跳过'
  }
  return statusMap[status] || status
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible && props.taskId) {
    loadTaskDetail()
  }
})
</script>

<style scoped>
.task-detail {
  padding: 20px 0;
}

.task-info {
  margin-bottom: 30px;
}

.task-info h3 {
  margin-bottom: 15px;
  color: #303133;
}

.detail-stats {
  margin-bottom: 30px;
}

.detail-stats h3 {
  margin-bottom: 15px;
  color: #303133;
}

.stats-cards {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  min-width: 100px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.action-buttons {
  margin: 30px 0;
  text-align: center;
}

.action-buttons .el-button {
  margin: 0 10px;
}

.detail-list h3 {
  margin-bottom: 15px;
  color: #303133;
}

.success-count {
  color: #67c23a;
  font-weight: bold;
}

.failed-count {
  color: #f56c6c;
  font-weight: bold;
}
</style>
