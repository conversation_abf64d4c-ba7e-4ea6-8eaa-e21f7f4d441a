<template>
  <el-dialog
    v-model="dialogVisible"
    title="清理卡住任务"
    width="600px"
    :before-close="handleClose"
  >
    <div class="cleanup-content">
      <el-alert
        title="警告"
        type="warning"
        description="此操作将删除长期卡住的任务及其所有相关数据，操作不可恢复，请谨慎操作！"
        show-icon
        :closable="false"
        style="margin-bottom: 20px"
      />
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="时间阈值" prop="minutesThreshold">
          <el-input-number
            v-model="form.minutesThreshold"
            :min="60"
            :max="10080"
            :step="60"
            style="width: 200px"
          />
          <span class="form-help">分钟（超过此时间未更新的任务将被清理）</span>
        </el-form-item>
        
        <el-form-item label="预设选项">
          <el-radio-group v-model="presetOption" @change="handlePresetChange">
            <el-radio :label="60">1小时</el-radio>
            <el-radio :label="360">6小时</el-radio>
            <el-radio :label="1440">1天</el-radio>
            <el-radio :label="4320">3天</el-radio>
            <el-radio :label="10080">7天</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <!-- 预览结果 -->
      <div v-if="previewResult" class="preview-section">
        <h4>预览结果</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="发现卡住任务">
            <span class="warning-text">{{ previewResult.foundCount }} 个</span>
          </el-descriptions-item>
          <el-descriptions-item label="时间阈值">
            {{ previewResult.minutesThreshold }} 分钟
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="previewResult.foundCount > 0" class="task-list">
          <h5>将被删除的任务：</h5>
          <ul>
            <li v-for="taskId in previewResult.taskIds" :key="taskId">
              {{ taskId }}
            </li>
          </ul>
        </div>
      </div>
      
      <!-- 执行结果 -->
      <div v-if="cleanupResult" class="result-section">
        <h4>清理结果</h4>
        <el-result
          :icon="cleanupResult.deletedCount > 0 ? 'success' : 'info'"
          :title="cleanupResult.deletedCount > 0 ? '清理完成' : '没有需要清理的任务'"
          :sub-title="`发现 ${cleanupResult.foundCount} 个卡住任务，成功删除 ${cleanupResult.deletedCount} 个`"
        >
          <template #extra>
            <div v-if="cleanupResult.deletedTaskIds.length > 0" class="deleted-list">
              <h5>已删除的任务：</h5>
              <ul>
                <li v-for="taskId in cleanupResult.deletedTaskIds" :key="taskId">
                  {{ taskId }}
                </li>
              </ul>
            </div>
          </template>
        </el-result>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="!cleanupResult" type="info" :loading="previewLoading" @click="handlePreview">
          预览
        </el-button>
        <el-button
          v-if="previewResult && !cleanupResult"
          type="danger"
          :loading="cleanupLoading"
          :disabled="previewResult.foundCount === 0"
          @click="handleCleanup"
        >
          确认清理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { batchTaskApi } from '@/api/batch-task'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const previewLoading = ref(false)
const cleanupLoading = ref(false)
const formRef = ref<FormInstance>()
const presetOption = ref(1440) // 默认1天
const previewResult = ref<any>(null)
const cleanupResult = ref<any>(null)

const form = reactive({
  minutesThreshold: 1440 // 默认1天
})

const rules: FormRules = {
  minutesThreshold: [
    { required: true, message: '请输入时间阈值', trigger: 'blur' },
    { type: 'number', min: 60, max: 10080, message: '时间阈值必须在60-10080分钟之间', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  form.minutesThreshold = 1440
  presetOption.value = 1440
  previewResult.value = null
  cleanupResult.value = null
  formRef.value?.clearValidate()
}

const handlePresetChange = (value: number) => {
  form.minutesThreshold = value
  previewResult.value = null
  cleanupResult.value = null
}

const handlePreview = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    previewLoading.value = true
    // 这里应该调用一个预览接口，但目前我们直接使用清理接口的逻辑
    // 实际应用中可以添加一个专门的预览接口
    const response = await batchTaskApi.getTaskList({
      page: 1,
      size: 1000,
      status: 'PROCESSING,RECOGNIZING,SAVING'
    })
    
    // 模拟预览逻辑
    const now = new Date()
    const thresholdTime = new Date(now.getTime() - form.minutesThreshold * 60 * 1000)
    
    const stuckTasks = response.data.list.filter((task: any) => {
      const updateTime = new Date(task.updatedTime)
      return updateTime < thresholdTime
    })
    
    previewResult.value = {
      foundCount: stuckTasks.length,
      minutesThreshold: form.minutesThreshold,
      taskIds: stuckTasks.map((task: any) => task.taskId)
    }
    
  } catch (error: any) {
    ElMessage.error(error.message || '预览失败')
  } finally {
    previewLoading.value = false
  }
}

const handleCleanup = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 ${previewResult.value.foundCount} 个卡住的任务吗？此操作不可恢复！`,
      '确认清理',
      { 
        type: 'error',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消'
      }
    )
    
    cleanupLoading.value = true
    const response = await batchTaskApi.cleanupStuckTasks(form.minutesThreshold)
    cleanupResult.value = response.data
    
    ElMessage.success(`清理完成，删除了 ${response.data.deletedCount} 个任务`)
    emit('success')
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '清理失败')
    }
  } finally {
    cleanupLoading.value = false
  }
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    resetForm()
  }
})
</script>

<style scoped>
.cleanup-content {
  padding: 10px 0;
}

.form-help {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.preview-section {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.preview-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.warning-text {
  color: #e6a23c;
  font-weight: bold;
}

.task-list {
  margin-top: 15px;
}

.task-list h5 {
  margin: 0 0 10px 0;
  color: #606266;
}

.task-list ul {
  margin: 0;
  padding-left: 20px;
  max-height: 150px;
  overflow-y: auto;
}

.task-list li {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
  color: #909399;
}

.result-section {
  margin-top: 20px;
}

.result-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.deleted-list h5 {
  margin: 0 0 10px 0;
  color: #606266;
}

.deleted-list ul {
  margin: 0;
  padding-left: 20px;
  max-height: 150px;
  overflow-y: auto;
}

.deleted-list li {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
  color: #67c23a;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
