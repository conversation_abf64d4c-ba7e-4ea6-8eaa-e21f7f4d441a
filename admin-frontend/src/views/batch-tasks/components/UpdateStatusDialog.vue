<template>
  <el-dialog
    v-model="dialogVisible"
    title="更新任务状态"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="当前状态">
        <el-tag :type="getStatusType(currentStatus)">
          {{ getStatusText(currentStatus) }}
        </el-tag>
      </el-form-item>
      
      <el-form-item label="新状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择新状态" style="width: 100%">
          <el-option label="上传中" value="UPLOADING" />
          <el-option label="处理中" value="PROCESSING" />
          <el-option label="识别中" value="RECOGNIZING" />
          <el-option label="预览中" value="PREVIEWING" />
          <el-option label="保存中" value="SAVING" />
          <el-option label="已完成" value="COMPLETED" />
          <el-option label="部分成功" value="PARTIAL_SUCCESS" />
          <el-option label="失败" value="FAILED" />
          <el-option label="已取消" value="CANCELLED" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="错误信息" v-if="form.status === 'FAILED'">
        <el-input
          v-model="form.errorMessage"
          type="textarea"
          :rows="3"
          placeholder="请输入错误信息（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { batchTaskApi } from '@/api/batch-task'

// Props
interface Props {
  visible: boolean
  taskId: string
  currentStatus?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

const form = reactive({
  status: '',
  errorMessage: ''
})

const rules: FormRules = {
  status: [
    { required: true, message: '请选择新状态', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  form.status = ''
  form.errorMessage = ''
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    await batchTaskApi.updateTaskStatus(props.taskId, {
      status: form.status,
      errorMessage: form.errorMessage || undefined
    })
    
    ElMessage.success('任务状态已更新')
    emit('success')
    handleClose()
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'UPLOADING': 'info',
    'PROCESSING': 'warning',
    'RECOGNIZING': 'warning',
    'PREVIEWING': 'primary',
    'SAVING': 'warning',
    'COMPLETED': 'success',
    'PARTIAL_SUCCESS': 'warning',
    'FAILED': 'danger',
    'CANCELLED': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'UPLOADING': '上传中',
    'PROCESSING': '处理中',
    'RECOGNIZING': '识别中',
    'PREVIEWING': '预览中',
    'SAVING': '保存中',
    'COMPLETED': '已完成',
    'PARTIAL_SUCCESS': '部分成功',
    'FAILED': '失败',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    resetForm()
  }
})
</script>
