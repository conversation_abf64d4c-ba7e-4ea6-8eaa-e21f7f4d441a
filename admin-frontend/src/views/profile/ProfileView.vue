<template>
  <div class="page-container">
    <div class="page-header">
      <h2>个人资料</h2>
    </div>
    
    <div class="content-card">
      <el-form
        ref="profileFormRef"
        :model="profileForm"
        :rules="profileRules"
        label-width="100px"
        class="profile-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="profileForm.username"
                disabled
                placeholder="用户名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input
                v-model="profileForm.realName"
                placeholder="请输入真实姓名"
                :disabled="!isEditing"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="profileForm.email"
                placeholder="请输入邮箱地址"
                :disabled="!isEditing"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="mobile">
              <el-input
                v-model="profileForm.mobile"
                placeholder="请输入手机号"
                :disabled="!isEditing"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="角色">
              <el-tag :type="profileForm.isSuperAdmin ? 'danger' : 'primary'">
                {{ profileForm.isSuperAdmin ? '超级管理员' : '普通管理员' }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-tag :type="profileForm.status === 1 ? 'success' : 'danger'">
                {{ profileForm.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="创建时间">
              <el-input
                :value="formatDate(profileForm.createTime)"
                disabled
                placeholder="创建时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最后登录">
              <el-input
                :value="formatDate(profileForm.lastLoginTime)"
                disabled
                placeholder="最后登录时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button
            v-if="!isEditing"
            type="primary"
            @click="startEdit"
          >
            编辑资料
          </el-button>
          <template v-else>
            <el-button
              type="primary"
              :loading="saveLoading"
              @click="saveProfile"
            >
              保存
            </el-button>
            <el-button @click="cancelEdit">
              取消
            </el-button>
          </template>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/api/auth'

const authStore = useAuthStore()

// 响应式数据
const profileFormRef = ref<FormInstance>()
const isEditing = ref(false)
const saveLoading = ref(false)

// 表单数据
const profileForm = reactive({
  id: 0,
  username: '',
  realName: '',
  email: '',
  mobile: '',
  isSuperAdmin: false,
  status: 1,
  createTime: '',
  lastLoginTime: ''
})

// 原始数据备份
const originalData = reactive({
  realName: '',
  email: '',
  mobile: ''
})

// 表单验证规则
const profileRules: FormRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '真实姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  mobile: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 方法
const loadProfile = async () => {
  try {
    const userInfo = authStore.userInfo
    if (userInfo) {
      Object.assign(profileForm, {
        id: userInfo.id,
        username: userInfo.username,
        realName: userInfo.realName || '',
        email: userInfo.email || '',
        mobile: userInfo.mobile || '',
        isSuperAdmin: userInfo.isSuperAdmin || false,
        status: userInfo.status || 1,
        createTime: userInfo.createTime || '',
        lastLoginTime: userInfo.lastLoginTime || ''
      })
      
      // 备份原始数据
      Object.assign(originalData, {
        realName: profileForm.realName,
        email: profileForm.email,
        mobile: profileForm.mobile
      })
    }
  } catch (error) {
    console.error('Load profile error:', error)
    ElMessage.error('加载个人资料失败')
  }
}

const startEdit = () => {
  isEditing.value = true
}

const cancelEdit = () => {
  // 恢复原始数据
  profileForm.realName = originalData.realName
  profileForm.email = originalData.email
  profileForm.mobile = originalData.mobile
  
  isEditing.value = false
  
  // 清除表单验证
  if (profileFormRef.value) {
    profileFormRef.value.clearValidate()
  }
}

const saveProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    saveLoading.value = true
    
    // 调用API更新个人资料
    await authApi.updateProfile({
      realName: profileForm.realName,
      email: profileForm.email,
      mobile: profileForm.mobile
    })
    
    // 更新本地存储的用户信息
    await authStore.getCurrentUser()
    
    // 更新备份数据
    Object.assign(originalData, {
      realName: profileForm.realName,
      email: profileForm.email,
      mobile: profileForm.mobile
    })
    
    isEditing.value = false
    ElMessage.success('个人资料更新成功')
  } catch (error) {
    console.error('Save profile error:', error)
    ElMessage.error('保存个人资料失败')
  } finally {
    saveLoading.value = false
  }
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadProfile()
})
</script>

<style scoped>
.profile-form {
  max-width: 800px;
}

.profile-form .el-form-item {
  margin-bottom: 20px;
}
</style>
