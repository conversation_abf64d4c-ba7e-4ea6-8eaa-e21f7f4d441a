<template>
  <div class="page-container">
    <div class="page-header">
      <h2>操作日志</h2>
      <p class="page-description">查看系统操作记录和审计日志</p>
    </div>

    <div class="content-card">
      <!-- 搜索筛选区域 -->
      <div class="search-section">
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="操作人员">
            <el-select
              v-model="searchForm.adminUserId"
              placeholder="请选择操作人员"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="admin in adminUsers"
                :key="admin.id"
                :label="admin.realName || admin.username"
                :value="admin.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="操作类型">
            <el-select
              v-model="searchForm.operationType"
              placeholder="请选择操作类型"
              clearable
              style="width: 150px"
            >
              <el-option label="登录" value="LOGIN" />
              <el-option label="登出" value="LOGOUT" />
              <el-option label="创建" value="CREATE" />
              <el-option label="更新" value="UPDATE" />
              <el-option label="删除" value="DELETE" />
              <el-option label="导入" value="IMPORT" />
              <el-option label="导出" value="EXPORT" />
            </el-select>
          </el-form-item>

          <el-form-item label="操作模块">
            <el-select
              v-model="searchForm.module"
              placeholder="请选择操作模块"
              clearable
              style="width: 150px"
            >
              <el-option label="用户管理" value="USER" />
              <el-option label="账套管理" value="ACCOUNT_SETS" />
              <el-option label="AI配置" value="AI_CONFIG" />
              <el-option label="系统设置" value="SYSTEM" />
            </el-select>
          </el-form-item>

          <el-form-item label="时间范围">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 350px"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="searchLogs">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作日志表格 -->
      <div class="table-section">
        <el-table
          :data="logList"
          v-loading="loading"
          border
          style="width: 100%"
          :default-sort="{ prop: 'createTime', order: 'descending' }"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="adminUserName" label="操作人员" width="120" />
          <el-table-column prop="operationType" label="操作类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getOperationTypeColor(row.operationType)">
                {{ getOperationTypeText(row.operationType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="module" label="操作模块" width="120">
            <template #default="{ row }">
              {{ getModuleText(row.module) }}
            </template>
          </el-table-column>
          <el-table-column prop="description" label="操作描述" min-width="200" />
          <el-table-column prop="ipAddress" label="IP地址" width="130" />
          <el-table-column prop="userAgent" label="用户代理" width="150" show-overflow-tooltip />
          <el-table-column prop="createTime" label="操作时间" width="160" sortable />
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="viewLogDetail(row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="操作日志详情"
      width="600px"
    >
      <div v-if="currentLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日志ID">
            {{ currentLog.id }}
          </el-descriptions-item>
          <el-descriptions-item label="操作人员">
            {{ currentLog.adminUserName }}
          </el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getOperationTypeColor(currentLog.operationType)">
              {{ getOperationTypeText(currentLog.operationType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作模块">
            {{ getModuleText(currentLog.module) }}
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ currentLog.ipAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="操作时间">
            {{ currentLog.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="操作描述" :span="2">
            {{ currentLog.description }}
          </el-descriptions-item>
          <el-descriptions-item label="用户代理" :span="2">
            {{ currentLog.userAgent }}
          </el-descriptions-item>
          <el-descriptions-item v-if="currentLog.requestData" label="请求数据" :span="2">
            <pre class="json-data">{{ formatJson(currentLog.requestData) }}</pre>
          </el-descriptions-item>
          <el-descriptions-item v-if="currentLog.responseData" label="响应数据" :span="2">
            <pre class="json-data">{{ formatJson(currentLog.responseData) }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

// 类型定义
interface OperationLog {
  id: number
  adminUserId: number
  adminUserName: string
  operationType: string
  module: string
  description: string
  ipAddress: string
  userAgent: string
  requestData?: string
  responseData?: string
  createTime: string
}

interface AdminUser {
  id: number
  username: string
  realName?: string
}

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const currentLog = ref<OperationLog | null>(null)

// 搜索表单
const searchForm = reactive({
  adminUserId: '',
  operationType: '',
  module: '',
  dateRange: [] as string[]
})

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 管理员用户列表
const adminUsers = ref<AdminUser[]>([
  { id: 1, username: 'admin', realName: '系统管理员' },
  { id: 2, username: 'operator', realName: '操作员' }
])

// 操作日志列表
const logList = ref<OperationLog[]>([
  {
    id: 1,
    adminUserId: 1,
    adminUserName: '系统管理员',
    operationType: 'LOGIN',
    module: 'SYSTEM',
    description: '管理员登录系统',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    createTime: '2024-01-15 09:30:00'
  },
  {
    id: 2,
    adminUserId: 1,
    adminUserName: '系统管理员',
    operationType: 'CREATE',
    module: 'USER',
    description: '创建用户账号',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    requestData: '{"username":"testuser","realName":"测试用户"}',
    createTime: '2024-01-15 10:15:00'
  }
])

// 方法
const loadLogs = async () => {
  try {
    loading.value = true

    // TODO: 调用API获取操作日志
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    // 模拟数据已在上面定义
    pagination.total = logList.value.length

  } catch (error) {
    console.error('Load logs error:', error)
    ElMessage.error('加载操作日志失败')
  } finally {
    loading.value = false
  }
}

const searchLogs = () => {
  pagination.current = 1
  loadLogs()
}

const resetSearch = () => {
  searchForm.adminUserId = ''
  searchForm.operationType = ''
  searchForm.module = ''
  searchForm.dateRange = []
  pagination.current = 1
  loadLogs()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadLogs()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadLogs()
}

const viewLogDetail = (log: OperationLog) => {
  currentLog.value = log
  detailDialogVisible.value = true
}

const getOperationTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    LOGIN: '登录',
    LOGOUT: '登出',
    CREATE: '创建',
    UPDATE: '更新',
    DELETE: '删除',
    IMPORT: '导入',
    EXPORT: '导出'
  }
  return typeMap[type] || type
}

const getOperationTypeColor = (type: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const colorMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    LOGIN: 'success',
    LOGOUT: 'info',
    CREATE: 'primary',
    UPDATE: 'warning',
    DELETE: 'danger',
    IMPORT: 'success',
    EXPORT: 'info'
  }
  return colorMap[type] || 'info'
}

const getModuleText = (module: string) => {
  const moduleMap: Record<string, string> = {
    USER: '用户管理',
    ACCOUNT_SETS: '账套管理',
    AI_CONFIG: 'AI配置',
    SYSTEM: '系统设置'
  }
  return moduleMap[module] || module
}

const formatJson = (jsonStr: string) => {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch {
    return jsonStr
  }
}

// 生命周期
onMounted(() => {
  loadLogs()
})
</script>

<style scoped>
.page-description {
  color: #666;
  margin-top: 8px;
  margin-bottom: 0;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.search-form {
  margin: 0;
}

.table-section {
  margin-top: 20px;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

.log-detail {
  padding: 10px 0;
}

.json-data {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
