<template>
  <div class="page-container">
    <div class="page-header">
      <h2>管理员账号</h2>
      <p class="page-description">管理系统管理员账号，包括创建、编辑和权限设置</p>
    </div>

    <div class="content-card">
      <!-- 搜索和操作区域 -->
      <div class="toolbar-section">
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索用户名或姓名"
            style="width: 250px"
            clearable
            @keyup.enter="searchAdmins"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="searchAdmins">
            搜索
          </el-button>
        </div>

        <div class="action-area">
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            添加管理员
          </el-button>
        </div>
      </div>

      <!-- 管理员列表表格 -->
      <div class="table-section">
        <el-table
          :data="adminList"
          v-loading="loading"
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="realName" label="真实姓名" width="120" />
          <el-table-column prop="email" label="邮箱" width="180" />
          <el-table-column prop="mobile" label="手机号" width="130" />
          <el-table-column prop="isSuperAdmin" label="角色" width="100">
            <template #default="{ row }">
              <el-tag :type="row.isSuperAdmin ? 'danger' : 'primary'">
                {{ row.isSuperAdmin ? '超级管理员' : '普通管理员' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                {{ row.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="lastLoginTime" label="最后登录" width="160" />
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="showEditDialog(row)"
              >
                编辑
              </el-button>
              <el-button
                :type="row.status === 1 ? 'warning' : 'success'"
                size="small"
                @click="toggleStatus(row)"
              >
                {{ row.status === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteAdmin(row)"
                :disabled="row.isSuperAdmin"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑管理员对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form
        ref="adminFormRef"
        :model="adminForm"
        :rules="adminRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="adminForm.username"
                placeholder="请输入用户名"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input
                v-model="adminForm.realName"
                placeholder="请输入真实姓名"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="adminForm.email"
                placeholder="请输入邮箱地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="mobile">
              <el-input
                v-model="adminForm.mobile"
                placeholder="请输入手机号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="!isEdit">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="adminForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="adminForm.confirmPassword"
                type="password"
                placeholder="请确认密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="角色" prop="isSuperAdmin">
              <el-radio-group v-model="adminForm.isSuperAdmin">
                <el-radio :label="false">普通管理员</el-radio>
                <el-radio :label="true">超级管理员</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="adminForm.status">
                <el-radio :label="1">正常</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="saveLoading"
          @click="saveAdmin"
        >
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'

// 类型定义
interface AdminUser {
  id?: number
  username: string
  realName: string
  email: string
  mobile: string
  password?: string
  confirmPassword?: string
  isSuperAdmin: boolean
  status: number
  lastLoginTime?: string
  createTime?: string
}

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const searchKeyword = ref('')
const adminFormRef = ref<FormInstance>()

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 管理员表单
const adminForm = reactive<AdminUser>({
  username: '',
  realName: '',
  email: '',
  mobile: '',
  password: '',
  confirmPassword: '',
  isSuperAdmin: false,
  status: 1
})

// 管理员列表
const adminList = ref<AdminUser[]>([
  {
    id: 1,
    username: 'admin',
    realName: '系统管理员',
    email: '<EMAIL>',
    mobile: '13800138000',
    isSuperAdmin: true,
    status: 1,
    lastLoginTime: '2024-01-15 09:30:00',
    createTime: '2024-01-01 00:00:00'
  },
  {
    id: 2,
    username: 'operator',
    realName: '操作员',
    email: '<EMAIL>',
    mobile: '13800138001',
    isSuperAdmin: false,
    status: 1,
    lastLoginTime: '2024-01-14 16:20:00',
    createTime: '2024-01-02 10:00:00'
  }
])

// 表单验证规则
const adminRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '真实姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  mobile: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== adminForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑管理员' : '添加管理员'
})

// 方法
const loadAdmins = async () => {
  try {
    loading.value = true

    // TODO: 调用API获取管理员列表
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    // 模拟数据已在上面定义
    pagination.total = adminList.value.length

  } catch (error) {
    console.error('Load admins error:', error)
    ElMessage.error('加载管理员列表失败')
  } finally {
    loading.value = false
  }
}

const searchAdmins = () => {
  pagination.current = 1
  loadAdmins()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadAdmins()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadAdmins()
}

const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const showEditDialog = (admin: AdminUser) => {
  isEdit.value = true
  Object.assign(adminForm, {
    id: admin.id,
    username: admin.username,
    realName: admin.realName,
    email: admin.email,
    mobile: admin.mobile,
    isSuperAdmin: admin.isSuperAdmin,
    status: admin.status
  })
  dialogVisible.value = true
}

const handleDialogClose = () => {
  resetForm()
  if (adminFormRef.value) {
    adminFormRef.value.resetFields()
  }
}

const resetForm = () => {
  Object.assign(adminForm, {
    id: undefined,
    username: '',
    realName: '',
    email: '',
    mobile: '',
    password: '',
    confirmPassword: '',
    isSuperAdmin: false,
    status: 1
  })
}

const saveAdmin = async () => {
  if (!adminFormRef.value) return

  try {
    await adminFormRef.value.validate()
    saveLoading.value = true

    if (isEdit.value) {
      // 编辑管理员
      const index = adminList.value.findIndex(item => item.id === adminForm.id)
      if (index !== -1) {
        Object.assign(adminList.value[index], {
          realName: adminForm.realName,
          email: adminForm.email,
          mobile: adminForm.mobile,
          isSuperAdmin: adminForm.isSuperAdmin,
          status: adminForm.status
        })
      }
      ElMessage.success('管理员信息更新成功')
    } else {
      // 创建管理员
      const newAdmin: AdminUser = {
        id: Date.now(), // 临时ID
        username: adminForm.username,
        realName: adminForm.realName,
        email: adminForm.email,
        mobile: adminForm.mobile,
        isSuperAdmin: adminForm.isSuperAdmin,
        status: adminForm.status,
        createTime: new Date().toLocaleString('zh-CN')
      }
      adminList.value.unshift(newAdmin)
      ElMessage.success('管理员创建成功')
    }

    dialogVisible.value = false
    loadAdmins()
  } catch (error) {
    console.error('Save admin error:', error)
    ElMessage.error('保存管理员信息失败')
  } finally {
    saveLoading.value = false
  }
}

const toggleStatus = (admin: AdminUser) => {
  const action = admin.status === 1 ? '禁用' : '启用'
  ElMessageBox.confirm(`确定要${action}管理员 "${admin.realName}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    admin.status = admin.status === 1 ? 0 : 1
    ElMessage.success(`管理员${action}成功`)
  })
}

const deleteAdmin = (admin: AdminUser) => {
  ElMessageBox.confirm(`确定要删除管理员 "${admin.realName}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = adminList.value.findIndex(item => item.id === admin.id)
    if (index !== -1) {
      adminList.value.splice(index, 1)
      ElMessage.success('管理员删除成功')
      loadAdmins()
    }
  })
}

// 生命周期
onMounted(() => {
  loadAdmins()
})
</script>

<style scoped>
.page-description {
  color: #666;
  margin-top: 8px;
  margin-bottom: 0;
}

.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.search-area {
  display: flex;
  align-items: center;
  gap: 10px;
}

.action-area {
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-section {
  margin-top: 20px;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}
</style>
