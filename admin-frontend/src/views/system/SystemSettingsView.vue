<template>
  <div class="page-container">
    <div class="page-header">
      <h2>系统设置</h2>
      <p class="page-description">管理系统基本配置和参数设置</p>
    </div>

    <div class="content-card">
      <el-tabs v-model="activeTab" class="system-tabs">
        <!-- 基本设置 -->
        <el-tab-pane label="基本设置" name="basic">
          <el-form
            ref="basicFormRef"
            :model="basicSettings"
            :rules="basicRules"
            label-width="120px"
            class="settings-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="系统名称" prop="systemName">
                  <el-input
                    v-model="basicSettings.systemName"
                    placeholder="请输入系统名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="系统版本" prop="systemVersion">
                  <el-input
                    v-model="basicSettings.systemVersion"
                    placeholder="请输入系统版本"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="公司名称" prop="companyName">
                  <el-input
                    v-model="basicSettings.companyName"
                    placeholder="请输入公司名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="contactPhone">
                  <el-input
                    v-model="basicSettings.contactPhone"
                    placeholder="请输入联系电话"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="系统描述" prop="systemDescription">
              <el-input
                v-model="basicSettings.systemDescription"
                type="textarea"
                :rows="3"
                placeholder="请输入系统描述"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="basicLoading"
                @click="saveBasicSettings"
              >
                保存基本设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 安全设置 -->
        <el-tab-pane label="安全设置" name="security">
          <el-form
            ref="securityFormRef"
            :model="securitySettings"
            :rules="securityRules"
            label-width="120px"
            class="settings-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="会话超时时间" prop="sessionTimeout">
                  <el-input-number
                    v-model="securitySettings.sessionTimeout"
                    :min="30"
                    :max="480"
                    :step="30"
                    controls-position="right"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：分钟，范围：30-480分钟</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="密码最小长度" prop="passwordMinLength">
                  <el-input-number
                    v-model="securitySettings.passwordMinLength"
                    :min="6"
                    :max="20"
                    controls-position="right"
                    style="width: 100%"
                  />
                  <div class="form-tip">范围：6-20位</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="登录失败锁定" prop="loginFailLock">
                  <el-switch
                    v-model="securitySettings.loginFailLock"
                    active-text="启用"
                    inactive-text="禁用"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大失败次数" prop="maxFailAttempts">
                  <el-input-number
                    v-model="securitySettings.maxFailAttempts"
                    :min="3"
                    :max="10"
                    :disabled="!securitySettings.loginFailLock"
                    controls-position="right"
                    style="width: 100%"
                  />
                  <div class="form-tip">范围：3-10次</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item>
              <el-button
                type="primary"
                :loading="securityLoading"
                @click="saveSecuritySettings"
              >
                保存安全设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 系统参数 -->
        <el-tab-pane label="系统参数" name="params">
          <div class="params-section">
            <div class="section-header">
              <h3>系统参数配置</h3>
              <el-button type="primary" @click="addParam">
                <el-icon><Plus /></el-icon>
                添加参数
              </el-button>
            </div>

            <el-table :data="systemParams" border style="width: 100%">
              <el-table-column prop="paramKey" label="参数键" width="200" />
              <el-table-column prop="paramValue" label="参数值" />
              <el-table-column prop="description" label="描述" />
              <el-table-column label="操作" width="150">
                <template #default="{ row, $index }">
                  <el-button
                    type="primary"
                    size="small"
                    @click="editParam(row, $index)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteParam($index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 参数编辑对话框 -->
    <el-dialog
      v-model="paramDialogVisible"
      :title="paramDialogTitle"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="paramFormRef"
        :model="paramForm"
        :rules="paramRules"
        label-width="80px"
      >
        <el-form-item label="参数键" prop="paramKey">
          <el-input
            v-model="paramForm.paramKey"
            placeholder="请输入参数键"
            :disabled="paramEditIndex !== -1"
          />
        </el-form-item>
        <el-form-item label="参数值" prop="paramValue">
          <el-input
            v-model="paramForm.paramValue"
            placeholder="请输入参数值"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="paramForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入参数描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="paramDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="paramLoading"
          @click="saveParam"
        >
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('basic')
const basicFormRef = ref<FormInstance>()
const securityFormRef = ref<FormInstance>()
const paramFormRef = ref<FormInstance>()
const basicLoading = ref(false)
const securityLoading = ref(false)
const paramLoading = ref(false)
const paramDialogVisible = ref(false)
const paramEditIndex = ref(-1)

// 基本设置
const basicSettings = reactive({
  systemName: '财务管理系统',
  systemVersion: '1.0.0',
  companyName: '',
  contactPhone: '',
  systemDescription: ''
})

// 安全设置
const securitySettings = reactive({
  sessionTimeout: 120,
  passwordMinLength: 8,
  loginFailLock: true,
  maxFailAttempts: 5
})

// 系统参数
const systemParams = ref([
  {
    paramKey: 'upload.max.size',
    paramValue: '10MB',
    description: '文件上传最大大小限制'
  },
  {
    paramKey: 'ocr.api.timeout',
    paramValue: '30000',
    description: 'OCR接口超时时间（毫秒）'
  }
])

// 参数表单
const paramForm = reactive({
  paramKey: '',
  paramValue: '',
  description: ''
})

// 表单验证规则
const basicRules: FormRules = {
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' }
  ],
  systemVersion: [
    { required: true, message: '请输入系统版本', trigger: 'blur' }
  ]
}

const securityRules: FormRules = {
  sessionTimeout: [
    { required: true, message: '请设置会话超时时间', trigger: 'blur' }
  ],
  passwordMinLength: [
    { required: true, message: '请设置密码最小长度', trigger: 'blur' }
  ],
  maxFailAttempts: [
    { required: true, message: '请设置最大失败次数', trigger: 'blur' }
  ]
}

const paramRules: FormRules = {
  paramKey: [
    { required: true, message: '请输入参数键', trigger: 'blur' }
  ],
  paramValue: [
    { required: true, message: '请输入参数值', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入参数描述', trigger: 'blur' }
  ]
}

// 计算属性
const paramDialogTitle = computed(() => {
  return paramEditIndex.value === -1 ? '添加参数' : '编辑参数'
})

// 方法
const loadSettings = async () => {
  try {
    // TODO: 从API加载设置数据
    console.log('Loading system settings...')
  } catch (error) {
    console.error('Load settings error:', error)
    ElMessage.error('加载系统设置失败')
  }
}

const saveBasicSettings = async () => {
  if (!basicFormRef.value) return

  try {
    await basicFormRef.value.validate()
    basicLoading.value = true

    // TODO: 调用API保存基本设置
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    ElMessage.success('基本设置保存成功')
  } catch (error) {
    console.error('Save basic settings error:', error)
    ElMessage.error('保存基本设置失败')
  } finally {
    basicLoading.value = false
  }
}

const saveSecuritySettings = async () => {
  if (!securityFormRef.value) return

  try {
    await securityFormRef.value.validate()
    securityLoading.value = true

    // TODO: 调用API保存安全设置
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    ElMessage.success('安全设置保存成功')
  } catch (error) {
    console.error('Save security settings error:', error)
    ElMessage.error('保存安全设置失败')
  } finally {
    securityLoading.value = false
  }
}

const addParam = () => {
  paramForm.paramKey = ''
  paramForm.paramValue = ''
  paramForm.description = ''
  paramEditIndex.value = -1
  paramDialogVisible.value = true
}

const editParam = (param: any, index: number) => {
  paramForm.paramKey = param.paramKey
  paramForm.paramValue = param.paramValue
  paramForm.description = param.description
  paramEditIndex.value = index
  paramDialogVisible.value = true
}

const deleteParam = (index: number) => {
  ElMessageBox.confirm('确定要删除这个参数吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    systemParams.value.splice(index, 1)
    ElMessage.success('参数删除成功')
  })
}

const saveParam = async () => {
  if (!paramFormRef.value) return

  try {
    await paramFormRef.value.validate()
    paramLoading.value = true

    if (paramEditIndex.value === -1) {
      // 添加新参数
      systemParams.value.push({
        paramKey: paramForm.paramKey,
        paramValue: paramForm.paramValue,
        description: paramForm.description
      })
      ElMessage.success('参数添加成功')
    } else {
      // 编辑现有参数
      systemParams.value[paramEditIndex.value] = {
        paramKey: paramForm.paramKey,
        paramValue: paramForm.paramValue,
        description: paramForm.description
      }
      ElMessage.success('参数更新成功')
    }

    paramDialogVisible.value = false
  } catch (error) {
    console.error('Save param error:', error)
    ElMessage.error('保存参数失败')
  } finally {
    paramLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.page-description {
  color: #666;
  margin-top: 8px;
  margin-bottom: 0;
}

.system-tabs {
  margin-top: 20px;
}

.settings-form {
  max-width: 800px;
  padding: 20px 0;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.params-section {
  padding: 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
</style>
