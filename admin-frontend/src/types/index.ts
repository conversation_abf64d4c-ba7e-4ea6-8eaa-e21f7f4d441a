// 通用响应类型
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
}

// 分页参数
export interface PaginationParams {
  page: number
  size: number
}

// 分页响应
export interface PaginationResponse<T> {
  list: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 管理员用户类型
export interface AdminUser {
  id: number
  username: string
  realName: string
  email?: string
  mobile?: string
  avatarUrl?: string
  status: boolean
  isSuperAdmin: boolean
  lastLoginTime?: string
  lastLoginIp?: string
  loginCount: number
  createTime: string
  updateTime: string
  roles: AdminRole[]
  permissions: string[]
}

// 管理员角色类型
export interface AdminRole {
  id: number
  roleName: string
  roleCode: string
  description?: string
  permissions: string[]
  status: boolean
  isSystem: boolean
  createTime: string
  updateTime: string
}

// 登录表单
export interface LoginForm {
  username: string
  password: string
}

// 普通用户类型
export interface User {
  id: number
  mobile: string
  nickname?: string
  realName?: string
  email?: string
  avatarUrl?: string
  accountSetsId?: number
  createDate: string
  initPassword?: string
  status?: boolean
  role?: string
  accountSets?: AccountSet[]
  accountSetsList?: UserAccountSet[]
}

// 用户账套关联
export interface UserAccountSet {
  id: number
  userId: number
  accountSetsId: number
  role: string
  accountSetsName: string
  companyName: string
  createDate: string
}

// 账套类型
export interface AccountSet {
  id: number
  companyName: string
  enableDate: string
  creditCode?: string
  accountingStandards: number
  address?: string
  cashierModule: boolean
  industry?: number
  fixedAssetModule: boolean
  vatType: number
  voucherReviewed: boolean
  createDate: string
  creatorId: number
  currentAccountDate: string
  encoding: string
  userCount?: number
  creator?: User
  creatorName?: string
  creatorMobile?: string
  userList?: AccountSetUser[]
}

// 账套用户
export interface AccountSetUser {
  userId: number
  realName: string
  mobile: string
  role: string
  createDate: string
}

// AI配置类型
export interface AiConfig {
  id: number
  userId?: number
  accountSetsId?: number
  configKey: string
  configValue: string
  description?: string
  isActive: boolean
  createTime: string
  updateTime: string
}

// AI配置详情
export interface AiConfigDetail {
  userId: number
  userName: string
  userMobile: string
  accountSetsId?: number
  accountSetsName?: string
  enabled: boolean
  baseUrl?: string
  apiKey?: string
  defaultModel?: string
  temperature?: number
  maxTokens?: number
  timeout?: number
  maxRetries?: number
  updateTime?: string
}

// AI配置模板
export interface AiConfigTemplate {
  id: number
  templateName: string
  templateCode: string
  providerName: string
  providerType: string
  defaultConfig: Record<string, any>
  configSchema?: Record<string, any>
  description?: string
  usageCount: number
  status: boolean
  isDefault: boolean
  createTime: string
  updateTime: string
}

// 字段映射模板
export interface FieldMappingTemplate {
  id: number
  templateName: string
  templateCode: string
  bankName?: string
  documentType: string
  mappingRules: Record<string, string>
  ocrFields?: string[]
  targetFields?: string[]
  sampleData?: Record<string, any>
  usageCount: number
  successRate: number
  status: boolean
  isDefault: boolean
  createTime: string
  updateTime: string
}

// 操作日志
export interface OperationLog {
  id: number
  adminUserId: number
  adminUsername: string
  operationType: string
  operationModule: string
  operationDesc: string
  requestMethod?: string
  requestUrl?: string
  requestParams?: string
  responseData?: string
  ipAddress?: string
  userAgent?: string
  executionTime?: number
  status: boolean
  errorMessage?: string
  createTime: string
}

// 系统配置
export interface SystemConfig {
  id: number
  configGroup: string
  configKey: string
  configValue: string
  configType: 'string' | 'number' | 'boolean' | 'json'
  description?: string
  isEncrypted: boolean
  isSystem: boolean
  sortOrder: number
  status: boolean
  createTime: string
  updateTime: string
}

// 统计数据
export interface DashboardStats {
  userCount: number
  accountCount: number
  aiConfigCount: number
  templateCount: number
  userTrend: number[]
  accountTrend: number[]
  aiConfigTrend: number[]
  templateTrend: number[]
  userActivity: Array<{ date: string; count: number }>
  aiUsage: Array<{ provider: string; count: number }>
}

// 导入导出记录
export interface ImportExportLog {
  id: number
  adminUserId: number
  operationType: 'import' | 'export'
  dataType: string
  fileName?: string
  filePath?: string
  fileSize?: number
  totalRecords: number
  successRecords: number
  failedRecords: number
  errorDetails?: string
  status: 'processing' | 'completed' | 'failed'
  startTime: string
  endTime?: string
  executionTime?: number
}

// 菜单项类型
export interface MenuItem {
  path: string
  name: string
  title: string
  icon?: string
  permission?: string
  children?: MenuItem[]
  hidden?: boolean
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  fixed?: boolean | string
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
  type?: string
}

// 搜索表单基类
export interface BaseSearchForm {
  keyword?: string
  page: number
  size: number
}

// 用户搜索表单
export interface UserSearchForm extends BaseSearchForm {
  role?: string
  accountSetsId?: number
  status?: boolean
}

// AI配置搜索表单
export interface AiConfigSearchForm extends BaseSearchForm {
  userId?: number
  accountSetsId?: number
  enabled?: boolean
}

// 操作日志搜索表单
export interface LogSearchForm extends BaseSearchForm {
  operationType?: string
  operationModule?: string
  adminUserId?: number
  startTime?: string
  endTime?: string
  status?: boolean
}

// 用户表单
export interface UserForm {
  mobile: string
  realName: string
  nickname: string
  email: string
  password: string
  accountSetsId: number | null
  role: string
}

// 账套表单
export interface AccountSetForm {
  companyName: string
  enableDate: string
  creditCode: string
  accountingStandards: number
  vatType: number
  address: string
  creatorId: number | null
}

// 创建用户数据
export interface CreateUserData {
  mobile: string
  realName: string
  nickname: string
  email: string
  password: string
  accountSetsId?: number | null
  role: string
}

// 创建账套数据
export interface CreateAccountSetsData {
  companyName: string
  enableDate: string
  creditCode: string
  accountingStandards: number
  vatType: number
  address: string
  creatorId: number | null
}
