import { request } from '@/utils/request'

export interface AccountSetsListParams {
  page?: number
  size?: number
  keyword?: string
  creatorId?: number
}

export interface CreateAccountSetsData {
  companyName: string
  enableDate: string
  creditCode?: string
  accountingStandards: number
  address?: string
  industry?: number
  vatType: number
  voucherReviewed: number
  fixedAssetModule?: number
  cashierModule?: number
  creatorId: number
}

export interface UpdateAccountSetsData {
  companyName?: string
  enableDate?: string
  creditCode?: string
  accountingStandards?: number
  address?: string
  industry?: number
  vatType?: number
  voucherReviewed?: number
  fixedAssetModule?: number
  cashierModule?: number
}

/**
 * 获取账套列表
 */
export const getAccountSetsList = (params: AccountSetsListParams) => {
  return request.get('/admin/account-sets/list', { params })
}

/**
 * 获取账套详情
 */
export const getAccountSetsDetail = (accountSetsId: number) => {
  return request.get(`/admin/account-sets/${accountSetsId}`)
}

/**
 * 创建账套
 */
export const createAccountSets = (data: CreateAccountSetsData) => {
  return request.post('/admin/account-sets', data)
}

/**
 * 更新账套信息
 */
export const updateAccountSets = (accountSetsId: number, data: UpdateAccountSetsData) => {
  return request.put(`/admin/account-sets/${accountSetsId}`, data)
}

/**
 * 删除账套
 */
export const deleteAccountSets = (accountSetsId: number) => {
  return request.delete(`/admin/account-sets/${accountSetsId}`)
}

/**
 * 为账套添加用户
 */
export const addUserToAccountSets = (accountSetsId: number, data: { userId: number, role: string }) => {
  return request.post(`/admin/account-sets/${accountSetsId}/users`, data)
}

/**
 * 从账套中移除用户
 */
export const removeUserFromAccountSets = (accountSetsId: number, userId: number) => {
  return request.delete(`/admin/account-sets/${accountSetsId}/users/${userId}`)
}
