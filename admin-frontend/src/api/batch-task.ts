import request from '@/utils/request'

export interface TaskListParams {
  page: number
  size: number
  status?: string
  importType?: string
  accountSetsId?: number
  keyword?: string
}

export interface UpdateStatusParams {
  status: string
  errorMessage?: string
}

/**
 * 批量任务管理API
 */
export const batchTaskApi = {
  /**
   * 获取任务列表
   */
  getTaskList(params: TaskListParams) {
    return request({
      url: '/admin/batch-tasks/list',
      method: 'get',
      params
    })
  },

  /**
   * 获取任务详情
   */
  getTaskDetail(taskId: string) {
    return request({
      url: `/admin/batch-tasks/${taskId}`,
      method: 'get'
    })
  },

  /**
   * 继续卡住的任务
   */
  continueTask(taskId: string) {
    return request({
      url: `/admin/batch-tasks/${taskId}/continue`,
      method: 'post'
    })
  },

  /**
   * 删除任务
   */
  deleteTask(taskId: string) {
    return request({
      url: `/admin/batch-tasks/${taskId}`,
      method: 'delete'
    })
  },

  /**
   * 重新执行任务
   */
  retryTask(taskId: string) {
    return request({
      url: `/admin/batch-tasks/${taskId}/retry`,
      method: 'post'
    })
  },

  /**
   * 清理长期卡住的任务
   */
  cleanupStuckTasks(minutesThreshold: number) {
    return request({
      url: '/admin/batch-tasks/cleanup-stuck',
      method: 'post',
      params: { minutesThreshold }
    })
  },

  /**
   * 获取任务统计信息
   */
  getStatistics() {
    return request({
      url: '/admin/batch-tasks/statistics',
      method: 'get'
    })
  },

  /**
   * 更新任务状态
   */
  updateTaskStatus(taskId: string, params: UpdateStatusParams) {
    return request({
      url: `/admin/batch-tasks/${taskId}/status`,
      method: 'put',
      params
    })
  }
}
