import { request } from '@/utils/request'
import type { AdminUser, LoginForm, ApiResponse } from '@/types'

export const authApi = {
  // 管理员登录
  login(data: LoginForm): Promise<ApiResponse<AdminUser>> {
    return request.post('/admin/auth/login', data)
  },
  
  // 管理员登出
  logout(): Promise<ApiResponse<void>> {
    return request.post('/admin/auth/logout')
  },
  
  // 获取当前管理员信息
  getCurrentUser(): Promise<ApiResponse<AdminUser>> {
    return request.get('/admin/auth/current')
  },
  
  // 修改密码
  changePassword(data: { oldPassword: string; newPassword: string }): Promise<ApiResponse<void>> {
    return request.post('/admin/auth/change-password', data)
  },

  // 更新个人资料
  updateProfile(data: { realName: string; email: string; mobile: string }): Promise<ApiResponse<void>> {
    return request.post('/admin/auth/update-profile', data)
  },
  
  // 检查会话状态
  checkSession(): Promise<ApiResponse<{ isValid: boolean; sessionId: string; maxInactiveInterval: number }>> {
    return request.get('/admin/auth/check-session')
  }
}
