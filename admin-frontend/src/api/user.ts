import { request } from '@/utils/request'

export interface UserListParams {
  page?: number
  size?: number
  keyword?: string
  role?: string
  accountSetsId?: number
}

export interface CreateUserData {
  mobile: string
  realName: string
  nickname: string
  email?: string
  password: string
  accountSetsId?: number
  role?: string
}

export interface UpdateUserData {
  realName?: string
  nickname?: string
  email?: string
}

/**
 * 获取用户列表
 */
export const getUserList = (params: UserListParams) => {
  return request.get('/admin/users/list', { params })
}

/**
 * 获取用户详情
 */
export const getUserDetail = (userId: number) => {
  return request.get(`/admin/users/${userId}`)
}

/**
 * 创建用户
 */
export const createUser = (data: CreateUserData) => {
  return request.post('/admin/users/create', data)
}

/**
 * 更新用户信息
 */
export const updateUser = (userId: number, data: UpdateUserData) => {
  return request.put(`/admin/users/${userId}`, data)
}

/**
 * 重置用户密码
 */
export const resetPassword = (userId: number) => {
  return request.post(`/admin/users/${userId}/reset-password`)
}

/**
 * 删除用户
 */
export const deleteUser = (userId: number) => {
  return request.delete(`/admin/users/${userId}`)
}

/**
 * 获取用户的账套关联
 */
export const getUserAccountSets = (userId: number) => {
  return request.get(`/admin/users/${userId}/account-sets`)
}

/**
 * 为用户添加账套权限
 */
export const addUserToAccountSet = (userId: number, data: { accountSetsId: number, role: string }) => {
  return request.post(`/admin/users/${userId}/account-sets`, data)
}

/**
 * 从用户中移除账套权限
 */
export const removeUserFromAccountSet = (userId: number, accountSetsId: number) => {
  return request.delete(`/admin/users/${userId}/account-sets/${accountSetsId}`)
}
