// 字典数据常量
export const DICT_DATA = {
  // 会计准则
  accountingStandards: [
    { value: 0, label: '小企业会计准则' },
    { value: 1, label: '企业会计准则' }
  ],
  
  // 增值税种类
  vatType: [
    { value: 0, label: '小规模纳税人' },
    { value: 1, label: '一般纳税人' }
  ],
  
  // 是否需要审核
  voucherReviewed: [
    { value: 0, label: '不需要' },
    { value: 1, label: '需要' }
  ],
  
  // 启用/禁用
  enableOptions: [
    { value: 0, label: '不启用' },
    { value: 1, label: '启用' }
  ],
  
  // 行业选项
  industry: [
    { value: 0, label: 'IT·通信·电子·互联网' },
    { value: 1, label: '金融业' },
    { value: 2, label: '房地产·建筑业' },
    { value: 3, label: '商业服务' },
    { value: 4, label: '贸易·批发·零售·租赁业' },
    { value: 5, label: '文体教育·工艺美术' },
    { value: 6, label: '生产·加工·制造' },
    { value: 7, label: '交通·运输·物流·仓储' },
    { value: 8, label: '服务业' },
    { value: 9, label: '文化·传媒·娱乐·体育' },
    { value: 10, label: '能源·矿产·环保' },
    { value: 11, label: '政府·非盈利机构' },
    { value: 12, label: '农·林·牧·渔·其他' }
  ]
}

// 地区数据类型定义
export interface DistrictItem {
  id: string
  title: string
  parentId: string
}

// 地区数据（简化版，实际使用时可能需要完整的地区数据）
export const DISTRICT_DATA: DistrictItem[] = [
  // 省级
  { id: '110000', title: '北京市', parentId: '0' },
  { id: '120000', title: '天津市', parentId: '0' },
  { id: '130000', title: '河北省', parentId: '0' },
  { id: '140000', title: '山西省', parentId: '0' },
  { id: '150000', title: '内蒙古自治区', parentId: '0' },
  { id: '210000', title: '辽宁省', parentId: '0' },
  { id: '220000', title: '吉林省', parentId: '0' },
  { id: '230000', title: '黑龙江省', parentId: '0' },
  { id: '310000', title: '上海市', parentId: '0' },
  { id: '320000', title: '江苏省', parentId: '0' },
  { id: '330000', title: '浙江省', parentId: '0' },
  { id: '340000', title: '安徽省', parentId: '0' },
  { id: '350000', title: '福建省', parentId: '0' },
  { id: '360000', title: '江西省', parentId: '0' },
  { id: '370000', title: '山东省', parentId: '0' },
  { id: '410000', title: '河南省', parentId: '0' },
  { id: '420000', title: '湖北省', parentId: '0' },
  { id: '430000', title: '湖南省', parentId: '0' },
  { id: '440000', title: '广东省', parentId: '0' },
  { id: '450000', title: '广西壮族自治区', parentId: '0' },
  { id: '460000', title: '海南省', parentId: '0' },
  { id: '500000', title: '重庆市', parentId: '0' },
  { id: '510000', title: '四川省', parentId: '0' },
  { id: '520000', title: '贵州省', parentId: '0' },
  { id: '530000', title: '云南省', parentId: '0' },
  { id: '540000', title: '西藏自治区', parentId: '0' },
  { id: '610000', title: '陕西省', parentId: '0' },
  { id: '620000', title: '甘肃省', parentId: '0' },
  { id: '630000', title: '青海省', parentId: '0' },
  { id: '640000', title: '宁夏回族自治区', parentId: '0' },
  { id: '650000', title: '新疆维吾尔自治区', parentId: '0' },
  { id: '710000', title: '台湾省', parentId: '0' },
  { id: '810000', title: '香港特别行政区', parentId: '0' },
  { id: '820000', title: '澳门特别行政区', parentId: '0' },
  
  // 市级（部分示例）
  { id: '110100', title: '北京市', parentId: '110000' },
  { id: '120100', title: '天津市', parentId: '120000' },
  { id: '310100', title: '上海市', parentId: '310000' },
  { id: '500100', title: '重庆市', parentId: '500000' },
  { id: '130100', title: '石家庄市', parentId: '130000' },
  { id: '130200', title: '唐山市', parentId: '130000' },
  { id: '320100', title: '南京市', parentId: '320000' },
  { id: '320200', title: '无锡市', parentId: '320000' },
  { id: '330100', title: '杭州市', parentId: '330000' },
  { id: '330200', title: '宁波市', parentId: '330000' },
  { id: '440100', title: '广州市', parentId: '440000' },
  { id: '440300', title: '深圳市', parentId: '440000' }
]

// 获取省级数据
export const getProvinces = (): DistrictItem[] => {
  return DISTRICT_DATA.filter(item => item.parentId === '0')
}

// 获取市级数据
export const getCities = (provinceId: string): DistrictItem[] => {
  return DISTRICT_DATA.filter(item => item.parentId === provinceId)
}

// 根据ID获取地区名称
export const getDistrictName = (id: string): string => {
  const item = DISTRICT_DATA.find(item => item.id === id)
  return item ? item.title : ''
}
