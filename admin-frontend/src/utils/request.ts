import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiResponse } from '@/types'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// 创建 axios 实例
const service: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 30000,
  withCredentials: true, // 支持跨域携带 cookie
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  paramsSerializer: {
    serialize: (params) => {
      return new URLSearchParams(params).toString()
    }
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加 token 到请求头
    const token = localStorage.getItem('admin_token')
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<any>) => {
    const { data } = response

    // 处理Result格式 (后端返回的格式)
    if (data.code !== undefined) {
      // 转换为前端期望的格式
      const apiResponse = {
        success: data.code === 200,
        message: data.message || '',
        data: data.data,
        code: data.code
      }

      if (apiResponse.success) {
        return apiResponse
      } else {
        // 业务错误处理
        const errorMessage = apiResponse.message || '请求失败'

        // 根据错误码进行不同处理
        switch (apiResponse.code) {
          case 401:
            // 未授权，跳转到登录页
            handleUnauthorized()
            break
          case 403:
            // 权限不足
            ElMessage.error('权限不足')
            break
          case 404:
            ElMessage.error('请求的资源不存在')
            break
          case 500:
            ElMessage.error('服务器内部错误')
            break
          default:
            ElMessage.error(errorMessage)
        }

        return Promise.reject(new Error(errorMessage))
      }
    }

    // 处理原有的ApiResponse格式
    if (data.success !== undefined) {
      if (data.success) {
        return data
      } else {
        // 业务错误处理
        const errorMessage = data.message || '请求失败'

        // 根据错误码进行不同处理
        switch (data.code) {
          case 401:
            // 未授权，跳转到登录页
            handleUnauthorized()
            break
          case 403:
            // 权限不足
            ElMessage.error('权限不足')
            break
          case 404:
            ElMessage.error('请求的资源不存在')
            break
          case 500:
            ElMessage.error('服务器内部错误')
            break
          default:
            ElMessage.error(errorMessage)
        }

        return Promise.reject(new Error(errorMessage))
      }
    }

    // 如果都不匹配，直接返回数据
    return { success: true, data: data, message: 'success' }
  },
  (error) => {
    console.error('Response error:', error)
    
    // 网络错误处理
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          handleUnauthorized()
          break
        case 403:
          ElMessage.error('权限不足')
          router.push('/403')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        case 502:
          ElMessage.error('网关错误')
          break
        case 503:
          ElMessage.error('服务不可用')
          break
        case 504:
          ElMessage.error('网关超时')
          break
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请稍后重试')
    } else if (error.message === 'Network Error') {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error(error.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 处理未授权错误
const handleUnauthorized = () => {
  const authStore = useAuthStore()
  
  ElMessageBox.confirm(
    '登录状态已过期，请重新登录',
    '系统提示',
    {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    authStore.logout().then(() => {
      router.push('/login')
    })
  }).catch(() => {
    // 用户取消，也需要清除登录状态
    authStore.logout().then(() => {
      router.push('/login')
    })
  })
}

// 请求方法封装
export const request = {
  get<T = any>(url: string, params?: any): Promise<ApiResponse<T>> {
    return service.get(url, { params })
  },
  
  post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return service.post(url, data)
  },
  
  put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return service.put(url, data)
  },
  
  delete<T = any>(url: string, params?: any): Promise<ApiResponse<T>> {
    return service.delete(url, { params })
  },
  
  patch<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return service.patch(url, data)
  }
}

// 文件上传请求
export const uploadRequest = (url: string, formData: FormData, onProgress?: (progress: number) => void): Promise<ApiResponse> => {
  return service.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

// 文件下载请求
export const downloadRequest = (url: string, params?: any, filename?: string): Promise<void> => {
  return service.get(url, {
    params,
    responseType: 'blob'
  }).then((response: any) => {
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  })
}

export default service
