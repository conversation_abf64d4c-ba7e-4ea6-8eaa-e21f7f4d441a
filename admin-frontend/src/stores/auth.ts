import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { AdminUser, LoginForm } from '@/types'
import { authApi } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<AdminUser | null>(null)
  const token = ref<string>('')
  const permissions = ref<string[]>([])
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isSuperAdmin = computed(() => user.value?.isSuperAdmin || false)
  const userInfo = computed(() => user.value)
  
  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (isSuperAdmin.value) return true
    
    // 支持通配符权限检查
    if (permissions.value.includes('*')) return true
    
    // 检查具体权限
    if (permissions.value.includes(permission)) return true
    
    // 检查模块通配符权限
    const [module] = permission.split(':')
    if (permissions.value.includes(`${module}:*`)) return true
    
    return false
  }
  
  // 检查多个权限（任一满足即可）
  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }
  
  // 检查多个权限（全部满足）
  const hasAllPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(permission => hasPermission(permission))
  }
  
  // 登录
  const login = async (loginForm: LoginForm): Promise<void> => {
    try {
      const response = await authApi.login(loginForm)
      const userData = response.data
      
      // 保存用户信息和权限
      user.value = userData
      permissions.value = userData.permissions || []
      
      // 保存 token 到本地存储
      const sessionToken = `admin_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      token.value = sessionToken
      localStorage.setItem('admin_token', sessionToken)
      localStorage.setItem('admin_user', JSON.stringify(userData))
      
    } catch (error) {
      throw error
    }
  }
  
  // 登出
  const logout = async (): Promise<void> => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('Logout API error:', error)
    } finally {
      // 清除本地状态
      user.value = null
      token.value = ''
      permissions.value = []

      // 清除本地存储
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')

      // 清除会话存储
      sessionStorage.clear()
    }
  }
  
  // 获取当前用户信息
  const getCurrentUser = async (): Promise<void> => {
    try {
      const response = await authApi.getCurrentUser()
      const userData = response.data
      
      user.value = userData
      permissions.value = userData.permissions || []
      
      // 更新本地存储
      localStorage.setItem('admin_user', JSON.stringify(userData))
      
    } catch (error) {
      // 获取用户信息失败，清除登录状态
      logout()
      throw error
    }
  }
  
  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string): Promise<void> => {
    try {
      await authApi.changePassword({ oldPassword, newPassword })
    } catch (error) {
      throw error
    }
  }
  
  // 初始化（从本地存储恢复状态）
  const initialize = (): void => {
    const savedToken = localStorage.getItem('admin_token')
    const savedUser = localStorage.getItem('admin_user')
    
    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        const userData = JSON.parse(savedUser)
        user.value = userData
        permissions.value = userData.permissions || []
      } catch (error) {
        console.error('Failed to restore auth state:', error)
        logout()
      }
    }
  }
  
  // 更新用户信息
  const updateUserInfo = (userData: Partial<AdminUser>): void => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('admin_user', JSON.stringify(user.value))
    }
  }
  
  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    permissions: readonly(permissions),
    
    // 计算属性
    isLoggedIn,
    isSuperAdmin,
    userInfo,
    
    // 方法
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    login,
    logout,
    getCurrentUser,
    changePassword,
    initialize,
    updateUserInfo
  }
})

// 在应用启动时初始化
export const initializeAuth = () => {
  const authStore = useAuthStore()
  authStore.initialize()
}
