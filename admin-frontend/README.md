# 财务系统管理后台

基于 Vue 3 + TypeScript + Element Plus 构建的现代化管理后台界面。

## 技术栈

- **Vue 3.4+** - 渐进式 JavaScript 框架
- **TypeScript** - JavaScript 的超集，提供类型安全
- **Vite 5+** - 下一代前端构建工具
- **Element Plus** - 基于 Vue 3 的组件库
- **Pinia** - Vue 3 官方推荐的状态管理库
- **Vue Router 4** - Vue.js 官方路由管理器
- **Axios** - 基于 Promise 的 HTTP 客户端
- **ECharts** - 数据可视化图表库
- **Tailwind CSS** - 原子化 CSS 框架

## 功能特性

- 🔐 **权限管理** - 基于角色的权限控制系统
- 👥 **用户管理** - 用户账号的增删改查和权限分配
- 📊 **账套管理** - 财务账套的创建和管理
- 🤖 **AI配置管理** - 智能AI功能的配置和管理
- 📝 **模板管理** - 字段映射模板的管理
- 📈 **数据统计** - 可视化的数据统计和分析
- 📋 **操作日志** - 详细的操作审计日志
- 🎨 **现代化UI** - 响应式设计，支持深色模式
- 🚀 **高性能** - 基于 Vite 的快速构建和热更新

## 开发环境要求

- Node.js >= 18.0.0
- Yarn >= 1.22.0 (推荐) 或 npm >= 8.0.0

## 快速开始

### 安装依赖

```bash
# 推荐使用 Yarn
yarn install

# 或使用 npm
npm install

# 如果存在 package-lock.json，建议删除以避免冲突
rm -f package-lock.json
```

### 启动开发服务器

```bash
# 推荐使用 Yarn
yarn dev

# 或使用 npm
npm run dev
```

开发服务器将在 `http://localhost:3001` 启动。

### 构建生产版本

```bash
# 推荐使用 Yarn
yarn build

# 或使用 npm
npm run build
```

### 预览生产构建

```bash
npm run preview
# 或
yarn preview
```

## 项目结构

```
admin-frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   ├── assets/            # 资源文件
│   ├── components/        # 通用组件
│   ├── layout/            # 布局组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── types/             # TypeScript 类型定义
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   ├── main.ts            # 应用入口
│   └── style.css          # 全局样式
├── .env                   # 环境变量
├── .env.development       # 开发环境变量
├── .env.production        # 生产环境变量
├── index.html             # HTML 模板
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript 配置
├── tailwind.config.js     # Tailwind CSS 配置
└── vite.config.ts         # Vite 配置
```

## 环境变量

### 开发环境 (.env.development)

```env
VITE_API_BASE_URL=http://localhost:9080/api
```

### 生产环境 (.env.production)

```env
VITE_API_BASE_URL=/api
```

## 部署

### 构建

```bash
npm run build
```

构建产物将生成在 `dist` 目录中。

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 管理后台路由
    location /admin {
        try_files $uri $uri/ /admin/index.html;
    }

    # API 代理
    location /api {
        proxy_pass http://backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 开发指南

### 添加新页面

1. 在 `src/views/` 目录下创建页面组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 如需权限控制，在路由 meta 中添加 permission 字段

### 添加新API

1. 在 `src/api/` 目录下创建对应的 API 文件
2. 使用 `src/utils/request.ts` 中的 request 方法
3. 在 `src/types/index.ts` 中定义相关类型

### 状态管理

使用 Pinia 进行状态管理，store 文件位于 `src/stores/` 目录。

### 样式规范

- 使用 Tailwind CSS 进行样式开发
- 组件内部样式使用 scoped CSS
- 全局样式定义在 `src/style.css`

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
